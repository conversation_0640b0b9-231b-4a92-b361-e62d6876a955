<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <application>
        <activity
            android:name=".ui.page.MessageReplyDetailActivity"
            android:windowSoftInputMode="adjustResize|stateHidden"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.page.TUIForwardChatActivity"
            android:screenOrientation="portrait" />
<!--        <activity-->
<!--            android:name=".ui.page.TUIC2CChatActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait"-->
<!--            android:windowSoftInputMode="adjustResize|stateHidden" />-->
        <activity
            android:name=".ui.page.TUIGroupChatActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".ui.page.MessageReceiptDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".component.camera.CameraActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".component.imagevideoscan.ImageVideoScanActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <provider
            android:name=".TUIChatService"
            android:authorities="${applicationId}.TUIChat.Init"
            android:enabled="true"
            android:exported="false" />
    </application>

</manifest>