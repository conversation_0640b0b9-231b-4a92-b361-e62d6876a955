<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:focusable="true"
    android:gravity="center"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/content_image_iv"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerVertical="true"
        android:clickable="false"
        android:scaleType="fitCenter" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@+id/content_image_iv"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingLeft="8dp" >

        <TextView
            android:id="@+id/order_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerInParent="true"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="12sp"
            android:lineHeight="17sp"
            android:textStyle="bold"
            android:clickable="false"
            android:focusable="false"
            android:textColor="@color/chat_text_color"
            android:text="order_title"/>

        <TextView
            android:id="@+id/order_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerInParent="true"
            android:layout_marginTop="3dp"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="12sp"
            android:lineHeight="17sp"
            android:clickable="false"
            android:focusable="false"
            android:textColor="#999999"
            android:text="order_description" />

        <TextView
            android:id="@+id/order_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerInParent="true"
            android:layout_marginTop="2dp"
            android:singleLine="true"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textSize="18sp"
            android:lineHeight="25sp"
            android:clickable="false"
            android:focusable="false"
            android:textColor="@color/order_message_color"
            android:text="order_price" />

     </LinearLayout>
</RelativeLayout>