<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/calling_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/left_icon"
        android:layout_width="15.36dp"
        android:layout_height="15.36dp"
        android:layout_centerInParent="true"
        android:clickable="false"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_audio_call" />

    <TextView
        android:id="@+id/msg_body_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="?attr/chat_self_msg_text_color"
        android:paddingLeft="7dp"
        android:paddingRight="7dp"
        android:maxWidth="276dp"
        android:textSize="@dimen/chat_message_text_size" />

    <ImageView
        android:id="@+id/right_icon"
        android:layout_width="15.36dp"
        android:layout_height="15.36dp"
        android:layout_centerInParent="true"
        android:scaleType="fitCenter"
        android:clickable="false"
        android:src="@drawable/ic_audio_call" />
</LinearLayout>

