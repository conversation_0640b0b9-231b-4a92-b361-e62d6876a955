<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>
        <activity
            android:name=".ui.pages.AddMoreActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.pages.FriendProfileActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.pages.NewFriendActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.pages.BlackListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.pages.GroupListActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.pages.StartC2CChatActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.pages.StartGroupChatActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />

        <activity
            android:name=".ui.pages.ForwardSelectGroupActivity"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.pages.StartGroupMemberSelectActivity"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait" />

        <provider
            android:name=".TUIContactService"
            android:authorities="${applicationId}.TUIContact.Init"
            android:enabled="true"
            android:exported="false" />
    </application>
</manifest>