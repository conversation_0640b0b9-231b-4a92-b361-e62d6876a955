<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <com.tencent.qcloud.tuicore.component.TitleBarLayout
        android:id="@+id/new_friend_titlebar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/page_title_height" />

    <ListView
        android:id="@+id/new_friend_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/not_found_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="120dp"
        android:gravity="center_horizontal"
        android:textSize="13.44sp"
        android:textColor="#444444"
        android:visibility="gone"
        android:text="@string/contact_no_new_friend_application"
        tools:visibility="visible"
        tools:text="暂无群聊" />

</LinearLayout>