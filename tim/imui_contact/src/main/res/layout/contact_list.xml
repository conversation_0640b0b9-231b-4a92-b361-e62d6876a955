<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#F2F3F5"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/contact_member_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/not_found_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="120dp"
        android:gravity="center_horizontal"
        android:textSize="13.44sp"
        android:textColor="#444444"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="暂无群聊" />

    <com.tencent.qcloud.tuikit.tuicontact.component.indexlib.IndexBar.widget.IndexBar
        android:id="@+id/contact_indexBar"
        android:layout_width="24dp"
        android:layout_marginTop="54dp"
        android:layout_marginBottom="40.8dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical|right"
        app:indexBarPressBackground="@color/partTranslucent"
        app:indexBarTextSize="12sp" />

    <TextView
        android:id="@+id/contact_tvSideBarHint"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="center"
        android:background="@drawable/shape_side_bar_bg"
        android:gravity="center"
        android:textColor="@android:color/white"
        android:textSize="40sp"
        android:visibility="gone"
        tools:text="A"
        tools:visibility="visible" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ProgressBar
            android:id="@+id/contact_loading_bar"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_centerInParent="true" />
    </RelativeLayout>
</FrameLayout>
