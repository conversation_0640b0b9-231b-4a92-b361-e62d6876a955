<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/status_bar_color"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <com.tencent.qcloud.tuicore.component.TitleBarLayout
        android:id="@+id/start_c2c_chat_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/page_title_height" />

    <com.tencent.qcloud.tuikit.tuicontact.ui.view.ContactListView
        android:id="@+id/contact_list_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
