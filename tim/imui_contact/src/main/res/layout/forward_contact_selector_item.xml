<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:paddingBottom="@dimen/btn_margin_bottom"
    android:paddingRight="12dp"
    android:paddingTop="8dp"
    android:clickable="true">

    <com.tencent.qcloud.tuicore.component.RoundCornerImageView
        android:id="@+id/ivAvatar"
        android:layout_width="@dimen/contact_avatar_width"
        android:layout_height="@dimen/contact_avatar_height"
        app:corner_radius="3.2dp"
        android:scaleType="centerCrop"
        android:clickable="false"
        android:focusable="false"
        android:background="?attr/core_default_user_icon" />

</LinearLayout>

