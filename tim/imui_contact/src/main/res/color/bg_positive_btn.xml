<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true" android:color="@color/btn_positive_hover" >
        <shape xmlns:android="http://schemas.android.com/apk/res/android"
            android:shape="rectangle" >
            <corners android:radius="5dp" />
            <solid android:color="@color/btn_positive_hover" />
        </shape>
    </item>
    <item android:color="@color/btn_positive" >
        <shape xmlns:android="http://schemas.android.com/apk/res/android"
            android:shape="rectangle" >
            <corners android:radius="5dp" />
            <solid android:color="@color/btn_positive" />
        </shape>
    </item>
</selector>