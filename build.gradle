// Top-level build file where you can add configuration options common to all sub-projects/modules.
// buildscript里是gradle脚本执行所需依赖，分别是对应的maven库和插件
buildscript {
    ext.kotlin_version = '2.1.21' // 使用最新的Kotlin版本

    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
        maven { url 'https://repo1.maven.org/maven2' }
        maven { url 'https://repo.baichuan-android.taobao.com/content/groups/BaichuanRepositories/' }
        maven { url "https://mvn.mob.com/android" }
        // 配置HMS Core SDK的Maven仓地址。
        maven { url 'https://developer.huawei.com/repo/' }
        google()
    }
    dependencies {
        //gradle插件的版本号和gradle版本号是对应的，较新的插件版本需要要求较新版的gradle，所以提示你更新gradle；
        classpath 'com.android.tools.build:gradle:8.9.1' // default
        classpath 'com.jakewharton:butterknife-gradle-plugin:10.2.3' //butterknife
//        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.3'
        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.8.4' // mp3_re
        classpath 'com.getkeepsafe.dexcount:dexcount-gradle-plugin:0.5.5' // dexcount
//        classpath 'com.tencent.bugly:symtabfileuploader:latest.release' // bugly
        classpath 'com.meituan.android.walle:plugin:1.1.7'// 美团多渠道打包 Walle
//        classpath 'com.didichuxing.doraemonkit:doraemonkit-plugin:3.1.4'
//        classpath "com.mob.sdk:MobSDK:2018.0319.1724"

        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

    }
}
// 项目本身需要的依赖
allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
        maven { url "https://maven.google.com" }
        maven {
            url "${rootProject.projectDir}/google-play-services-7-8-87"
        }
        // 配置本地Maven仓库地址
        maven {
            url "${rootProject.projectDir}/repo"
        }
        // 配置HMS Core SDK的Maven仓地址。
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "https://jitpack.io" }
        maven { url 'https://repo1.maven.org/maven2' }
        maven { url "https://mirrors.tencent.com/nexus/repository/maven-public/" }
        google()
    }
}

ext {
    // Sdk and tools
    minSdkVersion = 23
    targetSdkVersion = 35  // 升级到API 35 - Google Play强制要求
    //该用哪个SDK编译，和运行时的版本号没有关系
    compileSdkVersion = 35  // 升级到API 35
    //android构建工具的版本,在SDK Manager中安装选择版本，buildToolsVersion的版本需要>=CompileSdkVersion
    buildToolsVersion = '35.0.0'

//    // google plya 版本号
//    versionCode = 322
//    versionName = "4.2.4"
}

// 应用AAR发布脚本
apply from: 'scripts/publish-all-aars.gradle'

