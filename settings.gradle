include ':tim:imui_chat', ':tim:im_push', ':tim:imui_contact'
include ':totwoo'

if (isLibTotwooLibrarySource.toBoolean()) {
    include ':totwoo_library'
}

//include ':totwoo_wear'

if (isLibEaseSource.toBoolean()) {
    include ':ease'
}

if (isLibMp3ReSource.toBoolean()) {
    include ':mp3_re'
}

if (isLibVideoRecorderSource.toBoolean()) {
    include ':VideoRecorder'
}

if (isLibFrameworkSource.toBoolean()) {
    include ':framework'
}

if (isLibTimCoreSource.toBoolean()) {
    include ':tim:imui_core'
}