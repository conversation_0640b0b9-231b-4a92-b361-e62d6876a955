<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

<!--    <permission-->
<!--        android:name="android.permission.FLASHLIGHT"-->
<!--        android:permissionGroup="android.permission-group.HARDWARE_CONTROLS"-->
<!--        android:protectionLevel="normal" />-->

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" />

</manifest>