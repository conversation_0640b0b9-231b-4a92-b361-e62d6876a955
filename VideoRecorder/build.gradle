plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion


    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'


        }
    }
    namespace 'sz.itguy.wxlikevideo'

    publishing {
        singleVariant('release') {
        }
    }
}

dependencies {
    implementation ("com.google.android.material:material:1.12.0")

    implementation fileTree(dir:'libs', include:['*.jar'])

    implementation 'org.bytedeco.javacpp-presets:ffmpeg:4.1-1.4.4'
    implementation 'org.bytedeco:javacv:1.5.7'
    implementation 'org.bytedeco:ffmpeg:5.0-1.5.7'
}

publishing {
    publications {
        maven(MavenPublication) {
            groupId = 'com.totwoo'
            artifactId = 'video-recorder'
            version = "1.0.0-${new Date().format('yyyyMMdd-HHmm')}"

            afterEvaluate {
                from components.release
            }
        }
    }
    repositories {
        maven {
            name = 'localRepo'
            url = "${rootProject.projectDir}/repo"
        }
    }
}