package com.totwoo.totwoo.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.totwoo.library.exception.DbException;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.controller.LocalNotification;
import com.totwoo.totwoo.data.AlarmCustomNotifyLogic;
import com.totwoo.totwoo.utils.PreferencesUtils;

/**
 * Created by totwoo on 2018/3/2.
 */

public class CustomNotifyAlarmService extends Service {
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int custom_id = PreferencesUtils.getInt(CustomNotifyAlarmService.this, AlarmCustomNotifyLogic.CURRENT_CUSTOM_NOTIFY_ID,-1);
        if(custom_id >= 0){
            try {
                CustomItemBean bean = AlarmCustomNotifyLogic.getInstance().getBean(custom_id);
                if(bean.getIs_open() == 1){
                    //发送给下位机闪光
                    LocalNotification.getInstance().customNotification(bean,this);
                }
            } catch (DbException e) {
                e.printStackTrace();
            }
        }
        //订下一次闹钟
        AlarmCustomNotifyLogic.getInstance().notifyAlarm();
        stopSelf();
        return super.onStartCommand(intent, flags, startId);
    }
}
