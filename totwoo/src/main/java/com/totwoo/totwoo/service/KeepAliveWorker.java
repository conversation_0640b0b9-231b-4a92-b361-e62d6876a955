//package com.totwoo.totwoo.service;
//
//import android.content.Context;
//
//import androidx.annotation.NonNull;
//import androidx.work.Worker;
//import androidx.work.WorkerParameters;
//
//import com.blankj.utilcode.util.ThreadUtils;
//import com.tencent.mars.xlog.Log;
//import com.totwoo.totwoo.ble.BluetoothManage;
//
//public class KeepAliveWorker extends Worker {
//
//    public KeepAliveWorker(@NonNull Context context, @NonNull WorkerParameters params) {
//        super(context, params);
//    }
//
//    @NonNull
//    @Override
//    public Result doWork() {
//
//
//        ThreadUtils.runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                BluetoothManage.getInstance().reconnect(false);
//
////                Context context = getApplicationContext();
////                final Intent intent = new Intent(context, KeepAliveService.class);
////                intent.setAction(BleParams.ACTION_KEEP_ALIVE);
////                ContextCompat.startForegroundService(context, intent);
//
////                setForegroundAsync(createForegroundInfo());
//                Log.e("KeepAliveService", "doWork");
//            }
//        });
//
//        return Result.success();
//    }
//
////    @NonNull
////    private ForegroundInfo createForegroundInfo() {
////        if (ToTwooApplication.mService != null) {
////            return new ForegroundInfo(KeepAliveService.FOREGROUND_SERVICE_ID, ToTwooApplication.mService.getJewelryStateNotification());
////        }
////    }
//}