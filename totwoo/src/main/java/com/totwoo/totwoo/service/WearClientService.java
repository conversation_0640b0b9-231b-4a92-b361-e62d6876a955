package com.totwoo.totwoo.service;

import android.graphics.Bitmap;

import com.bumptech.glide.Glide;
import com.google.android.gms.wearable.Asset;
import com.google.android.gms.wearable.PutDataMapRequest;
import com.google.gson.Gson;
import com.totwoo.library.db.sqlite.Selector;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.LogUtils;
import com.totwoo.library.util.PublicConstant;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ConstellationDataModel;
import com.totwoo.totwoo.bean.MessageBean;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.data.TotwooLogic;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.wearClientUtil.TeleportClient;
import com.totwoo.totwoo.utils.wearClientUtil.TeleportService;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

public class WearClientService extends TeleportService {

    private TotwooLogic totwooLogic;

    private Gson gson;


    public WearClientService() {
    }

    private TeleportClient mTeleportClient;

    @Override
    public void onCreate() {
        super.onCreate();

        //The quick way is to use setOnGetMessageTask, and set a new task

        LogUtils.i("init message");
        if (ToTwooApplication.mTeleportClient == null) {
            mTeleportClient = new TeleportClient(this);
            mTeleportClient.connect();
        } else {
            mTeleportClient = ToTwooApplication.mTeleportClient;
        }

        setOnGetMessageTask(new GetMessageAction());

        gson = new Gson();

        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    public class GetMessageAction implements Action1<String> {

        @Override
        public void call(String path) {
            switch (path) {
                case PublicConstant.WEAR_SEND_TOTWOO:
                    sendTotwoo();

                    break;
                case PublicConstant.WEAR_SYNC_DATA:
                    Owner currOwner;

                    List<MessageBean> totwooList = new ArrayList<>();

                    String pairedIcon;

                    ConstellationDataModel mConstellationInfo;
                    if (path.equals("syncData")) {
                        if (ToTwooApplication.owner == null) {
                            currOwner = Owner.getCurrOwner();
                        } else {
                            currOwner = ToTwooApplication.owner;
                        }
                        Selector selector = Selector
                                .from(MessageBean.class)
                                .where("msg_type",
                                        "in",
                                        new int[]{MessageBean.MSG_TYPE_TOTWOO_IN,
                                                MessageBean.MSG_TYPE_TOTWOO_OUT})
                                .orderBy("send_time", true);
                        try {
                            totwooList = DbHelper.getDbUtils().findAll(selector);
                        } catch (DbException e) {
                            e.printStackTrace();
                        }
                        pairedIcon = PreferencesUtils.getString(WearClientService.this, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "");

//                        JSONObject homePage = HttpHelper.getDataCache(HttpHelper.URL_HOMEPAGE);

                        JSONObject dataCache = HttpHelper.getDataCache(HttpHelper.URL_CONSTELLATION_LIST);

                        String pairedName = PreferencesUtils.getString(WearClientService.this, CoupleLogic.PAIRED_PERSON_NICK_NAME, "");


                        PutDataMapRequest dataMapRequest = PutDataMapRequest.create(PublicConstant.SYNC_DATA);
                        ArrayList<String> dataList = new ArrayList<>();
                        dataList.add(CommonUtils.isLogin() + "");

                        if (CommonUtils.isLogin()) {
                            dataList.add(currOwner.getPairedId() + "");
                            if (totwooList != null) {
                                dataList.add(gson.toJson(totwooList));
                            } else {
                                dataList.add("");
                            }

                            dataList.add(pairedIcon);

//                            if (homePage != null) {
//                                dataList.add(homePage.toString());
//                            } else {
//                                dataList.add("");
//                            }

                            if (dataCache != null) {
                                dataList.add(dataCache.optString("today"));
                            } else {
                                dataList.add("");
                            }

                            dataList.add(pairedName);
                            dataList.add(currOwner.getNickName());
                            if (currOwner.getHeaderUrl() != null && !currOwner.getHeaderUrl().isEmpty()) {
                                try {
                                    Bitmap bitmapFromMemCache = Glide.with(WearClientService.this).asBitmap().load(currOwner.getHeaderUrl()).into(400, 400).get();
                                    Asset asset = createAssetFromBitmap(bitmapFromMemCache);
                                    dataMapRequest.getDataMap().putAsset(PublicConstant.USER_ICON_KEY, asset);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                } catch (ExecutionException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (!pairedIcon.isEmpty()) {
                                try {
                                    Bitmap bitmapFromMemCache = Glide.with(WearClientService.this).asBitmap().load(pairedIcon).into(400, 400).get();
                                    Asset asset = createAssetFromBitmap(bitmapFromMemCache);
                                    dataMapRequest.getDataMap().putAsset(PublicConstant.PAIRED_ICON_KEY, asset);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                } catch (ExecutionException e) {
                                    e.printStackTrace();
                                }
                            }
                        }

                        dataMapRequest.getDataMap().putStringArrayList(PublicConstant.WEAR_LIST_DATA, dataList);
                        mTeleportClient.syncDataItem(dataMapRequest);

                        Observable.timer(5, TimeUnit.SECONDS, AndroidSchedulers.mainThread())
                                .subscribeOn(Schedulers.newThread())
                                .subscribe(new Action1<Long>() {
                                    @Override
                                    public void call(Long aLong) {
                                        mTeleportClient.sendMessage(PublicConstant.SYNC_DATA_COMPLETE, null);
                                    }
                                });
                        break;
                    }
            }
        }
    }

    public void sendTotwoo() {
        if (totwooLogic == null) {
            totwooLogic = new TotwooLogic(this);
            totwooLogic.setTotwooSendCallBack(new TotwooLogic.TotwooSendCallBack() {
                @Override
                public void onSuccess() {
                }

                @Override
                public void onFailed(String error_msg) {
                    mTeleportClient.sendMessage("send_totwoo_failure", null);
                }
            });
        }

        totwooLogic.totwooSend(false);
    }

    private static Asset createAssetFromBitmap(Bitmap bitmap) {
        final ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteStream);
        return Asset.createFromBytes(byteStream.toByteArray());
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void receiveTotwooSend(TotwooMessage bean){
        String beanjson = gson.toJson(bean.getMessageBean());
        mTeleportClient.sendMessage(PublicConstant.WEAR_SEND_TOTWOO_SUCCESS, beanjson.getBytes());
    }
}
