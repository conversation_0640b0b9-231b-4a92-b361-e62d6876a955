package com.totwoo.totwoo.service;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.Manifest;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ServiceInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Message;
import android.os.PowerManager;
import android.text.TextUtils;
import android.view.View;
import android.widget.RemoteViews;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.RomUtils;
import com.blankj.utilcode.util.Utils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import java.io.ByteArrayOutputStream;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.huawei.hms.maps.model.LatLng;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.AlarmCustomNotifyLogic;
import com.totwoo.totwoo.data.AlarmLogic;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.keepalive.AlarmKeepAlive;
import com.totwoo.totwoo.keepalive.SystemReceiverKeepAlive;
import com.totwoo.totwoo.keepalive.WorkerKeepAlive;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DelayedOperationUtil;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.location.MyMapLocationClient;
import com.totwoo.totwoo.utils.location.MyMapLocationClientOption;

import java.util.ArrayList;
import java.util.HashMap;

import cn.jpush.android.api.JPushInterface;
import no.nordicsemi.android.support.v18.scanner.BluetoothLeScannerCompat;
import no.nordicsemi.android.support.v18.scanner.ScanCallback;
import no.nordicsemi.android.support.v18.scanner.ScanResult;

public class KeepAliveService extends Service implements SubscriberListener {

    private static final String TAG = "KeepAliveService";
    public static final int FOREGROUND_SERVICE_ID = 0x1024;
    private static final String CHANNEL_ID = "20180311";

    private final HashMap<String, Bitmap> mHeadCacheMap = new HashMap<>();

    private Handler handler;

    private boolean hasRegister = false;

    NotificationManagerCompat notificationManager;
    private AlarmKeepAlive alarmKeepAlive;

    private SystemReceiverKeepAlive systemReceiverKeepAlive = new SystemReceiverKeepAlive();

    @Override
    public void onCreate() {
        Log.e(TAG, "onCreate");
        super.onCreate();
        notificationManager = NotificationManagerCompat.from(this);
        createNotificationChannel();


        if (!hasRegister) {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
            intentFilter.addAction(Intent.ACTION_SCREEN_ON);
            intentFilter.addAction(Intent.ACTION_SCREEN_OFF);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {

                registerReceiver(systemReceiverKeepAlive, intentFilter, RECEIVER_NOT_EXPORTED);
            } else {
                registerReceiver(systemReceiverKeepAlive, intentFilter);
            }
        }
        InjectUtils.injectOnlyEvent(this);
        hasRegister = true;

        interStartForegroundService();

        // 重置业务中的提醒闹钟
        restartNotify();

        startAllKeepAliveComponents();
    }

    /**
     * 启动前台服务
     */
    private void interStartForegroundService() {
        Intent intent = new Intent(this, KeepAliveService.class);
        intent.setAction(BleParams.ACTION_REFRESH_FOREGROUND_STATUS);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && hasForegroundPermission(this)) {
            try {
                // targetSdk 31 以上, 后台开启前台服务可能会触发: ForegroundServiceStartNotAllowedException
                // https://developer.android.com/about/versions/12/behavior-changes-12#foreground-service-launch-restrictions
                startForegroundService(intent);
                startForegroundInner();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            startService(intent);
        }
    }

    private void createNotificationChannel() {
        if (notificationManager == null) {
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "TOTWOO_CONNECT",
                    NotificationManager.IMPORTANCE_HIGH
            );

            notificationManager.createNotificationChannel(serviceChannel);
        }
    }

    public Notification getJewelryStateNotification() {
        NotificationCompat.Builder builder = getStateNotificationBuilder(CHANNEL_ID);
        Intent intent = new Intent(this, HomeActivityControl.getInstance().getTagertClass());
        PendingIntent pendingIntent = PendingIntent.getActivity(this,
                33, intent,
                Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
        builder.setContentIntent(pendingIntent);
        return builder.build();
    }

    @NonNull
    private NotificationCompat.Builder getStateNotificationBuilder(String channelId) {
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, channelId);
        builder.setSilent(true)
                .setWhen(System.currentTimeMillis())
                .setCategory(Notification.CATEGORY_STATUS)
                .setOngoing(true)
                .setSmallIcon(R.drawable.ic_launcher_small);
        boolean connect = JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED;
        if (ToTwooApplication.owner == null || TextUtils.isEmpty(ToTwooApplication.owner.getPairedId())) {
            String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
            builder.setLargeIcon(BitmapFactory.decodeResource(getResources(), TextUtils.isEmpty(jewName) ?
                            R.mipmap.ic_launcher : BleParams.getJewelryResourceId(jewName)))
                    .setContentTitle("totwoo")
                    .setContentText(getBleStatue());
        } else {
            try {
                RemoteViews rv = new RemoteViews(AppUtils.getAppPackageName(), R.layout.paired_notification_layout);

                rv.setTextViewText(R.id.paired_notification_me_name_tv, owner.getNickName());
                rv.setTextViewText(R.id.paired_notification_paired_name_tv, PreferencesUtils.getString(this, CoupleLogic.PAIRED_PERSON_NICK_NAME, ""));

                String headUrl = BitmapHelper.checkRealPath(owner.getHeaderUrl());
                Bitmap myHead = mHeadCacheMap.get(headUrl);
                if (myHead != null) {
                    rv.setImageViewBitmap(R.id.paired_notification_me_head_iv, myHead);
                } else {
                    Glide.with(this).asBitmap()
                            .load(headUrl)
                            .apply(RequestOptions.bitmapTransform(new CircleCrop())
                                    .override(70, 70)
                                    .format(DecodeFormat.PREFER_RGB_565))
                            .listener(new RequestListener<Bitmap>() {
                                @Override
                                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                                    return false;
                                }

                                @Override
                                public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                                    // Glide已经处理了尺寸和格式，直接使用
                                    // 如果需要进一步优化，使用RxJava异步处理
                                    if (needsFurtherCompression(resource)) {
                                        compressAndCacheAsync(resource, headUrl, rv, R.id.paired_notification_me_head_iv);
                                    } else {
                                        // 直接使用Glide处理后的结果
                                        mHeadCacheMap.put(headUrl, resource);
                                        rv.setImageViewBitmap(R.id.paired_notification_me_head_iv, resource);
                                    }
                                    return false;
                                }
                            }).submit();
                }

                String pairedHeadUrl = BitmapHelper.checkRealPath(PreferencesUtils.getString(this, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, ""));
                Bitmap otherHead = mHeadCacheMap.get(pairedHeadUrl);
                if (otherHead != null) {
                    rv.setImageViewBitmap(R.id.paired_notification_paired_head_iv, otherHead);
                } else {
                    Glide.with(this).asBitmap()
                            .load(pairedHeadUrl)
                            .apply(RequestOptions.bitmapTransform(new CircleCrop())
                                    .override(70, 70)
                                    .format(DecodeFormat.PREFER_RGB_565))
                            .listener(new RequestListener<Bitmap>() {
                                @Override
                                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                                    return false;
                                }
                                @Override
                                public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                                    // Glide已经处理了尺寸和格式，直接使用
                                    if (needsFurtherCompression(resource)) {
                                        compressAndCacheAsync(resource, pairedHeadUrl, rv, R.id.paired_notification_paired_head_iv);
                                    } else {
                                        // 直接使用Glide处理后的结果
                                        mHeadCacheMap.put(pairedHeadUrl, resource);
                                        rv.setImageViewBitmap(R.id.paired_notification_paired_head_iv, resource);
                                    }
                                    return false;
                                }
                            }).submit();
                }

                String jewInfoStr = getBleStatue();
                rv.setTextViewText(R.id.paired_notification_me_jewelry_state_tv, jewInfoStr);

                int resId = connect ? R.drawable.paired_notification_jew_connected_icon : R.drawable.paired_notification_jew_disconnected_icon;
                rv.setTextViewCompoundDrawables(R.id.paired_notification_me_name_tv, resId, 0, 0, 0);

                int twoCount = PreferencesUtils.getInt(this, CoupleLogic.PAIRED_TOTWOO_COUNT, -1);
                if (twoCount < 0) {
                    rv.setViewVisibility(R.id.paired_notification_totwoo_count_tv, View.INVISIBLE);
                } else {
                    rv.setViewVisibility(R.id.paired_notification_totwoo_count_tv, View.VISIBLE);
                    rv.setTextViewText(R.id.paired_notification_totwoo_count_tv, getString(R.string.love_collect, String.valueOf(twoCount)));
                }
                // 不使用 DecoratedCustomViewStyle，避免系统包裹样式引入不兼容视图
                builder.setCustomContentView(rv).setCustomBigContentView(rv);
            } catch (Throwable t) {
                // 如果自定义通知膨胀失败，回退到简单通知，避免前台服务崩溃
                Log.e(TAG, "inflate custom RemoteViews failed, fallback to simple notification: " + t.getMessage());
                String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
                builder.setLargeIcon(BitmapFactory.decodeResource(getResources(), TextUtils.isEmpty(jewName) ? R.mipmap.ic_launcher : BleParams.getJewelryResourceId(jewName)))
                        .setContentTitle("totwoo")
                        .setContentText(getBleStatue());
            }
        }

        return builder;
    }


    /**
     * 判断是否需要进一步压缩
     * Glide已经处理了尺寸和格式，只有在特殊情况下才需要进一步压缩
     */
    private boolean needsFurtherCompression(Bitmap bitmap) {
        if (bitmap == null) return false;

        // 估算bitmap的内存大小
        int bytes = bitmap.getByteCount();

        // 如果内存占用超过10KB，或者是某些特殊机型，才进行进一步压缩
        boolean needsCompression = bytes > 10 * 1024;

        Log.d(TAG, "Bitmap内存占用: " + (bytes / 1024) + "KB, 需要进一步压缩: " + needsCompression);
        return needsCompression;
    }

    /**
     * 异步压缩并缓存头像
     */
    private void compressAndCacheAsync(Bitmap bitmap, String url, RemoteViews rv, int imageViewId) {
        rx.Observable.fromCallable(() -> {
                    // 在后台线程进行质量压缩
                    return optimizeBitmapQualityForNotification(bitmap, 10 * 1024);
                })
                .subscribeOn(rx.schedulers.Schedulers.io()) // 后台线程执行
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread()) // 主线程接收结果
                .subscribe(
                        compressedBitmap -> {
                            // 成功压缩
                            if (compressedBitmap != null) {
                                mHeadCacheMap.put(url, compressedBitmap);
                                rv.setImageViewBitmap(imageViewId, compressedBitmap);
                            }
                        },
                        throwable -> {
                            mHeadCacheMap.put(url, bitmap);
                            rv.setImageViewBitmap(imageViewId, bitmap);
                        }
                );
    }

    /**
     * 尺寸压缩 - 专为通知优化
     */
    private Bitmap resizeBitmapForNotification(Bitmap original, int targetSize) {
        if (original == null) return null;

        try {
            // 计算缩放比例
            float scale = Math.min((float) targetSize / original.getWidth(),
                    (float) targetSize / original.getHeight());

            int newWidth = Math.round(original.getWidth() * scale);
            int newHeight = Math.round(original.getHeight() * scale);

            // 创建压缩后的Bitmap，使用RGB_565格式减少内存占用
            Bitmap.Config config = Bitmap.Config.RGB_565; // 强制使用RGB_565

            Bitmap resized = Bitmap.createScaledBitmap(original, newWidth, newHeight, true);

            // 如果需要转换格式且不是同一个对象
            if (resized != original && resized.getConfig() != config) {
                try {
                    Bitmap converted = resized.copy(config, false);
                    if (converted != null) {
                        if (!resized.isRecycled()) {
                            resized.recycle();
                        }
                        resized = converted;
                    }
                } catch (Exception e) {
                    Log.w(TAG, "格式转换失败，使用原格式: " + e.getMessage());
                }
            }

            // 回收原图（如果创建了新的bitmap）
            if (resized != original && !original.isRecycled()) {
                original.recycle();
            }

            return resized;
        } catch (Exception e) {
            Log.e(TAG, "尺寸压缩失败: " + e.getMessage());
            return original;
        }
    }

    /**
     * 质量优化压缩 - 借鉴鲁班压缩的质量控制思路
     * 专为RemoteViews通知场景优化
     */
    private Bitmap optimizeBitmapQualityForNotification(Bitmap bitmap, int targetBytes) {
        if (bitmap == null) return null;

        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();

            // 初始质量设置为80（针对通知场景优化）
            int quality = 80;
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);

            // 如果初始压缩就满足要求，直接返回
            if (baos.toByteArray().length <= targetBytes) {
                byte[] bytes = baos.toByteArray();
                baos.close();
                return BitmapFactory.decodeByteArray(bytes, 0, bytes.length);
            }

            // 二分法查找最佳质量值（鲁班压缩的核心算法思路）
            int minQuality = 15; // 最低质量阈值
            int maxQuality = quality;

            while (minQuality < maxQuality) {
                quality = (minQuality + maxQuality) / 2;
                baos.reset();
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);

                int currentSize = baos.toByteArray().length;
                if (currentSize <= targetBytes) {
                    minQuality = quality + 1;
                } else {
                    maxQuality = quality - 1;
                }
            }

            // 使用找到的最佳质量值
            quality = maxQuality;
            baos.reset();
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);

            byte[] bytes = baos.toByteArray();
            baos.close();

            Log.d(TAG, "通知头像质量压缩完成，质量值: " + quality + ", 大小: " + (bytes.length / 1024) + "KB");

            Bitmap result = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);

            // 回收原bitmap（如果不是同一个对象）
            if (result != bitmap && !bitmap.isRecycled()) {
                bitmap.recycle();
            }

            return result;

        } catch (Exception e) {
            Log.e(TAG, "质量压缩失败: " + e.getMessage());
            return bitmap;
        }
    }

    private String getBleStatue() {
        if (ActivityUtils.getTopActivity() == null) {
            return "";
        }
        return switch (JewInfoSingleton.getInstance().getConnectState()) {
            case JewInfoSingleton.STATE_CONNECTED ->
                    ActivityUtils.getTopActivity().getString(R.string.jewelry_connected);
            case JewInfoSingleton.STATE_DISCONNECTED ->
                    ActivityUtils.getTopActivity().getString(R.string.jewelry_disconnected);
            case JewInfoSingleton.STATE_UNPAIRED ->
                    ActivityUtils.getTopActivity().getString(R.string.connecting_jewelry_);
            default -> ActivityUtils.getTopActivity().getString(R.string.totwoo_connecting);
        };
    }

    @EventInject(eventType = S.E.E_UPDATE_PAIRED_STATE)
    public void notifyTotwooState(EventData data) {
        LogUtils.d("JewelryConnectService totwoo data change.");
        foregroundNotificationUpdate();
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CHANGE)
    public void notifyJewelryState(EventData data) {
        //设备电量变化不做刷新
        if (data != null) {
            return;
        }

        LogUtils.d("JewelryConnectService paired state change: "
                + JewInfoSingleton.getInstance().getConnectState() + " >>>>: " + PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, ""));

        foregroundNotificationUpdate();
    }

    /**
     * 更新前台通知，2s 内只触发一次，防止频繁触发
     */
    private void foregroundNotificationUpdate() {
        DelayedOperationUtil.handleMessageReceived(2000, new Runnable() {
            @Override
            public void run() {
                //Android 12 或更高版本为目标平台的应用在后台运行时无法启动前台服务
//                if (!AppUtils.isAppRunning(getPackageName())) {
//                    return;
//                }
                if (isScreenOn(ToTwooApplication.baseContext)) {
                    updateNotification();
                }
            }
        });
    }


    public synchronized void updateNotification() {
        if (notificationManager == null) {
            return;
        }

        if (RomUtils.isXiaomi()) {
            notificationManager.cancel(FOREGROUND_SERVICE_ID);
        }

        Notification notification = getJewelryStateNotification();
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
            notificationManager.notify(FOREGROUND_SERVICE_ID, notification);
        }
    }

    public boolean isScreenOn(Context context) {
        PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        return powerManager.isInteractive();
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "KeepAliveService onDestroy 开始清理资源");

        try {
            unregisterReceiver(systemReceiverKeepAlive);
        } catch (Exception e) {
            Log.e(TAG, "注销广播接收器失败: " + e.getMessage());
        }

        // 清理Handler
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
            handler = null;
        }

        // 停止前台服务
        if (notificationManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && hasForegroundPermission(this)) {
            stopForeground(true);
        }

        // 清理头像缓存
        if (mHeadCacheMap != null) {
            mHeadCacheMap.clear();
        }

        // 停止定位服务（TODO: 移至独立的LocationService）
        endLocation();

        // 清理全局引用
        ToTwooApplication.mService = null;

    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            Log.d(TAG, String.format("intent action: %s, data: %s, Extras: %s", intent.getAction(), intent.getData(), CommonUtils.bundleToString(intent.getExtras())));

            // 通知栏快捷回复按钮, 或者手表端的快捷回复
            if (TextUtils.equals(action, BleParams.ACTION_SEND_TOTWOO)) {
                BluetoothManage.getInstance().sendTotwoo(false);
            } else if (TextUtils.equals(action, BleParams.ACTION_KEEP_ALIVE)
                    || TextUtils.equals(action, BleParams.ACTION_RECONNECT)) {
                scheduleReconnect();

                // 处理息屏等系统事件触发的保活启动
                String trigger = intent.getStringExtra("trigger");
                if ("SCREEN_OFF".equals(trigger)) {
                    BluetoothManage.getInstance().startBackgroundScan();
                } else if ("SCREEN_ON".equals(trigger)) {
                    BluetoothManage.getInstance().stopBackgroundScan();
                }

            } else if (TextUtils.equals(action, BleParams.ACTION_START)) {
                if (ToTwooApplication.mService == null) {
                    ToTwooApplication.mService = this;
                }
            } else if (TextUtils.equals(action, BleParams.ACTION_REFRESH_FOREGROUND_STATUS)) {
                // 刷新前台状态（保留原有逻辑）
            } else if (TextUtils.equals(action, BleParams.ACTION_BLE_SCAN_NOTIFY)) {
                dealBleScanNotify(intent);
            }
        }

        if (NotifyUtil.getAppNotifyModel(ToTwooApplication.baseContext).isNotifySwitch()) {
            LogUtils.e("JewelryConnectService toggleNotificationListenerService");
            toggleNotificationListenerService();
            CommonUtils.isNotificationServiceEnabled();
        }
        return START_STICKY;
    }

    private void startForegroundInner() {
        try {
            Notification stateNotification = getJewelryStateNotification();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                try {
                    // targetSdk 31 以上, 后台开启前台服务可能会触发: ForegroundServiceStartNotAllowedException
                    // https://developer.android.com/about/versions/12/behavior-changes-12#foreground-service-launch-restrictions
                    startForeground(FOREGROUND_SERVICE_ID, stateNotification,
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                startForeground(FOREGROUND_SERVICE_ID, stateNotification);
            }
            LogUtils.e("开启通知");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理后台BLE 扫描的处理结果
     *
     * @param intent
     */
    private void dealBleScanNotify(Intent intent) {
        //  one or more of the extras EXTRA_CALLBACK_TYPE, EXTRA_ERROR_CODE and EXTRA_LIST_SCAN_RESULT to indicate the result of the scan.
        int errorCode = intent.getIntExtra(BluetoothLeScannerCompat.EXTRA_ERROR_CODE, 0);
        ArrayList<ScanResult> results = intent.getParcelableArrayListExtra(BluetoothLeScannerCompat.EXTRA_LIST_SCAN_RESULT);

        Log.d(TAG, "后台扫描回调: " + errorCode + ", results: " + results);
        if (errorCode == ScanCallback.SCAN_FAILED_ALREADY_STARTED) {
            BluetoothManage.getInstance().stopBackgroundScan();
            BluetoothManage.getInstance().startBackgroundScan();
        } else if (errorCode == ScanCallback.SCAN_FAILED_SCANNING_TOO_FREQUENTLY) {
            // 3 分钟后重试
            new Handler().postDelayed(() -> BluetoothManage.getInstance().startBackgroundScan(), 3 * 60 * 1000);
        } else if (errorCode != 0) {
            // 其他类型错误, 直接忽略, 不处理
            LogUtils.e("Start background ble scan failed: " + errorCode);
        }

        String address = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, null);
        String name = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, null);

        if (address == null || !BluetoothAdapter.checkBluetoothAddress(address) || TextUtils.isEmpty(name)) {
            // 如果当前未绑定首饰, 或者绑定首饰无效, 直接忽略本次结果, 且停止后台扫描
            BluetoothManage.getInstance().stopBackgroundScan();
            return;
        }

        boolean ok = false;
        if (results != null && results.size() > 0) {
            ScanResult res = results.get(0);
            if (res != null && res.getScanRecord() != null) {
                // 扫描结果不匹配, 证明扫描器对应的过滤器已经过时, 需要重新启动新的扫描任务
                if (!TextUtils.equals(res.getScanRecord().getDeviceName(), name) || !TextUtils.equals(res.getDevice().getAddress(), address)) {
                    BluetoothManage.getInstance().stopBackgroundScan();
                    BluetoothManage.getInstance().startBackgroundScan();
                } else {
                    // 连接指定设备
                    BluetoothManage.getInstance().connect(res);
                    ok = true;
                }
            }
            // 这里代表本地扫描结果无效, 直接忽略本次唤醒结果即可
        } else {
            BluetoothManage.getInstance().startBackgroundScan();
        }

        // 如果当前没有结果, 则当做普通的唤醒, 尝试重连即可
        if (!ok) {
            scheduleReconnect();
        }
        //更新去掉
//        startForegroundInner();
    }

    /**
     * 业务中的闹钟重新设置, 例如喝水提醒, 灯光提醒等
     */
    private void restartNotify() {
        if (BleParams.isSM2() || BleParams.isButtonBatteryJewelry()) {
            return;
        }

        HandlerThread handlerThread = new HandlerThread("keepAlive");
        handlerThread.start();
        handler = new Handler(handlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                if (Utils.getApp() != null) {
                    try {
                        AlarmLogic.getInstance().notifyAlarm();
                        AlarmCustomNotifyLogic.getInstance().notifyAlarm();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    handler.sendEmptyMessageDelayed(0, BleParams.NOTIFY_INTERVAL);
                    LogUtils.i("aab JPushInterface.isPushStopped(KeepAliveService.this) = " + JPushInterface.isPushStopped(KeepAliveService.this));
                }
            }
        };
        handler.sendEmptyMessage(0);
    }

    private boolean isInitService = false;

    private void toggleNotificationListenerService() {
        try {
            if (isInitService) {
                return;
            }
            isInitService = true;
            PackageManager pm = getPackageManager();

            LogUtils.e("JewelryConnectService toggleNotificationListenerService");
            pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);

            pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
            startService(new Intent(KeepAliveService.this, AppNotifyRemindService.class));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ==================== 核心保活机制区域 ====================
    // 保活逻辑已迁移到 KeepAliveManager 和 AlarmKeepAlive
    // 此处保留必要的业务逻辑接口
    // =========================================================


    public void scheduleReconnect() {
        BluetoothManage.getInstance().reconnect(false);
    }

    /**
     * 启动所有保活机制 - 已迁移到 KeepAliveManager
     */
    /**
     * 统一启动所有守护组件 - 版本适配优化
     * 职责：根据Android版本选择最优保活策略
     */
    private void startAllKeepAliveComponents() {

        WorkerKeepAlive.scheduleWork(this);

        // Android 12+ 有CompanionDevice保活，降低AlarmKeepAlive频率
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
        alarmKeepAlive = new AlarmKeepAlive(this);
        alarmKeepAlive.start();
//        }
        BluetoothManage.getInstance().startBackgroundScan();
    }


    /**
     * 统一停止所有守护组件
     */
    public void stopAllKeepAliveComponents() {
        Log.d(TAG, "🛑 统一停止所有守护组件");
        // 1. 停止WorkManager任务
        WorkerKeepAlive.cancelWork(this);
        // 2. 停止AlarmKeepAlive（如果有实例的话）
        stopAlarmKeepAlive();

        BluetoothManage.getInstance().stopBackgroundScan();
    }


    /**
     * 停止AlarmKeepAlive
     */
    private void stopAlarmKeepAlive() {
        if (alarmKeepAlive != null) {
            alarmKeepAlive.stop();
        }
    }


    // ==================== 业务功能区域 ====================
    // 以下代码为业务相关功能，与保活机制无关
    // 包括：位置服务、业务闹钟、事件处理等
    // ========================================================

    private boolean isLocate;

    private LatLng myLocationLatLng;
    //声明mlocationClient对象
    public MyMapLocationClient mlocationClient;
    //声明mLocationOption对象
    public MyMapLocationClientOption mLocationOption = null;

    private long time;
    private String guard_id;

    public void startLocation(long restTime, String guard_id) {
        if (isLocate) {
            return;
        }
        try {
            this.time = restTime;
            this.guard_id = guard_id;
            if (mlocationClient == null) {
                mlocationClient = MyMapLocationClient.newLocationClient(this);

                //初始化定位参数
                mLocationOption = new MyMapLocationClientOption();
                //设置定位监听
                mlocationClient.setLocationListener(aMapLocation -> {
                    LogUtils.e("aab aMapLocation get");
                    if (aMapLocation.getErrorCode() == 0) {
                        myLocationLatLng = new LatLng(aMapLocation.getLatitude(), aMapLocation.getLongitude());
                    }
                });
                //设置定位模式为高精度模式，Battery_Saving为低功耗模式，Device_Sensors是仅设备模式
                mLocationOption.setLocationMode(MyMapLocationClientOption.MyMapLocationMode.PRIORITY_HIGH_ACCURACY);
                //设置定位间隔,单位毫秒,默认为2000ms
                mLocationOption.setInterval(30000);
                //设置定位参数
                mlocationClient.setLocationOption(mLocationOption);
            }
            mlocationClient.startLocation();
            isLocate = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private MyBinder mBinder = new MyBinder();

    public class MyBinder extends Binder {
        public KeepAliveService getService() {
            return KeepAliveService.this;
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return mBinder;
    }


    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    public void endLocation() {
        if (mlocationClient != null) {
            mlocationClient.stopLocation();
        }
        isLocate = false;
    }


    public final boolean hasForegroundPermission(Context context) {
        if (Build.VERSION.SDK_INT < 34 || ContextCompat.checkSelfPermission(context, "android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE") != PackageManager.PERMISSION_DENIED) {
            return true;
        }
        return false;
    }

}
