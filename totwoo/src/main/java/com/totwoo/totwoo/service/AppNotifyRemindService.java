package com.totwoo.totwoo.service;

import android.annotation.SuppressLint;
import android.os.Build;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.activity.AppNotificationsActivity;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.List;

import rx.Observable;

/**
 * Created by <PERSON><PERSON><PERSON>ow<PERSON> on 16/9/9.
 */
@SuppressLint("OverrideAbstract")
public class AppNotifyRemindService extends NotificationListenerService {

    private List<String> allowNotifyAppPackages;

    private int reCloseFlashCount;


    @Override
    public void onCreate() {
        LogUtils.e("JewelryConnectService toggleNotificationListenerService");

        super.onCreate();
    }

    @Override
    public void onDestroy() {
//        toggleNotificationListenerService();
        super.onDestroy();
    }

    @Override
    public void onNotificationPosted(final StatusBarNotification sbn) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
            LogUtils.e("aab sbn.getNotification().extras.toString() = " + sbn.getNotification().extras.toString());
        }
        LogUtils.e("aab sbn.getPackageName() = " + sbn.getPackageName());
        LogUtils.e("aab sbn.getNotification().tickerText = " + sbn.getNotification().tickerText);


        allowNotifyAppPackages = new Gson().fromJson(PreferencesUtils.getString(getBaseContext(), AppNotificationsActivity.APP_NOTIFY_REMIND_PACKAGES, ""), new TypeToken<List<String>>() {
        }.getType());

        final JewelryNotifyModel mdd = NotifyUtil.getAppNotifyModel(this);
        if (allowNotifyAppPackages == null
                || !mdd.isNotifySwitch()
                || !CommonUtils.isLogin()
                || (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED)) {
            return;
        }


        Observable.from(allowNotifyAppPackages)
                .filter(s -> s.equals(sbn.getPackageName())).subscribe(s -> {
                    reCloseFlashCount = 0;
                    BluetoothManage.getInstance().notifyJewelry(mdd.getVibrationSeconds()
                            , mdd.getFlashColorValue());
                });
        super.onNotificationPosted(sbn);
    }

//    private void toggleNotificationListenerService() {
//        PackageManager pm = getPackageManager();
//        pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
//                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
//
////        pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
////                PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
//    }
}
