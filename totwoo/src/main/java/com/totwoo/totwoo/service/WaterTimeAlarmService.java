package com.totwoo.totwoo.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.holderBean.HomeWaterTimeBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.controller.LocalNotification;
import com.totwoo.totwoo.data.AlarmLogic;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;

/**
 * Created by totwoo on 2018/3/2.
 */

public class WaterTimeAlarmService extends Service {
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (PreferencesUtils.getBoolean(WaterTimeAlarmService.this,
                NotifyUtil.WATER_TIME_SWITCH_KEY, true) && CommonUtils.isLogin() && BleParams.isReminderJewlery()) {
            //发送给下位机闪光
            JewelryNotifyModel nowSetModel = NotifyUtil.getWaterTimeNotifyModel(ToTwooApplication.baseContext);
            BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
            //启动通知
            LocalNotification.getInstance().waterTimeNotification(new HomeWaterTimeBean(true), this);
            //订下一次闹钟
            AlarmLogic.getInstance().notifyAlarm();
            stopSelf();
        }
        return super.onStartCommand(intent, flags, startId);
    }
}
