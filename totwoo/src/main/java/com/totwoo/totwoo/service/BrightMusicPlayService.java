package com.totwoo.totwoo.service;

import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_PART_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_VALUE;

import android.app.Service;
import android.content.Intent;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.bean.MusicIndexBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.io.IOException;

public class BrightMusicPlayService extends Service implements SubscriberListener {
    private MediaPlayer mediaPlayer;

    @Override
    public void onCreate() {
        super.onCreate();
        InjectUtils.injectOnlyEvent(this);
        mediaPlayer = new MediaPlayer();
        mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
    }

    //播放闪光音乐模式的预览
    @EventInject(eventType = S.E.E_MUSIC_PLAY_START, runThread = TaskType.UI)
    public void musicPlayStart(EventData data) {
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_END, null);
                mediaPlayer.reset();
            }
        });

        int musicIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, MUSIC_VALUE, 0);
        int brightIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, COLOR_VALUE, -1);
        brightIndex = Math.abs(brightIndex) - 1;

        if (brightIndex > 0) {
            playMusic(musicIndex);
        } else {
            playMusic(0);
        }
    }

    //播放闪光音乐模式的预览
    @EventInject(eventType = S.E.E_MUSIC_PLAY_START_PART, runThread = TaskType.UI)
    public void musicPlayStartPart(EventData data) {
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_END, null);
                mediaPlayer.reset();
            }
        });
        int musicIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, MUSIC_PART_VALUE, 0);
        int brightIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, COLOR_VALUE, -1);
        brightIndex = Math.abs(brightIndex) - 1;

        if (brightIndex > 0) {
            if (musicIndex == 1) {
                musicIndex = 6;
                playMusic(musicIndex);
            } else {
                playMusic(musicIndex);
            }
        } else {
            playMusic(0);
        }
    }

    //播放闪光音乐模式
    @EventInject(eventType = S.E.E_MUSIC_PLAY_PLAY, runThread = TaskType.UI)
    public void musicPlay(EventData data) {
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                mediaPlayer.start();
            }
        });
        int musicIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, MUSIC_VALUE, 0);
        int brightIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, COLOR_VALUE, -1);
        brightIndex = Math.abs(brightIndex) - 1;

        if (brightIndex > 0) {
            playMusic(musicIndex);
        } else {
            playMusic(0);
        }
    }

    //播放闪光音乐模式
    @EventInject(eventType = S.E.E_MUSIC_PLAY_PLAY_PART, runThread = TaskType.UI)
    public void musicPlayPart(EventData data) {
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                mediaPlayer.start();
            }
        });
        int musicIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, MUSIC_PART_VALUE, 0);
        int brightIndex = PreferencesUtils.getInt(BrightMusicPlayService.this, COLOR_VALUE, -1);
        brightIndex = Math.abs(brightIndex) - 1;

        if (brightIndex > 0) {
            if (musicIndex == 1) {
                musicIndex = 6;
                playMusic(musicIndex);
            } else {
                playMusic(musicIndex);
            }
        } else {
            playMusic(0);
        }
    }

    //播放totwoo、来电音乐
    @EventInject(eventType = S.E.E_MUSIC_PLAY_ONCE, runThread = TaskType.UI)
    public void musicPlayOnce(EventData data) {
        MusicIndexBean musicIndexBean = (MusicIndexBean) data;
        mediaPlayer.setOnCompletionListener(mp -> mediaPlayer.reset());

        int musicIndex = musicIndexBean.getMusic_index();
        playMusic(musicIndex);
    }

    @EventInject(eventType = S.E.E_MUSIC_PLAY_STOP, runThread = TaskType.UI)
    public void musicStop(EventData data) {
        try {
            if (mediaPlayer.isPlaying()) {
                mediaPlayer.pause();
                mediaPlayer.reset();
            }
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    @EventInject(eventType = S.E.E_MUSIC_PLAY_STOP_BY_JEW, runThread = TaskType.UI)
    public void musicPlayStop(EventData data) {
        if (!BleParams.isMWJewlery()) {
            return;
        }
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_END, null);
        try {
            if (mediaPlayer.isPlaying()) {
                mediaPlayer.pause();
                mediaPlayer.reset();
            }
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    public void playMusic(int musicIndex) {
        if (mediaPlayer.isPlaying()) {
            mediaPlayer.pause();
            mediaPlayer.reset();
        }
        if (musicIndex != 0) {
            try {
                mediaPlayer.setDataSource(this, getMusicRes(musicIndex));
                mediaPlayer.prepareAsync();
                mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                    @Override
                    public void onPrepared(MediaPlayer mp) {
                        mediaPlayer.start();
                    }
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private Uri getMusicRes(int index) {
        int musicResId = 0;
        switch (index) {
            case 1:
                musicResId = R.raw.the_film;
                break;
            case 2:
                musicResId = R.raw.bass;
                break;
            case 3:
                musicResId = R.raw.popular;
                break;
            case 4:
                musicResId = R.raw.pure_music;
                break;
            case 5:
                musicResId = R.raw.vocal;
                break;
            case 6:
                musicResId = R.raw.birthday;
                break;
            case 7:
                musicResId = R.raw.electronic;
                break;
            case 8:
                musicResId = R.raw.love_you;
                break;
            case 9:
                musicResId = R.raw.iphone_ring;
                break;
            case 10:
                musicResId = R.raw.fake_call_ring;
                break;

        }
        String uri = "android.resource://" + getPackageName() + "/" + musicResId;
        return Uri.parse(uri);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mediaPlayer != null) {
            mediaPlayer.release();
        }
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_DESTROY, null);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
