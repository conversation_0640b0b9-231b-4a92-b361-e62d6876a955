package com.totwoo.totwoo.service;

import android.app.Service;
import android.content.Intent;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;

import androidx.annotation.Nullable;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.bean.FaceBookSharePathEventData;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.ShakeMonitor;

/**
 * Created by totwoo on 2018/5/22.
 */

/**
 * Messenger的服务端
 * Totwoo作为服务端，虚拟人作为客户端
 */
public class MessengerService extends Service implements ShakeMonitor.OnEventListener{
    private static final int MSG_JEWELRY_START = 2;
    private static final int MSG_JEWELRY_CLICK = 3;
    private static final int MSG_JEWELRY_TOUCH = 4;
    private static final int MSG_FACEBOOK_IMAGE = 5;
    private static final int MSG_FACEBOOK_VIDEO = 6;
    private ShakeMonitor shakeMonitor;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return mMessenger.getBinder();
    }

    private Messenger mMessenger;
    private Messenger clientMessenger;
    private Handler handler;

    @Override
    public void onCreate() {
        super.onCreate();
        HandlerThread handlerThread = new HandlerThread("MessengerService");
        handlerThread.start();
        handler = new Handler(handlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msgfromClient) {
                Message clientMessage = msgfromClient;
                Message msgToClient = Message.obtain(clientMessage);
                switch (clientMessage.what) {
                    //msg 客户端传来的消息
                    case MSG_JEWELRY_START:
                        msgToClient.what = MSG_JEWELRY_START;
                        try {
                            if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                                msgToClient.arg1 = 1;
                            }else{
                                msgToClient.arg1 = 0;
                            }
                            clientMessenger = clientMessage.replyTo;
                            clientMessenger.send(msgToClient);

                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                        checkAndSetShakeState();
                        break;
                    case MSG_JEWELRY_CLICK:
                        try {
                            msgToClient.what = MSG_JEWELRY_CLICK;
                            clientMessenger.send(msgToClient);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                        break;
                    case MSG_JEWELRY_TOUCH:
                        try {
                            msgToClient.what = MSG_JEWELRY_TOUCH;
                            clientMessenger.send(msgToClient);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                        break;
                    case MSG_FACEBOOK_IMAGE:
                        LogUtils.e("aab MSG_FACEBOOK_IMAGE");
                        String path = (String) clientMessage.getData().get("path");
                        LogUtils.e("aab path = " + path);
                        FaceBookSharePathEventData faceBookSharePath = new FaceBookSharePathEventData();
                        faceBookSharePath.setPath(path);
                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_VIS_FACEBOOK_IMAGE, faceBookSharePath);
                        break;
                    case MSG_FACEBOOK_VIDEO:
                        LogUtils.e("aab MSG_FACEBOOK_VIDEO");
                        String vedioPath = (String) clientMessage.getData().get("path");
                        LogUtils.e("aab vedioPath = " + vedioPath);
                        FaceBookSharePathEventData faceBookVedioSharePath = new FaceBookSharePathEventData();
                        faceBookVedioSharePath.setPath(vedioPath);
                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_VIS_FACEBOOK_VEDIO, faceBookVedioSharePath);

                        break;

                }
                super.handleMessage(msgfromClient);
            }
        };
        mMessenger = new Messenger(handler);
    }

    private void checkAndSetShakeState() {
        if (shakeMonitor == null) {
            shakeMonitor = new ShakeMonitor(this);
            shakeMonitor.setOnEventListener(this);
        }
        shakeMonitor.start();
    }

    @Override
    public void onEvent(int type) {
        LogUtils.e("aab MessengerService onShake");
        Message clickMessage = Message.obtain();
        if (type == 1) {
            clickMessage.what = MSG_JEWELRY_CLICK;
        } else {
            clickMessage.what = MSG_JEWELRY_TOUCH;
        }
        handler.sendMessage(clickMessage);
    }

    @Override
    public boolean onUnbind(Intent intent) {
        if (shakeMonitor != null) {
            shakeMonitor.stop();
        }
        return super.onUnbind(intent);
    }
}
