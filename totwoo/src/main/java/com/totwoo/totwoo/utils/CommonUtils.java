package com.totwoo.totwoo.utils;

import static android.content.Context.NOTIFICATION_SERVICE;
import static com.google.android.gms.common.util.IOUtils.copyStream;
import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.hardware.Camera;
import android.location.LocationManager;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.FileUtils;
import android.os.PowerManager;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.annotation.StringRes;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.widget.ImageViewCompat;
import androidx.exifinterface.media.ExifInterface;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileIOUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.Utils;
import com.tencent.mars.xlog.Log;
import com.tencent.mars.xlog.Xlog;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.BuildConfig;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.JewelryPairedListActivity;
import com.totwoo.totwoo.activity.JewelrySelectActivity;
import com.totwoo.totwoo.activity.StepTargetSettingActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.activity.WeiboAuthActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.bean.BQItemDataBean;
import com.totwoo.totwoo.bean.DataCache;
import com.totwoo.totwoo.bean.LoginInfoBean;
import com.totwoo.totwoo.bean.MeanBean;
import com.totwoo.totwoo.bean.MessageBean;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.bean.PeriodBean;
import com.totwoo.totwoo.bean.Step;
import com.totwoo.totwoo.bean.Uv;
import com.totwoo.totwoo.bean.WaterInfoBean;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomTypefaceSpan;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.TagAliasCallback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/4/11.
 */

public class CommonUtils {
    //1000:想你，1002:需要你，1006:伤心 sad，1004:对不起 sorry，1009:我爱你; 1010 : Hug   1011: kisses   1012 : i miss you
// 1013: I'm safe   1014 Sleep well!   1015:  i've arrived     1016: Are you ok?
    public static final String CONTENT_TOTWOO = "1000";
    public static final String CONTENT_MISS = "1000";
    public static final String CONTENT_NEED_YOU = "1002";
    public static final String CONTENT_PAIN = "1003";
    public static final String CONTENT_SORRY = "1004";
    public static final String CONTENT_SAD = "1006";
    public static final String CONTENT_LOVE = "1009";

    public static final String CONTENT_HUG = "1010";
    public static final String CONTENT_KISSES = "1011";
    public static final String CONTENT_SINIAN = "1012";
    public static final String CONTENT_SAFE = "1013";
    public static final String CONTENT_SLEEP = "1014";
    public static final String CONTENT_ARRIVED = "1015";
    public static final String CONTENT_OK = "1016";

    public final static String SHOW_BQ_CUSTOM__GUIDE_TAG = "show_bq_custom__guide_tag";


    public static String getPagPath(String content) {
        switch (content) {
            case CommonUtils.CONTENT_MISS:
                return "pag/pag_aixin.pag";
            case CommonUtils.CONTENT_NEED_YOU:
                return "pag/pag_xiong.pag";
            case CommonUtils.CONTENT_LOVE:
                return "pag/pag_yun.pag";
            case CommonUtils.CONTENT_SAD:
                return "pag/pag_sad.pag";
            case CommonUtils.CONTENT_SORRY:
                return "pag/pag_sorry.pag";
            default:
                return "pag/pag_aixin.pag";
        }
    }

    /**
     * 获取自定义表情图片
     *
     * @param emoji_encoding
     * @return
     */
    public static int getImg(String emoji_encoding) {
        int img = R.drawable.custom_bq_xin;
        if (TextUtils.isEmpty(emoji_encoding)) {
            return img;
        }


        img = switch (emoji_encoding) {
            case CONTENT_MISS -> R.drawable.custom_bq_xin;
            case CONTENT_NEED_YOU -> R.drawable.custom_bq_xiong;
            case CONTENT_LOVE -> R.drawable.custom_bq_yun;
            case CONTENT_SAD -> R.drawable.custom_bq_sad;
            case CONTENT_SORRY -> R.drawable.custom_bq_sorry;
            case CONTENT_PAIN -> R.drawable.bq_pain_big;
            default -> R.drawable.custom_bq_xin;
        };
        return img;
    }


    public static ArrayList<MeanBean> getRecommendedMeaning() {
        ArrayList<MeanBean> tags = new ArrayList<>();
        tags.add(new MeanBean(CONTENT_MISS, compantGetString(R.string.bq_totwoo)));
        tags.add(new MeanBean(CONTENT_NEED_YOU, compantGetString(R.string.bq_1002)));
        tags.add(new MeanBean(CONTENT_SAD, compantGetString(R.string.bq_1006)));
        tags.add(new MeanBean(CONTENT_SORRY, compantGetString(R.string.bq_1004)));
        tags.add(new MeanBean(CONTENT_LOVE, compantGetString(R.string.bq_love)));

        tags.add(new MeanBean(CONTENT_HUG, compantGetString(R.string.bq_1010)));
        tags.add(new MeanBean(CONTENT_KISSES, compantGetString(R.string.bq_1011)));
        tags.add(new MeanBean(CONTENT_SINIAN, compantGetString(R.string.bq_1012)));
        tags.add(new MeanBean(CONTENT_SAFE, compantGetString(R.string.bq_1013)));
        tags.add(new MeanBean(CONTENT_SLEEP, compantGetString(R.string.bq_1014)));
        tags.add(new MeanBean(CONTENT_ARRIVED, compantGetString(R.string.bq_1015)));
        tags.add(new MeanBean(CONTENT_OK, compantGetString(R.string.bq_1016)));
        return tags;
    }


    public static String compantGetString(@StringRes int resId) {
        //多语言 12以下不生效a
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2) {
            if (ActivityUtils.getTopActivity() != null) {
                return ActivityUtils.getTopActivity().getString(resId);
            }
            return Utils.getApp().getString(resId);
        } else {
            return Utils.getApp().getString(resId);
        }
    }

    /**
     * 默认表情含义
     *
     * @return
     */
    public static ArrayList<BQItemDataBean> getDefaultBQ() {
        ArrayList<BQItemDataBean> emolist = new ArrayList<>(5);
        emolist.add(new BQItemDataBean(CommonUtils.CONTENT_MISS, CommonUtils.CONTENT_MISS));
        emolist.add(new BQItemDataBean(CommonUtils.CONTENT_NEED_YOU, CommonUtils.CONTENT_NEED_YOU));
        emolist.add(new BQItemDataBean(CommonUtils.CONTENT_LOVE, CommonUtils.CONTENT_LOVE));
        emolist.add(new BQItemDataBean(CommonUtils.CONTENT_SORRY, CommonUtils.CONTENT_SORRY));
        emolist.add(new BQItemDataBean(CommonUtils.CONTENT_SAD, CommonUtils.CONTENT_SAD));
        return emolist;
    }



    //设置textview 加粗程度
    public static void setStrokeWidth(TextView textView, float width) {
        TextPaint paint = textView.getPaint();
        if (paint != null) {
            paint.setStrokeWidth(width);
            paint.setStyle(Paint.Style.FILL_AND_STROKE);
        }
    }

    public static String hexAsciiToString(String hexPart) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < hexPart.length(); i += 2) {
            String hexByte = hexPart.substring(i, i + 2); // 每两个字符一组
            int asciiValue = Integer.parseInt(hexByte, 16); // 将16进制转换为10进制
            if (asciiValue > 0) { // 忽略 ASCII 值为 0 的字符
                result.append((char) asciiValue); // 转换为字符并追加
            }
        }
        return result.toString();
    }


    /**
     * 获取自定义表情含义内容
     *
     * @param feel_type  推荐的含义码，或者自定义的含义内容
     * @return  含义对应的内容
     */
    public static String getCustomBQMeaning(String feel_type) {
        for (MeanBean meanBean : getRecommendedMeaning()) {
            if (TextUtils.equals(feel_type, meanBean.getMeaningCode())) {
                return meanBean.getContent();
            }
        }
        return feel_type;
    }

    public static boolean isCameraCanUse() {
        boolean canUse = true;
        Camera mCamera = null;
        try {
            mCamera = Camera.open(0);
            Camera.Parameters mParameters = mCamera.getParameters();
            mCamera.setParameters(mParameters);
        } catch (Exception e) {
            canUse = false;
        }
        if (canUse) {
            mCamera.release();
            mCamera = null;
        }
        //Timber.v("isCameraCanuse="+canUse);
        return canUse;
    }

    // 音频获取源
    public static int audioSource = MediaRecorder.AudioSource.MIC;
    // 设置音频采样率，44100是目前的标准，但是某些设备仍然支持22050，16000，11025
    public static int sampleRateInHz = 44100;
    // 设置音频的录制的声道CHANNEL_IN_STEREO为双声道，CHANNEL_CONFIGURATION_MONO为单声道
    public static int channelConfig = AudioFormat.CHANNEL_IN_STEREO;
    // 音频数据格式:PCM 16位每个样本。保证设备支持。PCM 8位每个样本。不一定能得到设备支持。
    public static int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
    // 缓冲区字节大小
    public static int bufferSizeInBytes = 0;

    /**
     * 判断是是否有录音权限
     */
    @SuppressLint("MissingPermission")
    public static boolean HasRecordSoundPermission(final Context context) {
        bufferSizeInBytes = 0;
        bufferSizeInBytes = AudioRecord.getMinBufferSize(sampleRateInHz, channelConfig, audioFormat);
        AudioRecord audioRecord = new AudioRecord(audioSource, sampleRateInHz, channelConfig, audioFormat, bufferSizeInBytes);
        //开始录制音频
        try {
            // 防止某些手机崩溃，例如联想
            audioRecord.startRecording();
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
        /**
         * 根据开始录音判断是否有录音权限
         */
        if (audioRecord.getRecordingState() != AudioRecord.RECORDSTATE_RECORDING) {
            return false;
        }
        audioRecord.stop();
        audioRecord.release();
        audioRecord = null;

        return true;
    }


    public static int getScreenWidth() {
        Resources resources = ToTwooApplication.baseContext.getResources();
        DisplayMetrics dm = resources.getDisplayMetrics();
        return dm.widthPixels;
    }

    public static int getScreenHeight() {
        Resources resources = ToTwooApplication.baseContext.getResources();
        DisplayMetrics dm = resources.getDisplayMetrics();
        return dm.heightPixels;
    }

    public static int getRealHeight() {
        WindowManager windowManager = (WindowManager) ToTwooApplication.baseContext.getSystemService(Context.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        DisplayMetrics dm = new DisplayMetrics();
        display.getRealMetrics(dm);
        return dm.heightPixels;
    }

    public static CharSequence setNumberGoldenSpan(String text, int number, int size) {
        Typeface typefaceGithic = ResourcesCompat.getFont(ToTwooApplication.baseContext, R.font.gothicb);
        CustomTypefaceSpan tfspan = new CustomTypefaceSpan(typefaceGithic);

        SpannableString spannableString = new SpannableString(text);
        int index = spannableString.toString().indexOf(number + "");
        spannableString.setSpan(new AbsoluteSizeSpan(size, true), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(tfspan, index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#c7ad92")), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        return spannableString;
    }

    public static CharSequence setNumberOrangeSpan(String text, int number, int size) {
        Typeface typefaceGithic = ResourcesCompat.getFont(ToTwooApplication.baseContext, R.font.gothicb);
        CustomTypefaceSpan tfspan = new CustomTypefaceSpan(typefaceGithic);

        SpannableString spannableString = new SpannableString(text);
        int index = spannableString.toString().indexOf(number + "");
        spannableString.setSpan(new AbsoluteSizeSpan(size, true), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(tfspan, index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#FF5757")), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        return spannableString;
    }

    public static CharSequence setNumberNormalGoldenSpan(String text, int number, int size) {
        Typeface typefaceGithic = ResourcesCompat.getFont(ToTwooApplication.baseContext, R.font.gothicb);
        CustomTypefaceSpan tfspan = new CustomTypefaceSpan(typefaceGithic);

        SpannableString spannableString = new SpannableString(text);
        int index = spannableString.toString().indexOf(number + "");
        spannableString.setSpan(new AbsoluteSizeSpan(size, true), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(tfspan, index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#e5cf7f")), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        return spannableString;
    }

    /**
     * dp转化为px
     *
     * @param context  context
     * @param dipValue dp value
     * @return 转换之后的px值
     */
    public static int dip2px(Context context, float dipValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }

    public static boolean isLogin() {
        if (ToTwooApplication.baseContext == null || owner == null) {
            return false;
        }
        boolean spLogin = PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.USER_LOGIN, false);
        return spLogin || owner.isLogin();
    }

    public static void setLogin(boolean isLogin) {
        PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.USER_LOGIN, isLogin);
        owner.setLogin(isLogin);
    }

    private static final String ENABLED_NOTIFICATION_LISTENERS = "enabled_notification_listeners";

    /**
     * Is Notification Service Enabled.
     * Verifies if the notification listener service is enabled.
     * Got it from: <a href="https://github.com/kpbird/NotificationListenerService-Example/blob/master/NLSExample/src/main/java/com/kpbird/nlsexample/NLService.java">NLService.java</a>
     *
     * @return True if eanbled, false otherwise.
     */
    public static boolean isNotificationServiceEnabled() {
        String pkgName = ToTwooApplication.baseContext.getPackageName();
        final String flat = Settings.Secure.getString(ToTwooApplication.baseContext.getContentResolver(), ENABLED_NOTIFICATION_LISTENERS);
        if (!TextUtils.isEmpty(flat)) {
            final String[] names = flat.split(":");
            for (int i = 0; i < names.length; i++) {
                final ComponentName cn = ComponentName.unflattenFromString(names[i]);
                if (cn != null) {
                    if (TextUtils.equals(pkgName, cn.getPackageName())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }


    /**
     * zy：如果是00或者是+开头的，去掉00、+直接上传给服务器
     * 如果是登录地区码开头的手机号，就直接把号码上传
     * 如果是别的情况，就直接加上登录地区码上传
     * 可能存在bug，需告知客户填写成正确的手机码才能使用
     *
     * @param number 本地手机号
     * @return 上传给服务器的手机号
     */
    public static String checkPhoneNumber(String number) {
        if (TextUtils.isEmpty(number)) {
            return null;
        }
        if (TextUtils.isEmpty(number)) {
            return null;
        } else if (number.startsWith("00")) {
            return number.substring(2);
        } else if (number.startsWith("+")) {
            return number.substring(1);
        } else if (number.startsWith(PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? "86" : "1"))) {
            return number;
        } else {
            return PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? "86" : "1") + number;
        }
    }


    /**
     * 取出手机号码中的无效字符, 返回简单的号码
     *
     * @param phone
     * @return
     */
    public static String getSimplePhone(String phone) {
        if (phone == null) {
            return null;
        }
        // 过滤掉号码 中的空格和分割线
        char[] pp = new char[phone.length()];
        int j = 0;
        for (int i = 0; i < phone.length(); i++) {
            char c = phone.charAt(i);
            if (c != '-' && c != ' ' && c != '(' && c != ')') {
                pp[j++] = c;
            }
        }
        return new String(pp, 0, j);
    }

    /**
     * 从短信内容中提取验证码
     *
     * @param msg
     * @return
     */
    public static String extractVerCode(String msg) {
        // 回填临时验证码, 从 stringHttpBaseBean.getErrorMsg() 提取连续的 4~6 位验证码
        Pattern p = Pattern.compile("\\d{4,6}");
        Matcher m = p.matcher(msg);
        if (m.find()) {
            return m.group();
        }
        return null;
    }

    /**
     * 获取验证码前, 对手机号进行检查确认, 符合条件再往下走
     *
     * @return
     */
    public static boolean phoneSmsPre0CheckAndDo(Context context, String countryCode, String phone, Runnable successRunnable) {
        if (!"46".equals(countryCode) && phone.startsWith("0")) {
            CommonMiddleDialog dialog = new CommonMiddleDialog(context);
            dialog.setMessage(R.string.phone_warn_info_start_with_0_for_sms);
            dialog.setSure(R.string.confirm, v1 -> {
                if (successRunnable != null) {
                    successRunnable.run();
                }
                dialog.dismiss();
            });
            dialog.setCancel(R.string.cancel, v1 -> dialog.dismiss());
            dialog.show();
        } else {
            if (successRunnable != null) {
                successRunnable.run();
            }
        }
        return false;
    }

    public static void setInfo(Context context, LoginInfoBean data) {
        CommonUtils.setLogin(true);
        ToTwooApplication.owner.setNew(data.getIs_new_user() == 1);
        ToTwooApplication.owner.setTotwooId(data.getTotwoo_id());
        ToTwooApplication.owner.setToken(data.getToken());
        ToTwooApplication.owner.setPhone(data.getMobilephone());
        PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.USER_PHONE, data.getMobilephone());
        PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.TOKEN, data.getToken());
        ToTwooApplication.owner.setNickName(data.getNick_name());
        ToTwooApplication.owner.setCity(data.getCity());

        ToTwooApplication.owner.setGender(Integer.parseInt(data.getSex()));

        PreferencesUtils.put(context, CommonArgs.PREF_LAST_HEAD_ICON, data.getHead_portrait());
        PreferencesUtils.put(context, CommonArgs.PREF_LAST_PHONE, data.getMobilephone());
        PreferencesUtils.put(context, CommonArgs.PREF_LAST_USERNAME, data.getNick_name());
        PreferencesUtils.put(context, CommonArgs.PREF_LAST_GENDER, Integer.parseInt(data.getSex()));

        switch (data.getLove_status()) {
            case "S":
                ToTwooApplication.owner.setLoveStatus(1);
                break;
            case "F":
                ToTwooApplication.owner.setLoveStatus(2);
                break;
            case "M":
                ToTwooApplication.owner.setLoveStatus(3);
                break;
            case "SEC":
                ToTwooApplication.owner.setLoveStatus(0);
                break;
        }
        ToTwooApplication.owner.setHeaderUrl(data.getHead_portrait());
        int height = TextUtils.isEmpty(data.getHeight()) ? 0 : Integer.parseInt(data.getHeight());
        ToTwooApplication.owner.setHeight(height);
        int weight = TextUtils.isEmpty(data.getWeight()) ? 0 : Integer.parseInt(data.getWeight());
        ToTwooApplication.owner.setWeight(weight);
        if (TextUtils.isEmpty(data.getBirthday()) || data.getBirthday().startsWith("0000")) {
            ToTwooApplication.owner.setBirthday("1985-06-15");
        } else {
            ToTwooApplication.owner.setBirthday(data.getBirthday());
        }
        int walk_goal;
        if (TextUtils.isEmpty(data.getWalk_goal()) || Integer.parseInt(data.getWalk_goal()) == 0) {
            walk_goal = 8000;
        } else {
            walk_goal = Integer.parseInt(data.getWalk_goal());
        }
        ToTwooApplication.owner.setWalkTarget(walk_goal);

        PreferencesUtils.put(context, StepTargetSettingActivity.STEP_TARGET, walk_goal);
        if (!TextUtils.isEmpty(data.getImei())) {
            PreferencesUtils.put(context, BleParams.SAFE_JEWLERY_IMEI, data.getImei());
        }

        HashSet<String> tags = new HashSet<>(2);
        tags.add(Apputils.systemLanguageIsChinese(context) ? "cn" : "en");

        // 2.1.0 版本之后, 更新推送方式, 清除原有的 Alias, 防止推送异常
        // 使用 Application Context 避免内存泄露
        Context appContext = context.getApplicationContext();
        JPushInterface.setAliasAndTags(appContext, "", tags, new TagAliasCallback() {
            @Override
            public void gotResult(int arg0, String arg1, Set<String> arg2) {
                LogUtils.w("Jpush alias " + arg1 + " set state: " + arg0);
                if (arg0 == 0) {
//                            ToastUtils.showShortDebug(LoginActivity.this, "设置 标签成功: "
//                                    + arg1);
                    PreferencesUtils.put(appContext, HomeBaseActivity.JPUSH_ALIAS_OK, true);
                }
            }
        });
        // 保存当前国家代码
        if (data.getIs_share() == 1) {
            PreferencesUtils.put(context, CommonArgs.NEW_USER_BENFIT_SHARE, true);
        }
        //异常退出可能导致bug，临时解决方案
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_FIRST_CONNECT);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.TOTWOO_DEVICE_INFO);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_BLE_ADRESS_TAG);

        initXLog();
    }

    private static void initXLog() {
        System.loadLibrary("c++_shared");
        System.loadLibrary("marsxlog");

        String cacheDir = Utils.getApp().getFilesDir() + "/twxlog";

        Xlog xlog = new Xlog();
        Log.setLogImp(xlog);
        String totwooId = ToTwooApplication.owner.getTotwooId();
        if (BuildConfig.DEBUG) {
            Log.setConsoleLogOpen(true);
        } else {
            Log.setConsoleLogOpen(false);
        }
        Log.appenderOpen(Xlog.LEVEL_DEBUG, Xlog.AppednerModeAsync, cacheDir, cacheDir, totwooId, 2);
    }


    public static void uploadXLogFile() {
        Log.appenderFlushSync(true);
        //七牛上传
        String cacheDir = Utils.getApp().getFilesDir().toString() + "/twxlog";
        File logFile = new File(cacheDir, ToTwooApplication.owner.getTotwooId() + "_" + TimeUtils.millis2String(System.currentTimeMillis(), "yyyyMMdd") + ".xlog");
        if (!logFile.exists()) {
            return;
        }
        byte[] fileBytes = FileIOUtils.readFile2BytesByStream(logFile);
        postFile(fileBytes);
    }

    public static void shareXLog(Context context) {
        // 刷新日志缓存到文件
        Log.appenderFlushSync(true);

        String cacheDir = Utils.getApp().getFilesDir().toString() + "/twxlog";
        File logFile = new File(cacheDir, ToTwooApplication.owner.getTotwooId() + "_" + TimeUtils.millis2String(System.currentTimeMillis(), "yyyyMMdd") + ".xlog");
        if (!logFile.exists()) {
            return;
        }
        // 生成文件的 URI
        Uri fileUri = CommonUtils.getUriForFile(Utils.getApp(), logFile);


        // 构建分享意图
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_STREAM, fileUri);
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        // 启动分享
        context.startActivity(Intent.createChooser(shareIntent, "Share Screenshot"));
    }

    // 把bitmap转成file上传服务器
    public static void postFile(byte[] fileBytes) {
        // 上传图片
        HttpHelper.card.getQiNiuToken(4, "xlog").subscribeOn(Schedulers.io()).subscribe(new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
//                byte[] fileBytes = CommonUtils.getFileBytesFromUri(MeSettingActivity.this, bitmap);
                if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0 && fileBytes != null) {
                    RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), fileBytes);
                    MultipartBody.Part part = MultipartBody.Part.createFormData("file", "head_icon", requestFile);
                    HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken())).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Subscriber<QiNiuResponse>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                        }

                        @Override
                        public void onNext(QiNiuResponse qiNiuResponse) {
                            // 上传成功
                            ToastUtils.showLong(ToTwooApplication.baseContext, R.string.upload_success);
                        }
                    });
                }
            }
        });
    }

    @NonNull
    public static SpannableString stylePolicyString(@NonNull Context context) {

        String policyStr = context.getString(R.string.terms_agree);
        String item1 = context.getString(R.string.terms_policy);
        String item2 = context.getString(R.string.terms_private);

        // 检索 policyStr 中的 item1 和 item2, 调整颜色为 R.color.safe_green_color, 且通过富文本标记 item 部分可点击, 监听点击事件, 跳转到网页,
        SpannableString spannableString = new SpannableString(policyStr);
        int start = policyStr.indexOf(item1);
        int end = start + item1.length();
        if (start != -1 && end != -1) {
            spannableString.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    WebViewActivity.loadUrl(context, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_PRIVATE_POLICY), false);
                }

                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    // 可以设置链接样式，比如下划线和颜色
                    ds.setColor(ContextCompat.getColor(context, R.color.color_main));
                    ds.setUnderlineText(false);
                }
            }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        start = policyStr.indexOf(item2);
        end = start + item2.length();
        if (start != -1 && end != -1) {
            spannableString.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    WebViewActivity.loadUrl(context, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_PRIVATE), false);
                }

                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    // 可以设置链接样式，比如下划线和颜色
                    ds.setColor(ContextCompat.getColor(context, R.color.color_main));
                    ds.setUnderlineText(false);
                }
            }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        return spannableString;
    }


    private boolean isNotificationListenerServiceEnabled() {
        Set<String> packageNames = NotificationManagerCompat.getEnabledListenerPackages(ToTwooApplication.baseContext);
        if (packageNames.contains(ToTwooApplication.baseContext.getPackageName())) {
            return true;
        }
        return false;
    }

    /**
     * 获取当前手机系统版本号
     *
     * @return 系统版本号
     */
    public static String getSystemVersion() {
        return android.os.Build.VERSION.RELEASE;
    }

    public static String md5(String string) {
        if (TextUtils.isEmpty(string)) {
            return "";
        }
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = md5.digest(string.getBytes());
            String result = "";
            for (byte b : bytes) {
                String temp = Integer.toHexString(b & 0xff);
                if (temp.length() == 1) {
                    temp = "0" + temp;
                }
                result += temp;
            }
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 读取图片的旋转的角度
     *
     * @return 图片的旋转角度
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static int getBitmapDegree(InputStream stream) {
        try {
            return getBitmapDegree(new ExifInterface(stream));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 读取图片的旋转的角度
     *
     * @param path 图片绝对路径
     * @return 图片的旋转角度
     */
    public static int getBitmapDegree(String path) {
        try {
            return getBitmapDegree(new ExifInterface(path));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 读取图片的旋转的角度
     *
     * @return 图片的旋转角度
     */
    public static int getBitmapDegree(@NonNull ExifInterface exifInterface) {
        int degree = 0;
        //            从指定路径下读取图片，并获取其EXIF信息
//            获取图片的旋转信息
        int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
        switch (orientation) {
            case ExifInterface.ORIENTATION_ROTATE_90:
                degree = 90;
                break;
            case ExifInterface.ORIENTATION_ROTATE_180:
                degree = 180;
                break;
            case ExifInterface.ORIENTATION_ROTATE_270:
                degree = 270;
                break;
        }
        return degree;
    }

    /**
     * 将图片按照某个角度进行旋转
     *
     * @param bm     需要旋转的图片
     * @param degree 旋转角度
     * @return 旋转后的图片
     */
    public static Bitmap rotateBitmapByDegree(Bitmap bm, int degree) {
        Bitmap returnBm = null;

//        根据旋转角度，生成旋转矩阵
        Matrix matrix = new Matrix();
        matrix.postRotate(degree);
        try {
//            将原始图片按照旋转矩阵进行旋转，并得到新的图片
            returnBm = Bitmap.createBitmap(bm, 0, 0, bm.getWidth(), bm.getHeight(), matrix, true);
        } catch (OutOfMemoryError e) {

        }
        if (returnBm == null) {
            returnBm = bm;
        }
        if (bm != returnBm) {
            bm.recycle();
        }
        return returnBm;
    }

    public static String getZeroStart(int number) {
        if (number >= 0 && number < 10) {
            return "0" + number;
        } else {
            return number + "";
        }
    }

    /**
     * 把日期按格式转换的通用方法
     *
     * @param date      日期传入格式 yyyy-MM-dd
     * @param isChinese 是不是中文
     * @return 中文输出格式为 yyyy年MM月dd日，英文输出格式为 MM/dd/yyyy
     */
    public static String getFormatDate(String date, boolean isChinese) {
        if (TextUtils.isEmpty(date) || !date.contains("-")) {
            return "";
        }
        String[] infos = date.split("-");
        String result;
        try {
            result = isChinese ? infos[0] + "年" + infos[1] + "月" + infos[2] + "日" : infos[1] + "/" + infos[2] + "/" + infos[0];
        } catch (Exception e) {
            result = date;
        }
        return result;
    }

    /**
     * 把日期按格式转换的通用方法
     *
     * @param millions  日期时间毫秒值
     * @param isChinese 是不是中文
     * @return 中文输出格式为 yyyy年MM月dd日，英文输出格式为 MM/dd/yyyy
     */
    private static SimpleDateFormat ymdFormat = new SimpleDateFormat("yyyy-MM-dd");

    public static String getFormatDate(long millions, boolean isChinese) {
        Date date = new Date();
        date.setTime(millions);
        String dateString = ymdFormat.format(date);
        if (TextUtils.isEmpty(dateString) || !dateString.contains("-")) {
            return "";
        }
        String[] infos = dateString.split("-");
        String result;
        try {
            result = isChinese ? infos[0] + "年" + infos[1] + "月" + infos[2] + "日" : infos[1] + "/" + infos[2] + "/" + infos[0];
        } catch (Exception e) {
            result = dateString;
        }
        return result;
    }

    /**
     * 把日期按格式转换的通用方法
     *
     * @param millions 日期时间毫秒值
     * @return 输出格式为 yyyy/MM/dd
     */
    public static String getFormatDate(long millions) {
        Date date = new Date();
        date.setTime(millions);
        String dateString = ymdFormat.format(date);
        if (TextUtils.isEmpty(dateString) || !dateString.contains("-")) {
            return "";
        }
        String[] infos = dateString.split("-");
        String result;
        try {
            result = infos[0] + "/" + infos[1] + "/" + infos[2];
        } catch (Exception e) {
            result = dateString;
        }
        return result;
    }

    /**
     * 把日期按格式转换的通用方法
     *
     * @param date      日期传入格式 yyyy-MM-dd
     * @param isChinese 是不是中文
     * @return 中文输出格式为 yyyy.MM.dd，英文输出格式为 MM/dd/yyyy
     */
    public static String getFormatDatePoint(String date, boolean isChinese) {
        if (TextUtils.isEmpty(date) || !date.contains("-")) {
            return "";
        }
        String[] infos = date.split("-");
        String result;
        try {
            result = isChinese ? infos[0] + "." + infos[1] + "." + infos[2] : infos[1] + "/" + infos[2] + "/" + infos[0];
        } catch (Exception e) {
            result = date;
        }
        return result;
    }

    public static Uri getUriForFile(@NonNull Context context, @NonNull File file) {
        Uri fileUri = null;
        try {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//                ContentValues contentValues = new ContentValues();
//                contentValues.put(MediaStore.Images.Media.DATA, file.getAbsolutePath());
//                contentValues.put(MediaStore.Images.Media.DISPLAY_NAME, file.getName());
//                contentValues.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
//                fileUri = context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
//            } else
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                fileUri = FileProvider.getUriForFile(context, BuildConfig.APPLICATION_ID + ".fileProvider", file);
            } else {
                fileUri = Uri.fromFile(file);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileUri;
    }

    /**
     * 拷贝指定 Uri 对应的文件到指定路径
     *
     * @param uri
     * @param path
     * @return
     */
    public static boolean copyUriToPath(Context context, Uri uri, String path) {
        try {
            InputStream inputStream = context.getContentResolver().openInputStream(uri);
            if (inputStream == null) {
                return false;
            }
            OutputStream outputStream = new FileOutputStream(path);
            copyStream(inputStream, outputStream);
            inputStream.close();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    public static byte[] getFileBytesFromUri(@NonNull Context context, @NonNull Uri uri) {
        if (context == null || uri == null) {
            return null;
        }

        byte[] bytes = null;


        ByteArrayOutputStream fos = new ByteArrayOutputStream();
        try {
            InputStream is = context.getContentResolver().openInputStream(uri);
            if (is == null) {
                return null;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                FileUtils.copy(is, fos);
            } else {
                byte[] buffer = new byte[1024 * 10];
                while (true) {
                    int len = is.read(buffer);
                    if (len == -1) {
                        break;
                    }
                    fos.write(buffer, 0, len);
                }
            }
            bytes = fos.toByteArray();

            fos.close();
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return bytes;
    }


    /**
     * @param context
     * @param intent
     * @param uri
     * @param writeAble 是否可写
     */
    public static void grantPermissions(Context context, Intent intent, Uri uri, boolean writeAble) {
        int flag = Intent.FLAG_GRANT_READ_URI_PERMISSION;
        if (writeAble) {
            flag |= Intent.FLAG_GRANT_WRITE_URI_PERMISSION;
        }
        intent.addFlags(flag);
        List<ResolveInfo> resInfoList = context.getPackageManager().queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
        for (ResolveInfo resolveInfo : resInfoList) {
            String packageName = resolveInfo.activityInfo.packageName;
            context.grantUriPermission(packageName, uri, flag);
        }
    }


    /**
     * 设置页面全屏, 并声明状态栏颜色
     *
     * @param activity
     * @param isLight  状态栏是否亮色
     */
    public static void setStateBar(Activity activity, boolean isLight) {
        if (activity == null) {
            return;
        }
//        LogUtils.e("CommonUtils", "setStateBar: " + isLight + Log.getStackTraceString(new Throwable()));
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //实现状态栏图标和文字颜色为浅色
            Window window = activity.getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            if (isLight) {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            } else {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            }
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        }
    }

    /**
     * 设置状态栏透明（黑色UI适配）
     * Android 15适配：移除弃用的setStatusBarColor
     */
    public static void setStateBarTransparentForBlackUI(Activity activity) {
        if (activity == null) {
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = activity.getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            // Android 15适配：不再使用弃用的setStatusBarColor
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.VANILLA_ICE_CREAM) {
                window.setStatusBarColor(Color.TRANSPARENT);
            }
            // Android 15+: 使用EdgeToEdge.enable()替代

            window.setBackgroundDrawable(new ColorDrawable(0));
        }
    }

    public static void setListViewHeightBasedOnChildren(ListView listView) {
        if (listView == null) return;
        ListAdapter listAdapter = listView.getAdapter();
        if (listAdapter == null) {
            // pre-condition
            return;
        }
        int totalHeight = 0;
        for (int i = 0; i < listAdapter.getCount(); i++) {
            View listItem = listAdapter.getView(i, null, listView);
            listItem.measure(0, 0);
            totalHeight += listItem.getMeasuredHeight();
        }
        ViewGroup.LayoutParams params = listView.getLayoutParams();
        params.height = totalHeight + (listView.getDividerHeight() * (listAdapter.getCount() - 1));
        listView.setLayoutParams(params);
    }

    public static void jumpToJewList(Context context) {
        String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//        if (TextUtils.isEmpty(jewName)) {
////            context.startActivity(new Intent(context, JewConnectTypeSelectActivity.class));
//            context.startActivity(new Intent(context, JewelryConnectActivity.class));
//        } else if (BleParams.isPendant(jewName)) {
//            context.startActivity(new Intent(context, JewelryListActivity.class).putExtra(CommonArgs.FROM_TYPE, JewelryListActivity.SELECT_TYPE_PENDANT));
//        } else {
//            context.startActivity(new Intent(context, JewelryListActivity.class).putExtra(CommonArgs.FROM_TYPE, JewelryListActivity.SELECT_TYPE_BRACELET));
//        }
        if (TextUtils.isEmpty(jewName)) {
            context.startActivity(new Intent(context, JewelrySelectActivity.class));
//            context.startActivity(new Intent(context, JewelryConnectActivity.class));
        } else {
            context.startActivity(new Intent(context, JewelryPairedListActivity.class));
//            context.startActivity(new Intent(context, JewelryInfoActivity.class));
        }

    }

    public static void clearUserData(boolean isUnRegister) {
        ACache aCache = ACache.get(ToTwooApplication.baseContext);
        // 重置内存中所有用户信息
        owner.setWalkTarget(8000);
        owner = new Owner();
        JPushInterface.setAlias(ToTwooApplication.baseContext, "", null);
        TimInitBusiness.logout();
        PreferencesUtils.put(ToTwooApplication.baseContext, HomeBaseActivity.JPUSH_ALIAS_OK, false);

        /*清除求签数据*/
//        PreferencesUtils.put(ToTwooApplication.baseContext, QianDetailActivity.LAST_QIAN_TAG_PREFIX + 0, 0L);
//        PreferencesUtils.put(ToTwooApplication.baseContext, QianDetailActivity.LAST_QIAN_TAG_PREFIX + 1, 0L);
//        PreferencesUtils.put(ToTwooApplication.baseContext, QianDetailActivity.LAST_QIAN_TAG_PREFIX + 2, 0L);

        PreferencesUtils.remove(ToTwooApplication.baseContext, WaterInfoBean.WATER_TIME_REPEAT_REMIND);
        PreferencesUtils.remove(ToTwooApplication.baseContext, WaterInfoBean.WATER_TIME_REPEAT_COUNT);
        PreferencesUtils.remove(ToTwooApplication.baseContext, WaterInfoBean.DEFAULT_WATER_TIMES);
        PreferencesUtils.remove(ToTwooApplication.baseContext, WaterTimeDbHelper.WATER_TIME_DATA_INIT);

        PreferencesUtils.remove(ToTwooApplication.baseContext, PeriodBean.PERIOD_WEEK);
        PreferencesUtils.remove(ToTwooApplication.baseContext, PeriodBean.PERIOD_MONTH);
        PreferencesUtils.remove(ToTwooApplication.baseContext, PeriodBean.PERIOD_NOTIFY_TIME);

        PreferencesUtils.remove(ToTwooApplication.baseContext, WeiboAuthActivity.WEIBO_TOKEN);
        PreferencesUtils.remove(ToTwooApplication.baseContext, WeiboAuthActivity.WEIBO_SHARE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, WeiboAuthActivity.WEIBO_EXPIRE_TIME);
        PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.PREF_HAS_PASSWORD);
        aCache.remove(CommonArgs.IMEI_DEAD_LINE_TIME);

        File f = new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE);
        if (f.exists()) f.delete();

        CommonUtils.setLogin(false);

        try {
            DbHelper.getDbUtils().deleteAll(Owner.class);
            DbHelper.getDbUtils().deleteAll(Step.class);
            DbHelper.getDbUtils().deleteAll(Uv.class);
            DbHelper.getDbUtils().deleteAll(DataCache.class);
            DbHelper.getDbUtils().deleteAll(MessageBean.class);
            WaterTimeDbHelper.getInstance().deleteAllTable();
            CustomNotifyDbHelper.getInstance().deleteAllBean();
        } catch (DbException e) {
            e.printStackTrace();
        }

        if (isUnRegister) {
            BluetoothManage.getInstance().clearAndOut();
        } else {
            BluetoothManage.getInstance().unPair();
        }
    }

    public static boolean isPasswordValid(String str) {
//        String regex = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]*$";
//        String regex = "^(?![A-Za-z]+$)(?!\\d+$)(?![\\W_]+$)\\S{6,16}$";
//        return str.matches(regex);

        // 4.2.3 要求
        // 设置密码的要求改为：6-16位的字符，只要位数对即可，不限制形式，不要求必须使用字母、数字或符号
        // 页面提示文案这版先不改，只把现存的要求改下就行
        return str.length() >= 6 && str.length() <= 16;
    }

    public static String pwdEncode(String pwd) {
        return md5(md5(pwd) + "totwoo2015");
    }

    public static void installApk(File file, Context context) {
        AppUtils.installApp(file);
//        Intent intent = new Intent();
//        // 执行动作
//        intent.setAction(Intent.ACTION_VIEW);
//        // 执行的数据类型
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            Uri uriForFile = CommonUtils.getUriForFile(context, file);
//            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
//            intent.setDataAndType(uriForFile, "application/vnd.android.package-archive");
//            //兼容8.0
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                boolean hasInstallPermission = context.getPackageManager().canRequestPackageInstalls();
//                if (!hasInstallPermission) {
//                    startInstallPermissionSettingActivity(context);
//                    return;
//                }
//            }
//        } else {
//            intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");
//        }
//        if (context.getPackageManager().queryIntentActivities(intent, 0).size() > 0) {
//            //如果APK安装界面存在，携带请求码跳转。使用forResult是为了处理用户 取消 安装的事件。外面这层判断理论上来说可以不要，但是由于国内的定制，这个加上还是比较保险的
//            context.startActivity(intent);
//        }
//        context.startActivity(intent);

    }

    /**
     * 跳转到设置-允许安装未知来源-页面
     */
    @RequiresApi(api = Build.VERSION_CODES.O)
    private static void startInstallPermissionSettingActivity(Context context) {
        //后面跟上包名，可以直接跳转到对应APP的未知来源权限设置界面。使用startActivityForResult 是为了在关闭设置界面之后，获取用户的操作结果，然后根据结果做其他处理
        Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES, Uri.parse("package:" + context.getPackageName()));
        context.startActivity(intent);
    }

    public static void downloadingNotification(int progress) {
        showNotification(ToTwooApplication.baseContext.getString(R.string.downloading), ToTwooApplication.baseContext.getString(R.string.app_download) + " " + progress + " %", null, 12, progress);
    }

    public static void downloadSuccessNotification(Intent intent) {
        showNotification(ToTwooApplication.baseContext.getString(R.string.download_success), ToTwooApplication.baseContext.getString(R.string.download_success), intent, 12, -1);
    }

    public static void downloadFailNotification() {
        showNotification(ToTwooApplication.baseContext.getString(R.string.download_failure), ToTwooApplication.baseContext.getString(R.string.download_failure), null, 12, -1);
    }

    @SuppressLint("MissingPermission")
    public static void showNotification(String title, String content, Intent intent, int notify_id, int progress) {
        NotificationManager notificationManager = (NotificationManager) ToTwooApplication.baseContext.getSystemService(NOTIFICATION_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String channelId = "20180310";
            NotificationChannel mChannel = new NotificationChannel(channelId, "totwoo", NotificationManager.IMPORTANCE_HIGH);
            notificationManager.createNotificationChannel(mChannel);
            Notification.Builder builder = new Notification.Builder(ToTwooApplication.baseContext, channelId).setSmallIcon(R.drawable.ic_launcher_small).setOnlyAlertOnce(true).setLargeIcon(BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.mipmap.ic_launcher)).setContentTitle(TextUtils.isEmpty(title) ? ToTwooApplication.baseContext.getString(R.string.app_name) : title).setTicker(TextUtils.isEmpty(title) ? ToTwooApplication.baseContext.getString(R.string.app_name) : title).setCategory(Notification.CATEGORY_REMINDER).setContentText(content).setWhen(System.currentTimeMillis()).setAutoCancel(true);

            if (intent != null) {
                PendingIntent pendingIntent = PendingIntent.getActivity(ToTwooApplication.baseContext, notify_id, intent, Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_IMMUTABLE));
                builder.setContentIntent(pendingIntent);
            }
            if (progress >= 0) {
                builder.setProgress(100, progress, false);
            }

            notificationManager.notify(notify_id, builder.build());
        } else {
            // 自定义通知栏功能配置
            final NotificationManagerCompat notifyManager = NotificationManagerCompat.from(ToTwooApplication.baseContext);
            final NotificationCompat.Builder builder = new NotificationCompat.Builder(ToTwooApplication.baseContext).setDefaults(Notification.FLAG_ONLY_ALERT_ONCE).setSmallIcon(R.drawable.ic_launcher_small).setLargeIcon(BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.mipmap.ic_launcher)).setContentTitle(TextUtils.isEmpty(title) ? ToTwooApplication.baseContext.getString(R.string.app_name) : title).setTicker(TextUtils.isEmpty(title) ? ToTwooApplication.baseContext.getString(R.string.app_name) : title).setContentText(content).setPriority(NotificationCompat.PRIORITY_DEFAULT).setAutoCancel(true);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                builder.setCategory(Notification.CATEGORY_REMINDER);
            }


            // 创建通知栏的储备意图
            if (intent != null) {
                PendingIntent pendingIntent = PendingIntent.getActivity(ToTwooApplication.baseContext, notify_id, intent, Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
                builder.setContentIntent(pendingIntent);
            }
            if (progress >= 0) {
                builder.setProgress(100, progress, false);
            }

            notifyManager.notify(notify_id, builder.build());
        }
    }

    private static final double EARTH_RADIUS = 6378137;//赤道半径(单位m)

    /**
     * 转化为弧度(rad)
     */
    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 基于余弦定理求两经纬度距离
     *
     * @param lon1 第一点的精度
     * @param lat1 第一点的纬度
     * @param lon2 第二点的精度
     * @param lat2 第二点的纬度
     * @return 返回的距离，单位km
     */
    public static double lantitudeLongitudeDist(double lon1, double lat1, double lon2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);

        double radLon1 = rad(lon1);
        double radLon2 = rad(lon2);

        if (radLat1 < 0) radLat1 = Math.PI / 2 + Math.abs(radLat1);// south
        if (radLat1 > 0) radLat1 = Math.PI / 2 - Math.abs(radLat1);// north
        if (radLon1 < 0) radLon1 = Math.PI * 2 - Math.abs(radLon1);// west
        if (radLat2 < 0) radLat2 = Math.PI / 2 + Math.abs(radLat2);// south
        if (radLat2 > 0) radLat2 = Math.PI / 2 - Math.abs(radLat2);// north
        if (radLon2 < 0) radLon2 = Math.PI * 2 - Math.abs(radLon2);// west
        double x1 = EARTH_RADIUS * Math.cos(radLon1) * Math.sin(radLat1);
        double y1 = EARTH_RADIUS * Math.sin(radLon1) * Math.sin(radLat1);
        double z1 = EARTH_RADIUS * Math.cos(radLat1);

        double x2 = EARTH_RADIUS * Math.cos(radLon2) * Math.sin(radLat2);
        double y2 = EARTH_RADIUS * Math.sin(radLon2) * Math.sin(radLat2);
        double z2 = EARTH_RADIUS * Math.cos(radLat2);

        double d = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2) + (z1 - z2) * (z1 - z2));
        //余弦定理求夹角
        double theta = Math.acos((EARTH_RADIUS * EARTH_RADIUS + EARTH_RADIUS * EARTH_RADIUS - d * d) / (2 * EARTH_RADIUS * EARTH_RADIUS));
        double dist = theta * EARTH_RADIUS;
        return Math.abs(dist);
    }

    public static boolean isLocServiceEnable(final Context context) {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        // 通过GPS卫星定位，定位级别可以精确到街（通过24颗卫星定位，在室外和空旷的地方定位准确、速度快）
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        // 通过WLAN或移动网络(3G/2G)确定的位置（也称作AGPS，辅助GPS定位。主要用于在室内或遮盖物（建筑群或茂密的深林等）密集的地方定位）
//        boolean network = NetUtils.isWifi(context);
//        return gps || network;
        return gps;

    }

    public static String byteTrans(long byteSize) {
        double byteSizeDouble = byteSize;
        DecimalFormat df = new DecimalFormat("#.0");
        if (byteSizeDouble / 1024 < 1) {
            return byteSize + "B";
        } else if (byteSizeDouble / 1024 / 1024 < 1) {
            double targetSize = byteSizeDouble / 1024;
            return df.format(targetSize) + "K";
        } else if (byteSizeDouble / 1024 / 1024 / 1024 < 1) {
            double targetSize = byteSizeDouble / 1024 / 1024;
            return df.format(targetSize) + "M";
        } else {
            double targetSize = byteSizeDouble / 1024 / 1024 / 1024;
            return df.format(targetSize) + "G";
        }
    }

    public static String millionsTrans(long timeMillions) {
        long secL = timeMillions / 1000;
        int min = (int) (secL / 60);
        int sec = (int) (secL % 60);
        return getZeroStart(min) + ":" + getZeroStart(sec);
    }

    public static String randomString() {
        Random random = new Random();
        int count = 12;
        byte[] bytes = new byte[count];

        for (int i = 0; i < count; i++) {
            int integer = random.nextInt(100);
            bytes[i] = (byte) integer;
        }
        return Base64.encodeToString(bytes, Base64.DEFAULT);
    }


    @RequiresApi(api = Build.VERSION_CODES.M)
    public static boolean isIgnoringBatteryOptimizations(Context baseContext) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean isIgnoring = false;
            PowerManager powerManager = (PowerManager) baseContext.getSystemService(Context.POWER_SERVICE);
            if (powerManager != null) {
                isIgnoring = powerManager.isIgnoringBatteryOptimizations(baseContext.getPackageName());
            }
            return isIgnoring;
        }
        return true;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    public static void requestIgnoreBatteryOptimizations(Context baseContext) {
        try {
            Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + baseContext.getPackageName()));
            baseContext.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean isInstallPackage(Context mContext, String packageName) {
        PackageManager packageManager = mContext.getPackageManager();
        List<PackageInfo> installedPackages = packageManager.getInstalledPackages(0);
        for (PackageInfo info : installedPackages) {
            if (TextUtils.equals(info.packageName, packageName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将 Bundle 对象遍历转化成 String, 便于打印输出
     *
     * @param bundle
     * @return
     */
    @NonNull
    public static String bundleToString(Bundle bundle) {
        if (bundle == null) {
            return "null";
        }
        StringBuilder sb = new StringBuilder();
        for (String key : bundle.keySet()) {
            sb.append(key).append("=").append(bundle.get(key).toString()).append(", ");
        }
        if (sb.length() > 2) {
            sb.delete(sb.length() - 2, sb.length());
        }
        return sb.toString();
    }

    /**
     * 获取当前的闪光状态
     *
     * @return
     */
    public static boolean jewelryFlashOpen(@NonNull Context context) {
        return PreferencesUtils.getInt(context, COLOR_VALUE, -1) > 0;
    }

    /**
     * 获取当前选中的闪光颜色状态
     *
     * @param context
     * @return
     */
    public static int getJewelryFlashIndex(Context context) {
        return Math.abs(PreferencesUtils.getInt(context, COLOR_VALUE, -1));
    }


    static int cont = 0;
    static long clickTime = 0;

    public static void setTestMultiClick(@NonNull View view, @NonNull View.OnClickListener listener, int count) {
        view.setOnClickListener(v -> {
            if (SystemClock.elapsedRealtime() - clickTime < 1000) {
                cont++;
                if (cont == count - 1) {
                    listener.onClick(v);
                }
            } else {
                cont = 0;
            }
            clickTime = SystemClock.elapsedRealtime();
        });
    }


    /**
     * oppo 系统 5.1-7.0 适配
     */
    public static boolean isColorOS51To7() {
        String manufacturer = Build.MANUFACTURER;
        String version = Build.VERSION.INCREMENTAL;

        return manufacturer.equalsIgnoreCase("oppo") && (version.contains("ColorOS_5") || version.contains("ColorOS_6") || version.contains("ColorOS_7"));
    }


    public static boolean isMIUI12OrAbove() {
        String miuiVersion = "";
        try {
            Class<?> systemPropertiesClass = Class.forName("android.os.SystemProperties");
            Method getMethod = systemPropertiesClass.getMethod("get", String.class);
            String versionName = (String) getMethod.invoke(systemPropertiesClass, "ro.miui.ui.version.name");
            if (!TextUtils.isEmpty(versionName)) {
                miuiVersion = versionName;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 检查 MIUI 版本号是否为 12 或更高版本
        if (!TextUtils.isEmpty(miuiVersion)) {
            String[] parts = miuiVersion.split("\\.");
            if (parts.length > 0) {
                try {
                    int majorVersion = Integer.parseInt(parts[0].replace("V", ""));
                    return majorVersion >= 12;
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }


    public static void setTintColor(ImageView myImageView, @ColorInt int newColor) {
        // 创建一个ColorFilter
        ImageViewCompat.setImageTintList(myImageView, ColorStateList.valueOf(newColor));
        ImageViewCompat.setImageTintMode(myImageView, PorterDuff.Mode.SRC_IN);
    }

    public static int getBatteryIconColor(int battery, boolean isBlack) {
        if (battery <= 30) {
            return Color.RED;
        } else if (battery <= 40) {
            return Color.parseColor("#ffe5cf7f");
        } else {
            if (isBlack) {
                return Color.BLACK;
            } else {
                return Color.WHITE;
            }
        }
    }

    /**
     * 解析零宽字符串中的颜色值和震动强度
     *
     * @param text 包含零宽字符的字符串
     * @return 返回RGB颜色值，如果解析失败返回-1
     */
    public static int parseZeroWidthColor(String text) {
        if (text == null || text.length() < 10) {
            return -1;
        }

        try {
            // 提取零宽字符序列
            StringBuilder numbers = new StringBuilder();
            for (int i = 0; i < text.length(); i++) {
                char c = text.charAt(i);
                // 检查是否为零宽字符
                if (c == '\u200B' || c == '\u200C' || c == '\u200D' || c == '\u2060' || c == '\u2061' || c == '\u2062' || c == '\u2063' || c == '\u2064' || c == '\u206A' || c == '\u206B' || c == '\u206C' || c == '\u206D' || c == '\u206E' || c == '\u206F' || c == '\uFEFF') {
                    // 将零宽字符转换为对应的数字
                    if (c == '\u200B') numbers.append("0");
                    else if (c == '\u200C') numbers.append("1");
                    else if (c == '\u200D') numbers.append("2");
                    else if (c == '\u2060') numbers.append("3");
                    else if (c == '\u2061') numbers.append("4");
                    else if (c == '\u2062') numbers.append("5");
                    else if (c == '\u2063') numbers.append("6");
                    else if (c == '\u2064') numbers.append("7");
                    else if (c == '\uFEFF') numbers.append("8");
                    else numbers.append("9");
                }
            }

            // 确保提取到10个数字
            if (numbers.length() != 10) {
                return -1;
            }

            // 解析RGB颜色值
            String rHex = numbers.substring(0, 3);
            String gHex = numbers.substring(3, 6);
            String bHex = numbers.substring(6, 9);

            // 转换为十进制
            int r = Integer.parseInt(rHex);
            int g = Integer.parseInt(gHex);
            int b = Integer.parseInt(bHex);

            // 检查RGB值是否在有效范围内(0-255)
            if (r > 255 || g > 255 || b > 255) {
                return -1;
            }

            // 组合为RGB颜色值
            return (r << 16) | (g << 8) | b;

        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }


    public static String convertToUnicode(String str) {
        StringBuilder sb = new StringBuilder();
        for (char ch : str.toCharArray()) {
            // 包含用户提供的 10 个零宽字符
            if (ch == '\u200B' || ch == '\u200C' || ch == '\u200D' || ch == '\u200E' || ch == '\u200F' || ch == '\u202A' || ch == '\u202C' || ch == '\u2060' || ch == '\uFEFF' || ch == '\u202F') {
                sb.append(String.format("\\u%04X", (int) ch)); // 转换为 Unicode 码
            } else {
                sb.append(ch); // 保持正常字符
            }
        }
        return sb.toString();

    }

    //u200B\u200B\u200B\u200D\u202A\u202A\u200B\u200B\u200B\u200B
    public static String replaceZeroWidthWithNumbers(String str) {
        return str.replace("\u200B", "0").replace("\u200C", "1").replace("\u200D", "2").replace("\u200E", "3").replace("\u200F", "4").replace("\u202A", "5").replace("\u202C", "6").replace("\u2060", "7").replace("\uFEFF", "8").replace("\u202F", "9");
    }


    public static String getColorHex(String str) {
        // 取最后10位
        String last10 = str.length() >= 10 ? str.substring(str.length() - 10) : str;

        // 每3位转换为16进制
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < 9; i += 3) {
            String part = last10.substring(i, i + 3);
            int decimal = Integer.parseInt(part);
            hexString.append(String.format("%02X", decimal));
        }
        return hexString.toString();
    }

    public static int getVibrationStrength(String str) {
        // 取最后10位
        String last10 = str.length() >= 10 ? str.substring(str.length() - 10) : str;

        // 获取最后一位作为震动强度
        return Character.getNumericValue(last10.charAt(9));
    }
}
