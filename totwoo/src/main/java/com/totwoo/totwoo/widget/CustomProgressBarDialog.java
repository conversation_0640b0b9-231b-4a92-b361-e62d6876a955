package com.totwoo.totwoo.widget;

import android.content.Context;
import android.os.Handler;
import android.widget.Toast;

import com.totwoo.totwoo.R;

/**
 * 自定义的进度加载框<br>
 * 当前方案, 通过类似的Toast 提示, 如果请求时间不超过 一定值(默认400毫秒)不显示
 * 
 * <AUTHOR>
 * @date 2015-2015年7月15日
 */
public class CustomProgressBarDialog {
	/**
     * 默认延时显示的延时时间
     */
    private final int DEFAULT_SHOW_OFFSET_TIME = 800;

	/** 要维持整个toast 持续显示, 需要间断重新show 的时间 */
	private final int DEFAULT_CONTINUE_SHOW_TIME = 2000;

	/** 默认使用的 Toast */
	private Toast mToast;

	/** 展示Toast 的Runable, 未显示时直接取消 */
	private Runnable showRunnable;

	/** 是否正在展示 */
	private boolean isShowing;

	private Handler mHandler;

	public CustomProgressBarDialog(Context context) {
		initDialog(context);
	}

	public CustomProgressBarDialog(Context context, int theme) {
		initDialog(context);
	}

	/**
	 * 初始化对话框
	 * 
	 * @param context
	 */
	private void initDialog(Context context) {
		mHandler = new Handler();
		mToast = Toast.makeText(context, R.string.loadding, Toast.LENGTH_LONG);
		showRunnable = () -> {
            if (mToast != null) {
                mToast.show();
                show(DEFAULT_CONTINUE_SHOW_TIME);
            }
        };
	}

	/**
	 * 展示自定义加载进度界面
	 */
	public void show() {
		if (isShowing) {
			return;
		}
		show(DEFAULT_SHOW_OFFSET_TIME);
	}

	/**
	 * 制定时间后调用展示方法
	 * 
	 * @param time
	 *            制定的时间
	 */
	public void show(int time) {
		isShowing = true;
		mHandler.postDelayed(showRunnable, time);
	}

	/**
	 * 隐藏加载框
	 */
	public void dismiss() {
		try {
			if (isShowing) {
				mToast.cancel();
			}
			mHandler.removeCallbacks(showRunnable);
			isShowing = false;
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 当前进度框是否正在展示
	 */
	public boolean isShowing() {
		return isShowing;
	}

	/**
	 * 设置加载信息, 默认为空
	 * 
	 * @param textId
	 */
	public void setMessage(int textId) {
		mToast.setText(textId);
	}
}
