package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.totwoo.totwoo.R;

import butterknife.BindView;
import butterknife.ButterKnife;

public class LoveManageApartDialog extends Dialog {

    private Context context;
    private View rootView;
    @BindView(R.id.love_manage_cancel)
    TextView mCancelTv;
    @BindView(R.id.love_manage_sure)
    TextView mSureTv;

    public LoveManageApartDialog(Context context) {
        super(context, R.style.MyDialog);
        this.context = context;
        initDialog(context);
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.love_manage_apart_dialog, null);

        ButterKnife.bind(this,rootView);
//        rootView.findViewById(R.id.certification_target_sure_tv).setOnClickListener(onClickListener);
        mCancelTv.setOnClickListener(v -> dismiss());

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(rootView);
    }

    public void setOnClickListener(View.OnClickListener onClickListener){
        mSureTv.setOnClickListener(onClickListener);
    }
}