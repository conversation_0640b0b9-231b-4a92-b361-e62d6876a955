package com.totwoo.totwoo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint.Cap;
import android.graphics.Paint.Style;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;

import androidx.core.content.res.ResourcesCompat;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;

public class RoundProgressBarWidthNumber extends
        HorizontalProgressBarWithNumber {
    /**
     * mRadius of view
     */
    private int mRadius = dp2px(30);
    private int mMaxPaintWidth;
    private Typeface typeface;

    private Context mContext;

    public RoundProgressBarWidthNumber(Context context) {
        this(context, null);
    }

    public RoundProgressBarWidthNumber(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        mReachedProgressBarHeight = (int) (mUnReachedProgressBarHeight * 2.5f);
        TypedArray ta = context.obtainStyledAttributes(attrs,
                R.styleable.RoundProgressBarWidthNumber);
        mRadius = (int) ta.getDimension(
                R.styleable.RoundProgressBarWidthNumber_radius, mRadius);
        ta.recycle();
        typeface = ResourcesCompat.getFont(context, R.font.agencyb);
        mPaint.setStyle(Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setStrokeCap(Cap.ROUND);
    }

    /**
     * 这里默认在布局中padding值要么不设置，要么全部设置
     */
    @Override
    protected synchronized void onMeasure(int widthMeasureSpec,
                                          int heightMeasureSpec) {

        mMaxPaintWidth = Math.max(mReachedProgressBarHeight,
                mUnReachedProgressBarHeight);
        int expect = mRadius * 2 + mMaxPaintWidth + getPaddingLeft()
                + getPaddingRight();
        int width = resolveSize(expect, widthMeasureSpec);
        int height = resolveSize(expect, heightMeasureSpec);
        int realWidth = Math.min(width, height);
        mRadius = (realWidth - getPaddingLeft() - getPaddingRight() - mMaxPaintWidth) / 2;

        setMeasuredDimension(realWidth, realWidth);

    }

    @Override
    protected synchronized void onDraw(Canvas canvas) {

        String text = getProgress() + "";
//		LogUtils.i("progress",getProgress()+"");
        mPaint.setTextSize(Apputils.dp2px(mContext, 40));
        mPaint.setTextSize(Apputils.dp2px(mContext, 20));
        float bfbWidth = mPaint.measureText("%");
        float bfbHeight = mPaint.descent() + mPaint.ascent();

        canvas.save();
        canvas.translate(getPaddingLeft() + mMaxPaintWidth / 2, getPaddingTop()
                + mMaxPaintWidth / 2);
        mPaint.setStyle(Style.STROKE);
        // draw unreaded bar
        mPaint.setColor(mUnReachedBarColor);
        mPaint.setStrokeWidth(mUnReachedProgressBarHeight);
        canvas.drawCircle(mRadius, mRadius, mRadius, mPaint);
        // draw reached bar
        mPaint.setColor(mReachedBarColor);
        mPaint.setStrokeWidth(mReachedProgressBarHeight);
        float sweepAngle = getProgress() * 1.0f / getMax() * 360;
        // for (int i = 0; i < 10; i++) {
        //
         float offdis = mReachedProgressBarHeight / 3;
        canvas.drawArc(new RectF(offdis, offdis, mRadius * 2 - offdis, mRadius
                * 2 - offdis), 270, sweepAngle, false, mPaint);
        // }
        // draw text

        mPaint.setStyle(Style.FILL);
        mPaint.setTypeface(typeface);
        mPaint.setTextSize(Apputils.dp2px(mContext, 40));
        float textWidth = mPaint.measureText(text);
        float textHeight = (mPaint.descent() + mPaint.ascent());

        canvas.drawText(text, mRadius - textWidth / 2 - bfbWidth / 2, mRadius
                - textHeight / 2, mPaint);
        mPaint.setTextSize(Apputils.dp2px(mContext, 20));
        float bfbDrawX = mRadius
                + (textWidth - (mRadius - (mRadius - textWidth / 2 - bfbWidth / 2)));

        canvas.drawText("%", bfbDrawX, mRadius - textHeight / 2, mPaint);
        // canvas.drawText(text, mRadius - textWidth / 2,
        // mRadius - textHeight / 2, mPaint);
//		LogUtils.i("progress",bfbDrawX+":"+(mRadius - textHeight / 2+":"+(mRadius - textWidth / 2 - bfbWidth / 2)+":"+(mRadius
//				- textHeight / 2)));
        canvas.restore();
    }
}
