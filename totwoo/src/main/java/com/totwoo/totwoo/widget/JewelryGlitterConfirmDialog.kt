package com.totwoo.totwoo.widget

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.Utils
import com.totwoo.library.util.Apputils
import com.totwoo.totwoo.R

class JewelryGlitterConfirmDialog(
    context: Context,
    private val onConfirm: () -> Unit,
    private val onCancel: () -> Unit
) : Dialog(context, R.style.MyDialog) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_jewelry_glitter_confirm)

        // 设置弹窗宽度
//        window?.let { window ->
//            val params = window.attributes
//            params.width = WindowManager.LayoutParams.WRAP_CONTENT
//            params.height = WindowManager.LayoutParams.WRAP_CONTENT
//            window.attributes = params
//        }

        // 点击外部不可取消
        setCanceledOnTouchOutside(false)
        
        // 设置按钮点击事件
        findViewById<com.hjq.shape.view.ShapeButton>(R.id.btn_confirm).setOnClickListener {
            onConfirm.invoke()
            dismiss()
        }
        
        findViewById<android.widget.TextView>(R.id.btn_cancel).setOnClickListener {
            onCancel.invoke()
            dismiss()
        }

        findViewById<android.widget.LinearLayout>(R.id.ll_lucky_day).run {
            if (Apputils.systemLanguageIsOther(Utils.getApp())) {
                visibility = View.GONE
            } else {
                visibility = View.VISIBLE
            }
        }
    }
} 