package com.totwoo.totwoo.widget;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ClickUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.NotifyTotwooActivity;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.data.TotwooLogic;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

/**
 * Created by xinyoulingxi on 2017/5/15.
 */

public class SendBQDialogController {

    protected CustomDialog dialog;
    protected Context context;
    protected TotwooLogic tl;
    protected BQDialogInterface bqInterface;

    protected boolean doPrepare;

    public SendBQDialogController(Context context, TotwooLogic tl) {
        this.context = context;
        this.tl = tl;
    }

    public void setListener(BQDialogInterface bqInterface) {
        this.bqInterface = bqInterface;
    }

    public CustomDialog showBQDialog(boolean doPrepare) {
        dialog = new CustomDialog(context, R.style.send_totwoo_bg_dialog);
        View v = View.inflate(context, R.layout.the_heart_bq_dialog, null);
        dialog.setRootView(v);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);
        }

        BQDialogListener listener = new BQDialogListener();
        v.findViewById(R.id.the_heart_bq_dialog_totwoo).setOnClickListener(listener);
        v.findViewById(R.id.the_heart_bq_dialog_love).setOnClickListener(listener);
        v.findViewById(R.id.the_heart_bq_dialog_sad).setOnClickListener(listener);
        v.findViewById(R.id.the_heart_bq_dialog_kiss).setOnClickListener(listener);
        v.findViewById(R.id.the_heart_bq_dialog_pain).setOnClickListener(listener);
        v.findViewById(R.id.the_heart_bq_dialog_sorry).setOnClickListener(listener);
        v.findViewById(R.id.the_heart_bq_dialog_cancel).setOnClickListener(listener);
        View setFlash = v.findViewById(R.id.the_heart_bq_dialog_set);

        setFlash.setOnClickListener(listener);

        ClickUtils.expandClickArea(setFlash,40);

        LinearLayout rootView = v.findViewById(R.id.the_heart_bq_dialog);


        TextView tvHint = v.findViewById(R.id.the_heart_bq_dialog_hint);
        TextView tvKnock = v.findViewById(R.id.the_heart_knock_info_tv);
        LinearLayout llKnock = v.findViewById(R.id.the_heart_knock_info_ll);

        String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (TextUtils.isEmpty(jewName)) {
            v.findViewById(R.id.the_heart_bq_dialog_set).setVisibility(View.GONE);
            llKnock.setVisibility(View.GONE);
            rootView.setBackgroundResource(R.drawable.the_heart_bq_bg_small);
        } else {
            rootView.setBackgroundResource(R.drawable.the_heart_bq_bg);
            tvHint.setVisibility(View.GONE);
            String talkTip = JewelryOnlineDataManager.getInstance().getConnectedJewInfo().getTalk_tip();
            if (!TextUtils.isEmpty(talkTip)) {
                tvKnock.setText(talkTip);
            } else {
                tvKnock.setText(BleParams.isTouchJewelry() ? R.string.default_totwoo_info_string_touch : R.string.default_totwoo_info_string);
            }
        }
        dialog.show();
        this.doPrepare = doPrepare;

        return dialog;
    }

    protected class BQDialogListener implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            switch (v.getId()) {
                case R.id.the_heart_bq_dialog_set:
                    if (bqInterface != null)
                        bqInterface.onCancel();
                    dialog.dismiss();
                    dialog = null;
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_DIALOG_LOVE_CODE);
                    Intent intent1 = new Intent(context, NotifyTotwooActivity.class);
                    context.startActivity(intent1);
                    break;
                case R.id.the_heart_bq_dialog_totwoo:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_MISS_U);
                    sendMessage(CommonUtils.CONTENT_MISS);
                    break;
                case R.id.the_heart_bq_dialog_love:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_SEND_ILOVEYOU);
                    sendMessage(CommonUtils.CONTENT_LOVE);
                    break;
                case R.id.the_heart_bq_dialog_sad:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_SAD);
                    sendMessage(CommonUtils.CONTENT_SAD);
                    break;
                case R.id.the_heart_bq_dialog_kiss:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_NEED_U);
                    sendMessage(CommonUtils.CONTENT_NEED_YOU);
                    break;
                case R.id.the_heart_bq_dialog_pain:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_GO_AWAY);
                    sendMessage(CommonUtils.CONTENT_PAIN);
                    break;
                case R.id.the_heart_bq_dialog_sorry:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_SORRY);
                    sendMessage(CommonUtils.CONTENT_SORRY);
                    break;
                case R.id.the_heart_bq_dialog_cancel:
                    if (bqInterface != null)
                        bqInterface.onCancel();
                    dialog.dismiss();
                    dialog = null;
                    break;
                default:
                    break;
            }
        }
    }

    protected void sendMessage(String content) {
        if (tl == null) {
            return;
        }

        if (bqInterface != null && doPrepare)
            bqInterface.prepareSend();
        tl.totwooSend(content);
        //ImPushController.getInstance().sendTotwoo(content);
        if (dialog != null) {
            dialog.dismiss();

            dialog = null;
        }
    }

    protected void sendCustomBQMessage(String content,String text) {
        if (tl == null) {
            return;
        }

        if (bqInterface != null && doPrepare)
            bqInterface.prepareSend();
        tl.totwooSendCustomBQ(false,content,false);
        //ImPushController.getInstance().sendTotwoo(content);
        if (dialog != null) {
            dialog.dismiss();

            dialog = null;
        }
    }

    public interface BQDialogInterface {
        void prepareSend();

        void onCancel();
    }
}
