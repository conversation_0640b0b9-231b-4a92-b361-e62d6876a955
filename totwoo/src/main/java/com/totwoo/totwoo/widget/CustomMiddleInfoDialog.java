package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.CommonUtils;

/**
 * 隐私弹窗的中间弹窗
 */
public class CustomMiddleInfoDialog extends Dialog {

    ImageView mainIconIv;
    TextView titleTv;
    FrameLayout emptyLl;
    TextView denyTv;
    TextView confirmTv;
    ImageView cancelIv;
    ImageView cancelPairIv;
    LinearLayout infoLl;
    TextView infoTv1;
    TextView infoTv2;
    TextView infoTv3;
    TextView infoTvAdd;
    LinearLayout infoLlAdd;
    CheckBox infoCbAdd;

    ViewGroup termsLlAdd;
    LinearLayout termsLl1Add;
    CheckBox termsCbAdd;
    TextView terms1TvAdd;
    TextView termsPrivateTvAdd;
    TextView terms2TvAdd;
    TextView privacyDescription;

    Context context;
    private View rootView;

    public CustomMiddleInfoDialog(Context context) {
        super(context, R.style.MyDialog);
        this.context = context;
        rootView = LayoutInflater.from(context).inflate(
                R.layout.custom_middle_info_dialog, null);
        mainIconIv = rootView.findViewById(R.id.custom_middle_icon_iv);
        titleTv = rootView.findViewById(R.id.custom_middle_title_tv);
        emptyLl = rootView.findViewById(R.id.custom_middle_empty_fl);
        denyTv = rootView.findViewById(R.id.custom_middle_deny_tv);
        confirmTv = rootView.findViewById(R.id.custom_middle_confirm_tv);
        cancelIv = rootView.findViewById(R.id.custom_middle_cancel_iv);
        cancelPairIv = rootView.findViewById(R.id.custom_middle_cancel_pair_iv);
        infoLl = rootView.findViewById(R.id.custom_middle_info_ll);
        infoTv1 = rootView.findViewById(R.id.custom_middle_info_tv1);
        infoTv2 = rootView.findViewById(R.id.custom_middle_info_tv2);
        infoTv3 = rootView.findViewById(R.id.custom_middle_info_tv3);
        infoTvAdd = rootView.findViewById(R.id.custom_middle_info_add);
        infoLlAdd = rootView.findViewById(R.id.custom_add_ll);
        infoCbAdd = rootView.findViewById(R.id.custom_add_cb);
        termsLlAdd = rootView.findViewById(R.id.custom_add_terms_ll);
        termsLl1Add = rootView.findViewById(R.id.custom_add_terms_ll1);
        termsCbAdd = rootView.findViewById(R.id.custom_add_terms_cb);
        terms1TvAdd = rootView.findViewById(R.id.custom_add_terms_1);
        termsPrivateTvAdd = rootView.findViewById(R.id.custom_add_terms_private);
        terms2TvAdd = rootView.findViewById(R.id.custom_add_terms_2);
        privacyDescription = rootView.findViewById(R.id.consent_privacy_description);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(rootView);
    }

    public void setMainIconIv(int resourceId) {
        mainIconIv.setVisibility(View.VISIBLE);
        mainIconIv.setImageResource(resourceId);
    }

    public void setTextAdd(int resourceId) {
        infoTvAdd.setVisibility(View.VISIBLE);
        infoLlAdd.setVisibility(View.VISIBLE);
        infoTvAdd.setText(resourceId);
    }

    public void setTextAdd(String text,View.OnClickListener addClickListener) {
        infoTvAdd.setVisibility(View.VISIBLE);
        infoLlAdd.setVisibility(View.VISIBLE);
        infoTvAdd.setText(text);
        infoTvAdd.setOnClickListener(addClickListener);
    }

    public void setTextAdd(CharSequence text,View.OnClickListener addClickListener) {
        infoTvAdd.setVisibility(View.VISIBLE);
        infoLlAdd.setVisibility(View.VISIBLE);
        infoTvAdd.setText(text);
        infoTvAdd.setOnClickListener(addClickListener);
    }

    public void setPrivacyDescription(){
        privacyDescription.setVisibility(View.VISIBLE);
        privacyDescription.setText(CommonUtils.stylePolicyString(context));
        privacyDescription.setMovementMethod(LinkMovementMethod.getInstance());

    }

    public void setTerms(View.OnClickListener policyClickListener, View.OnClickListener privateClickListener, View.OnClickListener disclaimerClickListener){
        termsLlAdd.setVisibility(View.VISIBLE);
//        termsLl1Add.setVisibility(View.VISIBLE);
        terms1TvAdd.setOnClickListener(policyClickListener);
//        if(Apputils.systemLanguageIsChinese(getContext())){
//            termsPrivateTvAdd.setVisibility(View.VISIBLE);
//        }else{
//            termsPrivateTvAdd.setVisibility(View.GONE);
//        }
        termsPrivateTvAdd.setOnClickListener(privateClickListener);
        terms2TvAdd.setOnClickListener(disclaimerClickListener);
    }

    public void setAddTextSize(float size){
        infoTvAdd.setTextSize(size);
    }

    public void setAddCb(){
        infoCbAdd.setVisibility(View.VISIBLE);
    }

    public boolean getAddCb(){
        return infoCbAdd.isChecked();
    }

    public boolean getTermsCb(){
        return termsCbAdd.isChecked();
    }

    public void setTitleTv(int resourceId) {
        setTitleTv(context.getResources().getString(resourceId));
    }

    public void setTitleTv(String title) {
        setTitleTv(title, true);
    }

    public void setTitleTv(int resourceId, boolean isBold) {
        setTitleTv(context.getResources().getString(resourceId), isBold);
    }

    public void setTitleTv(String title, boolean isBold) {
        if (isBold) {
            titleTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        }
        titleTv.setVisibility(View.VISIBLE);
        titleTv.setText(title);
    }

    public void setCustomLinearLayout(View contentView) {
        emptyLl.setVisibility(View.VISIBLE);
        emptyLl.addView(contentView);
    }

    public void setDenyTv(String denyInfo, View.OnClickListener denyClickListener) {
        denyTv.setVisibility(View.VISIBLE);
        denyTv.setText(denyInfo);
        denyTv.setOnClickListener(denyClickListener);
    }

    public void setConfirmTv(String confirmInfo, View.OnClickListener confirmClickListener) {
        confirmTv.setVisibility(View.VISIBLE);
        confirmTv.setText(confirmInfo);
        confirmTv.setOnClickListener(confirmClickListener);
    }

    public void setCancelIv(View.OnClickListener cancelClickListener) {
        cancelIv.setVisibility(View.VISIBLE);
        cancelPairIv.setVisibility(View.VISIBLE);
        cancelIv.setOnClickListener(cancelClickListener);
    }

    public void setInfoText(String par1, String par2, String par3) {
        if (!TextUtils.isEmpty(par1)) {
            infoLl.setVisibility(View.VISIBLE);
            infoTv1.setVisibility(View.VISIBLE);
            infoTv1.setText(par1);
        }
        if (!TextUtils.isEmpty(par2)) {
            infoLl.setVisibility(View.VISIBLE);
            infoTv2.setVisibility(View.VISIBLE);
            infoTv2.setText(par2);
        }
        if (!TextUtils.isEmpty(par3)) {
            infoLl.setVisibility(View.VISIBLE);
            infoTv3.setVisibility(View.VISIBLE);
            infoTv3.setText(par3);
        }
    }

    public void setInfoText(String par1, String par2) {
        setInfoText(par1, par2, null);
    }

    public void setInfoText(String par1) {
        setInfoText(par1, null);
    }
}
