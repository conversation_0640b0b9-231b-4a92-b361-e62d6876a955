package com.totwoo.totwoo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.VideoView;

/**
 * 能实现全屏播放的VideoView,因为android自带得VideoView按照视频比例定死, 无法实现全屏处理
 * 
 * <AUTHOR>
 * @date 2015年10月10日
 */
public class FullScreenVideoView extends VideoView {
	private int mVideoWidth;
	private int mVideoHeight;

	public FullScreenVideoView(Context context) {
		super(context);
	}

	public FullScreenVideoView(Context context, AttributeSet attrs) {
		super(context, attrs);
	}

	public FullScreenVideoView(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
	}

	@Override
	protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
		// 下面的代码是让视频的播放的长宽是根据你设置的参数来决定

		int width = getDefaultSize(mVideoWidth, widthMeasureSpec);
		int height = getDefaultSize(mVideoHeight, heightMeasureSpec);
		setMeasuredDimension(width, height);
	}
}
