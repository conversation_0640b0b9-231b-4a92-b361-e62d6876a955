package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.Arrays;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SafeTimeSelectDialog extends Dialog {
    @BindView(R.id.safe_hour_wv)
    WheelViewWhite mHourWheelView;
    @BindView(R.id.safe_minutes_wv)
    WheelViewWhite mMinutesWheelView;
    @BindView(R.id.safe_time_cancel_tv)
    TextView mCancelTv;
    @BindView(R.id.safe_time_sure_tv)
    TextView mSureTv;

    private View rootView;
    private TimeSelectListener timeSelectListener;

    public SafeTimeSelectDialog(Context context) {
        super(context, R.style.safe_time_dialog);
        initDialog(context);
    }

    public SafeTimeSelectDialog(Context context, int theme) {
        super(context, theme);
        initDialog(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(rootView);

        // 设置全屏， 靠底部展示
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = Apputils.getScreenWidth(getContext());
        getWindow().setAttributes(params);
        getWindow().setGravity(Gravity.BOTTOM);
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.dialog_safe_select_time, null);
        ButterKnife.bind(this,rootView);
        mHourWheelView.setItems(Arrays.asList(ConfigData.SAFE_SELECT_HOUR),5,context.getString(R.string.safe_guard_hour));
        mHourWheelView.setSeletion(1);
        mMinutesWheelView.setItems(Arrays.asList(ConfigData.SAFE_SELECT_MINUTES),5,context.getString(R.string.safe_guard_min));
        mMinutesWheelView.setSeletion(0);
        mCancelTv.setOnClickListener(v -> dismiss());
        mSureTv.setOnClickListener(v -> {
            if(mHourWheelView.getSeletedIndex() == 0 && mMinutesWheelView.getSeletedIndex() == 0){
                ToastUtils.showShort(context,R.string.safe_guard_time_error);
                return;
            }
            timeSelectListener.onSelectTime(mHourWheelView.getSeletedItem(),mMinutesWheelView.getSeletedItem());
        });
    }

    public void resetSeletion(){
        mHourWheelView.setSeletion(1);
        mMinutesWheelView.setSeletion(0);
    }

    public void setSureClickListener(TimeSelectListener timeSelectListener){
        this.timeSelectListener = timeSelectListener;
    }

    public interface TimeSelectListener{
        void onSelectTime(String hour,String min);
    }
}
