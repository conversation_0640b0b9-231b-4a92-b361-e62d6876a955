package com.totwoo.totwoo.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.HorizontalScrollView;

/**
 * Created by lixingmao on 2017/3/24.
 */

public class NoTouchHorizontalScrollView extends HorizontalScrollView {
    public NoTouchHorizontalScrollView(Context context) {
        super(context);
    }

    public NoTouchHorizontalScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NoTouchHorizontalScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public NoTouchHorizontalScrollView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        return false;
    }
}
