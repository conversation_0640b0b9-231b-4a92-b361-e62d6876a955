package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.SizeUtils;
import com.totwoo.totwoo.R;

public class CertificationTargetDialog extends Dialog {

    private Context context;
    private View.OnClickListener onClickListener;
    private TextView mCountTv;
    private TextView mInfoTv;
    private View rootView;

    public CertificationTargetDialog(Context context, View.OnClickListener onClickListener) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        initDialog(context);
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.certification_target_dialog, null);

        mCountTv = rootView.findViewById(R.id.certification_target_count_tv);
        mInfoTv = rootView.findViewById(R.id.certification_target_info_tv);
        rootView.findViewById(R.id.certification_target_sure_tv).setOnClickListener(onClickListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(rootView);

        if (getWindow() != null && getWindow().getAttributes() != null) {
            WindowManager.LayoutParams params = getWindow().getAttributes();
            params.width = SizeUtils.dp2px(300);
            getWindow().setAttributes(params);
        }

    }

    public void setCount(int count) {
        String text = context.getString(R.string.certification_dialog_target_count, count);

        String contStr = String.valueOf(count);
        mCountTv.setText(setStyle(text, text.indexOf(contStr), text.indexOf(contStr) + contStr.length()));
    }

    public void setInfo(String info) {
        String text = context.getString(R.string.certification_dialog_target_info, info);

        mInfoTv.setText(setStyle(text, text.indexOf(info) - 1, text.indexOf(info) + info.length() + 1));
    }

    private SpannableString setStyle(String string, int index, int endIndex) {
        try {
            SpannableString spannableString = new SpannableString(string);
            spannableString.setSpan(new AbsoluteSizeSpan(17, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context,R.color.color_main)), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spannableString;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new SpannableString("");
    }
}