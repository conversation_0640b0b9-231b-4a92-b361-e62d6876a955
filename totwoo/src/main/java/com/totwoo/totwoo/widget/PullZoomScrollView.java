package com.totwoo.totwoo.widget;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ScrollView;

import com.totwoo.library.util.Apputils;

/**
 * designed by gpl
 */
public class PullZoomScrollView extends ScrollView implements View.OnTouchListener {
    /**
     * The view is not scrolling. Note navigating the list using the trackball counts as
     * being in the idle state since these transitions are not animated.
     */
    public static int SCROLL_STATE_IDLE = 0;

    /**
     * The user is scrolling using touch, and their finger is still on the screen
     */
    public static int SCROLL_STATE_TOUCH_SCROLL = 1;

    /**
     * The user had previously been scrolling using touch and had performed a fling. The
     * animation is now coasting to a stop
     */
    public static int SCROLL_STATE_FLING = 2;

    //mFirstPosition's value when mZoomView not being scaled
    private static final float POSITION_ORIGIN = -Integer.MAX_VALUE;
    private float maxDistance;
    // the first position that should be recorded
    private float mFirstPosition = POSITION_ORIGIN;
    // show the view scall state
    private Boolean mScaling = false;
    // the view will be scalled
    private View mZoomView;
    //mZoomView original width
    private int mZoomViewOriginalWidth;
    //mZoomView original height
    private int mZoomViewOriginalHeight;

    private View[] targetViews;

    private static final float PARALLAX_SCALE = 0.4f;


    public interface OnScrollListener {

        void onScrollStateChanged(ScrollView view, int scrollState);

        void onScroll(ScrollView view, boolean isTouchScroll, int l, int t, int oldl, int oldt);
    }

    private static final boolean DEBUG = true;

    private static final int CHECK_SCROLL_STOP_DELAY_MILLIS = 80;
    private static final int MSG_SCROLL = 1;

    private boolean mIsTouched = false;
    private int mScrollState = SCROLL_STATE_IDLE;

    private OnScrollListener mOnScrollListener;

    private final Handler mHandler = new Handler(Looper.getMainLooper(), new Handler.Callback() {

        private int mLastY = Integer.MIN_VALUE;

        @Override
        public boolean handleMessage(Message msg) {
            if (msg.what == MSG_SCROLL) {
                final int scrollY = getScrollY();
                log("handleMessage, lastY = " + mLastY + ", y = " + scrollY);
                if (!mIsTouched && mLastY == scrollY) {
                    mLastY = Integer.MIN_VALUE;
                    setScrollState(SCROLL_STATE_IDLE);
                } else {
                    mLastY = scrollY;
                    restartCheckStopTiming();
                }
                return true;
            }
            return false;
        }
    });

    private void restartCheckStopTiming() {
        mHandler.removeMessages(MSG_SCROLL);
        mHandler.sendEmptyMessageDelayed(MSG_SCROLL, CHECK_SCROLL_STOP_DELAY_MILLIS);
    }


    public PullZoomScrollView(Context context) {
        super(context);
    }

    public PullZoomScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public PullZoomScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        init();
    }

    public interface ScrollViewListener
    {
        void onScrollViewChanged(PullZoomScrollView sc, int x, int y, int oldx, int oldy);
    }

    public ScrollViewListener scrollViewListener;

    public void setScrollViewListener(ScrollViewListener scrollViewListener) {
        this.scrollViewListener = scrollViewListener;
    }

    private void init() {
        setOverScrollMode(OVER_SCROLL_NEVER);
        if (getChildAt(0) != null) {
            ViewGroup vg = (ViewGroup) getChildAt(0);
            if (vg.getChildAt(0) != null) {
                mZoomView = vg.getChildAt(0);
                setOnTouchListener(this);
            }
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if (mZoomViewOriginalWidth <= 0 || mZoomViewOriginalHeight <= 0) {
            mZoomViewOriginalWidth = mZoomView.getMeasuredWidth();
            mZoomViewOriginalHeight = mZoomView.getMeasuredHeight();
            maxDistance = mZoomViewOriginalHeight * 3 / 4;
            if (maxDistance > Apputils.getScreenHeight(getContext()) - mZoomViewOriginalHeight) {
                maxDistance = Apputils.getScreenHeight(getContext()) - mZoomViewOriginalHeight;
            }
        }
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                handleDownEvent(event);
                break;
            case MotionEvent.ACTION_MOVE:
                //when rootview scrolldistance==0 record the touch position by mFirstPosition
                if (!mScaling && (getScrollY() == 0)) {
                    mFirstPosition = event.getY();
                } else if (!mScaling) break;
                int distance = (int) ((event.getY() - mFirstPosition));
                // when scroll up relative to mFirstPosition we will do nothing
                if (distance < 0) {
                    mScaling = false;
                    break;
                }
                // when scroll down relative to mFirstPosition an from we will scall the mZoomView
                mScaling = true;
                doZoom(distance, false);
                return true;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                recoverZoomView();
                handleUpEvent(event);
                break;
            default:
                break;
        }
        return false;
    }

    public void setZooViews(View... views) {
        this.targetViews = views.clone();
    }

    public void setOnScrollListener(OnScrollListener onScrollListener) {
        mOnScrollListener = onScrollListener;
    }

    /**
     * zoom view
     *
     * @param s
     */
    public void doZoom(float s, boolean isRecover) {
        if (s <= 0) return;

        if (!isRecover)
            s *= PARALLAX_SCALE;

        s = s > maxDistance ? maxDistance : s;
        if (mZoomViewOriginalHeight <= 0 || mZoomViewOriginalWidth <= 0) {
            return;
        }

        if (targetViews != null) {
            for (int i = 0; i < targetViews.length; i++) {
                View child = targetViews[i];
                ViewGroup.LayoutParams lp = child.getLayoutParams();
                lp.width = (int) (mZoomViewOriginalWidth + s);
                lp.height = (int) (mZoomViewOriginalHeight * ((mZoomViewOriginalWidth + s) / mZoomViewOriginalWidth));
                child.setLayoutParams(lp);
            }
        } else {
            ViewGroup.LayoutParams lp = mZoomView.getLayoutParams();
            lp.width = (int) (mZoomViewOriginalWidth + s);
            lp.height = (int) (mZoomViewOriginalHeight * ((mZoomViewOriginalWidth + s) / mZoomViewOriginalWidth));
            mZoomView.setLayoutParams(lp);
        }

        if (mOnScrollListener != null) {
            mOnScrollListener.onScroll(this, true, 0, (int) -s, 0, 0);
        }
    }

    /**
     * set zoom view to original state
     */
    public void recoverZoomView() {
        if (!mScaling) return;
        float distance = 0;

        if (targetViews != null) {
            for (int i = 0; i < targetViews.length; i++) {
                View child = targetViews[i];
                distance = child.getMeasuredWidth() - mZoomViewOriginalWidth;
            }
        } else {
            distance = mZoomView.getMeasuredWidth() - mZoomViewOriginalWidth;
        }

        ValueAnimator anim = ObjectAnimator.ofFloat(0.0F, 1.0F).setDuration((long) (distance * 0.618));
        final float finalDistance = distance;
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cVal = (Float) animation.getAnimatedValue();
                doZoom(finalDistance - ((finalDistance) * cVal), true);
            }
        });
        anim.setInterpolator(new AccelerateDecelerateInterpolator());
        anim.start();
        mScaling = false;
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        log(String.format("onScrollChanged, isTouched = %s, l: %d --> %d, t: %d --> %d", mIsTouched, oldl, l, oldt, t));
        if (mIsTouched) {
            setScrollState(SCROLL_STATE_TOUCH_SCROLL);
        } else {
            setScrollState(SCROLL_STATE_FLING);
            restartCheckStopTiming();
        }
        if (mOnScrollListener != null) {
            mOnScrollListener.onScroll(this, mIsTouched, l, t, oldl, oldt);
        }

        if (scrollViewListener != null) {
            scrollViewListener.onScrollViewChanged(this, l, t, oldl, oldt);
        }
    }

    private void handleDownEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                log("handleEvent, action = " + ev.getAction());
                mIsTouched = true;
                break;
        }
    }

    private void handleUpEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                log("handleEvent, action = " + ev.getAction());
                mIsTouched = false;
                restartCheckStopTiming();
                break;
        }
    }

    private void setScrollState(int state) {
        if (mScrollState != state) {
            log(String.format("---- onScrollStateChanged, state: %d --> %d", mScrollState, state));
            mScrollState = state;
            if (mOnScrollListener != null) {
                mOnScrollListener.onScrollStateChanged(this, state);
            }
        }
    }

    private void log(String obj) {
        if (DEBUG) {
            Log.d(getClass().getSimpleName(), obj);
        }
    }
}