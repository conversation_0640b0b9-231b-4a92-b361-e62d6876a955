package com.totwoo.totwoo.widget

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import android.widget.ImageView
import com.totwoo.totwoo.R

/**
 * 隐私保护说明弹窗
 * <AUTHOR>
 * @date 2024/12/16
 */
class PrivacyProtectionDialog(
    context: Context
) : Dialog(context, R.style.FullScreenDialogStyle) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_privacy_protection)

        // 设置全屏及动画
        window?.let { window ->
            val params = window.attributes
            params.width = WindowManager.LayoutParams.MATCH_PARENT
            params.height = WindowManager.LayoutParams.MATCH_PARENT
            params.gravity = Gravity.BOTTOM
            window.attributes = params

            // 设置动画
            window.setWindowAnimations(R.style.BottomDialogAnimation)
        }


        // 初始化对话框
        initDialog()
    }

    private fun initDialog() {
        // 获取视图组件
        val ivClose = findViewById<ImageView>(R.id.dialog_privacy_close_iv)
        // 设置关闭按钮点击事件
        ivClose.setOnClickListener { dismiss() }
        
        // 设置点击外部可关闭
        setCanceledOnTouchOutside(true)
    }

    override fun dismiss() {
        try {
            super.dismiss()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
