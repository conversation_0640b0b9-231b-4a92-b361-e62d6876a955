package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;

/**
 * 文本弹窗, 取消按钮位于弹窗下方, 透明的关闭按钮
 */
public class CustomMiddleTextDialog extends Dialog {

    private ImageView mainIconIv;
    private TextView titleTv;
    private FrameLayout emptyLl;
    private FrameLayout titleFl;
    private TextView denyTv;
    private TextView confirmTv;
    private ImageView cancelIv;
    private ImageView cancelPairIv;
    private LinearLayout infoLl;
    private LinearLayout customMiddleLl;
    private TextView infoTv1;
    private TextView infoTv2;
    private TextView infoTv3;
    private TextView infoTvAdd;

    Context context;
    private View rootView;

    public CustomMiddleTextDialog(Context context) {
        super(context, R.style.MyDialog);
        this.context = context;
        rootView = LayoutInflater.from(context).inflate(
                R.layout.custom_middle_text_dialog, null);
        mainIconIv = rootView.findViewById(R.id.custom_middle_icon_iv);
        customMiddleLl = rootView.findViewById(R.id.custom_middle_ll);
        titleTv = rootView.findViewById(R.id.custom_middle_title_tv);
        titleFl = rootView.findViewById(R.id.custom_middle_title_fl);
        emptyLl = rootView.findViewById(R.id.custom_middle_empty_fl);
        denyTv = rootView.findViewById(R.id.custom_middle_deny_tv);
        confirmTv = rootView.findViewById(R.id.custom_middle_confirm_tv);
        cancelIv = rootView.findViewById(R.id.custom_middle_cancel_iv);
        cancelPairIv = rootView.findViewById(R.id.custom_middle_cancel_pair_iv);
        infoLl = rootView.findViewById(R.id.custom_middle_info_ll);
        infoTv1 = rootView.findViewById(R.id.custom_middle_info_tv1);
        infoTv2 = rootView.findViewById(R.id.custom_middle_info_tv2);
        infoTv3 = rootView.findViewById(R.id.custom_middle_info_tv3);
        infoTvAdd = rootView.findViewById(R.id.custom_middle_info_add);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(rootView);

        // 为Dialog启用Edge-to-Edge适配
        EdgeToEdgeUtils.enableEdgeToEdgeForDialog(this);
    }

    public void setWidth(int width) {
        setViewWidth(rootView,width);
        setViewWidth(customMiddleLl,width);
    }

    private void setViewWidth(View  view,int width) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        if (params != null) {
            params.width = width;
            rootView.setLayoutParams(params);
        }
    }

    public void setMainIconIv(int resourceId) {
        mainIconIv.setVisibility(View.VISIBLE);
        mainIconIv.setImageResource(resourceId);
    }

    public void setTextAdd(int resourceId) {
        infoTvAdd.setVisibility(View.VISIBLE);
        infoTvAdd.setText(resourceId);
    }

    public void setTextAdd(String title,View.OnClickListener addClickListener) {
        infoTvAdd.setVisibility(View.VISIBLE);
        infoTvAdd.setText(title);
        infoTvAdd.setOnClickListener(addClickListener);
    }

    public void setTitleTv(int resourceId) {
        setTitleTv(context.getResources().getString(resourceId));
    }

    public void setTitleTv(String title) {
        setTitleTv(title, true);
    }

    public void setTitleTv(int resourceId, boolean isBold) {
        setTitleTv(context.getResources().getString(resourceId), isBold);
    }

    public void setTitleTv(String title, boolean isBold) {
        if (isBold) {
            titleTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        }
        titleTv.setVisibility(View.VISIBLE);
        titleTv.setText(title);
    }

    public void setCustomLinearLayout(View contentView) {
        emptyLl.setVisibility(View.VISIBLE);
        emptyLl.addView(contentView);
    }

    public void setTitleView(View contentView){
        titleFl.setVisibility(View.VISIBLE);
        titleFl.addView(contentView);
    }

    public void setDenyTv(String denyInfo, View.OnClickListener denyClickListener) {
        denyTv.setVisibility(View.VISIBLE);
        denyTv.setText(denyInfo);
        denyTv.setOnClickListener(denyClickListener);
    }

    public void setConfirmTv(String confirmInfo, View.OnClickListener confirmClickListener) {
        confirmTv.setVisibility(View.VISIBLE);
        confirmTv.setText(confirmInfo);
        confirmTv.setOnClickListener(confirmClickListener);
    }

    public void setCancelIv(View.OnClickListener cancelClickListener) {
        cancelIv.setVisibility(View.VISIBLE);
        cancelPairIv.setVisibility(View.VISIBLE);
        cancelIv.setOnClickListener(cancelClickListener);
    }

    public void setInfoText(String par1, String par2, String par3) {
        if (!TextUtils.isEmpty(par1)) {
            infoLl.setVisibility(View.VISIBLE);
            infoTv1.setVisibility(View.VISIBLE);
            infoTv1.setText(par1);
        }
        if (!TextUtils.isEmpty(par2)) {
            infoLl.setVisibility(View.VISIBLE);
            infoTv2.setVisibility(View.VISIBLE);
            infoTv2.setText(par2);
        }
        if (!TextUtils.isEmpty(par3)) {
            infoLl.setVisibility(View.VISIBLE);
            infoTv3.setVisibility(View.VISIBLE);
            infoTv3.setText(par3);
        }
    }

    public void setInfoText(String par1, String par2) {
        setInfoText(par1, par2, null);
    }

    public void setInfoText(String par1) {
        setInfoText(par1, null);
    }
}
