package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.StyleSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.RankUserInfosBean;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;


public class CertificationShareDialog extends Dialog {
    @BindView(R.id.common_share_dialog_title)
    TextView tvTitle;
    private View rootView;
    @BindView(R.id.common_share_rv)
    RecyclerView rvIcons;
    @BindView(R.id.love_space_share_cancel)
    ImageView mCancel;
    @BindView(R.id.love_space_share_cl)
    ConstraintLayout mMainCl;
    @BindView(R.id.love_space_share_content_iv)
    ImageView mContentIv;
    @BindView(R.id.love_space_share_me)
    ImageView selfIv;
    @BindView(R.id.love_space_share_other)
    ImageView otherIv;
    @BindView(R.id.love_space_share_name_tv)
    TextView mNameTv;
    @BindView(R.id.love_space_share_count_tv)
    TextView mCountTv;
    @BindView(R.id.love_space_share_info_tv)
    TextView mInfoTv;

    private List<CommonShareType> types;
    private View.OnClickListener itemClickListener;
    private Context mContext;

    public CertificationShareDialog(Context context, List<CommonShareType> types, View.OnClickListener itemClickListener) {
        super(context, R.style.custom_dialog_99ff);
        initDialog(context);
        mContext = context;
        this.types = types;
        this.itemClickListener = itemClickListener;
        rvIcons.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        CommonShareIconAdapter commonShareIconAdapter = new CommonShareIconAdapter();
        rvIcons.setAdapter(commonShareIconAdapter);
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.certification_share_dialog, null);
        ButterKnife.bind(this,rootView);
        mCancel.setOnClickListener(v -> dismiss());
    }

    public View getShareView(){
        return mMainCl;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(rootView);

        // 设置全屏， 靠底部展示
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = Apputils.getScreenWidth(getContext());
        params.height = Apputils.getScreenHeight(getContext());
        getWindow().setAttributes(params);
        getWindow().setGravity(Gravity.BOTTOM);
    }

    public void setCustomTitle(CharSequence title) {
        tvTitle.setVisibility(View.VISIBLE);
        tvTitle.setText(title);
    }

    public void setBackground(int resId){
        mMainCl.setBackgroundResource(resId);
    }

    public void setContent(int resId){
        mContentIv.setImageResource(resId);
    }

    public void setUserInfo(RankUserInfosBean rankUserInfosBean) {
        RequestOptions femaleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        RequestOptions maleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        if (rankUserInfosBean.getSelf().getSex() == 0) {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getSelf().getHead_portrait())).apply(maleOptions).into(selfIv);
        } else {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getSelf().getHead_portrait())).apply(femaleOptions).into(selfIv);
        }
        if (rankUserInfosBean.getTarget().getSex() == 0) {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getTarget().getHead_portrait())).apply(maleOptions).into(otherIv);
        } else {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getTarget().getHead_portrait())).apply(femaleOptions).into(otherIv);
        }
        mNameTv.setText(mContext.getString(R.string.love_space_share_name, rankUserInfosBean.getTarget().getNick_name(), rankUserInfosBean.getSelf().getNick_name()));
    }

    public void setCount(int count) {
        String text = mContext.getString(R.string.certification_dialog_level_count, count);
        if (Apputils.systemLanguageIsChinese(mContext)) {
            mCountTv.setText(setStyle(text, 5, 6 + String.valueOf(count).length()));
        } else {
            mCountTv.setText(setStyle(text, 20, 21 + String.valueOf(count).length()));
        }
    }

    public void setInfo(String info) {
        String text = mContext.getString(R.string.certification_dialog_level_info, info);
        if (Apputils.systemLanguageIsChinese(mContext)) {
            mInfoTv.setText(setStyle(text, 6, 9 + info.length()));
        } else {
            mInfoTv.setText(setStyle(text, 30, 33 + info.length()));
        }
    }

    private SpannableString setStyle(String string, int index, int endIndex) {
        SpannableString spannableString = new SpannableString(string);
        endIndex = Math.min(endIndex, string.length());
        spannableString.setSpan(new AbsoluteSizeSpan(19, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private class CommonShareIconAdapter extends RecyclerView.Adapter<CommonShareIconAdapter.ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.love_space_share_icon_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.mIconIv.setOnClickListener(itemClickListener);
            switch (types.get(position)) {
                case QQ:
                    holder.mIconIv.setImageResource(R.drawable.share_common_qq);
                    holder.mIconIv.setTag(CommonShareType.QQ);
                    break;
                case QZONE:
                    holder.mIconIv.setImageResource(R.drawable.share_common_qzone);
                    holder.mIconIv.setTag(CommonShareType.QZONE);
                    break;
                case WECHAT:
                    holder.mIconIv.setImageResource(R.drawable.share_common_wechat);
                    holder.mIconIv.setTag(CommonShareType.WECHAT);
                    break;
                case FRIENDS:
                    holder.mIconIv.setImageResource(R.drawable.share_common_friends);
                    holder.mIconIv.setTag(CommonShareType.FRIENDS);
                    break;
                case WEIBO:
                    holder.mIconIv.setImageResource(R.drawable.share_common_weibo);
                    holder.mIconIv.setTag(CommonShareType.WEIBO);
                    break;
                case MESSAGE:
                    holder.mIconIv.setImageResource(R.drawable.share_common_msg);
                    holder.mIconIv.setTag(CommonShareType.MESSAGE);
                    break;
                case TWITTER:
                    holder.mIconIv.setImageResource(R.drawable.share_common_twitter);
                    holder.mIconIv.setTag(CommonShareType.TWITTER);
                    break;
                case FACEBOOK:
                    holder.mIconIv.setImageResource(R.drawable.share_common_fb);
                    holder.mIconIv.setTag(CommonShareType.FACEBOOK);
                    break;
            }
        }

        @Override
        public int getItemCount() {
            return types.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mIconIv;

            public ViewHolder(View itemView) {
                super(itemView);
                mIconIv = (ImageView) itemView.findViewById(R.id.common_share_icon_iv);
            }
        }
    }
}