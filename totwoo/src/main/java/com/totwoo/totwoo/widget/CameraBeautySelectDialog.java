//package com.totwoo.totwoo.widget;
//
//import android.content.Context;
//import android.content.DialogInterface;
//import androidx.constraintlayout.widget.ConstraintLayout;
//import androidx.recyclerview.widget.LinearLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//import android.text.TextPaint;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.SeekBar;
//import android.widget.TextView;
//
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.adapter.BeautyStyleAdapter;
//import com.totwoo.totwoo.adapter.FilterLibraryAdapter;
//import com.totwoo.totwoo.utils.CommonArgs;
//import com.totwoo.totwoo.utils.TrackEvent;
//import com.umeng.analytics.MobclickAgent;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//import cn.tillusory.sdk.TiSDKManager;
//
///**
// * Created by totwoo on 2018/8/22.
// */
//
//public class CameraBeautySelectDialog implements View.OnClickListener,BeautyStyleAdapter.BeautySelectListener {
//    private CustomDialog dialog;
//    private Context context;
//    @BindView(R.id.camera_beauty_retouch_ll)
//    LinearLayout camera_beauty_retouch_ll;
//    @BindView(R.id.camera_beauty_retouch_control_cl)
//    ConstraintLayout camera_beauty_retouch_control_cl;
//    @BindView(R.id.camera_beauty_beauty_control_cl)
//    ConstraintLayout camera_beauty_beauty_control_cl;
//    @BindView(R.id.camera_beauty_filter_rv)
//    RecyclerView camera_beauty_filter_rv;
//    @BindView(R.id.camera_style_rv)
//    RecyclerView camera_style_rv;
//    @BindView(R.id.camera_beauty_retouch_control_tv)
//    TextView camera_beauty_retouch_control_tv;
//    @BindView(R.id.camera_beauty_retouch_control_view)
//    View camera_beauty_retouch_control_view;
//    @BindView(R.id.camera_beauty_beauty_control_tv)
//    TextView camera_beauty_beauty_control_tv;
//    @BindView(R.id.camera_beauty_beauty_control_view)
//    View camera_beauty_beauty_control_view;
//    @BindView(R.id.camera_beauty_filter_control_tv)
//    TextView camera_beauty_filter_control_tv;
//    @BindView(R.id.camera_beauty_filter_control_view)
//    View camera_beauty_filter_control_view;
//    @BindView(R.id.camera_beauty_sb)
//    SeekBar camera_beauty_sb;
//    @BindView(R.id.face_camera_retouch_none_iv)
//    ImageView face_camera_retouch_none_iv;
//    @BindView(R.id.face_camera_retouch_white_iv)
//    ImageView face_camera_retouch_white_iv;
//    @BindView(R.id.face_camera_retouch_re_skin_iv)
//    ImageView face_camera_retouch_re_skin_iv;
//    @BindView(R.id.face_camera_retouch_pink_iv)
//    ImageView face_camera_retouch_pink_iv;
//    @BindView(R.id.face_camera_retouch_saturation_iv)
//    ImageView face_camera_retouch_saturation_iv;
//
//    private int current_retouch_position;
//    private int current_beauty_position;
//    private int current_type;
//    private int d_white = CommonArgs.DEFAULT_WHITE;
//    private int d_re_skin = CommonArgs.DEFAULT_RE_SKIN;
//    private int d_pink = CommonArgs.DEFAULT_PINK;
//    private int d_saturation = CommonArgs.DEFAULT_SATURATION;
//    private int d_re_eye = CommonArgs.DEFAULT_RE_EYE;
//    private int d_cheeks = CommonArgs.DEFAULT_CHEEKS;
//
//    private CameraBeautyClickListener cameraBeautyClickListener;
//    private BeautyStyleAdapter beautyStyleAdapter;
//
//    public CameraBeautySelectDialog(Context context) {
//        this.context = context;
//    }
//
//    public void showBeautyDialog(DialogInterface.OnDismissListener onDismissListener, CameraBeautyClickListener cameraBeautyClickListenerSet, TiSDKManager tiSDKManager) {
//        if (dialog == null) {
//            cameraBeautyClickListener = cameraBeautyClickListenerSet;
//            dialog = new CustomDialog(context, R.style.custom_dialog_tran);
//            View v = View.inflate(context, R.layout.camera_beauty_select_dialog, null);
//            dialog.setRootView(v);
//            ButterKnife.bind(this, v);
//
//            camera_beauty_sb.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
//                @Override
//                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
//                    if(!fromUser)
//                        return;
//
//                    int showProgress = seekBar.getProgress();
//                    if(current_type == 1){
//                        if(current_beauty_position == 3 || current_beauty_position == 5 || current_beauty_position == 6){
//                            showProgress = showProgress - 50;
//                        }
//                    }
//                    cameraBeautyClickListener.onSeekBarChange(showProgress);
//                    if (current_type == 0) {
//                        switch (current_retouch_position){
//                            case 1:
//                                cameraBeautyClickListener.onWhiteProgressChange(progress);
//                                break;
//                            case 2:
//                                cameraBeautyClickListener.onReSkinProgressChange(progress);
//                                break;
//                            case 3:
//                                cameraBeautyClickListener.onPinkProgressChange(progress);
//                                break;
//                            case 4:
//                                cameraBeautyClickListener.onSaturationProgressChange(progress);
//                                break;
//                        }
//                    } else if (current_type == 1) {
//                        beautyStyleAdapter.setProgress(progress);
//                    }
//                }
//
//                @Override
//                public void onStartTrackingTouch(SeekBar seekBar) {
//                    LogUtils.e("SeekBar onStartTrackingTouch");
//                    int showProgress = seekBar.getProgress();
//                    if(current_type == 1){
//                        if(current_beauty_position == 3 || current_beauty_position == 5 || current_beauty_position == 6){
//                            showProgress = showProgress - 50;
//                        }
//                    }
//                    cameraBeautyClickListener.onSeekBarShow(showProgress);
//                }
//
//                @Override
//                public void onStopTrackingTouch(SeekBar seekBar) {
//                    LogUtils.e("SeekBar onStopTrackingTouch");
//                    cameraBeautyClickListener.onSeekBarDismiss();
//                }
//            });
//
//            camera_style_rv.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
//            beautyStyleAdapter = new BeautyStyleAdapter(context,tiSDKManager);
//            camera_style_rv.setAdapter(beautyStyleAdapter);
//            beautyStyleAdapter.setBeautySelectListener(this);
//
//            camera_beauty_filter_rv.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
//            FilterLibraryAdapter filterLibraryAdapter = new FilterLibraryAdapter(context,tiSDKManager);
//            camera_beauty_filter_rv.setAdapter(filterLibraryAdapter);
//            bottomControl(0);
//            dialog.setOnDismissListener(onDismissListener);
//
//        }
//        dialog.show();
//    }
//
//    public void dismissDialog(){
//        if(dialog != null && dialog.isShowing()){
//            dialog.dismiss();
//        }
//    }
//
//    @OnClick({R.id.camera_beauty_retouch_control_cl, R.id.camera_beauty_beauty_control_cl, R.id.camera_beauty_filter_control_cl,
//            R.id.face_camera_retouch_none_iv, R.id.face_camera_retouch_white_iv, R.id.face_camera_retouch_re_skin_iv, R.id.face_camera_retouch_pink_iv,
//            R.id.face_camera_retouch_saturation_iv})
//    public void onClick(View view) {
//        switch (view.getId()) {
//            case R.id.camera_beauty_retouch_control_cl:
//                saveSeekBar();
//                bottomControl(0);
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CAMERA_RESKIN);
//                current_type = 0;
//                break;
//            case R.id.camera_beauty_beauty_control_cl:
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CAMERA_RETOUCH);
//                saveSeekBar();
//                bottomControl(1);
//                current_type = 1;
//                break;
//            case R.id.camera_beauty_filter_control_cl:
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CAMERA_FILTER);
//                saveSeekBar();
//                bottomControl(2);
//                current_type = 2;
//                break;
//            case R.id.face_camera_retouch_none_iv:
//                retouchControl(0);
//                break;
//            case R.id.face_camera_retouch_white_iv:
//                retouchControl(1);
//                break;
//            case R.id.face_camera_retouch_re_skin_iv:
//                retouchControl(2);
//                break;
//            case R.id.face_camera_retouch_pink_iv:
//                retouchControl(3);
//                break;
//            case R.id.face_camera_retouch_saturation_iv:
//                retouchControl(4);
//                break;
//        }
//    }
//
//    private void retouchControl(int position) {
//        switch (position) {
//            case 0:
//                camera_beauty_sb.setVisibility(View.INVISIBLE);
//                d_white = 0;
//                d_re_skin = 0;
//                d_pink = 0;
//                d_saturation = 0;
//                face_camera_retouch_none_iv.setBackgroundResource(R.drawable.face_camera_select);
//                face_camera_retouch_white_iv.setBackground(null);
//                face_camera_retouch_re_skin_iv.setBackground(null);
//                face_camera_retouch_pink_iv.setBackground(null);
//                face_camera_retouch_saturation_iv.setBackground(null);
//                current_retouch_position = position;
//                cameraBeautyClickListener.onRetouchStateChange();
//                break;
//            case 1:
//                camera_beauty_sb.setVisibility(View.VISIBLE);
//                saveRetouchSeekBar();
//                face_camera_retouch_none_iv.setBackground(null);
//                face_camera_retouch_white_iv.setBackgroundResource(R.drawable.face_camera_select);
//                face_camera_retouch_re_skin_iv.setBackground(null);
//                face_camera_retouch_pink_iv.setBackground(null);
//                face_camera_retouch_saturation_iv.setBackground(null);
//                current_retouch_position = position;
//                camera_beauty_sb.setProgress(d_white);
//                break;
//            case 2:
//                camera_beauty_sb.setVisibility(View.VISIBLE);
//                saveRetouchSeekBar();
//                face_camera_retouch_none_iv.setBackground(null);
//                face_camera_retouch_white_iv.setBackground(null);
//                face_camera_retouch_re_skin_iv.setBackgroundResource(R.drawable.face_camera_select);
//                face_camera_retouch_pink_iv.setBackground(null);
//                face_camera_retouch_saturation_iv.setBackground(null);
//                current_retouch_position = position;
//                camera_beauty_sb.setProgress(d_re_skin);
//                break;
//            case 3:
//                camera_beauty_sb.setVisibility(View.VISIBLE);
//                saveRetouchSeekBar();
//                face_camera_retouch_none_iv.setBackground(null);
//                face_camera_retouch_white_iv.setBackground(null);
//                face_camera_retouch_re_skin_iv.setBackground(null);
//                face_camera_retouch_pink_iv.setBackgroundResource(R.drawable.face_camera_select);
//                face_camera_retouch_saturation_iv.setBackground(null);
//                current_retouch_position = position;
//                camera_beauty_sb.setProgress(d_pink);
//                break;
//            case 4:
//                camera_beauty_sb.setVisibility(View.VISIBLE);
//                saveRetouchSeekBar();
//                face_camera_retouch_none_iv.setBackground(null);
//                face_camera_retouch_white_iv.setBackground(null);
//                face_camera_retouch_re_skin_iv.setBackground(null);
//                face_camera_retouch_pink_iv.setBackground(null);
//                face_camera_retouch_saturation_iv.setBackgroundResource(R.drawable.face_camera_select);
//                current_retouch_position = position;
//                camera_beauty_sb.setProgress(d_saturation);
//                break;
//        }
//    }
//
//    private int getCurrentRetouchProgress() {
//        if (current_retouch_position == 1) {
//            return d_white;
//        } else if (current_retouch_position == 2) {
//            return d_re_skin;
//        } else if (current_retouch_position == 3) {
//            return d_pink;
//        } else if (current_retouch_position == 4) {
//            return d_saturation;
//        } else {
//            return 0;
//        }
//    }
//
//    private void saveRetouchSeekBar() {
//        if (current_retouch_position == 1) {
//            d_white = camera_beauty_sb.getProgress();
//        } else if (current_retouch_position == 2) {
//            d_re_skin = camera_beauty_sb.getProgress();
//        } else if (current_retouch_position == 3) {
//            d_pink = camera_beauty_sb.getProgress();
//        } else if (current_retouch_position == 4) {
//            d_saturation = camera_beauty_sb.getProgress();
//        }
//    }
//
//    private void saveBeautySeekBar() {
//        if (current_beauty_position == 1) {
//            d_re_eye = camera_beauty_sb.getProgress();
//        } else if (current_beauty_position == 2) {
//            d_cheeks = camera_beauty_sb.getProgress();
//        }
//    }
//
//    private void saveSeekBar() {
//        if (current_type == 0) {
//            saveRetouchSeekBar();
//        } else if (current_type == 1) {
//            saveBeautySeekBar();
//        }
//    }
//
//    private int getCurrentBeautyProgress() {
//        if (current_beauty_position == 1) {
//            return d_re_eye;
//        } else if (current_beauty_position == 2) {
//            return d_cheeks;
//        } else {
//            return 0;
//        }
//    }
//
//    private void bottomControl(int position) {
//        if (position == 0) {
//            camera_beauty_retouch_ll.setVisibility(View.VISIBLE);
//            camera_style_rv.setVisibility(View.GONE);
//            camera_beauty_filter_rv.setVisibility(View.GONE);
//
//            TextPaint paintRetouch = camera_beauty_retouch_control_tv.getPaint();
//            paintRetouch.setFakeBoldText(true);
//            camera_beauty_retouch_control_tv.setTextSize(16);
//            camera_beauty_retouch_control_view.setVisibility(View.VISIBLE);
//
//            TextPaint paintBeauty = camera_beauty_beauty_control_tv.getPaint();
//            paintBeauty.setFakeBoldText(false);
//            camera_beauty_beauty_control_tv.setTextSize(14);
//            camera_beauty_beauty_control_view.setVisibility(View.GONE);
//
//            TextPaint paint = camera_beauty_filter_control_tv.getPaint();
//            paint.setFakeBoldText(false);
//            camera_beauty_filter_control_tv.setTextSize(14);
//            camera_beauty_filter_control_view.setVisibility(View.GONE);
//
//            if (current_retouch_position == 0) {
//                camera_beauty_sb.setVisibility(View.INVISIBLE);
//            } else {
//                camera_beauty_sb.setVisibility(View.VISIBLE);
//            }
//            camera_beauty_sb.setProgress(getCurrentRetouchProgress());
//        } else if (position == 1) {
//            camera_beauty_retouch_ll.setVisibility(View.GONE);
//            camera_style_rv.setVisibility(View.VISIBLE);
//            camera_beauty_filter_rv.setVisibility(View.GONE);
//
//            TextPaint paintRetouch = camera_beauty_retouch_control_tv.getPaint();
//            paintRetouch.setFakeBoldText(false);
//            camera_beauty_retouch_control_tv.setTextSize(14);
//            camera_beauty_retouch_control_view.setVisibility(View.GONE);
//
//            TextPaint paintBeauty = camera_beauty_beauty_control_tv.getPaint();
//            paintBeauty.setFakeBoldText(true);
//            camera_beauty_beauty_control_tv.setTextSize(16);
//            camera_beauty_beauty_control_view.setVisibility(View.VISIBLE);
//
//            TextPaint paint = camera_beauty_filter_control_tv.getPaint();
//            paint.setFakeBoldText(false);
//            camera_beauty_filter_control_tv.setTextSize(14);
//            camera_beauty_filter_control_view.setVisibility(View.GONE);
//
//            if (current_beauty_position == 0) {
//                camera_beauty_sb.setVisibility(View.INVISIBLE);
//            } else {
//                camera_beauty_sb.setVisibility(View.VISIBLE);
//            }
//            camera_beauty_sb.setProgress(getCurrentBeautyProgress());
//        } else if (position == 2) {
//            camera_beauty_retouch_ll.setVisibility(View.GONE);
//            camera_style_rv.setVisibility(View.GONE);
//            camera_beauty_filter_rv.setVisibility(View.VISIBLE);
//
//            TextPaint paintRetouch = camera_beauty_retouch_control_tv.getPaint();
//            paintRetouch.setFakeBoldText(false);
//            camera_beauty_retouch_control_tv.setTextSize(14);
//            camera_beauty_retouch_control_view.setVisibility(View.GONE);
//
//            TextPaint paintBeauty = camera_beauty_beauty_control_tv.getPaint();
//            paintBeauty.setFakeBoldText(false);
//            camera_beauty_beauty_control_tv.setTextSize(14);
//            camera_beauty_beauty_control_view.setVisibility(View.GONE);
//
//            TextPaint paint = camera_beauty_filter_control_tv.getPaint();
//            paint.setFakeBoldText(true);
//            camera_beauty_filter_control_tv.setTextSize(16);
//            camera_beauty_filter_control_view.setVisibility(View.VISIBLE);
//            camera_beauty_sb.setVisibility(View.INVISIBLE);
//        }
//    }
//
//    @Override
//    public void onBeautySelect(int index, int progress) {
//        if (index == 0) {
//            camera_beauty_sb.setVisibility(View.INVISIBLE);
//        } else {
//            camera_beauty_sb.setVisibility(View.VISIBLE);
//        }
//        current_beauty_position = index;
//        camera_beauty_sb.setProgress(progress);
//    }
//
//    public interface CameraBeautyClickListener{
//        void onWhiteProgressChange(int progress);
//        void onReSkinProgressChange(int progress);
//        void onPinkProgressChange(int progress);
//        void onSaturationProgressChange(int progress);
//        void onRetouchStateChange();
//        void onSeekBarShow(int progress);
//        void onSeekBarDismiss();
//        void onSeekBarChange(int progress);
//    }
//}
