package com.totwoo.totwoo.widget;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.blankj.utilcode.util.ClickUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.utils.BitmapShareUtil;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

public class CertificationLevelDialog extends Dialog {
    private Context context;
    private TextView mCountTv;
    private TextView mInfoTv;
    private View rootView;
    private TextView mCheckTv;
    private ImageView mCancelIv;
    private AxxPagView mLottieView;
    private ConstraintLayout mMainCl;
    private ImageView mContentIv;
    private TextView mShareTv;
    private TextView mTitleTv;
    private ViewGroup includePairedHeadLayout;
    private TextView loveNameTv;

    private String key_from = "";

    public CertificationLevelDialog(@NonNull Context context) {
        super(context, R.style.custom_dialog_99ff);
        this.context = context;
        initDialog(context);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(rootView);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = Apputils.getScreenWidth(getContext());
        params.height = Apputils.getScreenHeight(getContext());
        getWindow().setAttributes(params);

        ClickUtils.expandClickArea(mCancelIv, 40);
        mCancelIv.setOnClickListener(v -> dismiss());
//        mLottieView.setImageAssetsFolder("certification_dialog_bg_play/");
//        mLottieView.setAnimation("certification_dialog_bg.json");
//        mLottieView.setRepeatCount(LottieDrawable.INFINITE);
    }

    @Override
    protected void onStart() {
        super.onStart();
//        mLottieView.playAnimation();

        mLottieView.playAnimation();
    }

    @Override
    protected void onStop() {
        super.onStop();
        mLottieView.pauseAnimation();
        mLottieView.clearAnimation();
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.certification_level_dialog, null);

        mCountTv = rootView.findViewById(R.id.certification_level_count_tv);
//        mInfoTv = rootView.findViewById(R.id.certification_level_info_tv);
        mTitleTv = rootView.findViewById(R.id.certification_title_tv);
        mCheckTv = rootView.findViewById(R.id.certification_check_tv);
        mCancelIv = rootView.findViewById(R.id.certification_level_cancel_iv);
        mLottieView = rootView.findViewById(R.id.certification_bg_lav);
        mMainCl = rootView.findViewById(R.id.certification_main_cl);
        mContentIv = rootView.findViewById(R.id.certification_content_iv);
        mShareTv = rootView.findViewById(R.id.certification_level_share_tv);

        includePairedHeadLayout = rootView.findViewById(R.id.include_paired_head_layout);
        ImageView loveManageOther = rootView.findViewById(R.id.love_manage_other);
        ImageView loveManageMe = rootView.findViewById(R.id.love_manage_me);
        TextView titleTv = rootView.findViewById(R.id.certification_level_title_tv);

        loveNameTv = rootView.findViewById(R.id.love_name_tv);

        CommonUtils.setStrokeWidth(titleTv, 1f);
        //中文才有的
        View qrCodeView = rootView.findViewById(R.id.imageView2);
        TextView qrContent = rootView.findViewById(R.id.textView7);


        String otherHead = PreferencesUtils.getString(context, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "");
        BitmapHelper.setHead(getContext(), loveManageMe, ToTwooApplication.owner.getHeaderUrl(), owner.getGender());
        BitmapHelper.setHead(getContext(), loveManageOther, otherHead, 0);//性别已无用

        String pairedName = PreferencesUtils.getString(context, CoupleLogic.PAIRED_PERSON_NICK_NAME, "");
        CommonUtils.setStrokeWidth(loveNameTv, 0.5f);
        loveNameTv.setText(context.getString(R.string.love_space_share_name, pairedName, ToTwooApplication.owner.getNickName()));
        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
            qrCodeView.setVisibility(View.VISIBLE);
            qrContent.setVisibility(View.VISIBLE);
            CommonUtils.setStrokeWidth(qrContent, 0.5f);
        } else {
            //loveManageOther 调整margin
            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) loveManageOther.getLayoutParams();
            params.topMargin = SizeUtils.dp2px(30f);
            loveManageOther.setLayoutParams(params);

            qrCodeView.setVisibility(View.GONE);
            qrContent.setVisibility(View.GONE);
        }

        mShareTv.setVisibility(View.VISIBLE);
        mShareTv.setOnClickListener(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TextUtils.equals(key_from, "home")? TrackEvent.HOME_CERTIFICATE_SHARE_BUTTON:TrackEvent.LOVE_CERTIFICATE_SHARE_BUTTON);
            //把dialog 截图，通过系统分享分享出去
            BitmapShareUtil.shareBitmap(context, ImageUtils.view2Bitmap(mMainCl));
        });
    }

    public void setCheckTv(View.OnClickListener onClickListener) {
        mCheckTv.setVisibility(View.VISIBLE);
        mCheckTv.setOnClickListener(onClickListener);
    }


    public void setKeyFrom(String from) {
        this.key_from = from;
    }

    public void setCount(int count, int imgRedId) {
        @SuppressLint("StringFormatMatches") String text = context.getString(R.string.certification_dialog_level_count, count);
        mCountTv.setText(text);
        mContentIv.setImageResource(imgRedId);
    }

    public void setInfo(String info) {
//        String text = context.getString(R.string.certification_dialog_level_info, info);
        mTitleTv.setText(info);
//        mInfoTv.setText(setStyle(text, text.indexOf(info) - 1, text.indexOf(info) + info.length() + 1));
    }

    private SpannableString setStyle(String string, int index, int endIndex) {
        try {
            SpannableString spannableString = null;
            spannableString = new SpannableString(string);
            spannableString.setSpan(new AbsoluteSizeSpan(19, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spannableString;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new SpannableString("");
    }

}
