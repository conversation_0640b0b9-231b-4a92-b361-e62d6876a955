package com.totwoo.totwoo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.totwoo.totwoo.R;

/**
 * 硬件连接过程中, 显示操作状态的大的圆形Btn<br>
 * 根据不同的状态, 做不同的展示<br>
 * 默认状态为 搜索中
 *
 * <AUTHOR>
 * @date 2015年9月5日
 */
public class JewConnectFailedCircle extends FrameLayout {
    private Context mContext;

    public JewConnectFailedCircle(Context context, int state) {
        super(context);
        this.mContext = context;
    }

    public JewConnectFailedCircle(Context context) {
        super(context);
        this.mContext = context;
        initConnectFailedLayout();
    }

    public JewConnectFailedCircle(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        initConnectFailedLayout();
    }

    public JewConnectFailedCircle(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        initConnectFailedLayout();
    }

//    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
//    public JewConnectFailedCircle(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
//        super(context, attrs, defStyleAttr, defStyleRes);
//        this.mContext = context;
//        initConnectFailedLayout();
//    }

    /**
     * 创建连接失败的界面
     */
    private void initConnectFailedLayout() {
        setBackgroundResource(R.drawable.jew_con_btn_bg_black);

        TextView tv = new TextView(mContext, null,
                R.style.connect_state_text_view);
        tv.setText(R.string.connect_failed);
        tv.setTextSize(14);
        tv.setCompoundDrawablePadding(mContext.getResources()
                .getDimensionPixelSize(R.dimen.jew_action_btn_text_padding));
        tv.setCompoundDrawablesRelativeWithIntrinsicBounds(0,
                R.drawable.con_failed, 0, 0);
        LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT, Gravity.CENTER);

        addView(tv, params);
    }
}
