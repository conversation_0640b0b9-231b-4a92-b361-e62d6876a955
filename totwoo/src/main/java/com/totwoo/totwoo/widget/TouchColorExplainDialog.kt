package com.totwoo.totwoo.widget

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.toColorInt
import com.blankj.utilcode.util.SpanUtils
import com.totwoo.totwoo.R
import com.totwoo.totwoo.ble.BleParams

/**
 * 自定义触摸颜色说明对话框
 * 全屏显示，只有左上和右上圆角
 */
class TouchColorExplainDialog(
    context: Context
) : Dialog(context, R.style.FullScreenDialogStyle) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_touch_color_explain)

        // 设置全屏及动画
        window?.let { window ->
            val params = window.attributes
            params.width = WindowManager.LayoutParams.MATCH_PARENT
            params.height = WindowManager.LayoutParams.MATCH_PARENT
            params.gravity = Gravity.BOTTOM
            window.attributes = params

            // 设置动画
            window.setWindowAnimations(R.style.BottomDialogAnimation)
        }

        // 点击外部不可取消
        setCanceledOnTouchOutside(true)

        // 设置关闭按钮点击事件
        findViewById<ImageView>(R.id.iv_close).setOnClickListener {
            dismiss()
        }
        val view = findViewById<View>(R.id.tv_touch_area)

        findViewById<ImageView>(R.id.ivGuide).run {
            if (BleParams.isMWJewlery()) {
                visibility = View.VISIBLE
                view.visibility = View.VISIBLE

//               if (BleParams.is33Jewlery()){
//                  setImageResource(R.drawable.product_jewelry_name_mwp)
//                }else if (BleParams.is34Jewlery()) {
//                   setImageResource(R.drawable.product_jewelry_name_mwe)
//                }
            } else {
                visibility = View.GONE
                view.visibility = View.GONE
            }
        }



        findViewById<TextView>(R.id.tvTips).run {
            SpanUtils.with(this)
                .append(context.getString(R.string.dialog_flash_set_content_tips)).setForegroundColor(
                    "#CC0045".toColorInt()).append("  ")
//                .setForegroundColor(Color.parseColor("#CC0045"))
                .append(context.getString(R.string.dialog_flash_set_content2)).create()
        }
    }
} 