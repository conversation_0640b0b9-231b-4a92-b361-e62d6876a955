//package com.totwoo.totwoo.widget;
//
//import android.content.Context;
//import android.content.DialogInterface;
//
//import androidx.recyclerview.widget.GridLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.LinearLayout;
//
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.adapter.PaperLibraryAdapter;
//import com.totwoo.totwoo.utils.CommonUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import cn.tillusory.sdk.TiSDKManager;
//import cn.tillusory.sdk.bean.TiSticker;
//
///**
// * Created by totwoo on 2018/8/22.
// */
//
//public class CameraPaperSelectDialog {
//    private CustomDialog dialog;
//    private Context context;
//    private int screenWidth;
//    private RecyclerView face_camera_paper_rv;
//    private List<TiSticker> stickerList = new ArrayList<>();
//
//    public CameraPaperSelectDialog(Context context){
//        this.context = context;
//        stickerList.add(TiSticker.NO_STICKER);
//        stickerList.addAll(TiSticker.getAllStickers(context));
//    }
//
//    public void showPaperDialog(DialogInterface.OnDismissListener onDismissListener, TiSDKManager tiSDKManager){
//        if(dialog == null){
//            dialog = new CustomDialog(context, R.style.custom_dialog_tran);
//            View v = View.inflate(context, R.layout.camera_paper_select_dialog, null);
//            screenWidth = CommonUtils.getScreenWidth();
//            dialog.setRootView(v);
//            face_camera_paper_rv = v.findViewById(R.id.face_camera_paper_rv);
//            int height = (int) (screenWidth/5*2.5);
//            LinearLayout.LayoutParams layoutParamsRv = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,height);
//            face_camera_paper_rv.setLayoutParams(layoutParamsRv);
//
//            face_camera_paper_rv.setLayoutManager(new GridLayoutManager(context,5));
//            PaperLibraryAdapter paperLibraryAdapter = new PaperLibraryAdapter(context,stickerList,tiSDKManager);
//            face_camera_paper_rv.setAdapter(paperLibraryAdapter);
//            dialog.setOnDismissListener(onDismissListener);
//        }
//        dialog.show();
//    }
//
//    public void dismissDialog(){
//        if(dialog != null && dialog.isShowing()){
//            dialog.dismiss();
//        }
//    }
//}
