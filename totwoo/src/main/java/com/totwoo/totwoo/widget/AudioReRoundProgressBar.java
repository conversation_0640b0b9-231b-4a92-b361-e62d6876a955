package com.totwoo.totwoo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;

import androidx.core.content.res.ResourcesCompat;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;

/**
 * Created by huanggaowei on 16/7/18.
 */
public class AudioReRoundProgressBar extends HorizontalProgressBarWithNumber {

    /**
     * mRadius of view
     */
    private int mRadius = dp2px(30);
    private int mMaxPaintWidth;
    private Typeface typeface;
    Typeface rTypeface;
    Typeface cTypeface;
    private Context mContext;

    public void setMaxMultiple(int maxMultiple) {
        mMaxMultiple = maxMultiple;
    }

    private  int mMaxMultiple;

    public void setProgressStatus(String progressStatus) {
        this.progressStatus = progressStatus;
    }

    private String progressStatus = "";

    public AudioReRoundProgressBar(Context context) {
        this(context, null);
    }

    public AudioReRoundProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        TypedArray ta = context.obtainStyledAttributes(attrs,
                R.styleable.RoundProgressBarWidthNumber);
        mRadius = (int) ta.getDimension(
                R.styleable.RoundProgressBarWidthNumber_radius, mRadius);
        ta.recycle();
        typeface = ResourcesCompat.getFont(context,
                R.font.agencyb);
        rTypeface = ResourcesCompat.getFont(context,
                R.font.agencyr);
        cTypeface = ResourcesCompat.getFont(context,
                R.font.gothic);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
    }

    /**
     * 这里默认在布局中padding值要么不设置，要么全部设置
     */
    @Override
    protected synchronized void onMeasure(int widthMeasureSpec,
                                          int heightMeasureSpec) {

        mMaxPaintWidth = Math.max(mReachedProgressBarHeight,
                mUnReachedProgressBarHeight);
        int expect = mRadius * 2 + mMaxPaintWidth + getPaddingLeft()
                + getPaddingRight();
        int width = resolveSize(expect, widthMeasureSpec);
        int height = resolveSize(expect, heightMeasureSpec);
        int realWidth = Math.min(width, height);

        mRadius = (realWidth - getPaddingLeft() - getPaddingRight() - mMaxPaintWidth) / 2;

        setMeasuredDimension(realWidth, realWidth);

    }



    @Override
    protected synchronized void onDraw(Canvas canvas) {

        String text = getProgress()/mMaxMultiple + "";
//		LogUtils.i("progress",getProgress()+"");
        mPaint.setTextSize(Apputils.dp2px(mContext, 17));
        float bfbWidth = mPaint.measureText("sec");
        float bfbHeight = Math.abs(mPaint.descent() + mPaint.ascent());

        canvas.save();
        canvas.translate(getPaddingLeft() + mMaxPaintWidth / 2, getPaddingTop()
                + mMaxPaintWidth / 2);
        mPaint.setStyle(Paint.Style.STROKE);
        // draw unreaded bar
        mPaint.setColor(mUnReachedBarColor);
        mPaint.setStrokeWidth(mUnReachedProgressBarHeight);
        canvas.drawCircle(mRadius, mRadius, mRadius, mPaint);
//        // draw reached bar
//        mPaint.setColor();
//        mPaint.setStrokeWidth(mReachedProgressBarHeight);
//        // for (int i = 0; i < 10; i++) {
//        //
//        canvas.drawArc(new RectF(0, 0, mRadius * 2, mRadius
//                * 2), 0, 360, false, mPaint);

        mPaint.setColor(mReachedBarColor);
        mPaint.setStrokeWidth(mReachedProgressBarHeight);
        float sweepAngle = getProgress() * 1.0f / (float) getMax() * (float) 360;
        // for (int i = 0; i < 10; i++) {
        //
        float offdis = 0;
        canvas.drawArc(new RectF(offdis, offdis, mRadius * 2 - offdis, mRadius
                * 2 - offdis), 270, sweepAngle, false, mPaint);

        // }
        // draw text

        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(mTextColor);
        mPaint.setTypeface(typeface);
        mPaint.setTextSize(Apputils.dp2px(mContext, 43));
        float textWidth = mPaint.measureText(text);

        canvas.drawText(text, mRadius + 10 - (textWidth + bfbWidth) / 2, mRadius + bfbHeight
                , mPaint);
        mPaint.setTextSize(Apputils.dp2px(mContext, 17));
        mPaint.setTypeface(rTypeface);
        float bfbDrawX = mRadius
                + (textWidth - (mRadius - (mRadius - textWidth / 2 - bfbWidth / 2)));
        canvas.drawText("sec.", bfbDrawX + 10, mRadius + bfbHeight, mPaint);

        mPaint.setTypeface(cTypeface);
        mPaint.setTextSize(Apputils.dp2px(mContext, 13));
        mPaint.setColor(getResources().getColor(R.color.line_color_black_solid1));
        float statusTextWidth = mPaint.measureText(progressStatus);

        canvas.drawText(progressStatus, mRadius - statusTextWidth / 2, mRadius +bfbHeight+Apputils.dp2px(mContext,24), mPaint);
        // canvas.drawText(text, mRadius - textWidth / 2,
        // mRadius - textHeight / 2, mPaint);
//		LogUtils.i("progress",bfbDrawX+":"+(mRadius - textHeight / 2+":"+(mRadius - textWidth / 2 - bfbWidth / 2)+":"+(mRadius
//				- textHeight / 2)));
        canvas.restore();
    }

}
