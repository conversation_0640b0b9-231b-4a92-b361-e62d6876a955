//package com.totwoo.totwoo.widget;
//
//import java.util.Calendar;
//
//import android.annotation.SuppressLint;
//import android.content.Context;
//import android.graphics.Canvas;
//import android.graphics.Color;
//import android.graphics.Paint;
//import android.graphics.Paint.FontMetrics;
//import android.graphics.Paint.Style;
//import android.graphics.Path;
//import android.support.v4.util.LongSparseArray;
//import android.util.AttributeSet;
//import android.view.View;
//
//import com.totwoo.totwoo.R;
//
///**
// * 紫外线折线图数据表格
// *
// * <AUTHOR>
// * @date 2015-2015年7月20日
// */
//public class UltravioletRayDataTable extends View {
//	/** 相邻数据点间隔时间 */
//	private final long DEFAULT_TIME_INTERVAL = 5 * 60 * 1000;
//
//	private Context mContext;
//	/** 画笔 */
//	private Paint paint;
//
//	/** 表格底部线位置 */
//	private float table_bottom;
//
//	/** 一周的步数数据 */
//	private LongSparseArray<Integer> uvData;
//
//	/** 坐标系最左侧左边 */
//	private float lineLeft;
//
//	/** 坐标系宽度 */
//	private float lineWidth;
//
//	public UltravioletRayDataTable(Context context) {
//		super(context);
//		initTable(context);
//	}
//
//	public UltravioletRayDataTable(Context context, AttributeSet attrs) {
//		super(context, attrs);
//		initTable(context);
//	}
//
//	public UltravioletRayDataTable(Context context, AttributeSet attrs,
//			int defStyleAttr) {
//		super(context, attrs, defStyleAttr);
//		initTable(context);
//	}
//
//	@SuppressLint("NewApi")
//	public UltravioletRayDataTable(Context context, AttributeSet attrs,
//			int defStyleAttr, int defStyleRes) {
//		super(context, attrs, defStyleAttr, defStyleRes);
//		initTable(context);
//	}
//
//	/**
//	 * 初始化视图
//	 *
//	 * @param context
//	 */
//	private void initTable(Context context) {
//		mContext = context;
//		initTestData();
//
//		// 设置画笔
//		paint = new Paint();
//		paint.setAntiAlias(true);
//		paint.setDither(true);
//	}
//
//	@Override
//	protected void onDraw(Canvas canvas) {
//		super.onDraw(canvas);
//		paint.setStyle(Style.FILL);
//		paint.setStrokeWidth(4);
//
//		table_bottom = canvas.getHeight() - 80;
//		lineLeft = paint.measureText("11+") + 20;
//
//		// 绘制右上方标注
//		paint.setColor(mContext.getResources().getColor(
//				R.color.text_color_black_nomal));
//		paint.setTextSize(mContext.getResources().getDimension(
//				R.dimen.uv_nomal_text_size));
//		FontMetrics fm = paint.getFontMetrics();
//
//		String text = mContext.getResources().getString(
//				R.string.strong_uv_damage);
//		float x = canvas.getWidth() - paint.measureText(text);
//		float y = 0 - fm.ascent;
//		canvas.drawText(text, x, y, paint);
//		paint.setColor(mContext.getResources().getColor(
//				R.color.text_color_black_important));
//		canvas.drawRect(x - 60,
//				(float) Math.ceil(fm.descent - fm.ascent) / 2 - 5, x - 20,
//				(float) Math.ceil(fm.descent - fm.ascent) / 2 + 5, paint);
//
//		// 绘制标线，指数值
//		String[] indexs = { "11+", "8", "6", "3", "0" };
//		paint.setTextSize(mContext.getResources().getDimension(
//				R.dimen.uv_nomal_text_size));
//		float tar = table_bottom / 5;
//		for (int i = 0; i < indexs.length; i++) {
//			if (i < 2) {
//				paint.setColor(mContext.getResources().getColor(
//						R.color.text_color_black_important));
//				canvas.drawText(
//						indexs[i],
//						paint.measureText("11+") - paint.measureText(indexs[i]),
//						tar * (i + 1), paint);
//			} else {
//				paint.setColor(mContext.getResources().getColor(
//						R.color.text_color_black_note));
//				canvas.drawText(
//						indexs[i],
//						paint.measureText("11+") - paint.measureText(indexs[i]),
//						tar * (i + 1), paint);
//			}
//			paint.setColor(mContext.getResources().getColor(
//					R.color.text_color_black_note));
//			canvas.drawLine(lineLeft, tar * (i + 1), canvas.getWidth(), tar
//					* (i + 1), paint);
//		}
//		// 绘制底部时间标识
//		String[] times = { "0:00", "6:00", "12:00", "18:00", "23:59" };
//
//		paint.setTextSize(mContext.getResources().getDimension(
//				R.dimen.uv_nomal_text_size));
//		lineWidth = canvas.getWidth() - lineLeft;
//		for (int i = 0; i < times.length; i++) {
//			float center_x = lineWidth / 5 * (i + 0.5f) + lineLeft;
//
//			// 先用背景色绘制，清楚圆心部分线， 然后绘制空心小圆圈
//			paint.setColor(mContext.getResources().getColor(
//					R.color.layer_bg_white));
//			canvas.drawCircle(center_x, table_bottom, 8, paint);
//			paint.setStyle(Style.STROKE);
//			paint.setColor(mContext.getResources().getColor(
//					R.color.text_color_black_note));
//			canvas.drawCircle(center_x, table_bottom, 8, paint);
//			paint.setStyle(Style.FILL);
//
//			canvas.drawText(times[i], center_x - paint.measureText(times[i])
//					/ 2, canvas.getHeight() - paint.getFontMetrics().descent,
//					paint);
//		}
//
//		// 绘制数据表格
//
//		// 数据圆点x坐标
//		float org_x = lineWidth / 5 * 0.5f + lineLeft;
//		// 相邻数据点之间x 轴间距
//		float var_x = lineWidth / 5 / 72;
//		Path path = new Path();
//		path.moveTo(lineLeft, table_bottom);
//		path.lineTo(org_x, table_bottom);
//
//		Calendar cal = Calendar.getInstance();
//		cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH),
//				cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
//		long org_time = cal.getTimeInMillis();
//		for (int i = 0; i < uvData.size(); i++) {
//			x = (uvData.keyAt(i) - org_time) / DEFAULT_TIME_INTERVAL * var_x
//					+ org_x;
//			y = table_bottom - uvData.valueAt(i) / 14f * table_bottom;
//			path.lineTo(x, y);
//		}
//		paint.setColor(mContext.getResources().getColor(R.color.layer_bg_blue));
//		paint.setStrokeWidth(12);
//		paint.setStyle(Style.STROKE);
//		canvas.drawPath(path, paint);
//
//		paint.setColor(Color.BLUE);
//		paint.setStyle(Style.FILL);
//		paint.setStrokeWidth(4);
//		canvas.drawCircle(x, y, 16, paint);
//		canvas.drawLine(x, 60, x, table_bottom, paint);
//	}
//
//	private void initTestData() {
//		uvData = new LongSparseArray<Integer>();
//		Calendar cal = Calendar.getInstance();
//		cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH),
//				cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
//
//		int[] values = { 0, 1, 1, 2, 2, 3, 5, 7, 9, 8, 10, 9, 6, 4, 3, 2 };
//
//		for (long i = cal.getTimeInMillis(); i < System.currentTimeMillis() + 3600000 * 8; i += DEFAULT_TIME_INTERVAL * 12) {
//
//			int t0 = (int) (i - cal.getTimeInMillis()) / 3600000;
//			if (t0 < 7 || t0 > 20) {
//				uvData.put(i, 0);
//			} else {
//				uvData.put(i, values[t0 - 6]);
//			}
//		}
//	}
//}
