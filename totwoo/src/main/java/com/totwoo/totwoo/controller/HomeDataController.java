//package com.totwoo.totwoo.controller;
//
//import android.content.Context;
//
//import com.ease.data.DataController;
//import com.ease.model.BaseModel;
//import com.google.gson.Gson;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.totwoo.bean.holderBean.BrightSwitch;
//import com.totwoo.totwoo.bean.holderBean.HomeConstellationBean;
//import com.totwoo.totwoo.bean.holderBean.HomeSedentaryBean;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//
//import org.json.JSONObject;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import rx.Observable;
//import rx.Subscriber;
//
///**
// * Created by h<PERSON><PERSON>owei on 16/4/6.
// */
//public class HomeDataController extends DataController<BaseModel> {
//
//    private Context mContext;
//
//    public HomeDataController(Context context) {
//        this.mContext = context;
//    }
//
//    @Override
//    public Observable<List<BaseModel>> doInitialize() {
//        return Observable.create(new Observable.OnSubscribe<List<BaseModel>>() {
//            @Override
//            public void call(Subscriber<? super List<BaseModel>> subscriber) {
//                List<BaseModel> list = new ArrayList<>();
//
//                //星座数据
//                HomeConstellationBean dataModel = null;
//                Gson gson = new Gson();
//                JSONObject dataCache = HttpHelper.getDataCache(HttpHelper.URL_HOMEPAGE);
//                if (dataCache != null) {
//                    dataModel = gson.fromJson(dataCache.toString(), HomeConstellationBean.class);
//                }
//
//                list.add(dataModel); // 心有灵犀
//                list.add(new BrightSwitch(
//                        PreferencesUtils.getBoolean(mContext, "isBrightModeOn", false))); // 闪光
//
//                list.add(dataModel); // 星座
//
//                if (Apputils.systemLanguageIsChinese(mContext)){
//                    list.add(new BaseModel()); // 求签
//                }
//
//                list.add(new BaseModel()); // yes no
//
//                subscriber.onNext(list);
//                subscriber.onCompleted();
//            }
//        });
//    }
//
//    @Override
//    public Observable<List<BaseModel>> doRefresh() {
//        return Observable.create(new Observable.OnSubscribe<List<BaseModel>>() {
//            @Override
//            public void call(Subscriber<? super List<BaseModel>> subscriber) {
//                List<BaseModel> list = new ArrayList<>();
//
//                //星座数据
//                HomeConstellationBean dataModel = null;
//                Gson gson = new Gson();
//                JSONObject dataCache = HttpHelper.getDataCache(HttpHelper.URL_HOMEPAGE);
//                if (dataCache != null) {
//                    dataModel = gson.fromJson(dataCache.toString(), HomeConstellationBean.class);
//                }
//
//
//                list.add(dataModel); // 心有灵犀
//                list.add(new BrightSwitch(
//                        PreferencesUtils.getBoolean(mContext, "isBrightModeOn", false))); // 闪光
//
//                list.add(dataModel); // 星座
//
//                if (Apputils.systemLanguageIsChinese(mContext)) {
//                    list.add(new BaseModel()); // 求签
//                }
//
//                list.add(new BaseModel()); // yes no
//
//                subscriber.onNext(list);
//                subscriber.onCompleted();
//            }
//        });
//    }
//
//    @Override
//    public Observable<List<BaseModel>> doLoadMore() {
//        return null;
//    }
//}
