package com.totwoo.totwoo.controller;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import com.ease.data.DataController;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.totwoo.activity.AppNotificationsActivity;
import com.totwoo.totwoo.bean.holderBean.AppNotifyBean;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.ArrayList;
import java.util.List;

import rx.Observable;
import rx.Subscriber;
import rx.functions.Func1;

/**
 * Created by huanggaowei on 16/8/29.
 */
public class AppNotifyDataController extends DataController<AppNotifyBean> {

    public Context mContext;

    public List<String> needNotifyAppPackges;

    List<PackageInfo> installedPackages;

    List<AppNotifyBean> AppNotifyBeans = new ArrayList<>();

    PackageManager packageManager;

    public AppNotifyDataController(Context context) {
        mContext = context;
        packageManager = mContext.getPackageManager();
        installedPackages = packageManager.getInstalledPackages(0);
    }

    @Override
    public Observable<List<AppNotifyBean>> doInitialize() {
        needNotifyAppPackges = new Gson().fromJson(PreferencesUtils.getString(mContext, AppNotificationsActivity.APP_NOTIFY_REMIND_PACKAGES, "")
                , new TypeToken<List<String>>() {
                }.getType());
        return Observable.create(new Observable.OnSubscribe<List<AppNotifyBean>>() {
            @Override
            public void call(Subscriber<? super List<AppNotifyBean>> subscriber) {
                Observable.from(installedPackages).filter(new Func1<PackageInfo, Boolean>() {
                    @Override
                    public Boolean call(PackageInfo packageInfo) {
                        return filterApp(packageInfo,mContext.getPackageName());
                    }
                }).subscribe(new Subscriber<PackageInfo>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                    }

                    @Override
                    public void onNext(PackageInfo packageInfo) {
                        //把需要提醒的单独拿出来 放在前面
                        if (needNotifyAppPackges != null && isNeedNotify(packageInfo.packageName)) {
                            AppNotifyBeans.add(0, new AppNotifyBean(packageInfo.packageName,
                                    packageInfo.applicationInfo.loadLabel(packageManager).toString(),
                                    packageInfo.applicationInfo.loadIcon(packageManager), true));
                        } else {
                            AppNotifyBeans.add(new AppNotifyBean(packageInfo.packageName,
                                    packageInfo.applicationInfo.loadLabel(packageManager).toString(),
                                    packageInfo.applicationInfo.loadIcon(packageManager), false));
                        }
                    }
//                        @Override
//                        public void call(PackageInfo packageInfo) {
//
//                        }
                });
                subscriber.onNext(AppNotifyBeans);
                subscriber.onCompleted();
            }
        });
    }
    @Override
    public Observable<List<AppNotifyBean>> doRefresh() {
        return null;
    }

    @Override
    public Observable<List<AppNotifyBean>> doLoadMore() {
        return null;
    }

    private boolean isNeedNotify(String packageName) {
        for (int i = 0; i < needNotifyAppPackges.size(); i++) {
            if (needNotifyAppPackges.get(i).equals(packageName)) {
                return true;
            }
        }
        return false;
    }

    //判断是否是第三方应用和需要加入的系统应用
    private boolean filterApp(PackageInfo packageInfo,final String appPackageName){
        if (packageInfo.packageName.equals(appPackageName)){
            return false;
        }
        switch (packageInfo.packageName){
            case "com.android.calendar":
            case "com.android.mms":
            case "com.android.email":
            case "com.tencent.mobileqq":
            case "com.tencent.mm":
            return true;
        }
        return (packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0;
    }
}
