package com.totwoo.totwoo.controller;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.core.app.NotificationManagerCompat;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.CustomNotifyEditActivity;
import com.totwoo.totwoo.activity.LoveNotifyEditActivity;
import com.totwoo.totwoo.activity.WaterTimeSettingActivity;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.NotifyDataModel;
import com.totwoo.totwoo.bean.holderBean.HomeWaterTimeBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;

public class LocalNotification {

    private static volatile LocalNotification instance;

    private static final int WATER_TIME_NOTIFY_ID = 20180302;
    private static final int CUSTOM_NOTIFY_ID = 20180327;
    private static final int LOVE_NOTIFY_ID = 20181208;

    private LocalNotification() {

    }

    public static LocalNotification getInstance() {
        if (instance == null) {
            synchronized (LocalNotification.class) {
                instance = new LocalNotification();
            }
        }
        return instance;
    }

    public void waterTimeNotification(HomeWaterTimeBean bean,Context context){
        if (bean.isNotify()) {
            boolean notifySwitch = PreferencesUtils.getBoolean(context,
                    NotifyUtil.WATER_TIME_SWITCH_KEY, true);
            String jewName = PreferencesUtils.getString(context, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
            if (notifySwitch && CommonUtils.isLogin() && BleParams.isReminderJewlery()) {
                // 对于部分手机时间不更新的情况, 此处采用先取消原有通知, 在新建的方法
                NotificationManagerCompat.from(context).cancel(WATER_TIME_NOTIFY_ID);

                NotifyDataModel model = new NotifyDataModel(context);
                model.setUser_NickName(ToTwooApplication.owner.getNickName());
                model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_WATER_TIME, true);
                model.setNotify_Id(WATER_TIME_NOTIFY_ID);
                model.setTargetIntent(new Intent(context, WaterTimeSettingActivity.class));
                model.setNotify_title(context
                        .getString(R.string.water_time_reminder));
                NotifyDataModel.ShowNotify(context, model);
            }
        }
    }

    public void customNotification(CustomItemBean bean, Context context){
        if (bean.getIs_open() == 1) {
            if(bean.getDefine_type() == 5){
                setLoveNotifyModel(bean,context);
                return;
            }
            String jewName = PreferencesUtils.getString(context, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
            if (CommonUtils.isLogin() && BleParams.isReminderJewlery()) {
                // 对于部分手机时间不更新的情况, 此处采用先取消原有通知, 在新建的方法
                NotificationManagerCompat.from(context).cancel(CUSTOM_NOTIFY_ID);
                JewelryNotifyModel nowSetModel = new JewelryNotifyModel();
                nowSetModel.setFlashColor(bean.getNotify_mode());
                if(TextUtils.equals(bean.getShock_type(),"short"))
                    nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                else
                    nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);

                Intent intent = new Intent(context,CustomNotifyEditActivity.class);
                intent.putExtra("define_id",bean.getDefine_id());
                BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                NotifyDataModel model = new NotifyDataModel(context);
                model.setUser_NickName(ToTwooApplication.owner.getNickName());
                model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_CUSTOM, true);
                model.setNotify_Id(CUSTOM_NOTIFY_ID);
                model.setTargetIntent(intent);
//                model.setNotify_title(context.getString(R.string.custom_notify_reminder));
                String content = bean.getTitle();
                switch (bean.getDefine_type()) {
                    case CustomItemBean.SCHEDULE:
                        content = context.getString(R.string.custom_notify_type_event_push) + "：" + content;
                        break;
                    case CustomItemBean.BIRTHDAY:
                        content = context.getString(R.string.custom_notify_type_birthday_push) + "：" + content;
                        break;
                    case CustomItemBean.ANNIVERSARY:
                        content = context.getString(R.string.custom_notify_type_anniversary_push) + "：" + content;
                        break;
                    case CustomItemBean.OTHER:
                        content = context.getString(R.string.custom_notify_type_others_push) + "：" + content;
                        break;
                    default:
                        content = "" + content;
                        break;

                }
                model.setNotify_content(content);
                NotifyDataModel.ShowNotify(context, model);
            }
        }
    }

    private void setLoveNotifyModel(CustomItemBean bean,Context context){
        NotificationManagerCompat.from(context).cancel(LOVE_NOTIFY_ID);
        JewelryNotifyModel nowSetModel = new JewelryNotifyModel();
        nowSetModel.setFlashColor(bean.getNotify_mode());
        if(TextUtils.equals(bean.getShock_type(),"short"))
            nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
        else
            nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);

        BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());

        Intent intent = new Intent(context, LoveNotifyEditActivity.class);
        intent.putExtra("define_id",bean.getDefine_id());
        NotifyDataModel model = new NotifyDataModel(context);
        model.setUser_NickName(ToTwooApplication.owner.getNickName());
        model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_LOVE, true);
        model.setNotify_Id(LOVE_NOTIFY_ID);
        model.setTargetIntent(intent);
//      model.setNotify_title(context.getString(R.string.custom_notify_reminder));
        model.setNotify_content(bean.getTitle());
        NotifyDataModel.ShowNotify(context, model);
    }
}
