package com.totwoo.totwoo.controller;

import com.etone.framework.component.http.HttpAuthCookie;
import com.etone.framework.component.http.HttpParams;
import com.etone.framework.utils.JSONUtils;
import com.etone.framework.utils.StringUtils;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.net.URLEncoder;
import java.util.TimeZone;

/**
 * Created by xinyoulingxi on 2017/5/16.
 */

public class HttpValues extends HttpParams
{
    public String errorCode;
    public String errorMesg;
    public HttpValues(String httpEvent, String url)
    {
        super(httpEvent, url);
        setDefaultParams();
    }

    @Override
    public void addParams(String key, String value)
    {
        try
        {
            String newValue = URLEncoder.encode(value, this.charset);
            super.addParams(key, newValue);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void setDefaultParams()
    {
        this.cookieToSend = new HttpAuthCookie();
        this.cookieToSend.token = ToTwooApplication.owner.getToken();
        this.addParams("phoneType", "2001");    //1001是ios，2001是android
        this.addParams("version", Apputils.getVersionName(ToTwooApplication.baseContext));
        this.addParams("cn", (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? 1 : 0) + "");//1中文版本，0代表英文版本
        this.addParams("timeZone", "" + TimeZone.getDefault().getRawOffset() / 3600000);
        String totwooId = ToTwooApplication.owner.getTotwooId();

        if (!StringUtils.isEmpty(totwooId)) {
            this.addParams("totwoo_id", totwooId);
        }

        String firmwareVersion = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "");
        if (!StringUtils.isEmpty(firmwareVersion)) {
            this.addParams("firmwareVersion", firmwareVersion);
        }
    }

    public boolean isRequestOk()
    {
        errorCode = JSONUtils.getString(content, "errorCode", "-1");
        errorMesg = JSONUtils.getString(content, "errorMsg", "");

        return errorCode.equals("0");
    }
}
