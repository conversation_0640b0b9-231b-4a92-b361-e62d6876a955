package com.totwoo.totwoo.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;

/**
 * 用于接受 手表端推送通知, 回复 totwoo 的 Action 广播
 * Created by lixingmao on 16/6/28.
 */
public class TotwooSendReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (TextUtils.equals(BleParams.ACTION_SEND_TOTWOO, intent.getAction())) {
            // 启动服务, 发送 totwoo 消息
            BluetoothManage.getInstance().sendTotwoo(false);
        }
    }
}
