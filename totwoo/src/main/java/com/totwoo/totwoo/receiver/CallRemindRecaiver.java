package com.totwoo.totwoo.receiver;

import static com.totwoo.totwoo.activity.CallRemindSetActivity.ALL_CALL_REMIND_MUSIC_TYPE_KEY;
import static com.totwoo.totwoo.activity.CallRemindSetActivity.ALL_CONTACT_REMIND_MUSIC_TYPE_KEY;

import android.Manifest;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.provider.ContactsContract;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.activity.CallRemindSetActivity;
import com.totwoo.totwoo.bean.CallRemindContact;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.MusicItemBeans;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.List;

import rx.Observable;
import rx.functions.Action1;
import rx.functions.Func1;
import rx.schedulers.Schedulers;

public class CallRemindRecaiver extends BroadcastReceiver {
    private String TAG = "CallRemindRecaiver";

    private static String notifyColorString;
    private static int music_index;

    private Context mContext;

    private static boolean isNeedNotify = true;

    private static boolean isQueryContact = true;

    private int reCloseFlashCount;
    private PhoneStateListener listener;

    @Override
    public void onReceive(Context context, Intent intent) {
        mContext = context;
        LogUtils.e(TAG + " onReceive");
        if (PermissionUtil.isGranted(Manifest.permission.READ_PHONE_STATE)) {
            if (TextUtils.equals(intent.getAction(), Intent.ACTION_NEW_OUTGOING_CALL)) {

            } else {
                TelephonyManager tm = (TelephonyManager) context.getSystemService(Service.TELEPHONY_SERVICE);
                if (listener == null) {
                    listener = new PhoneStateListener() {

                        @Override
                        public void onCallStateChanged(int state, String incomingNumber) {
                            //注意，方法必须写在super方法后面，否则incomingNumber无法获取到值。
                            super.onCallStateChanged(state, incomingNumber);
                            callStateChange(state, incomingNumber);
//                        switch (state) {
//                            case TelephonyManager.CALL_STATE_RINGING:
//                                break;
//                            case TelephonyManager.CALL_STATE_OFFHOOK:
//                                break;
//                            case TelephonyManager.CALL_STATE_IDLE:
//                                break;
//                        }
                        }
                    };
//                tm.listen(listener, PhoneStateListener.LISTEN_CALL_STATE);
                }
                switch (tm.getCallState()) {
                    // 响铃中
                    case TelephonyManager.CALL_STATE_RINGING:
                        callStateChange(TelephonyManager.CALL_STATE_RINGING, intent.getStringExtra("incoming_number"));
                        LogUtils.e(TAG + " incomingNumber = " + intent.getStringExtra("incoming_number"));
                        break;
                    // 已接通
                    case TelephonyManager.CALL_STATE_OFFHOOK:
                        callStateChange(TelephonyManager.CALL_STATE_OFFHOOK, "");
                        LogUtils.e(TAG + " call offhook ");
                        break;
                    // 挂断
                    case TelephonyManager.CALL_STATE_IDLE:
                        callStateChange(TelephonyManager.CALL_STATE_IDLE, "");
                        LogUtils.e(TAG + " call Idle ");
                        break;
                }
            }
        } else {
            Log.e("CallRemindRecaiver","READ_PHONE_STATE未授权");
        }
    }


    private void callStateChange(int state, final String incomingNumber) {
        JewelryNotifyModel mdd = NotifyUtil.getCallNotifyModel(mContext);

        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED || !CommonUtils.isLogin() || !mdd.isNotifySwitch()) {
            return;
        }

        LogUtils.e(TAG + " state = " + state);
        switch (state) {
            //电话等待接听
            case TelephonyManager.CALL_STATE_RINGING:
                LogUtils.e(TAG + " incomingNumber = " + incomingNumber);
                if (BleParams.isSecurityJewlery()) {
                    return;
                }
                //所有来电
                if (PreferencesUtils.getBoolean(mContext, CallRemindSetActivity.ALL_CALL_REMIND_SWITCH_KEY, true)) {
                    notifyColorString = PreferencesUtils.getString(mContext, CallRemindSetActivity.ALL_CALL_REMIND_FLASH_KEY, "RED");
                    music_index = PreferencesUtils.getInt(mContext, ALL_CALL_REMIND_MUSIC_TYPE_KEY, 0);
                    startNotifyVibration();
                }

                if (TextUtils.isEmpty(incomingNumber)) {
                    return;
                }

                //通讯录来电
                if (PreferencesUtils.getBoolean(mContext, CallRemindSetActivity.ALL_CONTACT_REMIND_SWITCH_KEY, false)) {
                    LogUtils.e(TAG + " incomingNumber = " + incomingNumber);
                    notifyColorString = PreferencesUtils.getString(mContext, CallRemindSetActivity.ALL_CONTACT_REMIND_FLASH_KEY, "RED");
                    music_index = PreferencesUtils.getInt(mContext, ALL_CONTACT_REMIND_MUSIC_TYPE_KEY, 0);
                    //判断通讯录是否存在此来电
                    isPhoneNumberInContacts(incomingNumber, new ContacQueryListener() {
                        @Override
                        public void searchSuccess() {
                            LogUtils.e(TAG + " incomingNumber = " + incomingNumber);
                            startNotifyVibration();
                        }

                        @Override
                        public void searchFailure() {
                        }
                    });
                }
                //重要来电
                if (PreferencesUtils.getBoolean(mContext, CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_SWITCH_KEY, false)) {
                    String dataJson = PreferencesUtils.getString(mContext, CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_DATA_KEY, "");
                    final List<CallRemindContact> mCallRemindContacts = new Gson().fromJson(dataJson, new TypeToken<List<CallRemindContact>>() {
                    }.getType());
                    if (mCallRemindContacts != null) {
                        Observable.from(mCallRemindContacts)
                                .filter(new Func1<CallRemindContact, Boolean>() {
                                    @Override
                                    public Boolean call(CallRemindContact CallRemindContact) {
//                                            String country_code =  PreferencesUtils.getString(mContext, LoginActivity.COUNTRY_CODE_KEY, "86");
//                                            String phoneNumber =CallRemindContact.getPhoneNumber().substring(country_code.length());
                                        return CallRemindContact.getPhoneNumber().contains(incomingNumber);
                                    }
                                }).subscribe(new Action1<CallRemindContact>() {
                            @Override
                            public void call(CallRemindContact CallRemindContact) {
                                notifyColorString = CallRemindContact.getFlashColor();
                                music_index = CallRemindContact.getMusic_type();
                                startNotifyVibration();
                            }
                        });
                    }
                }
                break;
                //电话接听
            case TelephonyManager.CALL_STATE_OFFHOOK:
                //电话挂机
            case TelephonyManager.CALL_STATE_IDLE:
                if (!isNeedNotify) {
                    isNeedNotify = true;
                    if (music_index == 0 || BleParams.isNoneMusicJewelry()) {
                        BluetoothManage.getInstance().toggleIncommingCall(-1, NotifyUtil.getColorValue(notifyColorString));
                    } else {
                        BluetoothManage.getInstance().changeBirghtMode(-1, false);
                    }
                }
                break;
        }
    }


    public void startNotifyVibration() {
        //因为有些手机来电后会发生很多次回调 所以做个限制 3秒后才可以重新操作硬件 不然硬件一下接收很多指令会自动重启
        if (isNeedNotify) {
            reCloseFlashCount = 0;
            if (music_index == 0 || BleParams.isNoneMusicJewelry()) {
                BluetoothManage.getInstance().toggleIncommingCall(6, NotifyUtil.getColorValue(notifyColorString));
            } else {
                try {
                    BluetoothManage.getInstance().notifyBrightMusic(MusicItemBeans.getMusicBrightBeans().get(music_index - 1).getMusic_index(), NotifyUtil.getColorValue(notifyColorString));
                } catch (Exception e) {
                    BluetoothManage.getInstance().notifyBrightMusic(MusicItemBeans.getMusicBrightBeans().get(0).getMusic_index(), NotifyUtil.getColorValue(notifyColorString));
                }
            }
            isNeedNotify = false;
        }
    }


    private void isPhoneNumberInContacts(final String PhoneNumber, final ContacQueryListener listener) {
        if (!isQueryContact) {
            return;
        }
        isQueryContact = false;
        Observable.from(new String[]{PhoneNumber})
                .observeOn(Schedulers.newThread())
                .subscribe(new Action1<String>() {
                    @Override
                    public void call(String s) {
                        Cursor cursor = null;
                        try {
                            ContentResolver cr = mContext.getContentResolver();
                            String[] mContactsProjection = new String[]{
                                    ContactsContract.CommonDataKinds.Phone.NUMBER,
                            };

                            String phoneNum;

                            //查询contacts表中的所有数据
                            cursor = cr.query(ContactsContract.CommonDataKinds.Phone.CONTENT_URI, mContactsProjection, null, null, null);
                            if (cursor != null && cursor.getCount() > 0) {
                                while (cursor.moveToNext()) {
                                phoneNum = cursor.getString(0);

                                // 对手机号码进行预处理（去掉号码前的+86、首尾空格、“-”号等）
                                phoneNum = phoneNum.replaceAll("^(\\+86)", "");
                                phoneNum = phoneNum.replaceAll("^(86)", "");
                                phoneNum = phoneNum.replaceAll("-", "");
                                phoneNum = phoneNum.replaceAll(" ", "");
                                phoneNum = phoneNum.trim();
                                LogUtils.i("toggleIncommingCall", phoneNum + "");
                                if (TextUtils.equals(PhoneNumber,phoneNum)) {
                                    listener.searchSuccess();
                                    isQueryContact = true;
                                    return;
                                }
                                }
                            }
                            listener.searchFailure();
                            isQueryContact = true;
                        } catch (SecurityException e) {
                            // 处理权限异常
                            LogUtils.e("CallRemindRecaiver", "SecurityException when querying contacts: " + e.getMessage());
                            listener.searchFailure();
                            isQueryContact = true;
                        } catch (Exception e) {
                            // 处理其他异常
                            LogUtils.e("CallRemindRecaiver", "Exception when querying contacts: " + e.getMessage());
                            listener.searchFailure();
                            isQueryContact = true;
                        } finally {
                            if (cursor != null) {
                                cursor.close();
                            }
                        }
                    }
                }, new Action1<Throwable>() {
                    @Override
                    public void call(Throwable throwable) {
                        // RxJava错误处理回调
                        LogUtils.e("CallRemindRecaiver", "RxJava error in contact query: " + throwable.getMessage());
                        listener.searchFailure();
                        isQueryContact = true;
                    }
                });
    }

    public interface ContacQueryListener {
        void searchSuccess();

        void searchFailure();
    }

}
