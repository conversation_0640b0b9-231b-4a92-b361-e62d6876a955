package com.totwoo.totwoo.receiver;

import static com.blankj.utilcode.util.ServiceUtils.isServiceRunning;
import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.Utils;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.ConstellationActivity;
import com.totwoo.totwoo.activity.LoveNotifyListActivity;
import com.totwoo.totwoo.activity.LoveSpacePinkActivity;
import com.totwoo.totwoo.activity.MessageActivity;
import com.totwoo.totwoo.activity.PeriodSettingActivity;
import com.totwoo.totwoo.activity.QianActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.activity.WeiboAuthActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftDataActivity;
import com.totwoo.totwoo.activity.giftMessage.SendGiftGalleryActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.GiftMessageBean;
import com.totwoo.totwoo.bean.GiftMessageCard;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.MessageBean;
import com.totwoo.totwoo.bean.NotifyDataModel;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.controller.LocalNotification;
import com.totwoo.totwoo.data.AlarmCustomNotifyLogic;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.data.TotwooLogic;
import com.totwoo.totwoo.service.KeepAliveService;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DbHelper;
import com.totwoo.totwoo.utils.DesUtil;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Set;

import cn.jpush.android.api.CustomMessage;
import cn.jpush.android.service.JPushMessageReceiver;

/**
 * 接受极光推送通知的Receiver, 所有的推送, 消息逻辑在此类中进行。 目前项目统一采用 自定义通知的推送方式， 因此所有的消息处理机制全在
 * processCustomMessage() 方法中进行
 * <p>
 * Required since 5.2.0 -->
 * 新的 tag/alias 接口结果返回需要开发者配置一个自定义的Service -->
 * 5.2.0开始所有事件将通过该类回调 -->
 * 该服务需要继承 JPush 提供的 JPushMessageService 类, 并如下新增一个 Intent-Filter -->
 *
 * <AUTHOR>
 * @date 2015-2015年8月18日
 */
public class JpushReceiver extends JPushMessageReceiver {
    private final String TAG = JpushReceiver.class.getSimpleName();
    public static final String REGISTER_ID = "register_id";
    private Context mContext;

    @Override
    public void onMessage(Context context, CustomMessage customMessage) {
        super.onMessage(context, customMessage);
        if (ActivityUtils.getTopActivity() != null) {
            mContext = ActivityUtils.getTopActivity();
        } else {
            mContext = Utils.getApp();
        }

        ensureServiceRunning();

        Log.e("xLog", "push msg: " + customMessage.message);
        processCustomMessage(customMessage.message);
    }


    @Override
    public void onConnected(Context context, boolean b) {
        super.onConnected(context, b);
        Log.e(TAG, "push onConnected: " + b);
    }

    /**
     * 确保KeepAliveService运行
     * 复用现有的前台服务，简单可靠
     */
    private void ensureServiceRunning() {
        try {
            if (!isServiceRunning(KeepAliveService.class)) {
                Intent serviceIntent = new Intent(mContext, KeepAliveService.class);
                serviceIntent.setAction(BleParams.ACTION_KEEP_ALIVE);
                serviceIntent.putExtra("trigger", TAG);

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    mContext.startForegroundService(serviceIntent);
                } else {
                    mContext.startService(serviceIntent);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onRegister(Context context, String registerId) {
        super.onRegister(context, registerId);
        Log.e("xLog", " push Id = " + registerId);
        if (!TextUtils.isEmpty(registerId)) {
            PreferencesUtils.put(context, REGISTER_ID, registerId);
//            com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_JPUSH_RID, null);
        }
    }

    /**
     * 根据收到的消息内容, 组织 NotifyDataModel 数据
     *
     * @param src
     */
    public void processCustomMessage(String src) {
        if (!CommonUtils.isLogin()) {
            LogUtils.e("processCustomMessage.return");
            return;
        }
        try {
            JSONObject obj = new JSONObject(src);
            JSONObject extras = obj.optJSONObject("extras");
            if (extras != null) {
                NotifyDataModel model = new NotifyDataModel(ToTwooApplication.baseContext);
                model.setNotify_title("totwoo");
                model.setUser_Id(extras.optString("from"));
                model.setUser_Head(extras.optString("head"));
                if (obj.optJSONObject("alert") != null) {
                    String name = obj.optJSONObject("alert").optString(
                            "loc-args");
                    if (name.startsWith("[\"")) {
                        name = name.substring(2, name.length() - 2);
                    }
                    model.setUser_NickName(name);
                }

                int feelType = extras.optInt("feel_type");
                if (feelType == 0) {
                    feelType = 1000;
                }
                model.setFeel_type(feelType);
                model.setJumpUrl(extras.optString("jump_url"));
                model.setJump_type(extras.optString("jump_type"));
                int msg_type = extras.optInt("msg_type");
                model.setNotify_type(msg_type, true);
                //生日提醒和纪念日提醒推送显示的文案和灯光需要去数据库中取
                if (msg_type == NotifyDataModel.NOTIFY_TYPE_BIRTHDAY || msg_type == NotifyDataModel.NOTIFY_TYPE_ANNIVERSARY) {
                    CustomItemBean bean = AlarmCustomNotifyLogic.getInstance().getBean(extras.optInt("define_id"));
                    LocalNotification.getInstance().customNotification(bean, ToTwooApplication.baseContext);
                }
                if (msg_type == NotifyDataModel.NOTIFY_TYPE_SECURITY) {
                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_SECURITY_STATE_CHANGED, null);
                }

                if (msg_type == NotifyDataModel.NOTIFY_TYPE_UPDATED_EMOTICONS) {
                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_EMOTION, null);
                }

                if (msg_type == NotifyDataModel.NOTIFY_TYPE_LOVE) {
                    CustomItemBean bean = new CustomItemBean();
                    bean.setIs_open(1);
                    bean.setTitle(obj.optString("alert"));
                    bean.setDefine_id(extras.optInt("define_id"));
                    bean.setShock_type(extras.optString("shock_type"));
                    bean.setNotify_mode(extras.optString("notify_mode"));
                    bean.setDefine_type(5);
                    bean.setMsg_type(msg_type);
                    LocalNotification.getInstance().customNotification(bean, ToTwooApplication.baseContext);
                }

                LogUtils.e("json:" + model.getFeel_type() + ", url:" + model.getJumpUrl());

                dealSpecificTypeData(obj, extras, model);

                if (msg_type == NotifyDataModel.NOTIFY_TYPE_TOTWOO && !isOldPush(obj)) {//互动消息 0宽走服务器拼好的
                    if (!TextUtils.isEmpty(obj.getString("alert"))) {
                        model.setNotify_content(obj.optString("alert"));
                    }
                } else {
                    if (TextUtils.isEmpty(model.getNotify_content()) && !TextUtils.isEmpty(obj.getString("alert"))) {
                        model.setNotify_content(obj.optString("alert"));
                    }
                }

                if (TextUtils.isEmpty(model.getNotify_title()) && !TextUtils.isEmpty(obj.getString("title"))) {
                    model.setNotify_title(obj.optString("title"));
                }

                model.setNotify_time((long) extras.optInt("stamp") * 1000L);

                // 如果为特殊设置id, 则设置默认id
                if (model.getNotify_Id() == 0) {
                    model.setNotify_Id(extras.optInt("stamp"));
                }

                // 更新本地用户状态, 并封装对应跳转的Intent
                updateLocalUserState(ToTwooApplication.baseContext, model, extras, obj);

                // 根据设置开关, 展示对应的消息
                if (needShow(model, extras)) {
                    NotifyDataModel.ShowNotify(ToTwooApplication.baseContext, model);
                }
            }
        } catch (JSONException | DbException e) {
            LogUtils.e("processCustomMessage error:" + e.getMessage(), e);
        }
    }

    /**
     * 处理不同消息类型的数据
     *
     * @param obj
     * @param extras
     * @param model
     */
    private void dealSpecificTypeData(JSONObject obj, JSONObject extras, NotifyDataModel model) {
        // {"sound":"totwoo_bingbong.aiff","extras":{"stamp":1487666532,"head":"headimg/2017029/14866381450000025850209800475576.png",
//                "from":"8615330058983","msg_type":10,"totwoo_num":0,"count_send_num":0,"count_receive_num":2},"badge":65536,"alert":{"loc-args":["啊哈喽"],"loc-key":"TOTWOO_FORMAT"}}

        switch (model.getNotify_type()) {
            // 如果当前推送消息为星座运势, 则User_NickName赋值为星座名字
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION:
                model.setUser_NickName(extras.optString("name"));
                break;

            case NotifyDataModel.NOTIFY_TYPE_ADD_LOVE_NOTIFY:
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE_NOT_TURN:
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_MONTH:
            case NotifyDataModel.NOTIFY_TYPE_UPDATED_EMOTICONS:
            case NotifyDataModel.NOTIFY_TYPE_SHOW:
                model.setNotify_content(obj.optString("alert"));
                break;
            // 对于系统消息通知, 需要做特殊的处理
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE:
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE_EMERGENCY:
                model.setNotify_title(obj.optString("title"));
                model.setNotify_content(obj.optString("alert"));
                break;

            case NotifyDataModel.NOTIFY_TYPE_WEIBO_TOKEN:
            case NotifyDataModel.NOTIFY_TYPE_SLEEP:
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_WEEK:
                model.setNotify_title(obj.optString("title"));
                model.setNotify_content(obj.optString("alert"));

            case NotifyDataModel.NOTIFY_TYPE_GIFTS:
//            {"sound":"totwoo_bingbong.aiff","title":"你收到了{发给你的一封情书，赶紧去看看吧.","alert":"","extras":{"stamp":"1652783726","msg_type":"3000","id":"{","audioUrl":"","audioUPreviewImageUrl":"","imageUrl":"greetingcard/20220517/16527787440000043170413122633621.jpg","vedioUrl":"","vedioPreviewImageUrl":"","text":"测试 09","senderName":"{"},"badge":65536}', contentType='', title='', senderId='3059168ac662268dbbe3ff73', appId='com.totwoo.totwoo', platform='0'}
//                model.setNotify_title(mContext.getString(R.string.notify_remind_gift_title));
                model.setNotify_content(mContext.getString(R.string.receive_gift_content, extras.optString("senderName")));
            default:
        }
    }

    private String printMessage(Bundle bundle) {
        Set<String> keyset = bundle.keySet();
        if (keyset != null) {
            StringBuilder sb = new StringBuilder("{");
            for (String key : keyset) {
                sb.append(key).append(": ").append(bundle.get(key)).append(", ");
            }
            sb.append("}");
            return sb.toString();
        }

        return null;
    }

    /**
     * 根据当前设置, 判断当前消息是否应该显示
     *
     * @param model
     * @param extras
     * @return
     */
    private static boolean needShow(NotifyDataModel model, JSONObject extras) {
        if (checkExpired(extras)) return false;

        switch (model.getNotify_type()) {
            case NotifyDataModel.NOTIFY_TYPE_REQUEST:
            case NotifyDataModel.NOTIFY_TYPE_REPLY:
            case NotifyDataModel.NOTIFY_TYPE_APART:
            case NotifyDataModel.NOTIFY_TYPE_TOTWOO:
                return NotifyUtil.getTotwooNotifyModel(ToTwooApplication.baseContext).isNotifySwitch();
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_COMMEND:
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION:
                return NotifyUtil.getFortuneNotifyModel(ToTwooApplication.baseContext).isNotifySwitch();
            case NotifyDataModel.NOTIFY_TYPE_GIFTS:
            case NotifyDataModel.NOTIFY_TYPE_GIFT_MESSAGE_OPENED:
                return NotifyUtil.getGiftNotifyModel(ToTwooApplication.baseContext).isNotifySwitch();
            case NotifyDataModel.NOTIFY_TYPE_PERIOD:
                return NotifyUtil.getPeriodNotifyModel(ToTwooApplication.baseContext).isNotifySwitch();
            case NotifyDataModel.NOTIFY_TYPE_WATER_TIME:
                return NotifyUtil.getWishNotifyModel(ToTwooApplication.baseContext).isNotifySwitch();
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE:
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE_EMERGENCY:
            case NotifyDataModel.NOTIFY_TYPE_CUSTOM:
            case NotifyDataModel.NOTIFY_TYPE_ADD_LOVE_NOTIFY:
            case NotifyDataModel.NOTIFY_TYPE_WISH:
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE_NOT_TURN:
            case NotifyDataModel.NOTIFY_TYPE_WEIBO_TOKEN:
            case NotifyDataModel.NOTIFY_TYPE_SLEEP:
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_WEEK:
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_MONTH:
            case NotifyDataModel.NOTIFY_TYPE_QIAN:
            case NotifyDataModel.NOTIFY_TYPE_LOVE_FRAGMENT:
            case NotifyDataModel.NOTIFY_TYPE_LUCKY:
            case NotifyDataModel.NOTIFY_TYPE_BIRTHDAY_NOTIFY:
                return true;
            case NotifyDataModel.NOTIFY_TYPE_UPDATED_EMOTICONS:
            case NotifyDataModel.NOTIFY_TYPE_SHOW:
                if (TextUtils.isEmpty(model.getNotify_content())) {
                    return false;
                }
                return true;
            case NotifyDataModel.NOTIFY_TYPE_LOVE:
                return false;
            default:
                break;
        }

        return false;
    }

    /**
     * 校验消息是否 > 30 分
     *
     * @param extras
     * @return
     */
    private static boolean checkExpired(JSONObject extras) {
        //增加时间校验,30分后不震动
        long stamp = extras.optInt("stamp") * 1000L;
        if (Math.abs(System.currentTimeMillis() - stamp) > 1000 * 60 * 30) {
            return true;
        }
        return false;
    }

    /**
     * 更新用户本地状态
     *
     * @param model
     * @param extra
     * @param obj
     */
    private static void updateLocalUserState(Context mContext, NotifyDataModel model, JSONObject extra, JSONObject obj) {
        Intent intent = null;
        Intent action = null;
        switch (model.getNotify_type()) {
            case NotifyDataModel.NOTIFY_TYPE_REQUEST:
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                intent.putExtra("message", 1);
                // 本地存储临时的提示性 MessageBean 对象,
                MessageBean prompt = new MessageBean();
                prompt.setMsgType(MessageBean.MSG_TYPE_TOTWOO_PROMPT);
                prompt.setSendUid(model.getUser_Id());
                prompt.setSendTime(model.getNotify_time());
                prompt.setPicUrl(model.getUser_Head());
                prompt.setMsgTitle(model.getUser_NickName());
                prompt.setContent(mContext.getString(R.string.request_frist_message));

                if (!TextUtils.isEmpty(model.getUser_NickName())) {
                    PreferencesUtils.put(mContext,
                            TotwooMessage.REQUEST_NICKNAME,
                            model.getUser_NickName());
                }

                //发送消息, 通知状态变化
                TotwooMessage msg = new TotwooMessage(CoupleLogic.COUPLE_STATE_REQUEST + "", prompt);
                msg.setTalkId(extra.optString("talk_id"));
                msg.setOtherPhone(extra.optString("from"));

                ACache requestCache = ACache.get(mContext);
                requestCache.put(CommonArgs.NOTIFICATION_MESSAGE, msg);

                EventBus.getDefault().post(msg);

                // 首饰提醒
                if (needShow(model, extra)) {
                    if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                        return;
                    }
                    BluetoothManage.getInstance().notifyJewelry(6, 0xe60032);
                }
                try {
                    DbHelper.getDbUtils().save(prompt);
                } catch (DbException e) {
                    e.printStackTrace();
                }

                break;
            case NotifyDataModel.NOTIFY_TYPE_REPLY:
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                intent.putExtra("message", 1);
                ToTwooApplication.owner.setPairedId(model.getUser_Id());
                ToTwooApplication.otherPhone = model.getUser_Id();
                PreferencesUtils.put(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_ID, model.getUser_Id());

                //配对成功，更新含义
//                com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_EMOTION, null);

                // 本地存储临时的提示性 MessageBean 对象,
                MessageBean promptReply = new MessageBean();
                promptReply.setMsgType(MessageBean.MSG_TYPE_TOTWOO_PROMPT);
                promptReply.setSendUid(model.getUser_Id());
                promptReply.setSendTime(model.getNotify_time());
                promptReply.setPicUrl(model.getUser_Head());
                promptReply.setContent(mContext.getString(R.string.paired_frist_message));
                ToTwooApplication.owner.setPairedId(extra.optString("talk_id"));

                LogUtils.e("aab ToTwooApplication.otherPhone = " + ToTwooApplication.otherPhone);
                LogUtils.e("aab ToTwooApplication.owner.getPairedId() = " + ToTwooApplication.owner.getPairedId());

                EventBus.getDefault().post(new TotwooMessage(CoupleLogic.COUPLE_STATE_REPLY + "", promptReply));
//弹出恭喜你弹窗
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOVE_HINT_TOTWOO, null);

                // 首饰提醒
                if (needShow(model, extra)) {
                    if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
                        BluetoothManage.getInstance().notifyJewelry(6, 0xe60032);
                    }
                }

                // 更新缓存配对人头像
                if (!TextUtils.isEmpty(model.getUser_Head())) {
                    PreferencesUtils.put(mContext, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, model.getUser_Head());
                }

                if (!TextUtils.isEmpty(model.getUser_NickName())) {
                    PreferencesUtils.put(mContext, CoupleLogic.PAIRED_PERSON_NICK_NAME, model.getUser_NickName());
                }

                try {
                    DbHelper.getDbUtils().save(promptReply);

                    // 再增加一条本地的存储
                    promptReply.setMsgType(MessageBean.MSG_TYPE_TOTWOO_IN);
                    DbHelper.getDbUtils().save(promptReply);
                } catch (DbException e) {
                    e.printStackTrace();
                }
                break;
            case NotifyDataModel.NOTIFY_TYPE_APART:
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                ToTwooApplication.owner.setPairedId("");

                // 清除缓存的配对人昵称
                PreferencesUtils.put(mContext, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "");
                PreferencesUtils.put(mContext, CoupleLogic.PAIRED_PERSON_NICK_NAME, "");

                // 首饰提醒
                if (needShow(model, extra)) {
                    BluetoothManage.getInstance().notifyJewelry(6, 0xe60032);
                }

                //发到HoemAcitivity和 HomeTotwooHolder
                EventBus.getDefault().post(new TotwooMessage(CoupleLogic.COUPLE_STATE_APART + "", null));

                // 处理本地与配对放有关的数据
                TotwooLogic.totwooClear();
                CoupleLogic.clearCouplePairedData(mContext);
                break;
            case NotifyDataModel.NOTIFY_TYPE_TOTWOO: //收到totwoo消息
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                intent.putExtra("message", 1);
                // 收到totwoo, 更新本地数据
                MessageBean bean = new MessageBean();
                bean.setMsgType(MessageBean.MSG_TYPE_TOTWOO_IN);
                bean.setSendUid(model.getUser_Id());
                bean.setMsgTitle(model.getUser_NickName());
                bean.setPicUrl(model.getUser_Head());
                bean.setSendTime(model.getNotify_time());

                //替换nickname:
                String pairedName = PreferencesUtils.getString(Utils.getApp(), CoupleLogic.PAIRED_PERSON_NICK_NAME, "");
                bean.setNotify_content(model.getNotify_content().replace(pairedName+":","")
                        .replace(pairedName+"：",""));
                if (model.getFeel_type() != 0) {
                    bean.setContent(model.getFeel_type() + "");
                }

                //有 0宽走0宽，否则走旧逻辑
                //"alert":{"loc-args":["' . $nick_name . '"],"loc-key":"TOTWOO_FORMAT"

                JewelryNotifyModel notifyModel = NotifyUtil.getTotwooNotifyModel(mContext);
                if (notifyModel.isNotifySwitch() && !checkExpired(extra)
                        && !TextUtils.isEmpty(PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))) {
                    if (!isOldPush(obj)) {// 0宽,长条，皓月
                        handleReceive0Totwoo(obj);
                    } else {
                        if (JewInfoSingleton.getInstance().getJewVersion() == 1) {
                            int feelType = model.getFeel_type() - 1000;
                            if (feelType == 9) {
                                BluetoothManage.getInstance().notifyJewelry(PreferencesUtils.getInt(mContext, NotifyUtil.LOVE_YOU_VIBRATION_SEC_KEY, LONG_VIBRATION_SEC),
                                        NotifyUtil.getColorValue(PreferencesUtils.getString(mContext, NotifyUtil.LOVE_YOU_FLASH_KEY, "PINK")));
                            } else if (feelType > 0) {
                                BluetoothManage.getInstance().notifyJewelryBq(feelType);
                            } else {
                                if (BleParams.isCodeJewelry()) {
                                    BluetoothManage.getInstance().notifyMorseCode(notifyModel.getVibrationSeconds(), NotifyUtil.getColorValue(notifyModel.getFlashColor()));
                                } else {
                                    BluetoothManage.getInstance().notifyJewelry(notifyModel.getVibrationSeconds(), notifyModel.getFlashColorValue());
                                }
                            }
                        } else { //自定义表情
                            if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {//皓月，长条
                                handleReceive0Totwoo(obj);
                            } else { //非皓月自定义表情  80系列
                                if (TextUtils.equals(model.getFeel_type() + "", CommonUtils.CONTENT_SORRY)) {
                                    BluetoothManage.getInstance().receiveFace(4);
                                } else if (TextUtils.equals(model.getFeel_type() + "", CommonUtils.CONTENT_SAD)) {
                                    BluetoothManage.getInstance().receiveFace(6);
                                } else {
                                    if (BleParams.isCodeJewelry()) {
                                        BluetoothManage.getInstance().notifyMorseCode(notifyModel.getVibrationSeconds(), NotifyUtil.getColorValue(notifyModel.getFlashColor()));
                                    } else {
                                        BluetoothManage.getInstance().notifyJewelry(notifyModel.getVibrationSeconds(), notifyModel.getFlashColorValue());
                                    }
                                }
                            }
                        }
                    }
                }


                // 更新缓存配对人头像
                if (!TextUtils.isEmpty(model.getUser_Head())) {
                    PreferencesUtils.put(mContext, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, model.getUser_Head());
                    LogUtils.w("JPushReceiver:totwoo user head:=" + model.getUser_Head());
                }
                if (!TextUtils.isEmpty(model.getUser_NickName())) {
                    PreferencesUtils.put(mContext,
                            CoupleLogic.PAIRED_PERSON_NICK_NAME,
                            model.getUser_NickName());
                }

                //发送到HomeTotwooHolder
                msg = new TotwooMessage(CommonArgs.ACTION_TOTWOO_DATA_CHANGEED, bean);
                msg.setCountReceiveNum(extra.optInt("count_receive_num"));
                msg.setCountSendNum(extra.optInt("count_send_num"));
                intent.putExtra("msgInfo", msg);
                ACache aCache = ACache.get(mContext);
                aCache.put(CommonArgs.NOTIFICATION_MESSAGE, msg);
                EventBus.getDefault().post(msg);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_RECEIVED_TOTWOO_MESSAGE, null);
                break;
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION:
                intent = new Intent(mContext, ConstellationActivity.class);
                intent.putExtra(CommonArgs.FROM_TYPE, "push");
                if (needShow(model, extra)) {
                    JewelryNotifyModel mdd = NotifyUtil.getFortuneNotifyModel(mContext);
                    BluetoothManage.getInstance().notifyJewelry(mdd.getVibrationSeconds(), mdd.getFlashColorValue());
                }
                break;
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_WEEK:
                intent = new Intent(mContext, ConstellationActivity.class);
                intent.putExtra(CommonArgs.FROM_TYPE, "week");
                break;
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_MONTH:
                intent = new Intent(mContext, ConstellationActivity.class);
                intent.putExtra(CommonArgs.FROM_TYPE, "month");
                break;
            case NotifyDataModel.NOTIFY_TYPE_CONSTELLATION_COMMEND:
                intent = new Intent(mContext, ConstellationActivity.class);
//			intent.putExtra(ConstellationActivity.EXTRA_IS_COMMEND, true);
                break;
            case NotifyDataModel.NOTIFY_TYPE_ADD_LOVE_NOTIFY:
                intent = new Intent(mContext, LoveNotifyListActivity.class);
                break;
            case NotifyDataModel.NOTIFY_TYPE_QIAN:
                intent = new Intent(mContext, QianActivity.class);
                break;
            case NotifyDataModel.NOTIFY_TYPE_GIFT_MESSAGE_OPENED:
                intent = new Intent(mContext, SendGiftGalleryActivity.class);
                break;
            case NotifyDataModel.NOTIFY_TYPE_WISH:
//                String url = "/v3/WishShare/index?wish_id=" + extra.optInt("wish_id") + "&wish_type=" + extra.optInt("wish_type")
//                        + "&totwoo_id=" + ToTwooApplication.owner.getTotwooId() + "&language=" + extra.optString("language") + "&source=totwoo";
                String url = HttpHelper.HOST + "/v3/WishShare/index?sign=" + DesUtil.wishSignInApp(extra.optInt("wish_id") + "", extra.optInt("wish_type"));
                intent = new Intent(mContext, WebViewActivity.class);
                intent.putExtra("mIsMovie", false);
                LogUtils.e("url = " + url);
                intent.putExtra("mUrl", url);
                intent.putExtra("isShare", false);
                intent.putExtra("shareTitle", "");
                intent.putExtra("shareContent", "");
                JewelryNotifyModel wishNotifyModel = NotifyUtil.getWishNotifyModel(mContext);
                if (!checkExpired(extra)) {
                    BluetoothManage.getInstance().notifyJewelry(wishNotifyModel.getVibrationSeconds(), wishNotifyModel.getFlashColorValue());
                }
                break;
            case NotifyDataModel.NOTIFY_TYPE_PERIOD:
                intent = new Intent(mContext, PeriodSettingActivity.class);
                if (needShow(model, extra)) {
                    JewelryNotifyModel mdd = NotifyUtil.getPeriodNotifyModel(mContext);
                    BluetoothManage.getInstance().notifyJewelry(mdd.getVibrationSeconds(), mdd.getFlashColorValue());
                }
//			intent.putExtra(ConstellationActivity.EXTRA_IS_COMMEND, true);
                break;
            case NotifyDataModel.NOTIFY_TYPE_WEIBO_TOKEN:
                intent = new Intent(mContext, WeiboAuthActivity.class);
                break;
            case NotifyDataModel.NOTIFY_TYPE_SLEEP:
                intent = new Intent(mContext, LoveSpacePinkActivity.class);
//                BluetoothManage.getInstance().notifyJewelry(6, 0xe60032);
                break;
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE:
            case NotifyDataModel.NOTIFY_TYPE_MESSAGE_EMERGENCY:
                intent = new Intent(mContext, MessageActivity.class);
                if (!checkExpired(extra)) {
                    if (BleParams.isSecurityJewlery()) {
                        BluetoothManage.getInstance().notifyJewelry(6, 0xffffff);
                    } else {
                        BluetoothManage.getInstance().notifyJewelry(6, 0xe60032);
                    }
                }
                break;
            case NotifyDataModel.NOTIFY_TYPE_LOVE_FRAGMENT:
            case NotifyDataModel.NOTIFY_TYPE_LUCKY:
            case NotifyDataModel.NOTIFY_TYPE_BIRTHDAY_NOTIFY:
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                intent.putExtra("message", 1);
                break;
            case NotifyDataModel.NOTIFY_TYPE_SEDENTARY:
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                intent.putExtra("message", 2);
                break;
            case NotifyDataModel.NOTIFY_TYPE_QUANTITY:
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                intent.putExtra("message", 3);
                break;
            case NotifyDataModel.NOTIFY_TYPE_GIFTS:
//            "extras":{"stamp":"1652783726","msg_type":"3000","id":"{","audioUrl":"","audioUPreviewImageUrl":"","imageUrl":"greetingcard/20220517/16527787440000043170413122633621.jpg","vedioUrl":"","vedioPreviewImageUrl":"","text":"测试 09","senderName":"{"},"badge":65536}', contentType='', title='', senderId='3059168ac662268dbbe3ff73', appId='com.totwoo.totwoo', platform='0'}

                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                JewelryNotifyModel notify = NotifyUtil.getGiftNotifyModel(mContext);
                if (notify.isNotifySwitch() && !checkExpired(extra)) {
                    BluetoothManage.getInstance().notifyJewelry(notify.getVibrationSeconds(), notify.getFlashColorValue());
                }

                GiftMessageBean giftMessageBean = new GiftMessageBean();
                giftMessageBean.setGreetingCardId(extra.optString("id"));
                giftMessageBean.setSenderName(extra.optString("senderName"));
                try {
                    giftMessageBean.setGreetingCardType(Integer.parseInt(extra.optString("greetingCardType")));
                    giftMessageBean.setSendTime(Long.parseLong(extra.optString("stamp")));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }

                GiftMessageCard card = new GiftMessageCard();
                card.setText(extra.optString(""));
                card.setAudioUrl(extra.optString("audioUrl"));
                card.setImageUrl(extra.optString("imageUrl"));
                card.setVedioUrl(extra.optString("vedioUrl"));
                card.setVedioPreviewImageUrl(extra.optString("vedioPreviewImageUrl"));

                giftMessageBean.setGreetingCardData(card);

                //推送情书，直接跳转到情书页面 ,过滤离线，因为离线会走 启动接口
//                if (giftMessageBean != null && Math.abs(System.currentTimeMillis() - giftMessageBean.getSendTime() * 1000) < 800) {
                intent = new Intent(mContext, GiftDataActivity.class);
                intent.putExtra(CommonArgs.FROM_TYPE, GiftDataActivity.RECEIVER);
                intent.putExtra(GiftDataActivity.ITEM, giftMessageBean);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(intent);
//                }
                break;
            default:
                intent = new Intent(mContext, HomeActivityControl.getInstance().getTagertClass());
                break;
        }
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        model.setTargetIntent(intent);
    }

    /**
     * 收到totwoo消息，0宽字符
     *
     * @param obj
     */
    private static void handleReceive0Totwoo(JSONObject obj) {
        if (!TextUtils.isEmpty(obj.optString("alert"))) {
            String alert = obj.optString("alert");

            String s = CommonUtils.replaceZeroWidthWithNumbers(alert);

            String colorHex = CommonUtils.getColorHex(s);
            int vibrationCount = 0;
            switch (CommonUtils.getVibrationStrength(s)) {
                case 0:
                    vibrationCount = NotifyUtil.SHORT_VIBRATION_SEC;
                    break;
                case 2:
                    vibrationCount = NotifyUtil.MARQUEE_VIBRATION_SEC;
                    break;
                default:
                    vibrationCount = NotifyUtil.LONG_VIBRATION_SEC;
            }

            if (!TextUtils.isEmpty(colorHex)) {
                int color = Color.parseColor("#" + colorHex);
                BluetoothManage.getInstance().notifyJewelry(vibrationCount, color);
                Log.e("xLog", "收互动: " + vibrationCount + ", color = " + colorHex);
            } else {
                Log.e("xLog", "未找到颜色值");
            }
        }
    }

    //是否旧版本推送
    private static boolean isOldPush(JSONObject obj) {
        if (!TextUtils.isEmpty(obj.optString("alert"))) {
            String alert = obj.optString("alert");
            return alert.contains("loc-args");
        }
        return false;
    }
}
