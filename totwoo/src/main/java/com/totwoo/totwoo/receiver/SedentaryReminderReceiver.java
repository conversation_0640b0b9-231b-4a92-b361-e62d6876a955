//package com.totwoo.totwoo.receiver;
//
//import android.app.AlarmManager;
//import android.app.PendingIntent;
//import android.content.Context;
//import android.content.Intent;
//
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.activity.HomeActivity;
//import com.totwoo.totwoo.activity.SedentaryReminderActivity;
//import com.totwoo.totwoo.bean.NotifyDataModel;
//import com.totwoo.totwoo.bean.Sedentary;
//import com.totwoo.totwoo.bean.Step;
//import com.totwoo.totwoo.ble.BleWrapper;
//import com.totwoo.totwoo.utils.DateUtil;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//
//import org.greenrobot.eventbus.Subscribe;
//import org.greenrobot.eventbus.ThreadMode;
//
//import java.util.Timer;
//import java.util.TimerTask;
//
///**
// * 久坐提醒广播接收者，通过发送广播来开启记时提醒
// *
// * <AUTHOR>
// */
//
//public class SedentaryReminderReceiver{
//
//    public static final String RECEIVER_ACTION = "send.sedentary.reminder";
//    // 设备连接状态
//    public static boolean DEVICES_CONNECT_STATE ;
//
//    public static final String PREF_SEDENTARY_START_TIME = "sendentary_start_time";
//
//    public  final static  String CONNECT_STATE = "connect_state";
//
//    // 上次行走步数
//    public static final String STEP_DATA_LAST = "last_step_data";
//
//    // 系统计时组件
//    private AlarmManager am;
//
//    private PendingIntent sendIntent;
//    // 用户设置的提醒数据
//    private Sedentary sedentary;
//
//    // 执行的任务
//    private int action;
//    // 执行的任务常量
//    public static final int START = 1;// 开始计时
//    public static final int STOP = 2;// 停止计时
//    public static final int CHANGE = 3;// 计时改动
//    public static final int CHANGE_PARAMETER = 5;// 久坐提醒参数改动
//    public static final int REMINDER = 4;// 执行提醒任务
//
//    private boolean isInweeks;
//
//    private boolean isInTime;
//
//    private int nextWeek;
//
//    private static long nextTime;
//
//    private Context mContext;
//
//    private static Timer timer;
//
//    private static TimerTask task;
//    // 上次断开连接的时间
//    private static long lastStopUTC;
//
//    private int oneMS = 60000;
//
//
//    public SedentaryReminderReceiver(Context mContext) {
//        this.mContext = mContext;
//    }
//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onReceive(final Intent intent) {
//
//        switch (intent.getAction()) {
//            case BleWrapper.ACTION_BLE_DEVICES_CONNECT:
//                LogUtils.i("SedentaryReminderReceiver", "START");
//                    if (PreferencesUtils.getBoolean(mContext,CONNECT_STATE,false)==false){
//                        PreferencesUtils.put(mContext, PREF_SEDENTARY_START_TIME,
//                                System.currentTimeMillis());
//                        LogUtils.i("SedentaryReminderReceiver", "ReStart");
//                    }
//                DEVICES_CONNECT_STATE = true;
//                PreferencesUtils.put(mContext,CONNECT_STATE,DEVICES_CONNECT_STATE);
//                if (timer != null) {
//                    LogUtils.i("SedentaryReminderReceiver", "timer != null");
//                    timer.cancel();
//                    timer = null;
//                }
//                if (task != null) {
//                    LogUtils.i("SedentaryReminderReceiver", "task != null");
//                    task.cancel();
//                    task = null;
//                }
//                // 距离上次stop的时间差
//                long UTCdiff = System.currentTimeMillis() - lastStopUTC;
//                // 如果小于一分钟 那么直接返回
//                if (UTCdiff <= 1000 * 60) {
//                    LogUtils.i("SedentaryReminderReceiver", "start-return");
//                    return;
//                }
//                LogUtils.i("SedentaryReminderReceiver", "START_ture");
//                intent.putExtra("action", START);
//                break;
//            case BleWrapper.ACTION_BLE_DEVICES_DISCONNECT:
//                LogUtils.i("SedentaryReminderReceiver", "STOP");
//                DEVICES_CONNECT_STATE = false;
//
//                task = new TimerTask() {
//                    @Override
//                    public void run() {
//                        if (intent != null) {
//                            intent.putExtra("action", STOP);
//                        }
//                        if (mContext != null) {
//                            PreferencesUtils.put(mContext,
//                                    PREF_SEDENTARY_START_TIME, -1L);
//                            PreferencesUtils.put(mContext,CONNECT_STATE,DEVICES_CONNECT_STATE);
//                            initReminder(mContext, intent);
//                        }
//                        if (timer != null) {
//                            timer.cancel();
//                            timer = null;
//                        }
//                        if (task != null) {
//                            task.cancel();
//                            task = null;
//                        }
//
//                        LogUtils.i("SedentaryReminderReceiver", "STOP_ture");
//                    }
//                };
//                if (timer == null) {
//                    timer = new Timer();
//                }
//                // 一分钟之后才真正停止提醒
//
//                if (task != null && timer != null)
//                    timer.schedule(task, 1000 * 60);
//
//                lastStopUTC = System.currentTimeMillis();
//                return;
//            case BleWrapper.ACTION_BLE_STEP_DATA_AVAILABLE:
//                intent.putExtra("action", CHANGE);
//                Step step = (Step) intent
//                        .getSerializableExtra(BleWrapper.EXTRA_BLE_DATA_TAG_STEP);
//                // LogUtils.i("SedentaryReminderReceiver", step.getSteps() + "");
//                int lastStep = PreferencesUtils.getInt(mContext, STEP_DATA_LAST, 0);
//                PreferencesUtils.put(mContext, STEP_DATA_LAST, step.getSteps());
//                // 根据健步数据判断是否有走动
//
//                if (step.getSteps() == lastStep || step.getSteps() == 0) {
//                    return;
//                }
//                PreferencesUtils.put(mContext, PREF_SEDENTARY_START_TIME,
//                        System.currentTimeMillis());
//                break;
//        }
//        initReminder(mContext, intent);
//    }
//
//    private void initReminder(Context context, final Intent intent) {
//        init(context, intent);
//        // 判断用户是否开启提醒和
//        if (sedentary.isOpen() && DEVICES_CONNECT_STATE) {
//            // 当前时间是否在用户设置时间内如果不在 需要算出下次提醒时间 和是否登录
//            if (isTimeIncluded()) {
//                setAm(intent);
//            } else {
//                // 下次设置的时间
//                long differTime = sedentary.getStartTime()
//                        - DateUtil.getStringToDate("HH:mm", DateUtil.getHour()
//                        + ":" + DateUtil.getMin());
//                int week = DateUtil.getWeek();
//                // 下次提醒的星期
//                int differWeek;
//                char[] chars = sedentary.getRepeatRemind().toCharArray();
//                if (isWeek(week, chars) && differTime > 0) {
//                    nextWeek = week;
//                } else {
//                    for (int i = 1; i <= 7; i++) {
//                        nextWeek = week + i;
//                        if (nextWeek > 7) {
//                            nextWeek = nextWeek - 7;
//                        }
//                        if (isWeek(nextWeek, chars)) {
//                            break;
//                        }
//                    }
//                }
//
//                differWeek = nextWeek - week;
//
//                if (differWeek <= 0) {
//
//                    if (differWeek == 0 && differTime > 0) {
//
//                    } else {
//                        differWeek += 7;
//                    }
//
//                }
//
//                nextTime = System.currentTimeMillis() + differTime + differWeek
//                        * 24 * 60 * 1000 * 60 + sedentary.getSitWhenlong() * 60
//                        * 1000;
//                am.set(AlarmManager.RTC_WAKEUP, nextTime, sendIntent);
//                LogUtils.i("SedentaryReminderReceiver",
//                        DateUtil.getDateToString("HH:mm:ss", nextTime));
//            }
//
//        } else {
//            am.cancel(sendIntent);
//        }
//    }
//
//    /**
//     * 判断当前时间是否需要提醒
//     *
//     * @return
//     */
//    private boolean isTimeIncluded() {
//
//        long currentTime = System.currentTimeMillis();
//
//        String s = DateUtil.getDateToString("HH:mm", currentTime);
//
//        currentTime = DateUtil.getStringToDate("HH:mm", s);
//
//        int week = DateUtil.getWeek();
//
//        char[] weeks = sedentary.getRepeatRemind().toCharArray();
//
//        isInweeks = false;
//
//        isInweeks = isWeek(week, weeks);
//
//        if (sedentary.getStartTime() < sedentary.getStopTime()) {
//            isInTime = currentTime > sedentary.getStartTime()
//                    && currentTime < (sedentary.getStopTime() - sedentary
//                    .getSitWhenlong() * 60000);
//        } else {
//            isInTime = !(currentTime < sedentary.getStartTime() && currentTime > (sedentary
//                    .getStopTime() - sedentary.getSitWhenlong() * 60000));
//        }
//
//        return isInTime && isInweeks;
//    }
//
//    private boolean isWeek(int week, char[] weeks) {
//        boolean isWeek = false;
//        for (int i = 0; i < weeks.length; i++) {
//            int num = Integer.parseInt(String.valueOf(weeks[i]));
//            num = num + 2;
//            if (num > 7) {
//                num = num - 7;
//            }
//            if (week == num) {
//                isWeek = true;
//                break;
//            }
//        }
//        return isWeek;
//    }
//
//    /**
//     * 计时设置
//     *
//     * @param intent
//     */
//    private void setAm(Intent intent) {
//
//        action = intent.getIntExtra("action", CHANGE);
//        switch (action) {
//            case START:
//                nextTime = System.currentTimeMillis() + oneMS
//                        * sedentary.getSitWhenlong();
//                am.set(AlarmManager.RTC_WAKEUP, nextTime, sendIntent);
//                break;
//            case STOP:
//                am.cancel(sendIntent);
//                break;
//            case CHANGE:
//                PreferencesUtils.put(mContext, PREF_SEDENTARY_START_TIME,
//                        System.currentTimeMillis());
//                nextTime = System.currentTimeMillis() + oneMS
//                        * sedentary.getSitWhenlong();
//                am.set(AlarmManager.RTC_WAKEUP, nextTime, sendIntent);
//                break;
//            case CHANGE_PARAMETER:
//                PreferencesUtils.put(mContext, PREF_SEDENTARY_START_TIME,
//                        System.currentTimeMillis());
//                int changeSit = intent.getIntExtra("changeSitPoor", 0);
//                if (changeSit > 0) {
//                    nextTime = nextTime + changeSit * oneMS;
//                }else {
//                    nextTime = System.currentTimeMillis() + oneMS
//                            * sedentary.getSitWhenlong();
//                }
//                am.set(AlarmManager.RTC_WAKEUP, nextTime, sendIntent);
//                break;
//            case REMINDER:
//                nextTime = System.currentTimeMillis() + oneMS
//                        * sedentary.getSitWhenlong();
//                NotifyDataModel model = new NotifyDataModel(mContext);
//                model.setUser_NickName(ToTwooApplication.owner.getNickName());
//                model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_SEDENTARY, true);
//                model.setNotify_Id(12456);
//                model.setTargetIntent(new Intent(mContext, HomeActivity.class));
//                model.setNotify_title(mContext
//                        .getString(R.string.sedentary_reminder));
//                if (ToTwooApplication.owner.isLogin()) {
////                    ShowNotify(model);
//                    NotifyDataModel.ShowNotify(mContext, model);
//                }
//                am.set(AlarmManager.RTC_WAKEUP, nextTime, sendIntent);
//                LogUtils.i("SedentaryReminderReceiver", "send");
//                break;
//        }
//        LogUtils.i("SedentaryReminderReceiver",
//                DateUtil.getDateToString("HH:mm:ss", nextTime));
//    }
//
//    private void init(Context context, Intent intent) {
//
//        Intent sRIntent = new Intent(RECEIVER_ACTION);
//        sRIntent.putExtra("action", REMINDER);
//        // 获取sp里面存储的用户久坐提醒数据
//        if (sedentary == null) {
//            sedentary = new Sedentary(PreferencesUtils.getBoolean(context,
//                    SedentaryReminderActivity.SEDENTARY_REMINDER_SWITCH_KEY,
//                    true), PreferencesUtils.getInt(context, "sitWhenlong", SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME),
//                    PreferencesUtils.getLong(context, "startTime",
//                            DateUtil.getStringToDate("HH:mm", "09:00")),
//                    PreferencesUtils.getLong(context, "stopTime",
//                            DateUtil.getStringToDate("HH:mm", "18:00")),
//                    PreferencesUtils
//                            .getString(context, "repeatRemind", "01234"));
//        }
//
//        if (null == sendIntent) {
//            sendIntent = PendingIntent.getBroadcast(context, 0, sRIntent,
//                    Apputils.wrapImmutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
//        }
//        if (null == am) {
//            am = (AlarmManager) context.getSystemService(context.ALARM_SERVICE);
//        }
//
//    }
//}
