package com.totwoo.totwoo.newConrtoller;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.MediaStore;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.base.BaseContraller;
import com.etone.framework.component.http.HttpParams;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.bean.ImageBean;
import com.totwoo.totwoo.bean.holderBean.ImageFolderBean;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by xinyoulingxi on 2017/8/4.
 */

public class LoadSystemImagesController extends BaseContraller
{
    public static final String LOAD_SYSTEM_IMAGE = "com.etone.framework.event.EventBus.LOAD_SYSTEM_IMAGE";

    private static final LoadSystemImagesController instance = new LoadSystemImagesController();

    private LoadSystemImagesController()
    {
        super();
    }

    public static LoadSystemImagesController getInstance()
    {
        return instance;
    }

    public void getAllImages(Context context)
    {
        HttpParams hp = new HttpParams("", "");
        hp.putUserDefine("context", context);
        EventBus.onPostReceived(LOAD_SYSTEM_IMAGE, hp);
    }

    @EventInject(eventType = LOAD_SYSTEM_IMAGE, runThread = TaskType.Async)
    public void getAllImages(EventData data)
    {
        HttpParams hp = (HttpParams) data;
        Context context = (Context) hp.getUserDefine("context");
        Uri uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
        ContentResolver resolver = context.getContentResolver();

        //支持gif列表
//        Cursor cursor = resolver.query(uri, null, MediaStore.Images.Media.MIME_TYPE + "=? or " + MediaStore.Images.Media.MIME_TYPE + "=? or " + MediaStore.Images.Media.MIME_TYPE + "=? ",
//                new String[]{"image/jpeg", "image/png","image/gif"}, MediaStore.Images.Media.DATE_MODIFIED);
        Cursor cursor = resolver.query(uri, null, MediaStore.Images.Media.MIME_TYPE + "=? or " + MediaStore.Images.Media.MIME_TYPE + "=?", new String[]{"image/jpeg", "image/png"}, MediaStore.Images.Media.DATE_MODIFIED + " DESC");
        ArrayList<ImageBean> allImageList = new ArrayList<>();
        HashMap<String, ImageFolderBean> folderMap = new HashMap<>();
        ArrayList<ImageFolderBean> folderList = new ArrayList<>();
        String path;
        String bigImagePath;
        ImageBean bean;
        ImageFolderBean folderBean;
        File tmpFile;
        if (null != cursor)
        {
            while (cursor.moveToNext())
            {
                path = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Thumbnails.DATA));
                bigImagePath = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Media.DATA));
                LogUtils.e("缩略图地址" + path);
                tmpFile = new File(path);
                if (!tmpFile.exists())
                    continue;

                bean = new ImageBean(path, bigImagePath);
                allImageList.add(bean);

                String parentPath = tmpFile.getParentFile().getAbsolutePath();
                folderBean = folderMap.get(parentPath);
                if (folderBean == null)
                {
                    folderBean = new ImageFolderBean(parentPath, tmpFile);
                    folderMap.put(parentPath, folderBean);
                    folderList.add(folderBean);
                }
                folderBean.imgList.add(bean);
            }
            folderBean = new ImageFolderBean(context, allImageList);
            folderBean.imgList = allImageList;
            folderList.add(0, folderBean);

            cursor.close();
        }

        hp.putUserDefine("allImageList", allImageList);
        hp.putUserDefine("folderMap", folderMap);
        hp.putUserDefine("folderList", folderList);

        EventBus.onPostReceived(S.E.E_LOAD_SYSTEM_IMAGE_FINISH, hp);
    }
}
