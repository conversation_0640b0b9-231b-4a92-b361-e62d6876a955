package com.totwoo.totwoo.newConrtoller;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.base.BaseContraller;
import com.etone.framework.component.http.HttpUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.JSONUtils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.bean.Step;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.utils.HttpHelper;

import java.util.ArrayList;

/**
 * Created by xinyoulingxi on 2017/6/1.
 */

public class StepController extends BaseContraller
{
    private static final StepController instance = new StepController();
    private StepController()
    {
        super();
    }
    public static StepController getInstance()
    {
        return instance;
    }

    public void getStepList()
    {
        HttpValues hv = new HttpValues(S.H.H_STEP_GET_LIST, HttpHelper.URL_WALK_DATA);
        HttpUtils.run(hv);
    }

    @EventInject (eventType = S.H.H_STEP_GET_LIST, runThread = TaskType.Async)
    public void onGetStepListFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk())
        {
            LogUtils.e("content:" + hv.content);
            String[] res = JSONUtils.getStringArray(hv.content, "data", null);
            ArrayList<Step> list = new ArrayList<>();
            if (res != null)
            {
                for (int i = res.length - 2; i >= 0; i--)
                {
                    Step step = new Step(res[i]);
                    list.add(step);
                }
            }

            hv.putUserDefine("stepList", list);
            EventBus.onPostReceived(S.E.E_STEP_GET_LIST_SUCCESSED, data);
        }
        else
        {
            EventBus.onPostReceived(S.E.E_STEP_GET_LIST_FAILED, data);
        }
    }
}
