package com.totwoo.totwoo.newConrtoller;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.base.BaseContraller;
import com.etone.framework.component.http.HttpUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.utils.HttpHelper;

import java.util.Calendar;

/**
 * Created by xinyoulingxi on 2018/1/3.
 */

public class ImPushController extends BaseContraller
{
    private static final ImPushController instance = new ImPushController();
    private ImPushController()
    {
        super();
    }

    public static ImPushController getInstance()
    {
        return instance;
    }

    public void sendTotwoo(String content)
    {
        HttpValues hv = new HttpValues (S.H.H_IM_PUSH_TOTWOO_SEND, HttpHelper.URL_TOTWOO_SEND);
        hv.addParams("send_from", "jewelry");
        Calendar cal = Apputils.getZeroCalendar(null);
        hv.addParams("startTime", cal.getTimeInMillis() / 1000 + "");
        cal.add(Calendar.DAY_OF_MONTH, 1);
        hv.addParams("endTime", cal.getTimeInMillis() / 1000 + "");
        if (content != null && content.length() != 0)
            hv.addParams("content", content);

        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_IM_PUSH_TOTWOO_SEND, runThread = TaskType.Async)
    public void onSendTotwooFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        String res = hv.content;
        LogUtils.e("res:" + res);
    }
}
