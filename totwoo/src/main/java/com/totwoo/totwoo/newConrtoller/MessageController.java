package com.totwoo.totwoo.newConrtoller;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.base.BaseContraller;
import com.etone.framework.component.http.HttpMethod;
import com.etone.framework.component.http.HttpUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.utils.HttpHelper;

/**
 * Created by xinyoulingxi on 2017/10/31.
 */

public class MessageController extends BaseContraller
{
    private static final MessageController instance = new MessageController();
    public static MessageController getInstance()
    {
        return instance;
    }

    public MessageController()
    {
        super();
    }

    public void messageShow()
    {
        HttpValues hv = new HttpValues(S.H.H_MESSAGE_SHOW, HttpHelper.HOSTURL_V3 + "Message/show");
        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_MESSAGE_SHOW, runThread = TaskType.Async)
    public void onMessageShowFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk())
        {
            EventBus.onPostReceived(S.E.E_MESSAGE_SHOW_SUCCESSED, data);
        }
        else
        {
            EventBus.onPostReceived(S.E.E_MESSAGE_SHOW_FAILED, data);
        }
    }

    public void messageClick(String messageId)
    {
        HttpValues hv = new HttpValues(S.H.H_MESSAGE_CLICK, HttpHelper.HOSTURL_V3 + "Message/click");
        hv.method = HttpMethod.POST;
        hv.addParams("message_id", messageId);
        HttpUtils.run(hv);
    }

    @EventInject (eventType = S.H.H_MESSAGE_CLICK, runThread = TaskType.Async)
    public void onMessageClickFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        //成功失败不关心结果，所以这里不处理，如果需要关心，则在这里加上广播即可
        if (hv.isRequestOk())
        {
            //成功
        }
        else
        {
            //失败
        }
    }
}
