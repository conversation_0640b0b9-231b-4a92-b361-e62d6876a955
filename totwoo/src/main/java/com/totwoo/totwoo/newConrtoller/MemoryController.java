package com.totwoo.totwoo.newConrtoller;

import android.text.TextUtils;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.base.BaseContraller;
import com.etone.framework.component.http.HttpMethod;
import com.etone.framework.component.http.HttpUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.JSONUtils;
import com.google.gson.JsonObject;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.bean.OnlineStatueEventData;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.utils.AppObserver;
import com.totwoo.totwoo.utils.HttpHelper;

import java.util.ArrayList;

import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by xinyoulingxi on 2017/8/2.
 */

public class MemoryController extends BaseContraller {
    private static final MemoryController instance = new MemoryController();

    private MemoryController() {
        super();
    }

    public static MemoryController getInstance() {
        return instance;
    }

    public void getList(int page) {
        String url = HttpHelper.HOSTURL + "memory/getList";
        HttpValues hv = new HttpValues(S.H.H_MEMORY_GET_LIST, url);
        hv.method = HttpMethod.POST;

        hv.addParams("page", page + "");
        hv.addParams("perpage", "10");

        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_MEMORY_GET_LIST, runThread = TaskType.Async)
    public void onGetListFinished(EventData data) {
        HttpValues hv = (HttpValues) data;
        LogUtils.e(hv.content.replaceAll("\r", "").replace("\n", ""));
        if (hv.isRequestOk()) {
            String tmp = JSONUtils.getString(hv.content, "data", "");
            int count = JSONUtils.getInt(tmp, "count", 0);
            String[] res = JSONUtils.getStringArray(tmp, "list", new String[]{});
            ArrayList<MemoryBean> list = new ArrayList<>();
            for (int i = 0; i < res.length; i++) {
                MemoryBean bean = new MemoryBean(res[i]);
                list.add(bean);
            }
            hv.putUserDefine("MemoryBeanList", list);
            hv.putUserDefine("count", count);
            EventBus.onPostReceived(S.E.E_MEMORY_GET_LIST_SUCCESSED, data);

            for (int i = 0; i < list.size(); i++)
                LogUtils.e(list.get(i).toString());
        } else {
            EventBus.onPostReceived(S.E.E_MEMORY_GET_LIST_FAILED, data);
        }
    }

    public void save(MemoryBean bean) {
        if (bean == null)
            return;

        String url = HttpHelper.HOSTURL + "Memory/save";
        HttpValues hv = new HttpValues(S.H.H_MEMORY_SAVE, url);
        hv.method = HttpMethod.POST;

        hv.addParams("memory_type", bean.memory_type + "");
        hv.addParams("content", bean.content);
        switch (bean.memory_type) {
            case MemoryBean.TYPE_SAY:
                break;
            case MemoryBean.TYPE_IMG:
                if (bean.img_url == null)
                    return;
                String tmp = "[";
                for (int i = 0; i < bean.img_url.length; i++)
                    tmp += "\"" + bean.img_url[i] + "\", ";
                tmp = tmp.substring(0, tmp.length() - 2) + "]";
                hv.addParams("img_url", tmp);
                break;
            case MemoryBean.TYPE_AUD:
                hv.addParams("audio_url", bean.audio_url);
                break;
            case MemoryBean.TYPE_VED:
                hv.addParams("vedio_url", bean.vedio_url);
                hv.addParams("cover_url", bean.cover_url);
                break;
            default:
                break;
        }

        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_MEMORY_SAVE, runThread = TaskType.Async)
    public void onSaveFinished(EventData data) {
        HttpValues hv = (HttpValues) data;
        LogUtils.e("res:" + hv.content);
        String res = hv.content;
        if (hv.isRequestOk()) {
            String tmp = JSONUtils.getString(res, "data", "");
            LogUtils.e("tmp:" + tmp);
            MemoryBean mb = new MemoryBean(tmp);
            LogUtils.e("mb:" + mb.toString());
            hv.putUserDefine(S.M.M_IMAGES, mb);
            EventBus.onPostReceived(S.E.E_MEMORY_SAVE_SUCCESSED, data);
        } else {
            EventBus.onPostReceived(S.E.E_MEMORY_SAVE_FAILED, data);
        }
    }

    public void total() {
        String url = HttpHelper.HOSTURL + "Memory/total";
        HttpValues hv = new HttpValues(S.H.H_MEMORY_TOTAL, url);
        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_MEMORY_TOTAL)
    public void onMemoryTotalFinished(EventData data) {
        HttpValues hv = (HttpValues) data;
        String res = hv.content;
        LogUtils.e("res:" + res);
        if (hv.isRequestOk()) {
            String tmp = JSONUtils.getString(res, "data", "");
            LogUtils.e("res:" + tmp);
            int isEmpty = JSONUtils.getInt(tmp, "is_empty", 0);
            int txtTotal = JSONUtils.getInt(tmp, "txt_total", 0);
            int imgTotal = JSONUtils.getInt(tmp, "img_total", 0);
            int audTotal = JSONUtils.getInt(tmp, "audio_total", 0);
            int vedTotal = JSONUtils.getInt(tmp, "vedio_total", 0);
            hv.putUserDefine("isEmpty", isEmpty);
            hv.putUserDefine("txtTotal", txtTotal);
            hv.putUserDefine("imgTotal", imgTotal);
            hv.putUserDefine("audTotal", audTotal);
            hv.putUserDefine("vedTotal", vedTotal);
            LogUtils.e("res:" + ", isEmpty:" + isEmpty + ", txtTotal:" + txtTotal + ", imgTotal:" + imgTotal + ", audTotal:" + audTotal + ", vedTotal:" + vedTotal);
            EventBus.onPostReceived(S.E.E_MEMORY_TOTAL_SUCCESSED, hv);
        } else {
            EventBus.onPostReceived(S.E.E_MEMORY_TOTAL_FAILED, hv);
        }
    }

    public void delete(MemoryBean bean) {
        if (bean == null)
            return;

        String url = HttpHelper.HOSTURL + "Memory/delete";
        HttpValues hv = new HttpValues(S.H.H_MEMORY_DELETE, url);
        hv.method = HttpMethod.POST;

        hv.addParams("memory_id", bean.memory_id + "");
        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_MEMORY_DELETE)
    public void onDeleteFinished(EventData data) {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk()) {
            EventBus.onPostReceived(S.E.E_MEMORY_DELETE_SUCCESSED, data);
        } else {
            EventBus.onPostReceived(S.E.E_MEMORY_DELETE_FAILED, data);
        }
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {
        super.onEventException(eventType, data, e);
        HttpValues hv = (HttpValues) data;
        LogUtils.e("onEventException, type:" + eventType);
        switch (eventType) {
            case S.H.H_MEMORY_GET_LIST:
                EventBus.onPostReceived(S.E.E_MEMORY_GET_LIST_FAILED, data);
                break;
            case S.H.H_MEMORY_SAVE:
                EventBus.onPostReceived(S.E.E_MEMORY_SAVE_FAILED, data);
                break;
            case S.H.H_MEMORY_DELETE:
                EventBus.onPostReceived(S.E.E_MEMORY_DELETE_FAILED, data);
                break;
            default:
                e.printStackTrace();
                break;
        }
    }

    public void checkFirmwareType() {
        HttpValues hv = new HttpValues(S.H.H_CHECK_FIRMWARE_TYPE, HttpHelper.HOSTURL_V3 + "Firmware/checkType");
        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_CHECK_FIRMWARE_TYPE, runThread = TaskType.Async)
    public void onCheckFirmwareTypeFinished(EventData data) {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk()) {
            EventBus.onPostReceived(S.E.E_CHECK_FIRMWARE_TYPE_SUCCESSED, data);
        } else {
            EventBus.onPostReceived(S.E.E_CHECK_FIRMWARE_TYPE_FAILED, data);
        }
    }

    private String lastName = "init";

    public void checkConnectState(String name) {
//        if(TextUtils.equals(lastName,name)){
//            LogUtils.e("aab name = " + name);
//            return;
//        }
//        lastName = name;
//        HttpValues hv = new HttpValues(S.H.H_CHECK_JEWELRY_CONNECT, HttpHelper.HOSTURL_V3 + "firmware/connectionState");
//        hv.addParams("firmware_type", name);
//        hv.addParams("target_mobile", ToTwooApplication.otherPhone);
//        HttpUtils.run(hv);

        Log.e("checkConnectState", name + " ," + ToTwooApplication.otherPhone);
        if (ToTwooApplication.otherPhone == null || TextUtils.isEmpty(ToTwooApplication.otherPhone)) {
            return;
        }
        HttpHelper.commonService.connectionState(name, ToTwooApplication.otherPhone)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new AppObserver<JsonObject>() {

                    @Override
                    public void onSuccess(JsonObject baseBean) {
                        Log.e("ConnectStateResult", baseBean.toString());
                        String target_firmware_type = baseBean.get("target_firmware_type").getAsString();
                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_ONLINE_STATUE_CHANGE, new OnlineStatueEventData(target_firmware_type));
                    }
                });
    }

    @EventInject(eventType = S.H.H_CHECK_JEWELRY_CONNECT, runThread = TaskType.Async)
    public void onCheckConnectStateFinished(EventData data) {
        HttpValues hv = (HttpValues) data;
        LogUtils.e("res:" + hv.content);
        if (hv.isRequestOk()) {
            LogUtils.e("check jewelry state ok");
        } else {
            LogUtils.e("check jewelry state error");
        }
    }
}
