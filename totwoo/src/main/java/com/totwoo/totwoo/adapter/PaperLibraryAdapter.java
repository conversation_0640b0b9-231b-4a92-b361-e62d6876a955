//package com.totwoo.totwoo.adapter;
//
//import android.content.Context;
//import android.os.Handler;
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//import androidx.constraintlayout.widget.ConstraintLayout;
//import androidx.recyclerview.widget.RecyclerView;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.view.animation.Animation;
//import android.view.animation.AnimationUtils;
//import android.widget.ImageView;
//import android.widget.Toast;
//
//import com.bumptech.glide.Glide;
//import com.liulishuo.okdownload.DownloadTask;
//import com.liulishuo.okdownload.core.cause.EndCause;
//import com.liulishuo.okdownload.core.listener.DownloadListener2;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.utils.CommonUtils;
//
//import java.io.File;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
//import cn.tillusory.sdk.TiSDK;
//import cn.tillusory.sdk.TiSDKManager;
//import cn.tillusory.sdk.bean.TiSticker;
//import cn.tillusory.sdk.common.TiUtils;
//
///**
// * Created by totwoo on 2018/8/21.
// */
//
//public class PaperLibraryAdapter extends RecyclerView.Adapter<PaperLibraryAdapter.ViewHolder> {
//
//    private int height;
//    private TiSDKManager tiSDKManager;
//    private List<TiSticker> stickerList;
//    private Handler handler = new Handler();
//    private int selectedPosition = 0;
//    private Context context;
//
//    private Map<String, String> downloadingStickers = new ConcurrentHashMap<>();
//
//    public PaperLibraryAdapter(Context context, List<TiSticker> stickerList, TiSDKManager tiSDKManager) {
//        height = CommonUtils.getScreenWidth() / 5;
//        this.context = context;
//        this.tiSDKManager = tiSDKManager;
//        this.stickerList = stickerList;
//    }
//
//    @Override
//    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
//        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.paper_library_item, parent, false);
//        PaperLibraryAdapter.ViewHolder viewHolder = new PaperLibraryAdapter.ViewHolder(view);
//
////        DLManager.getInstance(view.getContext()).setMaxTask(5);
//        return viewHolder;
//    }
//
//    @Override
//    public void onBindViewHolder(final ViewHolder holder, final int position) {
//        holder.camera_paper_library_cl.setLayoutParams(new ViewGroup.LayoutParams(height, height));
//
//        final TiSticker tiSticker = stickerList.get(holder.getAdapterPosition());
////        if (position == 0) {
////            holder.camera_paper_library_view.setVisibility(View.VISIBLE);
////        } else {
////            holder.camera_paper_library_view.setVisibility(View.GONE);
////        }
////        holder.camera_paper_library_item_iv.setImageResource(R.drawable.face_camera_select_no);
//        if (selectedPosition == position) {
//            holder.camera_paper_library_view.setVisibility(View.VISIBLE);
//        } else {
//            holder.camera_paper_library_view.setVisibility(View.GONE);
//        }
//
//        //显示封面
//        if (tiSticker == TiSticker.NO_STICKER) {
//            holder.camera_paper_library_item_iv.setImageResource(R.drawable.face_camera_select_no);
//        } else {
//            Glide.with(context)
//                    .load(stickerList.get(position).getThumb())
//                    .into(holder.camera_paper_library_item_iv);
//        }
//
//        //判断是否已经下载
//        if (tiSticker.isDownloaded()) {
//            holder.downloadIV.setVisibility(View.GONE);
//            holder.loadingIV.setVisibility(View.GONE);
//        } else {
//            //判断是否正在下载，如果正在下载，则显示加载动画
//            if (downloadingStickers.containsKey(tiSticker.getName())) {
//                holder.downloadIV.setVisibility(View.GONE);
//                holder.loadingIV.setVisibility(View.VISIBLE);
//                holder.startLoadingAnimation();
//            } else {
//                holder.downloadIV.setVisibility(View.VISIBLE);
//                holder.loadingIV.setVisibility(View.GONE);
//                holder.stopLoadingAnimation();
//            }
//        }
//
//        holder.itemView.setOnClickListener(v -> {
//
//            LogUtils.e("aab tiSticker.getUrl() = " + tiSticker.getUrl());
//            //如果没有下载，则开始下载到本地
//            if (!tiSticker.isDownloaded()) {
//
//                //如果已经在下载了，则不操作
//                if (downloadingStickers.containsKey(tiSticker.getName())) {
//                    return;
//                }
//
//                new DownloadTask.Builder(tiSticker.getUrl(), new File(TiSDK.getStickerPath(holder.itemView.getContext())))
//                        .setMinIntervalMillisCallbackProcess(30)
//                        .build()
//                        .enqueue(new DownloadListener2() {
//                            @Override
//                            public void taskStart(@NonNull DownloadTask task) {
//                                downloadingStickers.put(tiSticker.getName(), tiSticker.getUrl());
//                                handler.post(() -> notifyDataSetChanged());
//                            }
//
//                            @Override
//                            public void taskEnd(@NonNull DownloadTask task, @NonNull EndCause cause, @Nullable final Exception realCause) {
//                                if (cause == EndCause.COMPLETED) {
//                                    downloadingStickers.remove(tiSticker.getName());
//                                    File targetDir = new File(TiSDK.getStickerPath(holder.itemView.getContext()));
//                                    File file = task.getFile();
//                                    try {
//                                        //解压到贴纸目录
//                                        TiUtils.unzip(file, targetDir);
//                                        if (file != null) {
//                                            file.delete();
//                                        }
//
//                                        //修改内存与文件
//                                        tiSticker.setDownloaded(true);
//                                        tiSticker.stickerDownload(holder.itemView.getContext());
//
//                                        handler.post(() -> {
//                                            selectedPosition = position;
//                                            tiSDKManager.setSticker(tiSticker.getName());
//                                            notifyDataSetChanged();
//                                        });
//
//                                    } catch (Exception e) {
//                                        if (file != null) {
//                                            file.delete();
//                                        }
//                                    }
//                                } else {
//                                    downloadingStickers.remove(tiSticker.getName());
//
//                                    handler.post(() -> {
//                                        notifyDataSetChanged();
//                                        if (realCause != null) {
//                                            Toast.makeText(holder.itemView.getContext(), realCause.getMessage(), Toast.LENGTH_SHORT).show();
//                                        }
//                                    });
//                                }
//                            }
//                        });
//
////                    DLManager.getInstance(holder.itemView.getContext())
////                            .dlStart(tiSticker.getUrl(), new IDListener() {
////                                @Override
////                                public void onPrepare() {
////
////                                }
////
////                                @Override
////                                public void onStart(String fileName, String realUrl, int fileLength) {
////
////                                    downloadingStickers.put(tiSticker.getName(), tiSticker.getUrl());
////                                    handler.post(new Runnable() {
////                                        @Override
////                                        public void run() {
////                                            notifyDataSetChanged();
////                                        }
////                                    });
////                                }
////
////                                @Override
////                                public void onProgress(int progress) {
////
////                                }
////
////                                @Override
////                                public void onStop(int progress) {
////
////                                }
////
////                                @Override
////                                public void onFinish(File file) {
////                                    downloadingStickers.remove(tiSticker.getName());
////
////                                    File targetDir = new File(TiSDK.getStickerPath(holder.itemView.getContext()));
////                                    try {
////                                        //解压到贴纸目录
////                                        TiUtils.unzip(file, targetDir);
////                                        file.delete();
////
////                                        //修改内存与文件
////                                        tiSticker.setDownloaded(true);
////                                        tiSticker.stickerDownload(holder.itemView.getContext());
////
////                                        handler.post(new Runnable() {
////                                            @Override
////                                            public void run() {
////                                                selectedPosition = position;
////                                                tiSDKManager.setSticker(tiSticker.getName());
////                                                notifyDataSetChanged();
////                                            }
////                                        });
////
////                                    } catch (Exception e) {
////                                        file.delete();
////                                    }
////                                }
////
////                                @Override
////                                public void onError(final int status, final String error) {
////
////                                    downloadingStickers.remove(tiSticker.getName());
////
////                                    handler.post(new Runnable() {
////                                        @Override
////                                        public void run() {
////                                            notifyDataSetChanged();
////                                            if(status == 0){
////                                                ToastUtils.showShort(holder.itemView.getContext(), ToTwooApplication.baseContext.getResources().getString(R.string.error_net));
////                                            }
////
////                                        }
////                                    });
////                                }
////                            });
//
//            } else {
//                //如果已经下载了，则让贴纸生效
//                tiSDKManager.setSticker(tiSticker.getName());
//
//                //切换选中背景
//                selectedPosition = position;
//                notifyDataSetChanged();
//            }
//
//        });
//
//    }
//
//    @Override
//    public int getItemCount() {
//        return stickerList == null ? 0 : stickerList.size();
//    }
//
//    public static class ViewHolder extends RecyclerView.ViewHolder {
//        ConstraintLayout camera_paper_library_cl;
//        View camera_paper_library_view;
//        ImageView camera_paper_library_item_iv;
//        ImageView downloadIV;
//        ImageView loadingIV;
//
//        public ViewHolder(View itemView) {
//            super(itemView);
//            camera_paper_library_cl = itemView.findViewById(R.id.camera_paper_library_cl);
//            camera_paper_library_view = itemView.findViewById(R.id.camera_paper_library_view);
//            camera_paper_library_item_iv = itemView.findViewById(R.id.camera_paper_library_item_iv);
//            downloadIV = itemView.findViewById(R.id.downloadIV);
//            loadingIV = itemView.findViewById(R.id.loadingIV);
//        }
//
//        public void startLoadingAnimation() {
//            Animation animation = AnimationUtils.loadAnimation(itemView.getContext(), R.anim.loading_animation);
//            loadingIV.startAnimation(animation);
//        }
//
//        public void stopLoadingAnimation() {
//            loadingIV.clearAnimation();
//        }
//    }
//}
