package com.totwoo.totwoo.adapter;

import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.BrightMusicLightBean;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

public class BrightMusicAdapter extends RecyclerView.Adapter<BrightMusicAdapter.ViewHolder> {
    private BrightLightSelected brightLightSelected;
    private ArrayList<BrightMusicLightBean> musicLightBeans;
    private Context mContext;
    private int nowColorIndex;

    public BrightMusicAdapter(Context context) {
        this.mContext = context;
        nowColorIndex = PreferencesUtils.getInt(context, COLOR_VALUE, -1);
        nowColorIndex = Math.abs(nowColorIndex);
        musicLightBeans = new ArrayList<>();
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_colorful, R.drawable.notify_ray_colorful, R.drawable.bright_00_color, isSelectItem(0), false));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_pink, R.drawable.notify_ray_pink, R.drawable.bright_01_pink, isSelectItem(1), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_red, R.drawable.notify_ray_red, R.drawable.bright_02_red, isSelectItem(2), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_orange, R.drawable.notify_ray_orange, R.drawable.bright_03_orange, isSelectItem(3), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_yellow, R.drawable.notify_ray_yellow, R.drawable.bright_04_yellow, isSelectItem(4), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_green, R.drawable.notify_ray_green, R.drawable.bright_05_green, isSelectItem(5), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_cyan, R.drawable.notify_ray_cyan, R.drawable.bright_06_cyan, isSelectItem(6), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_blue, R.drawable.notify_ray_blue, R.drawable.bright_07_blue, isSelectItem(7), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_purple, R.drawable.notify_ray_purple, R.drawable.bright_08_purple, isSelectItem(8), true));
        musicLightBeans.add(new BrightMusicLightBean(R.string.bright_mode_white, R.drawable.notify_ray_white, R.drawable.bright_09_white, isSelectItem(9), true));
    }

    public void setOnLightClick(BrightLightSelected brightLightSelected) {
        this.brightLightSelected = brightLightSelected;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.bright_light_item, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.mContentCl.setBackgroundResource(musicLightBeans.get(position).getBgDrawableId());
        holder.mNameTv.setText(musicLightBeans.get(position).getNameId());
        if (musicLightBeans.get(position).isSelect()) {
            holder.mSelectIv.setVisibility(View.VISIBLE);
        } else {
            holder.mSelectIv.setVisibility(View.GONE);
        }
        if (musicLightBeans.get(position).isSelect() && musicLightBeans.get(position).isHasMusic() && isPlaying) {
            holder.mStatusIv.setVisibility(View.GONE);
            holder.mLottieAnimationView.setVisibility(View.VISIBLE);
            holder.mLottieAnimationView.playAnimation();
        } else if (musicLightBeans.get(position).isSelect() && musicLightBeans.get(position).isHasMusic() && !isPlaying) {
            holder.mStatusIv.setVisibility(View.VISIBLE);
            holder.mLottieAnimationView.setVisibility(View.GONE);
            holder.mLottieAnimationView.cancelAnimation();
        } else {
            holder.mStatusIv.setVisibility(View.GONE);
            holder.mLottieAnimationView.setVisibility(View.GONE);
        }
        if (position == musicLightBeans.size() - 1) {
            holder.mBottomView.setVisibility(View.VISIBLE);
        } else {
            holder.mBottomView.setVisibility(View.GONE);
        }
        holder.mContentCl.setOnClickListener(v -> {
            if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                ToastUtils.showShort(mContext, R.string.error_jewelry_connect);
                return;
            }

            if (!CommonUtils.jewelryFlashOpen(mContext)) {
                ToastUtils.showLong(mContext, R.string.flash_change_with_off);
                return;
            }

            setSelect(position);
            brightLightSelected.onItemClick(position);
        });
    }

    private void setSelect(int position) {
        for (int i = 0; i < musicLightBeans.size(); i++) {
            if (musicLightBeans.get(i).isSelect()) {
                BrightMusicLightBean brightMusicLightBean = musicLightBeans.get(i);
                brightMusicLightBean.setSelect(false);
                musicLightBeans.set(i, brightMusicLightBean);
            }
        }
        BrightMusicLightBean brightMusicLightBean = musicLightBeans.get(position);
        brightMusicLightBean.setSelect(true);
        musicLightBeans.set(position, brightMusicLightBean);
        notifyDataSetChanged();
    }

    private boolean isSelectItem(int position) {
        return position + 1 == nowColorIndex;
    }

    private boolean isPlaying = false;

    public void setIsPlaying(boolean isPlaying) {
        this.isPlaying = isPlaying;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return musicLightBeans == null ? 0 : musicLightBeans.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.bright_light_content_cl)
        ConstraintLayout mContentCl;
        @BindView(R.id.bright_light_name_tv)
        TextView mNameTv;
        @BindView(R.id.bright_light_select_iv)
        ImageView mSelectIv;
        @BindView(R.id.bright_light_status_iv)
        ImageView mStatusIv;
        @BindView(R.id.bright_light_bottom_view)
        View mBottomView;
        @BindView(R.id.bright_light_select_lav)
        LottieAnimationView mLottieAnimationView;

        public ViewHolder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    public interface BrightLightSelected {
        void onItemClick(int position);
    }
}
