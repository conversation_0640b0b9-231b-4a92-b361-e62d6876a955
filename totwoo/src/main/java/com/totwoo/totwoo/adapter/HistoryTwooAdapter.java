package com.totwoo.totwoo.adapter;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.TwooHistoryItem;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class HistoryTwooAdapter extends BaseQuickAdapter<TwooHistoryItem, BaseViewHolder> {

    private ArrayList<TwooHistoryItem> data;
    protected SimpleDateFormat format = new SimpleDateFormat("HH:mm", Locale.getDefault());
    private int partner_gender;

    public HistoryTwooAdapter( @Nullable List<TwooHistoryItem> data) {
        super(R.layout.history_message_item, data);
        this.data = (ArrayList<TwooHistoryItem>) data;
        ACache aCache = ACache.get(ToTwooApplication.baseContext);
        partner_gender = TextUtils.isEmpty(aCache.getAsString(CommonArgs.PARTNER_GENDER)) ? (1 - owner.getGender()) : Integer.valueOf(aCache.getAsString(CommonArgs.PARTNER_GENDER));
    }



    // BQ展示将错就错
    @Override
    protected void convert(BaseViewHolder helper, TwooHistoryItem item) {
        int position = helper.getLayoutPosition();
        // 设置日期分割线
        String dateText = getDateText(item.getCreateTime() * 1000L);
        String date = format.format(new Date(item.getCreateTime() * 1000L));
        String otherDate = date;
        if (item.getOtherTime() != 0)
            otherDate = format.format(new Date(item.getOtherTime() * 1000L));

        if (position == 1 || !TextUtils.equals(dateText, getDateText(data.get(position - 2).getCreateTime() * 1000L))) {
            helper.getView(R.id.message_date_line).setVisibility(View.VISIBLE);
        } else {
            helper.getView(R.id.message_date_line).setVisibility(View.GONE);
        }

        helper.setText(R.id.message_date_tv, dateText);

        boolean consonance = item.getConsonance() > 0;  //大于0表示一次心有灵犀
        if(consonance && TextUtils.equals(item.getSenderTotwoo_id(), ToTwooApplication.owner.getTotwooId())){
            helper.getView(R.id.message_send_cl).setVisibility(View.VISIBLE);
            helper.getView(R.id.message_receiver_cl).setVisibility(View.GONE);

            ImageView otherImage = helper.getView(R.id.message_send_other_image);
            ImageView meImage = helper.getView(R.id.message_send_me_image);

//            BitmapHelper.display(mContext, helper.getView(R.id.message_send_other_iv), getOtherHeadUrl());
            BitmapHelper.setHead(ToTwooApplication.baseContext, helper.getView(R.id.message_send_other_iv), getOtherHeadUrl(), partner_gender);
            otherImage.setImageResource(switchBQByContent(item.getOtherContent()));
            helper.setText(R.id.message_send_other_date,getOtherName() + " " + otherDate);

//            BitmapHelper.display(mContext, helper.getView(R.id.message_send_me_iv), getMyHeadUrl());
            BitmapHelper.setHead(ToTwooApplication.baseContext, helper.getView(R.id.message_send_me_iv), getMyHeadUrl(), owner.getGender());
            meImage.setImageResource(switchBQByContent(item.getContent()));
            helper.setText(R.id.message_send_me_date,getMyName() + " " + date);
        }else{
            helper.getView(R.id.message_receiver_cl).setVisibility(View.VISIBLE);
            helper.getView(R.id.message_send_cl).setVisibility(View.GONE);
            if(consonance){
                helper.getView(R.id.message_receiver_success).setVisibility(View.VISIBLE);
                helper.getView(R.id.message_receiver_other_iv).setVisibility(View.VISIBLE);
                helper.getView(R.id.message_receiver_other_image).setVisibility(View.VISIBLE);
                helper.getView(R.id.message_receiver_other_date).setVisibility(View.VISIBLE);
                helper.getView(R.id.message_receiver_me_iv).setVisibility(View.VISIBLE);
                helper.getView(R.id.message_receiver_me_image).setVisibility(View.VISIBLE);
                helper.getView(R.id.message_receiver_me_date).setVisibility(View.VISIBLE);
                ImageView otherImage = helper.getView(R.id.message_receiver_other_image);
                ImageView meImage = helper.getView(R.id.message_receiver_me_image);

//                BitmapHelper.display(mContext, helper.getView(R.id.message_receiver_other_iv), getOtherHeadUrl());
                BitmapHelper.setHead(ToTwooApplication.baseContext, helper.getView(R.id.message_receiver_other_iv), getOtherHeadUrl(), partner_gender);
                otherImage.setImageResource(switchBQByContent(item.getContent()));
                helper.setText(R.id.message_receiver_other_date,getOtherName() + " " + otherDate);

//                BitmapHelper.display(mContext, helper.getView(R.id.message_receiver_me_iv), getMyHeadUrl());
                BitmapHelper.setHead(ToTwooApplication.baseContext, helper.getView(R.id.message_receiver_me_iv), getMyHeadUrl(), owner.getGender());
                meImage.setImageResource(switchBQByContent(item.getOtherContent()));
                helper.setText(R.id.message_receiver_me_date,getMyName() + " " + date);
            }else{
                //我发送且没有心有灵犀的
                if(TextUtils.equals(item.getSenderTotwoo_id(), ToTwooApplication.owner.getTotwooId())){
                    helper.getView(R.id.message_receiver_other_iv).setVisibility(View.GONE);
                    helper.getView(R.id.message_receiver_other_image).setVisibility(View.GONE);
                    helper.getView(R.id.message_receiver_other_date).setVisibility(View.GONE);
                    helper.getView(R.id.message_receiver_me_iv).setVisibility(View.VISIBLE);
                    helper.getView(R.id.message_receiver_me_image).setVisibility(View.VISIBLE);
                    helper.getView(R.id.message_receiver_me_date).setVisibility(View.VISIBLE);
                }
                //对方发送且没有心有灵犀的
                else{
                    helper.getView(R.id.message_receiver_other_iv).setVisibility(View.VISIBLE);
                    helper.getView(R.id.message_receiver_other_image).setVisibility(View.VISIBLE);
                    helper.getView(R.id.message_receiver_other_date).setVisibility(View.VISIBLE);
                    helper.getView(R.id.message_receiver_me_iv).setVisibility(View.GONE);
                    helper.getView(R.id.message_receiver_me_image).setVisibility(View.GONE);
                    helper.getView(R.id.message_receiver_me_date).setVisibility(View.GONE);
                }
                helper.getView(R.id.message_receiver_success).setVisibility(View.GONE);
                ImageView otherImage = helper.getView(R.id.message_receiver_other_image);
                ImageView meImage = helper.getView(R.id.message_receiver_me_image);

//                BitmapHelper.display(mContext, helper.getView(R.id.message_receiver_other_iv), getOtherHeadUrl());
                BitmapHelper.setHead(ToTwooApplication.baseContext, helper.getView(R.id.message_receiver_other_iv), getOtherHeadUrl(), partner_gender);
                otherImage.setImageResource(switchBQByContent(item.getContent()));
                helper.setText(R.id.message_receiver_other_date,getOtherName() + " " + otherDate);

//                BitmapHelper.display(mContext, helper.getView(R.id.message_receiver_me_iv), getMyHeadUrl());
                BitmapHelper.setHead(ToTwooApplication.baseContext, helper.getView(R.id.message_receiver_me_iv), getMyHeadUrl(), owner.getGender());
                meImage.setImageResource(switchBQByContent(item.getContent()));
                helper.setText(R.id.message_receiver_me_date,getMyName() + " " + date);
            }

        }
    }

    private String getMyName() {
        return /*ToTwooApplication.owner.getNickName()*/ "";
    }

    private String getOtherName() {
//        return PreferencesUtils.getString(mContext, CoupleLogic.PAIRED_PERSON_NICK_NAME, "");
        return "";
    }

    private String getMyHeadUrl() {
        return ToTwooApplication.owner.getHeaderUrl();
    }

    private String getOtherHeadUrl() {
        return PreferencesUtils.getString(mContext, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "");
    }

    private String getDateText(long time) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        // 获得指定时间, 今天0点的差值
        long off = time - cal.getTimeInMillis();

        if (off >= 0) {
            return mContext.getString(R.string.today_upper_case);
        } else {
            SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
            return format.format(new Date(time));
        }
    }

    private int switchBQByContent(String content) {
        if (content == null || content.length() == 0)
            return R.drawable.bq_totwoo_big;
        else if (content.equals("1001")) //想你
            return R.drawable.bq_miss_big;
        else if (content.equals("1002"))    //亲亲
            return R.drawable.bq_kiss_big;
        else if (content.equals("1003"))
            return R.drawable.bq_pain_big;  //打你
        else if (content.equals("1004"))
            return R.drawable.bq_sorry_big; //对不起
        else if (content.equals("1006"))
            return R.drawable.bq_hurt_big;
        else if (content.equals("1009"))
            return R.drawable.bq_love_big;
        else
            return R.drawable.bq_totwoo_big;    //默认没有符合4个图标都，全部都是Totwoo
    }
}
