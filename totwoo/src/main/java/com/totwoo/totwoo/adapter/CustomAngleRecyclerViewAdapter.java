package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.ease.holder.BaseHolder;
import com.ease.model.BaseModel;
import com.totwoo.library.net.Constants;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ConstellationIndexBean;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.bean.HomePageIndexInfo;
import com.totwoo.totwoo.bean.PeriodStateBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.holder.CustomAPPHolder;
import com.totwoo.totwoo.holder.CustomCallHolder;
import com.totwoo.totwoo.holder.CustomCameraHolder;
import com.totwoo.totwoo.holder.CustomFlashHolder;
import com.totwoo.totwoo.holder.CustomFortuneHolder;
import com.totwoo.totwoo.holder.CustomMemoHolder;
import com.totwoo.totwoo.holder.CustomMemoryHolder;
import com.totwoo.totwoo.holder.CustomPeroidHolder;
import com.totwoo.totwoo.holder.CustomQianHolder;
import com.totwoo.totwoo.holder.CustomReminderPullHolder;
import com.totwoo.totwoo.holder.CustomSecretHolder;
import com.totwoo.totwoo.holder.CustomSedentaryHolder;
import com.totwoo.totwoo.holder.CustomSleepHolder;
import com.totwoo.totwoo.holder.CustomStepHolder;
import com.totwoo.totwoo.holder.CustomWaterHolder;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.widget.PullZoomRecyclerView;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Iterator;

public class CustomAngleRecyclerViewAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    public ArrayList<BaseHolder> mHolders;
    private ArrayList<CustomOrderBean> beans;
    private PullZoomRecyclerView mPullZoomRecyclerView;
    public static final int FROM_ANGEL = 1;
    public static final int FROM_MAGIC = 2;
    public static final int FROM_REMINDER = 3;
    private int current_type = 1;
    Context mContext;

    public CustomAngleRecyclerViewAdapter(Context context, PullZoomRecyclerView pullZoomRecyclerView, ArrayList<CustomOrderBean> beans, int from_type) {
        mContext = context;
        mHolders = new ArrayList<>();
        mPullZoomRecyclerView = pullZoomRecyclerView;
        this.current_type = from_type;

        // REMINDER 页面的情书已经上移到顶部 banner 位置, 需要移除.
        int itemStatus = ToTwooApplication.cacheData.getItem_status();
        Iterator<CustomOrderBean> it = beans.iterator();
        while (it.hasNext()) {
            CustomOrderBean bean = it.next();
            boolean shouldRemove = bean.getType() == NotifyUtil.SERCET_TYPE
                    || bean.getType() == NotifyUtil.APP_TYPE
                    || (bean.getType() == NotifyUtil.MEMO_TYPE)
                    || (!BleParams.isButtonBatteryJewelry() && bean.getType() == NotifyUtil.PERIOD_TYPE) // 仅恋恋系列保留 大姨妈 提醒
                    || (Apputils.systemLanguageIsOther(context) && bean.getType() == NotifyUtil.FORTUNE_TYPE)
                    || bean.getType() == NotifyUtil.MEMORY_TYPE
                    || bean.getType() == NotifyUtil.SLEEP_TYPE;

            // 额外的移除条件
            if (itemStatus == 1 && Constants.isHW &&
                    (bean.getType() == NotifyUtil.FORTUNE_TYPE || bean.getType() == NotifyUtil.QIAN_TYPE)) {
                shouldRemove = true;
            }
            //皓月去掉 健步生活，久坐,并且要把 CALL_TYPE置顶
            if (BleParams.isMWJewlery()) {
                if (bean.getType() == NotifyUtil.STEP_TYPE
                        || bean.getType() == NotifyUtil.SEDENTARY_TYPE
                        || bean.getType() == NotifyUtil.CALL_TYPE) {
                    shouldRemove = true;
                }
            }

            if (shouldRemove) {
                it.remove();
            }
        }

        // 添加闪光的条目
        if (current_type == FROM_ANGEL || current_type == FROM_MAGIC) {
            String jewName = PreferencesUtils.getString(mContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
            if (!TextUtils.isEmpty(jewName)) {
                beans.add(0, new CustomOrderBean(0, NotifyUtil.FLASH_TYPE, current_type == FROM_ANGEL ? 1 : 2));
            }
        }

        if (BleParams.isMWJewlery()) {
            // 添加 CALL 的条目
            beans.add(0, new CustomOrderBean(0, NotifyUtil.CALL_TYPE, current_type == FROM_ANGEL ? 1 : 2));
        }
        this.beans = beans;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (mHolders.isEmpty()) {
            mHolders.add(CustomReminderPullHolder.create(parent, mPullZoomRecyclerView, current_type));

            for (CustomOrderBean bean : beans) {
                addHolder(bean.getType(), parent);
            }
        }

        // 防止IndexOutOfBoundsException：确保viewType在有效范围内
        if (viewType < 0 || viewType >= mHolders.size()) {
            // 如果viewType超出范围，返回第一个holder作为默认值
            return mHolders.get(0);
        }

        return mHolders.get(viewType);
    }

    @Override
    public final int getItemViewType(int position) {
        return position;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        // 添加边界检查，防止IndexOutOfBoundsException
        if (position < 0 || position >= getItemCount()) {
            return;
        }

        BaseModel bm = null;

        // 确保holder不为null且是BaseHolder类型
        if (holder instanceof BaseHolder) {
            ((BaseHolder) holder).binding(bm);
        }
    }

    @Override
    public int getItemCount() {
        // 确保返回的数量与实际的holder数量一致
        // beans.size() + 1 是因为有一个CustomReminderPullHolder
        int count = beans.size() + 1;

        // 防止数据不一致导致的崩溃
        if (mHolders != null && !mHolders.isEmpty()) {
            // 如果holders已经创建，使用holders的实际大小
            count = Math.min(count, mHolders.size());
        }

        return count;
    }

    private void addHolder(int type, ViewGroup parent) {
        switch (type) {
            case NotifyUtil.FLASH_TYPE:
                mHolders.add(CustomFlashHolder.create(parent, current_type));
                break;
            case NotifyUtil.FORTUNE_TYPE:
                mHolders.add(CustomFortuneHolder.create(parent, current_type));
                break;
            case NotifyUtil.CAMERA_TYPE:
                mHolders.add(CustomCameraHolder.create(parent, current_type));
                break;
            case NotifyUtil.PERIOD_TYPE:
                mHolders.add(CustomPeroidHolder.create(parent, current_type));
                break;
            case NotifyUtil.QIAN_TYPE:
                mHolders.add(CustomQianHolder.create(parent, current_type));
                break;
            case NotifyUtil.CALL_TYPE:
                mHolders.add(CustomCallHolder.create(parent, current_type));
                break;
            case NotifyUtil.SEDENTARY_TYPE:
                mHolders.add(CustomSedentaryHolder.create(parent, current_type));
                break;
            case NotifyUtil.APP_TYPE:
                mHolders.add(CustomAPPHolder.create(parent, current_type));
                break;
            case NotifyUtil.WATER_TYPE:
                mHolders.add(CustomWaterHolder.create(parent, current_type));
                break;
            case NotifyUtil.STEP_TYPE:
                mHolders.add(CustomStepHolder.create(parent, current_type));
                break;
            case NotifyUtil.MEMO_TYPE:
                mHolders.add(CustomMemoHolder.create(parent, current_type));
                break;
            case NotifyUtil.SERCET_TYPE:
                mHolders.add(CustomSecretHolder.create(parent, current_type));
                break;
            case NotifyUtil.MEMORY_TYPE:
                mHolders.add(CustomMemoryHolder.create(parent, current_type));
                break;
            case NotifyUtil.SLEEP_TYPE:
                mHolders.add(CustomSleepHolder.create(parent, current_type));
                break;
        }
    }

    public static ConstellationIndexBean constellationIndexBeanHttpBaseBean;
    public static PeriodStateBean periodStateBean;
    public static HomePageIndexInfo homePageIndexInfo;
    public static boolean hasGreetingCardList = false;

    /**
     * 回收资源
     */
    public void recovery() {
        if (mHolders != null) {
            for (BaseHolder holder : mHolders) {
                EventBus.getDefault().unregister(holder);
                holder.unBind();
            }
        }
    }

    /**
     * 返回当前状态下, 是否包含某种特定类型的 Item
     *
     * @param itemType
     * @return
     */
    public boolean hasItemByType(int itemType) {
        if (beans != null) {
            for (CustomOrderBean bean : beans) {
                if (bean.getType() == itemType) {
                    return true;
                }
            }
        }
        return false;
    }

    public interface ItemTypeProvider {
        boolean hasItemByType(int itemTyp);
    }
}
