package com.totwoo.totwoo.adapter;

import android.graphics.Color;
import android.text.TextUtils;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.ColorBean;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.NotifyUtil;

import java.util.ArrayList;

/**
 * Created by totwoo on 2018/2/23.
 * 新版能自定义表情
 */

public class CustomColorLibraryAdapter extends BaseQuickAdapter<ColorLibraryBean, BaseViewHolder> {

    private String selectColor;
    private final ColorBean[] colors;

    /**
     *
     * @param selectColor 选中的颜色
     * @param count 一行展示几个
     * @param isLove 是否长条 love
     * @param isLoveNotify 爱的提醒  长条展示 保留9种颜色（与80 一样），82/82-01 只有5种颜色
     */
    public CustomColorLibraryAdapter(String selectColor, int count, boolean isLove,boolean isLoveNotify) {
        super(R.layout.custom_color_library_item);
        this.selectColor = selectColor;

        ArrayList<ColorLibraryBean> colorLibraryBeans = new ArrayList<>();


        if (BleParams.is82()) {
            if (isLoveNotify) {
                colors = NotifyUtil.colorsLoveNotify;
            } else {
                colors = NotifyUtil.colors82;
            }
        } else if (BleParams.is82_01()) {
            if (isLoveNotify) {
                colors = NotifyUtil.colorsLoveNotify;
            } else {
                colors = NotifyUtil.colors82_01;
            }
        } else if (BleParams.is83()) {
            colors = NotifyUtil.colors83;
        } else if (BleParams.is84()) {
            colors = NotifyUtil.colors84;
        } else if (BleParams.isCtJewlery()) {
            if (isLove) {
                colors = NotifyUtil.colorsCTLove;
            } else if (isLoveNotify) {
                colors = NotifyUtil.colors80;
            } else {
                colors = NotifyUtil.colorsCT;
            }
        } else {
            colors = NotifyUtil.colors80;
        }

        for (ColorBean color : colors) {
            ColorLibraryBean bean = new ColorLibraryBean();
            bean.setColor(color.getName());
            bean.setColorValue(color.getColor());
            if (TextUtils.equals(color.getName(), selectColor))
                bean.setSelect(true);
            else
                bean.setSelect(false);

            colorLibraryBeans.add(bean);
        }

        //如果colorLibraryBeans没选中的 ，默认选中第一个
        if (hasSelect(colorLibraryBeans) == null) {
            colorLibraryBeans.get(0).setSelect(true);
        }

        setNewData(colorLibraryBeans);
    }

    private ColorLibraryBean hasSelect(ArrayList<ColorLibraryBean> colorLibraryBeans) {
        for (ColorLibraryBean bean : colorLibraryBeans) {
            if (bean.isSelect()) {
                return bean;
            }
        }
        return null;
    }

    public void setSelectColor(String selectColor) {
        if (this.selectColor != selectColor) {
            this.selectColor = selectColor;
            for (ColorLibraryBean bean : getData()) {
                if (TextUtils.equals(bean.getColor(), selectColor))
                    bean.setSelect(true);
                else
                    bean.setSelect(false);
            }
            notifyDataSetChanged();
        }
    }


    public String getSelectColor() {
        return selectColor;
    }

    //获取选中的项目
    public ColorLibraryBean getSelectItem() {
        for (ColorLibraryBean bean : getData()) {
            if (bean.isSelect())
                return bean;
        }
        return null;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, ColorLibraryBean item) {
        ImageView imageView = helper.getView(R.id.color_library_iv);
        setSelectColor(imageView, item.getColor());
        helper.setVisible(R.id.color_ring, item.isSelect());
    }

    private void setSelectColor(ImageView imageView, String colorName) {
        int resId = NotifyUtil.getColorImageResId(colorName);
        imageView.setImageResource(resId);
        if (resId == R.drawable.custom_color_normal) {
            imageView.setColorFilter(Color.parseColor(NotifyUtil.getDisplayColorByColorName(colorName)));
        } else {
            imageView.setColorFilter(null);
        }
    }

}


