//package com.totwoo.totwoo.adapter;
//
//import android.content.Context;
//import androidx.recyclerview.widget.RecyclerView;
//
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//
//import com.totwoo.library.util.Apputils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.ToTwooApplication;
//
//import cn.tillusory.sdk.TiSDKManager;
//
///**
// * Created by totwoo on 2018/2/23.
// */
//
//public class FilterLibraryAdapter extends RecyclerView.Adapter<FilterLibraryAdapter.ViewHolder> {
//    private Context context;
//    private int calWidth;
//    private int count = 5;
//    private LinearLayout.LayoutParams layoutParams;
//    private LinearLayout.LayoutParams endlayoutParams;
////    private TiFilterEnum[] beans;
////    private ArrayList<TiFilterEnum> filterBeans;
//    private int selectedPosition = 0;
//    private TiSDKManager tiSDKManager;
//    private int[] ids = new int[]{
//            R.drawable.face_camera_filter_default,R.drawable.f9,R.drawable.f10,R.drawable.f11,R.drawable.f12,R.drawable.f13,R.drawable.f14,R.drawable.f15,
//            R.drawable.f16,R.drawable.f17,R.drawable.f18,R.drawable.f19,R.drawable.f20,R.drawable.f21,R.drawable.f22,R.drawable.f23, R.drawable.f24,
//            R.drawable.f25,R.drawable.f26,R.drawable.f27,R.drawable.f29,R.drawable.f30,R.drawable.f32, R.drawable.f34,R.drawable.f35,R.drawable.f36,
//            R.drawable.f39, R.drawable.f40,R.drawable.f41, R.drawable.f42,R.drawable.f43,R.drawable.f44,R.drawable.f45,R.drawable.f46,R.drawable.f47,
//            R.drawable.f48,R.drawable.f49,R.drawable.f1,R.drawable.f2,R.drawable.f3,R.drawable.f4,R.drawable.f6,R.drawable.f7, R.drawable.f8
//    };
//    //
//    public FilterLibraryAdapter(Context context,TiSDKManager tiSDKManager) {
//        this.context = context;
//        this.tiSDKManager = tiSDKManager;
//        calWidth = (Apputils.getScreenWidth(context) - Apputils.dp2px(context, 24)
//                - count * Apputils.dp2px(context, 40) - Apputils.dp2px(context, 20)) / count;
//        layoutParams = new LinearLayout.LayoutParams(calWidth,1);
//        endlayoutParams = new LinearLayout.LayoutParams(Apputils.dp2px(context,24),1);
////        beans = TiFilterEnum.values();
////        filterBeans = new ArrayList<>();
////        filterBeans.add(beans[0]);
////        filterBeans.add(beans[9]);
////        filterBeans.add(beans[10]);
////        filterBeans.add(beans[11]);
////        filterBeans.add(beans[12]);
////        filterBeans.add(beans[13]);
////        filterBeans.add(beans[14]);
////        filterBeans.add(beans[15]);
////        filterBeans.add(beans[16]);
////        filterBeans.add(beans[17]);
////        filterBeans.add(beans[18]);
////        filterBeans.add(beans[19]);
////        filterBeans.add(beans[20]);
////        filterBeans.add(beans[21]);
////        filterBeans.add(beans[22]);
////        filterBeans.add(beans[23]);
////        filterBeans.add(beans[24]);
////        filterBeans.add(beans[25]);
////        filterBeans.add(beans[26]);
////        filterBeans.add(beans[27]);
////        filterBeans.add(beans[29]);
////        filterBeans.add(beans[30]);
////        filterBeans.add(beans[32]);
////        filterBeans.add(beans[34]);
////        filterBeans.add(beans[35]);
////        filterBeans.add(beans[36]);
////        filterBeans.add(beans[39]);
////        filterBeans.add(beans[40]);
////        filterBeans.add(beans[41]);
////        filterBeans.add(beans[42]);
////        filterBeans.add(beans[43]);
////        filterBeans.add(beans[44]);
////        filterBeans.add(beans[45]);
////        filterBeans.add(beans[46]);
////        filterBeans.add(beans[47]);
////        filterBeans.add(beans[48]);
////        filterBeans.add(beans[49]);
////        filterBeans.add(beans[1]);
////        filterBeans.add(beans[2]);
////        filterBeans.add(beans[3]);
////        filterBeans.add(beans[4]);
////        filterBeans.add(beans[6]);
////        filterBeans.add(beans[7]);
////        filterBeans.add(beans[8]);
//    }
//
//    @Override
//    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
//        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.filter_library_item, parent, false);
//        ViewHolder viewHolder = new ViewHolder(view);
//        return viewHolder;
//    }
//
//    @Override
//    public void onBindViewHolder(final ViewHolder holder, final int position) {
//        if (selectedPosition == position) {
//            holder.filter_library_icon_iv.setImageResource(R.drawable.face_camera_filter_selected);
//        } else {
//            holder.filter_library_icon_iv.setImageDrawable(null);
//        }
//        holder.filter_library_icon_iv.setBackgroundResource(ids[position]);
//        if(Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)){
//            if (position == 0) {
//                holder.filter_library_name_tv.setText("原图");
//            }else{
////                holder.filter_library_name_tv.setText(filterBeans.get(position).getString(context));
//            }
//        }else {
//            if(position == 0){
//                holder.filter_library_name_tv.setText("Original");
//            }else{
//                holder.filter_library_name_tv.setText("F" + position);
//            }
//
//        }
//
//        holder.filter_library_icon_iv.setOnClickListener(v -> {
////            tiSDKManager.setFilterEnum(filterBeans.get(position));
//
//            selectedPosition = position;
//            notifyDataSetChanged();
//        });
//
////        if (position == filterBeans.size() - 1) {
////            holder.mView.setLayoutParams(endlayoutParams);
////        }else{
////            holder.mView.setLayoutParams(layoutParams);
////        }
////        if (position == 0) {
////            holder.mView_before.setVisibility(View.VISIBLE);
////        }else{
////            holder.mView_before.setVisibility(View.GONE);
////        }
//
//    }
//
//    @Override
//    public int getItemCount() {
////        return filterBeans == null ? 0 : filterBeans.size();
//        return 0;
//    }
//
//    public static class ViewHolder extends RecyclerView.ViewHolder {
//        ImageView filter_library_icon_iv;
//        TextView filter_library_name_tv;
//        View mView;
//        View mView_before;
//
//        public ViewHolder(View itemView) {
//            super(itemView);
//            filter_library_icon_iv = (ImageView) itemView.findViewById(R.id.filter_library_icon_iv);
//            filter_library_name_tv = (TextView) itemView.findViewById(R.id.filter_library_name_tv);
//            mView = itemView.findViewById(R.id.color_library_view);
//            mView_before = itemView.findViewById(R.id.color_library_view_before);
//        }
//    }
//}
