package com.totwoo.totwoo.adapter;

import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.BrightMusicBean;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

public class BrightMusicSelectAdapter extends RecyclerView.Adapter<BrightMusicSelectAdapter.ViewHolder> {
    private ArrayList<BrightMusicBean> brightMusicBeans;
    private BrightLightSelected brightLightSelected;
    private Context mContext;

    public BrightMusicSelectAdapter(Context context, ArrayList<BrightMusicBean> brightMusicBeans) {
        mContext = context;
        this.brightMusicBeans = brightMusicBeans;
    }

    public void setBrightMusicClick(BrightLightSelected brightLightSelected) {
        this.brightLightSelected = brightLightSelected;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.bright_music_item, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.mNameTv.setText(brightMusicBeans.get(position).getNameId());
        if (brightMusicBeans.get(position).isMusicSelect()) {
            holder.mNameTv.setTextColor(mContext.getResources().getColor(R.color.white));
            holder.mSelectIv.setVisibility(View.VISIBLE);
            holder.mContentCl.setBackgroundResource(getBackgroundResId());
        } else {
            holder.mNameTv.setTextColor(mContext.getResources().getColor(R.color.text_color_black_nomal));
            holder.mSelectIv.setVisibility(View.GONE);
            holder.mContentCl.setBackgroundResource(R.drawable.bright_music_gray);
        }
        if (position == brightMusicBeans.size() - 1) {
            holder.mBottomView.setVisibility(View.VISIBLE);
        } else {
            holder.mBottomView.setVisibility(View.GONE);
        }
        holder.mContentCl.setOnClickListener(v -> {
            if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                ToastUtils.showShort(mContext, CommonUtils.compantGetString(R.string.error_jewelry_connect));
                return;
            }

            if (!CommonUtils.jewelryFlashOpen(mContext)) {
                ToastUtils.showLong(mContext, CommonUtils.compantGetString(R.string.flash_change_with_off));
                return;
            }

            setSelect(position);
            brightLightSelected.onMusicItemClick(position);
        });
    }

    private int getBackgroundResId() {
        int nowColorIndex = PreferencesUtils.getInt(mContext, COLOR_VALUE, -1);
        nowColorIndex = Math.abs(nowColorIndex);
        int resId = 0;
        switch (nowColorIndex) {
            case 2:
                resId = R.drawable.bright_01_pink;
                break;
            case 3:
                resId = R.drawable.bright_02_red;
                break;
            case 4:
                resId = R.drawable.bright_03_orange;
                break;
            case 5:
                resId = R.drawable.bright_04_yellow;
                break;
            case 6:
                resId = R.drawable.bright_05_green;
                break;
            case 7:
                resId = R.drawable.bright_06_cyan;
                break;
            case 8:
                resId = R.drawable.bright_07_blue;
                break;
            case 9:
                resId = R.drawable.bright_08_purple;
                break;
            case 10:
                resId = R.drawable.bright_09_white;
                break;
        }
        return resId;
    }

    public void setSelect(int position) {
        for (int i = 0; i < brightMusicBeans.size(); i++) {
            if (brightMusicBeans.get(i).isMusicSelect()) {
                BrightMusicBean brightMusicBean = brightMusicBeans.get(i);
                brightMusicBean.setMusicSelect(false);
                brightMusicBeans.set(i, brightMusicBean);
            }
        }
        switch (position) {
            case 0:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_NOMUSIC);
                break;
            case 1:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_MUSICONE);
                break;
            case 2:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_MUSICTWO);
                break;
            case 3:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_MUSICTHREE);
                break;
            case 4:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_MUSICFOUR);
                break;
            case 5:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_MUSICFIVE);
                break;
            case 6:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_MUSICSIX);
                break;
            case 7:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET_MUSICSEVEN);
                break;

        }
        BrightMusicBean brightMusicBean = brightMusicBeans.get(position);
        brightMusicBean.setMusicSelect(true);
        brightMusicBeans.set(position, brightMusicBean);
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return brightMusicBeans == null ? 0 : brightMusicBeans.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.bright_music_content_cl)
        ConstraintLayout mContentCl;
        @BindView(R.id.bright_music_name_tv)
        TextView mNameTv;
        @BindView(R.id.bright_music_select_iv)
        ImageView mSelectIv;
        @BindView(R.id.bright_music_bottom_view)
        View mBottomView;

        public ViewHolder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    public interface BrightLightSelected {
        void onMusicItemClick(int position);
    }
}
