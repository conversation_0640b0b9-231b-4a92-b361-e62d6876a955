//package com.totwoo.totwoo.adapter;
//
//import android.content.Context;
//import android.support.annotation.NonNull;
//import android.support.v7.widget.RecyclerView;
//import android.view.ViewGroup;
//
//import com.ease.adapter.BaseAdapter;
//import com.ease.data.DataController;
//import com.ease.holder.BaseHolder;
//import com.ease.model.BaseModel;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.bean.holderBean.BrightSwitch;
//import com.totwoo.totwoo.bean.holderBean.HomeConstellationBean;
//import com.totwoo.totwoo.bean.holderBean.HomeSedentaryBean;
//import com.totwoo.totwoo.controller.HomeDataController;
//import com.totwoo.totwoo.holder.HomeBrightHolder;
//import com.totwoo.totwoo.holder.HomeConstellationHolder;
//import com.totwoo.totwoo.holder.HomeMemoryHolder;
//import com.totwoo.totwoo.holder.HomeQianHolder;
//import com.totwoo.totwoo.holder.HomeSecretHolder;
//import com.totwoo.totwoo.holder.HomeSedentaryHolder;
//import com.totwoo.totwoo.holder.HomeTotwooHolder;
//import com.totwoo.totwoo.holder.HomeYesNoHolder;
//import com.totwoo.totwoo.widget.PullZoomRecyclerView;
//
//import org.greenrobot.eventbus.EventBus;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// *
// * Created by huanggaowei on 16/4/6.
// */
//public class HomeRecyclerViewAdapter extends BaseAdapter<BaseModel> {
//    public List<BaseHolder> mHolders;
//
//    private PullZoomRecyclerView mPullZoomRecyclerView;
//
//    public HomeRecyclerViewAdapter(Context context, PullZoomRecyclerView pullZoomRecyclerView) {
//        super(context);
//        mHolders = new ArrayList<>();
//        mPullZoomRecyclerView = pullZoomRecyclerView;
//    }
//
//    @NonNull
//    @Override
//    public DataController<BaseModel> createDataController() {
//        return new HomeDataController(getContext());
//    }
//
//    @Override
//    public int getCommonType(int position) {
//        return position;
//    }
//
//    @Override
//    public RecyclerView.ViewHolder onCreateCommon(ViewGroup parent, int viewType) {
//        if (mHolders.size() == 0) {
//            mHolders.add(HomeTotwooHolder.create(parent, mPullZoomRecyclerView));
//            mHolders.add(HomeBrightHolder.create(parent, mPullZoomRecyclerView));
//            mHolders.add(HomeConstellationHolder.create(parent));
//            if (Apputils.systemLanguageIsChinese(getContext())){
//                mHolders.add(HomeQianHolder.create(parent));
//            }
//            mHolders.add(HomeYesNoHolder.create(parent));
//            mHolders.add(HomeMemoryHolder.create(parent));
//            mHolders.add(HomeSecretHolder.create(parent));
//        }
//        return mHolders.get(viewType);
//    }
//
//    @Override
//    public void onBindCommon(RecyclerView.ViewHolder holder, BaseModel item) {
//        ((BaseHolder)holder).binding(item);
//    }
//
//    /**
//     * 回收资源
//     */
//    public void recovery() {
//        if (mHolders != null) {
//            for (BaseHolder holder : mHolders) {
//                EventBus.getDefault().unregister(holder);
//            }
//        }
//    }
//}
