package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.TogetherProvinceBean;
import com.totwoo.totwoo.bean.TogetherSelectBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.databinding.TogetherCityItemBinding;
import com.totwoo.totwoo.databinding.TogetherPageItemBinding;
import com.totwoo.totwoo.databinding.TogetherProvinceItemBinding;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.HttpHelper;

import java.util.ArrayList;
import java.util.List;

import rx.Observer;

public class TogetherSelectAdapter extends RecyclerView.Adapter<TogetherSelectAdapter.ViewHolder> {
    private Context mContext;
    private ArrayList<TogetherSelectBean> selectedBeans;

    private TogetherProvinceAdapter togetherProvinceAdapter;
    private ArrayList<TogetherProvinceBean> provinceBeans;

    private TogetherProvinceAdapter togetherContinentAdapter;
    private ArrayList<TogetherProvinceBean> continentBeans;

    private TogetherCityAdapter togetherCityAdapter;
    private ArrayList<TogetherSelectBean> cityBeans;

    private TogetherCityAdapter togetherCountryAdapter;
    private ArrayList<TogetherSelectBean> countryBeans;

    private ACache aCache;
    private Gson gson;
    private static final String PROVINCE_DATA = "province_data";
    private static final String HOT_CITY_DATA = "hot_city_data";
    private static final String CONTINENT_DATA = "continent_data";
    private static final String HOT_COUNTRY_DATA = "hot_country_data";
    //    private static final int CACHE_TIME = 7 * 24 * 3600;
    private static final int CACHE_TIME = 60;

    public TogetherSelectAdapter(Context context, ArrayList<TogetherSelectBean> selectBeans) {
        this.mContext = context;
        this.selectedBeans = selectBeans;

        aCache = ACache.get(mContext);
        gson = new Gson();

        provinceBeans = new ArrayList<>();
        togetherProvinceAdapter = new TogetherProvinceAdapter(provinceBeans);
        getProvince();

        cityBeans = new ArrayList<>();
        togetherCityAdapter = new TogetherCityAdapter(cityBeans);
        getHotCity();

        continentBeans = new ArrayList<>();
        togetherContinentAdapter = new TogetherProvinceAdapter(continentBeans);
        getContinent();

        countryBeans = new ArrayList<>();
        togetherCountryAdapter = new TogetherCityAdapter(countryBeans);
        getCountry();
    }

    private OnCityClickListener onCityClickListener;

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TogetherPageItemBinding togetherPageItemBinding = TogetherPageItemBinding.inflate(LayoutInflater.from(mContext), parent, false);
        return new ViewHolder(togetherPageItemBinding.getRoot(), togetherPageItemBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        if (position == 0) {
            holder.togetherPageItemBinding.togetherProvinceRv.setLayoutManager(new LinearLayoutManager(mContext));
            holder.togetherPageItemBinding.togetherProvinceRv.setAdapter(togetherProvinceAdapter);

            holder.togetherPageItemBinding.togetherCityRv.setLayoutManager(new GridLayoutManager(mContext, 2));
            holder.togetherPageItemBinding.togetherCityRv.setAdapter(togetherCityAdapter);
        } else {
            holder.togetherPageItemBinding.togetherProvinceRv.setLayoutManager(new LinearLayoutManager(mContext));
            holder.togetherPageItemBinding.togetherProvinceRv.setAdapter(togetherContinentAdapter);

            holder.togetherPageItemBinding.togetherCityRv.setLayoutManager(new GridLayoutManager(mContext, 2));
            holder.togetherPageItemBinding.togetherCityRv.setAdapter(togetherCountryAdapter);
        }
    }

    @Override
    public int getItemCount() {
        return 2;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        public TogetherPageItemBinding togetherPageItemBinding;

        public ViewHolder(@NonNull View itemView, TogetherPageItemBinding togetherPageItemBinding) {
            super(itemView);
            this.togetherPageItemBinding = togetherPageItemBinding;
        }
    }

    public void setOnCityClickListener(OnCityClickListener onCityClickListener) {
        this.onCityClickListener = onCityClickListener;
    }

    public interface OnCityClickListener {
        void onClick(TogetherSelectBean togetherSelectBean, boolean isSelect);
    }

    public class TogetherProvinceAdapter extends RecyclerView.Adapter<TogetherProvinceAdapter.ViewHolder> {
        private ArrayList<TogetherProvinceBean> mProvinceBeans;

        public TogetherProvinceAdapter(List<TogetherProvinceBean> provinceBeans) {
            this.mProvinceBeans = (ArrayList<TogetherProvinceBean>) provinceBeans;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            TogetherProvinceItemBinding togetherProvinceItemBinding = TogetherProvinceItemBinding.inflate(LayoutInflater.from(mContext), parent, false);
            return new ViewHolder(togetherProvinceItemBinding.getRoot(), togetherProvinceItemBinding);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.togetherProvinceItemBinding.togetherProvinceName.setText(mProvinceBeans.get(position).getName());
            if (mProvinceBeans.get(position).isSelect()) {
                holder.togetherProvinceItemBinding.togetherProvinceName.setTextColor(mContext.getResources().getColor(R.color.color_main));
                holder.togetherProvinceItemBinding.togetherProvinceName.getPaint().setFakeBoldText(true);
            } else {
                holder.togetherProvinceItemBinding.togetherProvinceName.setTextColor(mContext.getResources().getColor(R.color.text_color_gray_7a));
                holder.togetherProvinceItemBinding.togetherProvinceName.getPaint().setFakeBoldText(false);
            }

            holder.togetherProvinceItemBinding.togetherProvinceCount.setText(mProvinceBeans.get(position).getSelectCount());
            if (mProvinceBeans.get(position).getSelectCount() > 0) {
                holder.togetherProvinceItemBinding.togetherProvinceCount.setVisibility(View.VISIBLE);
            } else {
                holder.togetherProvinceItemBinding.togetherProvinceCount.setVisibility(View.GONE);
            }

            if (position == mProvinceBeans.size() - 1) {
                holder.togetherProvinceItemBinding.togetherProvinceLine.setVisibility(View.GONE);
            } else {
                holder.togetherProvinceItemBinding.togetherProvinceLine.setVisibility(View.VISIBLE);
            }
        }

        @Override
        public int getItemCount() {
            return mProvinceBeans == null ? 0 : mProvinceBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            public TogetherProvinceItemBinding togetherProvinceItemBinding;

            public ViewHolder(@NonNull View itemView, TogetherProvinceItemBinding togetherProvinceItemBinding) {
                super(itemView);
                this.togetherProvinceItemBinding = togetherProvinceItemBinding;
            }
        }
    }

    public class TogetherCityAdapter extends RecyclerView.Adapter<TogetherCityAdapter.ViewHolder> {
        private ArrayList<TogetherSelectBean> mCityBeans;

        public TogetherCityAdapter(List<TogetherSelectBean> togetherSelectBeans) {
            mCityBeans = (ArrayList<TogetherSelectBean>) togetherSelectBeans;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            TogetherCityItemBinding togetherCityItemBinding = TogetherCityItemBinding.inflate(LayoutInflater.from(mContext), parent, false);
            return new ViewHolder(togetherCityItemBinding.getRoot(), togetherCityItemBinding);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            if (mCityBeans.get(position).isSelect()) {
                holder.togetherCityItemBinding.togetherCity.setTextColor(mContext.getResources().getColor(R.color.color_main));
                holder.togetherCityItemBinding.togetherCity.setBackground(mContext.getResources().getDrawable(R.drawable.shape_together_city_select));
            } else {
                holder.togetherCityItemBinding.togetherCity.setTextColor(mContext.getResources().getColor(R.color.text_color_gray_7a));
                holder.togetherCityItemBinding.togetherCity.setBackground(mContext.getResources().getDrawable(R.drawable.shape_together_city_unselected));
            }
            holder.togetherCityItemBinding.togetherCity.setOnClickListener(v -> {
                boolean contains = contains(mCityBeans.get(position).getId());
                if (contains) {
                    mCityBeans.remove(mCityBeans.get(position));
                    //TODO 减少省份或者大洲包含的数据
                } else {
                    TogetherSelectBean selectBean = mCityBeans.get(position);
                    selectBean.setSelect(true);
                    mCityBeans.add(selectBean);
                    //TODO 添加省份或者大洲包含的数据
                }
                //通知选择列表
                onCityClickListener.onClick(mCityBeans.get(position), !contains);
                //TODO 刷新各个列表的显示
            });
        }

        @Override
        public int getItemCount() {
            return mCityBeans == null ? 0 : mCityBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            public TogetherCityItemBinding togetherCityItemBinding;

            public ViewHolder(@NonNull View itemView, TogetherCityItemBinding togetherCityItemBinding) {
                super(itemView);
                this.togetherCityItemBinding = togetherCityItemBinding;
            }
        }
    }

    private boolean contains(int id) {
        if (selectedBeans == null || selectedBeans.size() == 0) {
            return false;
        }

        for (TogetherSelectBean togetherSelectBean : selectedBeans) {
            if (togetherSelectBean.getId() == id) {
                return true;
            }
        }
        return false;
    }

    private void getProvince() {
//        if(!TextUtils.isEmpty(aCache.getAsString(PROVINCE_DATA))){
//            String json = aCache.getAsString(PROVINCE_DATA);
//            provinceBeans = gson.fromJson(json,new TypeToken<List<TogetherProvinceBean>>(){}.getType());
//            togetherProvinceAdapter.notifyDataSetChanged();
//            return;
//        }
        LogUtils.e("aab getProvince");
        HttpHelper.footPrintService.getProvince()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherProvinceBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherProvinceBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            provinceBeans = (ArrayList<TogetherProvinceBean>) listHttpBaseBean.getData();
                            togetherProvinceAdapter.notifyDataSetChanged();
                            LogUtils.e("aab provinceBeans.size() = " + provinceBeans.size());
                            aCache.put(PROVINCE_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }

    private void getHotCity() {
        if (!TextUtils.isEmpty(aCache.getAsString(HOT_CITY_DATA))) {
            String json = aCache.getAsString(HOT_CITY_DATA);
            cityBeans = gson.fromJson(json, new TypeToken<List<TogetherSelectBean>>() {
            }.getType());
            togetherCityAdapter.notifyDataSetChanged();
            return;
        }
        HttpHelper.footPrintService.getHotCity()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherSelectBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherSelectBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            cityBeans = (ArrayList<TogetherSelectBean>) listHttpBaseBean.getData();
                            togetherCityAdapter.notifyDataSetChanged();
                            aCache.put(HOT_CITY_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }

    private void getCity(int pid, boolean fromChina) {

    }

    private void getContinent() {
        if (!TextUtils.isEmpty(aCache.getAsString(CONTINENT_DATA))) {
            String json = aCache.getAsString(CONTINENT_DATA);
            continentBeans = gson.fromJson(json, new TypeToken<List<TogetherProvinceBean>>() {
            }.getType());
            togetherContinentAdapter.notifyDataSetChanged();
            return;
        }
        HttpHelper.footPrintService.getContinent()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherProvinceBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherProvinceBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            continentBeans = (ArrayList<TogetherProvinceBean>) listHttpBaseBean.getData();
                            togetherContinentAdapter.notifyDataSetChanged();
                            aCache.put(CONTINENT_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }

    private void getCountry() {
        if (!TextUtils.isEmpty(aCache.getAsString(HOT_COUNTRY_DATA))) {
            String json = aCache.getAsString(HOT_COUNTRY_DATA);
            countryBeans = gson.fromJson(json, new TypeToken<List<TogetherSelectBean>>() {
            }.getType());
            togetherContinentAdapter.notifyDataSetChanged();
            return;
        }
        HttpHelper.footPrintService.getHot_country()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherSelectBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherSelectBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            countryBeans = (ArrayList<TogetherSelectBean>) listHttpBaseBean.getData();
                            togetherContinentAdapter.notifyDataSetChanged();
                            aCache.put(HOT_COUNTRY_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }
}
