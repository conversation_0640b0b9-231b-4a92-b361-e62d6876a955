package com.totwoo.totwoo.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.utils.NotifyUtil;

import java.util.ArrayList;

/**
 * Created by totwoo on 2018/9/17.
 */

public class CustomOrderAdapter extends RecyclerView.Adapter<CustomOrderAdapter.ViewHolder> {
    private ArrayList<CustomOrderBean> beans;
    private CustomerOrderClick customerOrderClick;

    public CustomOrderAdapter(ArrayList<CustomOrderBean> beans,CustomerOrderClick customerOrderClick){
        this.beans = beans;
        this.customerOrderClick = customerOrderClick;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.custom_order_item, parent, false);
        CustomOrderAdapter.ViewHolder viewHolder = new CustomOrderAdapter.ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        holder.tvName.setText(NotifyUtil.getNameByType(beans.get(position).getType()));
        if(beans.get(position).isSelect()){
            holder.ivMove.setVisibility(View.VISIBLE);
            holder.ivStatus.setImageResource(R.drawable.custom_order_item_delete);
        }else{
            holder.ivMove.setVisibility(View.GONE);
            holder.ivStatus.setImageResource(R.drawable.custom_order_item_add);
            holder.clContent.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    customerOrderClick.customItemClick(position);
                }
            });
        }
        holder.ivStatus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                customerOrderClick.customItemClick(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return beans == null ? 0 : beans.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder{
        ImageView ivStatus;
        TextView tvName;
        ImageView ivMove;
        ConstraintLayout clContent;
        public ViewHolder(View itemView) {
            super(itemView);
            ivStatus = itemView.findViewById(R.id.custom_order_item_status_iv);
            tvName = itemView.findViewById(R.id.custom_order_item_name_tv);
            ivMove = itemView.findViewById(R.id.custom_order_item_move_iv);
            clContent = itemView.findViewById(R.id.custom_order_item_content_cl);
        }
    }

    public interface CustomerOrderClick{
        void customItemClick(int position);
    }
}
