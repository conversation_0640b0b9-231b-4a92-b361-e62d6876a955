package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ease.adapter.BaseAdapter;
import com.ease.data.DataController;
import com.totwoo.totwoo.bean.holderBean.AppNotifyBean;
import com.totwoo.totwoo.controller.AppNotifyDataController;
import com.totwoo.totwoo.holder.AppNotifyListHolder;

/**
 * Created by huanggaowei on 16/8/25.
 */
public class AppNotifyAdapter extends BaseAdapter<AppNotifyBean> {

    private boolean isHide;


    public AppNotifyAdapter(Context context) {
        super(context);
    }

    @NonNull
    @Override
    public DataController<AppNotifyBean> createDataController() {
        return new AppNotifyDataController(getContext());
    }

    @Override
    public int getCommonType(int position) {
        return BaseAdapter.DEFAULT_COMMON_TYPE;
    }

    @Override
    public RecyclerView.ViewHolder onCreateCommon(ViewGroup parent, int viewType) {
        if (viewType == BaseAdapter.DEFAULT_COMMON_TYPE) {
            return AppNotifyListHolder.create(parent);
        }
        throw new IllegalArgumentException("the view type is wrong");
    }

    @Override
    public void onBindCommon(RecyclerView.ViewHolder holder, AppNotifyBean item) {
        if (holder instanceof AppNotifyListHolder) {
            ((AppNotifyListHolder) holder).binding(item);
            ((AppNotifyListHolder) holder).getRootView().setVisibility(isHide ? View.GONE : View.VISIBLE);
        }
    }

    public void hideAllItem(boolean hide) {
        this.isHide = hide;
    }
}
