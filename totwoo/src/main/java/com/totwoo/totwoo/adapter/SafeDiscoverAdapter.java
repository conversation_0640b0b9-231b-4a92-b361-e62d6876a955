package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.bean.SafeDiscoverInfo;

import java.util.List;

public class SafeDiscoverAdapter extends BaseQuickAdapter<SafeDiscoverInfo,BaseViewHolder> {
    private Context mContext;

    public SafeDiscoverAdapter(int layoutResId, @Nullable List data) {
        super(layoutResId, data);
    }

    public void setContext(Context context){
        this.mContext = context;
    }

    @Override
    protected void convert(BaseViewHolder helper, SafeDiscoverInfo item) {
        helper.setText(R.id.discover_item_text_tv,item.getTitle());
        View textBg = helper.getView(R.id.discover_item_text_bg);
        if(TextUtils.isEmpty(item.getTitle())){
            textBg.setVisibility(View.GONE);
        }else{
            textBg.setVisibility(View.VISIBLE);
        }
        ImageView imageView = helper.getView(R.id.discover_item_main_bg);
        imageView.setOnClickListener(v -> WebViewActivity.loadUrl(mContext, item.getLink(), false));
        RequestOptions options = new RequestOptions()
                .error(R.drawable.discover_safe_default);
        Glide.with(mContext).load(BitmapHelper.checkRealPath(item.getCover_url())).apply(options).into(imageView);
    }
}
