package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.WaterTimeBean;

import java.util.ArrayList;

/**
 * Created by totwoo on 2018/2/9.
 */

public class WaterTimeAdapter extends RecyclerView.Adapter<WaterTimeAdapter.ViewHolder>{
    private ArrayList<WaterTimeBean> beans;
    private Context context;
    private WaterTimeDrinkTimeClickListener waterTimeDrinkTimeClickListener;

    public WaterTimeAdapter(ArrayList<WaterTimeBean> beans,Context context) {
        this.beans = beans;
        this.context = context;
    }

    public void updateBeans(ArrayList<WaterTimeBean> beans) {
        this.beans = beans;
        notifyDataSetChanged();
    }

    public void setWaterTimeDrinkTimeListener(WaterTimeDrinkTimeClickListener waterTimeDrinkTimeClickListener){
        this.waterTimeDrinkTimeClickListener = waterTimeDrinkTimeClickListener;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.water_time_item,parent,false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        holder.mTimeCheckBox.setChecked(beans.get(position).isOpen());
        holder.mCountTv.setText(context.getString(getPositionTextStringId(position)));
        holder.mDrinkTimeTv.setText(beans.get(position).getDrinkTime());
//        holder.mDrinkTimeTv.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                waterTimeDrinkTimeClickListener.onClickItem(v,position);
//            }
//        });
        holder.mItemCl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                waterTimeDrinkTimeClickListener.onClickItem(v,position);
            }
        });
        holder.mTimeCheckBox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                waterTimeDrinkTimeClickListener.onClickCheckBox(v,position);
            }
        });
        if (position == beans.size() - 1) {
            holder.mBottomView.setVisibility(View.GONE);
        }else{
            holder.mBottomView.setVisibility(View.VISIBLE);
        }
    }



    @Override
    public int getItemCount() {
        return beans == null ? 0 : beans.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        CheckBox mTimeCheckBox;
        TextView mCountTv;
        TextView mDrinkTimeTv;
        ConstraintLayout mItemCl;
        View mBottomView;

        public ViewHolder(View itemView) {
            super(itemView);
            mTimeCheckBox = (CheckBox) itemView.findViewById(R.id.water_time_item_checkbox);
            mCountTv = (TextView) itemView.findViewById(R.id.water_time_count_tv);
            mDrinkTimeTv = (TextView) itemView.findViewById(R.id.water_time_drink_time_tv);
            mItemCl = (ConstraintLayout) itemView.findViewById(R.id.water_time_item);
            mBottomView = itemView.findViewById(R.id.water_time_item_bottom);
        }
    }

    public int getPositionTextStringId(int position){
        int resId = 0;
        switch (position){
            case 0:
                resId = R.string.water_time_count_1;
                break;

            case 1:
                resId = R.string.water_time_count_2;
                break;

            case 2:
                resId = R.string.water_time_count_3;
                break;

            case 3:
                resId = R.string.water_time_count_4;
                break;

            case 4:
                resId = R.string.water_time_count_5;
                break;

            case 5:
                resId = R.string.water_time_count_6;
                break;

            case 6:
                resId = R.string.water_time_count_7;
                break;

            case 7:
                resId = R.string.water_time_count_8;
                break;
        }
        return resId;
    }

    public interface WaterTimeDrinkTimeClickListener{
        void onClickItem(View view,int position);
        void onClickCheckBox(View view,int position);
    }
}
