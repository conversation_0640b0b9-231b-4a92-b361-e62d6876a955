package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.ble.BleParams;

import java.util.ArrayList;

/**
 * Created by totwoo on 2018/2/23.
 */

public class ColorLibraryAdapter extends RecyclerView.Adapter<ColorLibraryAdapter.ViewHolder> {
    private String selectColor;
    private Context context;
    private final ArrayList<ColorLibraryBean> colorLibraryBeans;
    private final View.OnClickListener colorItemClickListener;
    private final LinearLayout.LayoutParams layoutParams;
    private final LinearLayout.LayoutParams endlayoutParams;
    private final String[] colors;

    //
    public ColorLibraryAdapter(String selectColor, Context context, View.OnClickListener colorItemClickListener) {
        this.selectColor = selectColor;
        this.context = context;
        this.colorItemClickListener = colorItemClickListener;
        int count = BleParams.needRemovePYWOColor() ? 5 : 6;
        int calWidth = (Apputils.getScreenWidth(context) - 2 * Apputils.dp2px(context, 24)
                - count * Apputils.dp2px(context, 42)) / (count - 1);
        layoutParams = new LinearLayout.LayoutParams(calWidth, 1);
        endlayoutParams = new LinearLayout.LayoutParams(Apputils.dp2px(context, 24), 1);
        colorLibraryBeans = new ArrayList<>();

        colors = BleParams.needRemovePYWOColor() ?
                new String[]{"RED", "GREEN", "BLUE", "PURPLE", "CYAN"}
                : new String[]{"RED", "PINK", "YELLOW", "GREEN", "BLUE", "PURPLE", "WHITE", "CYAN", "ORANGE"};


        for (String color : colors) {
            ColorLibraryBean bean = new ColorLibraryBean();
            bean.setColor(color);
            if (TextUtils.equals(color, selectColor))
                bean.setSelect(true);
            else
                bean.setSelect(false);

            colorLibraryBeans.add(bean);
        }
    }

    public int getIndex(String color) {
        int length = colors.length;
        for (int i = 0; i < length; i++) {
            if (TextUtils.equals(color, colors[i])) {
                return i;
            }
        }
        return 0;
    }


    public void setSelectColor(String selectColor) {
        if (this.selectColor != selectColor) {
            this.selectColor = selectColor;
            for (ColorLibraryBean bean : colorLibraryBeans) {
                if (TextUtils.equals(bean.getColor(), selectColor))
                    bean.setSelect(true);
                else
                    bean.setSelect(false);
            }
            notifyDataSetChanged();
        }
    }

    public String getSelectColor() {
        return selectColor;
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.color_library_item, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        switch (colorLibraryBeans.get(position).getColor()) {
            case "RED":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_red));
                holder.mcolorIv.setTag("RED");
                break;
            case "PINK":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_pink));
                holder.mcolorIv.setTag("PINK");
                break;
            case "YELLOW":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_yellow));
                holder.mcolorIv.setTag("YELLOW");
                break;
            case "BLUE":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_blue));
                holder.mcolorIv.setTag("BLUE");
                break;
            case "GREEN":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_green));
                holder.mcolorIv.setTag("GREEN");
                break;
            case "PURPLE":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_purple));
                holder.mcolorIv.setTag("PURPLE");
                break;
            case "WHITE":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_white));
                holder.mcolorIv.setTag("WHITE");
                break;
            case "CYAN":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_cyan));
                holder.mcolorIv.setTag("CYAN");
                break;
            case "ORANGE":
                holder.mcolorIv.setBackground(context.getResources().getDrawable(R.drawable.notify_ray_orange));
                holder.mcolorIv.setTag("ORANGE");
                break;
        }

        if (colorLibraryBeans.get(position).isSelect()) {
            holder.mcolorIv.setImageResource(R.drawable.notify_ray_checked);
        } else {
            holder.mcolorIv.setImageDrawable(null);
        }

        holder.mcolorIv.setOnClickListener(colorItemClickListener);

        if (position == colorLibraryBeans.size() - 1) {
            holder.mView.setLayoutParams(endlayoutParams);
        } else {
            holder.mView.setLayoutParams(layoutParams);
        }
        if (position == 0) {
            holder.mView_before.setVisibility(View.VISIBLE);
        } else {
            holder.mView_before.setVisibility(View.GONE);
        }

    }

    @Override
    public int getItemCount() {
        return colorLibraryBeans == null ? 0 : colorLibraryBeans.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView mcolorIv;
        View mView;
        View mView_before;

        public ViewHolder(View itemView) {
            super(itemView);
            mcolorIv = (ImageView) itemView.findViewById(R.id.color_library_iv);
            mView = itemView.findViewById(R.id.color_library_view);
            mView_before = itemView.findViewById(R.id.color_library_view_before);
        }
    }
}
