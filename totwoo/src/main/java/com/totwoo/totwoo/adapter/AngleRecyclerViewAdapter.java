package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.ease.holder.BaseHolder;
import com.ease.model.BaseModel;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.bean.ConstellationIndexBean;
import com.totwoo.totwoo.bean.PeriodStateBean;
import com.totwoo.totwoo.holder.AngelHomeConstellationPullHolder;
import com.totwoo.totwoo.holder.HomeCallHolder;
import com.totwoo.totwoo.holder.HomeCameraHolder;
import com.totwoo.totwoo.holder.HomeCustomHolder;
import com.totwoo.totwoo.holder.HomePeroidHolder;
import com.totwoo.totwoo.holder.HomeSedentaryHolder;
import com.totwoo.totwoo.holder.HomeWaterTimeHolder;
import com.totwoo.totwoo.widget.PullZoomRecyclerView;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;

@Deprecated
public class AngleRecyclerViewAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    public ArrayList<BaseHolder> mHolders;
    private PullZoomRecyclerView mPullZoomRecyclerView;
    private ConstellationIndexBean bean;
    private PeriodStateBean periodStateBean;
    Context mContext;

    public AngleRecyclerViewAdapter(Context context, PullZoomRecyclerView pullZoomRecyclerView) {
        mContext = context;
        mHolders = new ArrayList<>();

        mPullZoomRecyclerView = pullZoomRecyclerView;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (mHolders.size() == 0) {
            mHolders.add(AngelHomeConstellationPullHolder.create(parent,mPullZoomRecyclerView));
            mHolders.add(HomeCameraHolder.create(parent));
            mHolders.add(HomePeroidHolder.create(parent));
            mHolders.add(HomeSedentaryHolder.create(parent));
            mHolders.add(HomeWaterTimeHolder.create(parent));
            mHolders.add(HomeCallHolder.create(parent));
            mHolders.add(HomeCustomHolder.create(parent));
        }

        LogUtils.e("onBindViewHolder:" + viewType);

        // 防止IndexOutOfBoundsException：确保viewType在有效范围内
        if (viewType < 0 || viewType >= mHolders.size()) {
            // 如果viewType超出范围，返回第一个holder作为默认值
            return mHolders.get(0);
        }

        return mHolders.get(viewType);
    }

    public void bindConstellationHolder(ConstellationIndexBean bean) {
        this.bean = bean;
    }

    public void bindPeriodHolder(PeriodStateBean periodStateBean) {
        this.periodStateBean = periodStateBean;
    }

    @Override
    public final int getItemViewType(int position) {
        return position;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        // 添加边界检查，防止IndexOutOfBoundsException
        if (position < 0 || position >= getItemCount()) {
            return;
        }

        BaseModel bm = null;

        if (position == 0)
            bm = bean;

        if(position == 2){
            bm = periodStateBean;
        }

        // 确保holder不为null且是BaseHolder类型
        if (holder instanceof BaseHolder) {
            ((BaseHolder) holder).binding(bm);
        }
    }

    @Override
    public int getItemCount() {
        int num = 7;

        return num;
    }

    /**
     * 回收资源
     */
    public void recovery() {
        if (mHolders != null) {
            for (BaseHolder holder : mHolders) {
                EventBus.getDefault().unregister(holder);
                holder.unBind();
            }
        }
    }
}
