package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.StringUtils;

import java.text.Collator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;

public class CitySelectAdapter extends BaseAdapter {

	private Context context;

	private Map<String, String> mapData;

	private ArrayList<String> listData;

	public CitySelectAdapter(Context context) {
		this.context = context;
	}

	public Map<String, String> getMapData() {
		return mapData;
	}

	/**
	 * 
	 * @param mapData
	 * @param grouping
	 *            是否需要根据首字母分组显示
	 */
	public void setMapData(Map<String, String> mapData, boolean grouping) {
		this.mapData = mapData;
		listData = new ArrayList<String>();
		for (String string : mapData.values()) {
			listData.add(string);
		}
		// 按首字母排序 并且在集合指定位置添加tag

		Collections.sort(listData, Collator.getInstance(Locale.CHINESE));
		if (grouping) {
			for (int i = 0; i < listData.size(); i++) {
				String sortKey;
				String sortKey2;
				// 判断是否跟上一个元素首字母相同 不同则加tag
				if (i == 0
						|| !StringUtils.getSortKey(listData.get(i)).equals(
								sortKey2 = StringUtils.getSortKey(listData
										.get(i - 1)))) {
					listData.add(i, StringUtils.getSortKey(listData.get(i)));
					i++;
				}
			}
		}
		notifyDataSetChanged();
	}

	@Override
	public int getCount() {

		return listData == null ? 0 : listData.size();
	}

	@Override
	public Object getItem(int position) {
		return listData.get(position - 1);
	}

	@Override
	public long getItemId(int position) {
		return position;
	}

	@Override
	public boolean isEnabled(int position) {
		if (Arrays.asList(ConfigData.Alphabets)
				.contains(listData.get(position))) {
			LogUtils.i("isEnabled", position + "");
			return false;
		}

		return super.isEnabled(position);
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {
		// 判断是否是tag
		if (Arrays.asList(ConfigData.Alphabets)
				.contains(listData.get(position))) {
			convertView = View.inflate(context, R.layout.select_city_tag, null);
			TextView select_city_item_tag = (TextView) convertView
					.findViewById(R.id.select_city_item_tag);
			select_city_item_tag.setText(listData.get(position));
		} else {
			if (convertView == null
					|| convertView.getId() == R.id.select_city_tag) {
				convertView = View.inflate(context, R.layout.luanuage_lv_item,
						null);
			}
			((TextView) convertView).setText(listData.get(position));
		}

		return convertView;

	}
}
