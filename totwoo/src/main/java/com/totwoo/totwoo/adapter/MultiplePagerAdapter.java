package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.totwoo.totwoo.R;

import java.util.ArrayList;

/**
 * Created by xinyoulingxi on 2017/5/12.
 */

public class MultiplePagerAdapter extends PagerAdapter
{
    private ArrayList<View> mList;
    private LayoutInflater layoutInflater;

    public MultiplePagerAdapter(Context context)
    {
        super();
        this.mList = new ArrayList<>();
        layoutInflater = LayoutInflater.from(context);
        View view1 = layoutInflater.inflate(R.layout.image_pager_item, null);
        ((ImageView) view1.findViewById(R.id.pagerIv)).setImageResource(R.drawable.home_bright_holder_bg);
        View view2 = layoutInflater.inflate(R.layout.image_pager_item, null);
//        ((ImageView) view2.findViewById(R.id.pagerIv)).setImageResource(R.drawable.home_bright_holder_bg_male);
        mList.add(view1);
        mList.add(view2);
    }

    @Override
    public int getCount() {
        return mList.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    /**
     * 页面宽度所占ViewPager测量宽度的权重比例，默认为1
     */
    @Override
    public float getPageWidth(int position) {
        return (float) 0.8;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        ((ViewPager) container).removeView(view);
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position)
    {
        ((ViewPager) container).addView(mList.get(position));
        // 自己实现
        return mList.get(position);
    }
}
