package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.WeekItemBean;

import java.util.ArrayList;

/**
 * Created by totwoo on 2018/2/23.
 */

public class WeekSelectAdapter extends RecyclerView.Adapter<WeekSelectAdapter.ViewHolder> {
    private ArrayList<WeekItemBean> weekItemBeans;
    private String[] weeks;

    //
    public WeekSelectAdapter(String selectWeek,Context context) {
        weeks = context.getResources().getStringArray(R.array.week_name_select);
        weekItemBeans = new ArrayList<>();
        int weeksize = weeks.length;
        for (int i = 0; i < weeksize; i++) {
            WeekItemBean bean = new WeekItemBean();
            bean.setIndex(i+1);
            bean.setWeekName(weeks[i]);
            bean.setSelect(false);
            weekItemBeans.add(bean);
        }
        ArrayList<Integer> integers = transToInts(selectWeek);
        if(integers!=null && integers.size() > 0){
            for(Integer integer: integers){
                weekItemBeans.get(integer -1).setSelect(true);
            }
        }
    }

    private ArrayList<Integer> transToInts(String target){
        ArrayList<Integer> integers = new ArrayList<>();
        if(TextUtils.isEmpty(target) || TextUtils.isEmpty(target.trim())){
            return integers;
        }else if(!target.contains(",")){
            if(target.equals("0")){
                return integers;
            }
            integers.add(Integer.valueOf(target));
            return integers;
        }else{
            String strs[] = target.split(",");
            for(String string : strs){
                integers.add(Integer.valueOf(string));
            }
            return integers;
        }
    }

    //获取的选中的数，并且拼接
    public String getSelectWeek() {
        if(weekItemBeans.size() > 0){
            StringBuffer buffer = new StringBuffer();
            for(WeekItemBean bean:weekItemBeans){
                if(bean.isSelect()){
                    buffer.append(bean.getIndex());
                    buffer.append(",");
                }
            }
            return buffer.length() == 0 ? "0" :buffer.substring(0,buffer.length() - 1);
        }else{
            return "0";
        }
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.week_select_item, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if(weekItemBeans.get(position).isSelect())
            holder.mSelectCb.setChecked(true);
        else
            holder.mSelectCb.setChecked(false);

        holder.mItemCl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                weekItemBeans.get(position).setSelect(!weekItemBeans.get(position).isSelect());
                notifyDataSetChanged();
            }
        });

        holder.mTitleIv.setText(weekItemBeans.get(position).getWeekName());

    }

    @Override
    public int getItemCount() {
        return weekItemBeans == null ? 0 : weekItemBeans.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView mTitleIv;
        CheckBox mSelectCb;
        ConstraintLayout mItemCl;

        public ViewHolder(View itemView) {
            super(itemView);
            mTitleIv = (TextView) itemView.findViewById(R.id.week_select_title);
            mSelectCb = (CheckBox) itemView.findViewById(R.id.week_select_cb);
            mItemCl = (ConstraintLayout) itemView.findViewById(R.id.week_select_layout);
        }
    }
}
