//package com.totwoo.totwoo.adapter;
//
//import android.content.Context;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//
//import com.totwoo.library.util.Apputils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.bean.BeautyStyleBean;
//import com.totwoo.totwoo.utils.CommonArgs;
//
//import java.util.ArrayList;
//
//import androidx.annotation.NonNull;
//import androidx.recyclerview.widget.RecyclerView;
//import cn.tillusory.sdk.TiSDKManager;
//
//public class BeautyStyleAdapter extends RecyclerView.Adapter<BeautyStyleAdapter.ViewHolder> {
//    private Context context;
//    private TiSDKManager tiSDKManager;
//    private int calWidth;
//    private int count = 5;
//    private LinearLayout.LayoutParams layoutParams;
//    private LinearLayout.LayoutParams endlayoutParams;
//    private ArrayList<BeautyStyleBean> beans;
//    private int currentIndex;
//    private BeautySelectListener beautySelectListener;
//
//    public BeautyStyleAdapter(Context context, TiSDKManager tiSDKManager) {
//        this.context = context;
//        this.tiSDKManager = tiSDKManager;
//        calWidth = (Apputils.getScreenWidth(context) - Apputils.dp2px(context, 24)
//                - count * Apputils.dp2px(context, 40) - Apputils.dp2px(context, 20)) / count;
//        layoutParams = new LinearLayout.LayoutParams(calWidth, 1);
//        endlayoutParams = new LinearLayout.LayoutParams(Apputils.dp2px(context, 24), 1);
//        beans = new ArrayList<>();
//        beans.add(new BeautyStyleBean(0, R.drawable.face_camera_select_no, R.string.face_camera_original, 0, false));
//        beans.add(new BeautyStyleBean(1, R.drawable.face_camera_eye, R.string.face_camera_re_eye, CommonArgs.DEFAULT_RE_EYE, false));
//        beans.add(new BeautyStyleBean(2, R.drawable.face_camera_face, R.string.face_camera_cheeks, CommonArgs.DEFAULT_CHEEKS, false));
//        beans.add(new BeautyStyleBean(3, R.drawable.face_camera_jaw, R.string.face_camera_chin, CommonArgs.DEFAULT_JAW, false));
//        beans.add(new BeautyStyleBean(4, R.drawable.face_camera_nose, R.string.face_camera_narrow, CommonArgs.DEFAULT_NOSE, false));
//        beans.add(new BeautyStyleBean(5, R.drawable.face_camera_mouth, R.string.face_camera_mouth, CommonArgs.DEFAULT_MOUTH, false));
//        beans.add(new BeautyStyleBean(6, R.drawable.face_camera_forhead, R.string.face_camera_forehead, CommonArgs.DEFAULT_FORHEAD, false));
//        beans.add(new BeautyStyleBean(7, R.drawable.face_camera_tooth, R.string.face_camera_teeth, CommonArgs.DEFAULT_TOOTH, false));
//    }
//
//    @NonNull
//    @Override
//    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
//        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.filter_library_item, parent, false);
//        return new ViewHolder(view);
//    }
//
//    @Override
//    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
//        holder.filter_library_icon_iv.setImageResource(beans.get(position).getIconId());
//        holder.filter_library_name_tv.setText(beans.get(position).getNameId());
//        if(beans.get(position).isSelect()){
//            holder.filter_library_icon_iv.setBackgroundResource(R.drawable.face_camera_select);
//        }else{
//            holder.filter_library_icon_iv.setBackground(null);
//        }
//
//        holder.filter_library_icon_iv.setOnClickListener(v -> {
//            for (BeautyStyleBean bean : beans) {
//                bean.setSelect(false);
//            }
//            BeautyStyleBean bean = beans.get(position);
//            bean.setSelect(true);
//            beans.set(position,bean);
//            currentIndex = position;
//            if(position == 0){
//                tiSDKManager.setEyeMagnifying(0);
//                beans.get(1).setProgress(0);
//                tiSDKManager.setChinSlimming(0);
//                beans.get(2).setProgress(0);
//                tiSDKManager.setJawTransforming(50);
//                beans.get(3).setProgress(50);
//                tiSDKManager.setNoseMinifying(0);
//                beans.get(4).setProgress(0);
//                tiSDKManager.setMouthTransforming(50);
//                beans.get(5).setProgress(50);
//                tiSDKManager.setForeheadTransforming(50);
//                beans.get(6).setProgress(50);
//                tiSDKManager.setTeethWhitening(0);
//                beans.get(7).setProgress(0);
//            }
//            beautySelectListener.onBeautySelect(position,beans.get(position).getProgress());
//            notifyDataSetChanged();
//        });
//
//        if (position == beans.size() - 1) {
//            holder.mView.setLayoutParams(endlayoutParams);
//        } else {
//            holder.mView.setLayoutParams(layoutParams);
//        }
//        if (position == 0) {
//            holder.mView_before.setVisibility(View.VISIBLE);
//        } else {
//            holder.mView_before.setVisibility(View.GONE);
//        }
//    }
//    //无、大眼、瘦脸、下巴、瘦鼻、嘴型、额头、美牙
//    public void setProgress(int progress){
//        switch (currentIndex){
//            case 0:
//                break;
//            case 1:
//                tiSDKManager.setEyeMagnifying(progress);
//                break;
//            case 2:
//                tiSDKManager.setChinSlimming(progress);
//                break;
//            case 3:
//                tiSDKManager.setJawTransforming(progress);
//                break;
//            case 4:
//                tiSDKManager.setNoseMinifying(progress);
//                break;
//            case 5:
//                tiSDKManager.setMouthTransforming(progress);
//                break;
//            case 6:
//                tiSDKManager.setForeheadTransforming(progress);
//                break;
//            case 7:
//                tiSDKManager.setTeethWhitening(progress);
//                break;
//        }
//        beans.get(currentIndex).setProgress(progress);
//    }
//
//    public void setBeautySelectListener(BeautySelectListener beautySelectListener){
//        this.beautySelectListener = beautySelectListener;
//    }
//
//    @Override
//    public int getItemCount() {
//        return beans == null ? 0 : beans.size();
//    }
//
//    public static class ViewHolder extends RecyclerView.ViewHolder {
//        ImageView filter_library_icon_iv;
//        TextView filter_library_name_tv;
//        View mView;
//        View mView_before;
//
//        public ViewHolder(View itemView) {
//            super(itemView);
//            filter_library_icon_iv = itemView.findViewById(R.id.filter_library_icon_iv);
//            filter_library_name_tv = itemView.findViewById(R.id.filter_library_name_tv);
//            mView = itemView.findViewById(R.id.color_library_view);
//            mView_before = itemView.findViewById(R.id.color_library_view_before);
//        }
//    }
//
//    public interface BeautySelectListener{
//        void onBeautySelect(int index,int progress);
//    }
//}
