package com.totwoo.totwoo.adapter;

import android.content.Context;
import android.os.Parcelable;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.PagerAdapter;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.CustomAngelFragment;
import com.totwoo.totwoo.fragment.CustomMagicFragment;
import com.totwoo.totwoo.fragment.CustomReminderFragment;
import com.totwoo.totwoo.fragment.LoveFragment;
import com.totwoo.totwoo.fragment.MeFragment;
import com.totwoo.totwoo.fragment.MemoryFragment;
import com.totwoo.totwoo.fragment.RemindFragment;
import com.totwoo.totwoo.fragment.WishFragment;
import com.totwoo.totwoo.utils.PreferencesUtils;

/**
 * Created by lixingmao on 2017/3/30.
 */

public class HomePageAdapter extends PagerAdapter implements SubscriberListener {
    public static final String TAG = "HomePageAdapter";
    public static final String TAGTEMP = "HomePageAdapter TEMP";
    private final FragmentManager mFragmentManager;
    private FragmentTransaction mCurTransaction = null;
    private Fragment mCurrentPrimaryItem = null;
    private int currentCount;

    public BaseFragment[] fragmentsData;
    private Context context;

    public HomePageAdapter(Context context, FragmentManager fm, int count) {
        LogUtils.e(TAGTEMP);
        InjectUtils.injectOnlyEvent(this);
        mFragmentManager = fm;
        fragmentsData = new BaseFragment[getCount()];
        this.context = context;
        this.currentCount = count;
    }

    @Override
    public void startUpdate(ViewGroup container) {
        if (container.getId() == View.NO_ID) {
            LogUtils.e(TAG + " container.getId() == View.NO_ID");
            throw new IllegalStateException("ViewPager with adapter " + this + " requires a view id");
        }
    }

    @Override
    public int getItemPosition(Object object) {
        LogUtils.e(TAGTEMP);
        return POSITION_NONE;
    }

    public BaseFragment getItem(int position) {
        String jewName = PreferencesUtils.getString(context, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//        LogUtils.e(HomePageAdapter.TAG + "jewName = " + jewName);

        if (fragmentsData[position] == null) {
            LogUtils.e(TAG + " fragmentsData[position] = " + position);
            switch (position) {
                case 0:
                    if (BleParams.isMemoryJewelry())
                        fragmentsData[position] = new MemoryFragment();
                    else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
                        fragmentsData[position] = new CustomAngelFragment();
//                        fragmentsData[position] = new AngelFragment();
                    else if(BleParams.isMWJewlery())
                        fragmentsData[position] = new CustomReminderFragment();
                    else if(jewName.equals(BleParams.JEWELRY_BLE_NAME_SC) || jewName.equals(BleParams.JEWELRY_BLE_NAME_WL))
//                        fragmentsData[position] = new LuckyFragment();
                        fragmentsData[position] = new WishFragment();
                    else
                        fragmentsData[position] = new LoveFragment();
                    break;
                case 1:
                    if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
                        fragmentsData[position] = new LoveFragment();
                    else if(BleParams.isMWJewlery())
//                        fragmentsData[position] = new HeartFragment();
                        fragmentsData[position] = new RemindFragment();
                    else
                        fragmentsData[position] = new CustomMagicFragment();
                    break;
                case 2:
                    fragmentsData[position] = new MeFragment();
                    break;
            }
        }

        return fragmentsData[position];
    }

    @Override
    public int getCount() {
        return 3;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        LogUtils.e(TAGTEMP);
        if (mCurTransaction == null) {
            LogUtils.e(TAG + " mCurTransaction == null");
            mCurTransaction = mFragmentManager.beginTransaction();
        }
        final long itemId = getItemId(position);
        // Do we already have this fragment?
        LogUtils.e(TAG + " containerId = " + currentCount);
        String name = makeFragmentName(container.getId(), itemId);
        LogUtils.e(TAG + " name = " + name);
        Fragment fragment = mFragmentManager.findFragmentByTag(name);
        if (fragment != null) {
            LogUtils.e(TAG + " fragment != null");
            mCurTransaction.show(fragment);
        } else {
            fragment = getItem(position);
            LogUtils.e(TAG + " fragment == null");
            mCurTransaction.add(container.getId(), fragment, makeFragmentName(container.getId(), itemId)).show(fragment);
        }

        if (fragment != mCurrentPrimaryItem) {
            fragment.setMenuVisibility(false);
            fragment.setUserVisibleHint(false);
        }

        return fragment;
    }
    @EventInject(eventType = S.E.E_HOMEPAGE_RESET, runThread = TaskType.UI)
    public void onResetHomepage(EventData data) {
        LogUtils.e(TAG + " onResetHomepage");
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        LogUtils.e(TAGTEMP);
        if (mCurTransaction == null) {
            mCurTransaction = mFragmentManager.beginTransaction();
        }
        mCurTransaction.hide((Fragment) object);
    }

    @Override
    public void setPrimaryItem(ViewGroup container, int position, Object object) {
        Fragment fragment = (Fragment) object;
        if (fragment != mCurrentPrimaryItem) {
            if (mCurrentPrimaryItem != null) {
                mCurrentPrimaryItem.setMenuVisibility(false);
                mCurrentPrimaryItem.setUserVisibleHint(false);
            }

            if (fragment != null) {
                fragment.setMenuVisibility(true);
                fragment.setUserVisibleHint(true);
            }

            mCurrentPrimaryItem = fragment;
        }
    }

    @Override
    public void finishUpdate(ViewGroup container) {
        try {
            if (mCurTransaction != null) {
                mCurTransaction.commitNowAllowingStateLoss();
                mCurTransaction = null;
                mFragmentManager.executePendingTransactions();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return ((Fragment) object).getView() == view;
    }

    @Override
    public Parcelable saveState() {
        LogUtils.e(TAGTEMP);
        return null;
    }

    @Override
    public void restoreState(Parcelable state, ClassLoader loader) {
        LogUtils.e(TAGTEMP);
    }

    /**
     * Return a unique identifier for the item at the given position.
     * <p>
     * <p>The default implementation returns the given position.
     * Subclasses should override this method if the positions of items can change.</p>
     *
     * @param position Position within this adapter
     * @return Unique identifier for the item at position
     */
    public long getItemId(int position) {
        return position;
    }

    private String makeFragmentName(int viewId, long id) {
        return "android:switcher:" + viewId + ":" + id + "containerId = " + currentCount;
    }

    public void recycle() {
        LogUtils.e(TAGTEMP);
        mCurrentPrimaryItem = null;

        for (int i = 0; i < fragmentsData.length; i++) {
            fragmentsData[i] = null;
        }
        fragmentsData = null;
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}