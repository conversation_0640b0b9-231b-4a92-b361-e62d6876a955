package com.totwoo.totwoo.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.JewelrySelectItemBean;
import com.totwoo.totwoo.widget.OnRecyclerItemClickListener;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

public class JewelrySelectAdapter extends RecyclerView.Adapter<JewelrySelectAdapter.ViewHolder> {
    private SelectItemClickListener selectItemClickListener;
//    private ArrayList<JewelrySelectItemBean> beans;
    private ArrayList<ArrayList<JewelrySelectItemBean>> allItems;
    private int spanCount;
    private Context context;


    public JewelrySelectAdapter(Context context, int spanCount, ArrayList<ArrayList<JewelrySelectItemBean>> allItems) {
        this.context = context;
        this.spanCount = spanCount;
        this.allItems = allItems;

    }

    public void setItemClick(SelectItemClickListener selectItemClickListener) {
        this.selectItemClickListener = selectItemClickListener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jewelry_select_adapter, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        JewelryItemAdapter jewelryItemAdapter = new JewelryItemAdapter(allItems.get(position));
        holder.mSelectRv.setAdapter(jewelryItemAdapter);
        holder.mSelectRv.setLayoutManager(new GridLayoutManager(context,spanCount));
        holder.mSelectRv.addOnItemTouchListener(new OnRecyclerItemClickListener(holder.mSelectRv) {
            @Override
            public void onItemClick(RecyclerView.ViewHolder vh) {
                selectItemClickListener.onClick(allItems.get(position).get(vh.getLayoutPosition()));
            }

            @Override
            public void onItemLongClick(RecyclerView.ViewHolder vh) {
                selectItemClickListener.onClick(allItems.get(position).get(vh.getLayoutPosition()));
            }
        });
    }

    @Override
    public int getItemCount() {
        return allItems != null ? allItems.size() : 0;
    }

    protected class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.jewelry_select_adapter_rv)
        RecyclerView mSelectRv;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    public class JewelryItemAdapter extends RecyclerView.Adapter<JewelryItemAdapter.ViewHolder> {
        private ArrayList<JewelrySelectItemBean> beans;
        private JewelryItemAdapter(ArrayList<JewelrySelectItemBean> beans){
            this.beans = beans;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jewelry_select_item, parent, false);
            return new ViewHolder(view);
        }

        @SuppressLint("ClickableViewAccessibility")
        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.mItemIv.setImageResource(beans.get(position).getImageResourceId());
            holder.mItemTv.setText(beans.get(position).getName());
            holder.mItemIv.setOnTouchListener((v, event) -> {
                switch (event.getAction()){
                    case MotionEvent.ACTION_DOWN:
                    case MotionEvent.ACTION_MOVE:
                        holder.mItemIv.setBackground(context.getResources().getDrawable(R.drawable.shape_circle_green_8));
                        break;
                    case MotionEvent.ACTION_CANCEL:
                    case MotionEvent.ACTION_UP:
                        holder.mItemIv.setBackground(context.getResources().getDrawable(R.drawable.shape_circle_ee_8));
                        break;
                }
                return true;
            });
        }

        @Override
        public int getItemCount() {
            return beans != null ? beans.size() : 0;
        }

        protected class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.jewelry_select_item_iv)
            ImageView mItemIv;
            @BindView(R.id.jewelry_select_item_tv)
            TextView mItemTv;
            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    public interface SelectItemClickListener {
        void onClick(JewelrySelectItemBean item);
    }
}
