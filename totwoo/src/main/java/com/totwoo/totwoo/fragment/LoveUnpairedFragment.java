package com.totwoo.totwoo.fragment;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.Utils;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.RoundRectOutlineProvider;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.CoupleRequestInfoActivity;
import com.totwoo.totwoo.activity.SelectCoupleActivity;
import com.totwoo.totwoo.bean.ContactsBean;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.databinding.LoveManageItemBinding;
import com.totwoo.totwoo.databinding.LoveUnpairedLayoutBinding;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.ViewUtil;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomProgressBarDialog;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

/**
 * 未配对状态下首页的画面, 单独抽离出来做为一个独立的 Fragment
 */
public class LoveUnpairedFragment extends BaseFragment {
    private CoupleLogic mCoupleLogic;
    private CustomProgressBarDialog progressBar;
    private LoveUnpairedLayoutBinding binding;
    /**
     * 配对历史数据
     */
    private ArrayList<ContactsBean> contactsBeans;

    private ContactsBean requestedBean;
    private boolean isPlayedShow = false;

    public static LoveUnpairedFragment newInstance() {
        return new LoveUnpairedFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LoveUnpairedLayoutBinding.inflate(inflater, container, false);


        EdgeToEdgeUtils.setupBottomInsetsForLoveHomeAct(binding.getRoot());


        mCoupleLogic = new CoupleLogic(getContext());

        CommonUtils.setStateBar(getActivity(), false);

        initListener();

        refreshJewState();
        refreshCoupleListData();

///        if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.LOVE_TIP_CONNECT_CLOSE, false)) {
//            binding.loveUnpairedTipConnect.setVisibility(View.VISIBLE);
//            showAndPlayAnim();
//        }

        return binding.getRoot();
    }

    private void initListener() {
        binding.loveUnpairedButtonCl.setOnClickListener(v -> {
            if (getActivity() != null) {
                if (requestedBean != null) {
                    startActivity(new Intent(getActivity(), CoupleRequestInfoActivity.class)
                            .putExtra(CoupleRequestInfoActivity.EXTRA_COUPLE_REQUESTED_BEAN, requestedBean));
                } else {
                    startActivity(new Intent(getActivity(), SelectCoupleActivity.class));
                }

                getActivity().overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
            }
        });
        binding.loveRequestCancel.setOnClickListener(v -> hideRequestMessageItem());

        binding.loveUnpaiedTipConnectTv.setOnClickListener(v -> CommonUtils.jumpToJewList(getContext()));

        binding.loveUnpairedMainIv.setOutlineProvider(new RoundRectOutlineProvider(Apputils.dp2px(Utils.getApp(), 20)));
        binding.loveUnpairedMainIv.setClipToOutline(true);

        // 重新调整主图高度, 使页面默认状态能充满全屏
        try {
            binding.loveUnpairedMainIv.post(new Runnable() {
                @Override
                public void run() {
                    if (binding ==null ||binding.getRoot() == null || binding.loveUnpairedMainIv == null || binding.loveUnpairedMainIv.getLayoutParams() == null || Utils.getApp() ==null) {
                        return;
                    }
                    int per = Apputils.dp2px(Utils.getApp(), 260);

                    if (binding.getRoot().getHeight() > per) {
                        binding.loveUnpairedMainIv.getLayoutParams().height = binding.getRoot().getHeight() - per - SizeUtils.dp2px(76);
                        binding.loveUnpairedMainIv.requestLayout();
                    }
                    binding.loveUnpairedMainIv.setOutlineProvider(new RoundRectOutlineProvider(Apputils.dp2px(Utils.getApp(), 20)));
                    binding.loveUnpairedMainIv.setClipToOutline(true);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


        binding.loveUnpaiedTipConnectCloseIv.setOnClickListener(v -> {
            PreferencesUtils.put(getContext(), CommonArgs.LOVE_TIP_CONNECT_CLOSE, true);
//            goneAndPlayAnim();
        });
    }

    private void hideRequestMessageItem() {
        View[] views;
        views = new View[]{binding.loveRequestClickLl, binding.loveRequestInfoLl, binding.loveRequestHeadIcon};
        // 执行动画
        ViewUtil.showParallaxAnim(getContext(), views, 100, R.anim.home_message_out, new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                binding.loveRequestLayout.setVisibility(View.GONE);

//                if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED
//                        && !PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.LOVE_TIP_CONNECT_CLOSE, false)
//                        && !PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.LOVE_STATUS_SINGLE, false)) {
//                    binding.loveUnpairedTipConnect.setVisibility(View.VISIBLE);
//                    showAndPlayAnim();
//                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
    }

    /**
     * 展示首饰连接的提示条
     */
    private void showAndPlayAnim() {
        if (!isPlayedShow && binding.loveUnpairedTipConnect.getHeight() > 0) {
            isPlayedShow = true;
            binding.loveUnpairedTipConnect.setVisibility(View.VISIBLE);
            binding.loveUnpairedTipConnectLottie.playAnimation();

            ObjectAnimator translationY = ObjectAnimator.ofFloat(binding.loveUnpairedTipConnect, "translationY", -binding.loveUnpairedTipConnect.getHeight(), 0);
            AnimatorSet animatorSet = new AnimatorSet();  //组合动画
            animatorSet.playTogether(translationY); //设置动画
            animatorSet.setDuration(500);  //设置动画时间
            animatorSet.start(); //
        }
    }

    /**
     * 隐藏首饰连接的提示条
     */
    private void goneAndPlayAnim() {
        ObjectAnimator translationY = ObjectAnimator.ofFloat(binding.loveUnpairedTipConnect, "translationY", 0, -binding.loveUnpairedTipConnect.getHeight());
        AnimatorSet animatorSet = new AnimatorSet();  //组合动画
        animatorSet.playTogether(translationY); //设置动画
        animatorSet.setDuration(500);  //设置动画时间
        animatorSet.start(); //
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                binding.loveUnpairedTipConnect.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        binding.loveUnpairedTipConnectLottie.pauseAnimation();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // 清理 View 的 post 操作
        if (binding != null && binding.loveUnpairedMainIv != null) {
            binding.loveUnpairedMainIv.removeCallbacks(null);
        }

        // 清理 CoupleLogic
        if (mCoupleLogic != null) {
            mCoupleLogic = null;
        }

        // 清理进度对话框
        if (progressBar != null && progressBar.isShowing()) {
            try {
                progressBar.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
            progressBar = null;
        }

        // 清理数据引用
        contactsBeans = null;
        requestedBean = null;
    }

    /**
     * 联网获取列表数据
     */
    private void refreshCoupleListData() {
        RequestParams param = HttpHelper.getBaseParams(true);
        param.addFormDataPart("page", 1);
        param.addFormDataPart("perpage", 1000);
        HttpRequest.get(
                HttpHelper.URL_COUPLE_LIST, param,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
                        // 隐藏进度框
                        showProgressBar(false);

                        parsingPairedContacts(s);
                        updateListUI();
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        ToastUtils.showLong(getContext(),
                                R.string.error_net);
                        // 隐藏进度框
                        showProgressBar(false);
                    }
                });

    }

    private void updateListUI() {
        if (getContext() == null) {
            return;
        }
        binding.loveUnpairManageLayout.removeAllViews();
        if (contactsBeans == null || contactsBeans.isEmpty()) {
            binding.loveUnpairManageTitle.setVisibility(View.GONE);
            binding.loveUnpairManageLayout.addView(binding.loveUnpairListEmptyTitle);
            binding.loveUnpairManageLayout.addView(binding.loveUnpairListEmptyInfo);
        } else {
            binding.loveUnpairManageTitle.setVisibility(View.VISIBLE);

            for (int i = 0, contactsBeansSize = contactsBeans.size(); i < contactsBeansSize; i++) {
                ContactsBean contactsBean = contactsBeans.get(i);
                binding.loveUnpairManageLayout.addView(genItemViewFromData(contactsBean));
                View line = new View(getContext());
                line.setBackgroundColor(0xFFF4F4F4);

                if (i != contactsBeans.size() - 1) {
                    binding.loveUnpairManageLayout.addView(line, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 1));
                }
            }
        }
    }

    private View genItemViewFromData(ContactsBean bean) {
        @NonNull LoveManageItemBinding itemBinding = LoveManageItemBinding.inflate(LayoutInflater.from(getContext()));

        // 头像, 优先显示网络
        if (!TextUtils.isEmpty(bean.getHeadUrl())) {
            BitmapHelper.display(this, itemBinding.loveManageItemIv,
                    bean.getHeadUrl());
        } else {
            BitmapHelper.display(this, itemBinding.loveManageItemIv, R.drawable.default_head_yellow);
        }

        itemBinding.getRoot().setOnLongClickListener(v -> {
            showDelDialog(bean);
            return true;
        });

        // 用户姓名, 如果为空, 显示用户Id
        if (!TextUtils.isEmpty(bean.getName())) {
            itemBinding.loveManageItemNameTv.setText(bean.getName());
        } else {
            itemBinding.loveManageItemNameTv.setText(bean.getPhoneNumber());
        }
        itemBinding.loveManageItemStatusTv.setTextSize(Apputils.systemLanguageIsChinese(getContext()) ? 14 : 12);

        switch (bean.getCoupleShip()) {
            case CoupleLogic.COUPLE_STATE_APART:
                itemBinding.loveManageItemInfoTv.setText(R.string.once_paired);
                itemBinding.loveManageItemStatusTv.setText(R.string.request_again);
                // 曾经配对状态, 点击提交申请
                itemBinding.loveManageItemStatusTv.setOnClickListener(v -> sendRequest(bean));

                break;
            case CoupleLogic.COUPLE_STATE_PAIRED:
                itemBinding.loveManageItemInfoTv.setText(R.string.paired);
                itemBinding.loveManageItemInfoTv.setTextColor(getResources().getColor(
                        R.color.text_color_golden));
                itemBinding.loveManageItemStatusTv.setText(R.string.apart_paired);

                // 已配对状态, 点击解除配对, 成功之后跳转介绍页
                itemBinding.loveManageItemStatusTv.setOnClickListener(v -> mCoupleLogic.apartCouple(ToTwooApplication.owner.getPairedId(), success -> {
                    if (success) {
                        bean.setCoupleShip(CoupleLogic.COUPLE_STATE_APART);
                        updateListUI();
                    }
                }));

                break;
            // 待回复状态, 点击回复同意(目前不能拒绝)
            case CoupleLogic.COUPLE_STATE_REPLY:
                itemBinding.loveManageItemInfoTv.setText(R.string.want_pair);
                itemBinding.loveManageItemStatusTv.setText(R.string.agree);
                itemBinding.loveManageItemStatusTv.setOnClickListener(v -> {
                    mCoupleLogic.replyRequest(bean.getTalkId(), success -> {
                        if (success) {
                            // 回复成功, 跳转心有灵犀详情页
//                            Intent intent = new Intent(getContext(),
//                                    LoveSpacePinkActivity.class);
//                            startActivity(intent);
//                            requireActivity().getSupportFragmentManager().beginTransaction().hide(this).commit();
                            // 配对成功的逻辑
                            //调用父类fragent  onshow 方法
                            LoveFragment fragment = (LoveFragment) getParentFragment();
                            if (fragment != null && !fragment.isDetached()) {
                                fragment.onShow();
                            }
                            //弹出恭喜你弹窗
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOVE_HINT_TOTWOO, null);
                        } else {
                            // 刷新列表
                            refreshCoupleListData();
                        }
                    });
                });

                break;
            // 请求状态, 点击取消请求
            case CoupleLogic.COUPLE_STATE_REQUEST:
                itemBinding.loveManageItemInfoTv.setText(R.string.wait_reply);
                itemBinding.loveManageItemStatusTv.setText(R.string.cancel_request);
                itemBinding.loveManageItemStatusTv.setOnClickListener(v -> mCoupleLogic.cancelRequest(bean, success -> {
                    // 取消成功, 重新刷新列表
                    refreshCoupleListData();
                }));

                break;
        }
        return itemBinding.getRoot();
    }

    /**
     * 解析服务端 Json 数据
     *
     * @param data
     */
    private void parsingPairedContacts(String data) {
        if (contactsBeans != null) {
            contactsBeans.clear();
        } else {
            contactsBeans = new ArrayList<>();
        }

        // 清理记录发送请求状态,
        requestedBean = null;

        if (data == null) {
            return;
        }

        JSONArray array = null;
        try {
            array = new JSONArray(data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (array == null) {
            return;
        }

        for (int i = 0; i < array.length(); i++) {
            JSONObject obj = array.optJSONObject(i);
            if (obj != null) {
                // 解析用户数据
                ContactsBean con = null;

                int state = obj.optInt("coupleShip");
                // 解析状态
                if (state == 1) {
                    con = new ContactsBean();
                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_REQUEST);
                    // 记录发送请求状态, 用于后续页面使用.
                    requestedBean = con;
                } else if (state == 3) {
                    con = new ContactsBean();
                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_PAIRED);

//                    ToTwooApplication.owner
//                            .setPairedId(obj.optString("talkId"));
                } else if (state == 4) {
                    con = new ContactsBean();
                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_APART);
                } else if (state == 2) {
                    con = new ContactsBean();
                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_REPLY);
                }

                if (con == null) {
                    continue;
                }

                con.setTalkId(obj.optString("talkId"));

                JSONObject info = obj
                        .optJSONObject("userinfo");
                if (info != null) {
                    con.setHeadUrl(info
                            .optString("head_portrait"));
                    con.setName(info
                            .optString("nick_name"));

                    con.setSpecific_id(info.optString("totwoo_id"));
                    con.setPhoneNumber(info.optString("mobilephone"));
                }

                contactsBeans.add(con);
            }
        }
    }

    private void showDelDialog(final ContactsBean contactsBean) {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(getContext());
        dialog.setMessage(R.string.delete_coule_item_prompt);
        dialog.setSure(R.string.confirm, v -> {
            deleteCoupleItem(contactsBean);
            dialog.dismiss();
        });
        dialog.setCancel(R.string.cancel);
        dialog.show();
    }

    private void deleteCoupleItem(final ContactsBean bean) {
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("talkId", bean.getTalkId());
        HttpRequest.post(HttpHelper.URL_DELTET_COUPLE, params, new RequestCallBack<String>() {
            @Override
            public void onLogicSuccess(String o) {
                super.onLogicSuccess(o);
                contactsBeans.remove(bean);

                // 如果删除了已配对的列表, 相当于直接解绑
                if (bean.getCoupleShip() == CoupleLogic.COUPLE_STATE_PAIRED) {
                    // 清除配对人信息
                    CoupleLogic.clearCouplePairedData(getContext());
                    //发到HoemAcitivity和 HomeTotwooHolder
                    EventBus.getDefault().post(new TotwooMessage(CoupleLogic.COUPLE_STATE_APART + "", null));
                }
                ToastUtils.showLong(getContext(), R.string.delete_success);
                refreshCoupleListData();
            }
        });
    }

    /**
     * 发送配对请求请求
     *
     * @param bean
     */
    private void sendRequest(final ContactsBean bean) {
        if (bean != null) {
            mCoupleLogic.sendRequest(bean, success -> {
                if (success) {
                    // 弹窗提示发送成功
                    final CommonMiddleDialog dialog = new CommonMiddleDialog(
                            getContext());
                    dialog.setMessage(R.string.send_success);

                    dialog.setSure(v -> dialog.dismiss());
                    dialog.show();

                    updateListUI();

                    // 重新刷新数据
                    refreshCoupleListData();
                }
            });
        }
    }

    public void receiveCoupleRequest(TotwooMessage message) {
        // 取消弹窗
//        ACache aCache = ACache.get(ToTwooApplication.baseContext);
//        aCache.remove(CommonArgs.NOTIFICATION_MESSAGE);
//        binding.loveRequestLayout.setVisibility(View.VISIBLE);
////        binding.loveUnpairedButtonCl.setVisibility(View.INVISIBLE);
//        BitmapHelper.display(this, binding.loveRequestHeadIcon, message.getMessageBean().getPicUrl());
//        binding.loveRequestInfoTv.setText(DateUtil.getTimeString(getContext(), message.getMessageBean().getSendTime()));
//
//        View[] views = {binding.loveRequestHeadIcon, binding.loveRequestInfoLl, binding.loveRequestClickLl};
//        // 执行动画
//        ViewUtil.showParallaxAnim(getContext(), views, 100, R.anim.home_message_in, null);

        refreshCoupleListData();

        binding.loveRequestConfirm.setOnClickListener(v -> {
            binding.loveRequestClickLl.setVisibility(View.GONE);
            binding.loveUnpairedButtonCl.setVisibility(View.VISIBLE);

            mCoupleLogic.replyRequest(message.getTalkId(), success -> {
                if (success) {
                    ToTwooApplication.otherPhone = message.getOtherPhone();
                    PreferencesUtils.put(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_ID, message.getOtherPhone());
                }
            });
        });
    }

    public void refreshJewState() {
//        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED
//                && !PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.LOVE_TIP_CONNECT_CLOSE, false)) {
//            binding.loveUnpairedTipConnect.setVisibility(View.VISIBLE);
//            showAndPlayAnim();
//        } else {
//            binding.loveUnpairedTipConnect.setVisibility(View.GONE);
//        }

        binding.loveUnpairedTopLayout.setJewState();
    }


    @Override
    public void onShow() {
        super.onShow();

        CommonUtils.setStateBar(getActivity(), false);

        refreshCoupleListData();

        if (binding.loveUnpairedTipConnectLottie.getVisibility() == View.VISIBLE) {
            binding.loveUnpairedTipConnectLottie.playAnimation();
        }
    }

    @Override
    public void onHide() {
        super.onHide();

        if (binding.loveUnpairedTipConnectLottie.getVisibility() == View.VISIBLE) {
            binding.loveUnpairedTipConnectLottie.pauseAnimation();
        }
    }

    /**
     * 展示进度框
     *
     * @param show true 为展示, false 为隐藏
     */
    private void showProgressBar(boolean show) {
        if (show) {
            if (progressBar == null) {
                progressBar = new CustomProgressBarDialog(
                        getContext());
            }
            progressBar.show();
        } else {

            if (progressBar != null && progressBar.isShowing()) {
                progressBar.dismiss();
            }
        }
    }
}
