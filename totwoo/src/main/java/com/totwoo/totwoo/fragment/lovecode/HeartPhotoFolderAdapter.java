package com.totwoo.totwoo.fragment.lovecode;

import android.app.Activity;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.AdapterView;
import com.etone.framework.annotation.ViewInject;
import com.etone.framework.base.BaseArrayListAdapter;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.holderBean.ImageFolderBean;

import java.io.File;
import java.util.ArrayList;

/**
 * Created by xinyoulingxi on 2017/8/4.
 */

@AdapterView(R.layout.activity_memory_photo_folder_item)
public class HeartPhotoFolderAdapter extends BaseArrayListAdapter<ImageFolderBean>
{
    private Activity context;
    public HeartPhotoFolderAdapter(Activity context, ArrayList<ImageFolderBean> resource)
    {
        super(context, resource, true);
        this.context = context;
    }

    @Override
    public void initHolderData(BaseHolder _holder, int position, ImageFolderBean item)
    {
        Holder holder = (Holder) _holder;
        BitmapHelper.display(context, holder.iv, new File(item.firstImg), R.drawable.memory_set_choose);
        holder.name.setText(item.dirName);
        holder.count.setText(item.imgList.size() + "");
        LogUtils.e(item.firstImg);
    }

    public static class Holder implements BaseHolder
    {
        @ViewInject(R.id.memory_photo_folder_item_iv)
        public ImageView iv;

        @ViewInject(R.id.memory_photo_folder_item_name)
        public TextView name;

        @ViewInject(R.id.memory_photo_folder_item_count)
        public TextView count;
    }
}
