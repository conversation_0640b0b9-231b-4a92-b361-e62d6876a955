package com.totwoo.totwoo.fragment.lovecode;

import android.app.Activity;
import android.view.View;
import android.widget.ImageView;

import com.etone.framework.annotation.AdapterView;
import com.etone.framework.annotation.ViewInject;
import com.etone.framework.base.BaseArrayListAdapter;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.MemoryPhotoSelectBean;
import com.totwoo.totwoo.utils.ToastUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.LinkedHashSet;

/**
 * Created by xinyoulingxi on 2017/8/3.
 */
@AdapterView(R.layout.activity_memory_photo_select_item)
public class HeartSelectAdapter extends BaseArrayListAdapter<MemoryPhotoSelectBean>
{
    public static final String MEMORY_PHOTO_TAKE = "memoryPhoneTake";

    private Activity context;

    public LinkedHashSet<String> photoPath;

    public HeartSelectAdapter(Activity context, ArrayList<MemoryPhotoSelectBean> resource, LinkedHashSet<String> photoPath)
    {
        super(context, resource, false);
        this.context = context;
        this.photoPath = photoPath;
    }

    @Override
    public void initHolderData(BaseHolder _holder, int position,final MemoryPhotoSelectBean item)
    {
        Holder holder = (Holder) _holder;
        holder.select.setVisibility(View.GONE);
        if (position == 0)
        {
            holder.camera.setVisibility(View.VISIBLE);
        }
        else
        {
            holder.camera.setVisibility(View.GONE);
            holder.select.setVisibility(View.VISIBLE);
            if (item.isSelected)
                holder.select.setImageResource(R.drawable.memory_select_on);
            else
                holder.select.setImageResource(R.drawable.memory_select_off);

            holder.select.setOnClickListener(new View.OnClickListener()
            {
                @Override
                public void onClick(View v)
                {
                    if (!item.isSelected)
                    {
                        if (photoPath.size() >= 8)
                        {
                            ToastUtils.showShort(context, R.string.memory_photo_8);
                            return;
                        }
                    }

                    item.isSelected = !item.isSelected;
                    if (item.isSelected)
                        photoPath.add(item.path);
                    else
                        photoPath.remove(item.path);

                    HeartSelectAdapter.this.notifyDataSetChanged();
                }
            });

            BitmapHelper.display(context, holder.bg, new File(item.thumbPath), R.drawable.memory_set_choose); //我的头像
        }
    }

    public static class Holder implements BaseHolder
    {
        @ViewInject(R.id.memory_photo_select_item_img)
        public ImageView bg;

        @ViewInject(R.id.memory_photo_select_item_select)
        public ImageView select;

        @ViewInject(R.id.memory_photo_select_item_camera)
        public ImageView camera;
    }
}
