package com.totwoo.totwoo.fragment;


import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.Qian;
import com.totwoo.totwoo.widget.ExpandableTextView;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 抽签结果详情页的主 Fragment
 * <p/>
 * Created by lixing<PERSON><PERSON> on 16/3/1.
 */
public class QianDetailPage extends FrameLayout implements View.OnClickListener {
    /**
     * 抽签结果数据
     */
    private Qian qian;

    /**
     * 是否处于整个 Activity 中, 即抽奖结果的及时展示页
     */
    private boolean isContent;

    @BindView(R.id.qian_detail_content_scrollview)
    ScrollView contentScrollview;

    @BindView(R.id.qian_has_shake_tv)
    TextView hasSakeTv;

    @BindView(R.id.qian_type_layout)
    FrameLayout qianTypeLayout;

    @BindView(R.id.qian_type_name_tv)
    TextView qianTypeNameTv;

    @BindView(R.id.qian_qian_content_tv)
    TextView qianContentTv;

    @BindView(R.id.qian_view_qian_detail_tv)
    LinearLayout viewQianTv;

    @BindView(R.id.qian_detail_etv)
    ExpandableTextView qianDetailEtv;

    @BindView(R.id.qian_view_detail_icon)
    ImageView openIv;

    private Handler mHandler;

    private Context mContext;


    /**
     * 获取 QianDetail 实例
     *
     * @param qian 抽签结构数据.
     * @return A new instance of fragment QianDetailPage.
     */
    public QianDetailPage(Context context, Qian qian, boolean isContent) {
        super(context, null);
        this.mContext = context;
        this.qian = qian;
        this.isContent = isContent;

        if (qian != null) {
            init();
        }
    }

    public QianDetailPage(Context context) {
        super(context);
        this.mContext = context;
    }

    public QianDetailPage(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
    }

    public QianDetailPage(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public QianDetailPage(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        this.mContext = context;
    }

    private void init() {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.fragment_qian_detail, null);

        ButterKnife.bind(this, rootView);

        initListener();

        showView();

        addView(rootView);
    }

    public void setQian(Qian qian) {
        if (qian != null) {
            this.qian = qian;
            init();
        }
    }

    public void setContent(boolean content) {
        isContent = content;
    }

    private void initListener() {
        openIv.setOnClickListener(this);
        qianDetailEtv.setOnClickListener(this);
        viewQianTv.setOnClickListener(this);

        qianDetailEtv.setOnExpandListener(new ExpandableTextView.OnExpandListener() {
            @Override
            public void onExpand(ExpandableTextView parent) {
                // 文字关闭后把屏幕跟文字底部对齐
                int[] position = new int[2];
                qianDetailEtv.getLocationOnScreen(position);
                final int value = position[1] + contentScrollview.getScrollY()
                        + qianDetailEtv.getHeight()
                        - Apputils.getScreenHeight(mContext)
                        + 50;
                new Thread() {
                    public void run() {
                        for (int i = contentScrollview.getScrollY(); i < value; i += 10) {
                            SystemClock.sleep(5);
                            Message message = Message.obtain();
                            message.what = 0;
                            message.arg1 = i;
                            mHandler.sendMessage(message);
                        }
                    }
                }.start();
            }
        });


        qianDetailEtv.setOnCollapseListener(new ExpandableTextView.OnCollapseListener() {
            @Override
            public void onCollapse(ExpandableTextView parent) {
                viewQianTv.setVisibility(View.VISIBLE);
            }
        });


        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case 0:
                        contentScrollview.setScrollY(msg.arg1);
                        break;
                }
            }
        };

    }


    /**
     * 具体数据展示
     */
    private void showView() {
//        if (isContent){
//            hasSakeTv.setVisibility(View.GONE);
//        }else{
//            switch (getQianCount()){
//                case 0:
//                    hasSakeTv.setText(R.string.has_qian_0);
//                    break;
//                case 1:
//                    hasSakeTv.setText(R.string.has_qian_1);
//                    break;
//                case 2:
//                    hasSakeTv.setText(R.string.has_qian_2);
//                    break;
//                case 3:
//                    hasSakeTv.setText(R.string.has_qian_3);
//                    break;
//            }
//        }

        if (qian.getQianType() == null){
            return;
        }

        switch (qian.getQianType()) {
            case "上上签":
                qianTypeLayout.setBackgroundResource(R.drawable.qian_type_0);
                break;
            case "上签":
                qianTypeLayout.setBackgroundResource(R.drawable.qian_type_1);
                break;
            case "中签":
                qianTypeLayout.setBackgroundResource(R.drawable.qian_type_2);
                break;
            case "中平签":
                qianTypeLayout.setBackgroundResource(R.drawable.qian_type_3);
                break;
            case "下签":
                qianTypeLayout.setBackgroundResource(R.drawable.qian_type_4);
                break;
            case "下下签":
                qianTypeLayout.setBackgroundResource(R.drawable.qian_type_5);
                break;
        }

        qianTypeNameTv.setText(qian.getQianName());
        qianContentTv.setText(qian.getQianInfo());
        qianDetailEtv.setText(qian.getQianDetail());

        // 如果不是即时抽奖结果, 默认展开
        if (!isContent) {
            openIv.setImageResource(R.drawable.cloes_minus_sign);
            qianDetailEtv.setVisibility(View.VISIBLE);
            viewQianTv.setVisibility(View.GONE);
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    qianDetailEtv.setExpanded(true);
                }
            }, 200);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.qian_view_detail_icon:
            case R.id.qian_view_qian_detail_tv:
            case R.id.qian_detail_etv:
                if (!qianDetailEtv.isExpanded()) {
                    openIv.setImageResource(R.drawable.cloes_minus_sign);
                    viewQianTv.setVisibility(View.GONE);
                    qianDetailEtv.setVisibility(View.VISIBLE);
                } else {
                    openIv.setImageResource(R.drawable.open_plus);
                    qianDetailEtv.setVisibility(View.INVISIBLE);
                }
                qianDetailEtv.toggle();
                break;
        }
    }
}
