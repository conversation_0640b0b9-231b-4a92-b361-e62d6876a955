package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.DONT_SHOW_TIPS_TAG;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.CameraActivity;
import com.totwoo.totwoo.activity.InitInfoActivity;
import com.totwoo.totwoo.activity.QianActivity;
import com.totwoo.totwoo.activity.StepSettingActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.activity.YesNoActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftMessageListActivity;
import com.totwoo.totwoo.activity.giftMessage.SendGiftGalleryActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.activity.security.FakeCallActivity;
import com.totwoo.totwoo.activity.wish.MLoadMoreView;
import com.totwoo.totwoo.adapter.SafeDiscoverAdapter;
import com.totwoo.totwoo.bean.GreetingCardInfo;
import com.totwoo.totwoo.bean.SafeDiscoverHttpBean;
import com.totwoo.totwoo.bean.SafeDiscoverInfo;
import com.totwoo.totwoo.bean.Step;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SnackBarUtil;
import com.totwoo.totwoo.utils.StepUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.TopLayerLayout;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class DiscoverSafeFragment extends BaseFragment implements HomeBaseActivity.JewelryStateChangeListener, SubscriberListener, BluetoothManage.WriteSuccessListener, OnClickListener {
    @BindView(R.id.notify_top_layout)
    TopLayerLayout mNotifyTopLayout;
    @BindView(R.id.discover_rv)
    RecyclerView mRecyeler;
    @BindView(R.id.discover_main_rl)
    RelativeLayout mMainBg;
    @BindView(R.id.discover_guard_cl)
    ConstraintLayout mGuardCl;
    @BindView(R.id.discover_fake_call_cl)
    ConstraintLayout mFakeCallCl;
    @BindView(R.id.discover_to_top_iv)
    ImageView mToTopIv;
    private ImageView mDiscoverLoadingIv;
    private TextView mDiscoverLoadingTv;
    private TextView mKnowLedge;
    private TextView mStepInfo;
    private TextView mCountTv;
    private TextView mStepInfo2;
    private TextView mCountTv2;
    private ConstraintLayout mInfoCl2;
    private ConstraintLayout mInfoCl;
    private TextView mGiftTv;

    private View view;
    private ArrayList<SafeDiscoverInfo> infos;
    private SafeDiscoverAdapter safeDiscoverAdapter;
    private int currentPage = 1;
    private boolean isOpen = false;
    private ACache aCache;
    private boolean hasGreetingCardList = false;
    private static final String DISCOVER_SAFE_LIST_CACHE = "discover_safe_list_cache";
    private static final String SAFE_HINT_SHOW = "safe_hint_show";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        InjectUtils.injectOnlyEvent(this);
        EventBus.getDefault().register(this);
    }

    private int tempScrollY;

    @SuppressLint("ClickableViewAccessibility")
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_discover, container, false);
        ButterKnife.bind(this, view);
        View headerView = getLayoutInflater().inflate(R.layout.discover_recycler_head, null);
        mDiscoverLoadingIv = headerView.findViewById(R.id.discover_loading_iv);
        mDiscoverLoadingTv = headerView.findViewById(R.id.discover_loading_tv);
        mKnowLedge = headerView.findViewById(R.id.discover_knowledge);
        mStepInfo = headerView.findViewById(R.id.discover_step_info_tv);
        mCountTv = headerView.findViewById(R.id.discover_step_count_tv);
        mStepInfo2 = headerView.findViewById(R.id.discover_2_step_info_tv);
        mCountTv2 = headerView.findViewById(R.id.discover_2_step_count_tv);
        mInfoCl2 = headerView.findViewById(R.id.discover_2_info_cl);
        mInfoCl = headerView.findViewById(R.id.discover_info_cl);
        mGiftTv = headerView.findViewById(R.id.discover_2_gift_tv);

        mKnowLedge.setOnClickListener(this);
        mDiscoverLoadingIv.setOnClickListener(this);
        mGuardCl.setOnClickListener(this);
        mFakeCallCl.setOnClickListener(this);
        mToTopIv.setOnClickListener(this);
        headerView.findViewById(R.id.discover_qian_ll).setOnClickListener(this);
        headerView.findViewById(R.id.discover_camera_ll).setOnClickListener(this);
        headerView.findViewById(R.id.discover_gift_ll).setOnClickListener(this);
        headerView.findViewById(R.id.discover_2_gift_content_cl).setOnClickListener(this);
        headerView.findViewById(R.id.discover_step_content_cl).setOnClickListener(this);
        headerView.findViewById(R.id.discover_2_step_content_cl).setOnClickListener(this);

        setJewState();
        int index = PreferencesUtils.getInt(ToTwooApplication.baseContext, COLOR_VALUE, -1);
        if (index < 0) {
            isOpen = false;
            mNotifyTopLayout.getRightIcon().setImageResource(R.drawable.remind_light_off_selector);
        } else {
            isOpen = true;
            mNotifyTopLayout.getRightIcon().setImageResource(R.drawable.remind_light_on_selector);
        }
        mNotifyTopLayout.getRightIcon().setOnClickListener(v -> flashChange());
        mNotifyTopLayout.getRightIcon().setVisibility(View.VISIBLE);
        BluetoothManage.getInstance().setWriteSuccessListener(this);

        aCache = ACache.get(getActivity());

        infos = new ArrayList<>();

        if (!Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
            mKnowLedge.setVisibility(View.GONE);
            mFakeCallCl.setVisibility(View.GONE);
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, 190), 0, 0);
            mRecyeler.setLayoutParams(layoutParams);
        }
        mRecyeler.setLayoutManager(new LinearLayoutManager(getActivity(), RecyclerView.VERTICAL, false));
        safeDiscoverAdapter = new SafeDiscoverAdapter(R.layout.discover_safe_info_layout, infos);
        safeDiscoverAdapter.setContext(getContext());
        safeDiscoverAdapter.setHeaderView(headerView);
        mRecyeler.setAdapter(safeDiscoverAdapter);
        mRecyeler.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                tempScrollY += dy;
                if (tempScrollY > 20) {
                    mToTopIv.setVisibility(View.VISIBLE);
                } else {
                    mToTopIv.setVisibility(View.GONE);
                }
            }
        });

        safeDiscoverAdapter.setLoadMoreView(new MLoadMoreView());

        safeDiscoverAdapter.setOnLoadMoreListener(() -> {
            currentPage++;
            getInfo();
        }, mRecyeler);

        try {
            SafeDiscoverHttpBean safeDiscoverHttpBean = (SafeDiscoverHttpBean) aCache.getAsObject(DISCOVER_SAFE_LIST_CACHE);
            LogUtils.e("aab safeDiscoverHttpBean.getList().size() = " + safeDiscoverHttpBean.getList().size());
            infos.addAll(safeDiscoverHttpBean.getList());
            safeDiscoverAdapter.notifyDataSetChanged();
            mDiscoverLoadingTv.setVisibility(View.GONE);
        } catch (Exception e) {
            LogUtils.e("aab e = " + e);
            e.printStackTrace();
        }

        getInfo();
        getGreetingCardInfo();
//        setStepStatus();
        setStepText(0);

        mInfoCl.setVisibility(View.VISIBLE);
        mInfoCl2.setVisibility(View.GONE);
//        if (TextUtils.equals(BleParams.JEWELRY_BLE_NAME_SAL, PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))) {
//            mInfoCl.setVisibility(View.VISIBLE);
//            mInfoCl2.setVisibility(View.GONE);
//        } else {
//            mInfoCl.setVisibility(View.GONE);
//            mInfoCl2.setVisibility(View.VISIBLE);
//        }

        return view;
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        setJewState();
    }

    @EventInject(eventType = S.E.E_SAFE_CLICK, runThread = TaskType.UI)
    public void onSafeHintClick(EventData data) {
        flashChange();
    }

    @Override
    public void onShow() {
        if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, SAFE_HINT_SHOW, false)) {
            PreferencesUtils.put(ToTwooApplication.baseContext, SAFE_HINT_SHOW, true);
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_SAFE_HINT, null);
        }
        CommonUtils.setStateBar(getActivity(), true);
    }

    private void setJewState() {
        if (view == null) {
            return;
        }
        if (mNotifyTopLayout == null) {
            mNotifyTopLayout = view.findViewById(R.id.notify_top_layout);
        }
        mNotifyTopLayout.setJewState();
    }

    private boolean isClicked = false;

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.discover_knowledge:
                WebViewActivity.loadUrl(getContext(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_SAFE_KNOWLEDGE), false);
                break;
            case R.id.discover_qian_ll:
                if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                    startActivity(new Intent(getActivity(), QianActivity.class));
                } else {
                    startActivity(new Intent(getActivity(), YesNoActivity.class));
                }
                break;
            case R.id.discover_camera_ll:
                if (isClicked)
                    return;

                isClicked = true;
                new Handler().postDelayed(() -> isClicked = false, 2000);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_CAMERA_PERMISSION, null);
                break;
            case R.id.discover_loading_iv:
                getInfo();
                break;
            case R.id.discover_gift_ll:
            case R.id.discover_2_gift_content_cl:
//                startActivity(new Intent(getContext(), StepCounterActivity.class));
                Intent intent;
                if (hasGreetingCardList) {
                    intent = new Intent(getContext(), GiftMessageListActivity.class);
                    intent.putExtra(CommonArgs.FROM_TYPE, GiftMessageListActivity.LIST_RECEIVER);
                } else {
                    intent = new Intent(getContext(), SendGiftGalleryActivity.class);
                }
                startActivity(intent);
                break;
            case R.id.discover_step_content_cl:
            case R.id.discover_2_step_content_cl:
                int height = ToTwooApplication.owner.getHeight();
                int weight = ToTwooApplication.owner.getWeight();
                if (height == 0 || weight == 0) {
//                    Intent stepIntent = new Intent(getContext(), UserInfoSettingActivity.class);
//                    stepIntent.putExtra("height", ToTwooApplication.owner.getHeight() == 0 ? -1 : ToTwooApplication.owner.getHeight());
//                    stepIntent.putExtra("weight", ToTwooApplication.owner.getWeight() == 0 ? -1 : ToTwooApplication.owner.getWeight());
//                    stepIntent.putExtra(IS_COUNTER_TARGET, true);
//                    startActivity(stepIntent);
                    startActivity(new Intent(getActivity(), InitInfoActivity.class).putExtra(InitInfoActivity.INIT_INFO, false));
                } else
                    startActivity(new Intent(getContext(), StepSettingActivity.class));
                break;
            case R.id.discover_guard_cl:
                if (!isEnableLocate()) {
                    return;
                }
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FINE_CLICK_GUARD);
//                startActivity(new Intent(getContext(), GuardActivity.class));
                break;
            case R.id.discover_fake_call_cl:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.SAFE_FIND_CAMOUFLAGEINCOMINGCALL);
                startActivity(new Intent(getContext(), FakeCallActivity.class));
                break;
            case R.id.discover_to_top_iv:
                mRecyeler.scrollToPosition(0);
                tempScrollY = 0;
                mToTopIv.setVisibility(View.GONE);
                break;
        }
    }

    private void getInfo() {
        HttpHelper.safeService.getList(currentPage, 10, 1)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<SafeDiscoverHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(getContext(), R.string.error_net);
                        if (mDiscoverLoadingTv.getVisibility() == View.VISIBLE) {
                            mDiscoverLoadingTv.setText(R.string.safe_discover_fail);
                            mDiscoverLoadingIv.setVisibility(View.VISIBLE);
                            mDiscoverLoadingTv.setOnClickListener(v -> getInfo());
                        }
                    }

                    @Override
                    public void onNext(HttpBaseBean<SafeDiscoverHttpBean> safeDiscoverHttpBeanHttpBaseBean) {
                        if (safeDiscoverHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            if (currentPage == 1) {
                                infos.clear();
                                aCache.put(DISCOVER_SAFE_LIST_CACHE, safeDiscoverHttpBeanHttpBaseBean.getData());
                            }
                            infos.addAll(safeDiscoverHttpBeanHttpBaseBean.getData().getList());
                            safeDiscoverAdapter.notifyDataSetChanged();
                            mDiscoverLoadingTv.setVisibility(View.GONE);
                            mDiscoverLoadingIv.setVisibility(View.GONE);
                            if (safeDiscoverHttpBeanHttpBaseBean.getData().getCount() / 10 < currentPage) {
                                safeDiscoverAdapter.loadMoreEnd();
                            } else {
                                safeDiscoverAdapter.loadMoreComplete();
                            }
                        }
                    }
                });
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    public void onChange() {
        setJewState();
    }

    private boolean cameraOpened;
    private int count;

    private void startCamera() {
        count = 0;
        cameraOpened = true;
        startActivity(new Intent(getActivity(), CameraActivity.class).putExtra(CommonArgs.FROM_TYPE, 1));
    }

    @EventInject(eventType = S.E.E_HOLDER_CAMERA_OFF, runThread = TaskType.UI)
    public void onPeriodInfoReceiverOff(EventData data) {
        cameraOpened = false;
        if (count > 0) {
            startCamera();
        }
    }

    @EventInject(eventType = S.E.E_CAMERA_PERMISSION_HAS, runThread = TaskType.UI)
    public void onHasCameraPermission(EventData data) {
        if (cameraOpened) {
            ToastUtils.showLong(getActivity(), R.string.home_camera_loadding);
            count++;
            return;
        }
        startCamera();
    }

    private void getGreetingCardInfo() {
        HttpHelper.commonService.getGreetingCardInfo()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<GreetingCardInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<GreetingCardInfo> greetingCardInfoBeanHttpBaseBean) {
                        if (greetingCardInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            hasGreetingCardList = greetingCardInfoBeanHttpBaseBean.getData().getIs_greetingcard() == 1;
                            if (hasGreetingCardList) {
                                mGiftTv.setText(R.string.custom_secret_check_banner);
                            } else {
                                mGiftTv.setText(R.string.custom_secret_creat_banner);
                            }
                        }
                    }
                });
    }

    @Override
    public void onWriteSuccessed() {
        if (isOpen) {
            isOpen = false;
            imageChangeHandler.sendEmptyMessage(0);
            PreferencesUtils.put(ToTwooApplication.baseContext, COLOR_VALUE, -1);
        } else {
            isOpen = true;
            imageChangeHandler.sendEmptyMessage(1);
            PreferencesUtils.put(ToTwooApplication.baseContext, COLOR_VALUE, 0);

            if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, DONT_SHOW_TIPS_TAG, false)) {
                SnackBarUtil.showLong(mMainBg, R.string.safe_bright_open_during, R.string.no_longer_tips_notify_guide, new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        PreferencesUtils.put(ToTwooApplication.baseContext, DONT_SHOW_TIPS_TAG, true);
                    }
                });
            }
        }
    }

    private ImageChangeHandler imageChangeHandler = new ImageChangeHandler();

    private class ImageChangeHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 0) {
                mNotifyTopLayout.getRightIcon().setImageResource(R.drawable.remind_light_off_selector);
            } else {
                mNotifyTopLayout.getRightIcon().setImageResource(R.drawable.remind_light_on_selector);
            }
        }
    }

    private void flashChange() {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(ToTwooApplication.baseContext, R.string.error_jewelry_connect);
            return;
        }
        if (!isOpen) {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.OPEN_FLASH);
            BluetoothManage.getInstance().changeBirghtMode(NotifyUtil.getColorValue("WHITE"), true);
        } else {
            BluetoothManage.getInstance().changeBirghtMode(-1, true);
        }
    }

//    @EventInject(eventType = S.E.E_STEP_NOTIFY_CHANGE, runThread = TaskType.UI)
//    public void onStepNotify(EventData data) {
//        setStepStatus();
//    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateStepData(Step steps) {
        if (steps == null) {
            try {
                steps = DbHelper.getDbUtils().findById(Step.class, Apputils.getZeroCalendar(null).getTimeInMillis());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        int step = steps == null ? 0 : steps.getSteps();

        float caliore = StepUtils.getCaliore(step, true);

//        if (NotifyUtil.getStepNotifyModel(ToTwooApplication.baseContext).isNotifySwitch()) {
//            setStepText(step);
//        }
        setStepText(step);
    }

//    private void setStepStatus() {
//        if (!NotifyUtil.getStepNotifyModel(ToTwooApplication.baseContext).isNotifySwitch()) {
//            mStepInfo.setText(R.string.step_counter);
//            mStepInfo2.setText(R.string.step_counter);
//            mStepInfo.setVisibility(View.VISIBLE);
//            mStepInfo2.setVisibility(View.VISIBLE);
//            mCountTv.setVisibility(View.GONE);
//            mCountTv2.setVisibility(View.GONE);
//            setStepTextAndClick(true, mStepTv);
//            setStepTextAndClick(true, mStep2Tv);
//        } else {
//            setStepText(0);
//            mStepInfo.setVisibility(View.GONE);
//            mStepInfo2.setVisibility(View.GONE);
//            mCountTv.setVisibility(View.VISIBLE);
//            mCountTv2.setVisibility(View.VISIBLE);
//            setStepTextAndClick(false, mStepTv);
//            setStepTextAndClick(false, mStep2Tv);
//        }
//    }

//    private void setStepTextAndClick(boolean isOn, TextView tv) {
//        if (isOn) {
//            tv.setText(R.string.home_call_setting_on);
//            tv.setOnClickListener(v -> {
//                int height = ToTwooApplication.owner.getHeight();
//                int weight = ToTwooApplication.owner.getWeight();
//                if (height == 0 || weight == 0) {
//                    Intent intent = new Intent(ToTwooApplication.baseContext, UserInfoSettingActivity.class);
//                    intent.putExtra("height", ToTwooApplication.owner.getHeight() == 0 ? -1 : ToTwooApplication.owner.getHeight());
//                    intent.putExtra("weight", ToTwooApplication.owner.getWeight() == 0 ? -1 : ToTwooApplication.owner.getWeight());
//                    intent.putExtra(IS_SHOULD_OPEN, true);
//                    startActivity(intent);
//                } else {
//                    if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
//                        JewelryNotifyModel model = NotifyUtil.getStepNotifyModel(ToTwooApplication.baseContext);
//                        model.setFlashColor("WHITE");
//                        model.setNotifySwitch(true);
//                        NotifyUtil.setStepNotify(ToTwooApplication.baseContext, model);
//                    } else {
//                        ToastUtils.showShort(ToTwooApplication.baseContext, R.string.error_jewelry_connect);
//                    }
//                    setStepStatus();
////                    startActivity(new Intent(getActivity(), NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_STEP));
//                }
//            });
//        } else {
//            tv.setText(R.string.home_call_setting_off);
//            tv.setOnClickListener(v -> {
//                JewelryNotifyModel model = NotifyUtil.getStepNotifyModel(ToTwooApplication.baseContext);
//                model.setNotifySwitch(false);
//                NotifyUtil.setStepNotify(ToTwooApplication.baseContext, model);
//                setStepStatus();
//            });
//        }
//    }

    private boolean isEnableLocate() {
        if (!hasLocationPermission()) {
            return false;
        } else if (!CommonUtils.isLocServiceEnable(getActivity())) {
            showLocationDenyDialog();
            return false;
        }
        return true;
    }

    private CustomDialog locationCustomDialog;

    private void showLocationDenyDialog() {
        if (locationCustomDialog == null) {
            locationCustomDialog = new CustomDialog(getActivity());
            locationCustomDialog.setTitle(R.string.tips);
            locationCustomDialog.setMessage(R.string.gps_request_hint);
            locationCustomDialog.setPositiveButton(R.string.set_open_hint, v -> startActivity(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)));
        }
        locationCustomDialog.show();
    }

    private boolean hasLocationPermission() {
        return PermissionUtil.hasLocationPermission(getActivity());
    }

    private void setStepText(int step) {
        String sedentaryInfo = getString(R.string.step_count, step);
        SpannableString spannableString = new SpannableString(sedentaryInfo);
        int stepIndex = sedentaryInfo.indexOf(step + "");
        spannableString = setStyle(sedentaryInfo, spannableString, step, stepIndex);
        mCountTv.setText(spannableString);
        mCountTv2.setText(spannableString);
    }

    private SpannableString setStyle(String string, SpannableString spannableString, int number, int index) {
        int endIndex = (number + "").length();
        spannableString.setSpan(new AbsoluteSizeSpan(20, true), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#2e9995")), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
        EventBus.getDefault().unregister(this);
    }
}
