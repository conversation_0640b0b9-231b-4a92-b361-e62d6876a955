package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.DONT_SHOW_TIPS_TAG;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_VALUE;

import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BrightMusicActivity;
import com.totwoo.totwoo.activity.CameraActivity;
import com.totwoo.totwoo.activity.ConstellationActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftMessageListActivity;
import com.totwoo.totwoo.activity.giftMessage.SendGiftGalleryActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.bean.ConstellationDataModel;
import com.totwoo.totwoo.bean.GreetingCardInfo;
import com.totwoo.totwoo.bean.LuckyGetInfoBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.service.BrightMusicPlayService;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SnackBarUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.ReminderTopLayerLayout;
import com.umeng.analytics.MobclickAgent;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

//棒棒糖页面
@Deprecated
public class LollipopFragment extends BaseFragment implements HomeBaseActivity.JewelryStateChangeListener, SubscriberListener, BluetoothManage.WriteSuccessListener {
    @BindView(R.id.notify_top_layout)
    ReminderTopLayerLayout mNotifyTopLayout;
    @BindView(R.id.reminder_today_date)
    TextView reminder_today_date;
    @BindView(R.id.reminder_number_tv)
    TextView reminder_number_tv;
    @BindView(R.id.reminder_color_tv)
    TextView reminder_color_tv;
    @BindView(R.id.reminder_match_tv)
    TextView reminder_match_tv;
    @BindView(R.id.reminder_constellation_icon_iv)
    ImageView reminder_constellation_icon_iv;
    @BindView(R.id.reminder_constellation_title_tv)
    TextView reminder_constellation_title_tv;
    @BindView(R.id.reminder_fortune_ratingBar)
    RatingBar reminder_fortune_ratingBar;
    @BindView(R.id.reminder_constellation_detail_tv)
    TextView reminder_constellation_detail_tv;
    @BindView(R.id.reminder_color_cl)
    ConstraintLayout reminder_color_cl;
    @BindView(R.id.lollipop_color_iv)
    ImageView mFlashIv;
    @BindView(R.id.lollipop_color_tv)
    TextView mFlashTv;
    @BindView(R.id.reminder_main_bg_iv)
    ImageView mMainBg;

    private boolean hasGreetingCardList = false;
    private Context mContext;
    private int finalIndex;
    public static final String IS_OPEN = "is_open";

    SimpleDateFormat simpleDateFormatM = new SimpleDateFormat("MM");
    SimpleDateFormat simpleDateFormatD = new SimpleDateFormat("dd");

    private static final String LOLLIPOP_HINT_SHOW = "lollipop_hint_show";

    private boolean dataInit;
    private boolean isOpen = false;
    private String constellation_name;
    private int conIcon;
    private int[] conIconRes = {R.drawable.reminder_shuiping_icon, R.drawable.reminder_shuangyu_icon,
            R.drawable.reminder_baiyang_icon, R.drawable.reminder_jinniu_icon,
            R.drawable.reminder_shuangzi_icon, R.drawable.reminder_juxie_icon,
            R.drawable.reminder_shizi_icon, R.drawable.reminder_chunv_icon,
            R.drawable.reminder_tiancheng_icon, R.drawable.reminder_tianxie_icon,
            R.drawable.reminder_sheshou_icon, R.drawable.reminder_mojie_icon
    };

    private View view;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        InjectUtils.injectOnlyEvent(this);
        if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, LOLLIPOP_HINT_SHOW, false)) {
            PreferencesUtils.put(ToTwooApplication.baseContext, LOLLIPOP_HINT_SHOW, true);
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_REMIND_HINT_LIGHT, null);
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_lollipop, container, false);
        ButterKnife.bind(this, view);

        mContext = ToTwooApplication.baseContext;
        setJewState();
        mContext.startService(new Intent(mContext, BrightMusicPlayService.class));

        //英文版数据没有幸运色。英文没有求签功能，中文没有YesOrNo功能
        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
            reminder_color_cl.setVisibility(View.VISIBLE);
        }

        reminder_color_tv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        reminder_match_tv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));

//        changeImageIcon = mNotifyTopLayout.getRightIcon();
//        changeImageIcon.setVisibility(View.VISIBLE);
//        changeImageIcon.setImageResource(R.drawable.icon_lucky_set);
//        changeImageIcon.setOnClickListener(v -> {
//            startActivity(new Intent(getActivity(), NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_FORTUNE));
//        });

        setConInfo();
        setTopLayout();
        getGreetingCardInfo();
        initData();
        int index = PreferencesUtils.getInt(mContext, COLOR_VALUE, -1);
        if (index < 0) {
            isOpen = false;
            mFlashIv.setImageResource(R.drawable.lollipop_light_off);
            mFlashTv.setText(R.string.turn_on_flash);
        } else {
            isOpen = true;
            mNotifyTopLayout.setmRight2Icon(R.drawable.remind_light_on_selector);
            mFlashIv.setImageResource(R.drawable.lollipop_light_on);
            mFlashTv.setText(R.string.turn_off_flash);
        }
        BluetoothManage.getInstance().setWriteSuccessListener(this);

        dataInit = true;
        CommonUtils.setStateBar(getActivity(), true);
        return view;
    }

    private void setConInfo() {
        int index = 0;
        if (owner.getBirthday() != null) {
            String[] birth = owner.getBirthday().split("-");
            int month = Integer.parseInt(birth[1]);
            int day = Integer.parseInt(birth[2]);
            constellation_name = Apputils.getAstro(ToTwooApplication.baseContext, month, day);
            index = Arrays.asList(
                    getResources().getStringArray(R.array.constellation_names_cap))
                    .indexOf(constellation_name);
            if (index < 0) {
                index = 0;
            }
        }
        conIcon = conIconRes[index];

        reminder_constellation_icon_iv.setImageResource(conIcon);
        reminder_constellation_title_tv.setText(constellation_name);
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        setJewState();
    }

    private void setJewState() {
        if (view == null) {
            return;
        }
        if (mNotifyTopLayout == null) {
            mNotifyTopLayout = view.findViewById(R.id.notify_top_layout);
        }
        mNotifyTopLayout.setJewState();
    }

    private void setTopLayout() {
        mNotifyTopLayout.setmRightSetVisible(View.GONE);
        mNotifyTopLayout.setRight2IconVisible(View.GONE);
        mNotifyTopLayout.setmRightIconListener(v -> {
            Intent intent;
            if (hasGreetingCardList) {
                intent = new Intent(getContext(), GiftMessageListActivity.class);
                intent.putExtra(CommonArgs.FROM_TYPE, GiftMessageListActivity.LIST_RECEIVER);
            } else {
                intent = new Intent(getContext(), SendGiftGalleryActivity.class);
            }
            startActivity(intent);
        });
        mNotifyTopLayout.getmRightIcon().setImageResource(R.drawable.lollipop_gift_message);
    }

    private void flashChange() {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(ToTwooApplication.baseContext, R.string.error_jewelry_connect);
            return;
        }
        // 表示当前灯光模式的状态, 0 表示从未开启, 1~9 表示9中模式正在开启中,
        // -1 ~ -9 表示当前关闭, 上次开启的为某个模式
        int index = PreferencesUtils.getInt(mContext, COLOR_VALUE, 1);
        int musicIndex = PreferencesUtils.getInt(mContext, MUSIC_VALUE, 0);
        finalIndex = Math.abs(index) - 1;
        if (!isOpen) {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.OPEN_FLASH);
            if (finalIndex == 0 || musicIndex == 0) {
                BluetoothManage.getInstance().changeBirghtMode(NotifyUtil.getColorValue(getFlashColorValue(finalIndex)), true);
            } else {
                BluetoothManage.getInstance().changeMusicBrightMode(musicIndex, NotifyUtil.getColorValue(getFlashColorValue(finalIndex)), true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_PLAY, null);
            }
        } else {
            if (finalIndex == 0 || musicIndex == 0) {
                BluetoothManage.getInstance().changeBirghtMode(-1, true);
            } else {
                BluetoothManage.getInstance().changeMusicBrightMode(musicIndex, -1, true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
            }
        }
    }

    private boolean isShowing = false;

    @Override
    public void onWriteSuccessed() {
        if (!isShowing) {
            return;
        }
        if (isOpen) {
            isOpen = false;
            imageChangeHandler.sendEmptyMessage(0);
            PreferencesUtils.put(mContext, COLOR_VALUE, -finalIndex - 1);
        } else {
            isOpen = true;
            imageChangeHandler.sendEmptyMessage(1);
            PreferencesUtils.put(mContext, COLOR_VALUE, finalIndex + 1);

            if (!PreferencesUtils.getBoolean(mContext, DONT_SHOW_TIPS_TAG, false)) {
                SnackBarUtil.showLong(mMainBg, R.string.bright_open_during, R.string.no_longer_tips_notify_guide, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        PreferencesUtils.put(mContext, DONT_SHOW_TIPS_TAG, true);
                    }
                });
            }
        }
    }

    private ImageChangeHandler imageChangeHandler = new ImageChangeHandler();

    private class ImageChangeHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 0) {
                mFlashIv.setImageResource(R.drawable.lollipop_light_off);
                mFlashTv.setText(R.string.turn_on_flash);
            } else {
                mFlashIv.setImageResource(R.drawable.lollipop_light_on);
                mFlashTv.setText(R.string.turn_off_flash);
            }
        }
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    public void onShow() {
        super.onShow();
        isShowing = true;
        int index = PreferencesUtils.getInt(ToTwooApplication.baseContext, COLOR_VALUE, -1);
        LogUtils.e("aab index = " + index);
        if (getActivity() == null) {
            return;
        }
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置
        if (dataInit)
            initData();
    }

    @Override
    public void onHide() {
        super.onHide();
        isShowing = false;
    }

    private void initData() {
        HttpHelper.commonServiceV2.getConstellationInfo()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<LuckyGetInfoBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<LuckyGetInfoBean> luckyGetInfoHttpBaseBean) {
                        if (luckyGetInfoHttpBaseBean.getErrorCode() == 0) {
                            ConstellationDataModel dataModel = luckyGetInfoHttpBaseBean.getData().getToday();
                            reminder_number_tv.setText(dataModel.getLuck_number());
                            reminder_fortune_ratingBar.setRating(Float.parseFloat(dataModel.getAll()));
                            if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                                reminder_match_tv.setText(dataModel.getMatch_friend());
                                reminder_color_tv.setText(dataModel.getLuck_color());
                                reminder_constellation_detail_tv.setText(dataModel.getShort_info());
                            } else {
                                reminder_match_tv.setText(dataModel.getMatch_friend_en_sort().toUpperCase());
                                reminder_constellation_detail_tv.setText(dataModel.getShort_info_en());
                            }
                        } else {
                            ToastUtils.showShort(ToTwooApplication.baseContext, R.string.error_net);
                        }
                    }
                });

        Date today = new Date();
        reminder_today_date.setText(simpleDateFormatM.format(today) + "/" + simpleDateFormatD.format(today));
    }

    @Override
    public void onChange() {
        setJewState();
    }

    private boolean isClicked = false;

    @OnClick({R.id.reminder_detail_cl, R.id.reminder_constellation_info_ll, R.id.reminder_constellation_cl, R.id.reminder_date_cl,
            R.id.reminder_camera_iv, R.id.reminder_light_set_iv, R.id.lollipop_color_iv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.reminder_detail_cl:
            case R.id.reminder_constellation_info_ll:
            case R.id.reminder_constellation_cl:
            case R.id.reminder_date_cl:
                startActivity(new Intent(getActivity(), ConstellationActivity.class));
                break;
            case R.id.lollipop_color_iv:
                flashChange();
                break;
            case R.id.reminder_light_set_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET);
                startActivity(new Intent(mContext, BrightMusicActivity.class).putExtra(IS_OPEN, isOpen));
                break;
            case R.id.reminder_camera_iv:
                if (isClicked)
                    return;

                isClicked = true;
                new Handler().postDelayed(() -> isClicked = false, 2000);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_CAMERA_PERMISSION, null);
//                startActivity(new Intent(getActivity(), FaceCameraActivity.class));
                break;
        }
    }

    private void getGreetingCardInfo() {
        HttpHelper.commonService.getGreetingCardInfo()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<GreetingCardInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<GreetingCardInfo> greetingCardInfoBeanHttpBaseBean) {
                        if (greetingCardInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            hasGreetingCardList = greetingCardInfoBeanHttpBaseBean.getData().getIs_greetingcard() == 1;
                        }
                    }
                });
    }

    private boolean cameraOpened;
    private int count;

    private void startCamera() {
        count = 0;
        cameraOpened = true;
        startActivity(new Intent(getActivity(), CameraActivity.class).putExtra(CommonArgs.FROM_TYPE, 1));
    }

    @EventInject(eventType = S.E.E_HOLDER_CAMERA_OFF, runThread = TaskType.UI)
    public void onPeriodInfoReceiverOff(EventData data) {
        cameraOpened = false;
        if (count > 0) {
            startCamera();
        }
    }

    @EventInject(eventType = S.E.E_CAMERA_PERMISSION_HAS, runThread = TaskType.UI)
    public void onHasCameraPermission(EventData data) {
        if (cameraOpened) {
            ToastUtils.showLong(getActivity(), R.string.home_camera_loadding);
            count++;
            return;
        }
        startCamera();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
        // 清理 BluetoothManage 监听器，防止内存泄露
        BluetoothManage.getInstance().setWriteSuccessListener(null);
    }

    private String getFlashColorValue(int position) {
        String str = null;
        switch (position) {
            case 0:
                str = "COLORFUL";
                break;
            case 1:
                str = "PINK";
                break;
            case 2:
                str = "RED";
                break;
            case 3:
                str = "ORANGE";
                break;
            case 4:
                str = "YELLOW";
                break;
            case 5:
                str = "GREEN";
                break;
            case 6:
                str = "CYAN";
                break;
            case 7:
                str = "BLUE";
                break;
            case 8:
                str = "PURPLE";
                break;
            case 9:
                str = "WHITE";
                break;
        }
        return str;
    }
}
