package com.totwoo.totwoo.fragment;


import androidx.core.util.Consumer;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.WrapperSubscriber;

import butterknife.Unbinder;
import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by lixingmao on 2016/12/14.
 */

public class BaseFragment extends Fragment {
    protected Unbinder unbind;


    public void onShow() {
        if (getContext() != null) {
            for (Fragment fragment : getChildFragmentManager().getFragments()) {
                if (fragment instanceof BaseFragment) {
                    ((BaseFragment) fragment).onShow();
                }
            }
        }

    }


    public void dismissProgressDialog() {
        FragmentActivity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).dismissProgressDialog();
        }
    }


    public void showProgressDialog() {
        FragmentActivity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).showProgressDialog();
        }
    }


    public void onHide() {
        if (getContext() != null) {
            for (Fragment fragment : getChildFragmentManager().getFragments()) {
                if (fragment instanceof BaseFragment) {
                    ((BaseFragment) fragment).onHide();
                }
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (unbind != null) {
            unbind.unbind();
        }
    }


    //

    /**
     * 通用的网络请求封装
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     * @param error       失败回调
     * @param showLoading 是否显示加载框
     */
    public  <T> void launchRequest(Observable<HttpBaseBean<T>> observable,
                                   Consumer<T> success,
                                   Consumer<HttpBaseBean> error,
                                   boolean showLoading) {

        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .retry(2) // 默认重试 2 次
                .subscribe(new WrapperSubscriber<>(showLoading) {
                    @Override
                    protected void onSuccess(T data) {
                        super.onSuccess(data);
                        success.accept(data);
                    }

                    @Override
                    protected void onFail(HttpBaseBean data) {
                        error.accept(data);
                    }
                });
    }

    /**
     * 通用的网络请求封装
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     * @param error       失败回调
     */
    public  <T> void launchRequest(Observable<HttpBaseBean<T>> observable,
                                   Consumer<T> success,
                                   Consumer<HttpBaseBean> error) {

        observable
                .compose(HttpHelper.rxSchedulerHelper())
                .retry(2) // 默认重试 2 次
                .subscribe( new WrapperSubscriber<T>(true) {
                    @Override
                    protected void onSuccess(T data) {
                        super.onSuccess(data);
                        success.accept(data);
                    }

                    @Override
                    protected void onFail(HttpBaseBean data) {
                        error.accept(data);
                    }
                });
    }

    /**
     * 通用的网络请求封装
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     * @param showLoading 是否显示加载框
     */
    public  <T> void launchRequest(Observable<HttpBaseBean<T>> observable,
                                   Consumer<T> success,
                                   boolean showLoading) {

        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .retry(2) // 默认重试 2 次
                .subscribe(new WrapperSubscriber<>(showLoading) {
                    @Override
                    protected void onSuccess(T data) {
                        super.onSuccess(data);
                        success.accept(data);
                    }
                });
    }


    /**
     * 通用的网络请求封装
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     */
    public  <T> void launchRequest(Observable<HttpBaseBean<T>> observable,
                                   Consumer<T> success) {

        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .retry(2) // 默认重试 2 次
                .subscribe(new WrapperSubscriber<>(true) {
                    @Override
                    protected void onSuccess(T data) {
                        super.onSuccess(data);
                        success.accept(data);
                    }
                });
    }
}
