package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.exception.DbException;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.WaterTimeSettingActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.adapter.CustomAngleRecyclerViewAdapter;
import com.totwoo.totwoo.bean.ConstellationIndexBean;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.bean.GreetingCardInfo;
import com.totwoo.totwoo.bean.HomePageIndexInfo;
import com.totwoo.totwoo.bean.PeriodBean;
import com.totwoo.totwoo.bean.PeriodStateBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.CustomOrderDbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.AngleTopLayerLayout;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.PullZoomRecyclerView;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;
import com.umeng.analytics.MobclickAgent;

import java.text.SimpleDateFormat;
import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/9/18.
 */

public class CustomReminderFragment extends BaseFragment implements
        SubscriberListener, HomeBaseActivity.JewelryStateChangeListener, CustomAngleRecyclerViewAdapter.ItemTypeProvider {
    @BindView(R.id.custom_reminder_fragment_recyclerview)
    PullZoomRecyclerView mRecyclerView;

    @BindView(R.id.reminder_top_layout)
    AngleTopLayerLayout mHeartTopLayout;

    final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

    private ImageView mTopbg;
    private CustomAngleRecyclerViewAdapter mRecyclerViewAdapter;
    private static final String DONT_SHOW_TIPS_TAG = "dont_show_tips_tag";
    private static final String ANGEL_HINT_SHOW = "angel_hint_show";
    private Context mContext;

    private ArrayList<CustomOrderBean> customOrderBeans;

    private boolean isInit = false;
    public static final String CACHE_REMINDER_IMG = "cache_reminder_img";
    public static final String CACHE_REMINDER_JUMP_URL = "cache_reminder_jump_url";
    public static final String CACHE_LOLLIPOP_IMG = "cache_lollipop_img";
    public static final String CACHE_LOLLIPOP_JUMP_URL = "cache_lollipop_jump_url";

    private View view;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_custom_reminder, container, false);
        ButterKnife.bind(this, view);
        mContext = ToTwooApplication.baseContext;
        InjectUtils.injectOnlyEvent(this);
        CommonUtils.setStateBar(getActivity(), true);
        try {
            initContentView();
        } catch (DbException e) {
            e.printStackTrace();
        }
        getInfo();
        isInit = true;
        setTopLayout();
        mTopbg = mHeartTopLayout.getmTopBarLayerIamge();
        return view;
    }

    private int tempScrollY = 0;

    private void initContentView() throws DbException {
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(5);
        mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), mRecyclerView, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_REMINDER);
        mRecyclerView.setAdapter(mRecyclerViewAdapter);
        //mRecyclerViewAdapter.addDataActionListener(this);

        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == 0) {
                    mHeartTopLayout.stopScroll();
                }
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                tempScrollY += dy;
                mHeartTopLayout.scroll(tempScrollY);
            }
        });
//        mRecyclerView.setmOnTouchListener(new PullZoomBaseView.OnTouchListener() {
//            private int lastY = 0;
//            private int touchEventId = 0;
//            private int i = 0;
//            private int height = PopupMenuUtil.dip2px(baseContext, 142);
//
//            @SuppressLint("HandlerLeak")
//            Handler handler = new Handler() {
//                @Override
//                public void handleMessage(Message msg) {
//                    super.handleMessage(msg);
//                    View scroller = (View) msg.obj;
//                    if (msg.what == touchEventId) {
//                        if (lastY == Math.abs(tempScrollY) && lastY == 0) {
//                            mTopbg.setBackgroundColor(ToTwooApplication.baseContext.getResources().getColor(R.color.layer_bg_black));
//                            float alpha = (float) Math.abs(tempScrollY) / height;
//                            mTopbg.setAlpha(alpha);
//                        } else {
//                            mTopbg.setBackgroundColor(ToTwooApplication.baseContext.getResources().getColor(R.color.layer_bg_black));
//                            float alpha = (float) Math.abs(tempScrollY) / height;
//                            mTopbg.setAlpha(alpha);
//                            if (i++ < 100) {
//                                handler.sendMessageDelayed(handler.obtainMessage(touchEventId, scroller), 5);
//                            }
//                            lastY = (int) Math.abs(tempScrollY);
//                        }
//                    }
//                }
//            };
//
//            @Override
//            public void onMove(MotionEvent ev) {
////                mTopbg.setBackground(getResources().getDrawable(R.drawable.top_layer_bg));
//                mTopbg.setBackgroundColor(ToTwooApplication.baseContext.getResources().getColor(R.color.layer_bg_black));
//                float alpha = (float) Math.abs(tempScrollY) / height;
//
//                mTopbg.setAlpha(alpha);
//            }
//
//            @Override
//            public void onUp(MotionEvent ev) {
//                float alpha = (float) Math.abs(tempScrollY) / height;
//
////                mTopbg.setBackground(getResources().getDrawable(R.drawable.top_layer_bg));
//                mTopbg.setBackgroundColor(ToTwooApplication.baseContext.getResources().getColor(R.color.layer_bg_black));
//
//                mTopbg.setAlpha(alpha);
//                i = 0;
//                handler.sendMessageDelayed(handler.obtainMessage(touchEventId, mRecyclerView), 5);
//            }
//        });
    }

    private void setTopLayout() {
        mHeartTopLayout.setBuringLayerView(mRecyclerView);
        mHeartTopLayout.setJewState();
//        mHeartTopLayout.getmRightIcon().setVisibility(View.INVISIBLE);
//        mHeartTopLayout.getmRight2Icon().setVisibility(View.INVISIBLE);
//        mHeartTopLayout.setmRightSetListener(v -> {
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FUNC_SORT);
//            startActivity(new Intent(getActivity(), CustomOrderActivity.class).putExtra("from_type", 5));
//        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
        if (mRecyclerViewAdapter != null) {
            mRecyclerViewAdapter.recovery();
        }
    }

    /**
     * 标签页
     * CustomOrderActivity
     */
    @EventInject(eventType = S.E.E_CUSTOM_ORDER_UPDATE, runThread = TaskType.UI)
    public void onOrderUpdateReceiver(EventData data) {
        try {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(5);
            mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), mRecyclerView, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_REMINDER);
            mRecyclerView.setAdapter(mRecyclerViewAdapter);
            tempScrollY = 0;
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onShow() {
        MobclickAgent.onPageStart(TrackEvent.ANGEL_PAGE);
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.HOMEPAGE_BOTTOM_ANGEL);
//        setJewState();

        if (isInit) {
            getInfo();
        }
        if (!PreferencesUtils.getBoolean(mContext, ANGEL_HINT_SHOW, false)) {
            PreferencesUtils.put(mContext, ANGEL_HINT_SHOW, true);
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_ANGEL_HINT, null);
        }
        CommonUtils.setStateBar(getActivity(), true);
    }

    @Override
    public void onChange() {
//        setJewState();
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        setJewState();
    }

    private void setJewState() {
        if (view == null) {
            return;
        }
        if (mHeartTopLayout == null) {
            mHeartTopLayout = view.findViewById(R.id.reminder_top_layout);
        }
        mHeartTopLayout.setJewState();
    }

    private void getInfo() {
        getPeriodState();
        getConstellationIndex();
        getGreetingCardInfo();
        if (BleParams.isLollipopJewelry()) {
            String cache_img = ACache.get(mContext).getAsString(CACHE_LOLLIPOP_IMG);
            if (TextUtils.isEmpty(cache_img)) {
                getTopImage();
            }
        } else {
            String cache_img = ACache.get(mContext).getAsString(CACHE_REMINDER_IMG);
            if (TextUtils.isEmpty(cache_img)) {
                getTopImage();
            }
        }
    }

    public static int define_num;

    private void getConstellationIndex() {
        HttpHelper.commonService.getConstellationIndex()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<ConstellationIndexBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<ConstellationIndexBean> constellationIndexBeanHttpBaseBean) {
                        if (constellationIndexBeanHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.constellationIndexBeanHttpBaseBean = constellationIndexBeanHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CUSTOM_NOTIFY, constellationIndexBeanHttpBaseBean.getData());
                            define_num = constellationIndexBeanHttpBaseBean.getData().getDefine_num();
                        }
                    }
                });
    }

    private void getPeriodState() {
        HttpHelper.periodSave.getPeriodState()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<PeriodStateBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodStateBean> periodStateBeanHttpBaseBean) {
                        if (periodStateBeanHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.periodStateBean = periodStateBeanHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_PERIOD, periodStateBeanHttpBaseBean.getData());
                        } else if (periodStateBeanHttpBaseBean.getErrorCode() == 4) {
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_PERIOD_OFF, null);
                        }
                    }
                });
    }

    private void getTopImage() {
        if (BleParams.isLollipopJewelry()) {
            HttpHelper.commonService.homePageIndex("Lollipop")
                    .subscribeOn(Schedulers.newThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Observer<HttpBaseBean<HomePageIndexInfo>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<HomePageIndexInfo> homePageIndexInfoHttpBaseBean) {
                            if (homePageIndexInfoHttpBaseBean.getErrorCode() == 0) {
                                CustomAngleRecyclerViewAdapter.homePageIndexInfo = homePageIndexInfoHttpBaseBean.getData();
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_TOP_INDEX, homePageIndexInfoHttpBaseBean.getData());
                                ACache.get(mContext).put(CACHE_LOLLIPOP_IMG, homePageIndexInfoHttpBaseBean.getData().getImg_url(), 6 * 3600);
                                ACache.get(mContext).put(CACHE_LOLLIPOP_JUMP_URL, homePageIndexInfoHttpBaseBean.getData().getJump_url(), 6 * 3600);
                            }
                        }
                    });
        } else {
            HttpHelper.commonService.homePageIndex("miss")
                    .subscribeOn(Schedulers.newThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Observer<HttpBaseBean<HomePageIndexInfo>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<HomePageIndexInfo> homePageIndexInfoHttpBaseBean) {
                            if (homePageIndexInfoHttpBaseBean.getErrorCode() == 0) {
                                CustomAngleRecyclerViewAdapter.homePageIndexInfo = homePageIndexInfoHttpBaseBean.getData();
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_TOP_INDEX, homePageIndexInfoHttpBaseBean.getData());
                                ACache.get(mContext).put(CACHE_REMINDER_IMG, homePageIndexInfoHttpBaseBean.getData().getImg_url(), 6 * 3600);
                                ACache.get(mContext).put(CACHE_REMINDER_JUMP_URL, homePageIndexInfoHttpBaseBean.getData().getJump_url(), 6 * 3600);
                            }
                        }
                    });
        }
    }

    private void getGreetingCardInfo() {
        HttpHelper.commonService.getGreetingCardInfo()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<GreetingCardInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<GreetingCardInfo> greetingCardInfoBeanHttpBaseBean) {
                        if (greetingCardInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            if (greetingCardInfoBeanHttpBaseBean.getData().getIs_greetingcard() == 1) {
                                CustomAngleRecyclerViewAdapter.hasGreetingCardList = true;
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CARD_YES, null);
                            } else {
                                CustomAngleRecyclerViewAdapter.hasGreetingCardList = false;
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CARD_NO, null);
                            }
                        }
                    }
                });
    }

    /**
     * 大姨妈标签页点击时候的弹窗
     * HomePeroidHolder
     */
    @EventInject(eventType = S.E.E_HOLDER_PERIOD_CHANGE, runThread = TaskType.UI)
    public void onPeriodInfoReceiver(EventData data) {
        showBirthDialog();
    }

    private void showBirthDialog() {
        String periodDay = owner.getPeriodDay();
        if (TextUtils.isEmpty(periodDay))
            periodDay = format.format(System.currentTimeMillis());

        long tenYears = 1L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack((timePickerView, millseconds) -> {
                    String tempBirthday = format.format(millseconds);
                    owner.setPeriodDay(tempBirthday);
                    HttpHelper.periodSave.updatePeriod(tempBirthday)
                            .subscribeOn(Schedulers.newThread())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Observer<HttpBaseBean<PeriodBean>>() {
                                @Override
                                public void onCompleted() {
                                }

                                @Override
                                public void onError(Throwable e) {

                                }

                                @Override
                                public void onNext(HttpBaseBean<PeriodBean> periodBeanHttpBaseBean) {
                                    if (periodBeanHttpBaseBean.getErrorCode() == 0) {
                                        getPeriodState();
                                        showWaterSuggestDialog();
                                    }
                                }
                            });
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.period_setting_start_dialog))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - tenYears)
                .setMaxMillseconds(System.currentTimeMillis())
                .setCurrentMillseconds(System.currentTimeMillis())
                .setThemeColor(mContext.getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(mContext.getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(mContext.getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getChildFragmentManager(), "year_month_day");
    }

    private void showWaterSuggestDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(getActivity());
        commonMiddleDialog.setInfo(R.string.drink_water_reminder);
        commonMiddleDialog.setTitle(R.string.warm_tips);
        commonMiddleDialog.setSure(R.string.set, v -> {
            try {
                requireContext().startActivity(new Intent(getActivity(), WaterTimeSettingActivity.class));
            } catch (Exception e) {
                e.printStackTrace();
            }
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    public boolean hasItemByType(int itemType) {
        if (mRecyclerViewAdapter != null) {
            return mRecyclerViewAdapter.hasItemByType(itemType);
        }
        return false;
    }
}
