package com.totwoo.totwoo.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.ConstellationActivity;
import com.totwoo.totwoo.activity.NotifySettingActivity;
import com.totwoo.totwoo.activity.QianActivity;
import com.totwoo.totwoo.activity.YesNoActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.activity.wish.WishCardGalleryActivity;
import com.totwoo.totwoo.activity.wish.WishDetailInfoActivity;
import com.totwoo.totwoo.activity.wish.WishListActivity;
import com.totwoo.totwoo.bean.WishTopInfo;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.TopLayerLayout;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

//许愿功能停用
@Deprecated
public class WishFragment extends BaseFragment implements HomeBaseActivity.JewelryStateChangeListener, SubscriberListener {

    @BindView(R.id.wish_notify_top_layout)
    TopLayerLayout mNotifyTopLayout;
    @BindView(R.id.wish_empty_hint_tv)
    TextView mEmptyHint;
    @BindView(R.id.wish_qian_iv)
    ImageView mQianIv;
    @BindView(R.id.wish_more_iv)
    ImageView mBottleMoreIv;
    @BindView(R.id.wish_bottle_big_lv)
    LottieAnimationView mBottleBigLv;
    @BindView(R.id.wish_bottle_big_cl)
    ConstraintLayout mBottleBigCl;
    @BindView(R.id.wish_bottle_big_count_tv)
    TextView mBottomBigCountTv;

    @BindView(R.id.wish_bottle_mid_lv)
    LottieAnimationView mBottleMidLv;
    @BindView(R.id.wish_bottle_mid_cl)
    ConstraintLayout mBottleMidCl;
    @BindView(R.id.wish_bottle_mid_count_tv)
    TextView mBottomMidCountTv;

    @BindView(R.id.wish_bottle_small_lv)
    LottieAnimationView mBottleSmallLv;
    @BindView(R.id.wish_bottle_small_cl)
    ConstraintLayout mBottleSmallCl;
    @BindView(R.id.wish_bottle_small_count_tv)
    TextView mBottomSmallCountTv;
    private static final String HTTP_WISH_CACHE = "http_wish_cache";

    private boolean isInit = false;
    private boolean hasWish = false;
    private ACache aCache;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        InjectUtils.injectOnlyEvent(this);
    }

    private View view;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_wish, container, false);
        ButterKnife.bind(this, view);
        setJewState();
        ImageView setIv = mNotifyTopLayout.getRightIcon();
        setIv.setImageResource(R.drawable.white_setting);
        setIv.setVisibility(View.VISIBLE);
        setIv.setOnClickListener(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LUCKY_TOPRIGHT_WISHALERTMODEL);
            startActivity(new Intent(getActivity(), NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_WISH));
        });
        aCache = ACache.get(getActivity());

        if (!Apputils.systemLanguageIsChinese(getActivity())) {
            mQianIv.setImageResource(R.drawable.icon_wish_yes_or_no);
        }

        try {
            handleWishHttpData((WishTopInfo) aCache.getAsObject(HTTP_WISH_CACHE));
        } catch (Exception e) {
            e.printStackTrace();
        }

        getWishTop();

        initWishLottie();

        isInit = true;

        return view;
    }

    private void initWishLottie() {
        mBottleBigLv.setImageAssetsFolder("lottie_bottle_big/");
        mBottleBigLv.setAnimation("wish_bottle_big.json");
        mBottleBigLv.setRepeatCount(LottieDrawable.INFINITE);

        mBottleMidLv.setImageAssetsFolder("lottie_bottle_mid/");
        mBottleMidLv.setAnimation("wish_bottle_mid.json");
        mBottleMidLv.setRepeatCount(LottieDrawable.INFINITE);

        mBottleSmallLv.setImageAssetsFolder("lottie_bottle_small/");
        mBottleSmallLv.setAnimation("wish_bottle_small.json");
        mBottleSmallLv.setRepeatCount(LottieDrawable.INFINITE);
    }

    @OnClick({R.id.wish_bottom_wish, R.id.wish_qian_iv, R.id.wish_constellation_iv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.wish_qian_iv:
                if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LUCKY_TO_DIVINATION);
                    startActivity(new Intent(getActivity(), QianActivity.class));
                } else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LUCKY_TO_YESORNO);
                    startActivity(new Intent(getActivity(), YesNoActivity.class));
                }
                break;
            case R.id.wish_bottom_wish:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LUCKY_TO_WISH);
                startActivity(new Intent(getActivity(), WishCardGalleryActivity.class).putExtra(WishCardGalleryActivity.HAS_WISH, hasWish));
                break;
            case R.id.wish_constellation_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LUCKY_TO_SHAKING);
                startActivity(new Intent(getActivity(), ConstellationActivity.class));
                break;
        }
    }

    @Override
    public void onChange() {
//        setJewState();
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        setJewState();
    }

    @Override
    public void onHide() {
        super.onHide();
        if (isInit) {
            pauseAllAnim();
        }
    }

    @Override
    public void onShow() {
        super.onShow();
        if (isInit) {
            getWishTop();
            playAllAnim();
            // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置
        }
    }

    private void setJewState() {
        if (view == null) {
            return;
        }
        if (mNotifyTopLayout == null) {
            mNotifyTopLayout = view.findViewById(R.id.wish_notify_top_layout);
        }
        mNotifyTopLayout.setJewState();
    }

    private void getWishTop() {
        HttpHelper.wishService.getWishTop()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<WishTopInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(getActivity(), getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(final HttpBaseBean<WishTopInfo> wishTopInfoHttpBaseBean) {
                        if (wishTopInfoHttpBaseBean.getErrorCode() == 0) {
                            handleWishHttpData(wishTopInfoHttpBaseBean.getData());
                            aCache.put(HTTP_WISH_CACHE, wishTopInfoHttpBaseBean.getData());
                        }
                    }
                });
    }

    private void handleWishHttpData(WishTopInfo wishTopInfo) {
        if (TextUtils.equals(wishTopInfo.getMore(), "show")) {
            mBottleMoreIv.setVisibility(View.VISIBLE);
            mBottleMoreIv.setOnClickListener(v -> startActivity(new Intent(getActivity(), WishListActivity.class)));
            ConstraintLayout.LayoutParams layoutParamsBig = new ConstraintLayout.LayoutParams(CommonUtils.dip2px(ToTwooApplication.baseContext, 122), CommonUtils.dip2px(ToTwooApplication.baseContext, 125));
            layoutParamsBig.horizontalBias = 0.48f;
            layoutParamsBig.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParamsBig.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParamsBig.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParamsBig.bottomMargin = CommonUtils.dip2px(ToTwooApplication.baseContext, 14);
            mBottleBigCl.setLayoutParams(layoutParamsBig);

            ConstraintLayout.LayoutParams layoutParamsMid = new ConstraintLayout.LayoutParams(CommonUtils.dip2px(ToTwooApplication.baseContext, 122), CommonUtils.dip2px(ToTwooApplication.baseContext, 125));
            layoutParamsMid.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParamsMid.leftMargin = CommonUtils.dip2px(ToTwooApplication.baseContext, 20);
            layoutParamsMid.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParamsMid.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParamsMid.verticalBias = 0.4f;
            mBottleMidCl.setLayoutParams(layoutParamsMid);
        } else {
            mBottleMoreIv.setAnimation(null);
            mBottleMoreIv.setVisibility(View.GONE);
            if(wishTopInfo.getInfo() != null && wishTopInfo.getInfo().size() == 1){
                ConstraintLayout.LayoutParams layoutParamsBig = new ConstraintLayout.LayoutParams(CommonUtils.dip2px(ToTwooApplication.baseContext, 122), CommonUtils.dip2px(ToTwooApplication.baseContext, 125));
                layoutParamsBig.horizontalBias = 0.48f;
                layoutParamsBig.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsBig.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsBig.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsBig.bottomMargin = CommonUtils.dip2px(ToTwooApplication.baseContext, 36);
                mBottleBigCl.setLayoutParams(layoutParamsBig);
            }else if(wishTopInfo.getInfo() != null && wishTopInfo.getInfo().size() == 2){
                ConstraintLayout.LayoutParams layoutParamsBig = new ConstraintLayout.LayoutParams(CommonUtils.dip2px(ToTwooApplication.baseContext, 122), CommonUtils.dip2px(ToTwooApplication.baseContext, 125));
                layoutParamsBig.horizontalBias = 0.72f;
                layoutParamsBig.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsBig.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsBig.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsBig.bottomMargin = CommonUtils.dip2px(ToTwooApplication.baseContext, 43);
                mBottleBigCl.setLayoutParams(layoutParamsBig);

                ConstraintLayout.LayoutParams layoutParamsMid = new ConstraintLayout.LayoutParams(CommonUtils.dip2px(ToTwooApplication.baseContext, 122), CommonUtils.dip2px(ToTwooApplication.baseContext, 125));
                layoutParamsMid.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsMid.leftMargin = CommonUtils.dip2px(ToTwooApplication.baseContext, 40);
                layoutParamsMid.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsMid.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParamsMid.verticalBias = 0.41f;
                mBottleMidCl.setLayoutParams(layoutParamsMid);
            }
        }

        if (TextUtils.equals(wishTopInfo.getMore_list(), "show")) {
            hasWish = true;
        } else {
            hasWish = false;
        }

        if (!isEmptyList(wishTopInfo)) {
            mEmptyHint.setVisibility(View.GONE);
        } else {
            mEmptyHint.setVisibility(View.VISIBLE);
        }

        if (wishTopInfo.getInfo() != null && wishTopInfo.getInfo().size() > 0) {
            switch (wishTopInfo.getInfo().size()) {
                case 1:
                    showBottleAndAnim(mBottleBigCl, mBottleBigLv);
                    hideBottleAndAnim(mBottleMidCl, mBottleMidLv);
                    hideBottleAndAnim(mBottleSmallCl, mBottleSmallLv);

                    setBottleCount(mBottomBigCountTv, wishTopInfo.getInfo().get(0).getLike_count());
                    mBottleBigCl.setOnClickListener(v -> startWishDetail(wishTopInfo.getInfo().get(0).getWish_id()));
                    break;
                case 2:
                    showBottleAndAnim(mBottleBigCl, mBottleBigLv);
                    showBottleAndAnim(mBottleMidCl, mBottleMidLv);
                    hideBottleAndAnim(mBottleSmallCl, mBottleSmallLv);

                    setBottleCount(mBottomBigCountTv, wishTopInfo.getInfo().get(0).getLike_count());
                    mBottleBigCl.setOnClickListener(v -> startWishDetail(wishTopInfo.getInfo().get(0).getWish_id()));
                    setBottleCount(mBottomMidCountTv, wishTopInfo.getInfo().get(1).getLike_count());
                    mBottleMidCl.setOnClickListener(v -> startWishDetail(wishTopInfo.getInfo().get(1).getWish_id()));
                    break;
                case 3:
                    showBottleAndAnim(mBottleBigCl, mBottleBigLv);
                    showBottleAndAnim(mBottleMidCl, mBottleMidLv);
                    showBottleAndAnim(mBottleSmallCl, mBottleSmallLv);

                    setBottleCount(mBottomBigCountTv, wishTopInfo.getInfo().get(0).getLike_count());
                    mBottleBigCl.setOnClickListener(v -> startWishDetail(wishTopInfo.getInfo().get(0).getWish_id()));
                    setBottleCount(mBottomMidCountTv, wishTopInfo.getInfo().get(1).getLike_count());
                    mBottleMidCl.setOnClickListener(v -> startWishDetail(wishTopInfo.getInfo().get(1).getWish_id()));
                    setBottleCount(mBottomSmallCountTv, wishTopInfo.getInfo().get(2).getLike_count());
                    mBottleSmallCl.setOnClickListener(v -> startWishDetail(wishTopInfo.getInfo().get(2).getWish_id()));
                    break;
            }
        }else{
            hideBottleAndAnim(mBottleBigCl, mBottleBigLv);
            hideBottleAndAnim(mBottleMidCl, mBottleMidLv);
            hideBottleAndAnim(mBottleSmallCl, mBottleSmallLv);
        }
    }

    private void showBottleAndAnim(ConstraintLayout constraintLayout, LottieAnimationView lottieAnimationView) {
        constraintLayout.setVisibility(View.VISIBLE);
        lottieAnimationView.playAnimation();
    }

    private void hideBottleAndAnim(ConstraintLayout constraintLayout, LottieAnimationView lottieAnimationView) {
        constraintLayout.setVisibility(View.GONE);
        lottieAnimationView.pauseAnimation();
    }

    private void playAllAnim() {
        playSingleAnim(mBottleBigCl, mBottleBigLv);
        playSingleAnim(mBottleMidCl, mBottleMidLv);
        playSingleAnim(mBottleSmallCl, mBottleSmallLv);
    }

    private void playSingleAnim(ConstraintLayout constraintLayout, LottieAnimationView lottieAnimationView) {
        if (constraintLayout.getVisibility() == View.VISIBLE) {
            lottieAnimationView.playAnimation();
        }
    }

    private void pauseAllAnim() {
        pauseSingleAnim(mBottleBigCl, mBottleBigLv);
        pauseSingleAnim(mBottleMidCl, mBottleMidLv);
        pauseSingleAnim(mBottleSmallCl, mBottleSmallLv);
    }

    private void pauseSingleAnim(ConstraintLayout constraintLayout, LottieAnimationView lottieAnimationView) {
        if (constraintLayout.getVisibility() == View.VISIBLE) {
            lottieAnimationView.pauseAnimation();
        }
    }

    private void startWishDetail(String wish_id) {
        startActivity(new Intent(getActivity(), WishDetailInfoActivity.class).putExtra(WishDetailInfoActivity.WISH_ID, wish_id).putExtra(WishDetailInfoActivity.HAS_COVER, true));
    }

    private boolean isEmptyList(WishTopInfo info) {
        if (TextUtils.equals(info.getMore(), "show")) {
            return false;
        } else if (info.getInfo() != null && info.getInfo().size() > 0) {
            return false;
        }
        return true;
    }

    private void setBottleCount(TextView textView, int count) {
        if (count >= 100) {
            textView.setText("99+");
            textView.setTextSize(12);
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setText(count + "");
            textView.setTextSize(14);
            textView.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
