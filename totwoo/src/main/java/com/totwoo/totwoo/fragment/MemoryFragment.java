package com.totwoo.totwoo.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.activity.memory.MemoryPageActivity;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.ApiException;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.TopLayerLayout;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Created by xinyoulingxi on 2017/12/1.
 */

//平安果停用
@Deprecated
public class MemoryFragment extends BaseFragment implements HomeBaseActivity.JewelryStateChangeListener, SubscriberListener {
    @BindView(R.id.fragment_memory_no_layout)
    LinearLayout noLayout;

    @BindView(R.id.fragment_memory_no_btn)
    TextView noBtn;

    @BindView(R.id.fragment_memory_yes_layout)
    RelativeLayout yesLayout;

    @BindView(R.id.fragment_memory_num_photo)
    TextView yesPhoto;

    @BindView(R.id.fragment_memory_num_vedio)
    TextView yesVedio;

    @BindView(R.id.fragment_memory_num_audio)
    TextView yesAudio;

    @BindView(R.id.fragment_memory_num_txt)
    TextView yesTxt;

    @BindView(R.id.heart_top_layout)
    TopLayerLayout mNotifyTopLayout;

    private View view;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        InjectUtils.injectOnlyEvent(this);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_memory, container, false);
        ButterKnife.bind(this, view);

        //initContentView();
        //EventBus.getDefault().register(this);
        setJewState();

        MemoryController.getInstance().total();

        onShow();
        return view;
    }

    private boolean isEmpty = false;

    @EventInject(eventType = S.E.E_MEMORY_TOTAL_SUCCESSED, runThread = TaskType.UI)
    public void onMemoryTotalSuccessed(EventData data) {
        HttpValues hv = (HttpValues) data;
        int isEmpty = (int) hv.getUserDefine("isEmpty");
        int txtTotal = (int) hv.getUserDefine("txtTotal");
        int imgTotal = (int) hv.getUserDefine("imgTotal");
        int audTotal = (int) hv.getUserDefine("audTotal");
        int vedTotal = (int) hv.getUserDefine("vedTotal");

        if (isEmpty == 0)   //全部为空
        {
            noLayout.setVisibility(View.VISIBLE);
            yesLayout.setVisibility(View.GONE);
            this.isEmpty = true;
        } else {
            this.isEmpty = false;
            yesLayout.setVisibility(View.VISIBLE);
            noLayout.setVisibility(View.GONE);
            yesPhoto.setText("" + imgTotal);
            yesVedio.setText("" + vedTotal);
            yesAudio.setText("" + audTotal);
            yesTxt.setText("" + txtTotal);
        }
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_SUCCESSED, runThread = TaskType.Async)
    public void onMemorySaveSuccessed(EventData data) {
        MemoryController.getInstance().total();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_SUCCESSED, runThread = TaskType.Async)
    public void onMemoryDeleteSuccessed(EventData data) {
        MemoryController.getInstance().total();
    }

    @EventInject(eventType = S.E.E_MEMORY_TOTAL_FAILED, runThread = TaskType.UI)
    public void onMemoryTotalFailed(EventData data) {
        HttpValues hv = (HttpValues) data;
        throw new ApiException(Integer.valueOf(hv.errorCode), hv.errorMesg);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    public void onChange() {
//        setJewState();
    }

    private void setJewState() {
        if (view == null) {
            return;
        }
        if (mNotifyTopLayout == null) {
            mNotifyTopLayout = view.findViewById(R.id.heart_top_layout);
        }
        mNotifyTopLayout.setJewState();
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        setJewState();
    }

    @Override
    public void onShow() {
        super.onShow();
        MobclickAgent.onPageStart(TrackEvent.MEMORY_PAGE);
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.HOMEPAGE_BOTTOM_MEMORY);

        if (getActivity() == null) {
            return;
        }

//        setJewState();
        if (getActivity() != null) {
            CommonUtils.setStateBar(getActivity(), true);
        }
    }

    @Override
    public void onHide() {
        super.onHide();
        MobclickAgent.onPageEnd(TrackEvent.MEMORY_PAGE);
    }

    @OnClick({R.id.fragment_memory_no_btn, R.id.fragment_memory_yes_see, R.id.fragment_memory_yes_btn, R.id.fragment_memory_no_layout, R.id.fragment_memory_yes_layout})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.fragment_memory_no_layout:
            case R.id.fragment_memory_no_btn:
                onMemoryNoBtnClick();
                break;
            case R.id.fragment_memory_yes_layout:
            case R.id.fragment_memory_yes_see:
                onMemoryYesSeeClick();
                break;
            case R.id.fragment_memory_yes_btn:
                onMemoryYesBtnClick();
                break;
        }
    }

    private void onMemoryYesSeeClick() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_CHECK_MEMORY);
        Intent intent = new Intent(this.getActivity(), MemoryPageActivity.class);
        if (isEmpty)
            intent.putExtra(MemoryPageActivity.IS_OPENED, true);
        intent.putExtra("hahaha", true);
        getActivity().startActivity(intent);
    }

    private void onMemoryYesBtnClick() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_INSERT_MEMORY);
        Intent intent = new Intent(this.getActivity(), MemoryPageActivity.class);
        intent.putExtra(MemoryPageActivity.IS_OPENED, true);
        intent.putExtra("wahaha", true);
        getActivity().startActivity(intent);
    }

    private void onMemoryNoBtnClick() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_INSERT_MEMORY);
        Intent intent = new Intent(this.getActivity(), MemoryPageActivity.class);
        if (isEmpty)
            intent.putExtra(MemoryPageActivity.IS_OPENED, true);
        intent.putExtra("hahaha", true);
        getActivity().startActivity(intent);
    }
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
    }
}
