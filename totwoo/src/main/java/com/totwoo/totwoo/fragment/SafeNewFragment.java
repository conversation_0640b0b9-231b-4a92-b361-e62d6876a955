package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity.IS_IMEI_SENT;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.WeiboAuthActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.activity.security.EmergencyDocActivity;
import com.totwoo.totwoo.activity.security.SecurityHintActivity;
import com.totwoo.totwoo.activity.security.SecurityNewListActivity;
import com.totwoo.totwoo.activity.security.SecurityReportListActivity;
import com.totwoo.totwoo.bean.SafeExpireTimeBean;
import com.totwoo.totwoo.bean.SafeTypeBean;
import com.totwoo.totwoo.bean.SecurityContactsHttpBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.TopBlackLayerLayout;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.Subscriber;


public class SafeNewFragment extends BaseFragment implements HomeBaseActivity.JewelryStateChangeListener, SubscriberListener {
    private View view;
    public static final String HTTP_INDEX_CACHE = "http_index_cache";
    private static final String HAS_INIT_CONTACT = "has_init_contact";
    @BindView(R.id.notify_top_black_layout)
    TopBlackLayerLayout mNotifyTopBlackLayout;
    @BindView(R.id.safe_guard_fee_cl)
    ConstraintLayout mFeeCl;
    @BindView(R.id.safe_connect_date_tv)
    TextView mGuardDaysTv;
    @BindView(R.id.safe_emergency_info_tv)
    TextView mEmergencyInfoTv;
    @BindView(R.id.safe_default_cl)
    ConstraintLayout mDefaultViewCl;
    @BindView(R.id.safe_normal_rl)
    RelativeLayout mNormalRl;
    @BindView(R.id.safe_emergency_cl)
    ConstraintLayout mEmergencyCl;
    @BindView(R.id.safe_emergency_lv)
    LottieAnimationView mEmergencyLv;
    @BindView(R.id.safe_guide_tv)
    TextView mGuideTv;
    @BindView(R.id.safe_content_cl)
    ConstraintLayout mSafeContentCl;
    @BindView(R.id.safe_contact_cl)
    ConstraintLayout mSafeContactCl;
    @BindView(R.id.safe_doc_cl)
    ConstraintLayout mSafeDocCl;
    @BindView(R.id.safe_report_cl)
    ConstraintLayout mReportCl;
    @BindView(R.id.safe_weibo_iv)
    ImageView mWeiboIcon;
    @BindView(R.id.safe_weibo_tv)
    TextView mWeiboTv;
    @BindView(R.id.safe_emergency_bottom_hint_tv)
    TextView mEmergencyHintTv;

    @BindView(R.id.safe_contact_bg)
    ImageView mContactBgIv;
    @BindView(R.id.safe_contact_iv)
    ImageView mContactIv;
    @BindView(R.id.safe_contact_tv)
    TextView mContactTv;

    @BindView(R.id.safe_doc_bg)
    ImageView mDocBgIv;
    @BindView(R.id.safe_doc_iv)
    ImageView mDocIv;
    @BindView(R.id.safe_doc_tv)
    TextView mDocTv;

    @BindView(R.id.safe_first_ll)
    LinearLayout mFirstLl;
    @BindView(R.id.safe_second_ll)
    LinearLayout mSecondLl;

    private ImageView rightIcon;

    private boolean isInit;
    private ACache aCache;
    private final static int DEFAULT_STATE = 0;
    private final static int HAS_CONTACT_STATE = 1;
    private final static int EMERGENCY_STATE = 2;

    private float contentHeight = 0.40f;
    private float itemHeight = 0.18f;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_safe_new, container, false);
        ButterKnife.bind(this, view);
        InjectUtils.injectOnlyEvent(this);
        aCache = ACache.get(getActivity());
        setJewState();
        CommonUtils.setStateBar(getActivity(), false);
        getSafeState();
        isInit = true;
        try {
            handlerSafeType((SafeTypeBean) aCache.getAsObject(HTTP_INDEX_CACHE));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (PreferencesUtils.getBoolean(ToTwooApplication.baseContext, IS_IMEI_SENT, false)) {
            getExpireTime();
        }
        rightIcon = mNotifyTopBlackLayout.getRightIcon();

        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
            mGuideTv.setVisibility(View.VISIBLE);
            rightIcon.setOnClickListener(v -> {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.GUARD_CLICK_DESC);
                startHintInfoActivity();});
            rightIcon.setImageResource(R.drawable.icon_info_black);
            mContactBgIv.setVisibility(View.GONE);
            mContactIv.setVisibility(View.GONE);
            mContactTv.setVisibility(View.GONE);
            mDocBgIv.setVisibility(View.GONE);
            mDocIv.setVisibility(View.GONE);
            mDocTv.setVisibility(View.GONE);
            mFirstLl.setVisibility(View.VISIBLE);
            mSecondLl.setVisibility(View.VISIBLE);
            mReportCl.setVisibility(View.GONE);
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * contentHeight));
            layoutParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, 90), 0, 0);
            mSafeContentCl.setLayoutParams(layoutParams);

            RelativeLayout.LayoutParams contactParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * itemHeight));
            contactParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, -20), 0, 0);
            contactParams.addRule(RelativeLayout.BELOW, R.id.safe_content_cl);
            mSafeContactCl.setLayoutParams(contactParams);

            RelativeLayout.LayoutParams docParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * itemHeight));
            docParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, -5), 0, 0);
            docParams.addRule(RelativeLayout.BELOW, R.id.safe_contact_cl);
            mSafeDocCl.setLayoutParams(docParams);

            RelativeLayout.LayoutParams reportParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * itemHeight));
            reportParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, -5), 0, 0);
            reportParams.addRule(RelativeLayout.BELOW, R.id.safe_doc_cl);
            mReportCl.setLayoutParams(reportParams);
        } else {
            mContactBgIv.setVisibility(View.VISIBLE);
            mContactIv.setVisibility(View.VISIBLE);
            mContactTv.setVisibility(View.VISIBLE);
            mDocBgIv.setVisibility(View.VISIBLE);
            mDocIv.setVisibility(View.VISIBLE);
            mDocTv.setVisibility(View.VISIBLE);
            mFirstLl.setVisibility(View.GONE);
            mSecondLl.setVisibility(View.GONE);
            mReportCl.setVisibility(View.VISIBLE);
            contentHeight = 0.35f;
            itemHeight = 0.16f;
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * contentHeight));
            layoutParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, 90), 0, 0);
            mSafeContentCl.setLayoutParams(layoutParams);

            RelativeLayout.LayoutParams contactParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * itemHeight));
            contactParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, -32), 0, 0);
            contactParams.addRule(RelativeLayout.BELOW, R.id.safe_content_cl);
            mSafeContactCl.setLayoutParams(contactParams);

            RelativeLayout.LayoutParams docParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * itemHeight));
            docParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, -30), 0, 0);
            docParams.addRule(RelativeLayout.BELOW, R.id.safe_contact_cl);
            mSafeDocCl.setLayoutParams(docParams);

            RelativeLayout.LayoutParams reportParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) (CommonUtils.getRealHeight() * itemHeight));
            reportParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, -30), 0, 0);
            reportParams.addRule(RelativeLayout.BELOW, R.id.safe_doc_cl);
            mReportCl.setLayoutParams(reportParams);
        }

        Apputils.getGlobalExecutor().submit(() -> {
            mEmergencyLv.setImageAssetsFolder("lottie_safe_alarm/");
            mEmergencyLv.setAnimation("safe_alarm.json");
        });

        return view;
    }

    @OnClick({R.id.safe_add_em_cl, R.id.safe_guide_tv, R.id.safe_guard_fee_tv, R.id.safe_guard_cancel_tv, R.id.safe_contact_cl, R.id.safe_contact_cn_cl,
            R.id.safe_doc_cl, R.id.safe_doc_cn_cl, R.id.safe_weibo_cl, R.id.safe_report_cn_cl, R.id.safe_report_cl})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.safe_add_em_cl:
                startActivity(new Intent(getContext(), SecurityNewListActivity.class).putExtra(CommonArgs.FROM_TYPE, SecurityNewListActivity.ADD_STATUS));
                break;
            case R.id.safe_guide_tv:
//                WebViewActivity.loadUrl(getContext(), HttpHelper.HOSTURL_SAFE_INFO, false);
                startHintInfoActivity();
                break;
            case R.id.safe_guard_fee_tv:
//                startActivity(new Intent(ToTwooApplication.baseContext, SecurityPayActivity.class));
                break;
            case R.id.safe_guard_cancel_tv:
                cancelEmergency();
                break;
            case R.id.safe_contact_cl:
            case R.id.safe_contact_cn_cl:
                startActivity(new Intent(getContext(), SecurityNewListActivity.class).putExtra(CommonArgs.FROM_TYPE, SecurityNewListActivity.MANAGER_STATUS));
                break;
            case R.id.safe_doc_cl:
            case R.id.safe_doc_cn_cl:
                startActivity(new Intent(getContext(), EmergencyDocActivity.class));
                break;
            case R.id.safe_weibo_cl:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.GUARD_SET_WEIBO_SYNC);
                startActivity(new Intent(getContext(), WeiboAuthActivity.class));
                break;
            case R.id.safe_report_cn_cl:
            case R.id.safe_report_cl:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.SAFE_CLICK_SAFETY);
                startActivity(new Intent(getContext(), SecurityReportListActivity.class));
                break;
        }
    }

    private void startHintInfoActivity() {
        startActivity(new Intent(getContext(), SecurityHintActivity.class).putExtra(CommonArgs.FROM_TYPE, true));
    }

    @Override
    public void onShow() {
        if (isInit) {
            CommonUtils.setStateBar(getActivity(), false);
            getSafeState();
        }
    }

    private void getSafeState() {
        HttpHelper.safeService.getSafeState(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SafeTypeBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(getContext(), getString(R.string.error_net));
                        LogUtils.e("aab e = " + e);
                    }

                    @Override
                    public void onNext(HttpBaseBean<SafeTypeBean> safeTypeBeanHttpBaseBean) {
                        if (safeTypeBeanHttpBaseBean.getErrorCode() == 0) {
                            handlerSafeType(safeTypeBeanHttpBaseBean.getData());
//                            aCache.put(HTTP_INDEX_CACHE, safeTypeBeanHttpBaseBean.getData());
                        }
                    }
                });
        HttpHelper.safeService.getGuardState(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SafeTypeBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(HttpBaseBean<SafeTypeBean> safeTypeBeanHttpBaseBean) {
                        if (safeTypeBeanHttpBaseBean.getErrorCode() == 0) {
                            if (TextUtils.equals(safeTypeBeanHttpBaseBean.getData().getType(), "GUARD") && Long.valueOf(safeTypeBeanHttpBaseBean.getData().getSurplus_time()) > 0) {
                                ToTwooApplication.mService.startLocation(Long.valueOf(safeTypeBeanHttpBaseBean.getData().getSurplus_time()),safeTypeBeanHttpBaseBean.getData().getId());
                            }
                        }
                    }
                });
    }

    private void setJewState() {
        if (view == null) {
            return;
        }
        if (mNotifyTopBlackLayout == null) {
            mNotifyTopBlackLayout = view.findViewById(R.id.notify_top_black_layout);
        }
        mNotifyTopBlackLayout.setJewState();
    }

    private void getExpireTime() {
        String imei = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI, "");
        if (!TextUtils.isEmpty(imei)) {
            HttpHelper.safeService.showExpireTime(imei)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<SafeExpireTimeBean>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(ToTwooApplication.baseContext, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean<SafeExpireTimeBean> safeExpireTimeBeanHttpBaseBean) {
                            if (safeExpireTimeBeanHttpBaseBean.getErrorCode() == 0) {
                                try {
                                    showDateDialog(safeExpireTimeBeanHttpBaseBean.getData().getIs_show_first() == 1,
                                            Long.valueOf(safeExpireTimeBeanHttpBaseBean.getData().getExpire_time()),
                                            safeExpireTimeBeanHttpBaseBean.getData().getTxt_1(),
                                            safeExpireTimeBeanHttpBaseBean.getData().getTxt_2());
                                } catch (NumberFormatException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    });
        }
    }

    private void handlerSafeType(SafeTypeBean safeTypeBean) {
        String type = safeTypeBean.getType();
        if (TextUtils.equals(type, "N")) {
            setViewByState(DEFAULT_STATE);
            rightIcon.setVisibility(View.GONE);
        } else if (TextUtils.equals(type, "Y")) {
            setViewByState(HAS_CONTACT_STATE);
            setDays(safeTypeBean.getDays());
            if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                rightIcon.setVisibility(View.VISIBLE);
            } else {
                rightIcon.setVisibility(View.GONE);
            }
        } else if (TextUtils.equals(type, "HELP")) {
            setViewByState(EMERGENCY_STATE);
            setDays(safeTypeBean.getDays());
            if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                rightIcon.setVisibility(View.VISIBLE);
            } else {
                rightIcon.setVisibility(View.GONE);
            }
            mEmergencyLv.playAnimation();
            if (TextUtils.equals(safeTypeBean.getStatus(), "CALL_SUCCESS")) {
                mEmergencyInfoTv.setText(getString(R.string.safe_emergency_success, safeTypeBean.getSuccess_tel()));
                mEmergencyHintTv.setText(R.string.safe_bottom_end_hint);
            } else if (TextUtils.equals(safeTypeBean.getStatus(), "CALL_FAIL")) {
                mEmergencyInfoTv.setText(R.string.safe_emergency_fail);
                mEmergencyHintTv.setText(R.string.safe_bottom_end_hint_default);
            } else {
                mEmergencyInfoTv.setText(R.string.safe_emergency_sending);
                mEmergencyHintTv.setText(R.string.safe_bottom_end_hint_default);
            }
        }
        if (!TextUtils.isEmpty(safeTypeBean.getAccess_token()) && safeTypeBean.getIs_weibo() == 1) {
            PreferencesUtils.put(ToTwooApplication.baseContext, WeiboAuthActivity.WEIBO_TOKEN, safeTypeBean.getAccess_token());
            PreferencesUtils.put(ToTwooApplication.baseContext, WeiboAuthActivity.WEIBO_SHARE, safeTypeBean.getIs_weibo() == 1);
            PreferencesUtils.put(ToTwooApplication.baseContext, WeiboAuthActivity.WEIBO_EXPIRE_TIME, safeTypeBean.getToken_time());
            mWeiboIcon.setImageResource(R.drawable.safe_weibo_icon);
            mWeiboTv.setText(R.string.safe_emergency_close_weibo);
        } else {
            mWeiboIcon.setImageResource(R.drawable.safe_weibo_icon_none);
            mWeiboTv.setText(R.string.safe_emergency_set_weibo);
        }

    }

    private void setDays(String days) {
        try {
            mGuardDaysTv.setText(days);
        } catch (Exception e) {
            mGuardDaysTv.setText(1 + "");
        }
    }

    private void setViewByState(int state) {
        switch (state) {
            case DEFAULT_STATE:
                mDefaultViewCl.setVisibility(View.VISIBLE);
                mNormalRl.setVisibility(View.GONE);
                mEmergencyCl.setVisibility(View.GONE);
                CommonUtils.setStateBar(getActivity(), false);
                break;
            case HAS_CONTACT_STATE:
                mDefaultViewCl.setVisibility(View.GONE);
                mNormalRl.setVisibility(View.VISIBLE);
                mEmergencyCl.setVisibility(View.GONE);
                break;
            case EMERGENCY_STATE:
                mDefaultViewCl.setVisibility(View.GONE);
                mNormalRl.setVisibility(View.GONE);
                mEmergencyCl.setVisibility(View.VISIBLE);
                break;
        }
    }

    private void showDateDialog(boolean isInit, long time, String text, String info) {
        long millions = time * 1000;
        aCache.put(CommonArgs.IMEI_DEAD_LINE_TIME, millions + "",24 * 60 * 3600);
        if (isInit) {
            setFirstDateDialog(text, info);
        } else if (millions < System.currentTimeMillis()) {
            mFeeCl.setVisibility(View.VISIBLE);
        } else if (millions - System.currentTimeMillis() <= 7 * 24 * 3600 * 1000) {
            setNearlyDateDialog(CommonUtils.getFormatDate(millions, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)));
        }
    }

    private void setFirstDateDialog(String text, String info) {
        CommonMiddleDialog dateDialog = new CommonMiddleDialog(getContext());
        dateDialog.setTitle(getString(R.string.safe_security_fee_hint_title));
        LinearLayout linearLayout = new LinearLayout(getContext());
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        TextView tvInfo = new TextView(getContext());
        tvInfo.setText(text);
        tvInfo.setTextColor(getResources().getColor(R.color.text_color_black_33));
        tvInfo.setTextSize(15);
        tvInfo.setLineSpacing(0, 1.3f);
        layoutParams.setMargins(CommonUtils.dip2px(getContext(), 20), CommonUtils.dip2px(getContext(), 15), CommonUtils.dip2px(getContext(), 20), 0);
        tvInfo.setLayoutParams(layoutParams);
        linearLayout.addView(tvInfo);
        TextView tvHint = new TextView(getContext());
        tvHint.setText(getString(R.string.safe_security_fee_hint_info_server, info));
        tvHint.setTextColor(getResources().getColor(R.color.text_color_gray_99));
        tvHint.setTextSize(13);
        tvHint.setLineSpacing(0, 1.3f);
        layoutParams.setMargins(CommonUtils.dip2px(getContext(), 20), CommonUtils.dip2px(getContext(), 12), CommonUtils.dip2px(getContext(), 20), 0);
        tvHint.setLayoutParams(layoutParams);
        linearLayout.addView(tvHint);
        dateDialog.setCustomView(linearLayout);
        dateDialog.setSure(R.string.i_know, v -> dateDialog.dismiss());
        dateDialog.show();
    }

    private void setNearlyDateDialog(String dateString) {
//        CommonMiddleDialog nearlyDialog = new CommonMiddleDialog(getContext());
//        nearlyDialog.setInfo(getString(R.string.safe_security_fee_hint_deadline, dateString));
//        nearlyDialog.setSure(getString(R.string.safe_security_fee_hint_now), v -> startActivity(new Intent(getContext(), SecurityPayActivity.class)));
//        nearlyDialog.setCancel(getString(R.string.safe_security_fee_hint_later));
//        nearlyDialog.show();
    }

    private void showContactsInitDialog() {
        CommonMiddleDialog contactsDialog = new CommonMiddleDialog(getContext());
        contactsDialog.setInfo(getString(R.string.safe_contacts_success_hint));
        contactsDialog.setSure(getString(R.string.i_know), v -> contactsDialog.dismiss());
        contactsDialog.setOnDismissListener(dialog -> getExpireTime());
        contactsDialog.show();
    }

    private CommonMiddleDialog cancelEmergencyDialog;

    private void cancelEmergency() {
        if (cancelEmergencyDialog == null) {
            cancelEmergencyDialog = new CommonMiddleDialog(getContext());
            cancelEmergencyDialog.setMessage(R.string.safe_emergency_stop_title);
            cancelEmergencyDialog.setSure(v -> {
                HttpHelper.safeService.cancelEmergency(2001)
                        .compose(HttpHelper.rxSchedulerHelper())
                        .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {
                                ToastUtils.showShort(getContext(), R.string.error_net);
                            }

                            @Override
                            public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                                if (objectHttpBaseBean.getErrorCode() == 0) {
                                    mEmergencyLv.pauseAnimation();
                                    setViewByState(HAS_CONTACT_STATE);
                                    ToastUtils.showShort(getContext(), R.string.safe_emergency_stop_toast);
                                }
                            }
                        });
                cancelEmergencyDialog.dismiss();
            });
            cancelEmergencyDialog.setCancel(R.string.give_up);
        }
        cancelEmergencyDialog.show();
    }

    @Override
    public void onChange() {
        setJewState();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    public Handler mHandler;

    @EventInject(eventType = S.E.E_IMEI_UPDATE_SUCCEED, runThread = TaskType.UI)
    public void imeiUpdateSucceed(EventData data) {
        HttpHelper.safeService.getContacts(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<SecurityContactsHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(getContext(), getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
                        if (securityContactsHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            if (securityContactsHttpBeanHttpBaseBean.getData() != null && securityContactsHttpBeanHttpBaseBean.getData().getInfo() != null
                                    && securityContactsHttpBeanHttpBaseBean.getData().getInfo().size() > 0 && !PreferencesUtils.getBoolean(ToTwooApplication.baseContext, HAS_INIT_CONTACT, false)) {
                                PreferencesUtils.put(ToTwooApplication.baseContext, HAS_INIT_CONTACT, true);
                                showContactsInitDialog();
                            } else {
                                getExpireTime();
                            }
                        }
                    }
                });
//        Handler mHandler = new Handler();
//        mHandler.postDelayed(() -> {
//            BluetoothManage.getInstance().checkOTA();
//        }, 7200);

    }

    @EventInject(eventType = S.E.E_IMEI_CHARGE_SUCCEED, runThread = TaskType.UI)
    public void imeiChargeSucceed(EventData data) {
        String deadline_time = aCache.getAsString(CommonArgs.IMEI_DEAD_LINE_TIME);
        if (Long.valueOf(deadline_time) > System.currentTimeMillis()) {
            mFeeCl.setVisibility(View.GONE);
        }
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        setJewState();
    }

    @EventInject(eventType = S.E.E_SECURITY_STATE_CHANGED, runThread = TaskType.UI)
    public void onSecurityStateChanged(EventData data) {
        getSafeState();
    }


    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
