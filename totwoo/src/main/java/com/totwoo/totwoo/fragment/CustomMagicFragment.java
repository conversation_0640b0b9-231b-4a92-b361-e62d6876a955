package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.ToTwooApplication.baseContext;
import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_PART_VALUE;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.google.gson.Gson;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BrightModeActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.adapter.CustomAngleRecyclerViewAdapter;
import com.totwoo.totwoo.bean.ConstellationIndexBean;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.bean.GreetingCardInfo;
import com.totwoo.totwoo.bean.HomePageIndexInfo;
import com.totwoo.totwoo.bean.PeriodBean;
import com.totwoo.totwoo.bean.PeriodStateBean;
import com.totwoo.totwoo.bean.SleepEventData;
import com.totwoo.totwoo.bean.SleepPreTimeBean;
import com.totwoo.totwoo.bean.SleepTodayGetBean;
import com.totwoo.totwoo.bean.SleepUpdateBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.databinding.FragmentCustomMagicBinding;
import com.totwoo.totwoo.service.BrightMusicPlayService;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.CustomOrderDbHelper;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;
import com.totwoo.totwoo.widget.pickerview.listener.OnDateSetListener;
import com.umeng.analytics.MobclickAgent;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;

import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/9/18.
 */

public class CustomMagicFragment extends BaseFragment implements
        SubscriberListener, HomeBaseActivity.JewelryStateChangeListener, BluetoothManage.WriteSuccessListener, CustomAngleRecyclerViewAdapter.ItemTypeProvider {
    final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

    private CustomAngleRecyclerViewAdapter mRecyclerViewAdapter;
    private static final String MAGIC_HINT_SHOW = "magic_hint_show";
    private Context mContext;
    private int finalIndex;
    private ArrayList<CustomOrderBean> customOrderBeans;

    private boolean isInit = false;
    public static final String CACHE_MAGIC_IMG_NEW = "cache_magic_img_new";
    public static final String CACHE_MAGIC_JUMP_URL = "cache_magic_jump_url";

    private FragmentCustomMagicBinding binding;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (container == null) {
            LogUtils.e("topLayer container is null");
        }
        binding = FragmentCustomMagicBinding.inflate(inflater, container, false);
        EdgeToEdgeUtils.setupBottomInsetsForLoveHomeAct(binding.getRoot());
        mContext = ToTwooApplication.baseContext;
        InjectUtils.injectOnlyEvent(this);
        try {
            initContentView();
        } catch (DbException e) {
            e.printStackTrace();
        }
        getInfo();
        mContext.startService(new Intent(mContext, BrightMusicPlayService.class));
        isInit = true;
        setTopLayout();
        BluetoothManage.getInstance().setWriteSuccessListener(this);
        return binding.getRoot();
    }


    private void initContentView() throws DbException {
        binding.customAngelFragmentRecyclerview.setLayoutManager(new LinearLayoutManager(getActivity()));
        getCustomOrderBean();
        if (BleParams.isNoFlashJewelry()) {
            mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), binding.customAngelFragmentRecyclerview, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_REMINDER);
        } else {
            mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), binding.customAngelFragmentRecyclerview, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_MAGIC);
        }
        binding.customAngelFragmentRecyclerview.setAdapter(mRecyclerViewAdapter);


        // 监听 RecyclerView 的滑动事件
        binding.customAngelFragmentRecyclerview.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                // 获取 RecyclerView 滑动的距离
                int scrollY = recyclerView.computeVerticalScrollOffset();

                // 根据滑动距离计算透明度
                int alpha = calculateAlpha(scrollY);


                // 设置顶部状态栏和标题栏的背景色
//                binding.magicTopLayout.setAlpha(alpha);
            }
        });


    }

    // 计算透明度的方法
    private int calculateAlpha(int scrollY) {
        int maxHeight = 300; // 顶部状态栏和标题栏透明的最大高度

        // 根据滑动距离计算透明度
        int alpha = (int) (255 * ((float) (1 - scrollY / maxHeight)));
        // 控制透明度范围在 0 到 255 之间
        return Math.min(Math.max(alpha, 0), 255);
    }

    private void getCustomOrderBean() throws DbException {
        String jewName = PreferencesUtils.getString(mContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (TextUtils.isEmpty(jewName)) {
            binding.emptyView.setVisibility(View.VISIBLE);
        } else {
            binding.emptyView.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(jewName)) {
            customOrderBeans = new ArrayList<>();
        } else if (BleParams.isMemoryJewelry()) {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, 2);
        } else if (BleParams.isWishJewlery()) {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, 3);
        } else if (BleParams.isLoveLetter()) {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, 6);
        } else if (BleParams.isButtonBatteryJewelry()) {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(6);
        } else if (BleParams.isCodeBangle()) {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, 5);
        } else if (BleParams.isCodeJewelry()) {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, 4);
        } else if (BleParams.isSM2() || BleParams.isCtJewlery()) {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(9);
        } else {
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, BleParams.isPendant(PreferencesUtils.getString(mContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "")) ? 0 : 1);
        }
    }



    private void setTopLayout() {
//        binding.magicTopLayout.setBuringLayerView(binding.customAngelFragmentRecyclerview);
//        binding.magicTopLayout.setJewState();
    }

    private void flashChange() {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(requireActivity(), R.string.error_jewelry_connect);
            return;
        }
        // 表示当前灯光模式的状态, 0 表示从未开启, 1~9 表示9中模式正在开启中,
        // -1 ~ -9 表示当前关闭, 上次开启的为某个模式
        int index = PreferencesUtils.getInt(mContext, COLOR_VALUE, 1);
        int musicIndex = PreferencesUtils.getInt(mContext, MUSIC_PART_VALUE, 0);
        if (index < 0) {
            index = -index;
        }
        finalIndex = index;
        if (!CommonUtils.jewelryFlashOpen(requireContext())) {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.OPEN_FLASH);
            if (BleParams.isNoneMusicJewelry() || finalIndex == 0 || musicIndex == 0) {
                BluetoothManage.getInstance().changeBirghtMode(NotifyUtil.getColorValue(BrightModeActivity.BrightModeListDataBean.dataBeans.get(index - 1).getFlashColorValue()), true);
            } else {
                BluetoothManage.getInstance().changeMusicBrightMode(7, NotifyUtil.getColorValue(getFlashColorValue(finalIndex - 1)), true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_PLAY_PART, null);
            }
        } else {
            if (BleParams.isNoneMusicJewelry() || finalIndex == 0 || musicIndex == 0) {
                BluetoothManage.getInstance().changeBirghtMode(-1, true);
            } else {
                BluetoothManage.getInstance().changeMusicBrightMode(musicIndex, -1, true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
            }
        }
    }

    /**
     * 顶部标签页点击闪光
     * CustomAngelPullHolder
     */
    @EventInject(eventType = S.E.E_HOLDER_FLASH_CHANGE, runThread = TaskType.UI)
    public void onLightClickReceiver(EventData data) {
        flashChange();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
        if (mRecyclerViewAdapter != null) {
            mRecyclerViewAdapter.recovery();
        }
        // 清理 BluetoothManage 监听器，防止内存泄露
        BluetoothManage.getInstance().setWriteSuccessListener(null);
    }

    @Override
    public void onWriteSuccessed() {
        if (getContext() == null) {
            return;
        }
        boolean open = CommonUtils.jewelryFlashOpen(requireContext());

        if (open) {
            PreferencesUtils.put(mContext, COLOR_VALUE, -finalIndex);
        } else {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_FLASH_TURN_ON);
            PreferencesUtils.put(mContext, COLOR_VALUE, finalIndex);
        }
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_FLASH_CHANGED, null);
    }

    /**
     * 标签页
     * CustomOrderActivity
     */
    @EventInject(eventType = S.E.E_CUSTOM_ORDER_UPDATE, runThread = TaskType.UI)
    public void onOrderUpdateReceiver(EventData data) {
        fileData();
    }

//    @EventInject(eventType = S.E.E_CONNECT_NEW_DEVICES, runThread = TaskType.UI)
//    public void connectNewDevices(NotifyMessage mesage) {
//        fileData();
//    }


    private void fileData() {
        try {
            LogUtils.e("aab E_CUSTOM_ORDER_UPDATE receiver");
            getCustomOrderBean();
            if (BleParams.isNoFlashJewelry()) {
                mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), binding.customAngelFragmentRecyclerview, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_REMINDER);
            } else {
                mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), binding.customAngelFragmentRecyclerview, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_MAGIC);
            }
            binding.customAngelFragmentRecyclerview.setAdapter(mRecyclerViewAdapter);

        } catch (DbException e) {
            e.printStackTrace();
        }
    }

//    private ImageChangeHandler imageChangeHandler = new ImageChangeHandler();

    @Override
    public void onShow() {
//        setJewState();
        MobclickAgent.onPageStart(TrackEvent.MAGIC_PAGE);
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.HOMEPAGE_BOTTOM_MAGIC);

        if (!TextUtils.isEmpty(PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "")) && !PreferencesUtils.getBoolean(ToTwooApplication.baseContext, MAGIC_HINT_SHOW, false)) {
            PreferencesUtils.put(ToTwooApplication.baseContext, MAGIC_HINT_SHOW, true);
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_MAGIC_HINT, null);
        }

        if (BleParams.isCodeBangle()) {
            if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
                updateSleepState();
            } else {
                getSleepTime();
            }
        }

        if (isInit) {
            getInfo();
        }
        if (getActivity() != null) {
            // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置
            // 修复三星手机非全面屏底部内容被遮挡问题
            setupBottomPaddingForSamsung();
        }
    }

    @Override
    public void onChange() {
//        setJewState();
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CHANGE)
    public void notifyJewelryState(EventData data) {
        //设备电量变化不做刷新
        if (data != null) {
            return;
        }
        fileData();
        setJewState();
        if (isUpdating && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            isUpdating = false;
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOADING_END, null);
            ToastUtils.showShort(mContext, R.string.error_jewelry_connect);
        }
    }

    //旧逻辑
    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
    }

    private void setJewState() {
//        binding.magicTopLayout.setJewState();
    }

    private void getInfo() {
        getPeriodState();
        getConstellationIndex();
        getGreetingCardInfo();
        String cache_img = ACache.get(mContext).getAsString(CACHE_MAGIC_IMG_NEW);
        if (TextUtils.isEmpty(cache_img)) {
            getTopImage();
        }
    }

    private void getConstellationIndex() {
        HttpHelper.commonService.getConstellationIndex()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<ConstellationIndexBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<ConstellationIndexBean> constellationIndexBeanHttpBaseBean) {
                        if (constellationIndexBeanHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.constellationIndexBeanHttpBaseBean = constellationIndexBeanHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CUSTOM_NOTIFY, constellationIndexBeanHttpBaseBean.getData());
                        }
                    }
                });
    }

    private void getPeriodState() {
        HttpHelper.periodSave.getPeriodState()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<PeriodStateBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodStateBean> periodStateBeanHttpBaseBean) {
                        if (periodStateBeanHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.periodStateBean = periodStateBeanHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_PERIOD, periodStateBeanHttpBaseBean.getData());
                        } else if (periodStateBeanHttpBaseBean.getErrorCode() == 4) {
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_PERIOD_OFF, null);
                        }
                    }
                });
    }

    private void getTopImage() {
        HttpHelper.commonService.homePageIndex("magic")
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<HomePageIndexInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<HomePageIndexInfo> homePageIndexInfoHttpBaseBean) {
                        if (homePageIndexInfoHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.homePageIndexInfo = homePageIndexInfoHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_TOP_INDEX, homePageIndexInfoHttpBaseBean.getData());
                            ACache.get(mContext).put(CACHE_MAGIC_IMG_NEW, homePageIndexInfoHttpBaseBean.getData().getImg_url_new(), 6 * 3600);
                            ACache.get(mContext).put(CACHE_MAGIC_JUMP_URL, homePageIndexInfoHttpBaseBean.getData().getJump_url(), 6 * 3600);
                        }
                    }
                });
    }

    private void getGreetingCardInfo() {
        HttpHelper.commonService.getGreetingCardInfo()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<GreetingCardInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<GreetingCardInfo> greetingCardInfoBeanHttpBaseBean) {
                        if (greetingCardInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            if (greetingCardInfoBeanHttpBaseBean.getData().getIs_greetingcard() == 1) {
                                CustomAngleRecyclerViewAdapter.hasGreetingCardList = true;
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CARD_YES, null);
                            } else {
                                CustomAngleRecyclerViewAdapter.hasGreetingCardList = false;
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CARD_NO, null);
                            }
                        }
                    }
                });
    }

    /**
     * 大姨妈标签页点击时候的弹窗
     * HomePeroidHolder
     */
    @EventInject(eventType = S.E.E_HOLDER_PERIOD_CHANGE, runThread = TaskType.UI)
    public void onPeriodInfoReceiver(EventData data) {
        showBirthDialog();
    }

    private void showBirthDialog() {
        String periodDay = owner.getPeriodDay();
        if (TextUtils.isEmpty(periodDay))
            periodDay = format.format(System.currentTimeMillis());

        long periodTime;
        try {
            periodTime = format.parse(periodDay).getTime();
        } catch (ParseException e) {
            periodTime = System.currentTimeMillis();
        }

        long tenYears = 1L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        String tempBirthday = format.format(millseconds);
                        owner.setPeriodDay(tempBirthday);
                        HttpHelper.periodSave.updatePeriod(tempBirthday)
                                .subscribeOn(Schedulers.newThread())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Observer<HttpBaseBean<PeriodBean>>() {
                                    @Override
                                    public void onCompleted() {
                                    }

                                    @Override
                                    public void onError(Throwable e) {

                                    }

                                    @Override
                                    public void onNext(HttpBaseBean<PeriodBean> periodBeanHttpBaseBean) {
                                        if (periodBeanHttpBaseBean.getErrorCode() == 0) {
                                            getPeriodState();
                                        }
                                    }
                                });
                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.period_setting_start_dialog))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - tenYears)
                .setMaxMillseconds(System.currentTimeMillis())
                .setCurrentMillseconds(System.currentTimeMillis())
                .setThemeColor(mContext.getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(mContext.getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(mContext.getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getChildFragmentManager(), "year_month_day");
    }

    private long currentMillions;

    private void updateSleepState() {
        long lastUpdateTime = PreferencesUtils.getLong(baseContext, CommonArgs.UPDATE_SLEEP_TIME, 0);
        currentMillions = System.currentTimeMillis();
        if (currentMillions - lastUpdateTime > 3900 * 1000) {
            HttpHelper.sleepDataService.getPrevTime()
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Observer<HttpBaseBean<SleepPreTimeBean>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<SleepPreTimeBean> sleepPreTimeBeanHttpBaseBean) {
                            long millions = sleepPreTimeBeanHttpBaseBean.getData().getPrev_time() * 1000;
                            long gapMillions = currentMillions - millions;
                            LogUtils.e("sleepData gapMillions / 60000 = " + gapMillions / 60000);
                            if (gapMillions / 60000 < 65) {
                                getSleepTime();
                                return;
                            }
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOADING_START, null);
                            isUpdating = true;
                            if (millions == 0) {
                                BluetoothManage.getInstance().writeSleepMessage(2880);
                            } else {
                                BluetoothManage.getInstance().writeSleepMessage(Math.min(2880, (int) (gapMillions / 60000)));
                            }
                            updateHandler.postDelayed(() -> updateSleepMessageTimeout(), 200000);
                        }
                    });

        } else {
            getSleepTime();
        }
    }

    private UpdateHandler updateHandler = new UpdateHandler();

    private class UpdateHandler extends Handler {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
        }
    }

    private boolean isUpdating = false;

    private void updateSleepMessageTimeout() {
        if (isUpdating) {
            isUpdating = false;
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOADING_END, null);
            PreferencesUtils.put(baseContext, CommonArgs.UPDATE_SLEEP_TIME, currentMillions);
            ToastUtils.showShort(baseContext, R.string.error_timeout);
        }
    }

    @EventInject(eventType = S.E.E_SLEEP_DATA_RECEIVE, runThread = TaskType.UI)
    public void onEventSleepDataReceive(EventData data) {
        SleepEventData sleepEventData = (SleepEventData) data;
        ArrayList<SleepUpdateBean> updateBeans = sleepEventData.getUpdateBeans();
        if (updateBeans == null || updateBeans.size() == 0) {
            isUpdating = false;
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOADING_END, null);
            PreferencesUtils.put(baseContext, CommonArgs.UPDATE_SLEEP_TIME, currentMillions);
            return;
        }
        Gson gson = new Gson();
        LogUtils.e("sleepData gson.toJson(updateBeans) = " + gson.toJson(updateBeans));
        HttpHelper.sleepDataService.updateSleepData(gson.toJson(updateBeans))
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        isUpdating = false;
                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOADING_END, null);
                        ToastUtils.showShort(mContext, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        isUpdating = false;
                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOADING_END, null);
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            PreferencesUtils.put(baseContext, CommonArgs.UPDATE_SLEEP_TIME, currentMillions);
                            getSleepTime();
                        }
                    }
                });
    }

    private void getSleepTime() {
        HttpHelper.sleepDataService.getTodayData()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SleepTodayGetBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<SleepTodayGetBean> sleepTodayGetBeanHttpBaseBean) {
                        if (sleepTodayGetBeanHttpBaseBean.getErrorCode() == 0) {
                            int total_min = sleepTodayGetBeanHttpBaseBean.getData().getTotal_sleep();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_SLEEP_TIME, sleepTodayGetBeanHttpBaseBean.getData());
                            LogUtils.e("sleepData total_min = " + total_min);
                        }
                    }
                });
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    private String getFlashColorValue(int position) {
        String str = null;
        switch (position) {
            case 0:
                str = "COLORFUL";
                break;
            case 1:
                str = "PINK";
                break;
            case 2:
                str = "RED";
                break;
            case 3:
                str = "ORANGE";
                break;
            case 4:
                str = "YELLOW";
                break;
            case 5:
                str = "GREEN";
                break;
            case 6:
                str = "CYAN";
                break;
            case 7:
                str = "BLUE";
                break;
            case 8:
                str = "PURPLE";
                break;
            case 9:
                str = "WHITE";
                break;
        }
        return str;
    }

    public boolean hasItemByType(int itemType) {
        if (mRecyclerViewAdapter != null) {
            return mRecyclerViewAdapter.hasItemByType(itemType);
        }
        return false;
    }

    /**
     * 为三星手机非全面屏设备设置精确的底部内边距
     * 修复底部内容被遮挡问题
     *
     * 计算方式：Tab高度(56dp) + 系统导航栏高度
     */
    private void setupBottomPaddingForSamsung() {
        if (getActivity() == null || binding == null || binding.getRoot() == null) {
            return;
        }

        // 使用ViewTreeObserver确保在布局完成后执行
        binding.getRoot().getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                // 移除监听器避免重复调用
                binding.getRoot().getViewTreeObserver().removeOnGlobalLayoutListener(this);

                // 获取系统导航栏高度
                int navigationBarHeight = getNavigationBarHeight();

                // Tab栏固定高度 56dp
                int tabHeight = (int) (56 * getResources().getDisplayMetrics().density);

                // 总的底部内边距 = Tab高度 + 导航栏高度
                int totalBottomPadding = tabHeight + navigationBarHeight;

                // 设置底部内边距
                binding.getRoot().setPadding(
                    binding.getRoot().getPaddingLeft(),
                    binding.getRoot().getPaddingTop(),
                    binding.getRoot().getPaddingRight(),
                    totalBottomPadding
                );
            }
        });
    }

    /**
     * 获取系统导航栏高度
     */
    private int getNavigationBarHeight() {
        if (getActivity() == null) {
            return 0;
        }

        int resourceId = getResources().getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return getResources().getDimensionPixelSize(resourceId);
        }
        return 0;
    }

}
