package com.totwoo.totwoo.activity.heart;

import com.etone.framework.utils.JSONUtils;
import com.totwoo.totwoo.utils.CommonArgs;

/**
 * Created by xinyoulingxi on 2017/9/22.
 */

public class NewHeartHomeBean
{
    /*背景*/
    public String background;

    /*在一起天数*/
    public int together_data;

    /*第一次配对*/
    public String first_couple;

    /*第一次心有灵犀*/
    public String first_consonance;

    /*心有灵犀次数*/
    public int consonance_count;

    public userinfo[] userinfos = new userinfo[2];
    public constellation[] constellations = new constellation[2];

    public static String lastMsgId;

    public NewHeartHomeBean()
    {
        userinfos[0] = new userinfo();
        userinfos[1] = new userinfo();
    }

    public NewHeartHomeBean(String json)
    {
        background = JSONUtils.getString(json, "background", "");
        if (!this.background.contains("http://"))
            this.background = CommonArgs.COMMEN_IMAGE_URL_HEAD + this.background;
        together_data = JSONUtils.getInt(json, "together_data", 0);
        first_couple = JSONUtils.getString(json, "first_couple", "");
        first_consonance = JSONUtils.getString(json, "first_consonance", "0");
        consonance_count = JSONUtils.getInt(json, "consonance_count", 0);

        String tmp = JSONUtils.getString(json, "userinfo", "");
        userinfos[0] = new userinfo(JSONUtils.getString(tmp, "self", ""));
        userinfos[1] = new userinfo(JSONUtils.getString(tmp, "target", ""));

        tmp = JSONUtils.getString(json, "constellation", "");
        constellations[0] = new constellation(JSONUtils.getString(tmp, "self", ""));
        constellations[1] = new constellation(JSONUtils.getString(tmp, "target", ""));
    }

    public static class userinfo
    {
        public String totwoo_id;
        public String head_portrait;
        public String nick_name;
        public String birthday;
        public int birthday_day;
        public int sex;

        public userinfo()
        {

        }

        public userinfo(String json)
        {
            totwoo_id = JSONUtils.getString(json, "totwoo_id", "");
            head_portrait = JSONUtils.getString(json, "head_portrait", "");
            nick_name = JSONUtils.getString(json, "nick_name", "");
            birthday = JSONUtils.getString(json, "birthday", "");
            birthday_day = JSONUtils.getInt(json, "birthday_day", -1);
            sex = Integer.parseInt(JSONUtils.getString(json, "sex", ""));
        }
    }

    public static class constellation
    {
        public String name;
        public String name_en;
        public String summary;
        public String summary_en;
        public String all;

        public constellation(String json)
        {
            name = JSONUtils.getString(json, "name", "");
            name_en = JSONUtils.getString(json, "name_en", "");
            summary = JSONUtils.getString(json, "summary", "");
            summary_en = JSONUtils.getString(json, "summary_en", "");
            all = JSONUtils.getString(json, "all", "");
        }
    }
}
