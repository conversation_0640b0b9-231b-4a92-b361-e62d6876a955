package com.totwoo.totwoo.activity;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.splashscreen.SplashScreen;

import com.airbnb.lottie.LottieAnimationView;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.CustomOrderDbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.widget.CustomMiddleInfoDialog;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Subscriber;

/**
 * 欢迎界面， 每次应用启动的呈现的界面， 为保证应用启动流畅度， 此 Activity 主线程中工作要尽可能的少</br> 前期设计底部视频动画， 顶层
 * logo 动画过渡</br>
 * <p>
 * 注：如需更改启动动画， 直接更换 raw 目录视频文件</br> 如果当前有引导页更新， 手动增加引导页版本号 GUIDANCE_VERSION
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class WelcomeActivity extends BaseActivity {

    public static final String PERMISSION_CHECKED = "permission_checked";

    @BindView(R.id.welcome_bg)
    LottieAnimationView bgView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);
        super.onCreate(savedInstanceState);


        // 如果应用尚未被杀掉, 就会直接跳转首页, 不在做初始化工作
        if (ToTwooApplication.isForeground && CommonUtils.isLogin()) {
//            Resources resources = getResources();
//            DisplayMetrics dm = resources.getDisplayMetrics();
//            Configuration config = resources.getConfiguration();
//            Locale locale = getResources().getConfiguration().locale;
//            String language = locale.getLanguage();
//            if (TextUtils.equals(language, "zh")) {
//                config.locale = Locale.SIMPLIFIED_CHINESE;
//                resources.updateConfiguration(config, dm);
//            }
            jump();
        } else {
            setContentView(R.layout.activity_welcome);

            // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

            ButterKnife.bind(this);
            Apputils.getGlobalExecutor().submit(this::initParams);
            setSpinState(false);

            if (!PreferencesUtils.getBoolean(WelcomeActivity.this, PERMISSION_CHECKED, false)) {
                final CustomMiddleInfoDialog customMiddleInfoDialog = new CustomMiddleInfoDialog(WelcomeActivity.this);
                customMiddleInfoDialog.setTitleTv(R.string.splash_permission_hint_tips);
                View view = LayoutInflater.from(this).inflate(R.layout.view_permission_layout, null);
                customMiddleInfoDialog.setCustomLinearLayout(view);
                customMiddleInfoDialog.setCanceledOnTouchOutside(false);
                // 屏蔽返回键
                customMiddleInfoDialog.setOnKeyListener((dialog, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
                customMiddleInfoDialog.setPrivacyDescription();
//                customMiddleInfoDialog.setTerms(v -> WebViewActivity.loadUrl(WelcomeActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_PRIVATE_POLICY), false),
//                        v -> WebViewActivity.loadUrl(WelcomeActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_PRIVATE), false),
//                        v -> WebViewActivity.loadUrl(WelcomeActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_DISCLAIMER), false));
                customMiddleInfoDialog.setConfirmTv(getString(R.string.confirm), v -> {
//                    if (!customMiddleInfoDialog.getTermsCb()) {
//                        Toast.makeText(WelcomeActivity.this, R.string.private_policy_hint, Toast.LENGTH_LONG).show();
//                        return;
//                    }
                    ((ToTwooApplication) ToTwooApplication.baseContext).doAfterTermAgree();
                    Apputils.getGlobalExecutor().submit(this::goNext);

                    PreferencesUtils.put(WelcomeActivity.this, PERMISSION_CHECKED, true);
                    customMiddleInfoDialog.dismiss();
                });
                customMiddleInfoDialog.setDenyTv(getString(R.string.cancel), v -> {
                    System.exit(0);
                });

                customMiddleInfoDialog.show();
            } else {
                Apputils.getGlobalExecutor().submit(this::goNext);
            }
        }

    }

    private void initParams() {
//        Resources resources = getResources();
//        DisplayMetrics dm = resources.getDisplayMetrics();
//        Configuration config = resources.getConfiguration();
//        Locale locale = getResources().getConfiguration().locale;
//        String language = locale.getLanguage();
//        if (TextUtils.equals(language, "zh")) {
//            config.locale = Locale.SIMPLIFIED_CHINESE;
//            resources.updateConfiguration(config, dm);
//        }
        initCustomOrder();
        initJewelryPaired();
    }

    private void initJewelryPaired() {
        List<LocalJewelryInfo> infos;
        String name = PreferencesUtils.getString(WelcomeActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        String mac_address = PreferencesUtils.getString(WelcomeActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, "");
        try {
            infos = LocalJewelryDBHelper.getInstance().getAllBeans();
            if (!TextUtils.isEmpty(name) && !TextUtils.isEmpty(mac_address) && infos.size() == 0) {
                LocalJewelryInfo info = new LocalJewelryInfo(mac_address, name, 1, System.currentTimeMillis());
                LocalJewelryDBHelper.getInstance().addBean(info);
                HttpHelper.multiJewelryService.addJewelry(BleParams.isNfcJewelry(name)?mac_address:"",mac_address, name, "", "", 1, "")
                        .compose(HttpHelper.rxSchedulerHelper())
                        .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {

                            }

                            @Override
                            public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {

                            }
                        });
            }
            LocalJewelryInfo selectInfo = LocalJewelryDBHelper.getInstance().getSelectedBean();
            if (TextUtils.isEmpty(name) && infos.size() > 0 && !TextUtils.isEmpty(selectInfo.getName())) {
                PreferencesUtils.put(WelcomeActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, selectInfo.getMac_address());
                PreferencesUtils.put(WelcomeActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, selectInfo.getName());
                PreferencesUtils.put(WelcomeActivity.this, BleParams.PAIRED_FIRST_CONNECT, true);
                BluetoothManage.getInstance().startBackgroundScan();
//             JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
            }
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    private void initCustomOrder() {
        //清空数据的测试代码
//        CustomOrderDbHelper.getInstance().deleteType(1);
//        CustomOrderDbHelper.getInstance().deleteType(2);
        if (!PreferencesUtils.getBoolean(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_DATA_INIT, false)) {
            ArrayList<CustomOrderBean> beans = new ArrayList<>();
            beans.add(new CustomOrderBean(0, NotifyUtil.FORTUNE_TYPE, 1));
            beans.add(new CustomOrderBean(1, NotifyUtil.CAMERA_TYPE, 1));
            beans.add(new CustomOrderBean(2, NotifyUtil.PERIOD_TYPE, 1));
            beans.add(new CustomOrderBean(3, NotifyUtil.QIAN_TYPE, 1));
            beans.add(new CustomOrderBean(4, NotifyUtil.CALL_TYPE, 1));
            beans.add(new CustomOrderBean(5, NotifyUtil.SEDENTARY_TYPE, 1));
            beans.add(new CustomOrderBean(6, NotifyUtil.APP_TYPE, 1));
            beans.add(new CustomOrderBean(7, NotifyUtil.WATER_TYPE, 1));
            beans.add(new CustomOrderBean(8, NotifyUtil.STEP_TYPE, 1));
            beans.add(new CustomOrderBean(9, NotifyUtil.MEMO_TYPE, 1));
            beans.add(new CustomOrderBean(10, NotifyUtil.SERCET_TYPE, 1));

            beans.add(new CustomOrderBean(0, NotifyUtil.FORTUNE_TYPE, 2));
            beans.add(new CustomOrderBean(1, NotifyUtil.SLEEP_TYPE, 2));
            beans.add(new CustomOrderBean(2, NotifyUtil.CAMERA_TYPE, 2));
            beans.add(new CustomOrderBean(3, NotifyUtil.PERIOD_TYPE, 2));
            beans.add(new CustomOrderBean(4, NotifyUtil.MEMORY_TYPE, 2));
            beans.add(new CustomOrderBean(5, NotifyUtil.QIAN_TYPE, 2));
            beans.add(new CustomOrderBean(6, NotifyUtil.CALL_TYPE, 2));
            beans.add(new CustomOrderBean(7, NotifyUtil.SEDENTARY_TYPE, 2));
            beans.add(new CustomOrderBean(8, NotifyUtil.APP_TYPE, 2));
            beans.add(new CustomOrderBean(9, NotifyUtil.STEP_TYPE, 2));
            beans.add(new CustomOrderBean(10, NotifyUtil.SERCET_TYPE, 2));

            beans.add(new CustomOrderBean(0, NotifyUtil.FORTUNE_TYPE, 3));
            beans.add(new CustomOrderBean(1, NotifyUtil.PERIOD_TYPE, 3));
            beans.add(new CustomOrderBean(2, NotifyUtil.QIAN_TYPE, 3));
            beans.add(new CustomOrderBean(3, NotifyUtil.CALL_TYPE, 3));
            beans.add(new CustomOrderBean(4, NotifyUtil.SEDENTARY_TYPE, 3));
            beans.add(new CustomOrderBean(5, NotifyUtil.APP_TYPE, 3));
            beans.add(new CustomOrderBean(6, NotifyUtil.STEP_TYPE, 3));
            beans.add(new CustomOrderBean(7, NotifyUtil.SERCET_TYPE, 3));

//            beans.add(new CustomOrderBean(0, NotifyUtil.PERIOD_TYPE,4));
//            beans.add(new CustomOrderBean(1, NotifyUtil.CALL_TYPE,4));
//            beans.add(new CustomOrderBean(2, NotifyUtil.SEDENTARY_TYPE,4));
//            beans.add(new CustomOrderBean(3, NotifyUtil.APP_TYPE,4));
//            beans.add(new CustomOrderBean(4, NotifyUtil.WATER_TYPE,4));
//            beans.add(new CustomOrderBean(5, NotifyUtil.STEP_TYPE,4));
//            beans.add(new CustomOrderBean(6, NotifyUtil.MEMO_TYPE,4));

            CustomOrderDbHelper.getInstance().addAllBeans(beans);

            PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_ADD_SLEEP, true);
            PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_ADD_PERIOD, true);
            PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_DATA_INIT, true);
        }
        if (!PreferencesUtils.getBoolean(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_MW, false)) {
            ArrayList<CustomOrderBean> beans = new ArrayList<>();
            beans.add(new CustomOrderBean(0, NotifyUtil.PERIOD_TYPE, 5));
            beans.add(new CustomOrderBean(1, NotifyUtil.CALL_TYPE, 5));
            beans.add(new CustomOrderBean(2, NotifyUtil.SEDENTARY_TYPE, 5));
            beans.add(new CustomOrderBean(3, NotifyUtil.APP_TYPE, 5));
            beans.add(new CustomOrderBean(4, NotifyUtil.WATER_TYPE, 5));
            beans.add(new CustomOrderBean(5, NotifyUtil.STEP_TYPE, 5));
            beans.add(new CustomOrderBean(6, NotifyUtil.MEMO_TYPE, 5));

            CustomOrderDbHelper.getInstance().addAllBeans(beans);

            PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_MW, true);
        }
        if (!PreferencesUtils.getBoolean(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_ADD_SLEEP, false)) {
            try {
                ArrayList<CustomOrderBean> customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, 4);
                CustomOrderBean bean = new CustomOrderBean(customOrderBeans.size(), NotifyUtil.SLEEP_TYPE, 2);
                ArrayList<CustomOrderBean> addBeans = new ArrayList<>();
                addBeans.add(bean);
                CustomOrderDbHelper.getInstance().addAllBeans(addBeans);
                PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_ADD_SLEEP, true);
            } catch (DbException e) {
                e.printStackTrace();
            }
        }

        if (!PreferencesUtils.getBoolean(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_BUTTON_BATTERY, false)) {
            ArrayList<CustomOrderBean> beans = new ArrayList<>();
            beans.add(new CustomOrderBean(0, NotifyUtil.FORTUNE_TYPE, 6));
            beans.add(new CustomOrderBean(1, NotifyUtil.PERIOD_TYPE, 6));
            beans.add(new CustomOrderBean(2, NotifyUtil.QIAN_TYPE, 6));
            beans.add(new CustomOrderBean(3, NotifyUtil.CALL_TYPE, 6));
            beans.add(new CustomOrderBean(4, NotifyUtil.SERCET_TYPE, 6));

            CustomOrderDbHelper.getInstance().addAllBeans(beans);

            PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_ADD_PERIOD, true);
            PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_BUTTON_BATTERY, true);
        }

        if (!PreferencesUtils.getBoolean(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_ADD_PERIOD, false)) {
            try {
                ArrayList<CustomOrderBean> customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(2, 4);
                ArrayList<CustomOrderBean> addBeans = new ArrayList<>();
                addBeans.add(new CustomOrderBean(customOrderBeans.size(), NotifyUtil.PERIOD_TYPE, 2));
                addBeans.add(new CustomOrderBean(customOrderBeans.size(), NotifyUtil.PERIOD_TYPE, 3));
                CustomOrderDbHelper.getInstance().addAllBeans(addBeans);
                PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_ADD_PERIOD, true);
            } catch (DbException e) {
                e.printStackTrace();
            }
        }

        if (!PreferencesUtils.getBoolean(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_SM2, false)) {
            ArrayList<CustomOrderBean> beans = new ArrayList<>();
            beans.add(new CustomOrderBean(0, NotifyUtil.SERCET_TYPE, 9));
            beans.add(new CustomOrderBean(1, NotifyUtil.CALL_TYPE, 9));
            beans.add(new CustomOrderBean(2, NotifyUtil.FORTUNE_TYPE, 9));
            beans.add(new CustomOrderBean(3, NotifyUtil.QIAN_TYPE, 9));
//            beans.add(new CustomOrderBean(4, NotifyUtil.MEMO_TYPE, 9));

            CustomOrderDbHelper.getInstance().addAllBeans(beans);

            PreferencesUtils.put(WelcomeActivity.this, CustomOrderDbHelper.CUSTOM_ORDER_SM2, true);
        }
    }




    /**
     * 启动动画结束之后， 判断下一步去向：</br>
     * <p>
     * 1, 未登录用户跳转注册登录页面。 </br> 2, 已登录用户，判断是否完成了基本用户信息的设置， 未设置完成条转入信息设置页； </br>
     * 3, 已设置完成用户，判断当前设备是否已经显示过当前版本引导页，未显示跳转版本引导页； </br> 4,
     * 已显示直接跳入主界面，没有显示过，跳转引导界面；</br>
     */
    private void goNext() {
        if (isFinishing() || isDestroyed()) {
            return;
        }

        runOnUiThread(() -> {
            if (bgView != null) {
                bgView.playAnimation();
                bgView.addAnimatorListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(@NonNull Animator animation) {
                        super.onAnimationEnd(animation);
                        bgView.removeAllAnimatorListeners();
                        mHandler.removeCallbacksAndMessages(null);
                        jump();
                    }
                });

                mHandler.postDelayed(this::jump, 2000);
            } else {
                jump();
            }
        });
    }

    private void jump() {
    /*if (PreferencesUtils.getInt(this, GuidanceActivity.SHOW_GUIDANCE_VERSION_TAG, 0) < GuidanceActivity.GUIDANCE_VERSION) {
startActivity(new Intent(this, GuidanceActivity.class));
} else */

        if (!CommonUtils.isLogin()) {
            HomeActivityControl.getInstance().openLoginActivity(WelcomeActivity.this);
        } else if (!ToTwooApplication.isInfoSetFinish(ToTwooApplication.owner)) {
            TimInitBusiness.login();
            startActivity(new Intent(WelcomeActivity.this, InitInfoActivity.class).putExtra(InitInfoActivity.INIT_INFO, true));
//        } else if (PreferencesUtils.getBoolean(WelcomeActivity.this, CommonArgs.NEW_USER_BENFIT_SHARE, false) && Apputils.systemLanguageIsChinese(WelcomeActivity.this)) {
//            TimInitBusiness.login();
//            startActivity(new Intent(WelcomeActivity.this, NewUserBenfitActivity.class));
        } else {
            TimInitBusiness.login();
            HomeActivityControl.getInstance().openHomeActivity(this);

            if (getIntent() != null && getIntent().getExtras() != null && getIntent().getExtras().getString("google.message_id") != null) {
                TimInitBusiness.navToChat();
            }
        }

        com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_PAIRED_STATE, null);

        overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
        // 切换动画
        finish();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        goNext();
    }
}
