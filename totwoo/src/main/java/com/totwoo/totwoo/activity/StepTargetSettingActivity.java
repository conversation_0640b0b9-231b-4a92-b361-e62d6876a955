package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.widget.ImageView;
import android.widget.TextView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.eventbusObject.StepTarget;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.widget.WheelView;
import com.totwoo.totwoo.widget.WheelView.OnWheelViewListener;

import org.greenrobot.eventbus.EventBus;

import butterknife.BindView;
import butterknife.ButterKnife;

public class StepTargetSettingActivity extends BaseActivity {

    public static final String STEP_TARGET = "step_target";
    // 最小目标
//    private int min_target = 1000;

    /**
     * 健步目标选择器
     */
    @BindView(R.id.setting_step_target_wheelview)
    WheelView setting_step_target_wheelview;
    /**
     * 健步类别tv
     */
    @BindView(R.id.step_category_tv)
    TextView step_category_tv;
    /**
     * 健步瘦身设置人偶
     */
    @BindView(R.id.step_target_doll_iv)
    ImageView step_target_doll_iv;

    private OnWheelViewListener onWheelViewListener;

    private int stepTarget;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_step_target_setting);
        ButterKnife.bind(this);
        initData();
        initListener();
    }

    private void initListener() {
        onWheelViewListener = (wheelView, selectedIndex, item) -> {
            try {
                stepTarget = Integer.valueOf(ConfigData.STEP_TARGETS.get(selectedIndex));
            } catch (NumberFormatException e) {
                stepTarget = 8000;
            }
            // 根据selectindex 设置人偶状态和文字
            if (selectedIndex < 9) {
                step_category_tv.setText(R.string.step_new_people);
                if (ToTwooApplication.owner.getGender() == 0) {
                    step_target_doll_iv
                            .setImageResource(R.drawable.boy_new_people);
                } else {
                    step_target_doll_iv
                            .setImageResource(R.drawable.gril_new_people);
                }
            } else if (selectedIndex < 14) {
                step_category_tv.setText(R.string.step_expert);
                if (ToTwooApplication.owner.getGender() == 0) {
                    step_target_doll_iv
                            .setImageResource(R.drawable.boy_expert);
                } else {
                    step_target_doll_iv
                            .setImageResource(R.drawable.gril_expert);
                }
            } else {
                step_category_tv.setText(R.string.step_madman);
                if (ToTwooApplication.owner.getGender() == 0) {
                    step_target_doll_iv
                            .setImageResource(R.drawable.boy_madman);
                } else {
                    step_target_doll_iv
                            .setImageResource(R.drawable.gril_madman);
                }
            }
        };
        setting_step_target_wheelview.setOnWheelViewListener(onWheelViewListener);
        // 返回按钮
        setTopLeftOnclik(v -> finish());
        // 完成按钮
        setTopRightOnClick(v -> {
            if (NotifyUtil.getStepNotifyModel(StepTargetSettingActivity.this).isNotifySwitch()) {
                BluetoothManage.getInstance().setStepTarget(stepTarget);
            }
            setTargetSuccess();
        });
    }

    private void setTargetSuccess() {
        //如果新设置的目标大于已设定的目标, 则清除已经设置的健步目标标识, 以便再次提醒
        //为了测试健步提醒
        if (stepTarget > PreferencesUtils.getInt(
                StepTargetSettingActivity.this, STEP_TARGET, 8000)) {
//            PreferencesUtils.put(StepTargetSettingActivity.this,
//                    HardDataChangeReceiver.LASTSENDTIME, 0L);
        }

        ToTwooApplication.owner.setWalkTarget(stepTarget);
        PreferencesUtils.put(StepTargetSettingActivity.this,
                STEP_TARGET, stepTarget);
        //发到HomeStepHolder
        EventBus.getDefault().post(new StepTarget(stepTarget));
        finish();
//        SysLocalDataBean.sycSetTargetData();
    }


    private void initData() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopRightString(R.string.done);
//        Drawable drawable = getResources().getDrawable(R.drawable.top_right_icon_ok);
//        drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());

//        getTopRightTv().setCompoundDrawables(drawable, null, null, null);
//        getTopRightTv().setCompoundDrawablePadding(Apputils.dp2px(this, 9));
        setting_step_target_wheelview.setItems(ConfigData.STEP_TARGETS, 5, getString(R.string.steps));
        stepTarget = ToTwooApplication.owner.getWalkTarget();
        if(!ConfigData.STEP_TARGETS.contains(stepTarget + "")){
            stepTarget = 8000;
        }
        setting_step_target_wheelview.setSeletion(ConfigData.STEP_TARGETS.indexOf(stepTarget + ""));
    }
}
