package com.totwoo.totwoo.activity.nfc;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.JsonElement;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.data.nfc.MediaBean;
import com.totwoo.totwoo.data.nfc.SecretInfoBean;
import com.totwoo.totwoo.data.nfc.SecretInfoManager;
import com.totwoo.totwoo.data.nfc.SecretType;
import com.totwoo.totwoo.databinding.ActivityMeidaListBinding;
import com.totwoo.totwoo.databinding.DialogInsidePicBlackBinding;
import com.totwoo.totwoo.databinding.NfcMediaListItemBinding;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.record.RecorderConfig;
import com.totwoo.totwoo.utils.AppObserver;
import com.totwoo.totwoo.utils.BlackAlertDialogUtil;
import com.totwoo.totwoo.utils.BlackBottomSheetDialog;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 图文列表页面
 */
public class NfcMediaListActivity extends BaseActivity {
    private static final String NFC_MEDIA_PIC_PATH = FileUtils.getCacheImageDir() + "/nfc_media_pic.jpg";
    /**
     * extra data type: 完整数据体
     */
    public static final String EXTRA_NFC_SECRET_MEDIA_DATA = "pref_nfc_secret_media_data";
    public static final int REQUEST_CODE_DETAIL = 333;

    private SecretInfoBean beanData;
    private ActivityMeidaListBinding binding;
    private NfcMediaListItemBinding imageBinding;
    private NfcMediaListItemBinding videoBinding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        beanData = (SecretInfoBean) getIntent().getSerializableExtra(EXTRA_NFC_SECRET_MEDIA_DATA);

        if (beanData == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }
        binding = ActivityMeidaListBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        binding.nfcMediaListBack.setOnClickListener(v -> finish());

        refreshView();
    }

    private void refreshView() {
        binding.nfcMediaListItemContainer.removeAllViews();
        MediaBean imageObj = getDataByType(1);
        MediaBean videoObj = getDataByType(2);

        if (imageBinding == null) {
            imageBinding = NfcMediaListItemBinding.inflate(LayoutInflater.from(this));
            imageBinding.nfcMediaItemDefaultTv.setText(R.string.nfc_add_pic);
            imageBinding.nfcMediaItemDefaultIcon.setImageResource(R.drawable.media_icon_pic);
        }

        if (videoBinding == null) {
            videoBinding = NfcMediaListItemBinding.inflate(LayoutInflater.from(this));
        }

        if (imageObj == null && videoObj != null) {
            // 优先放 video
            binding.nfcMediaListItemContainer.addView(refreshItem(videoObj, videoBinding, 2));
            binding.nfcMediaListItemContainer.addView(refreshItem(imageObj, imageBinding, 1));
        } else {
            binding.nfcMediaListItemContainer.addView(refreshItem(imageObj, imageBinding, 1));
            binding.nfcMediaListItemContainer.addView(refreshItem(videoObj, videoBinding, 2));
        }

        binding.nfcMediaListInfoTv.setVisibility(
                beanData.getListData() == null || beanData.getListData().isEmpty()
                        ? View.VISIBLE : View.GONE);
    }

    @NonNull
    private View refreshItem(@Nullable MediaBean bean, @NonNull NfcMediaListItemBinding bd, int type) {
        boolean isVideo = type == 2;

        if (bean == null) {
            // 默认画面
            bd.nfcMediaItemIv.setVisibility(View.GONE);
            bd.nfcMediaItemIv.setImageDrawable(null);
            bd.nfcMediaItemInfoTv.setVisibility(View.GONE);
            bd.nfcMediaItemDefaultIcon.setVisibility(View.VISIBLE);
            bd.nfcMediaItemDefaultTv.setVisibility(View.VISIBLE);
            bd.nfcMediaItemVideoIcon.setVisibility(View.GONE);

            bd.nfcMediaItemLayout.setOnClickListener(v -> {
                if (isVideo) {
                    if (PermissionUtil.hasCameraPermission(this)) {
                        RecorderConfig config = new RecorderConfig("NFC-MEDIA");
                        config.setSuggestWidth(720);
                        config.setSuggestHeight(1280);
                        config.setTarget((videoPath, coverPath) -> {
                            startActivityForResult(new Intent(this, NfcMediaDetailActivity.class)
                                    .putExtra(CommonArgs.VIDEO_PATH, videoPath)
                                    .putExtra(CommonArgs.COVER_PATH, coverPath), REQUEST_CODE_DETAIL);
                        });
                        config.goRecorder(this);
                        MobclickAgent.onEvent(this, TrackEvent.NFC.NFC_ADD_MEDIA_VIDEO);
                    }
                } else {
                    showPickPicDialog();
                    MobclickAgent.onEvent(this, TrackEvent.NFC.NFC_ADD_MEDIA_IMAGE);
                }
            });
        } else {
            // 有图片数据
            String imageUrl;
            if (isVideo) {
                imageUrl = !TextUtils.isEmpty(bean.getCoverUrl()) ? bean.getCoverUrl() : bean.getVideoUrl();
            } else {
                imageUrl = bean.getImgUrl();
            }
            bd.nfcMediaItemDefaultIcon.setVisibility(View.GONE);
            bd.nfcMediaItemDefaultTv.setVisibility(View.GONE);
            bd.nfcMediaItemIv.setVisibility(View.VISIBLE);

            RequestOptions requestOptions = new RequestOptions();
            if (imageUrl != null && imageUrl.startsWith("/")) {
                requestOptions = requestOptions.skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE);
            }

            Glide.with(this).asBitmap().apply(requestOptions).placeholder(R.drawable.loading_drawable).load(HttpHelper.getRealImageUrl(imageUrl)).into(bd.nfcMediaItemIv);
            bd.nfcMediaItemVideoIcon.setVisibility(isVideo ? View.VISIBLE : View.GONE);

            bd.nfcMediaItemInfoTv.setVisibility(View.VISIBLE);
            bd.nfcMediaItemInfoTv.setText(bean.getContent() == null ? "" : bean.getContent());

            bd.nfcMediaItemLayout.setOnClickListener(v -> {
                PreviewConfig config = new PreviewConfig(isVideo ? bean.getVideoUrl() : null, isVideo ? bean.getCoverUrl() : bean.getImgUrl(), bean.getContent());
                goPreview(bean.getId(), config, isVideo);
            });
        }

        // 重新调整 Item 尺寸
        bd.nfcMediaItemLayout.getLayoutParams().height = Apputils.dp2px(this, bean == null ? 142 : 232);
        bd.nfcMediaItemLayout.requestLayout();
        return bd.getRoot();
    }

    private void goPreview(String mediaId, PreviewConfig config, boolean isVideo) {
        config.setMenu(R.drawable.icon_delete_gray, 0, (activity) -> {
            BlackAlertDialogUtil.showCommonDialog(activity, R.string.custom_notify_list_delete_hint, () -> {
                NfcLoading.show(activity);
                MobclickAgent.onEvent(NfcMediaListActivity.this, isVideo ? TrackEvent.NFC.NFC_DELETE_MEDIA_VIDEO : TrackEvent.NFC.NFC_DELETE_MEDIA_IMAGE);
                HttpHelper.nfcService.mediaDelete(mediaId).compose(HttpHelper.rxSchedulerHelper()).subscribe(new AppObserver<JsonElement>() {
                    @Override
                    public void onCompleted() {
                        NfcLoading.dismiss();
                    }

                    @Override
                    public void onSuccess(JsonElement baseBean) {
                        ToastUtils.showLong(NfcMediaListActivity.this, R.string.delete_success);
                        activity.finish();

                        Iterator<MediaBean> it = beanData.getListData().iterator();

                        while (it.hasNext()) {
                            if (mediaId.equals(it.next().getId())) {
                                it.remove();
                            }
                        }
                        updateData();
                        // 页面刷新
                        refreshView();
                    }
                });
            });
        });
//        config.setTitleId(R.string.my_secret);
        config.goPreview(this);
    }

    /**
     * 获取指定类型的数据, 图片为 1, 视频为 2
     *
     * @param type
     * @return
     */
    private MediaBean getDataByType(int type) {
        ArrayList<MediaBean> listData = beanData.getListData();
        if (listData != null) {
            // 反向查找, 使用最新的数据
            for (int i = listData.size() - 1; i >= 0; i--) {
                try {
                    if (listData.get(i).getType() == type) {
                        return listData.get(i);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private void showPickPicDialog() {
        BlackBottomSheetDialog dialog = new BlackBottomSheetDialog(this);
        DialogInsidePicBlackBinding dialogBinding = DialogInsidePicBlackBinding.inflate(LayoutInflater.from(this));
        dialog.setContentView(dialogBinding.getRoot());
        dialogBinding.dialogTitleTv.setText(R.string.select_photo);
        dialogBinding.picAlbumTv.setText(R.string.memory_photo_Album);
        dialogBinding.picCameraTv.setText(R.string.use_camera);

        dialog.getWindow().findViewById(R.id.dialog_close).setOnClickListener(v -> dialog.dismiss());
        dialogBinding.picAlbumTv.setOnClickListener(v -> {
            PictureSelectUtil.with(this).gallery().setCallback(uri -> {
                CommonUtils.copyUriToPath(this, uri, NFC_MEDIA_PIC_PATH);

                startActivityForResult(new Intent(this, NfcMediaDetailActivity.class)
                        .putExtra(CommonArgs.COVER_PATH, NFC_MEDIA_PIC_PATH), REQUEST_CODE_DETAIL);
            }).select();
            dialog.dismiss();
        });
        // 相机tv监听点击开启拍照app
        dialogBinding.picCameraTv.setOnClickListener(v -> {
            PictureSelectUtil.with(this).camera().setCallback(uri -> {
                CommonUtils.copyUriToPath(this, uri, NFC_MEDIA_PIC_PATH);

                startActivityForResult(new Intent(this, NfcMediaDetailActivity.class)
                        .putExtra(CommonArgs.COVER_PATH, NFC_MEDIA_PIC_PATH), REQUEST_CODE_DETAIL);
            }).select();
            dialog.dismiss();
        });
        dialog.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_CODE_DETAIL && resultCode == RESULT_OK && data != null) {
            MediaBean bean = (MediaBean) data.getExtras().getSerializable(EXTRA_NFC_SECRET_MEDIA_DATA);
            if (bean != null) {
                if (beanData.getListData() == null) {
                    beanData.setListData(new ArrayList<>());
                }
                beanData.getListData().add(bean);
                updateData();
                refreshView();
            }
        }
    }

    private void updateData() {
        boolean validData = beanData != null && beanData.getListData() != null && beanData.getListData().size() > 0;
        List<SecretInfoBean> infos = SecretInfoManager.getInstance().getSavedInfos();
        int oldIndex = -1;
        for (int i = 0, infosSize = infos.size(); i < infosSize; i++) {
            SecretInfoBean info = infos.get(i);
            if (SecretType.TYPE_MEDIA.equals(info.getType().type)) {
                oldIndex = i;
            }
        }

        if (validData && oldIndex < 0) {
            infos.add(beanData);
        } else if (!validData && oldIndex > 0) {
            infos.remove(oldIndex);
        }

        setResult(RESULT_OK, new Intent().putExtra(EXTRA_NFC_SECRET_MEDIA_DATA, validData ? beanData : null));
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
