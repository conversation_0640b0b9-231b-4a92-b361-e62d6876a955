package com.totwoo.totwoo.activity.wish;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.WishTypeSelectBean;
import com.totwoo.totwoo.record.RecorderConfig;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.CustomMiddleTextDialog;
import com.totwoo.totwoo.widget.discretescrollview.DSVOrientation;
import com.totwoo.totwoo.widget.discretescrollview.DiscreteScrollView;
import com.totwoo.totwoo.widget.discretescrollview.InfiniteScrollAdapter;
import com.totwoo.totwoo.widget.discretescrollview.ScaleTransformer;
import com.umeng.analytics.MobclickAgent;

import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

public class WishCardGalleryActivity extends BaseActivity implements SubscriberListener {

    public static final String HAS_WISH = "has_wish";

    private List<WishTypeSelectBean> mList = new ArrayList<>();
    private DiscreteScrollView itemPicker;
    private InfiniteScrollAdapter infiniteAdapter;

    private CustomDialog changeBgDialog;
    private Uri uri;
    private TextView mMoreTv;
    private LottieAnimationView wish_card_meteor_lav;
    private LottieAnimationView wish_card_dandelion_lav;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_card_gallery);
        InjectUtils.injectOnlyEvent(this);
        init();
        PermissionUtil.hasStoragePermission(this);
        mMoreTv = findViewById(R.id.wish_type_bottom_more_tv);
        mMoreTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        wish_card_meteor_lav = findViewById(R.id.wish_card_meteor_lav);
        wish_card_dandelion_lav = findViewById(R.id.wish_card_dandelion_lav);
        mMoreTv.setOnClickListener(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_SEE_DETAILGUIDE);
            final CustomMiddleTextDialog customMiddleTextDialog = new CustomMiddleTextDialog(WishCardGalleryActivity.this);
            customMiddleTextDialog.setTitleTv(R.string.wish_type_hint_title);
            customMiddleTextDialog.setInfoText(getResources().getString(R.string.wish_type_hint_info));
            customMiddleTextDialog.setConfirmTv(getResources().getString(R.string.i_know), v1 -> customMiddleTextDialog.dismiss());
            customMiddleTextDialog.show();
        });
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopTitleColor(getResources().getColor(R.color.white));
        setTopTitle(R.string.wish_type_title);
        setTopLeftOnclik(v -> finish());
        if (getIntent().getBooleanExtra(HAS_WISH, false)) {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_TO_LIST);
            setTopRightIcon(R.drawable.wish_list);
            setTopRightOnClick(v -> startActivity(new Intent(WishCardGalleryActivity.this, WishListActivity.class)));
        }
    }

    private void init() {
        mList.add(new WishTypeSelectBean(R.drawable.wish_type_voice, getString(R.string.wish_type_voice), CommonArgs.COMMON_SEND_TYPE_SOUND));
        mList.add(new WishTypeSelectBean(R.drawable.wish_type_text, getString(R.string.wish_type_text), CommonArgs.COMMON_SEND_TYPE_TEXT));
        mList.add(new WishTypeSelectBean(R.drawable.wish_type_video, getString(R.string.wish_type_video), CommonArgs.COMMON_SEND_TYPE_VIDEO));
        mList.add(new WishTypeSelectBean(R.drawable.wish_type_image, getString(R.string.wish_type_image), CommonArgs.COMMON_SEND_TYPE_IMAGE));

        itemPicker = findViewById(R.id.item_picker);
        itemPicker.setOrientation(DSVOrientation.HORIZONTAL);
        infiniteAdapter = InfiniteScrollAdapter.wrap(new CardAdapter());
        itemPicker.setAdapter(infiniteAdapter);
        itemPicker.setItemTransitionTimeMillis(150);
        itemPicker.setItemTransformer(new ScaleTransformer.Builder()
                .setMinScale(0.8f)
                .build());
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    private class CardAdapter extends RecyclerView.Adapter<CardAdapter.ViewHolder> {

        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.view_card_item, parent, false);
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(CommonUtils.dip2px(WishCardGalleryActivity.this, 250), CommonUtils.getScreenHeight() / 2);
            itemView.setLayoutParams(layoutParams);
            return new ViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(final CardAdapter.ViewHolder holder, final int position) {
            holder.mContentView.setOnClickListener(v -> {
                switch (mList.get(position).getType()) {
                    case CommonArgs.COMMON_SEND_TYPE_TEXT:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_TO_SENDTEXT);
                        startActivity(new Intent(WishCardGalleryActivity.this, WishAddTextInfoActivity.class));
                        break;
                    case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_TO_SENDPHOTO);
                        if (PermissionUtil.hasCameraPermission(WishCardGalleryActivity.this) && PermissionUtil.hasStoragePermission(WishCardGalleryActivity.this)) {
                            getPhotoDialog(WishCardGalleryActivity.this);
                        }
                        break;
                    case CommonArgs.COMMON_SEND_TYPE_SOUND:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_TO_SENDVOICE);
                        if (PermissionUtil.hasAudioPermission(WishCardGalleryActivity.this) && PermissionUtil.hasStoragePermission(WishCardGalleryActivity.this)) {
                            if (PermissionUtil.hasAudioPermissionExtra(WishCardGalleryActivity.this)) {
                                startActivity(new Intent(WishCardGalleryActivity.this, WishAddVoiceInfoActivity.class));
                            }
                        }
                        break;
                    case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_TO_SENDVIDEO);
                        if (PermissionUtil.hasCameraPermission(WishCardGalleryActivity.this)) {
                            RecorderConfig config = new RecorderConfig("WISH");
                            config.setSuggestWidth(720);
                            config.setSuggestHeight(1280);
                            config.setTarget((videoPath, coverPath) -> {
                                Intent intent = new Intent(WishCardGalleryActivity.this, WishAddInfoActivity.class)
                                        .putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_VIDEO)
                                        .putExtra(CommonArgs.VIDEO_PATH, videoPath)
                                        .putExtra(CommonArgs.COVER_PATH, videoPath);
                                startActivity(intent);
                            });
                            config.goRecorder(WishCardGalleryActivity.this);
                        }
                        break;
                }
            });
            holder.mImageView.setImageResource(mList.get(position).getImageResoure());
            holder.mTextView.setText(mList.get(position).getDescribe());
        }

        @Override
        public int getItemCount() {
            return mList.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mImageView;
            TextView mTextView;
            ConstraintLayout mContentView;

            public ViewHolder(final View itemView) {
                super(itemView);
                mImageView = (ImageView) itemView.findViewById(R.id.wish_type_info_iv);
                mTextView = (TextView) itemView.findViewById(R.id.wish_type_info_tv);
                mContentView = (ConstraintLayout) itemView.findViewById(R.id.wish_type_content_cl);
            }
        }
    }

    public void getPhotoDialog(Context context) {
        changeBgDialog = new CustomDialog(context);
        changeBgDialog.setTitle(R.string.wish_add_img);
        LinearLayout modify_head_dialog_ll = new LinearLayout(context);
        modify_head_dialog_ll
                .setLayoutParams(new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT));
        modify_head_dialog_ll.setOrientation(LinearLayout.VERTICAL);
        TextView album_tv = new TextView(context);
        TextView camera_tv = new TextView(context);
        modify_head_dialog_ll.addView(album_tv);
        modify_head_dialog_ll.addView(camera_tv);

        changeBgDialog.setMainLayoutView(modify_head_dialog_ll);
        album_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        camera_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        album_tv.setPadding(Apputils.dp2px(context, 20), Apputils.dp2px(context, 15), Apputils.dp2px(context, 20), Apputils.dp2px(context, 15));
        camera_tv.setPadding(Apputils.dp2px(context, 20), Apputils.dp2px(context, 15), Apputils.dp2px(context, 20), Apputils.dp2px(context, 15));
        album_tv.setBackgroundResource(R.drawable.item_bg);
        camera_tv.setBackgroundResource(R.drawable.item_bg);
        album_tv.setText(R.string.album_select_usericon);
        camera_tv.setText(R.string.use_camera);
        album_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        camera_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        album_tv.setTextSize(16);
        camera_tv.setTextSize(16);
        changeBgDialog.setNegativeButtonText(R.string.cancel);
        // 相册tv监听点击开启选择图片app
        album_tv.setOnClickListener(v -> {
            PictureSelectUtil.with(this).gallery().crop(8, 5).setCallback(uri -> {
                receivePhoto(uri);
                changeBgDialog.dismiss();
            }).select();
        });
        // 相机tv监听点击开启拍照app
        camera_tv.setOnClickListener(v -> {

            PictureSelectUtil.with(this).camera().crop(8, 5).setCallback(uri -> {
                receivePhoto(uri);
                changeBgDialog.dismiss();
            }).select();
        });
        changeBgDialog.show();
    }

    private void receivePhoto(Uri uri) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = false;
        options.inSampleSize = 2;

        try {
            Bitmap bitmap = BitmapFactory.decodeStream(getContentResolver().openInputStream(uri));
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, new FileOutputStream(CommonArgs.CACHE_WISH_IMAGE));
            startActivity(new Intent(this, WishAddInfoActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_IMAGE));
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showLong(this, R.string.data_error);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        wish_card_dandelion_lav.pauseAnimation();
        wish_card_meteor_lav.pauseAnimation();
    }

    @Override
    protected void onResume() {
        super.onResume();
        wish_card_dandelion_lav.playAnimation();
        wish_card_meteor_lav.playAnimation();
    }

    /**
     * 心愿发布成功，干掉这个页面
     * WishAddTextInfoActivity、WishAddInfoActivity
     */
    @EventInject(eventType = S.E.E_WISH_POST_SUCCESSED, runThread = TaskType.UI)
    public void postSuccess(EventData data) {
        finish();
    }

    @EventInject(eventType = S.E.E_WISH_DELETE_ALL, runThread = TaskType.UI)
    public void deleteAll(EventData data) {
        getTopRightIcon().setVisibility(View.GONE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        wish_card_dandelion_lav.cancelAnimation();
        wish_card_meteor_lav.cancelAnimation();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
