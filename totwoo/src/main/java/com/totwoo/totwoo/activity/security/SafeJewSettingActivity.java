package com.totwoo.totwoo.activity.security;

import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.SeekBar;
import android.widget.Toast;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class SafeJewSettingActivity extends BaseActivity {
    public static final String SAFE_SHAKE_STATE = "safe_shake_state";
    public static final String SAFE_SHINE_STATE = "safe_shine_state";
    public static final String SAFE_BATTERY_STATE = "safe_battery_state";
    private boolean shakeState = false;
    private boolean shineState = false;
    private boolean batteryState = true;
    @BindView(R.id.safe_vibration_setting_progress_bar)
    SeekBar mVibrationSettingProgressBar;
    @BindView(R.id.safe_shake_checkbox)
    CheckBox mShakeCb;
    @BindView(R.id.safe_shine_checkbox)
    CheckBox mShineCb;
    @BindView(R.id.safe_battery_checkbox)
    CheckBox mBatteryCb;

    private int tempProgress;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_safe_jew_setting);
        ButterKnife.bind(this);
        shakeState = PreferencesUtils.getBoolean(this, SAFE_SHAKE_STATE, true);
        shineState = PreferencesUtils.getBoolean(this, SAFE_SHINE_STATE, true);
        batteryState = PreferencesUtils.getBoolean(this, SAFE_BATTERY_STATE, true);
        tempProgress = PreferencesUtils.getInt(this, BleParams.SELECT_VIBRATION_INDEX_TAG, 90);
        if (tempProgress > 0) {
            mVibrationSettingProgressBar.setProgress(tempProgress);
        }
        mVibrationSettingProgressBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                tempProgress = mVibrationSettingProgressBar.getProgress();
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    Toast.makeText(ToTwooApplication.baseContext, R.string.error_jewelry_connect, Toast.LENGTH_SHORT).show();
                    return;
                }
                BluetoothManage.getInstance().setVibrationIntensity(tempProgress);
                PreferencesUtils.put(SafeJewSettingActivity.this, BleParams.SELECT_VIBRATION_INDEX_TAG, tempProgress);
            }
        });
        mShakeCb.setChecked(shakeState);
        mShineCb.setChecked(shineState);
        mBatteryCb.setChecked(batteryState);
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.safe_set_title);
    }

    @OnClick({R.id.safe_shake_checkbox, R.id.safe_shine_checkbox, R.id.safe_battery_checkbox})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.safe_shake_checkbox:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(ToTwooApplication.baseContext, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                shakeState = !shakeState;
                mShakeCb.setChecked(shakeState);
                PreferencesUtils.put(SafeJewSettingActivity.this, SAFE_SHAKE_STATE, shakeState);
                sendToJew();
                break;
            case R.id.safe_shine_checkbox:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(ToTwooApplication.baseContext, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                }
                shineState = !shineState;
                mShineCb.setChecked(shineState);
                PreferencesUtils.put(SafeJewSettingActivity.this, SAFE_SHINE_STATE, shineState);
                sendToJew();
                break;
            case R.id.safe_battery_checkbox:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(ToTwooApplication.baseContext, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                }
                batteryState = !batteryState;
                mBatteryCb.setChecked(batteryState);
                PreferencesUtils.put(SafeJewSettingActivity.this, SAFE_BATTERY_STATE, batteryState);
                sendBatteryStatusToJew();
                break;
        }
    }

    private void sendToJew() {
        int state = 0;
        if (shakeState && shineState) {
            state = 3;
        } else if (shakeState) {
            state = 2;
        } else if (shineState) {
            state = 1;
        }
//        else{
//            state = 0;
//        }
        LogUtils.e("aab state = " + state);
        BluetoothManage.getInstance().setSafeShakeAndShine(state);
    }

    private void sendBatteryStatusToJew() {
        BluetoothManage.getInstance().setSafeBattery(batteryState ? 1 : 0);
    }
}
