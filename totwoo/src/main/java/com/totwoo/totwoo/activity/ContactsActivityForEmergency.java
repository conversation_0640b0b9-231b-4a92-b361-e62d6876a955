package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.activity.ContactsListActivity.SECURITY_CONTACT_LIST;

import android.os.Bundle;

import com.google.gson.Gson;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.LocalContactsBean;
import com.totwoo.totwoo.bean.SecurityContactsBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import rx.Subscriber;

public class ContactsActivityForEmergency extends ContactsBaseActivity {

    private ArrayList<SecurityContactsBean> contactsBeans;
    private int securityContactSelectedSize;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        contactsBeans = new ArrayList<>();
        if(getIntent().getParcelableArrayListExtra(SECURITY_CONTACT_LIST) != null){
            if (getIntent().getParcelableArrayListExtra(SECURITY_CONTACT_LIST) != null) {
                contactsBeans.addAll(getIntent().getParcelableArrayListExtra(SECURITY_CONTACT_LIST));
            }
        }

        selectOutOfIndexMsg = getString(R.string.safe_contacts_add_over_count);
        mTitleTv.setText(getString(R.string.safe_contacts_add_title));
        if (contactsBeans.size() > 0) {
            securityContactSelectedSize = contactsBeans.size();
        }
        selectSize = 3 - securityContactSelectedSize;
    }

    @Override
    void clickConfirm() {
        if (selectBeans.size() == 0) {
            ToastUtils.showShort(ContactsActivityForEmergency.this,R.string.safe_emergency_add_empty_toast);
            return;
        }

        List<SecurityContactsBean> beans = new ArrayList<>();
        if(securityContactSelectedSize != 0){
            for (SecurityContactsBean securityContactsBean : contactsBeans) {
                beans.add(new SecurityContactsBean(securityContactsBean.getName(),securityContactsBean.getTel()));
            }
        }

        //目前直接把选中的联系人传给服务器

        for (LocalContactsBean localContactsBean : selectBeans) {
            beans.add(new SecurityContactsBean(localContactsBean.getName(), localContactsBean.getNumber()));
        }

        Gson gson = new Gson();
        String jsonString = gson.toJson(beans);
        saveSecurityContacts(jsonString);
    }

    private void saveSecurityContacts(String jsonString) {
        HttpHelper.safeService.saveContacts(jsonString)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(ContactsActivityForEmergency.this,R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        finish();
                    }
                });
    }
}
