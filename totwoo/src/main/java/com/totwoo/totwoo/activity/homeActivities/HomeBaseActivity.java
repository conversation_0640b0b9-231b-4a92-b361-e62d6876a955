package com.totwoo.totwoo.activity.homeActivities;

import static com.totwoo.totwoo.activity.CallRemindSetActivity.ALL_CALL_REMIND_SWITCH_KEY;
import static com.totwoo.totwoo.activity.CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_SWITCH_KEY;
import static com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity.IS_IMEI_SENT;
import static com.totwoo.totwoo.fragment.LoveFragment.PREF_HAS_SHOW_PAIRED_DIALOG;

import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.webkit.URLUtil;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.airbnb.lottie.LottieAnimationView;
import com.blankj.utilcode.util.AppUtils;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.JSONUtils;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.BrightMusicActivity;
import com.totwoo.totwoo.activity.CustomOrderActivity;
import com.totwoo.totwoo.activity.SetPermissionActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftDataActivity;
import com.totwoo.totwoo.adapter.CustomAngleRecyclerViewAdapter;
import com.totwoo.totwoo.bean.AppUpdateBean;
import com.totwoo.totwoo.bean.FaceBookSharePathEventData;
import com.totwoo.totwoo.bean.GiftCardReceiveBean;
import com.totwoo.totwoo.bean.GiftMessageBean;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.bean.LoveStoriesBean;
import com.totwoo.totwoo.bean.NotifyDataModel;
import com.totwoo.totwoo.bean.NotifyMessage;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BleUtils;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.LoveFragment;
import com.totwoo.totwoo.fragment.LovePairedFragment;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.newConrtoller.MessageController;
import com.totwoo.totwoo.receiver.JpushReceiver;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.ARCameraShareUtil;
import com.totwoo.totwoo.utils.AppObserver;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.CustomNotifyDbHelper;
import com.totwoo.totwoo.utils.DesUtil;
import com.totwoo.totwoo.utils.DialogHelper;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.WaterTimeDbHelper;
import com.totwoo.totwoo.widget.AppDownloadDialog;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.SettingDialog;
import com.totwoo.totwoo.widget.SupportRenderScriptBlur;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import eightbitlab.com.blurview.BlurView;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public abstract class HomeBaseActivity extends BaseActivity implements SubscriberListener {
    public static final String NEED_SHOW_IMPORTANT_SETTING = "need_show_important_setting";
    private static final int REQUEST_CODE_GUIDE_PAGE = 666;

    @BindView(R.id.home_content_viewpager)
    ViewPager homeContentViewpager;

    @BindView(R.id.message_layout)
    public View messageView;

    @BindView(R.id.message_iv)
    public ImageView messageIv;

    @BindView(R.id.message_close)
    public ImageView messageClose;

    @BindView(R.id.angel_hint_order_cl)
    ConstraintLayout angel_hint_order_cl;

    @BindView(R.id.safe_hint_order_cl)
    ConstraintLayout safe_hint_order_cl;

    @BindView(R.id.lollipop_hint_order_cl)
    ConstraintLayout lollipop_hint_order_cl;

    @BindView(R.id.reminder_hint_order_cl)
    ConstraintLayout reminder_hint_order_cl;

    @BindView(R.id.love_hint_cl)
    ConstraintLayout love_hint_cl;
    @BindView(R.id.love_hint_cl_1)
    ConstraintLayout love_hint_cl_1;
    @BindView(R.id.love_hint_cl_2)
    ConstraintLayout love_hint_cl_2;
    @BindView(R.id.love_hint_cl_3)
    ConstraintLayout love_hint_cl_3;
    @BindView(R.id.love_hint_cl_4)
    ConstraintLayout love_hint_cl_4;

    @BindView(R.id.love_hint_cl_1_iv)
    ImageView love_hint_cl_1_iv;
    @BindView(R.id.love_hint_cl_1_text_iv)
    ImageView love_hint_cl_1_text_iv;
    @BindView(R.id.love_hint_cl_2_text_iv)
    ImageView love_hint_cl_2_text_iv;
    @BindView(R.id.love_hint_cl_3_text_iv)
    ImageView love_hint_cl_3_text_iv;
    @BindView(R.id.love_hint_cl_4_iv)
    ImageView love_hint_cl_4_iv;
    @BindView(R.id.love_hint_cl_4_text_iv)
    ImageView love_hint_cl_4_text_iv;

    @BindView(R.id.activity_loading_cl)
    ConstraintLayout mLoadingCl;
    @BindView(R.id.loading_lv)
    LottieAnimationView mLoadingLv;

    @BindView(R.id.angel_hint_order_iv)
    ImageView angel_hint_order_iv;

    @BindView(R.id.safe_hint_order_iv)
    ImageView safe_hint_order_iv;

    @BindView(R.id.lollipop_hint_order_iv)
    ImageView lollipop_hint_order_iv;

    @BindView(R.id.lollipop_hint_order_iv_2)
    ImageView lollipop_hint_order_iv_2;

    @BindView(R.id.reminder_hint_order_iv)
    ImageView reminder_hint_order_iv;

    @BindView(R.id.angel_hint_main_iv)
    ImageView angel_hint_main_iv;

    @BindView(R.id.safe_hint_main_iv)
    ImageView safe_hint_main_iv;

    @BindView(R.id.lollipop_hint_main_iv)
    ImageView lollipop_hint_main_iv;

    @BindView(R.id.lollipop_hint_main_iv_2)
    ImageView lollipop_hint_main_iv_2;

    @BindView(R.id.reminder_hint_main_iv)
    ImageView reminder_hint_main_iv;

    @BindView(R.id.home_bottom_tab0_iv)
    ImageView mBottomTabIv0;
    @BindView(R.id.home_bottom_tab0_tv)
    TextView mBottomTabTv0;
    @BindView(R.id.home_tab0_red_point)
    View mBottomTabPoint0;
    @BindView(R.id.home_bottom_tab1_iv)
    ImageView mBottomTabIv1;
    @BindView(R.id.home_bottom_tab1_tv)
    TextView mBottomTabTv1;
    @BindView(R.id.home_tab1_red_point)
    View mBottomTabPoint1;
    @BindView(R.id.home_bottom_tab2)
    RelativeLayout mBottomRl2;
    @BindView(R.id.home_bottom_tab2_iv)
    ImageView mBottomTabIv2;
    @BindView(R.id.home_bottom_tab2_tv)
    TextView mBottomTabTv2;
    @BindView(R.id.home_tab2_red_point)
    View mBottomTabPoint2;

    @BindView(R.id.home_bottom_tab3)
    RelativeLayout mBottomRl3;
    @BindView(R.id.home_bottom_tab3_iv)
    ImageView mBottomTabIv3;
    @BindView(R.id.home_bottom_tab3_tv)
    TextView mBottomTabTv3;
    @BindView(R.id.home_tab3_red_point)
    View mBottomTabPoint3;

    @BindView(R.id.home_bottom_bg_ll)
    LinearLayout mBottomLl;

    @BindView(R.id.bottomBlurView)
    BlurView bottomBlurView;

    @BindView(R.id.root)
    ViewGroup root;

    private HomeBasePageAdapter homeBasePageAdapter;
    private int currPosition = 0;
    private ArrayList<HomepageBottomInfo> bottomInfos;
    /**
     * 底部 Tab 禁止展示 Icon
     */
    private boolean bottomOnlyIcon;

    public static final String JPUSH_ALIAS_OK = "jpush_anlas_ok";
    /**
     * 用户性别发生变化发送的通知消息
     */
    public static final String ACTION_USER_GENDEN_CHANGE = "action_user_gendet_change";
    private int text_default_color;
    private int text_select_color;
//    public static final String APP_LANGUAGE = "app_language";
//    private boolean isChinese;

    private boolean isSettingDialogShowing = false;

    private SettingDialog importantAlertDialog;


    private boolean isCheckGifData;

    private int lastConnectState = JewInfoSingleton.STATE_DISCONNECTED;

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
//        outState.putBoolean(APP_LANGUAGE, isChinese);
        super.onSaveInstanceState(outState);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        ToTwooApplication.baseContext = this;// 全局引用的字符串, 对应多语言
        setContentView(R.layout.activity_home_new2);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        EventBus.getDefault().register(this);
        text_default_color = getResources().getColor(R.color.text_color_black);
        text_select_color = getResources().getColor(R.color.text_color_black);

        if (BleParams.isBluetoothJewelry(null) && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
            BluetoothManage.getInstance().setOTA(false);
            if (!BleUtils.isBlEEnable(this)) {
                showBluetoothDialog();
            } else if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                BluetoothManage.getInstance().reconnect(false);
            } else {
                // 触发 OTA 升级检查
//                onJewerlyConnected(null);
            }
            initJewelryThread();
            checkConnectHandler.sendEmptyMessage(0);
        }

        mHandler.postDelayed(this::putRegisterInfo,16000);


        initView();

        Apputils.getGlobalExecutor().submit(() -> {
//            isChinese = Apputils.systemLanguageIsChinese(HomeBaseActivity.this);
//            if (savedInstanceState != null) {
//                boolean lastLanguage = savedInstanceState.getBoolean(APP_LANGUAGE);
//                if (isChinese != lastLanguage) {
//                    ToastUtils.showLong(HomeBaseActivity.this, R.string.switch_language);
//                }
//            }

            if (BleParams.isBluetoothJewelry(null)) {
                PermissionUtil.hasBluetoothPermission(HomeBaseActivity.this);
                CommonUtils.isNotificationServiceEnabled();
            }
            checkAppUpdate(false);
            // 清除心有灵犀的通知提醒
            clearTOTWOONotification();


            if ("connectJew".equals(getIntent().getStringExtra("PAGE_FROM"))) {
                systemUserData();
            }
        });

        setSpinState(false);

        if (BleParams.isButtonBatteryJewelry()) {
            PreferencesUtils.put(this, IMPORTANT_CONTACT_REMIND_SWITCH_KEY, true);
            PreferencesUtils.put(this, ALL_CALL_REMIND_SWITCH_KEY, false);
        }


        initBlur();
        PermissionUtil.hasNotificationPermission(this);

        JewelryOnlineDataManager.getInstance().resetConnectedJewInfo();
    }


    private void initBlur() {
        final float radius = 25f;
        bottomBlurView.setupWith(root)
                .setBlurAlgorithm(new SupportRenderScriptBlur(this))
                .setBlurRadius(radius)
                .setHasFixedTransformationMatrix(true);

        // 为底部BlurView设置Edge-to-Edge适配，确保底部Tab不被导航栏遮挡
        setupBottomBlurViewEdgeToEdge();
    }

    /**
     * 为底部BlurView设置Edge-to-Edge适配
     * 确保底部Tab不被导航栏遮挡
     */
    private void setupBottomBlurViewEdgeToEdge() {
        if (bottomBlurView != null) {
            EdgeToEdgeUtils.setupBottomInsets(bottomBlurView);
        }
    }

    @Override
    protected void setupDefaultEdgeToEdge() {
    }

    private void systemUserData() {
        if (homeBasePageAdapter == null) {
            return;
        }
        for (int i = 0; i < homeBasePageAdapter.getCount(); i++) {
            if (homeBasePageAdapter.getItem(i) instanceof CustomAngleRecyclerViewAdapter.ItemTypeProvider) {
                CustomAngleRecyclerViewAdapter.ItemTypeProvider provider = (CustomAngleRecyclerViewAdapter.ItemTypeProvider) homeBasePageAdapter.getItem(i);
                if (provider.hasItemByType(NotifyUtil.STEP_TYPE)) {
                    SysLocalDataBean.synStepData(this);
                } else if (provider.hasItemByType(NotifyUtil.PERIOD_TYPE)) {
                    SysLocalDataBean.getPeriodStatus();
                } else if (provider.hasItemByType(NotifyUtil.WATER_TYPE)) {
                    SysLocalDataBean.getWaterTimeStatus();
                } else if (provider.hasItemByType(NotifyUtil.MEMO_TYPE)) {
                    SysLocalDataBean.getCustomNotifyStatus();
                }
//                getWishStatus();
            }
        }
    }

    /**
     * UI 相关操作
     */
    private void initView() {
        angel_hint_order_cl.setOnClickListener(v -> {
            angel_hint_order_cl.setVisibility(View.GONE);
            isHintShow = false;
        });
        angel_hint_order_iv.setOnClickListener(v -> {
            startActivity(new Intent(HomeBaseActivity.this, CustomOrderActivity.class).putExtra("from_type", currentFromType));
            angel_hint_order_cl.setVisibility(View.GONE);
            isHintShow = false;
        });
        safe_hint_order_cl.setOnClickListener(v -> {
            safe_hint_order_cl.setVisibility(View.GONE);
            isHintShow = false;
        });

        lollipop_hint_order_cl.setOnClickListener(v -> {
            if (lollipop_hint_order_iv.getVisibility() == View.VISIBLE) {
                lollipop_hint_order_iv.setVisibility(View.GONE);
                lollipop_hint_main_iv.setVisibility(View.GONE);
                lollipop_hint_order_iv_2.setVisibility(View.VISIBLE);
                lollipop_hint_main_iv_2.setVisibility(View.VISIBLE);
            } else {
                lollipop_hint_order_cl.setVisibility(View.GONE);
                isHintShow = false;
            }
        });

        reminder_hint_order_cl.setOnClickListener(v -> {
            reminder_hint_order_cl.setVisibility(View.GONE);
            isHintShow = false;
        });


        love_hint_cl.setOnClickListener(v -> {

        });
        mLoadingCl.setOnClickListener(v -> {

        });
        love_hint_cl_1.setOnClickListener(v -> {
            love_hint_cl_1.setVisibility(View.GONE);
            love_hint_cl_2.setVisibility(View.VISIBLE);
        });
        love_hint_cl_2.setOnClickListener(v -> {
            love_hint_cl_2.setVisibility(View.GONE);
            love_hint_cl_3.setVisibility(View.VISIBLE);
        });
        love_hint_cl_3.setOnClickListener(v -> {
            love_hint_cl_3.setVisibility(View.GONE);
            love_hint_cl_4.setVisibility(View.VISIBLE);
        });
        love_hint_cl_4.setOnClickListener(v -> {
            love_hint_cl_4.setVisibility(View.GONE);
            love_hint_cl.setVisibility(View.GONE);
            PreferencesUtils.put(HomeBaseActivity.this, LovePairedFragment.IS_TOTWOO_HINT_SHOWED, true);
//            showNextDialog();
        });

        int width = CommonUtils.getScreenWidth() - 2 * CommonUtils.dip2px(HomeBaseActivity.this, 30);
        int height = width / 4 * 5;
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width, height);
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        messageIv.setLayoutParams(layoutParams);

        int pushIndex = getIntent().getIntExtra(CommonArgs.HOME_PUSH_TAB_INDEX, 0);
        if (pushIndex != 0) {
            homeContentViewpager.setCurrentItem(pushIndex, false);
        }
    }


    private void showImportantAlertDialog() {
        // 添加Activity状态检查，防止WindowManager$BadTokenException
        if (isFinishing() || isDestroyed()) {
            return;
        }

        if (!PreferencesUtils.getBoolean(this, NEED_SHOW_IMPORTANT_SETTING, true)) {
            MessageController.getInstance().messageShow();
            return;
        }

        if (isSettingDialogShowing) {
            return;
        }

        try {
            isSettingDialogShowing = true;
            importantAlertDialog = new SettingDialog(this,
                    v -> {
    //                    WebActivity.showWeb(HomeBaseActivity.this, CommonUtils.getImportantSettingGuidePageUrl(), REQUEST_CODE_GUIDE_PAGE);
                        PreferencesUtils.put(HomeBaseActivity.this, NEED_SHOW_IMPORTANT_SETTING, false);
                        importantAlertDialog.dismiss();
                        MessageController.getInstance().messageShow();
                        startActivity(new Intent(this, SetPermissionActivity.class));
                    },
                    null);
            importantAlertDialog.setCanceledOnTouchOutside(false);
            importantAlertDialog.show();
        } catch (WindowManager.BadTokenException e) {
            // 处理窗口令牌异常，避免崩溃
            isSettingDialogShowing = false;
            LogUtils.e("HomeBaseActivity", "Failed to show dialog: " + e.getMessage());
        }
    }

    private boolean isFrist = true;

    @Override
    protected void onResume() {
        super.onResume();
        if (!isFrist) {
            if (currPosition >= 0 && homeBasePageAdapter.getItem(currPosition) != null) {
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOMEACTIVITY_ONSHOW, null);
                homeBasePageAdapter.getItem(currPosition).onShow();
            }
        }
        isFrist = false;
        // 延迟800ms 执行
        mHandler.postDelayed(() -> {

            // 检查是否有新消息, 显示或者隐藏小红点
//            checkNewMessage();

            checkLoveStories();
        }, 800);


        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED && BleUtils.isBlEEnable(this)) {
//            if (BluetoothManage.getInstance().getBondedDevices()) {
//                showUnBondDialog();
//            }
            // 为防止数据错误, 关闭 totwoo 消息屏蔽
            BluetoothManage.getInstance().setBlockStatus(false);
        }
    }

    private void checkLoveStories() {
        HttpHelper.commonService.showLoveStories()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new AppObserver<LoveStoriesBean>() {
                    @Override
                    public void onSuccess(LoveStoriesBean loveStoriesBean) {
                        messageView.setVisibility(View.VISIBLE);

                        String img_url = loveStoriesBean.getImg_url();
                        String jump_url = loveStoriesBean.getJump_url();
                        String love_message_id = loveStoriesBean.getLove_message_id();


                        BitmapHelper.displayWithoutPlaceHolder(HomeBaseActivity.this, messageIv, img_url);
                        if (!jump_url.equals("")) {
                            messageIv.setOnClickListener(v -> {
                                WebViewActivity.loadUrl(HomeBaseActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_LOVE_STORIES), false, false, getString(R.string.lovestories), "");
                                messageView.setVisibility(View.GONE);
                                clickLoveStories(love_message_id);
                            });
                        }

                        messageClose.setOnClickListener(v -> {
                            messageView.setVisibility(View.GONE);
                            clickLoveStories(love_message_id);
                        });
                    }
                });
    }


    private void clickLoveStories(String love_message_id) {
        HttpHelper.commonService.clickLoveStories(love_message_id)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new AppObserver<Object>() {
                    @Override
                    public void onSuccess(Object object) {
                    }
                });
    }

    @Override
    protected void onPause() {
        super.onPause();

        if (currPosition >= 0 && homeBasePageAdapter.getItem(currPosition) != null) {
            homeBasePageAdapter.getItem(currPosition).onHide();
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (checkConnectHandler != null) checkConnectHandler.removeCallbacksAndMessages(null);

        InjectUtils.injectUnregisterListenerAll(this);

        EventBus.getDefault().unregister(this);
        dismissProgressDialog();
        mHandler.removeCallbacksAndMessages(null);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }

    //设置底部导航栏的icon和名称。
    public void setBottomInfo(ArrayList<HomepageBottomInfo> homepageBottomInfos) {
        this.bottomInfos = homepageBottomInfos;
        setDefaultBottom();
    }

    public void setBottomShowIconOnly(boolean onlyIcon) {
        this.bottomOnlyIcon = onlyIcon;
        setDefaultBottom();
    }

    public void setTextColor(int select_color, int default_color) {
        text_select_color = select_color;
        text_default_color = default_color;
    }

    public void setBottomLlColor(int resourceId) {
        mBottomLl.setBackgroundColor(getResources().getColor(resourceId));
    }

    public void setFragmentsAndInitViewpager(Class<? extends BaseFragment>[] fragmentsData) {
        homeBasePageAdapter = new HomeBasePageAdapter(getSupportFragmentManager(), fragmentsData);
        homeContentViewpager.setAdapter(homeBasePageAdapter);
        homeContentViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                selectTab(position, false);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        selectTab(0, true, true);
    }

    private int currentFromType = 2;

    public void setCurrentFromType(int currentFromType) {
        this.currentFromType = currentFromType;
    }

    private int totwooIndex = 0;

    public void setTotwooIndex(int totwooIndex) {
        this.totwooIndex = totwooIndex;
    }

    @OnClick({R.id.home_bottom_tab0, R.id.home_bottom_tab1, R.id.home_bottom_tab2, R.id.home_bottom_tab3})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.home_bottom_tab0:
                homeContentViewpager.setCurrentItem(0, false);
                break;
            case R.id.home_bottom_tab1:
                homeContentViewpager.setCurrentItem(1, false);
                break;
            case R.id.home_bottom_tab2:
                homeContentViewpager.setCurrentItem(2, false);
                break;
            case R.id.home_bottom_tab3:
                homeContentViewpager.setCurrentItem(3, false);
                break;
        }
    }

    private CheckConnectHandler checkConnectHandler;

    /**
     * 首饰连接状态心跳, 实时同步服务端.
     */
    private void initJewelryThread() {
        HandlerThread handlerThread = new HandlerThread("initJewelryThread");
        handlerThread.start();
        checkConnectHandler = new CheckConnectHandler(this, handlerThread.getLooper());

    }

    private class CheckConnectHandler extends Handler {
        private final WeakReference<HomeBaseActivity> mActivity;

        private CheckConnectHandler(HomeBaseActivity activity, Looper looper) {
            super(looper);
            this.mActivity = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            HomeBaseActivity mainActivity = mActivity.get();
            if (mainActivity == null) {
                return;
            }

            //连接中一直上报
            if ((JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED)) {
                updateConnectStatus();
            } else if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_DISCONNECTED
                    && lastConnectState == JewInfoSingleton.STATE_CONNECTED) {
                updateConnectStatus();
            }
            lastConnectState = JewInfoSingleton.getInstance().getConnectState();
            //一分钟一次的传给服务器连接状态
            checkConnectHandler.sendEmptyMessageDelayed(0, 3 * 60 * 1000);
        }
    }


//    @EventInject(eventType = S.E.E_UPDATE_JPUSH_RID, runThread = TaskType.UI)
//    public void updateRegisterInfo(EventData data) {
//        putRegisterInfo();
//    }

    /**
     * 更新注册信息到服务端: 推送 token, 当前定位, 网络状态, 固件版本, 首饰类型, 连接状态等
     */
    private void putRegisterInfo() {
        int status = JewInfoSingleton.getInstance().getConnectState();
        String phoneType = Build.MODEL;
        String res = NetUtils.checkNetworkType(ToTwooApplication.baseContext);
        String ver = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "V");
        if (ver != null && ver.length() > 1)
            ver = ver.substring(1);
        else
            ver = "";
        String jewType = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");

        String countryCode = PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? "86" : "1");
        HttpHelper.update.updateRegister( PreferencesUtils.getString(this, JpushReceiver.REGISTER_ID, ""), status, phoneType,
                        TextUtils.isEmpty(res) ? "" : res.toUpperCase(), ver, jewType, countryCode, CommonUtils.getSystemVersion())
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean httpBaseBean) {

                    }
                });
    }

    public void showUnBondDialog() {
        final CustomDialog dialog = new CustomDialog(this);
        dialog.setMessage(R.string.error_bonded);
        dialog.setPositiveButton(R.string.system_setting_dialog_setting, v -> {
            Intent intent = new Intent(Settings.ACTION_BLUETOOTH_SETTINGS);
            startActivity(intent);
            dialog.dismiss();
        });
        dialog.show();
    }

    private void checkNewMessage() {
        LogUtils.e("checkNewMessage");
        HttpRequest.post(HttpHelper.URL_GET_MESSAGE_CHECK_NEW,
                HttpHelper.getBaseParams(true), new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
                        JSONObject data = HttpHelper.parserStringResponse(s);

                        NotifyMessage mes = new NotifyMessage();
                        if (data != null && data.optInt("unread_message_count") != 0) {
                            if (bottomInfos.size() == 2) {
                                mBottomTabPoint1.setVisibility(View.VISIBLE);
                            } else if (bottomInfos.size() == 3) {
                                mBottomTabPoint2.setVisibility(View.VISIBLE);
                            } else {
                                mBottomTabPoint3.setVisibility(View.VISIBLE);
                            }
                            mes.setIsNew(true);
                        } else {
                            if (bottomInfos.size() == 2) {
                                mBottomTabPoint1.setVisibility(View.GONE);
                            } else if (bottomInfos.size() == 3) {
                                mBottomTabPoint2.setVisibility(View.GONE);
                            } else {
                                mBottomTabPoint3.setVisibility(View.GONE);
                            }
                            mes.setIsNew(false);
                        }
                        EventBus.getDefault().postSticky(mes);
                    }
                });
    }

    private void selectTab(int i, boolean click) {
        selectTab(i, click, false);
    }

    private void selectTab(int i, boolean click, boolean isInit) {
        if (i != 2) {
            checkNewMessage();
        }
        if (currPosition == i && !isInit) {
            return;
        }
//        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
//            mHandler.postDelayed(this::checkGiftCard, 400);
//        }

        if (currPosition >= 0 && homeBasePageAdapter.getItem(currPosition) != null) {
            mHandler.post(() -> {
                homeBasePageAdapter.getItem(currPosition).onHide();
            });
        }

        currPosition = i;

        setBottom(i);

        if (click) {
            homeContentViewpager.setCurrentItem(i, false);
        }

        if (homeBasePageAdapter.getItem(currPosition) != null) {
            mHandler.post(() -> {
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOMEACTIVITY_ONSHOW, null);
                homeBasePageAdapter.getItem(currPosition).onShow();
            });
        }
//        updateConnectStatus();
    }

    private void updateConnectStatus() {
        String name = PreferencesUtils.getString(HomeBaseActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED)
            name = "";
        MemoryController.getInstance().checkConnectState(name);
    }

    /**
     * 展示请求蓝牙开启的对话框
     */
    public void showBluetoothDialog() {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
        dialog.setMessage(R.string.request_open_bluetooth);
        dialog.setSure(R.string.allow, v -> {
            BleUtils.enableBlueTooth(HomeBaseActivity.this);
            dialog.dismiss();

        });
        dialog.setCancel(R.string.cancel);

        dialog.show();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CHANGE, runThread = TaskType.UI)
    public void notifyJewerlyState(EventData data) {
        updateTopLayer();

        //设备电量变化不做刷新
        if (data != null) {
            return;
        }
        //连上后同步状态
        if (checkConnectHandler == null) {
            initJewelryThread();
        }
        checkConnectHandler.removeCallbacksAndMessages(null);
        checkConnectHandler.sendEmptyMessage(0);
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CONNECTED, runThread = TaskType.UI)
    public void onJewerlyConnected(EventData data) {
        //检查过弹窗再调用
        if (isCheckGifData) {
            checkOta();
        }
    }

    /**
     * 固件升级
     */
    private void checkOta() {
        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
            LogUtils.d("receive ota check request, real check after 4200ms.");
            mHandler.postDelayed(() -> {
                if (BleParams.isSecurityJewlery()) {
                    if (PreferencesUtils.getBoolean(this, IS_IMEI_SENT, false)) {
                        BluetoothManage.getInstance().checkOTA();
                    }
                } else {
                    BluetoothManage.getInstance().checkOTA();
                }
            }, 4500);
        }
    }


    private void updateTopLayer() {
        for (int i = 0; i < homeBasePageAdapter.getCount(); i++) {
            Fragment page = homeBasePageAdapter.getItem(i);

            if (page instanceof JewelryStateChangeListener) {
                ((HomeBaseActivity.JewelryStateChangeListener) page).onChange();
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOMEACTIVITY_ONSHOW, null);
            }
        }
    }

//    /**
//     * 检查当前是否有未接收贺卡, 加载对应资源, 成功之后展示
//     */
//    private void checkGreetingCard(boolean needLoad) {
//        if(BleParams.isSecurityJewlery()){
//            return;
//        }
//
//        GreetingCard card = GreetingCardLoader.getUnShowCard(this);
//        if (card != null) {
//            if (mResumed) {
//                showGreetingCard(card);
//            }
//        } else if (needLoad) {
//            GreetingCardLoader.getGreetingCardData(false);
//        }
//    }
//
//    /**
//     * 开始展示贺卡, 目前逻辑跳转贺卡接收页面,
//     *
//     * @param card
//     */
//    public void showGreetingCard(GreetingCard card) {
//        Intent intent = new Intent(this, GreetingCardShowActivity.class);
//        intent.putExtra(GreetingCardShowActivity.IS_NEW_CARD, true);
//        intent.putExtra("isFromPre", true);
//        intent.putExtra(GreetingCardShowActivity.EXAT_GREETING_CARD, card);
//        startActivity(intent);
//    }

    /**
     * 检查当前是否有未接收贺卡, 加载对应资源, 成功之后展示
     */
    private void checkGiftCard() {
        LogUtils.e("checkGiftCard 3");
        HttpHelper.card.getGift(ToTwooApplication.owner.getTotwooId(), ToTwooApplication.owner.getPhone(), PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<GiftCardReceiveBean>>() {
                    @Override
                    public void onCompleted() {
                        isCheckGifData = true;
                    }

                    @Override
                    public void onError(Throwable e) {
                        checkOta();
                    }

                    @Override
                    public void onNext(HttpBaseBean<GiftCardReceiveBean> giftCardReceiveBeanHttpBaseBean) {
                        if (giftCardReceiveBeanHttpBaseBean.getErrorCode() == 0 && giftCardReceiveBeanHttpBaseBean.getData().isHave()) {
                            GiftMessageBean giftMessageBean = new GiftMessageBean();
                            giftMessageBean.setGreetingCardId(giftCardReceiveBeanHttpBaseBean.getData().getGreetingCardId());
                            giftMessageBean.setSenderId(giftCardReceiveBeanHttpBaseBean.getData().getSenderId());
                            giftMessageBean.setSenderName(giftCardReceiveBeanHttpBaseBean.getData().getSenderName());
                            giftMessageBean.setGreetingCardType(giftCardReceiveBeanHttpBaseBean.getData().getGreetingCardType());
                            giftMessageBean.setSendTime(giftCardReceiveBeanHttpBaseBean.getData().getSendTime());
                            giftMessageBean.setGreetingCardData(giftCardReceiveBeanHttpBaseBean.getData().getGreetingCardData());
                            Intent intent = new Intent(HomeBaseActivity.this, GiftDataActivity.class);
                            intent.putExtra(CommonArgs.FROM_TYPE, GiftDataActivity.RECEIVER);
                            intent.putExtra(GiftDataActivity.ITEM, giftMessageBean);
                            startActivityForResult(intent, 80001);
                        } else {
                            checkOta();
                        }
                    }
                });

    }


    public interface JewelryStateChangeListener {
        void onChange();
    }

    /**
     * 清楚当前的所有的通知
     */
    private void clearTOTWOONotification() {
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        manager.cancel(NotifyDataModel.NOTI_TOTWOO_ID);

        PreferencesUtils.put(this, NotifyDataModel.NOTI_TOTWOO_COUNT_TAG, 0);
    }

    private CustomDialog failedDialog;

    public void showTokenFailedDialog() {
        if (failedDialog != null && failedDialog.isShowing()) {
            return;
        }
        //退出逻辑
        CommonUtils.clearUserData(false);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI);
        LocalJewelryDBHelper.getInstance().deleteAllBean();

        failedDialog = new CustomDialog(this);
        failedDialog.setMessage(R.string.token_invalid);
        failedDialog.setPositiveButton(R.string.i_know, v -> {
            showProgressDialog();
            mHandler.postDelayed(() -> {
                failedDialog.dismiss();
                sendCloseActivitiesBR(false);
                LogUtils.e("aab sendCloseActivitiesBR");
                HomeActivityControl.getInstance().openLoginActivity(HomeBaseActivity.this);
                finish();
                dismissProgressDialog();
            }, 800);
        });
        failedDialog.setCancelable(false);
        failedDialog.show();
    }

    /**
     * 接收虚拟人facebook分享传来的图片地址，调出分享
     *
     * @param data
     */
    @EventInject(eventType = S.E.E_VIS_FACEBOOK_IMAGE, runThread = TaskType.UI)
    public void onFaceBookImageShare(EventData data) {
        FaceBookSharePathEventData pathEventData = (FaceBookSharePathEventData) data;
        String path = pathEventData.getPath();
        ARCameraShareUtil.getInstance().imageShareToFaceBook(path, HomeBaseActivity.this);
    }

    /**
     * 接收虚拟人facebook分享传来的视频地址，调出分享
     *
     * @param data
     */
    @EventInject(eventType = S.E.E_VIS_FACEBOOK_VEDIO, runThread = TaskType.UI)
    public void onFaceBookVedioShare(EventData data) {
        FaceBookSharePathEventData pathEventData = (FaceBookSharePathEventData) data;
        String path = pathEventData.getPath();
        ARCameraShareUtil.getInstance().imageShareToFaceBook(path, HomeBaseActivity.this);
    }

    @EventInject(eventType = S.E.E_TOKEN_FAILED, runThread = TaskType.UI)
    public void onTokenFailed(EventData data) {
        showTokenFailedDialog();
    }

//    /**
//     * 接收登录页传来的需要 show 的 GreetingCard
//     */
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void receiveGreetingCard(GreetingCard card) {
//        if (mResumed && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
//            showGreetingCard(card);
//        } else {
//            GreetingCardLoader.saveUnShowCard(this, card);
//        }
//        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
//            showGreetingCard(card);
//        } else {
//            GreetingCardLoader.saveUnShowCard(this, card);
//        }
//    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void recieveLogout(Owner owner) {
        if (!CommonUtils.isLogin()) {
            WaterTimeDbHelper.getInstance().deleteAllTable();
            CustomNotifyDbHelper.getInstance().deleteAllBean();
            CommonUtils.setLogin(false);
            // 处理界面跳转工作
            sendCloseActivitiesBR(true);
        }
    }

    public static boolean hasUnread = false;

    @EventInject(eventType = S.E.E_RECEIVED_IM_MESSAGE, runThread = TaskType.UI)
    public void onReceivedIMMEssage(EventData data) {
        hasUnread = true;
        showLovePageNotify();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceiveState(final TotwooMessage message) {
        if (TextUtils.equals(message.getTotwooState(), TotwooMessage.TOTWOO_SEND_SUCCESS)) {
            return;
        }
        showLovePageNotify();
    }

    private void showLovePageNotify() {
        if (totwooIndex < 0) {
            return;
        }
        if (currPosition != 0 && totwooIndex == 0) {
            mBottomTabPoint0.setVisibility(View.VISIBLE);
        } else if (currPosition != 1 && totwooIndex == 1) {
            mBottomTabPoint1.setVisibility(View.VISIBLE);
        }
    }

    @EventInject(eventType = S.E.E_CAMERA_PERMISSION, runThread = TaskType.UI)
    public void onCheckCamera(EventData data) {
        if (!PermissionUtil.hasCameraPermission(this)) {
            return;
        }
        if (!PermissionUtil.hasStoragePermission(this)) {
            return;
        }
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_CAMERA_PERMISSION_HAS, null);
    }

    @EventInject(eventType = S.E.E_MESSAGE_SHOW_FAILED, runThread = TaskType.UI)
    public void onMessageShowFailed(EventData data) {
        checkGiftCard();
    }


    @EventInject(eventType = S.E.E_MESSAGE_SHOW_SUCCESSED, runThread = TaskType.UI)
    public void onMessageShowSuccess(EventData data) {
        HttpValues hv = (HttpValues) data;
        String tmp = JSONUtils.getString(hv.content, "data", "");
        final String message_id = JSONUtils.getString(tmp, "message_id", "");
        String img_url = JSONUtils.getString(tmp, "img_url", "");
        final String jump_url = JSONUtils.getString(tmp, "jump_url", "");
        final int is_share = JSONUtils.getInt(tmp, "is_share", 0);
        final String title = JSONUtils.getString(tmp, "title", "");
        final String content = JSONUtils.getString(tmp, "content", "");
        final String is_full_show = JSONUtils.getString(tmp, "is_full_show", "");
        messageView.setVisibility(View.VISIBLE);

        BitmapHelper.displayWithoutPlaceHolder(HomeBaseActivity.this, messageIv, img_url);
        if (!jump_url.equals("")) {
            messageIv.setOnClickListener(v -> {
                MessageController.getInstance().messageClick(message_id);
                String url = jump_url + DesUtil.fullSign();
                WebViewActivity.loadUrl(HomeBaseActivity.this, url, false, is_share == 1, title, content, is_full_show);
            });
        }

        messageClose.setOnClickListener(v -> {
            MessageController.getInstance().messageClick(message_id);
            messageView.setVisibility(View.GONE);
            checkGiftCard();
        });
    }

    @EventInject(eventType = S.E.E_FINISH_HOMEACTIVITY, runThread = TaskType.UI)
    public void onFinishThisActivity(EventData data) {
        finish();
    }

    @EventInject(eventType = S.E.E_UPDATE_EMOTION, runThread = TaskType.UI)
    public void onUpdateEmotion(EventData data) {
        ACache aCache = ACache.get(this);
        aCache.remove(CommonArgs.EMOJI_LIST);
    }

//    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_APART, runThread = TaskType.UI)
//    public void updateJewerlyState(EventData data) {
//        if (!isLogOut)
//            return;
//
//        failedDialog.dismiss();
//        sendCloseActivitiesBR(false);
//        LogUtils.e("aab sendCloseActivitiesBR");
//        HomeActivityControl.getInstance().openLoginActivity(this);
//        finish();
//    }

    @EventInject(eventType = S.E.E_LOVE_PAIR_ALBUM, runThread = TaskType.UI)
    public void onAlbumClick(EventData data) {
        PictureSelectUtil.with(this).gallery().crop(9, 16).setCallback(uri -> {
            try {
                CommonUtils.copyUriToPath(HomeBaseActivity.this, uri, CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOVE_PAIR_BACKGROUND, null);
            } catch (Exception e) {
                e.printStackTrace();
                ToastUtils.showLong(this, R.string.data_error);
            }
        }).select();
    }

    @EventInject(eventType = S.E.E_LOVE_PAIR_CAMERA, runThread = TaskType.UI)
    public void onCameraClick(EventData data) {
        PictureSelectUtil.with(this).camera().crop(9, 16).setCallback(uri -> {
            try {
                CommonUtils.copyUriToPath(HomeBaseActivity.this, uri, CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOVE_PAIR_BACKGROUND, null);
            } catch (Exception e) {
                e.printStackTrace();
                ToastUtils.showLong(this, R.string.data_error);
            }
        }).select();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_CODE_GUIDE_PAGE:
//                checkShowBatteryOptimizationIgnore();
                break;
            case 80001:
                checkOta();
                break;
        }
    }

    private boolean isHintShow = false;

    private void setOrderHintOn() {
        // 屏蔽排序的引导
//        angel_hint_order_cl.setVisibility(View.VISIBLE);
//        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
//            angel_hint_main_iv.setImageResource(R.drawable.angel_hint_cn);
//        } else {
//            angel_hint_main_iv.setImageResource(R.drawable.angel_hint_en);
//        }
//
//        isHintShow = true;
    }

    private void setBrightHintOn(boolean isShowLight) {
        if (isShowLight) {
            lollipop_hint_order_cl.setVisibility(View.VISIBLE);
            if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                lollipop_hint_main_iv.setImageResource(R.drawable.lollipop_hint_1_tv_cn);
            } else {
                lollipop_hint_main_iv.setImageResource(R.drawable.lollipop_hint_1_tv_en);
            }
            if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                lollipop_hint_main_iv_2.setImageResource(R.drawable.lollipop_hint_2_tv_cn);
            } else {
                lollipop_hint_main_iv_2.setImageResource(R.drawable.lollipop_hint_2_tv_en);
            }
//            lollipop_hint_order_iv.setOnClickListener(v -> {
//                startActivity(new Intent(HomeBaseActivity.this, BrightMusicActivity.class));
//                lollipop_hint_order_cl.setVisibility(View.GONE);
//                isHintShow = false;
//            });
        } else {
            reminder_hint_order_cl.setVisibility(View.VISIBLE);
            if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                reminder_hint_main_iv.setImageResource(R.drawable.remind_bottom_hint_cn);
            } else {
                reminder_hint_main_iv.setImageResource(R.drawable.remind_bottom_hint_en);
            }
            reminder_hint_order_iv.setImageResource(R.drawable.remind_bottom_hint_icon);
            reminder_hint_order_iv.setOnClickListener(v -> {
                startActivity(new Intent(HomeBaseActivity.this, BrightMusicActivity.class));
                reminder_hint_order_cl.setVisibility(View.GONE);
                isHintShow = false;
            });
        }

        isHintShow = true;
    }

    private void setLightHint() {
        safe_hint_order_cl.setVisibility(View.VISIBLE);
        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
            safe_hint_main_iv.setImageResource(R.drawable.safe_hint_light_cn);
        } else {
            safe_hint_main_iv.setImageResource(R.drawable.safe_hint_light_en);
        }
        ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.WRAP_CONTENT, ConstraintLayout.LayoutParams.WRAP_CONTENT);
        layoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
        layoutParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
        layoutParams.setMargins(0, CommonUtils.dip2px(HomeBaseActivity.this, 29.2f), CommonUtils.dip2px(HomeBaseActivity.this, 1), 0);
        safe_hint_order_iv.setLayoutParams(layoutParams);
        safe_hint_order_iv.setImageResource(R.drawable.safe_hint_light_icon);
        safe_hint_order_iv.setOnClickListener(v -> {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_SAFE_CLICK, null);
            safe_hint_order_cl.setVisibility(View.GONE);
            isHintShow = false;
        });
        isHintShow = true;
    }

    @EventInject(eventType = S.E.E_REMIND_HINT, runThread = TaskType.UI)
    public void onRemindHintReceiver(EventData data) {
        setBrightHintOn(false);
    }

    public boolean isSleepUpdate = false;

    @EventInject(eventType = S.E.E_LOADING_START, runThread = TaskType.UI)
    public void onLoadingStart(EventData data) {
        isSleepUpdate = true;
        mLoadingCl.setVisibility(View.VISIBLE);
        mLoadingLv.playAnimation();
    }

    @EventInject(eventType = S.E.E_LOADING_END, runThread = TaskType.UI)
    public void onLoadingEnd(EventData data) {
        isSleepUpdate = false;
        mLoadingCl.setVisibility(View.GONE);
        mLoadingLv.pauseAnimation();
    }

//    @EventInject(eventType = S.E.E_START_OTA_ACTIVITY, runThread = TaskType.UI)
//    public void startOTAActivity(EventData data) {
//        StartOtaParams startOtaParams = (StartOtaParams) data;
//        Intent otaIntent = new Intent(HomeBaseActivity.this, JewelryOTAActivity.class);
//        if (startOtaParams.isLowPower()) {
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_LOWPOWER);
//            otaIntent.putExtra(JewelryOTAActivity.IS_LOW_POWER, true);
//        } else {
//            if (TextUtils.isEmpty(startOtaParams.getPath())) {
//                return;
//            }
//            if (TextUtils.isEmpty(startOtaParams.getAddress())) {
//                return;
//            }
//            otaIntent.putExtra(JewelryOTAActivity.BLE_OTA_FILE_PATH_TAG, startOtaParams.getPath());
//            otaIntent.putExtra(JewelryOTAActivity.EXTRA_DFU_ADDRESS, startOtaParams.getAddress());
//        }
//        LogUtils.e("aab startActivity");
//        startActivity(otaIntent);
//    }

    @EventInject(eventType = S.E.E_SAFE_HINT, runThread = TaskType.UI)
    public void onSafeHintReceiver(EventData data) {
        setLightHint();
    }

    @EventInject(eventType = S.E.E_REMIND_HINT_LIGHT, runThread = TaskType.UI)
    public void onRemindLightHintReceiver(EventData data) {
        setBrightHintOn(true);
    }

    @EventInject(eventType = S.E.E_LOVE_HINT_TOTWOO, runThread = TaskType.UI)
    public void onLoveHintReceiver(EventData data) {
//        气泡引导 > 恭喜你弹窗 >重要设置 >忽略电池白名单
//        if (!Apputils.systemLanguageIsChinese(HomeBaseActivity.this)) {
//            love_hint_cl_1_iv.setImageResource(R.drawable.totwoo_hint_code_en);
//            love_hint_cl_1_text_iv.setImageResource(R.drawable.totwoo_hint_code_text_en);
//            love_hint_cl_2_text_iv.setImageResource(R.drawable.totwoo_hint_space_text_en);
//            love_hint_cl_3_text_iv.setImageResource(R.drawable.totwoo_hint_chat_text_en);
//            love_hint_cl_4_iv.setImageResource(R.drawable.totwoo_hint_twoo_en);
//            love_hint_cl_4_text_iv.setImageResource(R.drawable.totwoo_hint_twoo_text_en);
//        }
//        love_hint_cl.setVisibility(View.VISIBLE);
//        PreferencesUtils.put(HomeBaseActivity.this, LovePairedFragment.IS_TOTWOO_HINT_SHOWED, true);
        showNextDialog();
    }


    private void showNextDialog() {
        if (!PreferencesUtils.getBoolean(this, LoveFragment.PREF_HAS_SHOW_PAIRED_DIALOG, false)) {

            DialogHelper.MyFunction function = this::showImportantAlertDialog;
            DialogHelper.getInstance().getFirstPairedGuideDialogNew(this, function).show();
            PreferencesUtils.put(this, PREF_HAS_SHOW_PAIRED_DIALOG, true);
        }
    }

    @EventInject(eventType = S.E.E_LOVE_HINT_TOTWOO_DISMISS, runThread = TaskType.UI)
    public void onLoveDismissHintReceiver(EventData data) {
        love_hint_cl.setVisibility(View.GONE);
        PreferencesUtils.put(HomeBaseActivity.this, LovePairedFragment.IS_TOTWOO_HINT_SHOWED, true);
    }

    @EventInject(eventType = S.E.E_ANGEL_HINT, runThread = TaskType.UI)
    public void onAngelHintReceiver(EventData data) {
        setOrderHintOn();
    }

    @EventInject(eventType = S.E.E_MAGIC_HINT, runThread = TaskType.UI)
    public void onMagicHintReceiver(EventData data) {
        setOrderHintOn();
    }

    private boolean isBack = false;

    @Override
    public void onBackPressed() {
        if (isHintShow) {
            isHintShow = false;
            angel_hint_order_cl.setVisibility(View.GONE);
            return;
        }
        if (isBack) {
            super.onBackPressed();
            AppUtils.exitApp();
        } else {
            isBack = true;
            ToastUtils.showShort(HomeBaseActivity.this, getResources().getString(R.string.press_again_exit));
            mHandler.postDelayed(() -> isBack = false, 2000);
        }
    }

    private void setBottom(int index) {
        setDefaultBottom();
        switch (index) {
            case 0:
                mBottomTabIv0.setImageResource(bottomInfos.get(0).getSelectIconRes());
                mBottomTabTv0.setTextColor(text_select_color);
                mBottomTabPoint0.setVisibility(View.GONE);
                break;
            case 1:
                mBottomTabIv1.setImageResource(bottomInfos.get(1).getSelectIconRes());
                mBottomTabTv1.setTextColor(text_select_color);
                mBottomTabPoint1.setVisibility(View.GONE);
                break;
            case 2:
                mBottomTabIv2.setImageResource(bottomInfos.get(2).getSelectIconRes());
                mBottomTabTv2.setTextColor(text_select_color);
                mBottomTabPoint2.setVisibility(View.GONE);
                break;
            case 3:
                mBottomTabIv3.setImageResource(bottomInfos.get(3).getSelectIconRes());
                mBottomTabTv3.setTextColor(text_select_color);
                mBottomTabPoint3.setVisibility(View.GONE);
                break;
        }
    }


    /**
     * 检测 App 自升级.
     *
     * @param isManual true 表示手动检测, false 后台自动检测
     */
    public void checkAppUpdate(boolean isManual) {
//        if (isForceUpdate()) {
//            isForeUpdate = true;
//            showUpdateVersionDialog(PreferencesUtils.getString(HomeBaseActivity.this, CommonArgs.PREF_FORCE_UPDATE_INFO, ""),
//                    PreferencesUtils.getString(HomeBaseActivity.this, CommonArgs.PREF_FORCE_UPDATE_URL, ""),
//                    PreferencesUtils.getString(HomeBaseActivity.this, CommonArgs.PREF_FORCE_UPDATE_SERVICE_VERSION, ""),
//                    PreferencesUtils.getLong(HomeBaseActivity.this, CommonArgs.PREF_FORCE_UPDATE_LENGTH, 0));
//            return;
//        }
        HttpHelper.commonService.checkAppUpdate(Apputils.getVersionCode(HomeBaseActivity.this) + "")
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<AppUpdateBean>>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                        //弹出重要提醒对话款
                        // 添加Activity状态检查，防止WindowManager$BadTokenException
                        if (!isFinishing() && !isDestroyed()) {
                            showImportantAlertDialog();
                        }
                    }

                    @Override
                    public void onNext(HttpBaseBean<AppUpdateBean> appUpdateBeanHttpBaseBean) {
                        if (appUpdateBeanHttpBaseBean.getErrorCode() == 0) {
                            String url = appUpdateBeanHttpBaseBean.getData().getUrl();
                            if (!TextUtils.isEmpty(url) && URLUtil.isValidUrl(url)) {
                                DialogHelper.MyFunction function = HomeBaseActivity.this::showImportantAlertDialog;
                                showUpdateVersionDialog(appUpdateBeanHttpBaseBean.getData(), function);
                            }
                        } else {
                            if (isManual) {
                                ToastUtils.showLong(HomeBaseActivity.this, R.string.no_need_update);
                            }
                            FileUtils.cleanCacheApk();
                            //弹出重要提醒对话款
                            // 添加Activity状态检查，防止WindowManager$BadTokenException
                            if (!isFinishing() && !isDestroyed()) {
                                showImportantAlertDialog();
                            }
                        }
                    }
                });
    }

    private void showUpdateVersionDialog(AppUpdateBean data, DialogHelper.MyFunction function) {
        AppDownloadDialog appDownloadDialog = new AppDownloadDialog(this, data, function);
        appDownloadDialog.show();
    }

    private void setDefaultBottom() {
        if (bottomOnlyIcon) {
            mBottomTabTv0.setVisibility(View.GONE);
            mBottomTabTv1.setVisibility(View.GONE);
            mBottomTabTv2.setVisibility(View.GONE);
            mBottomTabTv3.setVisibility(View.GONE);
        }

        mBottomTabIv0.setImageResource(bottomInfos.get(0).getUnSelectIconRes());
        mBottomTabIv1.setImageResource(bottomInfos.get(1).getUnSelectIconRes());

        if (!bottomOnlyIcon) {
            mBottomTabTv0.setText(bottomInfos.get(0).getNameRes());
            mBottomTabTv0.setTextColor(text_default_color);
            mBottomTabTv1.setText(bottomInfos.get(1).getNameRes());
            mBottomTabTv1.setTextColor(text_default_color);
        }

        if (bottomInfos.size() == 2) {
            mBottomRl2.setVisibility(View.GONE);
            mBottomRl3.setVisibility(View.GONE);
        } else if (bottomInfos.size() == 3) {
            mBottomRl2.setVisibility(View.VISIBLE);
            mBottomTabIv2.setImageResource(bottomInfos.get(2).getUnSelectIconRes());
            if (!bottomOnlyIcon) {
                mBottomTabTv2.setText(bottomInfos.get(2).getNameRes());
                mBottomTabTv2.setTextColor(text_default_color);
            }
            mBottomRl3.setVisibility(View.GONE);
        } else {
            mBottomRl2.setVisibility(View.VISIBLE);
            mBottomTabIv2.setImageResource(bottomInfos.get(2).getUnSelectIconRes());
            mBottomRl3.setVisibility(View.VISIBLE);
            mBottomTabIv3.setImageResource(bottomInfos.get(3).getUnSelectIconRes());


            if (!bottomOnlyIcon) {
                mBottomTabTv2.setText(bottomInfos.get(2).getNameRes());
                mBottomTabTv2.setTextColor(text_default_color);
                mBottomTabTv3.setText(bottomInfos.get(3).getNameRes());
                mBottomTabTv3.setTextColor(text_default_color);
            }
        }
    }

    private class HomeBasePageAdapter extends PagerAdapter {
        public Class<? extends BaseFragment>[] fragmentsClasses;
        private final FragmentManager mFragmentManager;
        private FragmentTransaction mCurTransaction = null;
        private Fragment mCurrentPrimaryItem = null;
        private BaseFragment[] fragmentsData;

        public HomeBasePageAdapter(FragmentManager fm, Class<? extends BaseFragment>[] fragmentsClasses) {
            mFragmentManager = fm;
            this.fragmentsClasses = fragmentsClasses;
            fragmentsData = new BaseFragment[fragmentsClasses.length];
            for (int i = 0; i < fragmentsClasses.length; i++) {
                try {
                    fragmentsData[i] = fragmentsClasses[i].newInstance();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public int getCount() {
            return fragmentsData.length;
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return ((Fragment) object).getView() == view;
        }

        public BaseFragment getItem(int position) {
            List<Fragment> fragments = getSupportFragmentManager().getFragments();
            for (Fragment fragment : fragments) {
                if (fragment.getClass().getName().equals(fragmentsClasses[position].getName())) {
                    return (BaseFragment) fragment;
                }
            }
            return fragmentsData[position];
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            if (mCurTransaction == null) {
                mCurTransaction = mFragmentManager.beginTransaction();
            }

            // 当因为Config 变化, Activity 重启后, FragmentManager会内部自动创建一套新的 Fragment 实例,
            // 与 Adapter 保存的实例不一致, 因此无法收到相关 onShow, onHide 回调

            String name = makeFragmentName(position);
            Fragment fragment = mFragmentManager.findFragmentByTag(name);
            if (fragment != null) {
                mCurTransaction.show(fragment);
            } else {
                fragment = getItem(position);
                mCurTransaction.add(container.getId(), fragment, name).show(fragment);
            }

            if (fragment != mCurrentPrimaryItem) {
                fragment.setMenuVisibility(false);
                fragment.setUserVisibleHint(false);
            }

            return fragment;
        }

        private String makeFragmentName(int position) {
            return "android:switcher:" + fragmentsClasses[position].getCanonicalName();
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            if (mCurTransaction == null) {
                mCurTransaction = mFragmentManager.beginTransaction();
            }
            mCurTransaction.hide((Fragment) object);
        }

        @Override
        public void setPrimaryItem(ViewGroup container, int position, Object object) {
            Fragment fragment = (Fragment) object;
            if (fragment != mCurrentPrimaryItem) {
                if (mCurrentPrimaryItem != null) {
                    mCurrentPrimaryItem.setMenuVisibility(false);
                    mCurrentPrimaryItem.setUserVisibleHint(false);
                }
                if (fragment != null) {
                    fragment.setMenuVisibility(true);
                    fragment.setUserVisibleHint(true);
                }
                mCurrentPrimaryItem = fragment;
            }
        }

        @Override
        public void finishUpdate(ViewGroup container) {
            try {
                if (mCurTransaction != null) {
                    mCurTransaction.commitNowAllowingStateLoss();
                    mCurTransaction = null;
                    mFragmentManager.executePendingTransactions();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


}
