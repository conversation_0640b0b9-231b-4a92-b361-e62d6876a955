package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.CallRemindContact;
import com.totwoo.totwoo.bean.LocalContactsBean;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import rx.Observable;
import rx.Subscriber;

public class ContactsActivityForCall extends ContactsBaseActivity{

    public static final String ALLOW_ADD_COUNT = "allowAddCount";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        selectSize = getIntent().getIntExtra(ALLOW_ADD_COUNT,3);
        selectOutOfIndexMsg = getString(R.string.important_contact_exceed);
        mTitleTv.setText(getString(R.string.select_contact));
        mAddCl.setVisibility(View.GONE);
    }

    @Override
    void clickConfirm() {
        ArrayList<CallRemindContact> mCallRemindContacts = new ArrayList<>();
        Observable.from(selectBeans)
                .subscribe(new Subscriber<LocalContactsBean>() {
                    @Override
                    public void onCompleted() {
                        if (mCallRemindContacts.size() == 0) {
                            ToastUtils.show(getBaseContext(), getString(R.string.call_remind_add_contact_0), Toast.LENGTH_LONG);
                            return;
                        }
                        String dataJson = PreferencesUtils.getString(getBaseContext(), CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_DATA_KEY, "");
                        ArrayList<CallRemindContact> contacts = new Gson().fromJson(dataJson, new TypeToken<List<CallRemindContact>>() {
                        }.getType());
                        if (contacts != null) {
                            for (CallRemindContact mContact : contacts) {
                                for (CallRemindContact contact : mCallRemindContacts) {
                                    if (contact.getPhoneNumber().equals(mContact.getPhoneNumber())) {
                                        ToastUtils.show(getBaseContext(), getString(R.string.cant_choose_repeated_contact), Toast.LENGTH_LONG);
                                        return;
                                    }
                                }
                            }
                        }
                        getIntent().putParcelableArrayListExtra("contact", mCallRemindContacts);
                        setResult(0, getIntent());
                        finish();
                    }

                    @Override
                    public void onError(Throwable e) {
                        // RxJava错误处理回调
                        LogUtils.e("ContactsActivityForCall", "Contact processing error: " + e.getMessage());
                        ToastUtils.showLong(getBaseContext(), "处理联系人时发生错误");
                    }

                    @Override
                    public void onNext(LocalContactsBean contact) {
                        CallRemindContact callRemindContact = new CallRemindContact(contact.getNumber(), contact.getHeadIconId(), contact.getName(), "RED",0);
                        mCallRemindContacts.add(callRemindContact);
                    }
                });
    }
}
