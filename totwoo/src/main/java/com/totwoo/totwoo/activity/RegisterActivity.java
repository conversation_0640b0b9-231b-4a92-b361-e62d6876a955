package com.totwoo.totwoo.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.Toast;

import com.blankj.utilcode.util.ClickUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.LocalHttpJewelryInfo;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.bean.LoginInfoBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.databinding.ActivityRegisterBinding;
import com.totwoo.totwoo.receiver.JpushReceiver;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.ApiException;
import com.totwoo.totwoo.utils.CaptchaController;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SimpleTextWatcher;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomProgressBarDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import rx.Subscriber;

public class RegisterActivity extends BaseActivity implements SubscriberListener {
    /**
     * 标记验证码可重复获取的倒计时器，界面销毁是要停止计时
     */
    private CountDownTimer cuntTimer;

    private String countryCodeValue;

    private ActivityRegisterBinding binding;

    private CustomProgressBarDialog loadingDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRegisterBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        ClickUtils.expandClickArea(binding.registerPolicyCheckbox, 30);


        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        InjectUtils.injectOnlyEvent(this);

        countryCodeValue = PreferencesUtils.getString(ToTwooApplication.baseContext,
                CommonArgs.COUNTRY_CODE_KEY,
                Apputils.getSystemLanguageCountryCode(this));

        binding.registerCountryCodeTv.setText("+" + countryCodeValue);

        initView();
    }

    private void initView() {
        setSpinState(false);

        String extraPhone = getIntent().getStringExtra(CommonArgs.PREF_LAST_PHONE);
        if (extraPhone != null) {
            binding.registerPhoneEt.setText(extraPhone);
        }

        binding.registerPhoneEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    binding.registerPhoneClearIv.setVisibility(View.VISIBLE);
                } else {
                    binding.registerPhoneClearIv.setVisibility(View.INVISIBLE);
                }
            }
        });
        binding.registerCodeEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    binding.registerCodeClearIv.setVisibility(View.VISIBLE);
                } else {
                    binding.registerCodeClearIv.setVisibility(View.INVISIBLE);
                }
            }
        });
        binding.registerPasswordEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    binding.registerPwdClearIv.setVisibility(View.VISIBLE);
                    binding.passwordSeeIv.setVisibility(View.VISIBLE);
                } else {
                    binding.registerPwdClearIv.setVisibility(View.INVISIBLE);
                    binding.passwordSeeIv.setVisibility(View.INVISIBLE);
                }
            }
        });
        binding.registerCodeEt.setOnEditorActionListener((textView, id, keyEvent) -> {
            if (id == 1024 || id == EditorInfo.IME_NULL) {
                doRegister();
                return true;
            }
            return false;
        });

        binding.registerCountryCodeTv.setOnClickListener(v -> {
            Intent intent = new Intent(this, CountryCodeListActivity.class);
            startActivityForResult(intent, 0);
            // 切换动画
            overridePendingTransition(R.anim.activity_fade_in,
                    R.anim.activity_fade_out);
        });
        binding.registerPhoneClearIv.setOnClickListener(v -> binding.registerPhoneEt.setText(""));
        binding.registerPwdClearIv.setOnClickListener(v -> binding.registerPasswordEt.setText(""));
        binding.registerCodeClearIv.setOnClickListener(v -> binding.registerCodeEt.setText(""));
        binding.registerRegisterTv.setOnClickListener(v -> doRegister());
        binding.registerGetVoiceTv.setOnClickListener(v -> getVoiceCode());
        binding.registerCountDownTv.setOnClickListener(v -> getMsgCode());
        binding.passwordSeeIv.setOnClickListener(v -> hideOrShowPassword());

        binding.registerPolicyTv.setText(CommonUtils.stylePolicyString(this));
        binding.registerPolicyTv.setMovementMethod(LinkMovementMethod.getInstance());
    }


    private void hideOrShowPassword() {
        if (binding.registerPasswordEt.getInputType() != 128) {
            binding.passwordSeeIv.setImageResource(R.drawable.password_eye_open_black);
            binding.registerPasswordEt.setInputType(128);
        } else {
            binding.passwordSeeIv.setImageResource(R.drawable.password_eye_close_black);
            binding.registerPasswordEt.setInputType(129);
        }
        if (binding.registerPasswordEt.getText() != null) {
            binding.registerPasswordEt.setSelection(binding.registerPasswordEt.getText().length());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (cuntTimer != null) {
            cuntTimer.cancel();
        }
        InjectUtils.injectUnregisterListenerAll(this);
    }

    private boolean isMsgHaving;

    private void getMsgCode() {
        if (isVoiceHaving) {
            ToastUtils.showLong(RegisterActivity.this, R.string.voice_is_having);
            return;
        }
        if (isMsgHaving) {
            ToastUtils.showLong(RegisterActivity.this, R.string.verification_too_ofen);
            return;
        }
        String phone = binding.registerPhoneEt.getText().toString();

        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showLong(this, R.string.error_invalid_phone);
        } else if (!isPhoneValid(phone)) {
            ToastUtils.showLong(this, R.string.error_incorrect_phone);
        } else {
            CommonUtils.phoneSmsPre0CheckAndDo(this, countryCodeValue, phone, () -> {
                CaptchaController.getInstance().checkAndShowCaptcha(this, "reg", countryCodeValue + phone, countryCodeValue, (ret, ticket, randomStr) -> {
                    if (ret == 0) {
                        requestSms(countryCodeValue + phone, ticket, randomStr);
                    } else {
                        isMsgHaving = false;
                        if (ret != -10001) {
                            ToastUtils.showLong(this, R.string.verification_failed);
                        }
                    }
                });
            });
        }
    }

    private void requestSms(String phone, String ticket, String randomStr) {
        int time = (int) (System.currentTimeMillis() / 1000);
        String sourceStr = time + "+" + phone + "+" + "totwoo_safe_202311";

        String firmwareType = null;
        if (ticket != null && ticket.startsWith(BleParams.COMMON_JEWELEY_PRE)) {
            firmwareType = ticket;
            ticket = "";
        }

        HttpHelper.loginV3Service.getSmsT(phone, time, "reg", CommonUtils.md5(sourceStr), firmwareType, ticket, randomStr)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<String>>() {
                    @Override
                    public void onStart() {
                        isMsgHaving = true;
                        super.onStart();
                        if (loadingDialog == null) {
                            loadingDialog = new CustomProgressBarDialog(RegisterActivity.this);
                        }
                        loadingDialog.show();
                    }

                    @Override
                    public void onCompleted() {
                        if (loadingDialog != null && loadingDialog.isShowing()) {
                            loadingDialog.dismiss();
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(RegisterActivity.this, R.string.error_net);
                        isMsgHaving = false;
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            startCountdown(false);

                            etGetFoucs();
                            ToastUtils.showLong(RegisterActivity.this,
                                    R.string.vercode_has_send);
                        } else if (stringHttpBaseBean.getErrorCode() == 800) {
                            // 临时验证码
                            String msg = stringHttpBaseBean.getErrorMsg();
                            ToastUtils.showLong(RegisterActivity.this, msg);

                            String code = CommonUtils.extractVerCode(msg);

                            if (!TextUtils.isEmpty(code)) {
                                binding.registerCodeEt.setText(code);
                            }

                            etGetFoucs();
                            startCountdown(false);
                        } else {
                            String errMsg = ApiException.getHttpErrMessage(stringHttpBaseBean.getErrorCode(), stringHttpBaseBean.getErrorMsg());
                            ToastUtils.showLong(RegisterActivity.this, errMsg);
                            isMsgHaving = false;
                        }
                    }
                });
    }

    /**
     * 开始获取验证码的倒计时显示， 及时期间禁止点击
     */
    protected void startCountdown(boolean isVoice) {
        cuntTimer = new CountDownTimer(60 * 1000, 1000) {
            @SuppressLint("SetTextI18n")
            @Override
            public void onTick(long millisUntilFinished) {
                if (isVoice) {
                    binding.registerGetVoiceTv.setText(millisUntilFinished / 1000 + getString(R.string.the_second_can_repeat));
                } else {
                    binding.registerCountDownTv.setText(millisUntilFinished / 1000 + getString(R.string.the_second_can_repeat));
                }
            }

            @Override
            public void onFinish() {
                if (isVoice) {
                    binding.registerGetVoiceTv.setText(getString(R.string.voice_code));
                    isVoiceHaving = false;
                } else {
                    binding.registerCountDownTv.setText(getString(R.string.get_verification_code));
                    isMsgHaving = false;
                }
            }
        };
        cuntTimer.start();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (resultCode) {
            case 0:
                if (data != null) {
                    countryCodeValue = data.getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                    binding.registerCountryCodeTv.setText("+" + countryCodeValue);
                }
                break;

            default:
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 验证手机号是否有效
     *
     * @param phone
     * @return
     */
    private boolean isPhoneValid(String phone) {
        if (countryCodeValue.equals("86")) {
            Pattern p = Pattern.compile("^1\\d{10}$");
            Matcher m = p.matcher(phone);
            return m.matches();
        }
        return true;

    }

    private void etGetFoucs() {
        binding.registerCodeEt.setFocusable(true);
        binding.registerCodeEt.setFocusableInTouchMode(true);
        binding.registerCodeEt.requestFocus();
    }

    private boolean isVoiceHaving;

    private void getVoiceCode() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_CLICKGETVOIVE);
        if (isVoiceHaving) {
            ToastUtils.show(RegisterActivity.this, R.string.verification_too_ofen, Toast.LENGTH_SHORT);
            return;
        }
        if (isMsgHaving) {
            ToastUtils.show(RegisterActivity.this, R.string.msg_is_having, Toast.LENGTH_SHORT);
            return;
        }
        final String phone = binding.registerPhoneEt.getText().toString();

        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShort(this, R.string.error_invalid_phone);
        } else if (!isPhoneValid(phone)) {
            ToastUtils.showShort(this, R.string.error_incorrect_phone);
        } else {
            CommonUtils.phoneSmsPre0CheckAndDo(this, countryCodeValue, phone, () -> {
                isVoiceHaving = true;
                CaptchaController.getInstance().checkAndShowCaptcha(this, "reg", countryCodeValue + phone, countryCodeValue, (ret, ticket, randomStr) -> {
                    if (ret == 0) {
                        requestVoiceSms(countryCodeValue + phone, ticket, randomStr);
                    } else {
                        isVoiceHaving = false;
                        if (ret != -10001) {
                            ToastUtils.showLong(this, R.string.verification_failed);
                        }
                    }
                });
            });
        }
    }

    private void requestVoiceSms(String phone, String ticket, String randomStr) {
        int time = (int) (System.currentTimeMillis() / 1000);
        HttpHelper.loginV3Service.getVoiceSmsT(phone, time, HttpHelper.genNewSign(time, phone), ticket, randomStr)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(RegisterActivity.this, R.string.error_net);
                        isVoiceHaving = false;
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            isVoiceHaving = true;
                            ToastUtils.showLong(RegisterActivity.this, R.string.vercode_voice_send);
                            startCountdown(true);
                        } else {
                            isVoiceHaving = false;
                            if (stringHttpBaseBean.getErrorMsg() == null || stringHttpBaseBean.getErrorMsg().isEmpty()) {
                                ToastUtils.show(RegisterActivity.this, getString(R.string.error_net), Toast.LENGTH_SHORT);
                            } else {
                                ToastUtils.show(RegisterActivity.this, stringHttpBaseBean.getErrorMsg(), Toast.LENGTH_SHORT);
                            }
                        }
                    }
                });
    }


    /**
     * 验证验证码是否有效
     *
     * @param vercode
     * @return
     */
    private boolean isVerCodeValid(String vercode) {
        Pattern p = Pattern.compile("^\\d{4}$");
        Matcher m = p.matcher(vercode);
        return m.matches();
    }

    private void doRegister() {
        KeyboardUtils.hideSoftInput(this);
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_CLICKCODELOGIN);
        String phone = binding.registerPhoneEt.getText().toString();
        String vercode = binding.registerCodeEt.getText().toString();
        String pwd = binding.registerPasswordEt.getText().toString();
        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShort(this, R.string.error_invalid_phone);
        } else if (!isPhoneValid(phone)) {
            ToastUtils.showShort(this, R.string.error_incorrect_phone);
        } else if (TextUtils.isEmpty(vercode)) {
            ToastUtils.showShort(this, R.string.verification_code);
        } else if (!isVerCodeValid(vercode)) {
            ToastUtils.showShort(this, R.string.error_incorrect_vercode);
        } else if (TextUtils.isEmpty(pwd)) {
            ToastUtils.show(this, R.string.set_password_error_length, Toast.LENGTH_SHORT);
        } else if (!CommonUtils.isPasswordValid(pwd)) {
            ToastUtils.show(this, R.string.set_password_error_length, Toast.LENGTH_SHORT);
        } else if (!binding.registerPolicyCheckbox.isChecked()) {
            ToastUtils.showLong(this, getString(R.string.terms_agree_tips));
        } else {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(binding.registerRegisterTv.getWindowToken(), 0);
            String registerId = PreferencesUtils.getString(this, JpushReceiver.REGISTER_ID, "");
            LogUtils.e("registerId = " + registerId);

            int time = (int) (System.currentTimeMillis() / 1000);
            HttpHelper.loginV3Service.register(countryCodeValue + phone, time, CommonUtils.pwdEncode(pwd), HttpHelper.genNewSign(time, (countryCodeValue + phone)), vercode, registerId, "0")
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<LoginInfoBean>>() {
                        @Override
                        public void onStart() {
                            super.onStart();
                            binding.registerRegisterTv.setClickable(false);

                            if (loadingDialog == null) {
                                loadingDialog = new CustomProgressBarDialog(RegisterActivity.this);
                            }
                            loadingDialog.show();
                        }

                        @Override
                        public void onCompleted() {
                            binding.registerRegisterTv.setClickable(true);
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(RegisterActivity.this, R.string.error_net);
                            binding.registerRegisterTv.setClickable(true);
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                        }

                        @Override
                        public void onNext(HttpBaseBean<LoginInfoBean> loginInfoBeanHttpBaseBean) {
                            if (loginInfoBeanHttpBaseBean.getErrorCode() == 0) {
                                PreferencesUtils.put(RegisterActivity.this, CommonArgs.PREF_LAST_ENCODE_PASSWORD, CommonUtils.pwdEncode(pwd));
                                PreferencesUtils.put(RegisterActivity.this, CommonArgs.COUNTRY_CODE_KEY, countryCodeValue);
                                CommonUtils.setInfo(RegisterActivity.this, loginInfoBeanHttpBaseBean.getData());
                                TimInitBusiness.login();
                                HttpHelper.multiJewelryService.getJewelryList()
                                        .compose(HttpHelper.rxSchedulerHelper())
                                        .subscribe(new Subscriber<HttpBaseBean<List<LocalHttpJewelryInfo>>>() {
                                            @Override
                                            public void onCompleted() {
                                                binding.registerRegisterTv.setClickable(true);
                                            }

                                            @Override
                                            public void onError(Throwable e) {
                                                ToastUtils.showShort(RegisterActivity.this, R.string.error_net);
                                                binding.registerRegisterTv.setClickable(true);
                                            }

                                            @Override
                                            public void onNext(HttpBaseBean<List<LocalHttpJewelryInfo>> listHttpBaseBean) {
                                                if (listHttpBaseBean.getErrorCode() == 0 && listHttpBaseBean.getData() != null && listHttpBaseBean.getData().size() > 0) {
                                                    LogUtils.e("listHttpBaseBean.getData().size() = " + listHttpBaseBean.getData().size());
                                                    for (LocalHttpJewelryInfo info : listHttpBaseBean.getData()) {
                                                        LogUtils.e("info = " + info);
                                                        LocalJewelryDBHelper.getInstance().addBean(new LocalJewelryInfo(info.getMac_address(), info.getDevice_name(), info.getIs_select(), info.getCreate_time() * 1000));
                                                    }
                                                    try {
                                                        String jewleryInfo = LocalJewelryDBHelper.getInstance().getSelectedBean().toString();
                                                        LogUtils.e("jewleryInfo = " + jewleryInfo);
                                                    } catch (DbException e) {
                                                        LogUtils.e("e = " + e, e);
                                                    }
                                                }

                                                goNext();
//                                                if (loginInfoBeanHttpBaseBean.getData().getPwd_frame() == 0) {
//                                                    goNext();
//                                                } else {
//                                                    startActivity(new Intent(RegisterActivity.this, SetPasswordActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
//                                                    finish();
//                                                }
                                            }
                                        });

//                            } else if (loginInfoBeanHttpBaseBean.getErrorCode() == 108) {
//                                ToastUtils.showShort(RegisterActivity.this, R.string.error_incorrect_vercode);
                            } else {
                                ToastUtils.showShort(RegisterActivity.this, HttpHelper.getUnknownErrorMessage(RegisterActivity.this, loginInfoBeanHttpBaseBean.getErrorMsg()));
                            }
                        }
                    });
        }
    }


    private void goNext() {
//        try {
//            if (!LocalJewelryDBHelper.getInstance().getAllBeans().isEmpty()) {
//                LocalJewelryInfo info = LocalJewelryDBHelper.getInstance().getSelectedBean();
//                PreferencesUtils.put(RegisterActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, info.getMac_address());
//                PreferencesUtils.put(RegisterActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, info.getName());
//                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
//                BluetoothManage.getInstance().startBackgroundScan();
//                LogUtils.e("HomeActivityControl");
//                HomeActivityControl.getInstance().connectJew(RegisterActivity.this);
//            } else {
//                startActivity(new Intent(this, JewelrySelectActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
//            }
//        } catch (DbException e) {
//            e.printStackTrace();
//        }

        startActivity(new Intent(this, InitInfoActivity.class)
                .putExtra(InitInfoActivity.INIT_INFO, true));
        finish();
    }

    /**
     * 忘记密码验证成功
     * ForgetPasswordCodeActivity
     */
    @EventInject(eventType = S.E.E_LOGIN_SUCCESS, runThread = TaskType.UI)
    public void verifySuccess(EventData data) {
        finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
