package com.totwoo.totwoo.activity.memory;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.ScrollingMovementMethod;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;

import java.text.SimpleDateFormat;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/8.
 */

public class MemorySayShowActivity extends BaseActivity implements SubscriberListener
{
    @BindView(R.id.memory_say_tx)
    TextView tv;

    private MemoryBean mb;
    private CustomDialog dialog;

    private Dialog dialog1;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_say_show1);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        tv.setMovementMethod(ScrollingMovementMethod.getInstance());
        init();
        initTopBar2();
    }

    private void init()
    {
        Intent intent = getIntent();
        mb = (MemoryBean) intent.getSerializableExtra(S.M.M_IMAGES);
        SpannableString styledText = new SpannableString(mb.content);
        styledText.setSpan(new TextAppearanceSpan(this, R.style.memory_say_text_style_big), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        styledText.setSpan(new TextAppearanceSpan(this, R.style.memory_say_text_style_small), 1, mb.content.length()-1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        tv.setText(styledText, TextView.BufferType.SPANNABLE);
    }

    protected void initTopBar2()
    {
        setTopBackIcon(R.drawable.back_icon_black);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopRightIcon(R.drawable.delete);
        setTopRightOnClick(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                getPhotoDialog();
            }
        });
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
    }

    public void getPhotoDialog()
    {
        dialog = new CustomDialog(this);
        dialog.setTitle("");
        dialog.setMessage(R.string.memory_delete);
        dialog.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                MemoryController.getInstance().delete(mb);
            }
        });
        dialog.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void onDeleteMemorySuccessed(EventData data)
    {
        ToastUtils.showShort(this, R.string.memory_delete_success);
        if (dialog1 != null)
            dialog1.dismiss();
        this.finish();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_FAILED, runThread = TaskType.UI)
    public void onDeleteMemoryFailed(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        String msg = hv.errorMesg;
        ToastUtils.showShort(this, msg);
        if (dialog1 != null)
            dialog1.dismiss();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        if (dialog1 != null)
            dialog1.dismiss();
    }
}
