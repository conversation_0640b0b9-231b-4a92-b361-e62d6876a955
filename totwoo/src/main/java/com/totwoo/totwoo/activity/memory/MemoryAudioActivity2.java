package com.totwoo.totwoo.activity.memory;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.component.http.HttpParams;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.StringUtils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.newConrtoller.UpdatePictureController;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.LoadingDialog;
import com.totwoo.totwoo.widget.SceneAnimation;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/8.
 */

public class MemoryAudioActivity2 extends BaseActivity implements View.OnClickListener, SubscriberListener
{
    @BindView(R.id.make_card_audio_play_btn)
    ImageView mAudioVideoPlayBtn;

    @BindView(R.id.make_card_top_conver_layer)
    ImageView mMakeCardTopCoverIv;

    @BindView (R.id.make_card_audio_delete_iv)
    ImageView mDelete;

    @BindView (R.id.memory_vedio_add_edit)
    EditText mEdit;

    @BindView (R.id.memory_vedio_add_edit_num)
    TextView num;

    @BindView (R.id.memory_vedio_add_save)
    TextView save;

    @BindView (R.id.audio_gif)
    ImageView audioGif;

    @BindView (R.id.memory_save_gif)
    ImageView saveGif;

    @BindView (R.id.memory_save_bg)
    View saveBg;

    private MediaPlayer mPlayer;
    private Dialog dialog;
    private CustomDialog dialog1;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_audio2);
        ButterKnife.bind(this);

        InjectUtils.injectOnlyEvent(this);

        init();
        initListener();
    }

    private void initListener()
    {
        mDelete.setOnClickListener(this);
        mEdit.addTextChangedListener(new TextWatcher()
        {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after)
            {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count)
            {

            }

            @Override
            public void afterTextChanged(Editable s)
            {
                String content = mEdit.getText().toString();
                num.setText(content.length() + "/100");
            }
        });
        save.setOnClickListener(this);
    }

    protected void initTopBar()
    {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.memory_audio_title);
    }

    private void init()
    {
        sa = new SceneAnimation(audioGif, MemoryAudioActivity.gifs, 10, false);
        Intent intent = getIntent();
        String res = intent.getStringExtra(S.M.M_CONTENT);
        mEdit.setText(res);
        if (res != null)
            num.setText(res.length() + "/100");

        mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
        mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
        if (mPlayer == null)
            mPlayer = new MediaPlayer();
        try
        {
            mPlayer.setDataSource(MemoryAudioActivity.MAKE_CARD_AUDIO_RE_PATH);
            mPlayer.prepare();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        mAudioVideoPlayBtn.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                mMakeCardTopCoverIv.setVisibility(View.GONE);
                mAudioVideoPlayBtn.setVisibility(View.GONE);
                mPlayer.start();
                //((AnimationDrawable) audioGif.getDrawable()).start();
                sa.start();
                mPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener()
                {
                    @Override
                    public void onCompletion(MediaPlayer mp)
                    {
                        //((AnimationDrawable) audioGif.getDrawable()).stop();
                        mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
                        mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                        sa.stop();
                    }
                });
            }
        });
    }

    @Override
    public void onClick(View v)
    {
        switch (v.getId())
        {
            case R.id.make_card_audio_delete_iv:
                getDeleteDialog();
                break;
            case R.id.memory_vedio_add_save:
                LogUtils.e("log.save");
                uploadResources();
                break;
            default:
                break;
        }
    }

    private void goBack()
    {
        Intent intent = new Intent(this, MemoryAudioActivity.class);
        String res = mEdit.getText().toString();
        intent.putExtra(S.M.M_CONTENT, res);
        startActivity(intent);
        this.finish();
    }

    private void uploadResources()
    {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED)
        {
            ToastUtils.showShort(this, R.string.memory_connect_not);
            return;
        }

        BluetoothManage.getInstance().connectedStatus();
        LogUtils.e("log.uploadResources");
        save.setClickable(false);
        dialog = new LoadingDialog(this, getString(R.string.memory_store_in));
        dialog.show();
        UpdatePictureController.getInstance().uploadMemoryRes(this, MemoryAudioActivity.MAKE_CARD_AUDIO_RE_PATH, 2, "memory");
    }

    @EventInject(eventType = S.E.E_MEMORY_RESOURCE_UPLOAD_SUCCESSED, runThread = TaskType.UI)
    public void onUploadResourceSuccessed(EventData data)
    {
        HttpParams hp = (HttpParams) data;
        String url = (String) hp.getUserDefine("url");
        MemoryBean memoryBean = new MemoryBean();
        memoryBean.memory_type = MemoryBean.TYPE_AUD;
        memoryBean.content = mEdit.getText().toString();
        memoryBean.audio_url = url;
        MemoryController.getInstance().save(memoryBean);
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_SUCCESSED, runThread = TaskType.UI)
    public void onSaveSuccessed(EventData data)
    {
        if (dialog != null)
            dialog.dismiss();

        HttpValues hv = (HttpValues) data;
        MemoryBean mb = (MemoryBean) hv.getUserDefine(S.M.M_IMAGES);
        showSuccessAnim(this, mb);
    }

    private void showSuccessAnim(final Activity activity, final MemoryBean mb)
    {
        saveBg.setVisibility(View.VISIBLE);
        saveGif.setVisibility(View.VISIBLE);
        /*AnimationDrawable ad = ((AnimationDrawable)saveGif.getDrawable());
        ad.setOneShot(true);
        ad.start();*/
        new SceneAnimation(saveGif, MemoryListActivity.successGif, 20, true);
        BluetoothManage.getInstance().notifyJewelry(6, 0x0000ff);
        mHandler.postDelayed(new Runnable()
        {
            @Override
            public void run()
            {
                Intent intent = new Intent(MemoryAudioActivity2.this, MemoryPageActivity.class);
                intent.putExtra(MemoryPageActivity.IS_OPENED, true);
                MemoryAudioActivity2.this.startActivity(intent);
                /*intent = new Intent(MemoryAudioActivity2.this, MemoryAudioShowActivity.class);
                intent.putExtra(S.M.M_IMAGES, mb);
                MemoryAudioActivity2.this.startActivity(intent);*/
                activity.finish();
            }
        }, 80*40);
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_FAILED, runThread = TaskType.UI)
    public void onSaveFailed(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (StringUtils.isEmpty(hv.errorMesg))
            hv.errorMesg = getString(R.string.error_net);
        ToastUtils.showShort(this, hv.errorMesg);
        save.setClickable(true);
        if (dialog != null)
            dialog.dismiss();
    }

    @EventInject(eventType = S.E.E_MEMORY_RESOURCE_UPLOAD_FAILED, runThread = TaskType.UI)
    public void onUploadResourceFailed(EventData data)
    {
        ToastUtils.showShort(this, R.string.error_net);
        save.setClickable(true);
        if (dialog != null)
            dialog.dismiss();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        if (dialog != null)
            dialog.dismiss();
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
        try {
            if (mPlayer != null) {
                mPlayer.release();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    SceneAnimation sa;

    @Override
    protected void onPause()
    {
        super.onPause();

        try
        {
            if (mPlayer != null && mPlayer.isPlaying())
            {
                mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                mPlayer.pause();
                //((AnimationDrawable) audioGif.getDrawable()).stop();
                sa.stop();
            }
        }
        catch (IllegalStateException e)
        {
        }
        catch (Exception e)
        {
        }
    }

    public void getDeleteDialog()
    {
        dialog1 = new CustomDialog(this);
        dialog1.setTitle("");
        dialog1.setMessage(R.string.memory_delete);
        dialog1.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                goBack();
            }
        });
        dialog1.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                dialog1.dismiss();
            }
        });
        dialog1.show();
    }
}
