package com.totwoo.totwoo.activity.memory;

import android.app.Activity;
import android.app.Instrumentation;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.hardware.Camera;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.TextAppearanceSpan;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.component.bitmap.BitmapUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.PageWidget;
import com.totwoo.totwoo.widget.SceneAnimation;
import com.totwoo.totwoo.widget.pullToRefresh.PullToRefreshBase;
import com.totwoo.totwoo.widget.pullToRefresh.PullToRefreshListView;
import com.totwoo.totwoo.widget.scaleVideoView.ScalableVideoView;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.atomic.AtomicBoolean;

import butterknife.BindView;
import butterknife.ButterKnife;
import sz.itguy.wxlikevideo.camera.CameraHelper;

/**
 * 时光记忆 列表模式 页面, 包含顶部的时光记忆制作条
 */

public class MemoryListActivity extends BaseActivity implements SubscriberListener, PullToRefreshBase.OnRefreshListener, View.OnClickListener, AdapterView.OnItemClickListener {
    /*记忆存储成功后的播放动画*/
    public static final int[] successGif = new int[]{
            R.drawable.zpfw_00010, R.drawable.zpfw_00011, R.drawable.zpfw_00012, R.drawable.zpfw_00013, R.drawable.zpfw_00014,
            R.drawable.zpfw_00015, R.drawable.zpfw_00016, R.drawable.zpfw_00017, R.drawable.zpfw_00018, R.drawable.zpfw_00019,
            R.drawable.zpfw_00020, R.drawable.zpfw_00021, R.drawable.zpfw_00022, R.drawable.zpfw_00023, R.drawable.zpfw_00024,
            R.drawable.zpfw_00025, R.drawable.zpfw_00026, R.drawable.zpfw_00027, R.drawable.zpfw_00028, R.drawable.zpfw_00029,
            R.drawable.zpfw_00030, R.drawable.zpfw_00031, R.drawable.zpfw_00032, R.drawable.zpfw_00033, R.drawable.zpfw_00034,
            R.drawable.zpfw_00035, R.drawable.zpfw_00036, R.drawable.zpfw_00037, R.drawable.zpfw_00038, R.drawable.zpfw_00039,
            R.drawable.zpfw_00040, R.drawable.zpfw_00041, R.drawable.zpfw_00042, R.drawable.zpfw_00043, R.drawable.zpfw_00044,
            R.drawable.zpfw_00045, R.drawable.zpfw_00046, R.drawable.zpfw_00047, R.drawable.zpfw_00048, R.drawable.zpfw_00049,
            R.drawable.zpfw_00050, R.drawable.zpfw_00051, R.drawable.zpfw_00052, R.drawable.zpfw_00053, R.drawable.zpfw_00054,
            R.drawable.zpfw_00055, R.drawable.zpfw_00056, R.drawable.zpfw_00057, R.drawable.zpfw_00058, R.drawable.zpfw_00059,
            R.drawable.zpfw_00060, R.drawable.zpfw_00061, R.drawable.zpfw_00062, R.drawable.zpfw_00063, R.drawable.zpfw_00064,
            R.drawable.zpfw_00065, R.drawable.zpfw_00066, R.drawable.zpfw_00067, R.drawable.zpfw_00068, R.drawable.zpfw_00069,
            R.drawable.zpfw_00070, R.drawable.zpfw_00071, R.drawable.zpfw_00072, R.drawable.zpfw_00073, R.drawable.zpfw_00074,
            R.drawable.zpfw_00075, R.drawable.zpfw_00076, R.drawable.zpfw_00077, R.drawable.zpfw_00078, R.drawable.zpfw_00079,
            R.drawable.zpfw_00080, R.drawable.zpfw_00081, R.drawable.zpfw_00082, R.drawable.zpfw_00083, R.drawable.zpfw_00084,
            R.drawable.zpfw_00085, R.drawable.zpfw_00086, R.drawable.zpfw_00087, R.drawable.zpfw_00088, R.drawable.zpfw_00089,
    };

    @BindView(R.id.activity_memory_list_enter_layout)
    public View enterLayout;

    @BindView(R.id.memory_enter_close)
    public TextView close;

    @BindView(R.id.memory_enter_logo)
    public ImageView logo;

    @BindView(R.id.memory_enter_tap)
    public ImageView tap;

    @BindView(R.id.memory_enter_diary)
    public ImageView diary;

    @BindView(R.id.page_view)
    public PageWidget pageWidget;

    private ShakeMonitor mShakeMonitor;

    /********************************************************************************************/

    @BindView(R.id.memory_list_list)
    public PullToRefreshListView listView;

    @BindView(R.id.memory_btn_bg)
    public TextView btnBg;

    @BindView(R.id.memory_btn_layout)
    public LinearLayout btnLayout;

    @BindView(R.id.memory_btn_close)
    public ImageView btnClose;

    @BindView(R.id.memory_btn_say)
    public LinearLayout btnSay;

    @BindView(R.id.memory_btn_photo)
    public LinearLayout btnPhoto;

    @BindView(R.id.memory_btn_voice)
    public LinearLayout btnVoice;

    @BindView(R.id.memory_btn_vedio)
    public LinearLayout btnVedio;

    @BindView(R.id.memory_list_empty)
    public LinearLayout emptyLayout;

    @BindView(R.id.activity_memory_list_layout)
    public View layout;

    @BindView(R.id.activity_memory_list_empty_diary)
    public LinearLayout emptyDiary;

    @BindView(R.id.activity_memory_list_empty_photo)
    public LinearLayout emptyPhoto;

    @BindView(R.id.activity_memory_list_empty_voice)
    public LinearLayout emptyVoice;

    @BindView(R.id.activity_memory_list_empty_vedio)
    public LinearLayout emptyVedio;

    @BindView(R.id.activity_memory_list_empty_iv)
    public ImageView emptyIv;

    @BindView(R.id.layout_memory_list_enter)
    public View layoutEnter;

    @BindView(R.id.layout_memory_list_enter_diary)
    public View layoutDiary;

    @BindView(R.id.layout_memory_list_enter_picture)
    public View layoutPicture;

    @BindView(R.id.layout_memory_list_enter_voice)
    public View layoutVoice;

    @BindView(R.id.layout_memory_list_enter_vedio)
    public View layoutVedio;

    private MemoryAdapter adapter;
    private int page = 0;
    private int totalCount = 0;

    private AtomicBoolean inited = new AtomicBoolean(false);


    private boolean started = false;


    private void initViews() {
        if (Apputils.systemLanguageIsChinese(this)) {
            logo.setImageResource(R.drawable.memory_enter_logo_ch);
            tap.setImageResource(R.drawable.memory_enter_tap_ch);
            diary.setImageResource(R.drawable.memory_enter_diary_ch);
        } else {
            logo.setImageResource(R.drawable.memory_enter_logo_en);
            tap.setImageResource(R.drawable.memory_enter_tap_en);
            diary.setImageResource(R.drawable.memory_enter_diary_en);
        }
    }

    private void initAnims() {
        final Animation a1 = AnimationUtils.loadAnimation(this, R.anim.alaph_to20);
        final Animation a2 = AnimationUtils.loadAnimation(this, R.anim.alaph_to100);
        a1.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                tap.startAnimation(a2);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        a2.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                tap.startAnimation(a1);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        tap.startAnimation(a1);
    }

    private void initShakeListener() {
        mShakeMonitor = new ShakeMonitor(this);
        mShakeMonitor.isEnablePhoneShake(false);

        // 设置摇首饰的监听
        mShakeMonitor.setOnEventListener(type -> {
            if (!NetUtils.isConnected(MemoryListActivity.this)) {
                ToastUtils.showShort(MemoryListActivity.this, R.string.error_net);
                return;
            }
            final int width = Apputils.getScreenWidth(MemoryListActivity.this);
            final int height = Apputils.getScreenHeight(MemoryListActivity.this);
            final Bitmap bitmap = takeScreenShot(MemoryListActivity.this);
            final Bitmap bitmapBack = takeViewShot(MemoryListActivity.this, layout);
            pageWidget.setVisibility(View.VISIBLE);
            pageWidget.setScreen(width, height);

            final Bitmap mCurPageBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            final Bitmap mNextPageBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            final Canvas mCurPageCanvas = new Canvas(mCurPageBitmap);
            final Canvas mNextPageCanvas = new Canvas(mNextPageBitmap);
            final Rect c = new Rect(0, 0, width, height);
            mCurPageCanvas.drawBitmap(bitmap, c, c, null);
            pageWidget.setBitmaps(mCurPageBitmap, mCurPageBitmap);
            pageWidget.setOnTouchListener((v, e) -> {
                boolean ret;
                if (v == pageWidget) {
                    if (e.getAction() == MotionEvent.ACTION_DOWN) {
                        pageWidget.abortAnimation();
                        pageWidget.calcCornerXY(e.getX(), e.getY());

                        mCurPageCanvas.drawBitmap(bitmap, c, c, null);
                        if (pageWidget.DragToRight()) {
                            mNextPageCanvas.drawBitmap(bitmapBack, c, c, null);
                        } else {
                            mNextPageCanvas.drawBitmap(bitmapBack, c, c, null);
                        }
                        pageWidget.setBitmaps(mCurPageBitmap, mNextPageBitmap);
                    }

                    ret = pageWidget.doTouchEvent(e);
                    return ret;
                }
                return false;
            });

            mHandler.postDelayed(() -> new Thread(() -> {
                Instrumentation inst = new Instrumentation();
                long dowTime = SystemClock.uptimeMillis();
                inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime, MotionEvent.ACTION_DOWN, width - 300, height - 400, 0));
                inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime, MotionEvent.ACTION_MOVE, width - 310, height - 410, 0));
                for (int i = 0; i < 5; i++) {
                    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + i * 10, MotionEvent.ACTION_MOVE, width - 310, height - 410, 0));
                }
                inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + 40, MotionEvent.ACTION_UP, width - 310, height - 410, 0));
                mHandler.postDelayed(() -> pageWidget.setEnabled(false), 50);
                mShakeMonitor.stop();
                mShakeMonitor = null;
                mHandler.postDelayed(() -> {
                    enterLayout.setVisibility(View.GONE);
                    LogUtils.e("adapter.count:" + adapter.getCount());
                    if (adapter != null && adapter.getCount() > 0) {
                        //onItemClick(null, null, 1, 0);
                    }
                }, 2500);
            }).start(), 100);
        });
    }

    private static Bitmap takeViewShot(Activity activity, View view) {
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();
        int width = activity.getWindowManager().getDefaultDisplay().getWidth();
        int height = activity.getWindowManager().getDefaultDisplay().getHeight();

        Bitmap bitmap2 = Bitmap.createBitmap(bitmap, 0, 0, width, height);
        view.destroyDrawingCache();
        return bitmap2;
    }

    private static Bitmap takeScreenShot(Activity activity) {
        View view = activity.getWindow().getDecorView();
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();

        int width = activity.getWindowManager().getDefaultDisplay().getWidth();
        int height = activity.getWindowManager().getDefaultDisplay().getHeight();

        Bitmap bitmap2 = Bitmap.createBitmap(bitmap, 0, 0/*statusBarHeight*/, width, height /*- statusBarHeight*/);
        view.destroyDrawingCache();
        return bitmap2;
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mShakeMonitor != null)
            mShakeMonitor.stop();
        try {
            if (mPlayer != null && mPlayer.isPlaying()) {
                mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                mPlayer.pause();
                sa.stop();
            }

            if (mScalableVideoView != null && mScalableVideoView.isPlaying()) {
                mVideoPlayBtn.setVisibility(View.VISIBLE);
                mScalableVideoView.pause();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_list);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        Intent i = getIntent();
        MemoryController.getInstance().getList(0);
        initListView();
        initListener();

        CommonUtils.setStateBar(this, false);

        initViews();
        if (i.getBooleanExtra(MemoryPageActivity.IS_OPENED, false)) {
            enterLayout.setVisibility(View.GONE);
        } else {
            initShakeListener();
            initAnims();
        }

        BluetoothManage.getInstance().connectedStatus();

        close.setOnClickListener(this);
    }

    private void initListView() {
        listView.setMode(PullToRefreshBase.Mode.PULL_FROM_END);
        listView.getLoadingLayoutProxy(false, true).setPullLabel(getString(R.string.memory_list_load_more));
        listView.getLoadingLayoutProxy(false, true).setRefreshingLabel(getString(R.string.memory_list_loading));
        listView.getLoadingLayoutProxy(false, true).setReleaseLabel(getString(R.string.memory_list_load_more));
        listView.setOnRefreshListener(this);
        listView.setOnItemClickListener(this);
    }

    private void initListener() {
        btnBg.setOnClickListener(this);
        btnClose.setOnClickListener(this);
        btnSay.setOnClickListener(this);
        btnPhoto.setOnClickListener(this);
        btnVoice.setOnClickListener(this);
        btnVedio.setOnClickListener(this);

        emptyDiary.setOnClickListener(this);
        emptyPhoto.setOnClickListener(this);
        emptyVoice.setOnClickListener(this);
        emptyVedio.setOnClickListener(this);

        layoutDiary.setOnClickListener(this);
        layoutPicture.setOnClickListener(this);
        layoutVoice.setOnClickListener(this);
        layoutVedio.setOnClickListener(this);
    }

    @Override
    protected void initTopBar() {
        setTopbarBackground(R.color.layer_bg_white);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopTitle(getString(R.string.memory_list_title1));
        setTopRightGone();
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MemoryListActivity.this.finish();
            }
        });
        ImageView iv = getTopRightIcon();
        iv.setImageResource(R.drawable.memory_list_flip);
        iv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HttpValues hv = new HttpValues("", "");
                hv.putUserDefine("list", adapter.resource);
                hv.putUserDefine("count", totalCount);
                hv.putUserDefine("page", 0);
                hv.putUserDefine("pos", 0);
                EventBus.onPostReceived(S.E.E_MEMORY_LIST_SWITCH, hv);
                MemoryListActivity.this.finish();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (inited.compareAndSet(true, true)) {
            page = 0;
            MemoryController.getInstance().getList(0);
        }

        if (mShakeMonitor != null)
            mShakeMonitor.start();

        if (Apputils.systemLanguageIsChinese(this)) {
            emptyIv.setImageResource(R.drawable.memory_list_paper_ch);
        } else {
            emptyIv.setImageResource(R.drawable.memory_list_paper_en);
        }
    }

    @EventInject(eventType = S.E.E_MEMORY_GET_LIST_SUCCESSED, runThread = TaskType.UI)
    public void onGetMemoryListSuccessed(EventData data) {
        HttpValues hv = (HttpValues) data;
        ArrayList<MemoryBean> list = (ArrayList<MemoryBean>) hv.getUserDefine("MemoryBeanList");
        totalCount = (int) hv.getUserDefine("count");
        if (adapter == null || page == 0) {
            adapter = new MemoryAdapter(this, list);
            listView.setAdapter(adapter);
            if (page == 0 && (list == null || list.size() == 0)) {
                emptyLayout.setVisibility(View.VISIBLE);
                listView.setVisibility(View.GONE);
                layoutEnter.setVisibility(View.GONE);
                if (Apputils.systemLanguageIsChinese(this))
                    emptyIv.setImageResource(R.drawable.memory_list_paper_ch);
                else
                    emptyIv.setImageResource(R.drawable.memory_list_paper_en);
            } else {
                emptyLayout.setVisibility(View.GONE);
                listView.setVisibility(View.VISIBLE);
                layoutEnter.setVisibility(View.VISIBLE);
            }
        } else {
            adapter.addData(list);
            adapter.notifyDataSetChanged();
        }

        page++;
        listView.onRefreshComplete();
        inited.set(true);

        if (!started) {
            started = true;
            //initSay(list.get(0));
            /*if (adapter != null && adapter.getCount()>0)
            {
                onItemClick(null, null, 1, 0);
            }*/
        }
    }

    @EventInject(eventType = S.E.E_MEMORY_GET_LIST_FAILED, runThread = TaskType.UI)
    public void onGetMemoryListFailed(EventData data) {
        listView.onRefreshComplete();

        ToastUtils.showShort(this, R.string.error_net);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {
    }

    @Override
    public void onRefresh(PullToRefreshBase refreshView) {
        if (adapter == null)
            return;
        if (adapter.getCount() >= totalCount) {
            ToastUtils.showShort(this, R.string.memory_list_no);
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    listView.onRefreshComplete();
                }
            }, 500);
            return;
        }

        MemoryController.getInstance().getList(page);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.memory_btn_bg:
            case R.id.memory_btn_close:
                onCloseClicked();
                break;
            case R.id.memory_btn_say:
            case R.id.activity_memory_list_empty_diary:
            case R.id.layout_memory_list_enter_diary:
                onSayClicked();
                break;
            case R.id.memory_btn_photo:
            case R.id.activity_memory_list_empty_photo:
            case R.id.layout_memory_list_enter_picture:
                onPhotoClicked();
                break;
            case R.id.memory_btn_voice:
            case R.id.activity_memory_list_empty_voice:
            case R.id.layout_memory_list_enter_voice:
                onVoiceClicked();
                break;
            case R.id.memory_btn_vedio:
            case R.id.activity_memory_list_empty_vedio:
            case R.id.layout_memory_list_enter_vedio:
                onVedioClicked();
                break;
            case R.id.memory_enter_close:
                this.finish();
                break;
        }
    }

    private void onAddClicked() {
        btnBg.setVisibility(View.VISIBLE);
        btnLayout.setVisibility(View.VISIBLE);
    }

    private void onCloseClicked() {
        btnBg.setVisibility(View.GONE);
        btnLayout.setVisibility(View.GONE);
    }

    private void onSayClicked() {
        startActivity(new Intent(this, MemorySayActivity.class));
        onCloseClicked();
    }

    private void onPhotoClicked() {
        startActivity(new Intent(this, MemoryPhotoSelectActivity.class));
        onCloseClicked();
    }

    private void onVoiceClicked() {
        if (!NetUtils.checkPermission(this, "android.permission.RECORD_AUDIO")) {
            final CustomDialog dialog = new CustomDialog(this);
            dialog.setMessage(R.string.open_audio_error);
            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    dialog.dismiss();
                }
            });
            dialog.show();
            return;
        }
        startActivity(new Intent(this, MemoryAudioActivity.class));
        onCloseClicked();
    }

    private void onVedioClicked() {
        int cameraId = CameraHelper.getDefaultCameraID();
        Camera mCamera = CameraHelper.getCameraInstance(cameraId);
        if (mCamera == null) {
            final CustomDialog dialog = new CustomDialog(this);
            dialog.setMessage(R.string.open_camera_error1);
            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    dialog.dismiss();
                }
            });
            dialog.show();
            return;
        } else {
            mCamera.release();
        }

        startActivity(new Intent(this, MemoryVedioActivity.class));
        onCloseClicked();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
        try {
            if (mPlayer != null) {
                mPlayer.release();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        HttpValues hv = new HttpValues("", "");
        hv.putUserDefine("list", adapter.resource);
        hv.putUserDefine("count", totalCount);
        hv.putUserDefine("page", page);
        hv.putUserDefine("pos", position - 1);
        EventBus.onPostReceived(S.E.E_MEMORY_LIST_SWITCH, hv);
        this.finish();

        /*MemoryBean mb = adapter.getItem(position-1);
        switch (mb.memory_type)
        {
            case MemoryBean.TYPE_SAY:
                //startSayShowActivity(mb);
                initSay(mb);
                break;
            case MemoryBean.TYPE_IMG:
                //startImageShowActivity(mb);
                initPhotoView(false, mb);
                break;
            case MemoryBean.TYPE_AUD:
                //startAudioShowActivity(mb);
                initAudio(mb);
                break;
            case MemoryBean.TYPE_VED:
                //startVedioShowActivity(mb);
                initVedio(mb);
                break;
            default:
                break;
        }*/
    }

    private void startImageShowActivity(MemoryBean mb) {
        Intent intent = new Intent(this, MemoryPhotoShowActivity.class);
        intent.putExtra(S.M.M_SINGLE, false);
        intent.putExtra(S.M.M_IMAGES, mb);
        startActivity(intent);
    }

    private void startVedioShowActivity(MemoryBean mb) {
        Intent intent = new Intent(this, MemoryVedioShowActivity.class);
        intent.putExtra(S.M.M_IMAGES, mb);
        startActivity(intent);
    }

    private void startAudioShowActivity(MemoryBean mb) {
        Intent intent = new Intent(this, MemoryAudioShowActivity.class);
        intent.putExtra(S.M.M_IMAGES, mb);
        startActivity(intent);
    }

    private void startSayShowActivity(MemoryBean mb) {
        Intent intent = new Intent(this, MemorySayShowActivity.class);
        intent.putExtra(S.M.M_IMAGES, mb);
        startActivity(intent);
    }

    /*************************************说说****************************************************/
    @BindView(R.id.memory_say_tx)
    TextView Saytv;

    @BindView(R.id.list_say_layout)
    View sayLayout;

    private void initSay(MemoryBean mb) {
        sayLayout.setVisibility(View.VISIBLE);
        initSayTitle(mb);
        initContent(mb);
    }

    protected void initSayTitle(final MemoryBean mb) {
        setTopBackIcon(R.drawable.back_icon_black);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopRightIcon(R.drawable.delete);
        setTopRightOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDeleteDialog(mb);
            }
        });

        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initListTitle();
            }
        });
    }

    private void initContent(MemoryBean mb) {
        SpannableString styledText = new SpannableString(mb.content);
        styledText.setSpan(new TextAppearanceSpan(this, R.style.memory_say_text_style_big), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        styledText.setSpan(new TextAppearanceSpan(this, R.style.memory_say_text_style_small), 1, mb.content.length() - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        Saytv.setText(styledText, TextView.BufferType.SPANNABLE);
    }


    /****************************************语音********************************************/
    @BindView(R.id.list_audio_layout)
    View audioView;

    @BindView(R.id.make_card_audio_play_btn)
    ImageView mAudioVideoPlayBtn;

    @BindView(R.id.memory_photo_show_tv)
    public TextView audioTv;

    @BindView(R.id.audio_layout)
    public FrameLayout audioLayout;

    @BindView(R.id.audio_gif)
    public ImageView audioGif;

    private MediaPlayer mPlayer;

    private void initAudio(final MemoryBean mb) {
        audioView.setVisibility(View.VISIBLE);
        initAudioTitle(mb);
        setAudioContent(mb);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                initAudioView(mb);
            }
        }, 1000);
        mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
    }

    private void initAudioTitle(final MemoryBean mb) {
        setTopBackIcon(R.drawable.back_icon_white);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_white_important));
        setTopRightIcon(R.drawable.memory_photo_show_delete);
        setTopRightOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDeleteDialog(mb);
            }
        });
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPlayer != null && mPlayer.isPlaying()) {
                    mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                    mPlayer.pause();
                    sa.stop();
                }
                initListTitle();
            }
        });
    }

    private void initAudioListener() {
        audioLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPlayer.isPlaying()) {
                    mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                    mPlayer.pause();
                    sa.stop();
                }
            }
        });
    }

    private void setAudioContent(MemoryBean mb) {
        audioTv.setVisibility(View.VISIBLE);
        audioTv.setText(mb.content);
    }

    SceneAnimation sa;

    private void initAudioView(MemoryBean mb) {
        try {
            LogUtils.e("audio_url:" + mb.audio_url);
            LogUtils.e("uri:" + Uri.parse(mb.audio_url));
            sa = new SceneAnimation(audioGif, MemoryAudioActivity.gifs, 10, false);
            mPlayer = new MediaPlayer();
            mPlayer.setDataSource(this, Uri.parse(mb.audio_url));
            mPlayer.prepare();
            initAudioListener();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mAudioVideoPlayBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPlayer.start();
                mAudioVideoPlayBtn.setVisibility(View.GONE);
                sa.start();
                mPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                        sa.stop();
                    }
                });
            }
        });
    }


    /****************************************视频*******************************************/
    @BindView(R.id.list_vedio_layout)
    View vedioView;

    @BindView(R.id.vedio_play_btn)
    ImageView mVideoPlayBtn;

    @BindView(R.id.vedio_video_view)
    ScalableVideoView mScalableVideoView;

    @BindView(R.id.vedio_conver_layer)
    ImageView mMakeCardTopCoverIv;

    @BindView(R.id.vedio_show_tv)
    public TextView vedioTv;

    private boolean isShowVedioTitle = true;

    private void initVedio(final MemoryBean mb) {
        vedioView.setVisibility(View.VISIBLE);
        isShowVedioTitle = false;
        showOrHideTitle();
        initVedioTitle(mb);
        initCover(mb);
        setVedioContent(mb);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        initVedioView(mb);
                    }
                }).start();
            }
        }, 200);
    }

    private void initCover(MemoryBean mb) {
        mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
        BitmapHelper.display(this, mMakeCardTopCoverIv, mb.cover_url);
    }

    private void initVedioView(MemoryBean mb) {
        try {
            mVideoPlayBtn.setVisibility(View.VISIBLE);
            mScalableVideoView.setDataSource(mb.vedio_url);
            mScalableVideoView.prepare();
            mScalableVideoView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mScalableVideoView.isPlaying()) {
                        mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                        mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
                        mScalableVideoView.pause();
                        isShowVedioTitle = false;
                        showOrHideTitle();
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

        mVideoPlayBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mScalableVideoView.start();
                mScalableVideoView.setVisibility(View.VISIBLE);
                mMakeCardTopCoverIv.setVisibility(View.GONE);
                mVideoPlayBtn.setVisibility(View.GONE);
                showOrHideTitle();
                mScalableVideoView.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        mVideoPlayBtn.setVisibility(View.VISIBLE);
                        isShowVedioTitle = false;
                        showOrHideTitle();
                    }
                });
            }
        });
    }

    private void initVedioTitle(final MemoryBean mb) {
        setTopBackIcon(R.drawable.back_icon_white);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_white_important));
        setTopRightIcon(R.drawable.memory_photo_show_delete);
        setTopRightOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDeleteDialog(mb);
            }
        });
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initListTitle();
            }
        });
    }

    private void setVedioContent(MemoryBean mb) {
        vedioTv.setVisibility(isShowVedioTitle ? View.VISIBLE : View.GONE);
        vedioTv.setText(mb.content);
    }

    private void showOrHideTitle() {
        isShowVedioTitle = !isShowVedioTitle;
        getTopBar().setVisibility(isShowVedioTitle ? View.VISIBLE : View.GONE);
        vedioTv.setVisibility(isShowVedioTitle ? View.VISIBLE : View.GONE);
    }

    /****************************************照片*********************************************/
    @BindView(R.id.list_photo_layout)
    View photoView;

    @BindView(R.id.memory_photo_show_vp)
    public ViewPager vp;

    @BindView(R.id.memory_photo1_show_tv)
    public TextView photoTv;

    private ImageAdapter imageAdapter;
    private boolean isShowPhotoTitle = true;
    private boolean isSingleMode = false;

    private void initPhotoView(boolean isSingleMode, MemoryBean mb) {
        isShowPhotoTitle = true;
        this.isSingleMode = isSingleMode;
        photoView.setVisibility(View.VISIBLE);
        initPhotoTitle(mb);
        initViewPager(mb);
        setPhotoContent(0, mb);
    }

    private void setPhotoContent(int position, MemoryBean mb) {
        if (isSingleMode) {
            photoTv.setVisibility(View.GONE);
            return;
        }

        photoTv.setVisibility(isShowPhotoTitle ? View.VISIBLE : View.GONE);
        String src = (position + 1) + "/" + mb.img_url.length + "  " + mb.content;
        photoTv.setText(src);
    }

    private void initPhotoTitle(final MemoryBean mb) {
        if (isSingleMode)
            return;

        setTopBackIcon(R.drawable.back_icon_white);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_white_important));
        setTopRightIcon(R.drawable.memory_photo_show_delete);
        setTopRightOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDeleteDialog(mb);
            }
        });
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initListTitle();
            }
        });
    }

    private void initViewPager(MemoryBean mb) {
        vp.addOnPageChangeListener(new MyOnPageChangedListener(mb));
        ImageView[] ivs = new ImageView[mb.img_url.length];
        for (int i = 0; i < ivs.length; i++) {
            ivs[i] = new ImageView(this);
            ivs[i].setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
            ivs[i].setLayoutParams(lp);
            ivs[i].setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (isSingleMode)
                        MemoryListActivity.this.finish();
                    else
                        showOrHidePhotoTitle();
                }
            });
        }

        imageAdapter = new ImageAdapter(ivs, mb);
        vp.setAdapter(imageAdapter);
    }

    private void showOrHidePhotoTitle() {
        isShowPhotoTitle = !isShowPhotoTitle;
        getTopBar().setVisibility(isShowPhotoTitle ? View.VISIBLE : View.GONE);
        photoTv.setVisibility(isShowPhotoTitle ? View.VISIBLE : View.GONE);
    }

    private class ImageAdapter extends PagerAdapter {
        private ImageView[] viewlist;
        BitmapUtils bu;
        MemoryBean mb;

        public ImageAdapter(ImageView[] viewlist, MemoryBean mb) {
            this.viewlist = viewlist;
            bu = new BitmapUtils(MemoryListActivity.this);
            bu.configDefaultLoadingImage(R.mipmap.ic_launcher);
            this.mb = mb;
        }

        @Override
        public int getCount() {
            return viewlist.length;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View v = viewlist[position];
            ((ViewPager) container).addView(v);
            String url = mb.img_url[position];
            bu.display(viewlist[position], url);

            return v;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            ((ViewPager) container).removeView(viewlist[position]);
        }
    }

    private class MyOnPageChangedListener implements ViewPager.OnPageChangeListener {
        private MemoryBean mb;

        public MyOnPageChangedListener(MemoryBean mb) {
            this.mb = mb;
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageSelected(int position) {
            setPhotoContent(position, mb);
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    }


    private void initListTitle() {
        sayLayout.setVisibility(View.GONE);
        audioView.setVisibility(View.GONE);
        vedioView.setVisibility(View.GONE);
        photoView.setVisibility(View.GONE);
        initTopBar();
        if (inited.compareAndSet(true, true)) {
            page = 0;
            MemoryController.getInstance().getList(0);
        }
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void onDeleteMemorySuccessed(EventData data) {
        ToastUtils.showShort(this, R.string.memory_delete_success);
        initListTitle();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_FAILED, runThread = TaskType.UI)
    public void onDeleteMemoryFailed(EventData data) {
        HttpValues hv = (HttpValues) data;
        String msg = hv.errorMesg;
        ToastUtils.showShort(this, msg);
    }

    public void getDeleteDialog(final MemoryBean mb) {
        final CustomDialog dialogx = new CustomDialog(this);
        dialogx.setTitle("");
        dialogx.setMessage(R.string.memory_delete);
        dialogx.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MemoryController.getInstance().delete(mb);
                dialogx.dismiss();
            }
        });
        dialogx.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialogx.dismiss();
            }
        });
        dialogx.show();
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_SUCCESSED, runThread = TaskType.UI)
    public void onSaveSuccessed(EventData data) {
        this.finish();
    }
}