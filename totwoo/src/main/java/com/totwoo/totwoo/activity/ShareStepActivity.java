package com.totwoo.totwoo.activity;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.Step;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DbHelper;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.totwoo.totwoo.widget.RoundImageView;
import com.umeng.analytics.MobclickAgent;

import java.math.BigDecimal;
import java.text.DecimalFormat;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 健步生活分享界面
 * <p/>
 * Created by lixingmao on 16/1/19.
 */
public class ShareStepActivity extends BaseActivity implements SubscriberListener {

    @BindView(R.id.share_step_head_icon)
    RoundImageView headImg;

    @BindView(R.id.share_step_nick_name_tv)
    TextView nickNameTv;

    @BindView(R.id.share_step_title_tv)
    TextView titleTv;

    @BindView(R.id.share_step_step_value_tv)
    TextView stepTv;

    @BindView(R.id.share_step_distance_value_tv)
    TextView walkDisTv;

    @BindView(R.id.share_step_calorie_value_tv)
    TextView calorieTv;

    @BindView(R.id.share_step_bottom_image)
    ImageView bottomImg;

    /**
     * 文案提示
     */
    @BindView(R.id.share_step_step_info_tv)
    TextView step_situation_info_tv;

    /**
     * 健步数据状态小人图片
     */
    @BindView(R.id.share_step_situation_img)
    ImageView step_doll_iv;

    @BindView(R.id.share_step_distance_key_tv)
    TextView mDistanceTv;

    @BindView(R.id.share_step_step_key_tv)
    TextView mStepTv;

    private FacebookCallback<Sharer.Result> facebookCallback;

    private NewUserGiftDialog newUserGiftDialog;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_share_step);
        ButterKnife.bind(this);

        InjectUtils.injectOnlyEvent(this);

        // 健步
        int steps = 0;
        try {
            Step step = DbHelper.getDbUtils().findById(Step.class,
                    Apputils.getZeroCalendar(null).getTimeInMillis());
            if (step != null) {
                steps = step.getSteps();
            }
        } catch (DbException e) {
            e.printStackTrace();
        }

        steps = getIntent().getIntExtra("step", 0);
        showDate(steps);

        facebookCallback = new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                ToastUtils.showShort(ShareStepActivity.this,getResources().getString(R.string.share_complete));
            }

            @Override
            public void onCancel() {
                ToastUtils.showShort(ShareStepActivity.this,getResources().getString(R.string.share_cancel));
            }

            @Override
            public void onError(FacebookException error) {
                ToastUtils.showShort(ShareStepActivity.this,getResources().getString(R.string.share_error));
            }
        };

        findViewById(R.id.common_share_layout_new).setBackgroundResource(R.color.white);

        initShare();

    }

    private void initShare(){
        facebookCallback = new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                ToastUtils.showShort(ShareStepActivity.this,getResources().getString(R.string.share_complete));
            }

            @Override
            public void onCancel() {
                ToastUtils.showShort(ShareStepActivity.this,getResources().getString(R.string.share_cancel));
            }

            @Override
            public void onError(FacebookException error) {
                ToastUtils.showShort(ShareStepActivity.this,getResources().getString(R.string.share_error));
            }
        };

        if(Apputils.systemLanguageIsChinese(ShareStepActivity.this)){
            findViewById(R.id.common_share_title_tv).setVisibility(View.VISIBLE);
            ((TextView)findViewById(R.id.common_share_title_tv)).setText(CommonUtils.setNumberGoldenSpan(getResources().getString(R.string.share_text_head_info),88,16));

        }else{
            findViewById(R.id.common_share_title_tv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_facebook_iv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_twitter_iv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_qq_iv).setVisibility(View.GONE);
            findViewById(R.id.common_share_qzone_iv).setVisibility(View.GONE);
            findViewById(R.id.common_share_weibo_iv).setVisibility(View.GONE);
        }

        newUserGiftDialog = new NewUserGiftDialog(ShareStepActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(getIntent().getIntExtra(CommonArgs.FROM_TYPE,1) == 1){
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_STEP_LUCKY_CLICK);
                }else{
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_STEP_LUCKY_CLICK);
                }
                WebViewActivity.loadUrl(ShareStepActivity.this, HttpHelper.URL_GIFT, false);
                newUserGiftDialog.dismiss();
            }
        }, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                newUserGiftDialog.dismiss();
            }
        },CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券",88,20),"立即抽奖");

        findViewById(R.id.common_share_friend_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath());
            }
        });
        findViewById(R.id.common_share_wechat_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWechat(getPath());
            }
        });

        findViewById(R.id.common_share_qq_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToQQ(getPath());
            }
        });
        findViewById(R.id.common_share_qzone_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToQzone(getPath(),getString(R.string.share_step_title_info).replace(":", ""));
            }
        });

        findViewById(R.id.common_share_weibo_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWeibo(ShareStepActivity.this,getPath(),getString(R.string.share_step_title_info).replace(":", ""));
            }
        });
        findViewById(R.id.common_share_facebook_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToFacebook(getPath(),ShareStepActivity.this,facebookCallback);
            }
        });
        findViewById(R.id.common_share_twitter_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToTwitter(getPath(),getString(R.string.share_step_title_info).replace(":", ""));
            }
        });
    }

    private String getPath(){
        Bitmap snapShareBitmap = ShareUtilsSingleton.getBitmapByView(findViewById(R.id.share_step_content_layout));
        return FileUtils.saveBitmapFromSDCard(snapShareBitmap,
                "totwoo_cache_img_" + System.currentTimeMillis());
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
    }

    /**
     * 数据展示
     *
     * @param step
     */
    protected void showDate(int step) {
        // 用户信息
        if (!TextUtils.isEmpty(ToTwooApplication.owner.getHeaderUrl())) {
            BitmapHelper.display(this, headImg,
                    ToTwooApplication.owner.getHeaderUrl());
        }
        headImg.setClickable(false);

        nickNameTv.setText(ToTwooApplication.owner.getNickName());
        if (BleParams.isSecurityJewlery()) {
            titleTv.setText(R.string.safe_share_step_title_info);
            mDistanceTv.setText(R.string.safe_walk_distance);
            mStepTv.setText(R.string.safe_walk_step);
        }else{
            titleTv.setText(R.string.share_step_title_info);
            mDistanceTv.setText(R.string.walk_distance);
            mStepTv.setText(R.string.walk_step);
        }

        // 健步数据
        SpannableString stepss = new SpannableString(step
                + getString(R.string.step));
        stepss.setSpan(new AbsoluteSizeSpan(16, true), 0, stepss.length()
                        - getString(R.string.step).length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        stepTv.setText(stepss);

        // 公里数保留一位小数
        DecimalFormat format = new DecimalFormat("#.#");
        SpannableString walk = new SpannableString(
                format.format(getWalkDistance(step))
                        + getString(R.string.km));
        walk.setSpan(new AbsoluteSizeSpan(16, true), 0, walk.length()
                        - getString(R.string.km).length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        walkDisTv.setText(walk);

        // 四舍五入取整
        SpannableString calorie = new SpannableString(new BigDecimal(
                getCaliore(step)).setScale(0, BigDecimal.ROUND_HALF_UP)
                + getString(R.string.kcal));
        calorie.setSpan(new AbsoluteSizeSpan(16, true), 0, calorie.length()
                        - getString(R.string.kcal).length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        calorieTv.setText(calorie);

        setStepSituation(step);

        // 国际版调整
        if (!Apputils.systemLanguageIsChinese(this)) {
            bottomImg.setImageResource(R.drawable.share_bottom_img_en);
            findViewById(R.id.share_step_step_key_tv).setVisibility(View.INVISIBLE);
            findViewById(R.id.share_step_distance_key_tv).setVisibility(View.INVISIBLE);
            findViewById(R.id.share_step_calorie_key_tv).setVisibility(View.INVISIBLE);
        }
    }

    /**
     * 根据步数, 获得对应的健步里程, 单位(公里)
     *
     * @return
     */
    private float getWalkDistance(int step) {
        return 70.5f * ToTwooApplication.owner.getHeight() / 185 * step
                / 100000;
    }

    /**
     * 根据步数, 获得对应的卡路里消耗, 单位(千卡)
     *
     * @return
     */
    private float getCaliore(int step) {
        return getWalkDistance(step) * 1.036f
                * ToTwooApplication.owner.getWeight();
    }

    private void setStepSituation(int step) {
        int stepTarget = PreferencesUtils.getInt(this, StepTargetSettingActivity.STEP_TARGET, 8000);

        if (step >= stepTarget) {// 完成目标
            if (ToTwooApplication.owner.getGender() == 0) {
                step_doll_iv.setImageResource(R.drawable.share_step_male_2);
            } else {
                step_doll_iv.setImageResource(R.drawable.share_step_female_2);
            }

            step_situation_info_tv.setText(R.string.share_step_great_info);
        } else if (step > 0) {// 完成一部分
            if (ToTwooApplication.owner.getGender() == 0) {
                step_doll_iv.setImageResource(R.drawable.share_step_male_1);
            } else {
                step_doll_iv.setImageResource(R.drawable.share_step_female_1);
            }

            step_situation_info_tv.setText(getString(R.string.share_step_fighting_info, (stepTarget
                    - step)));
        } else {// 还没开始
            if (ToTwooApplication.owner.getGender() == 0) {
                step_doll_iv.setImageResource(R.drawable.share_step_male_0);
            } else {
                step_doll_iv.setImageResource(R.drawable.share_step_female_0);
            }

            step_situation_info_tv.setText(R.string.share_step_no_info);
        }
    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        newUserGiftDialog.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
