package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.content.res.Resources.NotFoundException;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager.widget.ViewPager.OnPageChangeListener;

import com.etone.framework.utils.StringUtils;
import com.google.gson.Gson;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ConstellationComment;
import com.totwoo.totwoo.bean.ConstellationDataModel;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.RoundImageView;
import com.totwoo.totwoo.widget.fallingView.FallObject;
import com.totwoo.totwoo.widget.fallingView.FallingView;
import com.umeng.analytics.MobclickAgent;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;


/**
 * 星座运势界面
 *
 * <AUTHOR>
 * @date 2015-2015年7月10日
 */
public class ConstellationActivity extends BaseActivity implements OnClickListener {
    public static final int TYPE_FORTUNE = 4;

    public static String TAG_CONSTELLATION_DATA = "constellation_data";
    public static String TAG_COMMEND_DATA = "commend_data";
    public static String TAG_CONSTELLATION_DATA_TYPE = "constellation_data_type";
    public static String TAG_CONSTELLATION_NAME = "constellation_name";

    /**
     * 主体 ViewPager
     */
    @BindView(R.id.cons_content_viewpager)
    ViewPager mViewPager;
    /**
     * 顶部 今天的 tab
     */
    @BindView(R.id.cons_date_today)
    TextView dateToday;
    /**
     * 顶部 明天的 tab
     */
    @BindView(R.id.cons_date_nextday)
    TextView dateNextday;
    /**
     * 顶部 一周 tab
     */
    @BindView(R.id.cons_date_week)
    TextView dateWeek;
    /**
     * 顶部 一月的 tab
     */
    @BindView(R.id.cons_date_month)
    TextView dateMonth;
    /**
     * 顶部 底部得游标动画 tab
     */
    @BindView(R.id.cons_date_cursor)
    ImageView dateCursor;

    @BindView(R.id.cons_qian_iv)
    ImageView ivConsQian;

    @BindView(R.id.cons_falling)
    FallingView fallingView;

    /**
     * 最新的星座数据
     */
    private ArrayList<ConstellationDataModel> consData;


    /**
     * 当前用户星座名称
     */
    private String conName;

    /**
     * 当前用户星座对应日期
     */
    private String conDate;
    /**
     * 当前用户星座对应图标
     */
    private int conIcon;

    private PagerAdapter mAdapter;

    /**
     * 星座图标
     */
    private int[] conIconRes = {R.drawable.con_shuiping_icon,
            R.drawable.con_shuangyu_icon, R.drawable.con_baiyang_icon,
            R.drawable.con_jinniu_icon, R.drawable.con_shuangzi_icon,
            R.drawable.con_juxie_icon, R.drawable.con_shizi_icon,
            R.drawable.con_chunv_icon, R.drawable.con_tiancheng_icon,
            R.drawable.con_tianxie_icon, R.drawable.con_sheshou_icon,
            R.drawable.con_moejie_icon
    };
    private boolean isPress;//手指是否触摸屏幕
    private boolean isOpen;//是否打开下一个activity

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_constellation);
        ButterKnife.bind(this);

        // 获取用户 星座信息
        getUserConInfo();

        // 初始化Cursor 移动动画效果
        initCursonanimer();

        if (Apputils.systemLanguageIsChinese(ConstellationActivity.this))
            dateMonth.setText("本月");
        else {
            dateMonth.setText("Yes Or No");
            ivConsQian.setVisibility(View.GONE);
        }

        mViewPager.post(new Runnable() {
            @Override
            public void run() {
                getData();
            }
        });
        mAdapter = new ConstellationPagerAdapter();
        mViewPager.setAdapter(mAdapter);
        intentStatus(getIntent());
    }

    private void intentStatus(Intent intent) {
        String fromType = intent.getStringExtra(CommonArgs.FROM_TYPE);
        if (TextUtils.equals(fromType, "push")) {
            FallObject.Builder builder = new FallObject.Builder(getResources().getDrawable(R.drawable.falling_angel));
            FallObject fallObject = builder
                    .setSpeed(4, true)
                    .setSize(198, 237, false)
                    .setWind(1, true, true)
                    .build();

            fallingView.addFallObject(fallObject, 10);//添加10个下落物体对象
        } else if (TextUtils.equals(fromType, "week")) {
            mViewPager.setCurrentItem(2, true);
        } else if (TextUtils.equals(fromType, "month")) {
            mViewPager.setCurrentItem(3, true);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        intentStatus(intent);
        super.onNewIntent(intent);
    }

    /**
     * 获取用户 星座信息
     */
    private void getUserConInfo() {
        try {

            if (conName == null) {
                return;
            }

            int index = Arrays.asList(
                    getResources().getStringArray(R.array.constellation_names))
                    .indexOf(conName);

            if (index == -1) {
                index = 0;
            }
            conDate = getResources()
                    .getStringArray(R.array.constellation_dates)[index];
            conIcon = conIconRes[index];
        } catch (NumberFormatException e) {
            e.printStackTrace();
        } catch (NotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    boolean isSelf;

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);

        Intent intent = getIntent();
        String title;
        String phone = intent.getStringExtra("phone");
        if (StringUtils.isEmpty(phone)) {
            isSelf = true;
            title = getString(R.string.constellation);
        } else {
            if (!TextUtils.isEmpty(intent.getStringExtra("title"))) {
                title = intent.getStringExtra("title");
            } else {
                title = getString(R.string.constellation1);
            }
            isSelf = false;
        }
        setTopTitle(title);
        //空click。没有加上的时候，点击顶部，会有影响部分控件颜色。
        getTopBar().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });

//        if (Apputils.systemLanguageIsChinese(ConstellationActivity.this)) {
//            setTopRightIcon(R.drawable.icon_share_gift);
//        } else {
//            setTopRightIcon(R.drawable.share_ico_2);
//        }

        setTopRightIcon(R.drawable.luckyday);

        getTopRightIcon().setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivityForResult(new Intent(ConstellationActivity.this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_FORTUNE), TYPE_FORTUNE);
            }
        });

//        setTopRightOnClick(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (!PermissionUtil.hasStoragePermission(ConstellationActivity.this)) {
//                    return;
//                }
//                if (consData == null || consData.size() == 0) {
//                    ToastUtils.showLong(ConstellationActivity.this, R.string.error_no_data);
//                    return;
//                }
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MY_FORTUNE_SHARE);
//                if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
//                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_HEROSCOPE_CLICK);
//                } else {
//                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_HEROSCOPE_CLICK);
//                }
//
//                // 跳转分享页面
//                Intent intent = new Intent(ConstellationActivity.this, ShareConstellationActivity.class);
//                intent.putExtra(TAG_CONSTELLATION_DATA, consData.get(mViewPager.getCurrentItem()));
//                intent.putExtra(TAG_CONSTELLATION_DATA_TYPE, mViewPager.getCurrentItem());
//                intent.putExtra(TAG_CONSTELLATION_NAME, conName);
//                intent.putExtra(CommonArgs.FROM_TYPE, getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1));
//
//                startActivity(intent);
//            }
//        });
        if (!isSelf) {
            getTopRight2Icon().setVisibility(View.GONE);
            getTopRightIcon().setVisibility(View.GONE);
            ivConsQian.setVisibility(View.GONE);
        }
    }

//    int mRes = 0;

    /**
     * 初始化Cursor 移动动画效果
     */
    private void initCursonanimer() {
        dateToday.setOnClickListener(this);
        dateNextday.setOnClickListener(this);
        dateWeek.setOnClickListener(this);
        dateMonth.setOnClickListener(this);
//        dateYear.setOnClickListener(this);

        // 重新定位下动画图标
        if (dateCursor.getLayoutParams() != null) {
            dateCursor.getLayoutParams().width = Apputils
                    .getScreenWidth(ConstellationActivity.this) / 4;
        }

        mViewPager.addOnPageChangeListener(new OnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                if (position == 0 && Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                    ivConsQian.setVisibility(View.VISIBLE);
                } else {
                    ivConsQian.setVisibility(View.GONE);
                }
                // 更新顶部栏的状态
                updateDateBar(position);
                if (position == 1) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FORTUNE_TOMORROW);
                    if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_DAY_HEROSCOPE_CHECK);
                    } else {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_DAY_HEROSCOPE_CHECK);
                    }
                } else if (position == 2) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FORTUNE_WEEK);
                    if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_WEEK_HEROSCOPE_CHECK);
                    } else {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_WEEK_HEROSCOPE_CHECK);
                    }
                } else if (position == 3) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FORTUNE_MONTH);
                    if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_MONTH_HEROSCOPE_CHECK);
                    } else {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_MONTH_HEROSCOPE_CHECK);
                    }
                }

            }

            @Override
            public void onPageScrolled(int position, float positionOffset,
                                       int positionOffsetPixels) {


                float offset = (Apputils.getScreenWidth(ConstellationActivity.this) * position + positionOffsetPixels) / 4f;
                dateCursor.setTranslationX(offset);

                if (!Apputils.systemLanguageIsChinese(ConstellationActivity.this)) {

                    if (mViewPager.getAdapter().getCount() - 1 == position && isPress && positionOffsetPixels == 0 && !isOpen) {
                        isOpen = true;//防止多次添加activity
                        Intent intent = new Intent();
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CONSTELLATION_FATE_CLICK);
                        intent.setClass(ConstellationActivity.this, YesNoActivity.class);
                        startActivity(intent);
                        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_in_left);
                    }


                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                if (state == ViewPager.SCROLL_STATE_DRAGGING) {
                    isPress = true;
                } else {//必须写else，不然的话，倒数第二页就开始自动跳转了
                    isPress = false;
                }
            }
        });

    }

    /**
     * 更新顶部日期栏的状态
     *
     * @param position
     */

    protected void updateDateBar(int position) {
        dateToday.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));
        dateNextday.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));
        dateWeek.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));
        dateMonth.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));
//        dateYear.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));

        switch (position) {
            case 0:
                dateToday.setTextColor(getResources().getColor(R.color.text_color_black));
                dateToday.getPaint().setFakeBoldText(true);
                break;
            case 1:
                dateNextday.setTextColor(getResources().getColor(R.color.text_color_black));
                dateNextday.getPaint().setFakeBoldText(true);
                break;
            case 2:
                dateWeek.setTextColor(getResources().getColor(R.color.text_color_black));
                dateWeek.getPaint().setFakeBoldText(true);

                break;
            case 3:
                dateMonth.setTextColor(getResources().getColor(R.color.text_color_black));
                dateMonth.getPaint().setFakeBoldText(true);
                break;
//            case 4:
//                dateYear.setTextColor(getResources().getColor(R.color.text_color_white));
//                break;
        }
    }

    /**
     * 星座运势主体页 ViewPagerAdapter
     *
     * <AUTHOR>
     * @date 2015-2015年8月7日
     */
    private class ConstellationPagerAdapter extends PagerAdapter {
        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public int getCount() {
            if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
                return 4;
            } else {
                return 3;
            }
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View view = LayoutInflater.from(
                    ConstellationActivity.this).inflate(
                    R.layout.constellation_page_item_layout, null);

            ViewHolder holder = new ViewHolder(view);

            showData(position, holder);

            view.setTag(position);
            container.addView(view);
            return view;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            for (int i = 0; i < container.getChildCount(); i++) {
                if (container.getChildAt(i).getTag() instanceof Integer
                        && (Integer) container.getChildAt(i).getTag() == position) {
                    container.removeViewAt(i);
                }
            }
        }

        // 重写此方法, 通过 notifyDataSetChange() 可以刷新当前界面
        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }

    }

    class ViewHolder {
        @BindView(R.id.comment_head_icon)
        RoundImageView mCommentHeadIcon;
        @BindView(R.id.comment_nick_name_tv)
        TextView mCommentNickNameTv;
        @BindView(R.id.profile_info_tv)
        TextView mProfileInfoTv;
        @BindView(R.id.column_name_tv)
        TextView mColumnNameTv;
        @BindView(R.id.constellation_comment_column_info)
        TextView mConstellationCommentColumnInfo;
        @BindView(R.id.comment_content_layout)
        LinearLayout mCommentContentLayout;
        @BindView(R.id.con_constellation_icon)
        ImageView mConConstellationIcon;
        @BindView(R.id.con_constellation_name_tv)
        TextView mConConstellationNameTv;
        @BindView(R.id.con_constellation_date_tv)
        TextView mConConstellationDateTv;
        @BindView(R.id.con_total_lucky_value_ratingbar)
        RatingBar mConTotalLuckyValueRatingbar;
        @BindView(R.id.con_total_summary_tv)
        TextView mConTotalSummaryTv;
        @BindView(R.id.con_page_date_tv)
        TextView mConPageDateTv;
        @BindView(R.id.con_date_fortune_tv)
        TextView mConDateFortuneTv;
        @BindView(R.id.con_date_fortune_info_tv)
        TextView mConDateFortuneInfoTv;
        @BindView(R.id.con_love_lucky_index_tv)
        TextView mConLoveLuckyIndexTv;
        @BindView(R.id.con_love_lucky_index_ratingbar)
        RatingBar mConLoveLuckyIndexRatingbar;
        @BindView(R.id.con_lucky_color_tv)
        TextView mConLuckyColorTv;
        @BindView(R.id.con_lucky_color_value_tv)
        TextView mConLuckyColorValueTv;
        @BindView(R.id.con_wark_lucky_index_tv)
        TextView mConWarkLuckyIndexTv;
        @BindView(R.id.con_wark_lucky_index_ratingbar)
        RatingBar mConWarkLuckyIndexRatingbar;
        @BindView(R.id.con_lucky_number_tv)
        TextView mConLuckyNumberTv;
        @BindView(R.id.con_lucky_number_value_tv)
        TextView mConLuckyNumberValueTv;
        @BindView(R.id.con_money_lucky_index_tv)
        TextView mConMoneyLuckyIndexTv;
        @BindView(R.id.con_money_lucky_index_ratingbar)
        RatingBar mConMoneyLuckyIndexRatingbar;
        @BindView(R.id.con_offirend_tv)
        TextView mConOffirendTv;
        //        @BindView(R.id.con_offirend_value_tv)
//        TextView mConOffirendValueTv;
        @BindView(R.id.date_love_tv)
        TextView mDateLoveTv;
        @BindView(R.id.con_love_lucky_summary_tv)
        TextView mConLoveLuckySummaryTv;
        @BindView(R.id.date_cause_tv)
        TextView mDateCauseTv;
        @BindView(R.id.con_wark_lucky_summary_tv)
        TextView mConWarkLuckySummaryTv;
        @BindView(R.id.date_money_tv)
        TextView mDateMoneyTv;
        @BindView(R.id.money_info_tv)
        TextView mMoneyInfoTv;
        //        @BindView(R.id.hint_info)
//        TextView mHintInfoTv;
        @BindView(R.id.con_bottom_content_layout)
        LinearLayout mConBottomContentLayout;

        ViewHolder(View view) {
            ButterKnife.bind(this, view);

            if (!ConstellationActivity.this.isSelf) {
                mCommentHeadIcon.setVisibility(View.GONE);
                mCommentNickNameTv.setVisibility(View.GONE);
                mProfileInfoTv.setVisibility(View.GONE);
            }
        }
    }

//    @Override
//    public void onClick(View v) {
//        switch (v.getId()) {
//            case R.id.cons_date_today:
//                mViewPager.setCurrentItem(0, false);
//                break;
//            case R.id.cons_date_nextday:
//                mViewPager.setCurrentItem(1, false);
//                break;
//            case R.id.cons_date_week:
//                mViewPager.setCurrentItem(2, false);
//                break;
//            case R.id.cons_date_month:
//                mViewPager.setCurrentItem(3, false);
//                break;
//        }
//    }

    @OnClick({R.id.cons_date_today, R.id.cons_date_nextday, R.id.cons_date_week, R.id.cons_date_month,
            R.id.cons_qian_iv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.cons_date_today:
                mViewPager.setCurrentItem(0, false);
                break;
            case R.id.cons_date_nextday:
                mViewPager.setCurrentItem(1, false);
                break;
            case R.id.cons_date_week:
                mViewPager.setCurrentItem(2, false);
                break;
            case R.id.cons_date_month:
                if (Apputils.systemLanguageIsChinese(ConstellationActivity.this))
                    mViewPager.setCurrentItem(3, false);
                else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CONSTELLATION_FATE_CLICK);
                    startActivity(new Intent(ConstellationActivity.this, YesNoActivity.class));
                }
//                overridePendingTransition(R.anim.slide_in_right,R.anim.slide_in_left);
                break;
            case R.id.cons_qian_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CONSTELLATION_PRAY_CLICK);
                startActivity(new Intent(ConstellationActivity.this, QianActivity.class));
                break;
        }
    }

    @Override
    protected void onResume() {
        isOpen = false;
        super.onResume();
    }

    /**
     * 获取星座运势相关数据
     */
    private void getData() {
        RequestParams params = HttpHelper.getBaseParams(true);
        Intent intent = getIntent();
        String url;
        String phone = intent.getStringExtra("phone");
        LogUtils.e("phone:" + phone);
        if (phone == null || phone.length() == 0) {
            url = HttpHelper.URL_CONSTELLATION_LIST;
        } else {
            url = HttpHelper.URL_CONSTELLATION_LIST_EXT;
            params.addFormDataPart("xingzuoTotwoo_id", phone);
        }
        HttpRequest.get(url, params,
                new RequestCallBack<String>(ConstellationActivity.this) {
                    @Override
                    public void onStart() {
                        super.onStart();
                        JSONObject data = HttpHelper.getDataCache(HttpHelper.URL_CONSTELLATION_LIST);
                        if (data != null) {
                            parseData(data);
                        }
                    }

                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);

                        JSONObject data = HttpHelper
                                .parserStringResponse(s);
                        if (data != null) {
                            parseData(data);

                            // 保存缓存数据
                            HttpHelper.saveDataCache(
                                    HttpHelper.URL_CONSTELLATION_LIST, s);
                        }
                    }
                });

        if (Apputils.systemLanguageIsChinese(this)) {
            //星点评
            HttpRequest.get(
                    HttpHelper.URL_CONSTELLATION_COMMENT, params,
                    new RequestCallBack<String>() {
                        @Override
                        public void onStart() {
                            JSONObject data = HttpHelper
                                    .getDataCache(HttpHelper.URL_CONSTELLATION_COMMENT);
                            if (data != null) {
                                Gson gson = new Gson();
                                comment = gson.fromJson(data.toString(),
                                        ConstellationComment.class);
                            }
                            super.onStart();
                        }

                        @Override
                        public void onLogicSuccess(String s) {
                            JSONObject data = HttpHelper
                                    .parserStringResponse(s);
                            if (data != null) {
                                Gson gson = new Gson();
                                comment = gson.fromJson(data.toString(),
                                        ConstellationComment.class);
                                // 保存缓存数据
                                HttpHelper.saveDataCache(
                                        HttpHelper.URL_CONSTELLATION_COMMENT,
                                        s);

                                if (mAdapter != null) {
                                    mAdapter.notifyDataSetChanged();
                                }
                            }
                        }
                    });
        }
    }

    /**
     * 星点评数据
     */
    private ConstellationComment comment;

    /**
     * 解析数据
     *
     * @param data
     */
    protected void parseData(JSONObject data) {
        if (data == null) {
            return;
        }
        if (Apputils.systemLanguageIsChinese(this)) {
            conName = data.optString("name");
        } else {
            conName = data.optString("name_en");
        }
        getUserConInfo();

        if (consData == null) {
            consData = new ArrayList<>();
        } else {
            consData.clear();
        }

        Gson gson = new Gson();
        consData.add(gson.fromJson(data.optString("today"), ConstellationDataModel.class));
        consData.add(gson.fromJson((data.optString("tomorrow")), ConstellationDataModel.class));
        consData.add(gson.fromJson((data.optString("week")), ConstellationDataModel.class));
        consData.add(gson.fromJson((data.optString("month")), ConstellationDataModel.class));
        consData.add(gson.fromJson((data.optString("year")), ConstellationDataModel.class));

        // 刷新当前数据
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 针对每个页面的数据进行展示
     *
     * @param position
     * @param holder
     */
    private void showData(int position, ViewHolder holder) {
        if (holder == null || position > 3) {
            return;
        }

        // 对于周, 月, 年的数据隐藏幸运颜色, 幸运数字, 速配星座相关条目
        if (position > 1) {
            holder.mConLuckyColorTv.setVisibility(View.GONE);
            holder.mConLuckyColorValueTv.setVisibility(View.GONE);
            holder.mConLuckyNumberTv.setVisibility(View.GONE);
            holder.mConLuckyNumberValueTv.setVisibility(View.GONE);
            holder.mConOffirendTv.setVisibility(View.GONE);
//            holder.mConOffirendValueTv.setVisibility(View.GONE);
        }

        if (position == 0 && comment != null && Apputils.systemLanguageIsChinese(this)) {
            holder.mCommentContentLayout.setVisibility(View.VISIBLE);

            BitmapHelper.display(ConstellationActivity.this,
                    holder.mCommentHeadIcon,
                    comment.getZhuoyue_img());
            holder.mProfileInfoTv.setText(comment.getZhuoyue_intro());
            holder.mConstellationCommentColumnInfo.setText(comment.getZhuoyue_name());
        }

        if (!Apputils.systemLanguageIsChinese(this)) {
            holder.mConBottomContentLayout.setVisibility(View.GONE);
            holder.mConLuckyColorTv.setVisibility(View.GONE);
            holder.mConLuckyColorValueTv.setVisibility(View.GONE);
            holder.mConDateFortuneInfoTv.setVisibility(View.GONE);
//            holder.mHintInfoTv.setVisibility(View.GONE);
        }

        try {
            Calendar cal = Calendar.getInstance();
            // 左上角日期
            switch (position) {
                case 0:
                    holder.mConPageDateTv.setText(formatDateString(cal));
                    holder.mConDateFortuneTv.setText(R.string.today);
                    holder.mConDateFortuneInfoTv.setText(R.string.today_lucky);
                    break;
                case 1:
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                    holder.mConPageDateTv.setText(formatDateString(cal));
                    holder.mConDateFortuneTv.setText(R.string.nextday);
                    holder.mConDateFortuneInfoTv.setText(R.string.nextday_lucky);
                    break;
                case 2:
                    cal.add(Calendar.DAY_OF_MONTH, -(cal.get(Calendar.DAY_OF_WEEK) - 1));
                    String first = formatDateString(cal);
                    cal.add(Calendar.DAY_OF_MONTH, 6);
                    holder.mConPageDateTv.setText(first + " - " + formatDateString(cal));
                    holder.mConDateFortuneTv.setText(R.string.week);
                    holder.mConDateFortuneInfoTv.setText(R.string.week_lucky);
                    break;
                case 3:
                    holder.mConDateFortuneTv.setText(R.string.month);
                    holder.mConDateFortuneInfoTv.setText(R.string.month_lucky);
                    holder.mConPageDateTv.setText(getResources().getStringArray(R.array.month_names_en)[cal
                            .get(Calendar.MONTH)]);
                    break;
//                case 4:
//                    holder.mConDateFortuneTv.setText(R.string.years);
//                    holder.mConDateFortuneInfoTv.setText(R.string.years_lucky);
//                    holder.mConPageDateTv.setText(cal.get(Calendar.YEAR) + "");
//                    break;

            }

            // 填充数据
            ConstellationDataModel model = null;
            if (consData != null) {
                model = consData.get(position);
            }

            if (model != null) {
                // 主体框内内容
                holder.mConConstellationIcon.setImageResource(conIcon);
                holder.mConConstellationNameTv.setText(conName.toUpperCase());
                holder.mConConstellationDateTv.setText(conDate);
                holder.mConTotalLuckyValueRatingbar
                        .setRating(Integer.parseInt(model.getAll()));

                if (Apputils.systemLanguageIsChinese(this)) {
                    holder.mConTotalSummaryTv.setText(model.getSummary());
                } else {
                    holder.mConTotalSummaryTv.setText(model.getSummary_en());
                }

                // 今日运势
                holder.mConLoveLuckyIndexRatingbar.setRating(Integer.parseInt(model.getLove()));
                holder.mConWarkLuckyIndexRatingbar.setRating(Integer.parseInt(model.getCause()));

                holder.mConLuckyNumberValueTv
                        .setText(model.getLuck_number());
                holder.mConMoneyLuckyIndexRatingbar
                        .setRating(Integer.parseInt(model.getMoney()));

                if (Apputils.systemLanguageIsChinese(this)) {
                    holder.mConOffirendTv
                            .setText(getString(R.string.match_constellation) + "\t\t" + model.getMatch_friend());
                } else {
                    holder.mConOffirendTv
                            .setText(getString(R.string.match_constellation) + "    " + model.getMatch_friend_en().toUpperCase());
                }

                holder.mConLuckyColorValueTv
                        .setText(model.getLuck_color());

                // 爱情, 事业运势
                holder.mConLoveLuckySummaryTv
                        .setText(model.getLove_sum());
                holder.mConWarkLuckySummaryTv
                        .setText(model.getCause_sum());
                holder.mMoneyInfoTv.setText(model.getMoney_sum());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 格式化 到指定的 日期格式
     */
    private String formatDateString(Calendar cal) {
        return getResources().getStringArray(R.array.month_names_en)[cal
                .get(Calendar.MONTH)]
                + " "
                + String.format("%02d", cal.get(Calendar.DAY_OF_MONTH));
    }

    CustomDialog dialog;

    public void getPhotoDialog() {
        dialog = new CustomDialog(this);
        LinearLayout modify_head_dialog_ll = new LinearLayout(this);
        modify_head_dialog_ll.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        modify_head_dialog_ll.setOrientation(LinearLayout.VERTICAL);
        TextView album_tv = new TextView(this);
        TextView camera_tv = new TextView(this);
        modify_head_dialog_ll.addView(album_tv);
        modify_head_dialog_ll.addView(camera_tv);

        dialog.setMainLayoutView(modify_head_dialog_ll);
        album_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        camera_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        album_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20), Apputils.dp2px(this, 15));
        camera_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20), Apputils.dp2px(this, 15));
        album_tv.setBackgroundResource(R.drawable.item_bg);
        camera_tv.setBackgroundResource(R.drawable.item_bg);
        album_tv.setText(getString(R.string.xingzuo_t1));
        Drawable drawable1 = getResources().getDrawable(R.drawable.luckyday);
        Drawable drawable2 = getResources().getDrawable(R.drawable.share_ico_2);
        album_tv.setCompoundDrawablesWithIntrinsicBounds(drawable1, null, null, null);
        album_tv.setCompoundDrawablePadding(Apputils.dp2px(this, 9));
        camera_tv.setCompoundDrawablesWithIntrinsicBounds(drawable2, null, null, null);
        camera_tv.setCompoundDrawablePadding(Apputils.dp2px(this, 9));
        camera_tv.setText(getString(R.string.xingzuo_t2));
        album_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        camera_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        album_tv.setTextSize(16);
        camera_tv.setTextSize(16);
        dialog.setNegativeButtonText(R.string.cancel);
        // 相册tv监听点击开启选择图片app
        album_tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivityForResult(new Intent(ConstellationActivity.this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_FORTUNE), TYPE_FORTUNE);
                dialog.dismiss();
            }
        });
        //分享
        camera_tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (consData == null || consData.size() == 0) {
                    ToastUtils.showLong(ConstellationActivity.this, R.string.error_no_data);
                    return;
                }

                // 跳转分享页面
                Intent intent = new Intent(ConstellationActivity.this, ShareConstellationActivity.class);
                intent.putExtra(TAG_CONSTELLATION_DATA, consData.get(mViewPager.getCurrentItem()));
                intent.putExtra(TAG_CONSTELLATION_DATA_TYPE, mViewPager.getCurrentItem());
                intent.putExtra(TAG_CONSTELLATION_NAME, conName);

                startActivity(intent);
                dialog.dismiss();
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理动画视图
        if (fallingView != null) {
            try {
                fallingView.clearAnimation();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 清理 ViewPager 和适配器
        if (mViewPager != null) {
            try {
                mViewPager.clearOnPageChangeListeners();
                mViewPager.setAdapter(null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 清理适配器引用
        mAdapter = null;

        // 清理对话框
        if (dialog != null && dialog.isShowing()) {
            try {
                dialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
            dialog = null;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
