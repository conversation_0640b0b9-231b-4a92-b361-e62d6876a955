package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.CallRemindContact;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.Sedentary;
import com.totwoo.totwoo.bean.UserOption;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.fragment.CustomAngelFragment;
import com.totwoo.totwoo.fragment.CustomReminderFragment;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by xinyoulingxi on 2017/9/27.
 *  version 1.0
 */
@Deprecated
public class NotifyActivity extends BaseActivity implements SubscriberListener {

    public final static int REQUESTCODE_TOTWOO = 1;
    public final static int REQUESTCODE_CALL = 2;
    public final static int REQUESTCODE_STEP = 3;
    public final static int REQUESTCODE_FORTUNE = 4;
    public final static int REQUESTCODE_SEDENTARY = 5;
    public final static int REQUESTCODE_APP = 6;
    public final static int REQUESTCODE_PERIOD = 7;
    public final static int REQUESTCODE_WATER = 8;
    public final static int REQUESTCODE_WISH = 9;
    public final static int REQUESTCODE_GIFT = 10;

    @BindView(R.id.call_notify_tv)
    TextView mCallNotifyInfoTv;
    @BindView(R.id.notify_remind_totwoo_way)
    TextView mNotifyRemindTotwooWay;
    @BindView(R.id.notify_remind_gift_way)
    TextView mNotifyGiftWay;
    @BindView(R.id.notify_remind_totwoo_rl)
    RelativeLayout mNotifyRemindTotwooRl;
    @BindView(R.id.notify_remind_step_way)
    TextView mNotifyRemindStepWay;
    @BindView(R.id.notify_remind_step_rl)
    RelativeLayout mNotifyRemindStepRl;
    @BindView(R.id.notify_remind_fortune_way)
    TextView mNotifyRemindFortuneWay;
    @BindView(R.id.notify_remind_fortune_rl)
    RelativeLayout mNotifyRemindFortuneRl;
    @BindView(R.id.notify_remind_sedentary_way)
    TextView mNotifyRemindSedentaryWay;
    @BindView(R.id.notify_remind_sedentary_rl)
    RelativeLayout mNotifyRemindSedentaryRl;
    @BindView(R.id.notify_remind_app_way)
    TextView mNotifyRemindAppWay;
    @BindView(R.id.notify_remind_app_rl)
    RelativeLayout mNotifyRemindAppRl;
    @BindView(R.id.notify_remind_period_rl)
    RelativeLayout mNotifyRemindPeriodRl;
    @BindView(R.id.notify_remind_water_rl)
    RelativeLayout mNotifyRemindWaterRl;
    @BindView(R.id.notify_memo_rl)
    RelativeLayout mNotifyMemoRl;
    @BindView(R.id.notify_wish_rl)
    RelativeLayout mNotifyWishRl;
    @BindView(R.id.notify_remind_period_way)
    TextView mNotifyRemindPeriodWay;
    @BindView(R.id.notify_remind_water_way)
    TextView mNotifyRemindWaterWay;
    @BindView(R.id.notify_wish_way)
    TextView mNotifyWishWay;

    @BindView(R.id.notify_remind_app_light)
    ImageView appIco1;
    @BindView(R.id.notify_remind_app_virbation)
    ImageView appIco2;
    @BindView(R.id.notify_remind_app_arrow)
    ImageView appIco3;

    @BindView(R.id.notify_remind_totwoo_light)
    ImageView totwooIco1;
    @BindView(R.id.notify_remind_totwoo_virbation)
    ImageView totwooIco2;
    @BindView(R.id.notify_remind_totwoo_arrow)
    ImageView totwooIco3;


    @BindView(R.id.notify_remind_gift_light)
    ImageView giftIco1;
    @BindView(R.id.notify_remind_gift_virbation)
    ImageView giftIco2;
    @BindView(R.id.notify_remind_gift_arrow)
    ImageView giftIco3;

    @BindView(R.id.notify_remind_step_light)
    ImageView stepIco1;
    @BindView(R.id.notify_remind_step_virbation)
    ImageView stepIco2;
    @BindView(R.id.notify_remind_step_arrow)
    ImageView stepIco3;

    @BindView(R.id.notify_remind_fortune_light)
    ImageView fortuneIco1;
    @BindView(R.id.notify_remind_fortune_virbation)
    ImageView fortuneIco2;
    @BindView(R.id.notify_remind_fortune_arrow)
    ImageView fortuneIco3;

    @BindView(R.id.notify_remind_sedentary_light)
    ImageView sedentaryIco1;
    @BindView(R.id.notify_remind_sedentary_virbation)
    ImageView sedentaryIco2;
    @BindView(R.id.notify_remind_sedentary_arrow)
    ImageView sedentaryIco3;

    @BindView(R.id.notify_remind_period_light)
    ImageView periodIco1;
    @BindView(R.id.notify_remind_period_virbation)
    ImageView periodIco2;
    @BindView(R.id.notify_remind_period_arrow)
    ImageView periodIco3;

    @BindView(R.id.notify_remind_water_light)
    ImageView waterIco1;
    @BindView(R.id.notify_remind_water_virbation)
    ImageView waterIco2;
    @BindView(R.id.notify_remind_water_arrow)
    ImageView waterIco3;

    @BindView(R.id.call_notify_ico1)
    ImageView callIco1;
    @BindView(R.id.call_notify_ico2)
    ImageView callIco2;
    @BindView(R.id.call_notify_ico3)
    ImageView callIco3;

    @BindView(R.id.call_notify_peo1)
    ImageView callPeo1;
    @BindView(R.id.call_notify_peo2)
    ImageView callPeo2;
    @BindView(R.id.call_notify_peo3)
    ImageView callPeo3;

    @BindView(R.id.notify_wish_light)
    ImageView wishIco1;
    @BindView(R.id.notify_wish_virbation)
    ImageView wishIco2;
    @BindView(R.id.notify_wish_arrow)
    ImageView wishIco3;

    @BindView(R.id.call_notify_layout)
    LinearLayout callIcos;

    @BindView(R.id.guide_rl)
    RelativeLayout mGuideRl;
    @BindView(R.id.notify_guide_steps_tv)
    TextView mNotifyGuideStepsTv;

    @BindView(R.id.notify_vibration_layout_ll)
    LinearLayout mVibrationLl;
    @BindView(R.id.notify_vibration_layout_bottom)
    View mVibration;

    private String separator;

    @BindView(R.id.vibration_setting_progress_bar)
    SeekBar mVibrationSettingProgressBar;

    private int tempProgress;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notify);
        ButterKnife.bind(this);
        LogUtils.e("notifyFragment, onCreateView");
        String jewName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (ToTwooApplication.owner.getGender() == 1) {
            mNotifyRemindPeriodRl.setVisibility(View.VISIBLE);
        } else {
            mNotifyRemindPeriodRl.setVisibility(View.GONE);
        }

        if (TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_SL) || BleParams.isLollipopJewelry()) {
            mNotifyRemindWaterRl.setVisibility(View.VISIBLE);
            if (CustomAngelFragment.define_num > 0) {
                mNotifyMemoRl.setVisibility(View.VISIBLE);
            }
        } else if (BleParams.isMemoryJewelry()) {
            mNotifyRemindTotwooRl.setVisibility(View.GONE);
        } else if (BleParams.isWishJewlery()) {
            mNotifyWishRl.setVisibility(View.VISIBLE);
        } else if (BleParams.isMWJewlery()) {
            mNotifyRemindTotwooRl.setVisibility(View.GONE);
            mNotifyRemindStepRl.setVisibility(View.GONE);
            mNotifyRemindPeriodRl.setVisibility(View.VISIBLE);
            mNotifyRemindWaterRl.setVisibility(View.VISIBLE);
            if (CustomReminderFragment.define_num > 0) {
                mNotifyMemoRl.setVisibility(View.VISIBLE);
            }
        } else if (BleParams.isButtonBatteryJewelry()) {
            mNotifyRemindStepRl.setVisibility(View.GONE);
            mVibrationLl.setVisibility(View.GONE);
            mVibration.setVisibility(View.GONE);
//            mNotifyRemindAppRl.setVisibility(View.GONE);
            mNotifyRemindSedentaryRl.setVisibility(View.GONE);
        } else if (BleParams.isSM2()) {
//            mNotifyRemindAppRl.setVisibility(View.GONE);
            mNotifyRemindSedentaryRl.setVisibility(View.GONE);
            mNotifyRemindPeriodRl.setVisibility(View.GONE);
            mNotifyRemindStepRl.setVisibility(View.GONE);
        }

        if (JewInfoSingleton.getInstance().getJewVersion() == 1) {
            mNotifyRemindTotwooRl.setVisibility(View.VISIBLE );
        }else {
            mNotifyRemindTotwooRl.setVisibility(View.GONE);
        }

        if (!BleParams.isButtonBatteryJewelry()) {
            mNotifyRemindPeriodRl.setVisibility(View.GONE);
        }

        // 移除 App 提醒
        mNotifyRemindAppRl.setVisibility(View.GONE);

        HttpHelper.userOption.getUseroption()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<UserOption>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<UserOption> userOptionHttpBaseBean) {
                        if (userOptionHttpBaseBean.getErrorCode() == 0) {
                            NotifyUtil.setTotwooSwitchKey(NotifyActivity.this, userOptionHttpBaseBean.getData().getTotwoo_notify() != 0);
                            NotifyUtil.setFortuneSwitchKey(NotifyActivity.this, userOptionHttpBaseBean.getData().getConstellation_notify() != 0);
                        }
                        initCommonItem(mNotifyRemindFortuneWay, fortuneIco1, fortuneIco2, fortuneIco3, NotifyUtil.getFortuneNotifyModel(NotifyActivity.this));
                        initCommonItem(mNotifyRemindTotwooWay, totwooIco1, totwooIco2, totwooIco3, NotifyUtil.getTotwooNotifyModel(NotifyActivity.this), true);

                    }
                });
        initContent();

        EventBus.getDefault().register(this);
        InjectUtils.injectOnlyEvent(this);

        tempProgress = PreferencesUtils.getInt(this, BleParams.SELECT_VIBRATION_INDEX_TAG, 90);

        if (tempProgress > 0) {
            mVibrationSettingProgressBar.setProgress(tempProgress);
        }

        mVibrationSettingProgressBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                tempProgress = mVibrationSettingProgressBar.getProgress();
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    Toast.makeText(ToTwooApplication.baseContext, R.string.error_jewelry_connect, Toast.LENGTH_SHORT).show();
                    return;
                }
                BluetoothManage.getInstance().setVibrationIntensity(tempProgress);
                PreferencesUtils.put(NotifyActivity.this, BleParams.SELECT_VIBRATION_INDEX_TAG, tempProgress);
            }
        });
    }

    @Override
    protected void initTopBar() {
        setTopLeftIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.me_t9);
        setTopLeftOnclik(v -> saveUserOption());
    }

    private void saveUserOption() {
        HttpHelper.userOption.saveUseroption(NotifyUtil.getTotwooNotifyModel(this).isNotifySwitch() ? 1 : 0, NotifyUtil.getFortuneNotifyModel(this).isNotifySwitch() ? 1 : 0)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<UserOption>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        finish();
                    }

                    @Override
                    public void onNext(HttpBaseBean<UserOption> userOptionHttpBaseBean) {
                        finish();
                    }
                });

    }

    @Override
    public void onBackPressed() {
        saveUserOption();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    private void initContent() {
        separator = Apputils.systemLanguageIsChinese(this) ? " " : ", ";
        mNotifyRemindFortuneRl.setVisibility(Apputils.systemLanguageIsOther(this)
                ? View.GONE : View.VISIBLE);

        initCallItem(NotifyUtil.getCallNotifyModel(this));

        initCommonItem(mNotifyRemindStepWay, stepIco1, stepIco2, stepIco3, NotifyUtil.getStepNotifyModel(this));
        initCommonItem(mNotifyRemindSedentaryWay, sedentaryIco1, sedentaryIco2, sedentaryIco3, NotifyUtil.getSedentaryNotifyModel(this));
        initCommonItem(mNotifyRemindPeriodWay, periodIco1, periodIco2, periodIco3, NotifyUtil.getPeriodNotifyModel(this));
        initCommonItem(mNotifyRemindWaterWay, waterIco1, waterIco2, waterIco3, NotifyUtil.getWaterTimeNotifyModel(this));
        initCommonItem(mNotifyWishWay, wishIco1, wishIco2, wishIco3, NotifyUtil.getWishNotifyModel(this));
        initCommonItem(mNotifyGiftWay, giftIco1, giftIco2, giftIco3, NotifyUtil.getGiftNotifyModel(this));
        initAppItem(NotifyUtil.getAppNotifyModel(this));
    }

    private void initCommonItem(TextView textView, ImageView ico1, ImageView ico2, ImageView ico3, JewelryNotifyModel model) {
        initCommonItem(textView, ico1, ico2, ico3, model, false);
    }

    private void initCommonItem(TextView textView, ImageView ico1, ImageView ico2, ImageView ico3, JewelryNotifyModel model, boolean isTotwoo) {
        ico1.setVisibility(View.GONE);
        ico2.setVisibility(View.GONE);
        ico3.setVisibility(View.GONE);
        if (model.isNotifySwitch()) {
            ico1.setVisibility(View.VISIBLE);
            ico2.setVisibility(View.VISIBLE);
            ico3.setVisibility(View.VISIBLE);
            textView.setVisibility(View.GONE);
            ico1.setImageResource(model.getFlashColorInteger());
            if (isTotwoo && PreferencesUtils.getInt(NotifyActivity.this, NotifyTotwooActivity.TOTWOO_MUSIC_TYPE, 0) > 0) {
                ico2.setImageResource(R.drawable.remind_long);
            } else {
                ico2.setImageResource(model.getVibrationInteger());
            }
            /*textView.setTextColor(getResources().getColor(R.color.text_color_black_nomal));
            if (Apputils.systemLanguageIsChinese(getActivity())) {
                textView.setText(model.getFlashColorString(getActivity())
                        + separator + model.getVibrationString(getActivity()));
            } else {
                textView.setText(model.getFlashColorString(getActivity()));
            }*/
        } else {
            ico1.setVisibility(View.GONE);
            ico2.setVisibility(View.GONE);
            ico3.setVisibility(View.GONE);
            textView.setVisibility(View.VISIBLE);
            textView.setTextColor(getResources().getColor(R.color.text_color_black_important));
            textView.setText(R.string.notify_off);
        }
    }

    private void initCallItem(JewelryNotifyModel model) {
        if (!model.isNotifySwitch()) {
            switchCallIcos(false);
            callIcos.setVisibility(View.GONE);
            mCallNotifyInfoTv.setVisibility(View.VISIBLE);
            mCallNotifyInfoTv.setText(R.string.notify_off);
        } else {
            callIcos.setVisibility(View.VISIBLE);
            mCallNotifyInfoTv.setVisibility(View.GONE);
            String needShowInfo = "";
            //重要联系人
            if (PreferencesUtils.getBoolean(this, CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_SWITCH_KEY, false)) {
                String dataJson = PreferencesUtils.getString(this, CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_DATA_KEY, "");
                List<CallRemindContact> mCallRamindContacts = new Gson().fromJson(dataJson, new TypeToken<List<CallRemindContact>>() {
                }.getType());
                if (mCallRamindContacts == null || mCallRamindContacts.size() == 0) {
                    needShowInfo = getString(R.string.dont_select_contact1);
                    switchCallIcos(false);
                    callIcos.setVisibility(View.GONE);
                    mCallNotifyInfoTv.setVisibility(View.VISIBLE);
                } else {
                    callIcos.setVisibility(View.VISIBLE);
                    switchCallIcos(false);

                    switch (mCallRamindContacts.size()) {
                        case 3:
                            callIco3.setVisibility(View.VISIBLE);
                            callIco3.setImageResource(NotifyUtil.getColorImageResId(mCallRamindContacts.get(2).getFlashColor()));
                            callPeo3.setVisibility(View.VISIBLE);
                        case 2:
                            callIco2.setVisibility(View.VISIBLE);
                            callIco2.setImageResource(NotifyUtil.getColorImageResId(mCallRamindContacts.get(1).getFlashColor()));
                            callPeo2.setVisibility(View.VISIBLE);
                        case 1:
                            callIco1.setVisibility(View.VISIBLE);
                            callIco1.setImageResource(NotifyUtil.getColorImageResId(mCallRamindContacts.get(0).getFlashColor()));
                            callPeo1.setVisibility(View.VISIBLE);
                            break;
                    }


                    /********************************/
//                    for (CallRamindContact callRamindContact : mCallRamindContacts) {
//                        needShowInfo = needShowInfo + NotifyUtil.getColorString(this, callRamindContact.getFalshColor()) + separator;
//                    }

//                    if (needShowInfo.endsWith(separator)) {
//                        needShowInfo = needShowInfo.substring(0, needShowInfo.length() - separator.length());
//                    }
//                    needShowInfo += " • " + getString(R.string.notify_remind_call_important_contact_count, mCallRamindContacts.size());
                }
            } else if (PreferencesUtils.getBoolean(this, CallRemindSetActivity.ALL_CALL_REMIND_SWITCH_KEY, true)) {
                //needShowInfo = NotifyUtil.getColorString(this, PreferencesUtils.getString(this, CallRemindSetActivity.ALL_CALL_REMIND_FLASH_KEY, "RED")) + " • " + getString(R.string.call_remind_all);
                callIcos.setVisibility(View.VISIBLE);
                switchCallIcos(false);
                callIco1.setVisibility(View.VISIBLE);
                callIco1.setImageResource(NotifyUtil.getColorImageResId(PreferencesUtils.getString(this, CallRemindSetActivity.ALL_CALL_REMIND_FLASH_KEY, "RED")));
                callPeo1.setVisibility(View.VISIBLE);
                callPeo1.setImageResource(R.drawable.people_all);
            } else if (PreferencesUtils.getBoolean(this, CallRemindSetActivity.ALL_CONTACT_REMIND_SWITCH_KEY, false)) {
                //needShowInfo = NotifyUtil.getColorString(this, PreferencesUtils.getString(this, CallRemindSetActivity.ALL_CONTACT_REMIND_FLASH_KEY, "RED")) + " • " + getString(R.string.call_ramind_contact);
                callIcos.setVisibility(View.VISIBLE);
                switchCallIcos(false);
                callIco1.setVisibility(View.VISIBLE);
                callIco1.setImageResource(NotifyUtil.getColorImageResId(PreferencesUtils.getString(this, CallRemindSetActivity.ALL_CONTACT_REMIND_FLASH_KEY, "RED")));
                callPeo1.setVisibility(View.VISIBLE);
                callPeo1.setImageResource(R.drawable.notify_txl_friend);
            }
            mCallNotifyInfoTv.setText(needShowInfo);
        }
    }

    private void switchCallIcos(boolean visible) {
        int tmp = visible ? View.VISIBLE : View.GONE;
        callIco1.setVisibility(tmp);
        callIco2.setVisibility(tmp);
        callIco3.setVisibility(tmp);
        callPeo1.setVisibility(tmp);
        callPeo2.setVisibility(tmp);
        callPeo3.setVisibility(tmp);

        callPeo1.setImageResource(R.drawable.people_one);
        callPeo2.setImageResource(R.drawable.people_one);
        callPeo3.setImageResource(R.drawable.people_one);
    }

    private void initAppItem(JewelryNotifyModel model) {
        appIco1.setVisibility(View.GONE);
        appIco2.setVisibility(View.GONE);
        appIco3.setVisibility(View.GONE);
        if (!CommonUtils.isNotificationServiceEnabled()) {
            model.setNotifySwitch(false);
            NotifyUtil.setAppNotify(this, model);
        }

        if (!model.isNotifySwitch()) {
            mNotifyRemindAppWay.setTextColor(getResources().getColor(R.color.text_color_black_important));
            mNotifyRemindAppWay.setText(R.string.notify_off);
            appIco1.setVisibility(View.GONE);
            appIco2.setVisibility(View.GONE);
            appIco3.setVisibility(View.GONE);
            mNotifyRemindAppWay.setVisibility(View.VISIBLE);
        } else {
            mNotifyRemindAppWay.setTextColor(getResources().getColor(R.color.text_color_black_nomal));

            if (Apputils.systemLanguageIsChinese(this)) {
                mNotifyRemindAppWay.setText(model.getFlashColorString(this)
                        + separator + model.getVibrationString(this));
            } else {
                mNotifyRemindAppWay.setText(model.getFlashColorString(this));
            }
            appIco1.setVisibility(View.VISIBLE);
            appIco2.setVisibility(View.VISIBLE);
            appIco3.setVisibility(View.VISIBLE);
            mNotifyRemindAppWay.setVisibility(View.GONE);
            appIco1.setImageResource(model.getFlashColorInteger());
            appIco2.setImageResource(model.getVibrationInteger());
        }
    }

    public void onShow() {
        BluetoothManage.getInstance().connectedStatus();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case 0://通知读取权限设置
                if (CommonUtils.isNotificationServiceEnabled()) {
                    startActivityForResult(new Intent(this, AppNotificationsActivity.class), 1);
                }
                break;
            case REQUESTCODE_APP://app提醒设置
                initAppItem(NotifyUtil.getAppNotifyModel(this));
                break;
            case REQUESTCODE_CALL://来电提醒设置
                initCallItem(NotifyUtil.getCallNotifyModel(this));
                break;
            case REQUESTCODE_FORTUNE:
                initCommonItem(mNotifyRemindFortuneWay, fortuneIco1, fortuneIco2, fortuneIco3, NotifyUtil.getFortuneNotifyModel(this));
                break;
            case REQUESTCODE_SEDENTARY:
                initCommonItem(mNotifyRemindSedentaryWay, sedentaryIco1, sedentaryIco2, sedentaryIco3, NotifyUtil.getSedentaryNotifyModel(this));
                break;
            case REQUESTCODE_PERIOD:
                initCommonItem(mNotifyRemindPeriodWay, periodIco1, periodIco2, periodIco3, NotifyUtil.getPeriodNotifyModel(this));
                break;
            case REQUESTCODE_WATER:
                initCommonItem(mNotifyRemindWaterWay, waterIco1, waterIco2, waterIco3, NotifyUtil.getWaterTimeNotifyModel(this));
                break;
            case REQUESTCODE_STEP:
                initCommonItem(mNotifyRemindStepWay, stepIco1, stepIco2, stepIco3, NotifyUtil.getStepNotifyModel(this));
                break;
            case REQUESTCODE_WISH:
                initCommonItem(mNotifyWishWay, wishIco1, wishIco2, wishIco3, NotifyUtil.getWishNotifyModel(this));
                break;
            case REQUESTCODE_TOTWOO:
                initCommonItem(mNotifyRemindTotwooWay, totwooIco1, totwooIco2, totwooIco3, NotifyUtil.getTotwooNotifyModel(this), true);
                break;
            case REQUESTCODE_GIFT:
                initCommonItem(mNotifyGiftWay, giftIco1, giftIco2, giftIco3, NotifyUtil.getGiftNotifyModel(this));
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @OnClick({R.id.notify_remind_totwoo_rl, R.id.notify_remind_gift_rl, R.id.call_notify_layout, R.id.fragment_user_call_layout,
            R.id.notify_remind_step_rl, R.id.notify_remind_fortune_rl, R.id.notify_guide_confirm_tv,
            R.id.notify_remind_period_rl, R.id.notify_remind_water_rl, R.id.notify_remind_sedentary_rl,
            R.id.notify_remind_app_rl, R.id.no_longer_tips_notify_guide_tv, R.id.notify_memo_rl, R.id.notify_wish_rl})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.notify_remind_totwoo_rl://心有灵犀
                startActivityForResult(new Intent(this, NotifyTotwooActivity.class), REQUESTCODE_TOTWOO);
                break;
            case R.id.notify_remind_gift_rl://情书
                startActivityForResult(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_GIFT), REQUESTCODE_GIFT);
                break;
            case R.id.call_notify_layout:
            case R.id.fragment_user_call_layout://来电
                startActivityForResult(new Intent(this, CallRemindSetActivity.class), REQUESTCODE_CALL);
                break;
            case R.id.notify_remind_step_rl://健步
                int height = ToTwooApplication.owner.getHeight();
                int weight = ToTwooApplication.owner.getWeight();
                if (height == 0 || weight == 0) {
//                    Intent intent = new Intent(this, UserInfoSettingActivity.class);
//                    intent.putExtra("height", ToTwooApplication.owner.getHeight() == 0 ? -1 : ToTwooApplication.owner.getHeight());
//                    intent.putExtra("weight", ToTwooApplication.owner.getWeight() == 0 ? -1 : ToTwooApplication.owner.getWeight());
//                    startActivity(intent);
                    startActivity(new Intent(this, InitInfoActivity.class).putExtra(InitInfoActivity.INIT_INFO, false));
                } else
                    startActivityForResult(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_STEP), REQUESTCODE_STEP);
                break;
            case R.id.notify_remind_fortune_rl://运势
                startActivityForResult(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_FORTUNE), REQUESTCODE_FORTUNE);
                break;
            case R.id.notify_remind_sedentary_rl://久坐
                startActivityForResult(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_SEDENTARY), REQUESTCODE_SEDENTARY);
                break;
            case R.id.notify_remind_period_rl://大姨妈
                startActivityForResult(new Intent(this, PeriodSettingActivity.class), REQUESTCODE_PERIOD);
//                startActivityForResult(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_PERIOD), REQUESTCODE_PERIOD);
                break;
            case R.id.notify_remind_water_rl://喝水
                startActivityForResult(new Intent(this, WaterTimeSettingActivity.class), REQUESTCODE_WATER);
//                startActivityForResult(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_WATER), REQUESTCODE_WATER);
                break;
            case R.id.notify_memo_rl:
                startActivity(new Intent(this, CustomNotifyListActivity.class));
                break;
            case R.id.notify_wish_rl:
                startActivityForResult(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_WISH), REQUESTCODE_WISH);
                break;
            case R.id.notify_remind_app_rl://app
                startActivityForResult(new Intent(this, AppNotificationsActivity.class), REQUESTCODE_APP);
                break;
//            case R.id.notify_menu_setting_iv://震动强度
//                startActivity(new Intent(this, VibrationIntensityActivity.class));
//                break;
        }
    }

    boolean isH5On = false;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void changeSedData(Sedentary sedentary) {
        if (this != null) {
            initCommonItem(mNotifyRemindSedentaryWay, sedentaryIco1, sedentaryIco2, sedentaryIco3, NotifyUtil.getSedentaryNotifyModel(this));
        }
    }

    @EventInject(eventType = S.E.E_STEP_NOTIFY_CHANGE, runThread = TaskType.UI)
    public void onStepNotify(EventData data) {
        initCommonItem(mNotifyRemindStepWay, stepIco1, stepIco2, stepIco3, NotifyUtil.getStepNotifyModel(this));
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
