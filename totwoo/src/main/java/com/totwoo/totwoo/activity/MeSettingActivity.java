package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.Selection;
import android.text.Spannable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.UriUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.bean.CheckIsPwdBean;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.bean.SafeTypeBean;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.BirthSettingView;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.HeightCenterView;
import com.totwoo.totwoo.widget.RoundImageView;
import com.totwoo.totwoo.widget.WeightCenterView;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * 用户设置，个人中心界面<br>
 * 提供用户信息修改， 应用设置
 *
 * <AUTHOR>
 * @date 2015-2015年7月10日
 */
public class MeSettingActivity extends BaseActivity implements
        OnClickListener, SubscriberListener {
    private final int SELECT_CITY = 100;
    private final int SET_PWD = 333;

    @BindView(R.id.setting_head_icon)
    RoundImageView mSettingHeadIcon;
    @BindView(R.id.me_phone_value_tv)
    TextView mMePhoneValueTv;

    @BindView(R.id.me_nickname_value_tv)
    TextView mMeNicknameValueTv;
    @BindView(R.id.me_gender_value_tv)
    TextView mMeGenderValueTv;
    @BindView(R.id.me_age_value_tv)
    TextView mMeAgeValueTv;
    @BindView(R.id.me_height_value_tv)
    TextView mMeHeightValueTv;
    @BindView(R.id.me_weight_value_tv)
    TextView mMeWeightValueTv;
    @BindView(R.id.me_city_value_tv)
    TextView mMeCityValueTv;
    @BindView(R.id.me_lave_state_value_tv)
    TextView mMeLaveStateValueTv;
    @BindView(R.id.me_password_value_tv)
    TextView mMePasswordValueTv;
    @BindView(R.id.setting_content_layout)
    LinearLayout mSettingContentLayout;

    CustomBottomDialog headIconDialog;

    private boolean hasPwd;


    int gender;
    private String nickName;

    /**
     * 标识是否有设置信息变化, 如果有, 需要同步服务器
     */
    private boolean settingChange;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_me_setting);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        initData();
        owner = Owner.getCurrOwner();
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.user_setting);

        getTopTitleView().setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                CommonUtils.shareXLog(MeSettingActivity.this);
                return true;
            }
        });
    }

    private void showHeadIcon() {
        RequestOptions options = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        Glide.with(this).load(BitmapHelper.checkRealPath(owner.getHeaderUrl())).apply(options).into(mSettingHeadIcon);
    }

    /**
     * 初始化数据
     */
    private void initData() {
        showHeadIcon();
        mSettingHeadIcon.setClickable(false);

        mMeNicknameValueTv.setText(owner.getNickName());
        mMePhoneValueTv.setText(owner.getPhone());
        if (TextUtils.isEmpty(owner.getBirthday()) || owner.getBirthday().startsWith("0000")) {
            owner.setBirthday(getString(R.string.please_select));
        }

        String birthday = owner.getBirthday();
        String[] birth = birthday.split("-");

        // 检查生日格式是否正确（应该有年-月-日三个部分）
        if (birth.length >= 3) {
            try {
                int month = Integer.parseInt(birth[1]);
                int day = Integer.parseInt(birth[2]);
                mMeAgeValueTv.setText(birthday + " " + Apputils.getAstro(this, month, day));
            } catch (NumberFormatException e) {
                // 如果解析失败，只显示生日字符串
                mMeAgeValueTv.setText(birthday);
            }
        } else {
            // 如果生日格式不正确，只显示生日字符串
            mMeAgeValueTv.setText(birthday);
        }
        switch (owner.getGender()) {
            case 0:
                mMeGenderValueTv.setText(R.string.male);
                break;
            case 1:
                mMeGenderValueTv.setText(R.string.female);
                break;
            case 2:
                mMeGenderValueTv.setText(R.string.other);
                break;
        }
        int height = owner.getHeight();
        int weight = owner.getWeight();
        mMeHeightValueTv.setText(height == 0 ? getString(R.string.please_select) : height + "cm");
        mMeWeightValueTv.setText(weight == 0 ? getString(R.string.please_select) : weight + "kg");
        mMeCityValueTv.setText(owner.getCity().isEmpty() ? getString(R.string.please_select) : owner.getCity());
        switch (owner.getLoveStatus()) {
            case 0:
                mMeLaveStateValueTv.setText(R.string.keep_secret);
                break;
            case 1:
                mMeLaveStateValueTv.setText(R.string.single);
                break;
            case 2:
                mMeLaveStateValueTv.setText(R.string.in_love);
                break;
            case 3:
                mMeLaveStateValueTv.setText(R.string.married);
                break;
        }

        refreshPwdState();

        hasPwd = PreferencesUtils.getBoolean(this, CommonArgs.PREF_HAS_PASSWORD, false);
        mMePasswordValueTv.setText(hasPwd ? "" : getString(R.string.not_set));
    }

    private void refreshPwdState() {
        HttpHelper.loginService.checkIsPwd(owner.getPhone())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<CheckIsPwdBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(HttpBaseBean<CheckIsPwdBean> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            hasPwd = objectHttpBaseBean.getData().getIs_pwd() == 1;
                            PreferencesUtils.put(MeSettingActivity.this, CommonArgs.PREF_HAS_PASSWORD, hasPwd);
                            mMePasswordValueTv.setText(hasPwd ? "" : getString(R.string.not_set));
                        }
                    }
                });
    }

    private void showHeadIconDialog() {
        if (headIconDialog == null) {
            headIconDialog = new CustomBottomDialog(this);
        }
        headIconDialog.setTitle(R.string.modify_head);
        LinearLayout picLayout = (LinearLayout) View.inflate(this, R.layout.dialog_inside_pic, null);

        headIconDialog.setMainView(picLayout);
        TextView mAlbum = picLayout.findViewById(R.id.pic_album_tv);
        TextView mCamera = picLayout.findViewById(R.id.pic_camera_tv);
        mAlbum.setOnClickListener(v -> {
            PictureSelectUtil.with(this).gallery().crop().shapeOval(true).setCallback(uri -> {
                receivePhoto(uri);
                headIconDialog.dismiss();
            }).select();
        });
        // 相机tv监听点击开启拍照app
        mCamera.setOnClickListener(v -> {
            PictureSelectUtil.with(this).camera().crop().shapeOval(true).setCallback(uri -> {
                receivePhoto(uri);
                headIconDialog.dismiss();
            }).select();
        });
        headIconDialog.show();
    }

    //增加图片压缩功能
    private void receivePhoto(Uri uri) {
        int maxWidth = ConvertUtils.dp2px(83);
        Bitmap bitmap = ImageUtils.compressBySampleSize(ImageUtils.getBitmap(UriUtils.uri2File(uri)),
                maxWidth, maxWidth, true);
        Glide.with(this).load(bitmap).into(mSettingHeadIcon);
        postUserHeadPortrait(ConvertUtils.bitmap2Bytes(bitmap));
    }

    CustomBottomDialog nameDialog;

    private void showNameDialog() {
        if (nameDialog == null) {
            nameDialog = new CustomBottomDialog(this);
        }
        mHandler.postDelayed(() -> {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

            inputMethodManager.toggleSoftInput(0,
                    InputMethodManager.HIDE_NOT_ALWAYS);
        }, 100);
        nameDialog.setTitle(R.string.modify_nick_name);
        LinearLayout nameLayout = (LinearLayout) View.inflate(
                this, R.layout.dialog_inside_name, null);

        nameDialog.setMainView(nameLayout);

        EditText mNameEt = nameLayout.findViewById(R.id.name_et);
        ImageView mClearIv = nameLayout.findViewById(R.id.name_clear_iv);

        mNameEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                LogUtils.i("edit", s.length() + "Changed");
                if (s.length() > 20) {
                    s = s.subSequence(0, 20);
                    mNameEt.setText(s);
                    mNameEt.setSelection(20);
                    if (Apputils.systemLanguageIsChinese(getApplicationContext())) {
                        ToastUtils.showShort(MeSettingActivity.this, "最多只能20个汉字");
                    } else {
                        ToastUtils.showShort(MeSettingActivity.this, "20 characters");
                    }
                }

                LogUtils.i("edit", "after");
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                LogUtils.i("edit", count + "before");
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        mClearIv.setOnClickListener(v -> {
            int index = mNameEt.getSelectionStart();
            try {
                mNameEt.getEditableText().delete(0, index);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        mNameEt.setText(owner.getNickName());
        // 设置光标到文本末尾
        CharSequence text = mNameEt.getText();
        if (text != null) {
            Spannable spanText = (Spannable) text;
            Selection.setSelection(spanText, text.length());
        }
        nameDialog.setSaveClick(v -> {
            nickName = mNameEt.getText().toString().trim();
            if (nickName.isEmpty()) {
                ToastUtils.show(MeSettingActivity.this, R.string.please_edit_nickname, 2000);
                return;
            }
            owner.setNickName(nickName);
            mMeNicknameValueTv.setText(nickName);

            synchronousUserInfo();
            nameDialog.dismiss();
        });
        nameDialog.show();
    }

    CustomBottomDialog genderDialog;

    private void showGenderDialog() {
        genderDialog = new CustomBottomDialog(this);
        genderDialog.setTitle(R.string.gender);

        ConstraintLayout genderLayout = (ConstraintLayout) View.inflate(this, R.layout.dialog_inside_gender, null);
        genderDialog.setMainView(genderLayout);
        mFemaleSelectTv = genderLayout.findViewById(R.id.gender_female_tv);
        mMaleSelectTv = genderLayout.findViewById(R.id.gender_male_tv);
        mOtherSelectTv = genderLayout.findViewById(R.id.gender_other_tv);

        gender = owner.getGender();
        notifyGenderChanged();

        OnClickListener femaleClick = v -> {
            if (gender != 1) {
                gender = 1;
                notifyGenderChanged();
            }
        };
        OnClickListener maleClick = v -> {
            if (gender != 0) {
                gender = 0;
                notifyGenderChanged();
            }
        };

        OnClickListener otherClick = v -> {
            if (gender != 2) {
                gender = 2;
                notifyGenderChanged();
            }
        };

        mFemaleSelectTv.setOnClickListener(femaleClick);
        mMaleSelectTv.setOnClickListener(maleClick);
        mOtherSelectTv.setOnClickListener(otherClick);

        genderDialog.setSaveClick(v -> {
            switch (gender) {
                case 0:
                    mMeGenderValueTv.setText(R.string.male);
                    owner.setGender(0);
                    genderDialog.dismiss();
                    break;
                case 1:
                    mMeGenderValueTv.setText(R.string.female);
                    owner.setGender(1);
                    genderDialog.dismiss();
                    break;
                case 2:
                    mMeGenderValueTv.setText(R.string.other);
                    owner.setGender(2);
                    genderDialog.dismiss();
                    break;
            }
            settingChange = true;
            synchronousUserInfo();
            EventBus.getDefault().post(HomeBaseActivity.ACTION_USER_GENDEN_CHANGE);
            showHeadIcon();
        });
        genderDialog.show();
    }

    TextView mFemaleSelectTv;
    TextView mMaleSelectTv;
    TextView mOtherSelectTv;

    private void notifyGenderChanged() {
        if (gender == 0) {
            mFemaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mMaleSelectTv.setBackgroundResource(R.drawable.shape_sex_selected_bg);
            mOtherSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
        } else if (gender == 1) {
            mFemaleSelectTv.setBackgroundResource(R.drawable.shape_sex_selected_bg);
            mMaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mOtherSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
        } else {
            mFemaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mMaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mOtherSelectTv.setBackgroundResource(R.drawable.shape_sex_selected_bg);
        }
    }

    CustomBottomDialog birthdayDialog;

    private void showBirthdayDialog() {
        birthdayDialog = new CustomBottomDialog(this);
        birthdayDialog.setTitle(R.string.birthday);
        LinearLayout birthdayLayout = (LinearLayout) View.inflate(this, R.layout.dialog_inside_birthday, null);
        BirthSettingView brightView = birthdayLayout.findViewById(R.id.birthday_view);
        birthdayDialog.setMainView(birthdayLayout);
        birthdayDialog.setSaveClick(v -> {
            owner.setBirthday(brightView.getN_year()
                    + "-" + CommonUtils.getZeroStart(brightView.getN_month()) + "-"
                    + CommonUtils.getZeroStart(brightView.getN_day()));
            String[] birth = owner.getBirthday().split("-");
            int month = Integer.parseInt(birth[1]);
            int day = Integer.parseInt(birth[2]);
            mMeAgeValueTv.setText(owner.getBirthday() + " " + Apputils.getAstro(MeSettingActivity.this, month, day));
            birthdayDialog.dismiss();
            synchronousUserInfo();
            settingChange = true;
        });
        birthdayDialog.show();
    }

    CustomBottomDialog heightDialog;

    private void showHeightDialog() {
        heightDialog = new CustomBottomDialog(this);
        heightDialog.setTitle(R.string.height);
        LinearLayout heightLayout = (LinearLayout) View.inflate(this, R.layout.dialog_inside_height, null);
        HeightCenterView heightCenterView = heightLayout.findViewById(R.id.height_view);
        heightDialog.setMainView(heightLayout);
        heightDialog.setSaveClick(v -> {
            owner.setHeight(heightCenterView.getN_height());
            mMeHeightValueTv.setText(heightCenterView.getN_height() + "cm");
//            heightSettingView.onSaved();
            heightDialog.dismiss();
            synchronousUserInfo();
            settingChange = true;

        });
        heightDialog.show();
    }

    CustomBottomDialog weightDialog;

    private void showWeightDialog() {
        weightDialog = new CustomBottomDialog(this);
        weightDialog.setTitle(R.string.weight);

        LinearLayout weightLayout = (LinearLayout) View.inflate(this, R.layout.dialog_inside_weight, null);
        WeightCenterView weightCenterView = weightLayout.findViewById(R.id.weight_view);
        weightDialog.setMainView(weightLayout);
        weightDialog.setSaveClick(v -> {
            owner.setWeight(weightCenterView.getN_weight());
            mMeWeightValueTv.setText(weightCenterView.getN_weight() + "kg");
            weightDialog.dismiss();
            settingChange = true;
            synchronousUserInfo();
        });

        weightDialog.show();
    }

    CustomBottomDialog marriageDialog;
    TextView mSingleTv;
    TextView mInLoveTv;
    TextView mMarriedTv;
    TextView mSecretTv;

    private void showMarriageDialog() {
        marriageDialog = new CustomBottomDialog(this);
        marriageDialog.setTitle(R.string.marital_status);
        LinearLayout marriageLayout = (LinearLayout) View.inflate(this, R.layout.dialog_inside_marriage, null);
        marriageDialog.setMainView(marriageLayout);

        mSingleTv = marriageLayout.findViewById(R.id.marriage_single);
        mInLoveTv = marriageLayout.findViewById(R.id.marriage_in_love);
        mMarriedTv = marriageLayout.findViewById(R.id.marriage_married);
        mSecretTv = marriageLayout.findViewById(R.id.marriage_keep_secret);
        notifyLoveStatus(owner.getLoveStatus());
        OnClickListener marital_dialog_click = v -> {
            switch (v.getId()) {
                case R.id.marriage_single:
                    owner.setLoveStatus(1);
                    break;
                case R.id.marriage_in_love:
                    owner.setLoveStatus(2);
                    break;
                case R.id.marriage_married:
                    owner.setLoveStatus(3);
                    break;
                case R.id.marriage_keep_secret:
                    owner.setLoveStatus(0);
                    break;
            }
            notifyLoveStatus(owner.getLoveStatus());
            marriageDialog.dismiss();
            mMeLaveStateValueTv.setText(((TextView) v).getText());
            settingChange = true;
            synchronousUserInfo();
        };
        mSingleTv.setOnClickListener(marital_dialog_click);
        mInLoveTv.setOnClickListener(marital_dialog_click);
        mMarriedTv.setOnClickListener(marital_dialog_click);
        mSecretTv.setOnClickListener(marital_dialog_click);

        marriageDialog.show();
    }

    private void notifyLoveStatus(int status) {
        mSingleTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
        mSingleTv.setBackground(getResources().getDrawable(R.drawable.shape_sleep_top_unselect_bg));
        mInLoveTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
        mInLoveTv.setBackground(getResources().getDrawable(R.drawable.shape_sleep_top_unselect_bg));
        mMarriedTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
        mMarriedTv.setBackground(getResources().getDrawable(R.drawable.shape_sleep_top_unselect_bg));
        mSecretTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
        mSecretTv.setBackground(getResources().getDrawable(R.drawable.shape_sleep_top_unselect_bg));
        switch (status) {
            case 1:
                mSingleTv.setTextColor(getResources().getColor(R.color.white));
                mSingleTv.setBackground(getResources().getDrawable(R.drawable.shape_me_married_status));
                break;
            case 2:
                mInLoveTv.setTextColor(getResources().getColor(R.color.white));
                mInLoveTv.setBackground(getResources().getDrawable(R.drawable.shape_me_married_status));
                break;
            case 3:
                mMarriedTv.setTextColor(getResources().getColor(R.color.white));
                mMarriedTv.setBackground(getResources().getDrawable(R.drawable.shape_me_married_status));
                break;
            case 0:
                mSecretTv.setTextColor(getResources().getColor(R.color.white));
                mSecretTv.setBackground(getResources().getDrawable(R.drawable.shape_me_married_status));
                break;
        }
    }

    @OnClick({R.id.me_head_portrait_layout, R.id.me_nickname_layout,
            R.id.me_gender_layout, R.id.me_age_layout, R.id.me_height_layout,
            R.id.me_weight_layout, R.id.me_city_layout, R.id.me_love_state_layout,
            R.id.me_password_layout, R.id.me_user_logout_tv, R.id.me_un_register_layout})
    public void onClick(View view) {
        Intent intent;
        switch (view.getId()) {
            case R.id.me_head_portrait_layout:
                showHeadIconDialog();
                break;
            case R.id.me_nickname_layout:
                showNameDialog();
                break;
            case R.id.me_gender_layout:
                showGenderDialog();
                break;
            case R.id.me_age_layout:
                showBirthdayDialog();
                break;
            case R.id.me_height_layout:
                showHeightDialog();
                break;
            case R.id.me_weight_layout:
                showWeightDialog();
                break;
            case R.id.me_city_layout:
                intent = new Intent(this, SelectCityActivity.class);
                startActivityForResult(intent, SELECT_CITY);
                break;
            case R.id.me_love_state_layout:
                showMarriageDialog();
                break;
            case R.id.me_password_layout:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MINTE_USERSET_PWDLOFIN);
                Intent passwordIntent = new Intent();
                if (!hasPwd) {
                    passwordIntent.setClass(MeSettingActivity.this, SetPasswordActivity.class);
                } else {
                    passwordIntent.setClass(MeSettingActivity.this, ModifyPasswordActivity.class);
                }
                startActivity(passwordIntent);
                break;
            case R.id.me_user_logout_tv:
                if (!NetUtils.isConnected(this)) {
                    ToastUtils.showShort(this, R.string.error_net);
                    return;
                }
                if (!BleParams.isSecurityJewlery()) {
                    showLogoutDialog();
                } else {
                    checkSecurityStatus();
                }
                break;
            case R.id.me_un_register_layout:
                showUnRegisterDialog();
                break;
        }
    }

    private void checkSecurityStatus() {
        HttpHelper.safeService.getSafeState(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SafeTypeBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(MeSettingActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<SafeTypeBean> safeTypeBeanHttpBaseBean) {
                        if (safeTypeBeanHttpBaseBean.getErrorCode() == 0) {
                            if (TextUtils.equals(safeTypeBeanHttpBaseBean.getData().getType(), "N")) {
                                showLogoutDialog();
                            } else if (TextUtils.equals(safeTypeBeanHttpBaseBean.getData().getType(), "Y")) {
                                showLogoutDialog();
                            } else if (TextUtils.equals(safeTypeBeanHttpBaseBean.getData().getType(), "GUARD")) {
                                showCancelGuardDialog();
                            } else if (TextUtils.equals(safeTypeBeanHttpBaseBean.getData().getType(), "HELP")) {
                                showCancelEmergencyDialog();
                            }
                        }
                    }
                });
    }

    private void showCancelEmergencyDialog() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(this);
        commonMiddleDialog.setMessage(R.string.jewelry_logout_emergency_hint);
        commonMiddleDialog.setSure(R.string.jewelry_list_unbind_confirm, v -> {
            HttpHelper.safeService.cancelEmergency(2001)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(MeSettingActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                            httplogout();
                            commonMiddleDialog.dismiss();
                        }
                    });
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    private void showCancelGuardDialog() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(this);
        commonMiddleDialog.setMessage(R.string.jewelry_logout_guard_hint);
        commonMiddleDialog.setSure(R.string.jewelry_list_unbind_confirm, v -> {
            HttpHelper.safeService.cancelGuard(2001)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(MeSettingActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                            httplogout();
                            commonMiddleDialog.dismiss();
                        }
                    });
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    private void showLogoutDialog() {
        showLogoutDialog(false);
    }

    private void showLogoutDialog(boolean force) {
        if (!hasPwd && !force) {
            CommonMiddleDialog pwdDialog = new CommonMiddleDialog(this);
            pwdDialog.setTitle(R.string.not_set_dialog_title);
            pwdDialog.setMessage(R.string.not_set_dialog_info);
            pwdDialog.setSure(R.string.go_setting, v -> {
                startActivityForResult(new Intent(this, SetPasswordActivity.class), SET_PWD);
                pwdDialog.dismiss();
            });
            pwdDialog.setCancel(R.string.continue_logout, v -> {
                showLogoutDialog(true);
                pwdDialog.dismiss();
            });
            pwdDialog.show();
            return;
        }

        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
        dialog.setTitle(R.string.exit_sign1);
        dialog.setCancel(R.string.cancel);
        if (LocalJewelryDBHelper.getInstance().hasSecurityJewelry()) {
            dialog.setInfo(R.string.safe_exi_sign_dialog_message);
        } else if (BleParams.isNfcJewelry(null)) {
            dialog.setInfo(R.string.nfc_logout_info);
        } else {
            dialog.setInfo(R.string.exi_sign_dialog_message);
        }
        dialog.setSure(R.string.confirm, v -> {
            httplogout();
            dialog.dismiss();
        });
        dialog.show();

        // 提前同步用户信息
        synchronousUserInfo(false);
    }

    private void showUnRegisterDialog() {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
        dialog.setTitle(R.string.important_prompt);
        dialog.setCancel(R.string.confirm_un_register, v -> {
            httpUnRegister();
            dialog.dismiss();
        });
        if (LocalJewelryDBHelper.getInstance().hasSecurityJewelry()) {
            dialog.setInfo(R.string.un_register_info);
        } else {
            dialog.setInfo(R.string.un_register_info);
        }
        dialog.setSure(R.string.cancel_un_register, v -> {
            dialog.dismiss();
        });
        dialog.show();
    }

    private boolean isUnRegister;

    private void httpUnRegister() {
        HttpHelper.commonServiceV2.unRegister(ToTwooApplication.owner.getPairedId())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(MeSettingActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            isLogOut = true;
                            isUnRegister = true;
                            CommonUtils.clearUserData(true);
                            PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI);
                            LocalJewelryDBHelper.getInstance().deleteAllBean();
                            ToastUtils.showShort(MeSettingActivity.this, R.string.un_register_success);


                            // 清理自动登录数据
                            PreferencesUtils.remove(MeSettingActivity.this, CommonArgs.PREF_LAST_ENCODE_PASSWORD);
                            PreferencesUtils.remove(MeSettingActivity.this, CommonArgs.PREF_LAST_HEAD_ICON);
                            PreferencesUtils.remove(MeSettingActivity.this, CommonArgs.PREF_LAST_PHONE);
                            PreferencesUtils.remove(MeSettingActivity.this, CommonArgs.PREF_LAST_USERNAME);
                            PreferencesUtils.remove(MeSettingActivity.this, CommonArgs.PREF_LAST_GENDER);
                        }
                    }
                });
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    private boolean isLogOut = false;

    /**
     * 退出登录接口
     */
    private void httplogout() {
        showProgressDialog();
        launchRequestWithFlexibleError(HttpHelper.commonService.bindState("", "", "", "quit"),
                data -> {
                    launchRequestWithFlexibleError(
                            HttpHelper.loginService.logout(2001),
                            logoutData -> {
                                dismissProgressDialog();
                                isLogOut = true;
                                CommonUtils.clearUserData(false);
                                PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI);
                                LocalJewelryDBHelper.getInstance().deleteAllBean();
                            },
                            fail -> {
                                dismissProgressDialog();
                                return false;
                            },
                            false
                    );

                }

                , fail -> {
                    dismissProgressDialog();
                    return false;
                },
                false

        );
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_APART, runThread = TaskType.UI)
    public void updateJewerlyState(EventData data) {
        if (!isLogOut)
            return;

        if (!isUnRegister) {
            ToastUtils.showShort(MeSettingActivity.this, R.string.Logout_Success);
        }
        sendCloseActivitiesBR(false);
        HomeActivityControl.getInstance().openLoginActivity(this);
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case SELECT_CITY:// 城市选择返回值
                String city = null;
                switch (resultCode) {
                    case SelectCityActivity.CITY:
                        city = data.getStringExtra("country") + " "
                                + data.getStringExtra("province") + " "
                                + data.getStringExtra("city");
                        break;
                    case SelectCityActivity.PROVINCE:
                        city = data.getStringExtra("country") + " "
                                + data.getStringExtra("province");
                        break;
                    case SelectCityActivity.COUNTRY:
                        city = data.getStringExtra("country");
                        break;
                    case SelectCityActivity.POSITION:
                        city = data.getStringExtra("position");
                        break;
                }
                if (city != null) {
                    owner.setCity(city);
                    mMeCityValueTv.setText(city);
                }
                break;
            case SET_PWD:
                if (resultCode == RESULT_OK) {
                    hasPwd = true;
                    mMePasswordValueTv.setText("");
                }
        }
    }

    // 把bitmap转成file上传服务器
    public void postUserHeadPortrait(byte[] fileBytes) {
        // 上传图片
        HttpHelper.card.getQiNiuToken(1, "headimg").subscribeOn(Schedulers.io()).subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {
                ToastUtils.showLong(MeSettingActivity.this, R.string.upload_filed);
            }

            @Override
            public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
//                byte[] fileBytes = CommonUtils.getFileBytesFromUri(MeSettingActivity.this, bitmap);
                if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0 && fileBytes != null) {
                    RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), fileBytes);
                    MultipartBody.Part part = MultipartBody.Part.createFormData("file", "head_icon", requestFile);
                    HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Subscriber<QiNiuResponse>() {
                                @Override
                                public void onCompleted() {

                                }

                                @Override
                                public void onError(Throwable e) {
                                    ToastUtils.showLong(MeSettingActivity.this, R.string.upload_filed);
                                }

                                @Override
                                public void onNext(QiNiuResponse qiNiuResponse) {
                                    // 上传成功
                                    owner.setHeaderUrl(qiNiuResponse.getKey());
                                    settingChange = true;
                                    synchronousUserInfo(true, true);
                                    ToastUtils.showLong(MeSettingActivity.this, R.string.upload_success);
                                    LogUtils.i("upload image success!" + qiNiuResponse.getKey());
                                }
                            });
                }
            }
        });
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (settingChange) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ORDER_UPDATE, null);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    private void synchronousUserInfo() {
        synchronousUserInfo(true, false);
    }

    private void synchronousUserInfo(boolean manual) {
        synchronousUserInfo(manual, false);
    }

    /**
     * 同步本地用户信息到服务器
     */
    private void synchronousUserInfo(boolean manual, boolean modifyHead) {
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("sex", owner.getGender());
        String love = null;
        switch (owner.getLoveStatus()) {
            case 0:
                love = "SEC";
                break;
            case 1:
                love = "S";
                break;
            case 2:
                love = "F";
                break;
            case 3:
                love = "M";
                break;
        }

        params.addFormDataPart("love_status", love);
        if (!owner.getCity().equals("")) {
            params.addFormDataPart("city", owner.getCity());
        }
        params.addFormDataPart("height", owner.getHeight() + "");
        params.addFormDataPart("weight", owner.getWeight() + "");
        params.addFormDataPart("birthday", owner.getBirthday());
        if (modifyHead) {
            params.addFormDataPart("head_portrait", owner.getHeaderUrl());
        }
        params.addFormDataPart("nick_name", owner.getNickName());

        HttpRequest.post(HttpHelper.URL_UPDATE_USER_INFO, params,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
                        if (manual) {
                            ToastUtils.showLong(MeSettingActivity.this, R.string.modify_success);
                        }

                        // 更新缓存用户信息
                        PreferencesUtils.put(MeSettingActivity.this, CommonArgs.PREF_LAST_HEAD_ICON, owner.getHeaderUrl());
                        PreferencesUtils.put(MeSettingActivity.this, CommonArgs.PREF_LAST_PHONE, owner.getPhone());
                        PreferencesUtils.put(MeSettingActivity.this, CommonArgs.PREF_LAST_USERNAME, owner.getNickName());
                        PreferencesUtils.put(MeSettingActivity.this, CommonArgs.PREF_LAST_GENDER, owner.getGender());

                        try {
                            String head = new JSONObject(s).optString("head_portrait");
                            if (!TextUtils.isEmpty("head") && !head.equals(owner.getHeaderUrl())) {
                                owner.setHeaderUrl(head);
                                showHeadIcon();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }

                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {
                        switch (t.getErrorCode()) {
                            case 801:
                                ToastUtils.show(MeSettingActivity.this,
                                        R.string.nick_name_there, 2000);
                                break;
                        }
                    }
                });
    }
}
