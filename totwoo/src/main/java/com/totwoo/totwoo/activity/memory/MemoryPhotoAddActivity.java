package com.totwoo.totwoo.activity.memory;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.component.http.HttpParams;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.StringUtils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.newConrtoller.UpdatePictureController;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.AdjustGridView;
import com.totwoo.totwoo.widget.LoadingDialog;
import com.totwoo.totwoo.widget.SceneAnimation;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashSet;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/3.
 */

public class MemoryPhotoAddActivity extends BaseActivity implements SubscriberListener, View.OnClickListener, AdapterView.OnItemClickListener
{
    @BindView(R.id.memory_photo_add_gv)
    public AdjustGridView gv;

    @BindView (R.id.memory_photo_add_edit)
    public EditText editText;

    @BindView (R.id.memory_photo_add_save)
    public TextView save;

    @BindView (R.id.memory_photo_add_edit_num)
    public TextView num;

    @BindView (R.id.memory_save_gif)
    ImageView saveGif;

    @BindView (R.id.memory_save_bg)
    View saveBg;

    private MemoryPhotoAddAdapter adapter;

    private ArrayList<String> urls;

    private LinkedHashSet<String> selectedPhotoPath;
    private String content;

    private Dialog dialog;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_photo_add);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        initData();
        initGridView();
        initListener();
    }

    private void initData()
    {
        Intent i = this.getIntent();
        this.content = i.getStringExtra(S.M.M_CONTENT);
        this.selectedPhotoPath = (LinkedHashSet<String>) i.getSerializableExtra(S.M.M_SELECTED);

        editText.setText(content);
        if (content != null)
            num.setText(content.length() + "/100");
    }

    private void initListener()
    {
        save.setOnClickListener(this);
        editText.addTextChangedListener(new TextWatcher()
        {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after)
            {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count)
            {

            }

            @Override
            public void afterTextChanged(Editable s)
            {
                String content = editText.getText().toString();
                num.setText(content.length() + "/100");
            }
        });
    }

    private void initGridView()
    {
        ArrayList<String> list = new ArrayList<>();

        Iterator<String> iterator = selectedPhotoPath.iterator();
        for (int i=0; i<selectedPhotoPath.size(); i++)
            list.add(iterator.next());
        adapter = new MemoryPhotoAddAdapter(this, list);
        gv.setAdapter(adapter);
        gv.setOnItemClickListener(this);
        adapter.checkSelectNewPhoto();
        adapter.notifyDataSetChanged();
    }

    @Override
    protected void initTopBar()
    {
        setTopbarBackground(R.color.layer_bg_white);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.memory_photo_title);
    }

    @Override
    public void onClick(View v)
    {
        switch (v.getId())
        {
            case R.id.memory_photo_add_save:
                uploadResources();
                break;
        }
    }

    private void uploadResources()
    {
        if (adapter.resource.size() == 1)
        {
            ToastUtils.showShort(this, R.string.memory_photo_add_one);
            return;
        }

        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED)
        {
            ToastUtils.showShort(this, R.string.memory_connect_not);
            return;
        }

        dialog = new LoadingDialog(this, getString(R.string.memory_store_in));
        dialog.show();

        urls = new ArrayList<>();
        //UpdatePictureController.getInstance().uploadMemoryRes(this, adapter.getItem(urls.size()), 1, "memory");
        UpdatePictureController.getInstance().uploadMemoryPic(this, adapter.getItem(urls.size()));
        save.setClickable(false);
    }

    @EventInject(eventType = S.E.E_MEMORY_RESOURCE_UPLOAD_SUCCESSED, runThread = TaskType.UI)
    public void onUploadResourceSuccessed(EventData data)
    {
        HttpParams hp = (HttpParams) data;
        String url = (String) hp.getUserDefine("url");
        urls.add(url);
        int count = adapter.resource.contains(MemoryPhotoAddAdapter.MEMORY_PHOTO_ADD) ? adapter.getCount() - 1 : 8;
        if (urls.size() < count)
            //UpdatePictureController.getInstance().uploadMemoryRes(this, adapter.getItem(urls.size()), 1, "memory");
            UpdatePictureController.getInstance().uploadMemoryPic(this, adapter.getItem(urls.size()));
        else
        {
            MemoryBean memoryBean = new MemoryBean();
            memoryBean.memory_type = MemoryBean.TYPE_IMG;
            memoryBean.content = editText.getText().toString();
            String[] res = new String[urls.size()];
            for (int i=0; i<res.length; i++)
                res[i] = urls.get(i);
            memoryBean.img_url = res;
            MemoryController.getInstance().save(memoryBean);
        }
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_SUCCESSED, runThread = TaskType.UI)
    public void onSaveSuccessed(EventData data)
    {
        if (dialog != null)
            dialog.dismiss();

        HttpValues hv = (HttpValues) data;
        MemoryBean mb = (MemoryBean) hv.getUserDefine(S.M.M_IMAGES);
        showSuccessAnim(this, mb);
    }

    private void showSuccessAnim(final Activity activity, final MemoryBean mb)
    {
        saveBg.setVisibility(View.VISIBLE);
        saveGif.setVisibility(View.VISIBLE);
        /*AnimationDrawable ad = ((AnimationDrawable)saveGif.getDrawable());
        ad.setOneShot(true);
        ad.start();*/
        new SceneAnimation(saveGif, MemoryListActivity.successGif, 20, true);
        BluetoothManage.getInstance().notifyJewelry(6, 0x0000ff);
        mHandler.postDelayed(new Runnable()
        {
            @Override
            public void run()
            {
                Intent intent = new Intent(MemoryPhotoAddActivity.this, MemoryPageActivity.class);
                intent.putExtra(MemoryPageActivity.IS_OPENED, true);
                MemoryPhotoAddActivity.this.startActivity(intent);
                /*intent = new Intent(MemoryPhotoAddActivity.this, MemoryPhotoShowActivity.class);
                intent.putExtra(S.M.M_IMAGES, mb);
                MemoryPhotoAddActivity.this.startActivity(intent);*/
                activity.finish();
            }
        }, 80*40);
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_FAILED, runThread = TaskType.UI)
    public void onSaveFailed(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (StringUtils.isEmpty(hv.errorMesg))
            hv.errorMesg = getString(R.string.error_net);
        ToastUtils.showShort(this, hv.errorMesg);
        save.setClickable(true);
        if (dialog != null)
            dialog.dismiss();
    }

    @EventInject(eventType = S.E.E_MEMORY_RESOURCE_UPLOAD_FAILED, runThread = TaskType.UI)
    public void onUploadResourceFailed(EventData data)
    {
        LogUtils.e("上传失败la");
        //ToastUtils.showShort(this, R.string.error_net);
        save.setClickable(true);
        if (dialog != null)
            dialog.dismiss();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        if (dialog != null)
            dialog.dismiss();
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id)
    {
        String path = adapter.getItem(position);
        if (path.equals(MemoryPhotoAddAdapter.MEMORY_PHOTO_ADD))
        {
            Intent intent = new Intent(this, MemoryPhotoSelectActivity.class);
            ArrayList<String> res = adapter.resource;

            int count = res.size() - 1;
            selectedPhotoPath.clear();
            for (int i=0; i<count; i++)
                selectedPhotoPath.add(res.get(i));
            String edt = editText.getText().toString();
            intent.putExtra(S.M.M_SELECTED, selectedPhotoPath);
            intent.putExtra(S.M.M_CONTENT, edt);
            intent.putExtra(S.M.M_ADD, true);
            startActivity(intent);
            this.finish();
        }
        else
        {
            Intent intent = new Intent(this, MemoryPhotoShowActivity.class);
            MemoryBean mb = new MemoryBean();
            mb.img_url = new String[]{adapter.resource.get(position)};
            intent.putExtra(S.M.M_SINGLE, true);
            intent.putExtra(S.M.M_IMAGES, mb);
            startActivity(intent);
        }
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
    }
}
