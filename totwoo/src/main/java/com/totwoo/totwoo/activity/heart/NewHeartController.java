package com.totwoo.totwoo.activity.heart;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.base.BaseContraller;
import com.etone.framework.component.http.HttpUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.JSONUtils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.utils.HttpHelper;

import java.util.ArrayList;


/**
 * Created by xinyoulingxi on 2017/5/26.
 */

public class NewHeartController extends BaseContraller
{
    private static final NewHeartController instance = new NewHeartController();
    private NewHeartController()
    {
        super();
    }

    public static final NewHeartController getInstance()
    {
        return instance;
    }

    public void getHeartHomeData()
    {
        HttpValues hv = new HttpValues(S.H.H_HEART_GET_HOME_DATA, HttpHelper.URL_TOTWOO_HOME);
        hv.addParams("perpage", "20");
        hv.addParams("page", (1) + "");
        hv.addParams("talkId", ToTwooApplication.owner.getPairedId());

        hv.putUserDefine("isRefresh", true);
        HttpUtils.run(hv);
    }

    public void getHeartHomeDataV3()
    {
        HttpValues hv = new HttpValues(S.H.H_HEART_GET_HOME_DATA_V3, HttpHelper.URL_TOTWOO_HOME_V3);
        hv.addParams("talk_id", ToTwooApplication.owner.getPairedId());
        HttpUtils.run(hv);
    }

    @EventInject(eventType = S.H.H_HEART_GET_HOME_DATA_V3, runThread = TaskType.Async)
    public void onGetHeartHomeDataV3(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk())
        {
            String content = JSONUtils.getString(hv.content, "data", "");
            LogUtils.e("contentV3:" + content);
            NewHeartHomeBean hhb = new NewHeartHomeBean(content);
            String tmp = JSONUtils.getString(content, "totwoo", "");
            String[] res = JSONUtils.getStringArray(tmp, "list", new String[]{});
            ArrayList<HeartListBean> totwooList = makeHeartList(res);
            hv.putUserDefine ("hhb", hhb);
            hv.putUserDefine("isRefresh", true);
            hv.putUserDefine("listCount", res.length);
            hv.putUserDefine ("totwooList", totwooList);
            EventBus.onPostReceived(S.E.E_HEART_GET_HOME_DATA_SUCCESSED, hv);
        }
        else
        {
            EventBus.onPostReceived(S.E.E_HEART_GET_HOME_DATA_FAILED, data);
        }
    }

    @EventInject(eventType = S.H.H_HEART_GET_HOME_DATA, runThread = TaskType.Async)
    public void onGetHeartHomeDataFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk())
        {
            LogUtils.e("aab H_HEART_GET_HOME_DATA");
            String content = JSONUtils.getString(hv.content, "data", "");
            LogUtils.e("content:" + content);
            HeartHomeBean hhb = new HeartHomeBean(content);

            String tmp = JSONUtils.getString(content, "totwoo_data", "");
            LogUtils.e(tmp);
            String[] res = JSONUtils.getStringArray(tmp, "totwooList", null);
            String lastMsgId = "-1";
            if (res != null)
            {
                lastMsgId = JSONUtils.getString(res[res.length-1], "id", "-1");
            }
            String userInfo = JSONUtils.getString(tmp, "userinfo", "");
            LogUtils.e("userInfo:" + userInfo);
            hhb.otherNickName = JSONUtils.getString(userInfo, "nick_name", "");
            hhb.otherPhone = JSONUtils.getString(userInfo, "mobilephone", "");
            hhb.otherPortarit = JSONUtils.getString(userInfo, "head_portrait", "");
            int listCount = res == null ? 0 : res.length;
            ArrayList<HeartListBean> totwooList = makeHeartList(res);
            int heartCount = JSONUtils.getInt(tmp, "xinCount", 0);

            hhb.xinCount = heartCount;
            hhb.lastMsgId = lastMsgId;
            hv.putUserDefine ("hhb", hhb);
            hv.putUserDefine ("totwooList", totwooList);
            hv.putUserDefine ("listCount", listCount);

            EventBus.onPostReceived(S.E.E_HEART_GET_HOME_DATA_SUCCESSED, hv);
        }
        else
        {
            EventBus.onPostReceived(S.E.E_HEART_GET_HOME_DATA_FAILED, data);
        }
    }

    public void getHeartPageDataV2(int page, boolean isRefresh, String lastMsgId, boolean isHistory)
    {
        LogUtils.i("aab page = " + page);
        HttpValues hv = new HttpValues(S.H.H_HEART_GET_PAGE_DATA, HttpHelper.URL_TOTWOO_LIST_V2);
        hv.addParams("perpage", "10");
        hv.addParams("page", (page + 1) + "");
        hv.addParams("talkId", ToTwooApplication.owner.getPairedId());
        hv.addParams("lastMsgId", isRefresh ? "-1" : lastMsgId);

        hv.putUserDefine("isRefresh", isRefresh);
        hv.putUserDefine("isHistory", isHistory);
        HttpUtils.run(hv);
    }

//    public void getHeartPageData(int page, boolean isRefresh)
//    {
//        HttpValues hv = new HttpValues(S.H.H_HEART_GET_PAGE_DATA, HttpHelper.URL_TOTWOO_LIST);
//        hv.addParams("perpage", "10");
//        hv.addParams("page", (page + 1) + "");
//        hv.addParams("talkId", ToTwooApplication.owner.getPairedId());
//
//        hv.putUserDefine("isRefresh", isRefresh);
//        HttpUtils.run(hv);
//    }

    @EventInject (eventType = S.H.H_HEART_GET_PAGE_DATA, runThread = TaskType.Async)
    public void onGetHeartPageDataFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk())
        {
            LogUtils.e("content:" + hv.content);
            String content = JSONUtils.getString(hv.content, "data", "");
            String[] res = JSONUtils.getStringArray(content, "totwooList", null);
            String userInfo = JSONUtils.getString(content, "userinfo", null);
            String otherName = JSONUtils.getString(userInfo, "nick_name", null);
            String otherPhone = JSONUtils.getString(userInfo, "mobilephone", "");
            String otherPortrait = JSONUtils.getString(userInfo, "head_portrait", "");
            int listCount = res == null ? 0 : res.length;
            String lastMsgId = "-1";
            if (res != null && res.length > 0)
            {
                lastMsgId = JSONUtils.getString(res[res.length-1], "id", "-1");
            }
//            else
//                lastMsgId = hhb.lastMsgId;
            ArrayList<HeartListBean> totwooList = makeHeartList(res);
            int heartCount = JSONUtils.getInt(content, "xinCount", 0);
            NewHeartHomeBean hhb = new NewHeartHomeBean();

            hhb.consonance_count = heartCount;
            hhb.userinfos[1].nick_name = otherName;
            hhb.userinfos[1].totwoo_id = otherPhone;
            hhb.userinfos[1].head_portrait = otherPortrait;
            /*hhb.otherNickName = otherName;
            hhb.otherPhone = otherPhone;
            hhb.otherPortarit = otherPortrait;*/
            hhb.lastMsgId = lastMsgId;
            hv.putUserDefine("hhb", hhb);
            hv.putUserDefine("totwooList", totwooList);
            hv.putUserDefine ("listCount", listCount);
            hv.putUserDefine("lastMsgId", lastMsgId);

            EventBus.onPostReceived(S.E.E_HEART_GET_PAGE_DATA_SUCCESSED, hv);
        }
        else
        {
            EventBus.onPostReceived(S.E.E_HEART_GET_PAGE_DATA_FAILED, hv);
        }
    }

    private ArrayList<HeartListBean> makeHeartList(String[] json)
    {
        ArrayList<HeartListBean> list =  new ArrayList<>();
        if (json == null)
            return list;

        String pairedId = ToTwooApplication.owner.getPairedId();
        for (int i=0; i<json.length; i++)
        {
            HeartListBean bean = new HeartListBean(json[i], pairedId);
            list.add(bean);
        }

        list = teratListData (list);

        return list;
    }

    public ArrayList<HeartListBean> teratListData(ArrayList<HeartListBean> srcList)
    {
        ArrayList<HeartListBean> targetList =  new ArrayList<>();
        HeartListBean tmpBean = null;
        int tmpFlag = 0;
        for (int i=0; i<srcList.size(); i++)
        {
            HeartListBean bean = srcList.get(i);
            LogUtils.e("time:" + bean.createTime);
            if (bean.consonance == 0)
            {
                targetList.add(bean);
            }
            else
            {
                if (tmpFlag != bean.consonance)
                {
                    if (tmpBean != null)
                        targetList.add(tmpBean);

                    tmpFlag = bean.consonance;
                    tmpBean = bean;

                    //如果最后一条数据没有匹配上，那么按一条独立都数据处理
                    if (i == srcList.size() - 1)
                        targetList.add(bean);
                }
                else
                {
                    tmpFlag = 0;
                    tmpBean.otherTime = bean.createTime;
                    tmpBean.otherContent = bean.content;
                    targetList.add(tmpBean);
                    tmpBean = null;
                }
            }
        }

        return targetList;
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        super.onEventException(eventType, data, e);
        e.printStackTrace();
        if (eventType.equals(S.H.H_HEART_GET_PAGE_DATA))
            EventBus.onPostReceived(S.E.E_HEART_GET_PAGE_DATA_FAILED, data);
    }
}
