package com.totwoo.totwoo.activity.security;

import static com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity.IS_IMEI_SENT;
import static com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity.READ_CONTACT_HINT;

import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.etone.framework.event.EventBus;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.JewelryInfoActivity;
import com.totwoo.totwoo.activity.JewelryPairedListActivity;
import com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity;
import com.totwoo.totwoo.bean.BindStateHttpBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BleUtils;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.WeakReferenceHandler;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class ImeiInfoUpdateActivity extends BaseActivity implements BluetoothManage.ImeiGetSuccessListener {
    @BindView(R.id.imei_fail_cl)
    ConstraintLayout mFailCl;
    @BindView(R.id.imei_fail_iv)
    ImageView mImeiFailIv;
    @BindView(R.id.imei_fail_unpair_tv)
    TextView mImeiFailTv;
    @BindView(R.id.imei_fail_info_tv)
    TextView mFailInfoTv;
    @BindView(R.id.imei_info_lv)
    LottieAnimationView mLottie;
    @BindView(R.id.imei_retry_tv)
    TextView mRetryTv;

    private int count;
    private DisCountHandler handler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_imei_info_update);
        ButterKnife.bind(this);
        setSpinState(false);

        if (!BleUtils.isBlEEnable(this)) {
            showBluetoothDialog();
        }
        handler = new DisCountHandler(this);
        mLottie.setImageAssetsFolder("lottie_img/");
        mLottie.setAnimation("imei_loading.json");
        mLottie.setRepeatCount(LottieDrawable.INFINITE);
        mLottie.playAnimation();

        BluetoothManage.getInstance().setImeiGetSuccessListener(this);

        mHandler.postDelayed(() -> startGetImeiInfo(), 2000);
    }

    private void startGetImeiInfo() {
        String imei = PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, "");
        if (!TextUtils.isEmpty(imei)) {
            updateImei();
            return;
        }
        getImeiInfo();
        count = 0;
        handler.sendEmptyMessageDelayed(0, 5000);
    }

    private void updateImei() {
        if (!NetUtils.isConnected(this)) {
            LogUtils.e("aab no net");
            ToastUtils.showShort(this, R.string.error_net);
            updateFail(true);
            return;
        }
        LogUtils.e("aab has net");
        String name = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        String mac_address = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "");
        String imei = PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, "");
        HttpHelper.commonService.bindState(name, imei, mac_address, "connect")
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<BindStateHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        updateFail(true);
                    }

                    @Override
                    public void onNext(HttpBaseBean<BindStateHttpBean> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            PreferencesUtils.put(ImeiInfoUpdateActivity.this, IS_IMEI_SENT, true);
                            finishCurrent();
                        } else if (objectHttpBaseBean.getErrorCode() == 1001) {
//                            EventBus.onPostReceived(S.E.E_TOKEN_FAILED, null);
                            finishCurrent();
                        } else if (objectHttpBaseBean.getErrorCode() == 902) {
                            isOtherBind = true;
                            updateFail(true);
                            mFailInfoTv.setText(getString(R.string.safe_update_error_connect, objectHttpBaseBean.getData().getConnect_mobile()));

                            mRetryTv.setText(R.string.safe_update_error_connect_button);
                        } else {
                            updateFail(true);
                        }
                    }
                });
    }

    private boolean isOtherBind = false;

    @Override
    public void onImeiGetSuccess() {
        handler.removeCallbacksAndMessages(null);
        updateImei();
    }

    public class DisCountHandler extends WeakReferenceHandler<ImeiInfoUpdateActivity> {

        public DisCountHandler(ImeiInfoUpdateActivity imeiInfoUpdateActivity) {
            super(imeiInfoUpdateActivity);
        }

        @Override
        public void handleLiveMessage(Message msg) {
            count++;
            if (count > 5) {
                updateFail(false);
            } else {
                getImeiInfo();
                handler.sendEmptyMessageDelayed(0, 5000);
            }
        }
    }

    @OnClick({R.id.imei_fail_unpair_tv, R.id.imei_retry_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.imei_fail_unpair_tv:
//                CommonUtils.jumpToJewList(ImeiInfoUpdateActivity.this);
                startActivity(new Intent(ImeiInfoUpdateActivity.this, JewelryInfoActivity.class)
                        .putExtra(JewelryPairedListActivity.SELECTED_JEWELRY, PreferencesUtils.getString(ImeiInfoUpdateActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))
                        .putExtra(JewelryPairedListActivity.SELECTED_ADDRESS, PreferencesUtils.getString(ImeiInfoUpdateActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, "")));
                break;
            case R.id.imei_retry_tv:
                if (!isOtherBind) {
                    mFailCl.setVisibility(View.GONE);
                    mHandler.postDelayed(() -> startGetImeiInfo(), 2000);
                } else {
                    startActivity(new Intent(ImeiInfoUpdateActivity.this, JewelryInfoActivity.class)
                            .putExtra(JewelryPairedListActivity.SELECTED_JEWELRY, PreferencesUtils.getString(ImeiInfoUpdateActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))
                            .putExtra(JewelryPairedListActivity.SELECTED_ADDRESS, PreferencesUtils.getString(ImeiInfoUpdateActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, "")));
                }
                break;
        }
    }

    private void updateFail(boolean isNetFail) {
        mFailCl.setVisibility(View.VISIBLE);
        if (isNetFail) {
            mImeiFailIv.setImageResource(R.drawable.imei_net_fail);
            mImeiFailTv.setVisibility(View.VISIBLE);
            mFailInfoTv.setText(R.string.safe_update_net_fail);
        } else {
            mImeiFailIv.setImageResource(R.drawable.imei_info_fail);
            mImeiFailTv.setVisibility(View.VISIBLE);
            mFailInfoTv.setText(R.string.safe_update_info_fail);
        }
    }

    private void getImeiInfo() {
        BluetoothManage.getInstance().imeiMessage();
    }

    private void finishCurrent() {
        if (!PreferencesUtils.getBoolean(this, SecurityHomeActivity.HAS_READ_HINT, false) && Apputils.systemLanguageIsChinese(this)) {
            startActivity(new Intent(ImeiInfoUpdateActivity.this, SecurityHintActivity.class));
        }else if(!PreferencesUtils.getBoolean(this, READ_CONTACT_HINT, false)){
            startActivity(new Intent(ImeiInfoUpdateActivity.this, SecurityNewListActivity.class).putExtra(CommonArgs.FROM_TYPE,SecurityNewListActivity.INIT_STATUS));
        }else{
            EventBus.onPostReceived(S.E.E_IMEI_UPDATE_SUCCEED, null);
        }
        finish();
        overridePendingTransition(R.anim.activity_slide_in_bottom, R.anim.activity_slide_out_bottom);
    }

    @Override
    public void onBackPressed() {

    }

    /**
     * 展示请求蓝牙开启的对话框
     */
    public void showBluetoothDialog() {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
        dialog.setMessage(R.string.request_open_bluetooth);
        dialog.setSure(R.string.allow, v -> {
            BleUtils.enableBlueTooth(ImeiInfoUpdateActivity.this);
            dialog.dismiss();
        });
        dialog.setCancel(R.string.cancel);

        dialog.show();
    }
}
