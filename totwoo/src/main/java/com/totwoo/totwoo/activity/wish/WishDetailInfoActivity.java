package com.totwoo.totwoo.activity.wish;

import android.animation.Animator;
import android.graphics.BitmapFactory;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;
import com.etone.framework.event.EventBus;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.bean.WishInfoBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.DesUtil;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.CustomMiddleInfoDialog;
import com.totwoo.totwoo.widget.WishShareDialog;
import com.umeng.analytics.MobclickAgent;

import org.json.JSONArray;
import org.json.JSONException;

import java.io.IOException;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class WishDetailInfoActivity extends BaseActivity {
    private static final String HAS_SHOW_DIALOG = "has_show_dialog";
    public static final String WISH_BEAN = "wish_bean";
    public static final String SHOW_SHARE = "show_share";
    public static final String WISH_ID = "wish_id";
    public static final String HAS_COVER = "has_cover";
    @BindView(R.id.wish_detail_done_tv)
    TextView mWishDoneTv;
    @BindView(R.id.wish_detail_done_status)
    ImageView mWishDoneStatusIv;
    @BindView(R.id.wish_detail_done_time_tv)
    TextView mWishDoneTimeTv;
    @BindView(R.id.wish_detail_content_tv)
    TextView mContentTv;
    @BindView(R.id.wish_detail_like_count_tv)
    TextView mLikeCountTv;
    @BindView(R.id.wish_detail_main_iv)
    ImageView mMainIv;
    @BindView(R.id.wish_detail_control_iv)
    ImageView mControlIv;
    @BindView(R.id.wish_detail_main_cv)
    CardView mMaincv;
    @BindView(R.id.wish_detail_card_front_cover)
    RelativeLayout mFrontCover;
    @BindView(R.id.wish_detail_all_content)
    ConstraintLayout mAllContent;
    @BindView(R.id.wish_detail_cover_lv)
    LottieAnimationView mWishDetailCoverLv;
    @BindView(R.id.wish_detail_save_bg)
    View saveBg;
    @BindView(R.id.wish_done_lv)
    LottieAnimationView mWishDoneLv;


    private String wish_id;
    private WishInfoBean wishInfoBean;
    private MediaPlayer mPlayer;
    private CustomDialog shareDialog;
    private WishShareDialog wishShareDialog;
    private FacebookCallback<Sharer.Result> facebookCallback;
    private ShakeMonitor mShakeMonitor;
    private boolean isVoicePlaying = false;
    private boolean hasShowDialog = false;

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        outState.putBoolean(HAS_SHOW_DIALOG, hasShowDialog);
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            hasShowDialog = savedInstanceState.getBoolean(HAS_SHOW_DIALOG);
        }
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_detail_info);
        ButterKnife.bind(this);

        if (getIntent().getBooleanExtra(HAS_COVER, false)) {
            mFrontCover.setVisibility(View.VISIBLE);
            mAllContent.setVisibility(View.GONE);
            initJewelryShake();
        } else {
            mFrontCover.setVisibility(View.GONE);
            mAllContent.setVisibility(View.VISIBLE);
        }
        wishInfoBean = (WishInfoBean) getIntent().getSerializableExtra(WISH_BEAN);
        if (wishInfoBean == null) {
            wish_id = getIntent().getStringExtra(WISH_ID);
            getInfo();
        } else {
            wish_id = wishInfoBean.getWish_id();
            handleInfo();
        }
        shareDialog = new CustomDialog(WishDetailInfoActivity.this);
//        shareDialog.setTitle(R.string.wish_share_dialog_title);
        shareDialog.setInfo(getResources().getString(R.string.wish_share_dialog_title));

        final String title = getString(R.string.wish_detail_share_title);
        final String content = getString(R.string.wish_detail_share_content);

        View view = getLayoutInflater().inflate(R.layout.common_share_layout_new, null);
        if (Apputils.systemLanguageIsChinese(WishDetailInfoActivity.this)) {
            view.findViewById(R.id.common_share_friend_iv).setOnClickListener(v -> {
                if (wishInfoBean == null) {
                    ToastUtils.showShort(WishDetailInfoActivity.this, getString(R.string.error_net));
                    return;
                }
                ShareUtilsSingleton.getInstance().shareUrlToWechatMoment(title, content, getImagePath(), getWishUrl());
                shareDialog.dismiss();
            });
            view.findViewById(R.id.common_share_wechat_iv).setOnClickListener(v -> {
                if (wishInfoBean == null) {
                    ToastUtils.showShort(WishDetailInfoActivity.this, getString(R.string.error_net));
                    return;
                }
                ShareUtilsSingleton.getInstance().shareUrlToWechat(title, content, getImagePath(), getWishUrl());
                shareDialog.dismiss();
            });
            view.findViewById(R.id.common_share_weibo_iv).setVisibility(View.INVISIBLE);
            view.findViewById(R.id.common_share_qq_iv).setVisibility(View.INVISIBLE);
            view.findViewById(R.id.common_share_qzone_iv).setVisibility(View.INVISIBLE);
        } else {
            facebookCallback = new FacebookCallback<Sharer.Result>() {
                @Override
                public void onSuccess(Sharer.Result result) {
                    ToastUtils.showShort(WishDetailInfoActivity.this, getResources().getString(R.string.share_complete));
                }

                @Override
                public void onCancel() {
                    ToastUtils.showShort(WishDetailInfoActivity.this, getResources().getString(R.string.share_cancel));
                }

                @Override
                public void onError(FacebookException error) {
                    ToastUtils.showShort(WishDetailInfoActivity.this, getResources().getString(R.string.share_error));
                }
            };
            view.findViewById(R.id.common_share_facebook_iv).setOnClickListener(v -> {
                ShareUtilsSingleton.getInstance().shareUrlToFacebook(title, getWishUrl(), WishDetailInfoActivity.this, facebookCallback);
                shareDialog.dismiss();
            });
            view.findViewById(R.id.common_share_facebook_iv).setVisibility(View.VISIBLE);
            view.findViewById(R.id.common_share_friend_iv).setVisibility(View.INVISIBLE);
            view.findViewById(R.id.common_share_wechat_iv).setVisibility(View.INVISIBLE);
            view.findViewById(R.id.common_share_weibo_iv).setVisibility(View.INVISIBLE);
            view.findViewById(R.id.common_share_qq_iv).setVisibility(View.INVISIBLE);
            view.findViewById(R.id.common_share_qzone_iv).setVisibility(View.INVISIBLE);
        }

        shareDialog.setMainLayoutView(view);
        shareDialog.setPositiveButton(R.string.cancel, v -> shareDialog.dismiss());

        if (getIntent().getBooleanExtra(SHOW_SHARE, false) && !hasShowDialog) {
            shareInfoDialog();
        }
        mWishDetailCoverLv.setImageAssetsFolder("lottie_wish_item/");
        mWishDetailCoverLv.setAnimation("wish_item_open.json");
    }

    private String getImagePath() {
        String path = FileUtils.saveBitmapFromSDCard(BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.wish_share_icon_new),
                "totwoo_cache_img_" + System.currentTimeMillis());
        return path;
    }

    private String getWishUrl() {
        return HttpHelper.HOST+"/v3/WishShare/index?sign=" + DesUtil.wishSign(wish_id, wishInfoBean.getWish_type());
//        return "/v3/WishShare/index?wish_id=" + wish_id + "&wish_type=" + wishInfoBean.getWish_type()
//                + "&totwoo_id=" + ToTwooApplication.owner.getTotwooId() + "&language=" + language;
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopTitleColor(getResources().getColor(R.color.white));
        setTopRightIcon(R.drawable.icon_wish_delete);
        setTopRigh2tIcon(R.drawable.icon_wish_share);
        setTopRightOnClick(v -> deleteDialog());
        getTopRight2Icon().setOnClickListener(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_TOPRIGHT_SHARE);
            shareDialog.show();
        });
    }

    private boolean likeClicked = false;

    @OnClick({R.id.wish_detail_like_ll, R.id.wish_detail_control_iv, R.id.wish_detail_done_tv, R.id.wish_show_close_cl})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.wish_detail_like_ll:
                if (likeClicked) {
                    return;
                }
                if (wishInfoBean == null) {
                    ToastUtils.showShort(WishDetailInfoActivity.this, getString(R.string.error_net));
                    return;
                }
                likeClicked = true;
//                String language = Apputils.systemLanguageIsChinese(WishDetailInfoActivity.this) ? "cn" : "en";
//                String url = "/v3/WishShare/index?wish_id=" + wish_id + "&wish_type=" + wishInfoBean.getWish_type()
//                        + "&totwoo_id=" + ToTwooApplication.owner.getTotwooId() + "&language=" + language + "&source=totwoo";
                String url = HttpHelper.HOST+"/v3/WishShare/index?sign=" + DesUtil.wishSignInApp(wish_id, wishInfoBean.getWish_type());
                WebViewActivity.loadUrl(this, url, false);
//                startActivity(new Intent(WishDetailInfoActivity.this,WishWebActivity.class).putExtra("URL",url));
                break;
            case R.id.wish_detail_control_iv:
                if (wishInfoBean.getWish_type() == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                    if (isVoicePlaying) {
                        mControlIv.setImageResource(R.drawable.wish_add_info_voice_play);
                        mPlayer.pause();
                        isVoicePlaying = false;
                    } else {
                        mControlIv.setImageResource(R.drawable.wish_add_info_voice_pause);
                        mPlayer.start();
                        isVoicePlaying = true;
                        //((AnimationDrawable) audioGif.getDrawable()).start();
                        mPlayer.setOnCompletionListener(mp -> {
                            mControlIv.setImageResource(R.drawable.wish_add_info_voice_play);
                            isVoicePlaying = false;
                        });
                    }
                } else if (wishInfoBean.getWish_type() == CommonArgs.COMMON_SEND_TYPE_VIDEO) {

                }
                break;
            case R.id.wish_detail_done_tv:
                showFinishDialog();
                break;
            case R.id.wish_show_close_cl:
                finish();
                break;
        }
    }

    private void getInfo() {
        HttpHelper.wishService.getWishInfo(wish_id)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<WishInfoBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(WishDetailInfoActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<WishInfoBean> wishInfoBeanHttpBaseBean) {
                        if (wishInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            wishInfoBean = wishInfoBeanHttpBaseBean.getData();
                            handleInfo();
                        }
                    }
                });
    }

    private void reachStatus(long status) {
        if (status == 0) {
            mWishDoneTv.setVisibility(View.VISIBLE);
            mWishDoneStatusIv.setVisibility(View.GONE);
        } else {
            mWishDoneTv.setVisibility(View.GONE);
            mWishDoneStatusIv.setVisibility(View.VISIBLE);
            mWishDoneTimeTv.setVisibility(View.VISIBLE);
            if (Apputils.systemLanguageIsChinese(WishDetailInfoActivity.this)) {
                mWishDoneStatusIv.setImageResource(R.drawable.wish_detail_reach_cn);
            } else {
                mWishDoneStatusIv.setImageResource(R.drawable.wish_detail_reach_en);
            }
            String date = DateUtil.getStringDateByMillions(status * 1000);
            mWishDoneTimeTv.setText(date.replaceAll("-", "."));
        }
    }

    private void handleInfo() {
        if (!TextUtils.isEmpty(wishInfoBean.getContent())) {
            mContentTv.setText(wishInfoBean.getContent());
            mContentTv.setVisibility(View.VISIBLE);
        } else {
            mContentTv.setVisibility(View.GONE);
        }
        mLikeCountTv.setText(wishInfoBean.getLike_count() + "");

        reachStatus(wishInfoBean.getIs_reach());

        switch (wishInfoBean.getWish_type()) {
            case CommonArgs.COMMON_SEND_TYPE_TEXT:
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, CommonUtils.dip2px(WishDetailInfoActivity.this, 200));
                layoutParams.setMargins(0, 0, 0, CommonUtils.dip2px(WishDetailInfoActivity.this, 50));
                int padding = CommonUtils.dip2px(WishDetailInfoActivity.this, 10);
                mContentTv.setPadding(padding, padding, padding, padding);
                mContentTv.setLayoutParams(layoutParams);
                mContentTv.setBackgroundResource(R.drawable.shape_wish_detai_text_bg);
                break;
            case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                mMaincv.setVisibility(View.VISIBLE);
                JSONArray jsonArray;
                String img_url = "";
                try {
                    jsonArray = new JSONArray(wishInfoBean.getImg_url());
                    img_url = (String) jsonArray.get(0);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                Glide.with(WishDetailInfoActivity.this).load(img_url).into(mMainIv);
                break;
            case CommonArgs.COMMON_SEND_TYPE_SOUND:
                mMaincv.setVisibility(View.VISIBLE);
                mPlayer = new MediaPlayer();
                try {
                    mPlayer.setDataSource(WishDetailInfoActivity.this, Uri.parse(wishInfoBean.getAudio_url()));
                    mPlayer.prepare();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                mMainIv.setImageResource(R.drawable.wish_add_info_voice);
                mControlIv.setImageResource(R.drawable.wish_add_info_voice_play);
                break;
            case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                mMaincv.setVisibility(View.VISIBLE);
                mControlIv.setImageResource(R.drawable.wish_add_info_video_play);
                Glide.with(WishDetailInfoActivity.this).load(wishInfoBean.getCover_url()).into(mMainIv);
                mControlIv.setOnClickListener(v ->
                        new PreviewConfig(
                                wishInfoBean.getVedio_url(),
                                wishInfoBean.getCover_url(), null).goPreview(this)
                );
                break;
        }
        setTopTitle(DateUtil.getStringDateByMillions(Long.parseLong(wishInfoBean.getCreate_time()) * 1000));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (wishInfoBean == null) {
            return;
        }

        if (wishInfoBean.getWish_type() == CommonArgs.COMMON_SEND_TYPE_SOUND) {
            try {
                if (mPlayer != null) {
                    mPlayer.release();
                }
            } catch (Exception e) {
                LogUtils.e("e = " + e);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mShakeMonitor != null)
            mShakeMonitor.stop();

        if (wishInfoBean == null) {
            return;
        }

        if (wishInfoBean.getWish_type() == CommonArgs.COMMON_SEND_TYPE_SOUND) {
            try {
                if (mPlayer != null && mPlayer.isPlaying()) {
                    mControlIv.setVisibility(View.VISIBLE);
                    mPlayer.pause();
                    //((AnimationDrawable) audioGif.getDrawable()).stop();
                }
            } catch (Exception e) {
                LogUtils.e("e = " + e);
            }
        }
    }

    private void showFinishDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(WishDetailInfoActivity.this);
        commonMiddleDialog.setMessage(R.string.wish_detail_finish_dialog);
        commonMiddleDialog.setSure(v -> {
            commonMiddleDialog.dismiss();
            HttpHelper.wishService.reachWish(wish_id)
                    .compose(HttpHelper.<HttpBaseBean<Object>>rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                            if (objectHttpBaseBean.getErrorCode() == 0) {
                                showSuccessAnim();
                            }
                        }
                    });
        });
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    private void shareInfoDialog() {
//        wishShareDialog = new WishShareDialog(WishDetailInfoActivity.this, v -> {
//            wishShareDialog.dismiss();
//            shareDialog.show();
//        }, v -> wishShareDialog.dismiss());
//        wishShareDialog.show();

        final CustomMiddleInfoDialog customMiddleInfoDialog = new CustomMiddleInfoDialog(this);
        customMiddleInfoDialog.setMainIconIv(R.drawable.wish_share_dialog_icon);
        customMiddleInfoDialog.setTitleTv(getString(R.string.wish_share_dialog_text_title));
        customMiddleInfoDialog.setInfoText(getString(R.string.wish_share_dialog_text), getString(R.string.wish_share_dialog_text2));
        customMiddleInfoDialog.setConfirmTv(getString(R.string.wish_share_dialog_share), v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.WISH_SEND_SHARE);
            customMiddleInfoDialog.dismiss();
            shareDialog.show();
        });
        customMiddleInfoDialog.setDenyTv(getString(R.string.wish_share_dialog_close), v -> customMiddleInfoDialog.dismiss());
        customMiddleInfoDialog.show();
        hasShowDialog = true;
    }

    private void deleteDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(WishDetailInfoActivity.this);
        commonMiddleDialog.setMessage(R.string.wish_detail_delete);
        commonMiddleDialog.setSure(v -> HttpHelper.wishService.deleteWish(wish_id)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        EventBus.onPostReceived(S.E.E_WISH_DELETE_SUCCESSED, null);
                        finish();
                    }
                }));
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    private void initJewelryShake() {
        mShakeMonitor = new ShakeMonitor(this);
        BluetoothManage.getInstance().connectedStatus();

        // 设置摇首饰的监听
        mShakeMonitor.setOnEventListener((type) -> {
            changeGif();
            if (mShakeMonitor != null) {
                mShakeMonitor.stop();
            }
        });
    }

    private boolean needloadDatas = false;

    @Override
    protected void onResume() {
        super.onResume();
        if (needloadDatas) {
            getInfo();
        }
        needloadDatas = true;
        likeClicked = false;
        if (mShakeMonitor != null)
            mShakeMonitor.start();
    }

    private void changeGif() {
        mWishDetailCoverLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                afterGif();
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mWishDetailCoverLv.playAnimation();
    }

    private void afterGif() {
        int height = CommonUtils.getScreenHeight();
        mAllContent.setVisibility(View.VISIBLE);
        TranslateAnimation translateAnimation = new TranslateAnimation(0, 0, -height, 0);
        translateAnimation.setFillAfter(true);
        translateAnimation.setDuration(300);
        translateAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mFrontCover.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mAllContent.startAnimation(translateAnimation);
    }

    private void showSuccessAnim() {
        saveBg.setVisibility(View.VISIBLE);
        mWishDoneLv.setVisibility(View.VISIBLE);
        mWishDoneLv.setImageAssetsFolder("lottie_wish_done/");
        mWishDoneLv.setAnimation("wish_done.json");
        mWishDoneLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                reachStatus(System.currentTimeMillis() / 1000);
                ToastUtils.showLong(WishDetailInfoActivity.this, R.string.wish_detail_finish_toast);
                saveBg.setVisibility(View.GONE);
                mWishDoneLv.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mWishDoneLv.playAnimation();

        EventBus.onPostReceived(S.E.E_WISH_REACH_SUCCESSED, null);
        BluetoothManage.getInstance().notifyJewelryBq(5);
    }
}
