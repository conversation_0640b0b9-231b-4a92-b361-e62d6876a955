package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.activity.LoveSpacePinkActivity.PAIRED_HEAD_URL;
import static com.totwoo.totwoo.activity.LoveSpacePinkActivity.PAIRED_NAMES;

import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.NotifyDataModel;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.databinding.ActivityLoveManageBinding;
import com.totwoo.totwoo.fragment.LoveFragment;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

public class LoveManageActivity extends BaseActivity {
    /**
     * 配对管理操作类
     */
    private CoupleLogic mCoupleLogic;

    private ActivityLoveManageBinding binding;

    private boolean isPaired;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityLoveManageBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());

        binding.includePairedHeadLayout.loveOtherConnectIv.setVisibility(View.VISIBLE);
        binding.includePairedHeadLayout.loveSelfConnectIv.setVisibility(View.VISIBLE);

        binding.includePairedHeadLayout.getRoot().setScaleX(1.09f);
        binding.includePairedHeadLayout.getRoot().setScaleY(1.09f);

        binding.includePairedHeadLayout.loveNameTv.setTextColor(getResources().getColor(R.color.text_color_gray));

        isPaired = !TextUtils.isEmpty(ToTwooApplication.owner.getPairedId());

        if (isPaired) {
            binding.loveManagePairTv.setText(R.string.apart_paired);
        }

        mCoupleLogic = new CoupleLogic(this);

        String pairedHeadUrl = getIntent().getStringExtra(PAIRED_HEAD_URL);
        RequestOptions options = new RequestOptions()
                .error(R.drawable.love_manage_default_head);
        if (!isPaired) {
            if (!TextUtils.isEmpty(pairedHeadUrl)) {
                Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(pairedHeadUrl)).apply(options).into(binding.includePairedHeadLayout.loveManageOther);
            }
        } else {
            ACache aCache = ACache.get(ToTwooApplication.baseContext);
            int partner_gender = TextUtils.isEmpty(aCache.getAsString(CommonArgs.PARTNER_GENDER)) ? (1 - owner.getGender()) : Integer.valueOf(aCache.getAsString(CommonArgs.PARTNER_GENDER));
            BitmapHelper.setHead(ToTwooApplication.baseContext, binding.includePairedHeadLayout.loveManageOther, pairedHeadUrl, partner_gender);
        }
        Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(ToTwooApplication.owner.getHeaderUrl())).apply(options).into(binding.includePairedHeadLayout.loveManageMe);

        binding.loveManagePairLayout.setOnClickListener(v -> {
            if (isPaired) {
                unPair();
            } else {
//                checkAndToContacts();
                ToastUtils.showLong(this, R.string.data_error);
                finish();
            }
        });

//        binding.loveManageIconCl.setOnClickListener(v -> {
//            if (!isPaired) {
//                checkAndToContacts();
//            }
//        });
        binding.includePairedHeadLayout.loveNameTv.setVisibility(View.VISIBLE);

        binding.includePairedHeadLayout.loveNameTv.setText(getIntent().getStringExtra(PAIRED_NAMES));

        clearNotify();
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopbarBackground(Color.TRANSPARENT);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.apart_paired);
    }

    private void unPair() {
        mCoupleLogic.apartCouple(ToTwooApplication.owner.getPairedId(), success -> {
            if (success) {
//                isPaired = !TextUtils.isEmpty(ToTwooApplication.owner.getPairedId());
//                mPairTv.setText(R.string.choose_from_contacts);
//                Glide.with(ToTwooApplication.baseContext).load(R.drawable.love_manage_default_head).into(otherIv);
                // 直接返回首页
                startActivity(new Intent(this, HomeActivityControl.getInstance().getTagertClass()).addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP));
                finish();
                PreferencesUtils.remove(ToTwooApplication.baseContext, LoveFragment.PREF_HAS_SHOW_PAIRED_DIALOG);
            }
        });
    }

//    @Override
//    protected void onResume() {
//        super.onResume();
//        getCoupleListData();
//        EventBus.getDefault().register(this);
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//        EventBus.getDefault().unregister(this);
//    }
//
//    private void showDelDialog(final ContactsBean contactsBean) {
//        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
//        dialog.setMessage(R.string.delete_coule_item_prompt);
//        dialog.setSure(R.string.confirm, v -> {
//            deleteCoupleItem(contactsBean);
//            dialog.dismiss();
//        });
//        dialog.setCancel(R.string.cancel);
//        dialog.show();
//    }


    /**
     * 清空用户管理的相关推送信息
     */
    private void clearNotify() {
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        manager.cancel(NotifyDataModel.NOTI_REPLAY_ID);
        manager.cancel(NotifyDataModel.NOTI_REQUEST_ID);
        manager.cancel(NotifyDataModel.NOTI_APART_ID);
    }

//    private void checkAndToContacts() {
//        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_WANTPAIR_CLICK_ADDRESSBOOK);
//        if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.ALLOW_CONTACT_GET, false)) {
//            showAllowContactsDialog();
//        } else {
//            openContacts();
//        }
//    }
//
//    private void showAllowContactsDialog() {
//        CommonMiddleDialog allowDialog = new CommonMiddleDialog(LoveManageActivity.this);
//        allowDialog.setTitle(R.string.love_manager_note);
//        allowDialog.setInfo(R.string.allow_dialog_info);
//        allowDialog.setCancel(R.string.allow_dialog_deny);
//        allowDialog.setSure(R.string.allow_dialog_allow, v -> {
//            PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.ALLOW_CONTACT_GET, true);
//            openContacts();
//            allowDialog.dismiss();
//        });
//        allowDialog.show();
//    }
//
//    private void openContacts() {
//        if (!PermissionUtil.hasContactsPermission(LoveManageActivity.this)) {
//            return;
//        }
//        startActivityForResult(new Intent(LoveManageActivity.this,
//                TheHeartChooseActivity.class), 100);
//    }

//    /**
//     * 联网获取列表数据
//     */
//    private void getCoupleListData() {
//        RequestParams param = HttpHelper.getBaseParams(true);
//        param.addFormDataPart("page", 1);
//        param.addFormDataPart("perpage", 1000);
//        HttpRequest.get(
//                HttpHelper.URL_COUPLE_LIST, param,
//                new RequestCallBack<String>() {
//                    @Override
//                    public void onLogicSuccess(String s) {
//                        super.onLogicSuccess(s);
//                        // 隐藏空的加载View
//                        if (!TextUtils.isEmpty(s) && !isPaired) {
//                            mPairLL.setVisibility(View.VISIBLE);
//                        } else {
//                            mPairLL.setVisibility(View.GONE);
//                        }
//
//                        contactsBeans.clear();
//
//                        // 隐藏进度框
//                        showProgressBar(false);
//
//                        if (TextUtils.isEmpty(s)) {
//                            return;
//                        }
//
//                        JSONArray array = null;
//                        try {
//                            array = new JSONArray(s);
//                        } catch (JSONException e) {
//                            e.printStackTrace();
//                        }
//                        if (array == null) {
//                            return;
//                        }
//
//                        for (int i = 0; i < array.length(); i++) {
//                            JSONObject obj = array.optJSONObject(i);
//                            if (obj != null) {
//                                // 解析用户数据
//                                ContactsBean con = null;
//
//                                int state = obj.optInt("coupleShip");
//                                // 解析状态
//                                if (state == 1) {
//                                    con = new ContactsBean();
//                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_REQUEST);
//                                } else if (state == 3) {
//                                    con = new ContactsBean();
//                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_PAIRED);
//
//                                    ToTwooApplication.owner
//                                            .setPairedId(obj.optString("talkId"));
//                                } else if (state == 4) {
//                                    con = new ContactsBean();
//                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_APART);
//                                } else if (state == 2) {
//                                    con = new ContactsBean();
//                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_REPLY);
//                                }
//
//                                if (con == null) {
//                                    continue;
//                                }
//
//                                con.setTalkId(obj.optString("talkId"));
//
//                                JSONObject info = obj
//                                        .optJSONObject("userinfo");
//                                if (info != null) {
//                                    con.setHeadUrl(info
//                                            .optString("head_portrait"));
//                                    con.setName(info
//                                            .optString("nick_name"));
//
//                                    con.setSpecific_id(info.optString("totwoo_id"));
//                                    con.setPhoneNumber(info.optString("mobilephone"));
//                                }
//
//                                contactsBeans.add(con);
//                            }
//                        }
//                        mAdapter.notifyDataSetChanged();
//                    }
//
//                    @Override
//                    public void onFailure(int error, String msg) {
//                        ToastUtils.showLong(LoveManageActivity.this,
//                                R.string.error_net);
//                        // 隐藏进度框
//                        showProgressBar(false);
//                    }
//                });
//
//    }
//
//    private void deleteCoupleItem(final ContactsBean bean) {
//        RequestParams params = HttpHelper.getBaseParams(true);
//        params.addFormDataPart("talkId", bean.getTalkId());
//        HttpRequest.post(HttpHelper.URL_DELTET_COUPLE, params, new RequestCallBack<String>() {
//            @Override
//            public void onLogicSuccess(String o) {
//                super.onLogicSuccess(o);
//                contactsBeans.remove(bean);
//
//                // 如果删除了已配对的列表, 相当于直接解绑
//                if (bean.getCoupleShip() == CoupleLogic.COUPLE_STATE_PAIRED) {
//                    // 清除配对人信息
//                    CoupleLogic.clearCouplePairedData(LoveManageActivity.this);
//                    //发到HoemAcitivity和 HomeTotwooHolder
//                    EventBus.getDefault().post(new TotwooMessage(CoupleLogic.COUPLE_STATE_APART + "", null));
//                }
//                ToastUtils.showLong(LoveManageActivity.this, R.string.delete_success);
//                getCoupleListData();
//            }
//        });
//    }
//
//    /**
//     * 发送配对请求请求
//     *
//     * @param bean
//     */
//    private void sendRequest(final ContactsBean bean) {
//        if (bean != null) {
//            mCoupleLogic.sendRequest(bean, success -> {
//                if (success) {
//                    // 弹窗提示发送成功
//                    final CommonMiddleDialog dialog = new CommonMiddleDialog(
//                            LoveManageActivity.this);
////                    dialog.setTitle(R.string.send_success);
//                    dialog.setMessage(R.string.send_success);
//
//                    dialog.setSure(v -> dialog.dismiss());
//                    dialog.show();
//
//                    if (mAdapter != null) {
//                        mAdapter.notifyDataSetChanged();
//                    }
//
//                    // 重新刷新数据
//                    getCoupleListData();
//                }
//            });
//        }
//    }
//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onStateChange(TotwooMessage message) {
//        if (message.getTotwooState().equals(CoupleLogic.COUPLE_STATE_REPLY + "")) {
//            // 回复成功, 跳转心有灵犀详情页
//            Intent tar = new Intent(LoveManageActivity.this, LoveSpacePinkActivity.class);
//            startActivity(tar);
//            finish();
//        } else {
//            isPaired = !TextUtils.isEmpty(ToTwooApplication.owner.getPairedId());
//            Glide.with(ToTwooApplication.baseContext).load(R.drawable.love_manage_default_head).into(otherIv);
//            getCoupleListData();
//            LogUtils.e("aab message.getTotwooState() = " + message.getTotwooState());
//        }
//    }
//
//    /**
//     * 展示进度框
//     *
//     * @param show true 为展示, false 为隐藏
//     */
//    private void showProgressBar(boolean show) {
//        if (show) {
//            if (progressBar == null) {
//                progressBar = new CustomProgressBarDialog(
//                        LoveManageActivity.this);
//            }
//            progressBar.show();
//        } else {
//
//            if (progressBar != null && progressBar.isShowing()) {
//                progressBar.dismiss();
//            }
//        }
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        for (int grantResult : grantResults) {
//            if (grantResult == PackageManager.PERMISSION_GRANTED) {
//                checkAndToContacts();
//            }
//        }
//        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
//    }
//
//    public class CoupleListAdapter extends RecyclerView.Adapter<CoupleListAdapter.ViewHolder> {
//        @NonNull
//        @Override
//        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
//            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.love_manage_item, parent, false);
//            return new CoupleListAdapter.ViewHolder(itemView);
//        }
//
//        @Override
//        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
//            ContactsBean bean = contactsBeans.get(position);
//            // 头像, 优先显示网络
//            if (!TextUtils.isEmpty(bean.getHeadUrl())) {
//                BitmapHelper.display(LoveManageActivity.this, holder.mHeadIv,
//                        bean.getHeadUrl());
//            } else {
//                BitmapHelper.display(LoveManageActivity.this, holder.mHeadIv, R.drawable.default_head_yellow);
//            }
//
//            // 用户姓名, 如果为空, 显示用户Id
//            if (!TextUtils.isEmpty(bean.getName())) {
//                holder.mNameTv.setText(bean.getName());
//            } else {
//                holder.mNameTv.setText(bean.getPhoneNumber());
//            }
//            if(Apputils.systemLanguageIsChinese(LoveManageActivity.this)){
//                holder.mStatusTv.setTextSize(14);
//            }else{
//                holder.mStatusTv.setTextSize(12);
//            }
//            switch (bean.getCoupleShip()) {
//                case CoupleLogic.COUPLE_STATE_APART:
//                    holder.mInfoTv.setText(R.string.once_paired);
//                    holder.mStatusTv.setText(R.string.request_again);
//                    // 曾经配对状态, 点击提交申请
//                    holder.mStatusTv.setOnClickListener(v -> sendRequest(bean));
//
//                    break;
//                case CoupleLogic.COUPLE_STATE_PAIRED:
//                    holder.mInfoTv.setText(R.string.paired);
//                    holder.mInfoTv.setTextColor(getResources().getColor(
//                            R.color.text_color_golden));
//                    holder.mStatusTv.setText(R.string.apart_paired);
//
//                    // 已配对状态, 点击解除配对, 成功之后跳转介绍页
//                    holder.mStatusTv.setOnClickListener(v -> mCoupleLogic.apartCouple(ToTwooApplication.owner.getPairedId(), success -> {
//                        if (success) {
//                            bean.setCoupleShip(CoupleLogic.COUPLE_STATE_APART);
//                            if (mAdapter != null) {
//                                mAdapter.notifyDataSetChanged();
//                            }
//                        }
//                    }));
//
//                    break;
//                // 待回复状态, 点击回复同意(目前不能拒绝)
//                case CoupleLogic.COUPLE_STATE_REPLY:
//                    holder.mInfoTv.setText(R.string.want_pair);
//                    holder.mStatusTv.setText(R.string.agree);
//                    holder.mStatusTv.setOnClickListener(v -> {
//                        mCoupleLogic.replyRequest(bean.getTalkId(), success -> {
//                            if (success) {
//                                // 回复成功, 跳转心有灵犀详情页
//                                Intent intent = new Intent(LoveManageActivity.this,
//                                        LoveSpacePinkActivity.class);
//                                startActivity(intent);
//                                finish();
//                            } else {
//                                // 刷新列表
//                                getCoupleListData();
//                            }
//                        });
//                    });
//
//                    break;
//                // 请求状态, 点击取消请求
//                case CoupleLogic.COUPLE_STATE_REQUEST:
//                    holder.mInfoTv.setText(R.string.wait_reply);
//                    holder.mStatusTv.setText(R.string.cancel_request);
//                    holder.mStatusTv.setOnClickListener(v -> mCoupleLogic.cancelRequest(bean, success -> {
//                        // 取消成功, 重新刷新列表
//                        getCoupleListData();
//                    }));
//
//                    break;
//            }
//        }
//
//        @Override
//        public int getItemCount() {
//            return contactsBeans == null ? 0 : contactsBeans.size();
//        }
//
//        public class ViewHolder extends RecyclerView.ViewHolder {
//            @BindView(R.id.love_manage_item_iv)
//            ImageView mHeadIv;
//            @BindView(R.id.love_manage_item_name_tv)
//            TextView mNameTv;
//            @BindView(R.id.love_manage_item_info_tv)
//            TextView mInfoTv;
//            @BindView(R.id.love_manage_item_status_tv)
//            TextView mStatusTv;
//
//            public ViewHolder(@NonNull View itemView) {
//                super(itemView);
//                ButterKnife.bind(this, itemView);
//            }
//        }
//    }
}
