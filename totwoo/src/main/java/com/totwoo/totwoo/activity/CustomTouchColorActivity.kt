package com.totwoo.totwoo.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.SpanUtils
import com.totwoo.library.util.ext.click
import com.totwoo.totwoo.R
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.ble.JewInfoSingleton
import com.totwoo.totwoo.databinding.ActivityCustomTouchColorBinding
import com.totwoo.totwoo.utils.NotifyUtil
import com.totwoo.totwoo.utils.ToastUtils
import com.totwoo.totwoo.widget.TouchColorExplainDialog

class CustomTouchColorActivity : BaseActivity() {

    private lateinit var binding: ActivityCustomTouchColorBinding
    private var currentColor: String = "RED"
    private lateinit var colorAdapter: CustomColorLibraryAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomTouchColorBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initTopBar()
        initView()
        initListener()

        SpanUtils.with(binding.tvTip).append(getString(R.string.touch_page_tips)).append(" ")
            .appendImage(R.drawable.icon_help_black)
            .create()

        binding.tvTip.click {
            // 显示自定义触摸颜色说明对话框
            TouchColorExplainDialog(this).show()
        }
    }

    override fun initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black)
        setTopLeftOnclik { finish() }
//        setTopTitle(R.string.page_jew_remind_touch_color)
        setSpinState(false)
    }

    private fun initView() {
        // 获取传入的当前颜色
        currentColor = intent.getStringExtra("current_color") ?: "RED"
        
        // 设置RecyclerView的布局管理器，每行显示7个颜色项
        binding.colorRecyclerView.layoutManager = GridLayoutManager(this, 7)
        
        // 初始化适配器
        colorAdapter = CustomColorLibraryAdapter(
            currentColor,
            if (BleParams.isCtJewlery()) 7 else 6, false,false)
        binding.colorRecyclerView.adapter = colorAdapter
        
        // 设置点击事件
        colorAdapter.setOnItemClickListener { _, _, position ->
            val colorBean = colorAdapter.data[position]
            colorAdapter.setSelectColor(colorBean.color)
            // 设置选中的颜色
            BluetoothManage.getInstance().setTouchColorPreview(NotifyUtil.getColorValue(colorBean.color))
        }

        if (BleParams.isCtJewlery()) {
            binding.ivJewelry.setImageResource(R.drawable.jewelry_90_choose_corlor)
        } else if (BleParams.is33Jewlery()){
            binding.ivJewelry.setImageResource(R.drawable.jewelry_33_choose_corlor)
        }else if (BleParams.is34Jewlery()) {
            binding.ivJewelry.setImageResource(R.drawable.jewelry_34_choose_corlor)
        } else {
            binding.ivJewelry.visibility  = View.GONE
        }
    }

    private fun initListener() {
        // 保存按钮
        binding.btnSave.click {
            if (JewInfoSingleton.getInstance().connectState != JewInfoSingleton.STATE_CONNECTED) {
                Toast.makeText(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT).show()
                return@click
            }

            // 获取选中的颜色
            val selectedItem = colorAdapter.getSelectItem()
            if (selectedItem == null) {
                Toast.makeText(this, "Please select a color", Toast.LENGTH_SHORT).show()
                return@click
            }
            
            val selectedColor = selectedItem.color
            
            // 写入固件
            BluetoothManage.getInstance().setTouchColor(NotifyUtil.getColorValue(selectedColor))
            
            // 返回结果
            setResult(RESULT_OK, Intent().apply {
                putExtra("selected_color", selectedColor)
            })
            ToastUtils.show(this@CustomTouchColorActivity, R.string.saved_success, Toast.LENGTH_SHORT)
            finish()
        }
    }
} 