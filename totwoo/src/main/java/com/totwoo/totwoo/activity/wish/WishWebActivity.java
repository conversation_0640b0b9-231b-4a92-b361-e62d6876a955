//package com.totwoo.totwoo.activity.wish;
//
//import android.content.Intent;
//import android.graphics.PixelFormat;
//import android.os.Bundle;
//import android.view.KeyEvent;
//
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.activity.BaseActivity;
//
//import androidx.annotation.Nullable;
//import androidx.fragment.app.FragmentManager;
//import androidx.fragment.app.FragmentTransaction;
//
//public class WishWebActivity extends BaseActivity {
//
//    private FragmentManager mFragmentManager;
//    private SuperWebX5Fragment mSuperWebX5Fragment;
//
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//
//        setContentView(R.layout.activity_wish_web);
//        getWindow().setFormat(PixelFormat.TRANSLUCENT);
//        mFragmentManager = this.getSupportFragmentManager();
//        String url = getIntent().getStringExtra("URL");
//        openFragment(url);
//    }
//
//    @Override
//    protected void initTopBar() {
//        setTopLeftIcon(R.drawable.back_icon_black);
//        setTopLeftOnclik(v -> finish());
//        setTopCancelIcon(R.drawable.close_icon);
//    }
//
//    private void openFragment(String url) {
//        FragmentTransaction ft = mFragmentManager.beginTransaction();
//        Bundle mBundle = null;
//        ft.add(R.id.container_framelayout, mSuperWebX5Fragment = SuperWebX5Fragment.getInstance(mBundle = new Bundle()), SuperWebX5Fragment.class.getName());
//        mBundle.putString(SuperWebX5Fragment.URL_KEY, url);
////        mBundle.putString(SuperWebX5Fragment.URL_KEY, "http://soft.imtt.qq.com/browser/tes/feedback.html");
//        ft.commit();
//    }
//
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        mSuperWebX5Fragment.onActivityResult(requestCode, resultCode, data);
//    }
//
//    @Override
//    public boolean onKeyDown(int keyCode, KeyEvent event) {
//        SuperWebX5Fragment mAgentWebX5Fragment = this.mSuperWebX5Fragment;
//        if (mAgentWebX5Fragment != null) {
//            FragmentKeyDown mFragmentKeyDown = mAgentWebX5Fragment;
//            if (mFragmentKeyDown.onFragmentKeyDown(keyCode, event)) {
//                return true;
//            } else {
//                return super.onKeyDown(keyCode, event);
//            }
//        }
//
//        return super.onKeyDown(keyCode, event);
//    }
//}
