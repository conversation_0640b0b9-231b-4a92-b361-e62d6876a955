package com.totwoo.totwoo.activity.giftMessage;

import android.animation.Animator;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Typeface;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.Gravity;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintProperties;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.blankj.utilcode.util.SizeUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.ContactsActivityForGift;
import com.totwoo.totwoo.activity.InitInfoActivity;
import com.totwoo.totwoo.activity.JewelrySelectActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.GiftMessageBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CenterCropRoundCornerTransform;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonShareDialog;
import com.totwoo.totwoo.widget.CommonShareType;
import com.totwoo.totwoo.widget.NewUserGiftDialog;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;

/**
 * 智能情书详情页面/预览页面, 展示不同类型的情书
 */
public class GiftDataActivity extends BaseActivity implements SubscriberListener, BluetoothManage.ConnectSuccessListener {
    public static final String TYPE = "type";
    public static final String ITEM = "item";
    public static final String PREVIEW = "preview";
    public static final String RECEIVER = "receiver";
    public static final String RECEIVER_LIST = "receiver_list";
    public static final String SEND_LIST = "send_list";
    public static final String OPEN_FROM_TYPE = "open_from_type";
    @BindView(R.id.gift_data_main_iv)
    ImageView mMainIv;
    @BindView(R.id.gift_data_name_text)
    TextView mNameTv;
    @BindView(R.id.gift_data_month_day_tv)
    TextView mMonthDayTv;
    @BindView(R.id.gift_data_year_tv)
    TextView mYearTv;
    @BindView(R.id.gift_data_info_text)
    TextView mInfoText;
    @BindView(R.id.gift_data_name_pre)
    TextView mNamePre;
    @BindView(R.id.gift_data_info_tv)
    TextView mInfoTv;
    @BindView(R.id.gift_data_share_tv)
    TextView mShareTv;
    @BindView(R.id.gift_data_play_iv)
    ImageView mVoicePlayIv;
    @BindView(R.id.gift_data_lav)
    LottieAnimationView mLottieView;
    @BindView(R.id.gift_info_content_cl)
    ConstraintLayout mContentInfo;
    @BindView(R.id.gift_message_cover)
    RelativeLayout mCoverRl;
    @BindView(R.id.card_open_white)
    ImageView mCardOpenWhiteIv;
    @BindView(R.id.card_open_lottie)
    LottieAnimationView mCardOpenLv;
    @BindView(R.id.card_open_tv)
    TextView mCardOpenTv;
    @BindView(R.id.card_open_close)
    ImageView mCloseIv;
    @BindView(R.id.gift_data_all_content)
    LinearLayout mAllContent;



    private String info;
    private String name;
    private int cardType;
    private String fromType;
    private GiftMessageBean giftMessageBean;
    private MediaPlayer mPlayer;
    private boolean isVoicePlaying = false;
    private ShakeMonitor mShakeMonitor;
    private FacebookCallback<Sharer.Result> facebookCallback;
    private NewUserGiftDialog newUserGiftDialog;
    private boolean isOpened;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gift_data);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        fromType = getIntent().getStringExtra(CommonArgs.FROM_TYPE);

        CommonUtils.setStateBar(this, false);

        Calendar calendar = Calendar.getInstance();
        if (TextUtils.equals(fromType, PREVIEW)) {
            setDateTvs(calendar);
            cardType = getIntent().getIntExtra(TYPE, 1);
            info = getIntent().getStringExtra(GiftInfoAddActivity.GIFT_SENDER_INFO);
            name = getIntent().getStringExtra(GiftInfoAddActivity.GIFT_SENDER_NAME);
            if (cardType == CommonArgs.COMMON_SEND_TYPE_IMAGE) {
                setImageUrl(BitmapFactory.decodeFile(CommonArgs.CACHE_GIFT_IMAGE), mMainIv);
                mVoicePlayIv.setVisibility(View.GONE);
            } else if (cardType == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                prepareMplayer(null, CommonArgs.CACHE_GIFT_AUDIO_PATH);
            } else if (cardType == CommonArgs.COMMON_SEND_TYPE_VIDEO) {
                setImageUrl(BitmapFactory.decodeFile(getIntent().getStringExtra(CommonArgs.COVER_PATH)), mMainIv);
                mVoicePlayIv.setVisibility(View.VISIBLE);
            }
        } else if (TextUtils.equals(fromType, RECEIVER_LIST)) {
            giftMessageBean = getIntent().getParcelableExtra(ITEM);
            Date date = new Date(giftMessageBean.getReceiveTime() * 1000);
            calendar.setTime(date);
            setDateTvs(calendar);
            setInfo();
            showShareButton(false);
            if (cardType == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                mHandler.postDelayed(() -> autoPlaySound(), 500);
            }
        } else if (TextUtils.equals(fromType, SEND_LIST)) {
            giftMessageBean = getIntent().getParcelableExtra(ITEM);
            Date date = new Date(giftMessageBean.getSendTime() * 1000);
            calendar.setTime(date);
            setDateTvs(calendar);
            setInfo();
            showShareButton(false);
            if (cardType == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                mHandler.postDelayed(() -> autoPlaySound(), 500);
            }
        } else if (TextUtils.equals(fromType, RECEIVER)) {
            mCoverRl.setVisibility(View.VISIBLE);
            mAllContent.setVisibility(View.GONE);
//            mCardOpenLv.setImageAssetsFolder("lottie_greeting_card/");
//            mCardOpenLv.setAnimation("greeting_card_list.json");

            String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");

            String letterTip = JewelryOnlineDataManager.getInstance().getConnectedJewInfo().getLetter_tip();
            if (!TextUtils.isEmpty(letterTip)) {
                    mCardOpenTv.setText(letterTip);
            } else {
                if (TextUtils.isEmpty(jewName)) {
                    mCardOpenTv.setText(R.string.receive_card_no_jewelry);
                } else if (BleParams.isSM2(jewName) && !TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_80) && !TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_81)) {
                    // 8x 系列新款首饰样式调整, 追加新的文言, 后续可能调整
                    mCardOpenTv.setText(R.string.receive_card_info2_touch_sm);
                } else if (BleParams.isTouchJewelry(jewName)) {
                    if (BleParams.isMWJewlery()||BleParams.isCtJewlery()){
                        mCardOpenTv.setText(R.string.open_love_msg_33);
                    }else {
                        mCardOpenTv.setText(R.string.receive_card_info2_touch);
                    }
                } else {
                    mCardOpenTv.setText(R.string.receive_card_info2);
                }
            }
            mCloseIv.setOnClickListener(v -> finishAndGoNext());
            initJewelryShake();
            giftMessageBean = getIntent().getParcelableExtra(ITEM);
            setDateTvs(calendar);
            setInfo();
            showShareButton(false);
            confirmGift();
        }

        if (TextUtils.isEmpty(info)) {
            mInfoText.setVisibility(View.INVISIBLE);
            mInfoTv.setVisibility(View.INVISIBLE);
        } else {
            mInfoTv.setText(info);

            mInfoTv.setMovementMethod(new ScrollingMovementMethod());
        }

        if (!TextUtils.isEmpty(name) && name.length() > 7) {
            ConstraintLayout.LayoutParams preLayoutParams = new ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.WRAP_CONTENT, ConstraintLayout.LayoutParams.WRAP_CONTENT);
            preLayoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
            preLayoutParams.bottomToTop = R.id.gift_data_name_text;
            preLayoutParams.setMargins(0, 0, CommonUtils.dip2px(GiftDataActivity.this, 18), CommonUtils.dip2px(GiftDataActivity.this, 5));
            mNamePre.setLayoutParams(preLayoutParams);

            new ConstraintProperties(mNameTv)
                    .removeConstraints(ConstraintProperties.START)
                    .removeConstraints(ConstraintProperties.END)
                    .connect(
                            ConstraintProperties.START,
                            R.id.gift_data_date_bg_iv,
                            ConstraintProperties.END, 0
                    ).connect(
                            ConstraintProperties.END,
                            R.id.gift_data_name_pre,
                            ConstraintProperties.END, 0
                    )
                    .margin(ConstraintProperties.START, SizeUtils.dp2px(10))
                    .apply();

            mNameTv.setGravity(Gravity.END);
        }


        mNameTv.setText(name);

        // 使用弱引用避免内存泄露
        WeakReference<GiftDataActivity> weakRef = new WeakReference<>(this);
        newUserGiftDialog = new NewUserGiftDialog(getApplicationContext(), v -> {
            GiftDataActivity activity = weakRef.get();
            if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
                WebViewActivity.loadUrl(activity, HttpHelper.URL_GIFT, false);
                if (newUserGiftDialog != null) {
                    newUserGiftDialog.dismiss();
                }
            }
        }, v -> {
            if (newUserGiftDialog != null) {
                newUserGiftDialog.dismiss();
            }
        }, CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券", 88, 20), "立即抽奖");
    }

    private void setTopMargin(View view,float dpValue) {
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) view.getLayoutParams();
        params.topMargin = SizeUtils.dp2px(dpValue); // 设置新的上边距
        view.setLayoutParams(params);
        view.requestLayout();
    }

    @Override
    protected void initTopBar() {
        if (!TextUtils.equals(fromType, RECEIVER)) {
            setTopBackIcon(R.drawable.back_icon_black);
            setTopLeftOnclik(v -> finishAndGoNext());
        }
        if (TextUtils.equals(fromType, PREVIEW)) {
            setTopRightString(getString(R.string.select_contact), getResources().getColor(R.color.color_main));
            setTopRightOnClick(v -> {
                if (PermissionUtil.hasContactsPermission(GiftDataActivity.this)) {
                    toSend();
                }
            });
        } else if (TextUtils.equals(fromType, RECEIVER)) {
            setTopRightString(getString(R.string.save_card), getResources().getColor(R.color.color_main));
            setTopRightOnClick(v -> finishAndGoNext());
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finishAndGoNext();
    }

    private void finishAndGoNext() {
        if (TextUtils.equals(fromType, RECEIVER_LIST) || TextUtils.equals(fromType, SEND_LIST)) {
            finish();
        } else if (TextUtils.equals(fromType, PREVIEW)) {
            finish();
        }  else if (TextUtils.equals(fromType, GiftDataActivity.RECEIVER)) {
            Intent intent = new Intent();
            setResult(RESULT_OK, intent);
            finish();
        } else {
            //判断去向
            String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
            if (TextUtils.equals(getIntent().getStringExtra(OPEN_FROM_TYPE), CommonArgs.LOGIN)) {
                if (ToTwooApplication.isInfoSetFinish(ToTwooApplication.owner)) {
                    HomeActivityControl.getInstance().openHomeActivity(GiftDataActivity.this);

                    // 去往首饰连接页面
                    if (TextUtils.isEmpty(jewName)) {
                        startActivity(new Intent(this, JewelrySelectActivity.class));
                    }
                } else {
//                    Intent intent = new Intent(GiftDataActivity.this, UserInfoSettingActivity.class);
//                    //intent.putExtra("checkstate", isSuccess);
//                    intent.putExtra("checkstate", false);
//                    startActivity(intent);
                    startActivity(new Intent(this, InitInfoActivity.class).putExtra(InitInfoActivity.INIT_INFO, true));
                }
            } else if (TextUtils.isEmpty(jewName)) {
                startActivity(new Intent(this, JewelrySelectActivity.class));
            }
            finish();
        }
    }

    @OnClick({R.id.gift_data_share_tv, R.id.gift_data_play_iv, R.id.gift_data_main_iv, R.id.gift_data_lav})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.gift_data_share_tv:
                showShareDialog();
                break;
            case R.id.gift_data_lav:
            case R.id.gift_data_main_iv:
            case R.id.gift_data_play_iv:
                if (cardType == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                    if (isVoicePlaying) {
                        mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
                        mPlayer.pause();
                        mLottieView.pauseAnimation();
                        isVoicePlaying = false;
                    } else {
                        mVoicePlayIv.setImageResource(R.drawable.gift_voice_pause);
                        mPlayer.start();
                        mLottieView.playAnimation();
                        isVoicePlaying = true;
                        //((AnimationDrawable) audioGif.getDrawable()).start();
                        mPlayer.setOnCompletionListener(mp -> {
                            mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
                            isVoicePlaying = false;
                            mLottieView.pauseAnimation();
                        });
                    }
                } else if (cardType == CommonArgs.COMMON_SEND_TYPE_VIDEO) {
                    if (TextUtils.equals(fromType, PREVIEW)) {

                        new PreviewConfig(
                                getIntent().getStringExtra(CommonArgs.VIDEO_PATH),
                                getIntent().getStringExtra(CommonArgs.COVER_PATH),
                                null)
                                .goPreview(this);
                    } else {
                        new PreviewConfig(
                                BitmapHelper.checkRealPath(giftMessageBean.getGreetingCardData().getVedioUrl()),
                                BitmapHelper.checkRealPath(giftMessageBean.getGreetingCardData().getVedioPreviewImageUrl()),
                                null)
                                .goPreview(this);
                    }
                }
                break;
        }
    }

    private void toSend() {
//        Intent intent = new Intent(GiftDataActivity.this, ContactsSelectActivity.class);
        Intent intent = new Intent(GiftDataActivity.this, ContactsActivityForGift.class);
        intent.putExtra(GiftDataActivity.TYPE, cardType);
        intent.putExtra(CommonArgs.VIDEO_PATH, getIntent().getStringExtra(CommonArgs.VIDEO_PATH));
        intent.putExtra(CommonArgs.COVER_PATH, getIntent().getStringExtra(CommonArgs.COVER_PATH));
        intent.putExtra(GiftInfoAddActivity.GIFT_SENDER_NAME, name);
        intent.putExtra(GiftInfoAddActivity.GIFT_SENDER_INFO, info);
        startActivity(intent);
    }

    private void confirmGift() {
        HttpHelper.card.confirmGift(ToTwooApplication.owner.getTotwooId(), ToTwooApplication.owner.getPhone(), giftMessageBean.getGreetingCardId())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean httpBaseBean) {

                    }
                });
    }

    private void setInfo() {
        info = giftMessageBean.getGreetingCardData().getText();
        name = giftMessageBean.getSenderName();

        if (giftMessageBean.getGreetingCardType() == 2 || giftMessageBean.getGreetingCardType() == 3) {
            cardType = CommonArgs.COMMON_SEND_TYPE_IMAGE;
            setImageUrl(giftMessageBean.getGreetingCardData().getImageUrl(), mMainIv);
            mVoicePlayIv.setVisibility(View.GONE);
        } else if (giftMessageBean.getGreetingCardType() == 4 || giftMessageBean.getGreetingCardType() == 5) {
            cardType = CommonArgs.COMMON_SEND_TYPE_SOUND;
            prepareMplayer(Uri.parse(BitmapHelper.checkRealPath(giftMessageBean.getGreetingCardData().getAudioUrl())), null);
        } else if (giftMessageBean.getGreetingCardType() == 8 || giftMessageBean.getGreetingCardType() == 9) {
            cardType = CommonArgs.COMMON_SEND_TYPE_VIDEO;
            setImageUrl(giftMessageBean.getGreetingCardData().getVedioPreviewImageUrl(), mMainIv);
            mVoicePlayIv.setVisibility(View.VISIBLE);

        }
    }

    private void prepareMplayer(Uri uri, String path) {
        mPlayer = new MediaPlayer();
        mLottieView.setImageAssetsFolder("lottie_gift_voice/");
        mLottieView.setAnimation("gift_voice_play.json");
        mLottieView.setRepeatCount(LottieDrawable.INFINITE);
        mLottieView.setVisibility(View.VISIBLE);
        mMainIv.setVisibility(View.INVISIBLE);
        try {
            if (TextUtils.isEmpty(path)) {
                mPlayer.setDataSource(GiftDataActivity.this, uri);
            } else {
                mPlayer.setDataSource(path);
            }
            mPlayer.prepareAsync();
        } catch (IOException e) {
            e.printStackTrace();
        }
        mVoicePlayIv.setVisibility(View.VISIBLE);
        mVoicePlayIv.setTranslationY(SizeUtils.dp2px(10));

        mInfoTv.setTranslationY(SizeUtils.dp2px(20));

    }

    private void setImageUrl(String imageUrl, ImageView mMainIv) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.gift_placeholder);
        Glide.with(this).load(BitmapHelper.checkRealPath(imageUrl)).apply(RequestOptions.bitmapTransform(new CenterCropRoundCornerTransform(40))).apply(options).into(mMainIv);
    }

    private void setImageUrl(Bitmap bitmap, ImageView mMainIv) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.gift_placeholder);
        Glide.with(this).load(bitmap).apply(RequestOptions.bitmapTransform(new CenterCropRoundCornerTransform(40))).apply(options).into(mMainIv);
    }

    private void setDateTvs(Calendar calendar) {
        mYearTv.setText(calendar.get(calendar.YEAR) + "");
        mMonthDayTv.setText(getResources().getStringArray(R.array.month_names_en)[calendar
                .get(Calendar.MONTH)] + "" + CommonUtils.getZeroStart(calendar.get(Calendar.DAY_OF_MONTH)));
    }

    private CommonShareDialog commonShareDialog;

    private String getShareTitle() {
        if (BleParams.isSecurityJewlery()) {
            return getString(R.string.safe_card_share_title);
        } else {
            return getString(R.string.card_share_title);
        }
    }

    private String getShareContent() {
        if (BleParams.isSecurityJewlery()) {
            return getString(R.string.safe_share_context);
        } else {
            return getString(R.string.share_context);
        }
    }

    private void showShareDialog() {
        if (commonShareDialog == null) {
            if (Apputils.systemLanguageIsChinese(GiftDataActivity.this)) {
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                types.add(CommonShareType.WEIBO);
                types.add(CommonShareType.QZONE);
                types.add(CommonShareType.QQ);
                commonShareDialog = new CommonShareDialog(GiftDataActivity.this, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareUrlToWechatMoment(getShareTitle(), getShareContent(), getGreetingPath(), getPath());
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareUrlToWechat(getShareTitle(), getShareContent(), getGreetingPath(), getPath());
                            break;
                        case WEIBO:
                            ShareUtilsSingleton.getInstance().shareUrlToWeibo(GiftDataActivity.this, getShareTitle(), getGreetingPath(), getPath());
                            break;
                        case QZONE:
                            ShareUtilsSingleton.getInstance().shareUrlToQzone(getShareTitle(), getShareContent(), getGreetingPath(), getPath());
                            break;
                        case QQ:
                            ShareUtilsSingleton.getInstance().shareUrlToQQ(getShareTitle(), getShareContent(), getGreetingPath(), getPath());
                            break;
                    }

                });
                commonShareDialog.setCustomTitle(CommonUtils.setNumberOrangeSpan(getResources().getString(R.string.share_text_head_info), 88, 17));
            } else {
                facebookCallback = new FacebookCallback<Sharer.Result>() {
                    @Override
                    public void onSuccess(Sharer.Result result) {
                        ToastUtils.showShort(GiftDataActivity.this, getResources().getString(R.string.share_complete));
                    }

                    @Override
                    public void onCancel() {
                        ToastUtils.showShort(GiftDataActivity.this, getResources().getString(R.string.share_cancel));
                    }

                    @Override
                    public void onError(FacebookException error) {
                        ToastUtils.showShort(GiftDataActivity.this, getResources().getString(R.string.share_error));
                    }
                };
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FACEBOOK);
                types.add(CommonShareType.TWITTER);
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                commonShareDialog = new CommonShareDialog(GiftDataActivity.this, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareUrlToWechatMoment(getShareTitle(), getShareContent(), getGreetingPath(), getPath());
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareUrlToWechat(getShareTitle(), getShareContent(), getGreetingPath(), getPath());
                            break;
                        case FACEBOOK:
                            ShareUtilsSingleton.getInstance().shareUrlToFacebook(getShareTitle(), getPath(), GiftDataActivity.this, facebookCallback);
                            break;
                        case TWITTER:
                            ShareUtilsSingleton.getInstance().shareUrlToTwitter(getShareTitle(), getGreetingPath(), getPath());
                            break;
                    }

                });
                commonShareDialog.setCustomTitle(getResources().getString(R.string.share_text_head_info));
            }
        }
        commonShareDialog.show();
    }

    private String getPath() {
        StringBuffer shareUrl = new StringBuffer("http://m.totwoo.cn/share/index.php?uid=");
        shareUrl.append(ToTwooApplication.owner.getTotwooId());
        switch (cardType) {
            case 0://纯文字不传type
                break;
            case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                shareUrl.append("&type=img");
                break;
            case CommonArgs.COMMON_SEND_TYPE_SOUND:
                shareUrl.append("&type=audio");
                break;
            case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                shareUrl.append("&type=video");
                break;
        }
        shareUrl.append("&tid=").append(giftMessageBean.getGreetingCardId());
        shareUrl.append("&state=").append(Apputils.systemLanguageIsChinese(this) ? "cn" : "en");
        return shareUrl.toString();
    }

    private String getGreetingPath() {
        if (BleParams.isSecurityJewlery()) {
            String path = FileUtils.saveBitmapFromSDCard(BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.icon_share_temp),
                    "totwoo_cache_img_share_logo");
            return path;
        } else {
            File f = new File(CommonArgs.GREETING_SHARE_IMAGE);
            if (!f.exists()) {
                try {
                    BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.card_share_abbreviations).compress(Bitmap.CompressFormat.JPEG, 100, new FileOutputStream(new File(CommonArgs.GREETING_SHARE_IMAGE)));
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
            }
            return f.getPath();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        if (TextUtils.equals(fromType, RECEIVER)) {
            if (mShakeMonitor != null)
                mShakeMonitor.stop();
            BluetoothManage.getInstance().stayIn(false);
            BluetoothManage.getInstance().setConnectSuccessListener(null);
        }

        try {
            if (mPlayer != null && mPlayer.isPlaying()) {
                mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
                mLottieView.pauseAnimation();
                mPlayer.pause();
                //((AnimationDrawable) audioGif.getDrawable()).stop();
            }
        } catch (Exception e) {

        }
    }

    private void showShareButton(boolean isShow) {

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (TextUtils.equals(fromType, RECEIVER) && !isOpened) {
            if (mShakeMonitor != null)
                mShakeMonitor.start();
            BluetoothManage.getInstance().stayIn(true);
            BluetoothManage.getInstance().setConnectSuccessListener(this);
        }

    }

    private void initJewelryShake() {
        mShakeMonitor = new ShakeMonitor(this);
        BluetoothManage.getInstance().connectedStatus();
//         设置摇首饰的监听
        mShakeMonitor.setOnEventListener((type) -> {
            //弹出信纸
            openCardAnim();
            isOpened = true;
            BluetoothManage.getInstance().stayIn(false);
            BluetoothManage.getInstance().setConnectSuccessListener(null);
            if (mShakeMonitor != null) {
                mShakeMonitor.stop();
            }
        });
    }

    private void openCardAnim() {
        AlphaAnimation whitePageAnim = new AlphaAnimation(0, 1.0f);
        whitePageAnim.setFillAfter(true);
        whitePageAnim.setDuration(1000);

        AlphaAnimation alphaAnimation = new AlphaAnimation(0, 1.0f);
        alphaAnimation.setFillAfter(true);
        alphaAnimation.setDuration(1000);

        mCardOpenLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mCardOpenWhiteIv.setVisibility(View.VISIBLE);
                mCardOpenWhiteIv.startAnimation(whitePageAnim);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        whitePageAnim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

                mAllContent.setVisibility(View.VISIBLE);
                mAllContent.startAnimation(alphaAnimation);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        alphaAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mCoverRl.setVisibility(View.GONE);
                if (cardType == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                    mHandler.postDelayed(() -> autoPlaySound(), 500);
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mCardOpenLv.playAnimation();
    }

    private void autoPlaySound() {
        mVoicePlayIv.setImageResource(R.drawable.gift_voice_pause);
        mPlayer.start();
        mLottieView.playAnimation();
        isVoicePlaying = true;
        mPlayer.setOnCompletionListener(mp -> {
            mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
            isVoicePlaying = false;
            mLottieView.pauseAnimation();
        });
    }

    private SpannableString setStyle() {
        String string = getString(R.string.receive_card_info);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = "敲击2下";
        String sub2 = "情书";
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        index = string.indexOf(sub2);
        endIndex = index + sub2.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private SpannableString setStyleTouch() {
        String string = getString(R.string.receive_card_info_touch);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = "手指触摸兔兔正面";
        String sub2 = "情书";
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        index = string.indexOf(sub2);
        endIndex = index + sub2.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private SpannableString setStyleTouchNoVibrate() {
        String string = getString(R.string.receive_card_info_touch_no_vibrate);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = "手指轻触兔兔1秒";
        String sub2 = "情书";
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        index = string.indexOf(sub2);
        endIndex = index + sub2.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int grantResult : grantResults) {
            if (grantResult != PackageManager.PERMISSION_DENIED) {
                toSend();
            }
        }
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, GiftDataActivity.this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理动画
        if (mLottieView != null) {
            mLottieView.cancelAnimation();
        }

        // 清理 MediaPlayer
        if (mPlayer != null) {
            try {
                if (mPlayer.isPlaying()) {
                    mPlayer.stop();
                }
                mPlayer.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
            mPlayer = null;
        }

        // 清理 ShakeMonitor
        if (mShakeMonitor != null) {
            mShakeMonitor.stop();
            mShakeMonitor = null;
        }

        // 清理 BluetoothManage 监听器
        BluetoothManage.getInstance().setConnectSuccessListener(null);
        BluetoothManage.getInstance().stayIn(false);

        // 清理对话框
        if (newUserGiftDialog != null && newUserGiftDialog.isShowing()) {
            try {
                newUserGiftDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
            newUserGiftDialog = null;
        }

        // 清理分享对话框
        if (commonShareDialog != null && commonShareDialog.isShowing()) {
            try {
                commonShareDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
            commonShareDialog = null;
        }

        // 清理 Glide 请求
        try {
            Glide.with(getApplicationContext()).clear(mMainIv);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 清理 Facebook 回调
        facebookCallback = null;

        // 注销事件监听
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        // 检查 Activity 是否已销毁，防止内存泄露
        if (!isFinishing() && !isDestroyed() && newUserGiftDialog != null) {
            newUserGiftDialog.show();
        }
    }

    /**
     * 寄语发送成功
     * ContactsSelectActivity
     */
    @EventInject(eventType = S.E.E_GIFT_SEND_SUCCEED, runThread = TaskType.UI)
    public void sendSucceed(EventData data) {
        finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    public void onConnectSuccessd() {
        mHandler.postDelayed(() -> BluetoothManage.getInstance().stayIn(true), 2000);
    }
}
