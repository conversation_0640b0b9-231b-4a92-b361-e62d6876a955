package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.FrameLayout.LayoutParams;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.Sedentary;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.WheelView;
import com.umeng.analytics.MobclickAgent;

import java.util.Arrays;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 久坐提醒页面 负责久坐提醒相关设置
 *
 * <AUTHOR>
 * @date 2015-2015年7月10日
 */
public class SedentaryReminderActivity extends BaseActivity implements OnClickListener {
    public final static int DEFAULT_SEDENTARY_TIME = 60;

    // 久坐提醒人偶
    @BindView(R.id.sedentary_reminder_info_iv)
    ImageView sedentary_reminder_info_iv;

    // 久坐提醒开关按钮
    @BindView(R.id.call_switch_cb)
    CheckBox call_switch_cb;

    // 久坐提醒开关title
    @BindView(R.id.call_switch_title_tv)
    TextView call_switch_title_tv;

    @BindView(R.id.call_switch_info_tv)
    TextView call_switch_info_tv;

    // 久坐时长提醒间隔TV
    @BindView(R.id.reminder_interval_value_tv)
    TextView reminder_interval_value_tv;

    @BindView(R.id.sendentary_setting_content)
    LinearLayout sendentary_setting_content;

    // 开始提醒时间tv
    @BindView(R.id.start_time_value_tv)
    TextView start_time_value_tv;

    // 结束提醒时间tv
    @BindView(R.id.stop_time_value_tv)
    TextView stop_time_value_tv;

    // 重复提醒选择（星期一）
    @BindView(R.id.repeat_remind_cb_mon)
    CheckBox repeat_remind_cb_mon;

    // 重复提醒选择（星期二）
    @BindView(R.id.repeat_remind_cb_tue)
    CheckBox repeat_remind_cb_tue;

    // 重复提醒选择（星期三）
    @BindView(R.id.repeat_remind_cb_wed)
    CheckBox repeat_remind_cb_wed;

    // 重复提醒选择（星期四）
    @BindView(R.id.repeat_remind_cb_thur)
    CheckBox repeat_remind_cb_thur;

    // 重复提醒选择（星期五）
    @BindView(R.id.repeat_remind_cb_fri)
    CheckBox repeat_remind_cb_fri;

    // 重复提醒选择（星期六）
    @BindView(R.id.repeat_remind_cb_sat)
    CheckBox repeat_remind_cb_sat;

    // 重复提醒选择（星期天）
    @BindView(R.id.repeat_remind_cb_sun)
    CheckBox repeat_remind_cb_sun;

    @BindView(R.id.repeat_remind_ll)
    LinearLayout repeat_remind_ll;

    private boolean[] repeat_remind_date_record = new boolean[7];

    private Sedentary sedentary;

    JewelryNotifyModel nowSetModel;

    @BindView(R.id.long_vibration_tv)
    TextView mLongVibrationTv;
    @BindView(R.id.short_vibration_tv)
    TextView mShortVibrationTv;

    @BindView(R.id.sedentary_reminder_color_library_rv)
    RecyclerView colorLibraryRecyclerView;

    private CustomColorLibraryAdapter colorLibraryAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sedentary_reminder);
        ButterKnife.bind(this);
        initData();
        setOnClick();

        initNotifys();
        if (TextUtils.equals(getIntent().getStringExtra("HomeSedentaryHolder"), "open")) {
            if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                ToastUtils.showLong(this, R.string.error_jewelry_connect);
                return;
            }
            openOutside();
            sendentary_setting_content.setVisibility(View.VISIBLE);
            call_switch_cb.setChecked(true);
            call_switch_title_tv.setText(R.string.notify_on);
        }
    }

    private void initNotifys() {
        mLongVibrationTv.setOnClickListener(this);
        mShortVibrationTv.setOnClickListener(this);


        nowSetModel = NotifyUtil.getSedentaryNotifyModel(this);
        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));
                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));
                setTextColorBtn(false);
                break;
        }

        int spanCount = BleParams.isCtJewlery() ? 7 : 6;
        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(this, spanCount));
        colorLibraryAdapter = new CustomColorLibraryAdapter(nowSetModel.getFlashColor(), spanCount,false,false);

        colorLibraryAdapter.setOnItemClickListener((adapter, view, position) -> {
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);

            if (colorLibraryBean != null) {
                nowSetModel.setFlashColor(colorLibraryBean.getColor());//颜色名字
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                saveNowModel(false);
            }
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_EDIT_COLOR_ALL);
        });
        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationTv.setTextColor(0xffffffff);
            mShortVibrationTv.setTextColor(0xde000000);
        } else {
            mShortVibrationTv.setTextColor(0xffffffff);
            mLongVibrationTv.setTextColor(0xde000000);
        }
    }

    /**
     * 初始化用户久坐数据
     */
    private void initData() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.sedentary_reminder);

        call_switch_info_tv.setText(R.string.sedentary_reminder_switch_info);

        sedentary = new Sedentary(PreferencesUtils.getBoolean(this,
                NotifyUtil.SEDENTARY_SWITCH_KEY, NotifyUtil.DEFAULT_SEDENTARY_STATUS), PreferencesUtils.getInt(
                this, "sitWhenlong", DEFAULT_SEDENTARY_TIME), PreferencesUtils.getLong(this,
                "startTime", DateUtil.getStringToDate("HH:mm", "09:00")),
                PreferencesUtils.getLong(this, "stopTime",
                        DateUtil.getStringToDate("HH:mm", "18:00")),
                PreferencesUtils.getString(this, "repeatRemind", "01234"));

        if (sedentary.isOpen()) {
            call_switch_title_tv
                    .setText(R.string.notify_on);
        } else {
            call_switch_title_tv
                    .setText(R.string.notify_off);
        }

        if (ToTwooApplication.owner.getGender() == 0) {
            sedentary_reminder_info_iv
                    .setImageResource(R.drawable.sedentary_boy);
        } else {
            sedentary_reminder_info_iv
                    .setImageResource(R.drawable.sedentary_gril);
        }
        call_switch_cb.setChecked(sedentary.isOpen());
        if (!sedentary.isOpen()) {
            sendentary_setting_content.setVisibility(View.GONE);
        }
        String value = getResources().getString(R.string.reminder_interval);
        @SuppressLint("StringFormatMatches") String mm = String.format(value, sedentary.getSitWhenlong());
        reminder_interval_value_tv.setText(mm);

        start_time_value_tv.setText(DateUtil.getDateToString("HH:mm",
                sedentary.getStartTime()));

        stop_time_value_tv.setText(DateUtil.getDateToString("HH:mm",
                sedentary.getStopTime()));
        // 设置重复提醒数据
        char[] c = sedentary.getRepeatRemind().toCharArray();
        for (int i = 0; i < c.length; i++) {
            int parseInt = Integer.parseInt(String.valueOf(c[i]));
            repeat_remind_date_record[parseInt] = true;
//            parseInt = parseInt == 0 ? 7 : parseInt;
            ((CheckBox) repeat_remind_ll.getChildAt(parseInt)).setChecked(true);
            ((CheckBox) repeat_remind_ll.getChildAt(parseInt)).setTextColor(getResources().getColor(R.color.white));
        }

    }

    private void openOutside() {
        sedentary.setRepeatRemind(
                PreferencesUtils.getString(SedentaryReminderActivity.this, "repeatRemind", "01234"));
        BluetoothManage.getInstance().setSedentaryReminder(sedentary);
        PreferencesUtils.put(SedentaryReminderActivity.this,
                NotifyUtil.SEDENTARY_SWITCH_KEY, true);
    }

    private void setOnClick() {
        // 开关checkbox
        OnCheckedChangeListener listener = new OnCheckedChangeListener() {

            @Override
            public void onCheckedChanged(CompoundButton buttonView, final boolean isChecked) {
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(SedentaryReminderActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                LogUtils.e("aab isChecked = " + isChecked);
                if (!isChecked) {
                    // 蓝牙协议层表示关闭久坐提醒
                    sedentary.setRepeatRemind("");
                } else {
                    sedentary.setRepeatRemind(
                            PreferencesUtils.getString(SedentaryReminderActivity.this, "repeatRemind", "01234"));
                }

                nowSetModel.setNotifySwitch(isChecked);
                BluetoothManage.getInstance().setSedentaryReminder(sedentary);
                PreferencesUtils.put(SedentaryReminderActivity.this,
                        NotifyUtil.SEDENTARY_SWITCH_KEY, isChecked);
            }
        };
        call_switch_cb.setOnCheckedChangeListener(listener);

        OnClickListener repeat_remind_cb_cklisn = new OnClickListener() {
            @Override
            public void onClick(View v) {
                CheckBox checkBox = (CheckBox) v;
                if (checkBox.isChecked()) {
                    checkBox.setTextColor(getResources().getColor(R.color.white));
                } else {
                    checkBox.setTextColor(getResources().getColor(R.color.text_color_gray_99));
                }
                repeat_remind_date_record[Integer.parseInt((String) v.getTag())] = !repeat_remind_date_record[Integer
                        .parseInt((String) v.getTag())];

                commitRepeatRemindToSp();

            }
        };

        repeat_remind_cb_mon.setOnClickListener(repeat_remind_cb_cklisn);
        repeat_remind_cb_tue.setOnClickListener(repeat_remind_cb_cklisn);
        repeat_remind_cb_wed.setOnClickListener(repeat_remind_cb_cklisn);
        repeat_remind_cb_thur.setOnClickListener(repeat_remind_cb_cklisn);
        repeat_remind_cb_fri.setOnClickListener(repeat_remind_cb_cklisn);
        repeat_remind_cb_sat.setOnClickListener(repeat_remind_cb_cklisn);
        repeat_remind_cb_sun.setOnClickListener(repeat_remind_cb_cklisn);

    }

    @Override
    protected void onResume() {
        super.onResume();

        BluetoothManage.getInstance().connectedStatus();
    }

    @OnClick({R.id.sit_whenlong_rl, R.id.start_time_rl, R.id.stop_time_rl,
            R.id.notify_switch_click_item, R.id.long_vibration_tv,
            R.id.short_vibration_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.sit_whenlong_rl:
                showSitWhenlongDialog();
                break;
            case R.id.start_time_rl:
                showSwitchTimeDialog(R.string.statr_time, start_time_value_tv,
                        "startTime", DateUtil.getStringToDate("HH:mm", "09:00"));
                break;
            case R.id.stop_time_rl:
                showSwitchTimeDialog(R.string.stop_time, stop_time_value_tv,
                        "stopTime", DateUtil.getStringToDate("HH:mm", "18:00"));
                break;
            case R.id.notify_switch_click_item:
                boolean checked = !call_switch_cb.isChecked();
                call_switch_cb.setChecked(checked);
                call_switch_title_tv.setText(checked ? R.string.notify_on : R.string.notify_off);
                //切换时候的动画
                Animation anim = AnimationUtils.loadAnimation(SedentaryReminderActivity.this, checked ? R.anim.layout_open : R.anim.layout_close);
                if (checked) {
                    sendentary_setting_content.setVisibility(View.VISIBLE);
                } else {
                    anim.setAnimationListener(new Animation.AnimationListener() {
                        @Override
                        public void onAnimationStart(Animation animation) {
                        }

                        @Override
                        public void onAnimationEnd(Animation animation) {
                            sendentary_setting_content.setVisibility(View.GONE);
                        }

                        @Override
                        public void onAnimationRepeat(Animation animation) {

                        }
                    });
                }
                sendentary_setting_content.startAnimation(anim);
                break;
            case R.id.long_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(true);
                break;
            case R.id.short_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(false);
                break;
        }
    }

    private void saveNowModel(boolean isSwitch) {
        if (isSwitch) {
            final Sedentary sed = nowSetModel.isNotifySwitch() ? Sedentary.getSedentaryData(this) : new Sedentary(false, 0, 0, 0, "");

            BluetoothManage.getInstance().setSedentaryReminder(sed);
            NotifyUtil.setSedentaryNotify(SedentaryReminderActivity.this, nowSetModel);

        } else {
            BluetoothManage.getInstance().setSedentaryNotify(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
            NotifyUtil.setSedentaryNotify(SedentaryReminderActivity.this, nowSetModel);
        }
    }

//    /**
//     * 久坐时长提醒间隔时间选择DiaLog
//     */
//    private void showSitWhenlongDialog() {
//        final CustomDialog dialog = new CustomDialog(this);
//        dialog.setTitle(R.string.sit_whenlong);
//        LinearLayout layout = new LinearLayout(this);
//        layout.setLayoutParams(new LayoutParams(
//                ViewGroup.LayoutParams.MATCH_PARENT,
//                ViewGroup.LayoutParams.WRAP_CONTENT));
//        layout.setOrientation(LinearLayout.HORIZONTAL);
//
//        layout.setPadding(Apputils.dp2px(this, 20), 0,
//                Apputils.dp2px(this, 20), 0);
//        final WheelView wheelView = new WheelView(this);
//        wheelView.setLayoutParams(new LinearLayout.LayoutParams(
//                ViewGroup.LayoutParams.MATCH_PARENT,
//                ViewGroup.LayoutParams.WRAP_CONTENT));
//        wheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
//        wheelView.setItems(Arrays.asList(ConfigData.SIT_WHENLONG_ARRAY), 3,
//                " min.");
//        // 获取当前设置的时间去设置wheelview
//        final int min = PreferencesUtils.getInt(this, "sitWhenlong", SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME);
//        wheelView.setSeletion((min - Integer
//                .parseInt(ConfigData.SIT_WHENLONG_ARRAY[0])) / 10);
//        layout.addView(wheelView);
//        dialog.setMainLayoutView(layout);
//        // dialog.setMessage(R.string.totwoo_send_instruction);
//        dialog.setNegativeButtonText(R.string.give_up);
//        dialog.setPositiveButton(R.string.save, new OnClickListener() {
//            @Override
//            public void onClick(View v) {
////                ToastUtils.showDebug(SedentaryReminderActivity.this,
////                        wheelView.getSeletedItem(), 3000);
//                String value = getResources().getString(
//                        R.string.reminder_interval);
//                String mm = String.format(value, wheelView.getSeletedItem());
//                reminder_interval_value_tv.setText(mm);
//                int parseInt = Integer.parseInt(wheelView.getSeletedItem());
//
//                sedentary.setSitWhenlong(parseInt);
//                //测试设为1分钟一次
////                sedentary.setSitWhenlong(1);
//                BluetoothManage.getInstance().setSedentaryReminder(sedentary);
//                PreferencesUtils.put(SedentaryReminderActivity.this,
//                        "sitWhenlong", sedentary.getSitWhenlong());
//
//                dialog.dismiss();
//            }
//        });
//        dialog.show();
//    }

    /**
     * 久坐时长提醒间隔时间选择DiaLog
     */
    private void showSitWhenlongDialog(){
        CustomBottomDialog dialog = new CustomBottomDialog(this);
        dialog.setTitle(R.string.sit_whenlong);
        LinearLayout layout = new LinearLayout(this);
        layout.setLayoutParams(new LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        layout.setOrientation(LinearLayout.HORIZONTAL);

        layout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView wheelView = new WheelView(this);
        wheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        wheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        wheelView.setItems(Arrays.asList(ConfigData.SIT_WHENLONG_ARRAY), 3,
                " min.");
        // 获取当前设置的时间去设置wheelview
        final int min = PreferencesUtils.getInt(this, "sitWhenlong", SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME);
        wheelView.setSeletion((min - Integer
                .parseInt(ConfigData.SIT_WHENLONG_ARRAY[0])) / 10);
        layout.addView(wheelView);
        dialog.setMainView(layout);
        dialog.setSaveClick(getString(R.string.save), v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String value = getResources().getString(
                    R.string.reminder_interval);
            String mm = String.format(value, wheelView.getSeletedItem());
            reminder_interval_value_tv.setText(mm);
            int parseInt = Integer.parseInt(wheelView.getSeletedItem());

            sedentary.setSitWhenlong(parseInt);
            //测试设为1分钟一次
//                sedentary.setSitWhenlong(1);
            BluetoothManage.getInstance().setSedentaryReminder(sedentary);
            PreferencesUtils.put(SedentaryReminderActivity.this,
                    "sitWhenlong", sedentary.getSitWhenlong());

            dialog.dismiss();
        });
        dialog.show();
    }

//    /**
//     * 久坐时长提醒开始或结束时间选择DiaLog
//     *
//     * @param tv
//     */
//    private void showSwitchTimeDialog(int resId, final TextView tv,
//                                      final String spKey, long defvalue) {
//        final CustomDialog dialog = new CustomDialog(this);
//        dialog.setTitle(resId);
//        RelativeLayout layout = (RelativeLayout) View.inflate(this,
//                R.layout.sedentary_reminder_start_time_dialog, null);
//        final WheelView hh_wl = (WheelView) layout.findViewById(R.id.hh_wl);
//        final WheelView mm_wl = (WheelView) layout.findViewById(R.id.mm_wl);
//        hh_wl.setItems(Arrays
//                        .asList(ConfigData.SEDENTARY_REMINDER_START_TIME_HH_ARRAY), 3,
//                null);
//        mm_wl.setItems(Arrays
//                        .asList(ConfigData.SEDENTARY_REMINDER_START_TIME_MM_ARRAY), 3,
//                null);
//        long time = PreferencesUtils.getLong(this, spKey, defvalue);
//        hh_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("HH", time)));
//        mm_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("mm", time)));
//        dialog.setMainLayoutView(layout);
//        // dialog.setMessage(R.string.totwoo_send_instruction);
//        dialog.setNegativeButtonText(R.string.give_up);
//        dialog.setPositiveButton(R.string.save, new OnClickListener() {
//            @Override
//            public void onClick(View v) {
////                ToastUtils.showDebug(SedentaryReminderActivity.this,
////                        hh_wl.getSeletedItem(), 3000);
//                String text = hh_wl.getSeletedItem() + ":"
//                        + mm_wl.getSeletedItem();
//
//                long selectUtc = DateUtil.getStringToDate("HH:mm", text);
//                switch (spKey) {
//                    case "startTime":
//                        long stopTime = PreferencesUtils.getLong(
//                                SedentaryReminderActivity.this, "stopTime",
//                                DateUtil.getStringToDate("HH:mm", "18:00"));
//                        // 开始时间必须早于结束时间
//                        if (selectUtc < stopTime) {
//                            // 时间段不能小于提醒间隔
//                            if ((stopTime - selectUtc) < PreferencesUtils.getInt(
//                                    SedentaryReminderActivity.this, "sitWhenlong",
//                                    SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME) * 1000 * 60) {
//                                ToastUtils.show(SedentaryReminderActivity.this,
//                                        R.string.time_range_morethan_sitWhenlong,
//                                        1000);
//                            } else {
//                                sedentary.setStartTime(selectUtc);
//
//                                BluetoothManage.getInstance().setSedentaryReminder(sedentary);
//                                PreferencesUtils.put(
//                                        SedentaryReminderActivity.this, spKey,
//                                        sedentary.getStartTime());
//                                tv.setText(text);
//                                dialog.dismiss();
//                            }
//                            // 开始时间和结束时间不能相同
//                        } else if (selectUtc == stopTime) {
//                            ToastUtils.show(SedentaryReminderActivity.this,
//                                    R.string.stoptime_same_starttime, 1000);
//                        } else {
//                            ToastUtils.show(SedentaryReminderActivity.this,
//                                    R.string.starttime_later_stoptime, 1000);
//                        }
//                        break;
//                    case "stopTime":
//                        long startTime = PreferencesUtils.getLong(
//                                SedentaryReminderActivity.this, "startTime",
//                                DateUtil.getStringToDate("HH:mm", "9:00"));
//                        // 停止时间不能早于开始时间
//                        if (selectUtc > startTime) {
//                            // 时间段不能小于提醒间隔
//                            if ((selectUtc - startTime) < PreferencesUtils.getInt(
//                                    SedentaryReminderActivity.this, "sitWhenlong",
//                                    SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME) * 1000 * 60) {
//                                ToastUtils.show(SedentaryReminderActivity.this,
//                                        R.string.time_range_morethan_sitWhenlong,
//                                        1000);
//                            } else {
//                                sedentary.setStopTime(selectUtc);
//
//                                BluetoothManage.getInstance().setSedentaryReminder(sedentary);
//                                PreferencesUtils.put(
//                                        SedentaryReminderActivity.this, spKey,
//                                        sedentary.getStopTime());
//                                tv.setText(text);
//                                dialog.dismiss();
//                            }
//                            // 开始时间和结束时间不能相同
//                        } else if (selectUtc == startTime) {
//                            ToastUtils.show(SedentaryReminderActivity.this,
//                                    R.string.stoptime_same_starttime, 1000);
//                        } else {
//                            ToastUtils.show(SedentaryReminderActivity.this,
//                                    R.string.stoptime_earlier_starttime, 1000);
//                        }
//                        break;
//                }
//
//            }
//        });
//        dialog.show();
//    }

    /**
     * 久坐时长提醒开始或结束时间选择DiaLog
     *
     * @param tv
     */
    private void showSwitchTimeDialog(int resId, final TextView tv,
                                      final String spKey, long defvalue) {
        final CustomBottomDialog dialog = new CustomBottomDialog(this);
        dialog.setTitle(resId);
        RelativeLayout layout = (RelativeLayout) View.inflate(this,
                R.layout.sedentary_reminder_start_time_dialog, null);
        final WheelView hh_wl = (WheelView) layout.findViewById(R.id.hh_wl);
        final WheelView mm_wl = (WheelView) layout.findViewById(R.id.mm_wl);
        hh_wl.setItems(Arrays
                        .asList(ConfigData.SEDENTARY_REMINDER_START_TIME_HH_ARRAY), 3,
                null);
        mm_wl.setItems(Arrays
                        .asList(ConfigData.SEDENTARY_REMINDER_START_TIME_MM_ARRAY), 3,
                null);
        long time = PreferencesUtils.getLong(this, spKey, defvalue);
        hh_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("HH", time)));
        mm_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("mm", time)));
        dialog.setMainView(layout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(getString(R.string.save), new OnClickListener() {
            @Override
            public void onClick(View v) {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        hh_wl.getSeletedItem(), 3000);
                String text = hh_wl.getSeletedItem() + ":"
                        + mm_wl.getSeletedItem();

                long selectUtc = DateUtil.getStringToDate("HH:mm", text);
                switch (spKey) {
                    case "startTime":
                        long stopTime = PreferencesUtils.getLong(
                                SedentaryReminderActivity.this, "stopTime",
                                DateUtil.getStringToDate("HH:mm", "18:00"));
                        // 开始时间必须早于结束时间
                        if (selectUtc < stopTime) {
                            // 时间段不能小于提醒间隔
                            if ((stopTime - selectUtc) < PreferencesUtils.getInt(
                                    SedentaryReminderActivity.this, "sitWhenlong",
                                    SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME) * 1000 * 60) {
                                ToastUtils.show(SedentaryReminderActivity.this,
                                        R.string.time_range_morethan_sitWhenlong,
                                        1000);
                            } else {
                                sedentary.setStartTime(selectUtc);

                                BluetoothManage.getInstance().setSedentaryReminder(sedentary);
                                PreferencesUtils.put(
                                        SedentaryReminderActivity.this, spKey,
                                        sedentary.getStartTime());
                                tv.setText(text);
                                dialog.dismiss();
                            }
                            // 开始时间和结束时间不能相同
                        } else if (selectUtc == stopTime) {
                            ToastUtils.show(SedentaryReminderActivity.this,
                                    R.string.stoptime_same_starttime, 3000);
                        } else {
                            ToastUtils.show(SedentaryReminderActivity.this,
                                    R.string.starttime_later_stoptime, 3000);
                        }
                        break;
                    case "stopTime":
                        long startTime = PreferencesUtils.getLong(
                                SedentaryReminderActivity.this, "startTime",
                                DateUtil.getStringToDate("HH:mm", "9:00"));
                        // 停止时间不能早于开始时间
                        if (selectUtc > startTime) {
                            // 时间段不能小于提醒间隔
                            if ((selectUtc - startTime) < PreferencesUtils.getInt(
                                    SedentaryReminderActivity.this, "sitWhenlong",
                                    SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME) * 1000 * 60) {
                                ToastUtils.show(SedentaryReminderActivity.this,
                                        R.string.time_range_morethan_sitWhenlong,
                                        1000);
                            } else {
                                sedentary.setStopTime(selectUtc);

                                BluetoothManage.getInstance().setSedentaryReminder(sedentary);
                                PreferencesUtils.put(
                                        SedentaryReminderActivity.this, spKey,
                                        sedentary.getStopTime());
                                tv.setText(text);
                                dialog.dismiss();
                            }
                            // 开始时间和结束时间不能相同
                        } else if (selectUtc == startTime) {
                            ToastUtils.show(SedentaryReminderActivity.this,
                                    R.string.stoptime_same_starttime, 1000);
                        } else {
                            ToastUtils.show(SedentaryReminderActivity.this,
                                    R.string.stoptime_earlier_starttime, 1000);
                        }
                        break;
                }

            }
        });
        dialog.show();
    }

    /**
     * 提交重复提醒日期数据到Sp
     */
    private void commitRepeatRemindToSp() {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < repeat_remind_date_record.length; i++) {
            if (repeat_remind_date_record[i]) {
                sb.append(i);
            }
        }

        sedentary.setRepeatRemind(sb.toString());
        BluetoothManage.getInstance().setSedentaryReminder(sedentary);
        PreferencesUtils.put(SedentaryReminderActivity.this, "repeatRemind", sedentary.getRepeatRemind());

    }

//	// 测试久坐提醒
//	private void sendSR(int action) {
//		Intent intent = new Intent(SedentaryReminderReceiver.RECEIVER_ACTION);
//		intent.putExtra("action", action);
////		sendBroadcast(intent);
//
//        EventBus.getDefault().post(intent);
//	}
//
//
//	// 测试久坐提醒
//	private void sendSR(int action, int changeSitPoor) {
//		Intent intent = new Intent(SedentaryReminderReceiver.RECEIVER_ACTION);
//		intent.putExtra("action", action);
//		intent.putExtra("changeSitPoor", changeSitPoor);
////		sendBroadcast(intent);
//
//        EventBus.getDefault().post(intent);
//
//    }

    @Override
    protected void onDestroy() {
//        synchronousUserInfo();
        super.onDestroy();
    }

    // 同步数据到服务器
    private void synchronousUserInfo() {
        RequestParams params = HttpHelper.getBaseParams(true);
        sedentary = new Sedentary(PreferencesUtils.getBoolean(this,
                NotifyUtil.SEDENTARY_SWITCH_KEY, NotifyUtil.DEFAULT_SEDENTARY_STATUS), PreferencesUtils.getInt(
                this, "sitWhenlong", SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME), PreferencesUtils.getLong(this,
                "startTime", DateUtil.getStringToDate("HH:mm", "09:00")),
                PreferencesUtils.getLong(this, "stopTime",
                        DateUtil.getStringToDate("HH:mm", "18:00")),
                PreferencesUtils.getString(this, "repeatRemind", "01234"));
        params.addFormDataPart("is_on", sedentary.isOpen() + "");
        params.addFormDataPart("interval", sedentary.getSitWhenlong() + "");
        params.addFormDataPart("start",
                DateUtil.getDateToString("HH:mm:ss", sedentary.getStartTime()));
        params.addFormDataPart("end",
                DateUtil.getDateToString("HH:mm:ss", sedentary.getStopTime()));
        StringBuffer repeat_day = new StringBuffer("0");
        for (int i = repeat_remind_date_record.length; i > 0; i--) {
            if (repeat_remind_date_record[i - 1]) {
                repeat_day.append("1");
            } else {
                repeat_day.append("0");
            }
        }
        int src = Integer.parseInt(repeat_day.toString(), 2);// 将二级制转换为int

        params.addFormDataPart("repeat_day", src + "");
        HttpRequest.post(
                HttpHelper.URL_UPDATE_USER_OPTION
                        + ToTwooApplication.owner.getTotwooId() + "/", params,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
//                        ToastUtils.showDebug(SedentaryReminderActivity.this,
//                                "成功", 2000);
                    }
                });
    }
}
