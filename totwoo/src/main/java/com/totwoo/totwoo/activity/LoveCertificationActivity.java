package com.totwoo.totwoo.activity;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;

import com.blankj.utilcode.util.BarUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.CertificationBean;
import com.totwoo.totwoo.bean.CertificationInfo;
import com.totwoo.totwoo.bean.RankInfoBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.databinding.ActivityLoveCertificationBinding;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CertificationLevelDialog;
import com.totwoo.totwoo.widget.CertificationShareDialog;
import com.totwoo.totwoo.widget.CertificationTargetDialog;
import com.totwoo.totwoo.widget.CommonShareType;
import com.totwoo.totwoo.widget.CustomMiddleTextDialog;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import rx.Observer;

public class LoveCertificationActivity extends BaseActivity implements SubscriberListener {
    public static final String RANK_INFO = "rank_info";

    CertificationAdapter certificationAdapter;
    RankInfoBean rankInfoBean;
    private FacebookCallback<Sharer.Result> facebookCallback;

    private ActivityLoveCertificationBinding binding;

    private int level = 0;

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityLoveCertificationBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        InjectUtils.injectActivity(this);


        ArrayList<CertificationBean> certificationBeans = CommonArgs.getCerList();

        binding.certificationRv.setLayoutManager(new GridLayoutManager(LoveCertificationActivity.this, 3) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        certificationAdapter = new CertificationAdapter(certificationBeans);
        binding.certificationRv.setAdapter(certificationAdapter);
        certificationAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (position < level) {
                showLevelDialog(certificationBeans.get(position));
            } else {
                showTargetDialog(certificationBeans.get(position));

            }
        });

        certificationTargetDialog = new CertificationTargetDialog(this, v -> certificationTargetDialog.dismiss());
        rankInfoBean = getIntent().getParcelableExtra(RANK_INFO);
        if (rankInfoBean != null) {
            getData();
        } else {
            HttpHelper.commonService.getRankInfo(ToTwooApplication.owner.getPairedId(), 100).compose(HttpHelper.rxSchedulerHelper()).subscribe(new Observer<HttpBaseBean<RankInfoBean>>() {
                @Override
                public void onCompleted() {

                }

                @Override
                public void onError(Throwable e) {
                    ToastUtils.showShort(LoveCertificationActivity.this, R.string.error_net);
                }

                @Override
                public void onNext(HttpBaseBean<RankInfoBean> rankInfoBeanHttpBaseBean) {
                    if (rankInfoBeanHttpBaseBean.getErrorCode() == 0 && rankInfoBeanHttpBaseBean.getData().getUserinfo() != null) {
                        rankInfoBean = rankInfoBeanHttpBaseBean.getData();
                        getData();
                    }
                }
            });
        }

        setTopLeftIcon(R.drawable.back_icon_white);
        setTopLeftOnclik(v -> finish());
        setTopRightIcon(R.drawable.certification_hint);
        setTopRightOnClick(v -> showHintDialog());

        getTopRightIcon().setScaleY(1.2f);
        getTopRightIcon().setScaleX(1.2f);
    }


    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopbarBackground(R.color.black);
        BarUtils.setStatusBarLightMode(this, false);
    }

    private void getData() {
//        setHeadsInfo(mImageMe, mImageOther);
        HttpHelper.commonService.getCertificationInfo(rankInfoBean.getConsonance_count()).compose(HttpHelper.rxSchedulerHelper()).subscribe(new Observer<HttpBaseBean<CertificationInfo>>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(HttpBaseBean<CertificationInfo> certificationInfoHttpBaseBean) {
                if (certificationInfoHttpBaseBean.getErrorCode() == 0) {
                    level = certificationInfoHttpBaseBean.getData().getGet_sign();
                    if (level == 0) {
                        binding.certificationHeadIv.setImageResource(R.drawable.lelvel_lock_1);
                    } else if (level <= 10 && level > 0) {
                        binding.certificationHeadIv.setImageResource(certificationAdapter.getData().get(level - 1).getResImg());
                    }


                    String text = getString(R.string.has_certification, level);
                    String key = level + "";
                    int index = text.indexOf(key);
                    binding.certificationCountTv.setText(setStyle(text,index, index + key.length()));

                    if (level < 10) {
                        int gapCount = certificationInfoHttpBaseBean.getData().getTarget_num() - rankInfoBean.getConsonance_count();
                        int currentCount = rankInfoBean.getConsonance_count() - certificationInfoHttpBaseBean.getData().getNum();
                        int needCount = certificationInfoHttpBaseBean.getData().getTarget_num() - certificationInfoHttpBaseBean.getData().getNum();
                        float percent = (float) currentCount / needCount;
                        LogUtils.e("aab percent = " + percent);

                    } else {
//                                mProgressCl.setVisibility(View.GONE);
//                                mFullTv.setVisibility(View.VISIBLE);
                    }
                    certificationAdapter.notifyDataSetChanged();
                }
            }
        });
    }

    private SpannableString setStyle(String string, int index, int endIndex) {
        try {
            SpannableString spannableString = new SpannableString(string);
            spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.white)), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spannableString;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new SpannableString("");
    }


    private CertificationTargetDialog certificationTargetDialog;

    private void showTargetDialog(CertificationBean certificationBean) {
        certificationTargetDialog.setCount(certificationBean.getLevelCount());
        certificationTargetDialog.setInfo(getResources().getString(certificationBean.getResName()));
        certificationTargetDialog.show();
    }


    private void showLevelDialog(CertificationBean certificationBean) {
        CertificationLevelDialog certificationLevelDialog = new CertificationLevelDialog(this);
        certificationLevelDialog.setCount(certificationBean.getLevelCount(), certificationBean.getResImg());
        certificationLevelDialog.setInfo(getResources().getString(certificationBean.getResName()));
        certificationLevelDialog.show();
    }

    private CustomMiddleTextDialog certificationHintDialog;

    private void showHintDialog() {
        if (certificationHintDialog == null) {
            certificationHintDialog = new CustomMiddleTextDialog(this);
            LinearLayout certificationHintTitle = (LinearLayout) View.inflate(this, R.layout.certification_hint_title, null);
            certificationHintDialog.setTitleView(certificationHintTitle);
            certificationHintDialog.setInfoText(getString(R.string.certification_level_hint_info));
            certificationHintDialog.setConfirmTv(getString(R.string.i_know), v -> certificationHintDialog.dismiss());
//            certificationHintDialog.setWidth(SizeUtils.dp2px(237));

        }
        certificationHintDialog.show();
    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        showNewUserGifDialog();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    private NewUserGiftDialog newUserGiftDialog;

    private void showNewUserGifDialog() {
        newUserGiftDialog = new NewUserGiftDialog(LoveCertificationActivity.this, v -> {
            if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_YESORNO_LUCKY_CLICK);
            } else {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_YESORNO_LUCKY_CLICK);
            }
            WebViewActivity.loadUrl(LoveCertificationActivity.this, HttpHelper.URL_GIFT, false);
            newUserGiftDialog.dismiss();
        }, v -> newUserGiftDialog.dismiss(), CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券", 88, 20), "立即抽奖");
        newUserGiftDialog.show();
    }

    private CertificationShareDialog certificationShareDialog;

    private void showShareDialog(int index) {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CERTIFICATION_SHARE);
        if (certificationShareDialog == null) {
            if (Apputils.systemLanguageIsChinese(LoveCertificationActivity.this)) {
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                types.add(CommonShareType.WEIBO);
                types.add(CommonShareType.QZONE);
                types.add(CommonShareType.QQ);
                certificationShareDialog = new CertificationShareDialog(LoveCertificationActivity.this, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareImageToWechat(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case WEIBO:
                            ShareUtilsSingleton.getInstance().shareImageToWeibo(LoveCertificationActivity.this, getPath(certificationShareDialog.getShareView()), "");
                            certificationShareDialog.dismiss();
                            break;
                        case QZONE:
                            ShareUtilsSingleton.getInstance().shareImageToQzone(getPath(certificationShareDialog.getShareView()), "");
                            certificationShareDialog.dismiss();
                            break;
                        case QQ:
                            ShareUtilsSingleton.getInstance().shareImageToQQ(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                    }

                });
                certificationShareDialog.setCanceledOnTouchOutside(false);
//                certificationShareDialog.setCustomTitle(CommonUtils.setNumberOrangeSpan(getResources().getString(R.string.share_text_head_info), 88, 17));
            } else {
                facebookCallback = new FacebookCallback<Sharer.Result>() {
                    @Override
                    public void onSuccess(Sharer.Result result) {
                        ToastUtils.showShort(LoveCertificationActivity.this, getResources().getString(R.string.share_complete));
                    }

                    @Override
                    public void onCancel() {
                        ToastUtils.showShort(LoveCertificationActivity.this, getResources().getString(R.string.share_cancel));
                    }

                    @Override
                    public void onError(FacebookException error) {
                        ToastUtils.showShort(LoveCertificationActivity.this, getResources().getString(R.string.share_error));
                    }
                };
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FACEBOOK);
                types.add(CommonShareType.TWITTER);
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                certificationShareDialog = new CertificationShareDialog(LoveCertificationActivity.this, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareImageToWechat(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case FACEBOOK:
                            ShareUtilsSingleton.getInstance().shareImageToFacebook(getPath(certificationShareDialog.getShareView()), LoveCertificationActivity.this, facebookCallback);
                            certificationShareDialog.dismiss();
                            break;
                        case TWITTER:
                            ShareUtilsSingleton.getInstance().shareImageToTwitter(getPath(certificationShareDialog.getShareView()), "");
                            certificationShareDialog.dismiss();
                            break;
                    }

                });
                certificationShareDialog.setCanceledOnTouchOutside(false);
//                certificationShareDialog.setCustomTitle(getResources().getString(R.string.share_text_head_info));
            }
            certificationShareDialog.setUserInfo(rankInfoBean.getUserinfo());
        }
        certificationShareDialog.setBackground(CommonArgs.mainImages[index]);
        certificationShareDialog.setContent(CommonArgs.contentImages[index]);
        try {
            certificationShareDialog.setInfo(getString(CommonArgs.CERTIFICATION_LEVEL_INFO_IDS[index]));
        } catch (Exception e) {
            e.printStackTrace();
        }

        certificationShareDialog.setCount(CommonArgs.CERTIFICATION_LEVEL_COUNT[index]);
        certificationShareDialog.show();
    }

    private String getPath(View view) {
        Bitmap snapShareBitmap = ShareUtilsSingleton.getBitmapByView(view);
        return FileUtils.saveBitmapFromSDCard(snapShareBitmap, "totwoo_cache_img_" + System.currentTimeMillis());
    }


    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    protected class CertificationAdapter extends BaseQuickAdapter<CertificationBean, BaseViewHolder> {
        public CertificationAdapter(ArrayList<CertificationBean> certificationBeans) {
            super(R.layout.certification_item_new, certificationBeans);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, CertificationBean item) {
            int position = helper.getLayoutPosition();
            helper.setText(R.id.certification_content_tv, item.getResName());

            ImageView imageView = helper.getView(R.id.certification_main_iv);
            if (position < level) {
                imageView.setImageResource(item.getResImg());
            } else {
                imageView.setImageResource(item.getResLockImg());
            }
        }
    }
}
