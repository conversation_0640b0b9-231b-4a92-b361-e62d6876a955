package com.totwoo.totwoo.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.AlarmCustomNotifyLogic;
import com.totwoo.totwoo.utils.CustomNotifyDbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/3/5.
 */

public class CustomNotifyListActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.custom_list_recycler)
    RecyclerView mRecycler;
    @BindView(R.id.custom_list_swipe)
    SwipeRefreshLayout mSwipeRefresh;
    List<CustomItemBean> beans;
    private boolean isLoading;
    private int currentPosition;
    private CustomItemAdapter customItemAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_custom_notify_list);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        initView();
    }

    private void initView() {
        mRecycler.setLayoutManager(new LinearLayoutManager(CustomNotifyListActivity.this, RecyclerView.VERTICAL, false));
//        mRecycler.setAdapter(new CustomItemAdapter());
        mSwipeRefresh.setOnRefreshListener(() -> {
            if (!isLoading) {
                getData();
            }
        });
        getData();
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopRightIcon(R.drawable.custom_list_add);
        setTopRightOnClick(v -> {
            startActivity(new Intent(CustomNotifyListActivity.this, AddCustomNotifyActivity.class).putExtra("from","list"));
//                try {
//                    CustomNotifyDbHelper.getInstance().testQuery();
//                } catch (DbException e) {
//                    e.printStackTrace();
//                }
        });
        setTopTitle(getString(R.string.custom_notify_list));
        setTopTitleColor(R.color.white);
    }

    private void getData() {
        isLoading = true;
        //talk_id,target_uid是爱的提醒需要的参数，这里传空就行
        HttpHelper.customService.getCustomList("","")
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<List<CustomItemBean>>>() {
                    @Override
                    public void onCompleted() {
                        AlarmCustomNotifyLogic.getInstance().notifyAlarm();
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(CustomNotifyListActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<List<CustomItemBean>> listHttpBaseBean) {
                        isLoading = false;
                        mSwipeRefresh.setRefreshing(false);
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            beans = listHttpBaseBean.getData();
                            if (customItemAdapter == null) {
                                customItemAdapter = new CustomItemAdapter(CustomNotifyListActivity.this);
                                mRecycler.setAdapter(customItemAdapter);
                            } else {
                                customItemAdapter.notifyDataSetChanged();
                            }
                        }
                    }
                });
    }

    private void delete() {
        HttpHelper.customService.deleteCustom(beans.get(currentPosition).getDefine_id(),"")
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(CustomNotifyListActivity.this, R.string.custom_notify_list_delete_fail);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            deleteSuccess();
                        }
                    }
                });

    }

    private void deleteSuccess() {
        CustomNotifyDbHelper.getInstance().deleteBean(beans.get(currentPosition).getDefine_id());
        beans.remove(currentPosition);
        if(beans.size() == 0){
            finish();
        }
        customItemAdapter.notifyDataSetChanged();
        AlarmCustomNotifyLogic.getInstance().notifyAlarm();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    public class CustomItemAdapter extends RecyclerView.Adapter<CustomItemAdapter.ViewHolder> {

        Context context;

        public CustomItemAdapter(Context context) {
            this.context = context;
        }

        @Override
        public int getItemCount() {
            return beans == null ? 0 : beans.size();
        }

        @Override
        public CustomItemAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.custom_list_item, parent, false);
            CustomItemAdapter.ViewHolder viewHolder = new CustomItemAdapter.ViewHolder(view);
            return viewHolder;
        }

        @Override
        public void onBindViewHolder(CustomItemAdapter.ViewHolder holder, final int position) {
            holder.mClParent.setOnClickListener(v -> {
                currentPosition = position;
                startActivity(new Intent(CustomNotifyListActivity.this, CustomNotifyEditActivity.class).putExtra("bean", beans.get(position)));
            });
            holder.mClParent.setOnLongClickListener(v -> {
                currentPosition = position;
                final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(CustomNotifyListActivity.this);
                commonMiddleDialog.setMessage(R.string.custom_notify_list_delete_hint);
                commonMiddleDialog.setSure(v1 -> {
                    delete();
                    commonMiddleDialog.dismiss();
                });
                commonMiddleDialog.setCancel(R.string.give_up);
                commonMiddleDialog.show();

                return true;
            });
            holder.mTvTitle.setText(beans.get(position).getTitle());

            switch (beans.get(position).getDefine_type()) {
                case 1:
                    holder.mTvTitle.setCompoundDrawablesRelativeWithIntrinsicBounds(context.getResources().getDrawable(R.drawable.custom_list_schdeule), null, null, null);
                    break;
                case 2:
                    holder.mTvTitle.setCompoundDrawablesRelativeWithIntrinsicBounds(context.getResources().getDrawable(R.drawable.custom_list_birthday), null, null, null);
                    break;
                case 3:
                    holder.mTvTitle.setCompoundDrawablesRelativeWithIntrinsicBounds(context.getResources().getDrawable(R.drawable.custom_list_ann), null, null, null);
                    break;
                case 4:
                    holder.mTvTitle.setCompoundDrawablesRelativeWithIntrinsicBounds(context.getResources().getDrawable(R.drawable.custom_list_other), null, null, null);
                    break;
            }

            switch (beans.get(position).getNotify_mode()) {
                case "RED":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_red);
                    break;
                case "PINK":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_pink);
                    break;
                case "YELLOW":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_yellow);
                    break;
                case "GREEN":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_green);
                    break;
                case "BLUE":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_blue);
                    break;
                case "PURPLE":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_purple);
                    break;
                case "WHITE":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_white);
                    break;
                case "CYAN":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_cyan);
                    break;
                case "ORANGE":
                    holder.mIvLight.setImageResource(R.drawable.remind_c_orange);
                    break;
                default:
                    holder.mIvLight.setImageResource(R.drawable.remind_c_white);
                    break;
            }

            if (beans.get(position).getIs_open() == 1) {
                holder.mIvLight.setVisibility(View.VISIBLE);
                holder.mIvVirbation.setVisibility(View.VISIBLE);
                holder.mTvClose.setVisibility(View.GONE);
            } else {
                holder.mIvLight.setVisibility(View.GONE);
                holder.mIvVirbation.setVisibility(View.GONE);
                holder.mTvClose.setVisibility(View.VISIBLE);
                holder.mTvClose.setText(R.string.notify_off);
            }

            if (TextUtils.equals(beans.get(position).getShock_type(), "short"))
                holder.mIvVirbation.setImageResource(R.drawable.remind_short_fill);
            else
                holder.mIvVirbation.setImageResource(R.drawable.remind_long);

        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mIvLight;
            ImageView mIvVirbation;
            TextView mTvTitle;
            TextView mTvClose;
            View mviewFill;
            ConstraintLayout mClParent;

            public ViewHolder(View itemView) {
                super(itemView);
                mIvLight = (ImageView) itemView.findViewById(R.id.custom_list_item_light);
                mIvVirbation = (ImageView) itemView.findViewById(R.id.custom_list_item_virbation);
                mTvTitle = (TextView) itemView.findViewById(R.id.custom_list_item_title);
                mTvClose = (TextView) itemView.findViewById(R.id.custom_list_item_close);
                mviewFill = itemView.findViewById(R.id.custom_list_item_view);
                mClParent = (ConstraintLayout) itemView.findViewById(R.id.custom_list_item_cl);
            }
        }
    }

    /**
     * 自定义提醒编辑页更新成功
     * CustomNotifyEditActivity
     */
    @EventInject(eventType = S.E.E_CUSTOM_UPDATE_SUCCESSED, runThread = TaskType.UI)
    public void updatePosition(EventData data) {
        CustomItemBean bean = (CustomItemBean) data;
//        beans.set(currentPosition, bean);
//        customItemAdapter.notifyDataSetChanged();
        getData();
//        LogUtils.e("aab bean.getDefine_time = " + bean.getDefine_time());
        CustomNotifyDbHelper.getInstance().update(bean);
        AlarmCustomNotifyLogic.getInstance().notifyAlarm();
    }

    /**
     * 自定义提醒添加也更新成功
     * AddCustomNotifyActivity
     */
    @EventInject(eventType = S.E.E_CUSTOM_ADD_SUCCESSED, runThread = TaskType.UI)
    public void add(EventData data) {
        getData();

    }

    /**
     * 自定义提醒编辑页删除成功
     * CustomNotifyEditActivity
     */
    @EventInject(eventType = S.E.E_CUSTOM_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void deletePosition(EventData data) {
//        beans.set(currentPosition,bean);
        deleteSuccess();
    }

    @Override
    protected void onDestroy() {
        InjectUtils.injectUnregisterListenerAll(this);
        super.onDestroy();
    }
}
