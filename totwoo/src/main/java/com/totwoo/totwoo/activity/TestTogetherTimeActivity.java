package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.bean.TogetherEventData;
import com.totwoo.totwoo.ble.BluetoothManage;

import butterknife.BindView;
import butterknife.ButterKnife;

public class TestTogetherTimeActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.get_time_tv)
    TextView mGetTimeTv;
    @BindView(R.id.set_time_tv)
    TextView mSetTimeTv;
    @BindView(R.id.step_tv)
    TextView mStepTv;
    @BindView(R.id.time_tv)
    TextView mTimeTv;
    @BindView(R.id.test_tv_1)
    TextView mTestTv1;
    @BindView(R.id.test_tv_2)
    TextView mTestTv2;
    private DownloadControlHandler downloadControlHandler;
    @Override
    protected  void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_together_time_test);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        mGetTimeTv.setOnClickListener(v -> BluetoothManage.getInstance().getTogetherTime());
        mSetTimeTv.setOnClickListener(v -> BluetoothManage.getInstance().setMacAddress("A1B2C3D4E5F6"));
//        byte[] index = packageSendPairedAddressData("A1B2C3D4E5F6");
//        LogUtils.e(" addWriteMessage data = " + BleUtils.bytesToHexString(index));
        downloadControlHandler = new DownloadControlHandler();
    }

    public static byte[] getBytes(int data) {
        byte[] bytes = new byte[1];
        bytes[0] = (byte) (data & 0xff);
        return bytes;
    }

    public static byte[] packageSendPairedAddressData(String address) {
        byte[] app_data = new byte[8];
        app_data[0] = (byte) (0xC5);
        app_data[1] = getBytes(Integer.parseInt(address.substring(0,2),16))[0];
        app_data[2] = getBytes(Integer.parseInt(address.substring(2,4),16))[0];
        app_data[3] = getBytes(Integer.parseInt(address.substring(4,6),16))[0];
        app_data[4] = getBytes(Integer.parseInt(address.substring(6,8),16))[0];
        app_data[5] = getBytes(Integer.parseInt(address.substring(8,10),16))[0];
        app_data[6] = getBytes(Integer.parseInt(address.substring(10,12),16))[0];
//        app_data[7] = (byte) getHexTotal(app_data);
        return app_data;
    }

    @EventInject(eventType = S.E.E_TOGETHER_TIME_DATA_RECEIVE, runThread = TaskType.UI)
    public void onEventSleepDataReceive(EventData data) {
        TogetherEventData eventData = (TogetherEventData) data;
        mStepTv.setText("在一起的步数： " + eventData.getStep() + "步");
        mTimeTv.setText("在一起的时间： " + eventData.getTime() + "分钟");
        mTestTv1.setText("测试数据1： " + eventData.getTest1());
        mTestTv2.setText("测试数据2： " + eventData.getTest2());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }


    private class DownloadThead extends HandlerThread{

        public DownloadThead(String name) {
            super(name);
        }

        @Override
        public void run() {
            super.run();
            //Do something
            downloadControlHandler.sendMessage(Message.obtain());
        }
    }

    private static class DownloadControlHandler extends Handler{
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
        }
    }
}
