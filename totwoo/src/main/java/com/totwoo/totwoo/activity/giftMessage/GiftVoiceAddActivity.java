package com.totwoo.totwoo.activity.giftMessage;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Message;
import android.provider.Settings;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.czt.mp3recorder.MP3Recorder;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.liulishuo.magicprogresswidget.MagicProgressBar;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.WeakReferenceHandler;
import com.totwoo.totwoo.widget.CustomDialog;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

public class GiftVoiceAddActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.gift_voice_time_pb)
    MagicProgressBar mTimeProgressBar;
    @BindView(R.id.gift_voice_count_tv)
    TextView mDiscountTimeTv;
    @BindView(R.id.gift_voice_record_iv)
    ImageView mRecordIv;
    @BindView(R.id.gift_voice_hint_tv)
    TextView mHintTv;
    @BindView(R.id.gift_voice_cancel_cl)
    ConstraintLayout mCancelCl;

    private DisCountHandler handler;
    private long perTime = 100;
    private long allTime = 0;
    private long endTime;
    Subscription reVideoStart;

    private MP3Recorder mRecorder = new MP3Recorder(new File(CommonArgs.CACHE_GIFT_AUDIO_PATH));
    private boolean reVideoing;
    private float actionDownY;

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gift_voice);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        mTimeProgressBar.setPercent(0.16f);
        handler = new DisCountHandler(GiftVoiceAddActivity.this);
        mRecordIv.setOnTouchListener((v, event) -> {
            if (!PermissionUtil.hasAudioPermission(GiftVoiceAddActivity.this) || !PermissionUtil.hasStoragePermission(GiftVoiceAddActivity.this)) {
                return true;
            }
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    actionDownY = event.getRawY();
                    //不显示拿不到区域
                    mRecordIv.animate().scaleX(1.2f).scaleY(1.2f).setDuration(200).start();
                    mHintTv.setText(R.string.gift_audio_up_cancel);
                    mHintTv.setCompoundDrawablesRelativeWithIntrinsicBounds(getResources().getDrawable(R.drawable.gift_voice_move_up), null, null, null);
                    mHintTv.setCompoundDrawablePadding(CommonUtils.dip2px(GiftVoiceAddActivity.this, 4));
                    mRecorder.setRecordSec(0);
                    endTime = System.currentTimeMillis() + 60000;
                    handler.sendEmptyMessageDelayed(0, perTime);
                    reVideoStart = Observable.timer(perTime, TimeUnit.MILLISECONDS, Schedulers.newThread())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Action1<Long>() {
                                @Override
                                public void call(Long aLong) {
                                    if (!reVideoing) {
                                        startRecord();
                                    }
                                    reVideoing = true;
                                }
                            });
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (event.getRawY() < actionDownY - 200) {
                        mCancelCl.setVisibility(View.VISIBLE);
                    } else {
                        mCancelCl.setVisibility(View.GONE);
                    }
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    mRecordIv.animate().scaleX(1f).scaleY(1f).setDuration(200).start();

                    if (!reVideoing) {
                        reVideoStart.unsubscribe();
                    }
                    if (event.getRawY() < actionDownY - 200) {
                        recordFinish();
                    } else if (allTime < 1300) {
                        recordFinish();
                        Toast.makeText(GiftVoiceAddActivity.this, getString(R.string.audio_re_time_low_toast), Toast.LENGTH_SHORT).show();
                    } else {
                        recordSuccess();
                    }
                    mHintTv.setText(R.string.audio_re_guide_longclick);
                    mHintTv.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null);
                    mRecordIv.setImageResource(R.drawable.gift_voice_default);
                    break;
            }
            return true;
        });

    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mRecorder.release();
        handler.removeCallbacksAndMessages(null);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    /**
     * 寄语编辑页删除
     * GiftInfoAddActivity
     */
    @EventInject(eventType = S.E.E_GIFT_DELETE_INFO, runThread = TaskType.UI)
    public void deleteInfo(EventData data) {
        finish();
    }

    /**
     * 寄语发送成功
     * ContactsSelectActivity
     */
    @EventInject(eventType = S.E.E_GIFT_SEND_SUCCEED, runThread = TaskType.UI)
    public void sendSucceed(EventData data) {
        finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    public class DisCountHandler extends WeakReferenceHandler<GiftVoiceAddActivity> {

        public DisCountHandler(GiftVoiceAddActivity giftVoiceAddActivity) {
            super(giftVoiceAddActivity);
        }

        @Override
        public void handleLiveMessage(Message msg) {
            if (allTime <= 60000) {
//                allTime -= perTime;
                allTime = 60000 - (endTime - System.currentTimeMillis());

                int discountTime = (int) (allTime / 1000);
                mDiscountTimeTv.setText(discountTime + "s");
                mTimeProgressBar.setPercent(percentCal(allTime) + 0.16f);

                handler.sendEmptyMessageDelayed(0, perTime);
            } else {
                mTimeProgressBar.setPercent(1f);
                mDiscountTimeTv.setText("60s");
                recordSuccess();
            }
        }
    }

    private float percentCal(long time) {
        float percent = (float) (time * 0.84 / 60000);
//        LogUtils.e("GiftVoiceAddActivity percent = " + percent);
        return (float) Math.round(percent * 100) / 100;
    }

    private boolean isSucceed = false;

    private void recordSuccess() {
        if (isSucceed) {
            return;
        }
        isSucceed = true;
        if (reVideoing) {
            stopRecord();
            reVideoing = false;
        }
        startActivity(new Intent(GiftVoiceAddActivity.this, GiftInfoAddActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_SOUND));
        mHandler.postDelayed(this::recordFinish, 1000);
    }

    private void recordFinish() {
        handler.removeCallbacksAndMessages(null);
        mTimeProgressBar.setPercent(0.16f);
        mDiscountTimeTv.setText("0s");
        mCancelCl.setVisibility(View.GONE);
    }

    /**
     * 开始录制
     */
    private void startRecord() {
        if (mRecorder.isRecording()) {
            Toast.makeText(this, R.string.make_card_reing_video, Toast.LENGTH_SHORT).show();
            return;
        }
        // 录制视频
        try {
            mRecorder.start();
        } catch (IOException e) {
            Toast.makeText(this, R.string.make_card_reing_video_failure, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 停止录制
     */
    private void stopRecord() {
        mRecorder.stop();
    }

    @Override
    protected void onResume() {
        super.onResume();
        isSucceed = false;
        allTime = 0;
    }

    private CustomDialog customDialog;

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int grantResult : grantResults) {
            if (grantResult == PackageManager.PERMISSION_DENIED) {
                if (customDialog == null) {
                    customDialog = new CustomDialog(this);
                }
                customDialog.setTitle(R.string.tips);
                customDialog.setPositiveButton(R.string.set_hint, v -> {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    customDialog.dismiss();
                });
                customDialog.setMessage(R.string.no_contact);
                if (!customDialog.isShowing()) {
                    customDialog.show();
                }
            }
        }
    }
}
