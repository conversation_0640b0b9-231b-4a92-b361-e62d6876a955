package com.totwoo.totwoo.activity.wish;

import com.chad.library.adapter.base.loadmore.LoadMoreView;
import com.totwoo.totwoo.R;

public class MLoadMoreView extends LoadMoreView {
    @Override
    public int getLayoutId() {
        return R.layout.load_more_view_m;
    }

    @Override
    protected int getLoadingViewId() {
        return R.id.load_more_loading_m;
    }

    @Override
    protected int getLoadFailViewId() {
        return R.id.load_more_fail_m;
    }

    @Override
    protected int getLoadEndViewId() {
        return R.id.load_more_end_m;
    }
}
