package com.totwoo.totwoo.activity;

import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.ApiException;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class ModifyPasswordActivity extends BaseActivity {
    @BindView(R.id.modify_password_clear_iv)
    ImageView modify_password_clear_iv;
    @BindView(R.id.modify_password_see_iv)
    ImageView modify_password_see_iv;
    @BindView(R.id.modify_password_again_clear_iv)
    ImageView modify_password_again_clear_iv;
    @BindView(R.id.modify_password_again_see_iv)
    ImageView modify_password_again_see_iv;
    @BindView(R.id.modify_previous_password_clear_iv)
    ImageView modify_previous_password_clear_iv;
    @BindView(R.id.modify_previous_password_see_iv)
    ImageView modify_previous_password_see_iv;
    @BindView(R.id.modify_previous_password_et)
    EditText modify_previous_password_et;
    @BindView(R.id.modify_password_et)
    EditText modify_password_et;
    @BindView(R.id.modify_password_again_et)
    EditText modify_password_again_et;
    @BindView(R.id.modify_password_finish_tv)
    TextView mFinishTv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_modify_password);
        ButterKnife.bind(this);



        modify_password_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    modify_password_clear_iv.setVisibility(View.VISIBLE);
                    setFinishTvClickable(!TextUtils.isEmpty(modify_password_again_et.getText().toString().trim()) &&
                            !TextUtils.isEmpty(modify_previous_password_et.getText().toString().trim()));
                } else {
                    modify_password_clear_iv.setVisibility(View.INVISIBLE);
                    setFinishTvClickable(false);
                }
            }
        });

        modify_password_again_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    modify_password_again_clear_iv.setVisibility(View.VISIBLE);
                    setFinishTvClickable(!TextUtils.isEmpty(modify_password_et.getText().toString().trim()) &&
                            !TextUtils.isEmpty(modify_previous_password_et.getText().toString().trim()));
                } else {
                    modify_password_again_clear_iv.setVisibility(View.INVISIBLE);
                    setFinishTvClickable(false);
                }
            }
        });

        modify_previous_password_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    modify_previous_password_clear_iv.setVisibility(View.VISIBLE);
                    setFinishTvClickable(!TextUtils.isEmpty(modify_password_et.getText().toString().trim()) &&
                            !TextUtils.isEmpty(modify_password_again_et.getText().toString().trim()));
                } else {
                    modify_previous_password_clear_iv.setVisibility(View.INVISIBLE);
                    setFinishTvClickable(false);
                }
            }
        });
    }

    private void setFinishTvClickable(boolean clickable) {
        if (clickable) {
            mFinishTv.setEnabled(true);
        } else {
            mFinishTv.setEnabled(false);
        }
    }

    @OnClick({R.id.modify_password_clear_iv, R.id.modify_password_see_iv, R.id.modify_password_again_clear_iv,
            R.id.modify_password_again_see_iv, R.id.modify_password_finish_tv, R.id.modify_previous_password_clear_iv,
            R.id.modify_previous_password_see_iv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.modify_password_clear_iv:
                modify_password_et.setText("");
                break;
            case R.id.modify_password_see_iv:
                hideOrShowPassword(modify_password_et, modify_password_see_iv);
                break;
            case R.id.modify_password_again_clear_iv:
                modify_password_again_et.setText("");
                break;
            case R.id.modify_password_again_see_iv:
                hideOrShowPassword(modify_password_again_et, modify_password_again_see_iv);
                break;
            case R.id.modify_previous_password_clear_iv:
                modify_previous_password_et.setText("");
                break;
            case R.id.modify_previous_password_see_iv:
                hideOrShowPassword(modify_previous_password_et, modify_previous_password_see_iv);
                break;
            case R.id.modify_password_finish_tv:
                confirm();
                break;
        }
    }

    private void hideOrShowPassword(EditText mCodeEt, ImageView mSeeIv) {
        if (mCodeEt.getInputType() != 128) {
            mSeeIv.setImageResource(R.drawable.password_eye_open_black);
            mCodeEt.setInputType(128);
        } else {
            mSeeIv.setImageResource(R.drawable.password_eye_close_black);
            mCodeEt.setInputType(129);
        }
        if (mCodeEt.getText() != null) {
            mCodeEt.setSelection(mCodeEt.getText().length());
        }
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopbarBackground(Color.TRANSPARENT);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
    }

    private void confirm() {
        String previous_password = modify_previous_password_et.getText().toString().trim();
        String password = modify_password_et.getText().toString().trim();
        String password_check = modify_password_again_et.getText().toString().trim();

        if (TextUtils.isEmpty(previous_password)) {
            ToastUtils.show(ModifyPasswordActivity.this, R.string.set_previous_password_empty, Toast.LENGTH_SHORT);
            return;
        } else if (TextUtils.isEmpty(password)) {
            ToastUtils.show(ModifyPasswordActivity.this, R.string.set_password_error_length, Toast.LENGTH_SHORT);
            return;
        } else if (TextUtils.isEmpty(password_check)) {
            ToastUtils.show(ModifyPasswordActivity.this, R.string.set_password_enter_again, Toast.LENGTH_SHORT);
            return;
        } else if (password.length() < 6 || password.length() > 16) {
            ToastUtils.show(ModifyPasswordActivity.this, R.string.set_password_error_length, Toast.LENGTH_SHORT);
            return;
        } else if (!CommonUtils.isPasswordValid(password)) {
            ToastUtils.show(ModifyPasswordActivity.this, R.string.set_password_error_length, Toast.LENGTH_SHORT);
            return;
        } else if (!TextUtils.equals(password, password_check)) {
            ToastUtils.show(ModifyPasswordActivity.this, R.string.set_password_error_different, Toast.LENGTH_SHORT);
            return;
        }
        HttpHelper.loginService.modifyPwd(CommonUtils.pwdEncode(previous_password), CommonUtils.pwdEncode(password), CommonUtils.pwdEncode(password_check))
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.show(ModifyPasswordActivity.this, R.string.error_net, Toast.LENGTH_SHORT);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> loginInfoBeanHttpBaseBean) {
                        if (loginInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            PreferencesUtils.put(ModifyPasswordActivity.this, CommonArgs.PREF_LAST_ENCODE_PASSWORD, CommonUtils.pwdEncode(password));
                            ToastUtils.show(ModifyPasswordActivity.this, R.string.modify_password_success, Toast.LENGTH_SHORT);
                            finish();
                        } else {
                            String errMsg = ApiException.getHttpErrMessage(loginInfoBeanHttpBaseBean.getErrorCode(), loginInfoBeanHttpBaseBean.getErrorMsg());
                            ToastUtils.show(ModifyPasswordActivity.this, errMsg, Toast.LENGTH_SHORT);
                        }
                    }
                });
    }
}
