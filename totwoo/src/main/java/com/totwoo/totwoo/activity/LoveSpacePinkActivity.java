package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.ToTwooApplication.baseContext;
import static com.totwoo.totwoo.activity.together.FootPrintSelectActivity.SAVE_SELECT_CITY_SUCCESS;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.google.gson.Gson;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.giftMessage.SendGiftGalleryActivity;
import com.totwoo.totwoo.activity.together.FootPrintSelectActivity;
import com.totwoo.totwoo.activity.together.MainTogetherActivity;
import com.totwoo.totwoo.bean.CertificationInfo;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.FootPrintHttpBean;
import com.totwoo.totwoo.bean.LoveSpaceInfo;
import com.totwoo.totwoo.bean.PeriodStateBean;
import com.totwoo.totwoo.bean.RankInfoBean;
import com.totwoo.totwoo.bean.RankUserInfosBean;
import com.totwoo.totwoo.bean.SleepDayData;
import com.totwoo.totwoo.bean.SleepUpdateBean;
import com.totwoo.totwoo.bean.TogetherBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.data.AlarmCustomNotifyLogic;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PopupMenuUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.SleepCalculateUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CommonShareType;
import com.totwoo.totwoo.widget.CustomTypefaceSpan;
import com.totwoo.totwoo.widget.LoveSpaceDialog;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.totwoo.totwoo.widget.TransProgressBar;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class LoveSpacePinkActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.love_space_constellation_ta_tv)
    TextView mConstellationTa;
    @BindView(R.id.love_space_constellation_me_tv)
    TextView mConstellationMe;
    @BindView(R.id.love_space_constellation_title_tv)
    TextView mConstellationTitle;
    @BindView(R.id.love_space_ratingBar)
    RatingBar mConstellationRb;
    @BindView(R.id.love_space_birth_tv)
    TextView mBirthTv;
    @BindView(R.id.love_space_birth_info)
    TextView mBirthInfo;
    @BindView(R.id.love_space_gift_info)
    TextView mGiftInfo;
    @BindView(R.id.love_space_constellation_info)
    TextView mConstellationInfoTv;
    @BindView(R.id.love_space_head_iv)
    ImageView mHeadIv;
    @BindView(R.id.love_space_sleep_cv)
    CardView mSleepCv;
    @BindView(R.id.love_space_constellation_cv)
    CardView mConstellationCv;
    @BindView(R.id.love_space_sleep_no_data)
    TextView mSleepNoData;
    @BindView(R.id.sleep_day_hour_tv)
    TextView mDayHourTv;
    @BindView(R.id.sleep_day_hour_text)
    TextView mDayHourText;
    @BindView(R.id.sleep_day_min_tv)
    TextView mDayMinTv;
    @BindView(R.id.sleep_day_min_text)
    TextView mDayMinText;
    @BindView(R.id.sleep_day_deep_hour_tv)
    TextView mDeepHourTv;
    @BindView(R.id.sleep_day_deep_hour_text)
    TextView mDeepHourText;
    @BindView(R.id.sleep_day_deep_min_tv)
    TextView mDeepMinTv;
    @BindView(R.id.sleep_day_deep_min_text)
    TextView mDeepMinText;
    @BindView(R.id.sleep_day_light_hour_tv)
    TextView mLightHourTv;
    @BindView(R.id.sleep_day_light_hour_text)
    TextView mLightHourText;
    @BindView(R.id.sleep_day_light_min_tv)
    TextView mLightMinTv;
    @BindView(R.id.sleep_day_light_min_text)
    TextView mLightMinText;
    @BindView(R.id.day_deep_sleep_tv)
    TextView mDeepSleepPercent;
    @BindView(R.id.day_light_sleep_tv)
    TextView mLightSleepPercent;
    @BindView(R.id.sleep_day_awake_tv)
    TextView mAwakeTv;
    @BindView(R.id.love_space_sleep_statement)
    TextView mStatementTv;
    @BindView(R.id.love_space_sleep_text)
    TextView mSleepTextTv;
    @BindView(R.id.love_space_sleep_line)
    View mSleepLine;
    @BindView(R.id.love_space_sleep_info_ll)
    LinearLayout mSleepInfo;
    @BindView(R.id.sleep_day_deep_cl)
    ConstraintLayout mDeepSleepCl;
    @BindView(R.id.sleep_day_light_cl)
    ConstraintLayout mLightSleepCl;
    @BindView(R.id.sleep_day_awake_cl)
    ConstraintLayout mAwakeCl;
    @BindView(R.id.love_space_sleep_me)
    TextView mSleepMeTv;

    @BindView(R.id.love_space_rank_week_tv)
    TextView mRankWeekTv;
    @BindView(R.id.love_space_rank_month_tv)
    TextView mRankMonthTv;
    @BindView(R.id.love_space_rank_total_tv)
    TextView mRankTotalTv;
    @BindView(R.id.love_space_rank_count_tv)
    TextView mRankCount;
    @BindView(R.id.love_space_rank_rank)
    TextView mRankRankTv;
    @BindView(R.id.love_space_rank_percent)
    TextView mRankPercentTv;
    @BindView(R.id.love_manage_me)
    ImageView mRankMe;
    @BindView(R.id.love_manage_other)
    ImageView mRankOther;

    @BindView(R.id.love_space_pre_lv_tv)
    TextView mCertificationPreTv;
    @BindView(R.id.love_space_suf_lv_tv)
    TextView mCertificationSufTv;
    @BindView(R.id.love_space_pre_lv_iv)
    TextView mCertificationCurrentIv;
    @BindView(R.id.love_space_suf_lv_iv)
    TextView mCertificationSufIv;
    @BindView(R.id.love_space_count_tv)
    TextView mCertificationCountTv;
    @BindView(R.id.love_space_gap_tv)
    TextView mCertificationGapTv;
    @BindView(R.id.love_space_level_progress_cl)
    ConstraintLayout mProgressCl;
    @BindView(R.id.love_space_mpb)
    TransProgressBar mMpb;
    @BindView(R.id.love_space_full_level_tv)
    TextView mFullTv;
    @BindView(R.id.heart_hurt_iv)
    ImageView mheartHurtIv;
    @BindView(R.id.love_space_period_info_tv)
    TextView mPeriodInfo;
    @BindView(R.id.love_space_content_sv)
    ScrollView mContentSv;

    @BindView(R.id.love_space_share_cl)
    ConstraintLayout mShareCl;
    @BindView(R.id.love_space_share_info_cl)
    ConstraintLayout mShareInfoCl;
    @BindView(R.id.love_space_share_me)
    ImageView mShareMe;
    @BindView(R.id.love_space_share_other)
    ImageView mShareOther;
    @BindView(R.id.love_space_share_name_tv)
    TextView mShareNameTv;
    @BindView(R.id.love_space_share_title_tv)
    TextView mShareTitleTv;
    @BindView(R.id.love_space_share_lv_iv)
    ImageView mShareLvIv;
    @BindView(R.id.love_space_share_time_title)
    TextView mShareTimeTitle;
    @BindView(R.id.love_space_share_time_text)
    TextView mShareTimeText;
    @BindView(R.id.love_space_share_totwoo_title)
    TextView mShareTotwooTitle;
    @BindView(R.id.love_space_share_totwoo_text)
    TextView mShareTotwooText;
    @BindView(R.id.love_space_share_rank_title)
    TextView mShareRankTitle;
    @BindView(R.id.love_space_share_rank_text)
    TextView mShareRankText;
    @BindView(R.id.love_space_share_time_iv)
    ImageView mShareTimeIv;
    @BindView(R.id.love_space_notify_info)
    TextView mNotifyInfo;
    @BindView(R.id.love_space_foot_info)
    TextView mFootInfo;
    @BindView(R.id.love_space_notify_cl)
    ConstraintLayout mNotifyCl;
    @BindView(R.id.love_space_period_cl)
    ConstraintLayout mPeriodCl;
    @BindView(R.id.love_space_foot_cl)
    ConstraintLayout mFootCl;
    @BindView(R.id.include_paired_head_layout)
    ConstraintLayout include_paired_head_layout;


    @BindView(R.id.love_name_tv)
    TextView loveNameTv;

    @BindView(R.id.week_arrow_down)
    View weekArrowDown;

    @BindView(R.id.month_arrow_down)
    View monthArrowDown;

    @BindView(R.id.total_arrow_down)
    View totalArrowDown;

//    @BindView(R.id.love_space_share_tv)
//    TextView shareTv;

    private boolean isSelectMe = false;
    private LoveSpaceInfo loveSpaceInfo;
    private ACache aCache;
    public static final String PAIRED_HEAD_URL = "paired_head_url";
    public static final String PAIRED_NAMES = "paired_names";
    private List<CustomItemBean> beans;

    private List<RankInfoBean> rankInfoBeans;

    private int selectedRankIndex = 0;

    private FacebookCallback<Sharer.Result> facebookCallback;
    private Gson gson;
    public static final int TO_SAVE_CITY = 1;

//    private int[] levelPres = {R.drawable.certification_pre_lv_1, R.drawable.certification_pre_lv_2, R.drawable.certification_pre_lv_3,
//            R.drawable.certification_pre_lv_4, R.drawable.certification_pre_lv_5, R.drawable.certification_pre_lv_6, R.drawable.certification_pre_lv_7,
//            R.drawable.certification_pre_lv_8, R.drawable.certification_pre_lv_9, R.drawable.certification_pre_lv_10};
//
//    private int[] levelSufs = {R.drawable.certification_suf_lv_1, R.drawable.certification_suf_lv_2, R.drawable.certification_suf_lv_3,
//            R.drawable.certification_suf_lv_4, R.drawable.certification_suf_lv_5, R.drawable.certification_suf_lv_6, R.drawable.certification_suf_lv_7,
//            R.drawable.certification_suf_lv_8, R.drawable.certification_suf_lv_9, R.drawable.certification_suf_lv_10};

    private int[] conIconRes = {R.drawable.love_space_shuiping_icon, R.drawable.love_space_shuangyu_icon,
            R.drawable.love_space_baiyang_icon, R.drawable.love_space_jinniu_icon,
            R.drawable.love_space_shuangzi_icon, R.drawable.love_space_juxie_icon,
            R.drawable.love_space_shizi_icon, R.drawable.love_space_chunv_icon,
            R.drawable.love_space_tiancheng_icon, R.drawable.love_space_tianxie_icon,
            R.drawable.love_space_sheshou_icon, R.drawable.love_space_mojie_icon
    };


    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_love_space_pink);
        ButterKnife.bind(this);
        InjectUtils.injectActivity(this);


//        include_paired_head_layout.setScaleX(1.8f);
//        include_paired_head_layout.setScaleY(1.8f);


        aCache = ACache.get(baseContext);
        gson = new Gson();
        getMainInfo();
        getPeriodInfo();
        selectRankInfo(0);
        getCertificationInfo();


        if (BleParams.isCodeBangle()) {
            mSleepMeTv.setVisibility(View.VISIBLE);
            mSleepLine.setVisibility(View.VISIBLE);
        } else {
            mSleepMeTv.setVisibility(View.GONE);
            mSleepLine.setVisibility(View.GONE);
        }
        if (!Apputils.systemLanguageIsChinese(LoveSpacePinkActivity.this)) {
            mFootCl.setVisibility(View.GONE);
//            shareTv.setVisibility(View.GONE);
            mConstellationCv.setVisibility(View.GONE);
        }

        mConstellationCv.setVisibility(View.GONE);

//        // TWO80,81 移除爱的提醒
//        if (BleParams.isSM2()) {
//            mNotifyCl.setVisibility(View.GONE);
//        }

        ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(CommonUtils.dip2px(LoveSpacePinkActivity.this, 35), CommonUtils.dip2px(LoveSpacePinkActivity.this, 35));
        layoutParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
        layoutParams.topToBottom = R.id.love_space_share_arrow;
        if (Apputils.systemLanguageIsChinese(LoveSpacePinkActivity.this)) {
            layoutParams.setMargins(CommonUtils.dip2px(LoveSpacePinkActivity.this, 55), CommonUtils.dip2px(LoveSpacePinkActivity.this, 17), 0, 0);
        } else {
            layoutParams.setMargins(CommonUtils.dip2px(LoveSpacePinkActivity.this, 15), CommonUtils.dip2px(LoveSpacePinkActivity.this, 17), 0, 0);
        }
        mShareTimeIv.setLayoutParams(layoutParams);




        setTopLeftIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
        setTopTitle(R.string.the_heart);
        getTopTitleView().setTextColor(getResources().getColor(R.color.black));

        setTopRightIcon(R.drawable.the_heart_manage_icon);

//        getTopBarBackgroundObject().setBackgroundColor(0xffff6379);
//        getTopBarBackgroundObject().setVisibility(View.VISIBLE);
//        getTopBarBackgroundObject().setAlpha(0);
//        getTopTitleView().setAlpha(0);
        setTopRightOnClick(v -> {
            try {
                startActivity(new Intent(LoveSpacePinkActivity.this, LoveManageActivity.class)
                        .putExtra(PAIRED_NAMES, loveNameTv.getText().toString())
                        .putExtra(PAIRED_HEAD_URL, rankInfoBeans.get(0).getUserinfo().getTarget().getHead_portrait()));
            } catch (Exception e) {
                startActivity(new Intent(LoveSpacePinkActivity.this, LoveManageActivity.class));
            }
        });

        addScrollListener();
    }


    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopbarBackground(R.color.transparent);
    }

    private void addScrollListener() {
        mContentSv.getViewTreeObserver().addOnScrollChangedListener(new ViewTreeObserver.OnScrollChangedListener() {
            @Override
            public void onScrollChanged() {
                int scrollY = mContentSv.getScrollY();
                // 根据滑动距离计算透明度
                int alpha = calculateAlpha(scrollY);

                // Edge-to-Edge模式下，状态栏已透明，只需设置TopBar背景色
                getTopBar().setBackgroundColor(Color.argb(alpha, 255, 246, 246));
            }
        });
    }


    // 计算透明度的方法
    private int calculateAlpha(int scrollY) {
        int maxHeight = PopupMenuUtil.dip2px(baseContext, 20);
        int alpha = (int) (255 * ((float) scrollY / maxHeight));
        return Math.min(Math.max(alpha, 0), 255);
    }


    @Override
    protected void onStart() {
        super.onStart();
        getSelectedCity();
    }


    @OnClick({R.id.love_space_constellation_ta_tv, R.id.love_space_constellation_me_tv, R.id.love_space_constellation_more, R.id.love_space_sleep_me,
            R.id.love_space_sleep_set, R.id.love_space_notify_cl, R.id.love_space_foot_cl, R.id.love_space_period_cl, R.id.love_space_rank_week_cl,
            R.id.love_space_rank_month_tv, R.id.love_space_rank_total_tv, R.id.love_space_totwoo_more, R.id.love_space_care_about_her,
            R.id.ll_love_space_count, R.id.love_space_level_progress_cl, R.id.love_space_sleep_care})
    protected void onClick(View view) {
        // 这里经常遇到各种数据异常导致的崩溃, 增加全局的异常捕获
        try {
            switch (view.getId()) {
                case R.id.love_space_constellation_ta_tv:
                    setIndicatorMe(false);
                    if (loveSpaceInfo != null && loveSpaceInfo.getUserinfo() != null && loveSpaceInfo.getConstellation() != null) {
                        setConstellInfo(loveSpaceInfo.getUserinfo().getTarget(), loveSpaceInfo.getConstellation().getTarget());
                    }
                    break;
                case R.id.love_space_constellation_me_tv:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_SEE_FORTUNE);
                    setIndicatorMe(true);
                    if (loveSpaceInfo != null && loveSpaceInfo.getUserinfo() != null && loveSpaceInfo.getConstellation() != null) {
                        setConstellInfo(loveSpaceInfo.getUserinfo().getSelf(), loveSpaceInfo.getConstellation().getSelf());
                    }
                    break;
                case R.id.love_space_constellation_more:
                    Intent i = new Intent(LoveSpacePinkActivity.this, ConstellationActivity.class);
                    if (!isSelectMe) {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_HIS_FORTUNEDETAIL);
                        i.putExtra("phone", ToTwooApplication.otherPhone);
                        if (loveSpaceInfo != null && loveSpaceInfo.getUserinfo() != null) {
                            if (TextUtils.equals(loveSpaceInfo.getUserinfo().getTarget().getSex(), "1")) {
                                i.putExtra("title", getString(R.string.love_space_her_horoscope));
                            } else {
                                i.putExtra("title", getString(R.string.love_space_his_horoscope));
                            }
                        } else {
                            i.putExtra("title", getString(R.string.love_space_his_horoscope));
                        }
                    } else {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_ME_FORTUNEDETAIL);
                    }
                    LoveSpacePinkActivity.this.startActivity(i);
                    break;
                case R.id.love_space_sleep_me:
                    startActivity(new Intent(LoveSpacePinkActivity.this, SleepStatisticActivity.class));
                    break;
                case R.id.love_space_sleep_set:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_SHESLEEP_REMINDSET);
                    startActivity(new Intent(LoveSpacePinkActivity.this, LoveSpaceNotifyStatusActivity.class).putExtra(CommonArgs.FROM_TYPE, LoveSpaceNotifyStatusActivity.SLEEP_TYPE));
                    break;
                case R.id.love_space_rank_week_cl:
                    selectRankInfo(0);
                    break;
                case R.id.love_space_rank_month_tv:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_THIRTYSORT);
                    selectRankInfo(1);
                    break;
                case R.id.love_space_rank_total_tv:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_ALLSORT);
                    selectRankInfo(2);
                    break;
                case R.id.love_space_notify_cl:
                    if (beans != null && beans.size() != 0) {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_SEE_ALLREMIND);
                        startActivity(new Intent(LoveSpacePinkActivity.this, LoveNotifyListActivity.class));
                    } else {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_ADD_LOVEREMIND);
                        startActivity(new Intent(LoveSpacePinkActivity.this, AddLoveNotifyActivity.class).putExtra(CommonArgs.FROM_TYPE, "space"));
                    }
                    break;
                case R.id.love_space_foot_cl:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PRINT_LOVESPACE);
                    TogetherBean togetherBean = gson.fromJson(aCache.getAsString(FOOT_PRINT_DATA), TogetherBean.class);
                    if (togetherBean != null && togetherBean.getList() != null && togetherBean.getList().size() > 0) {
                        if (rankInfoBeans != null && rankInfoBeans.size() > 0) {
                            startActivity(new Intent(LoveSpacePinkActivity.this, MainTogetherActivity.class)
                                    .putExtra(PAIRED_HEAD_URL, rankInfoBeans.get(0).getUserinfo().getTarget().getHead_portrait())
                                    .putExtra(PAIRED_NAMES, getString(R.string.love_space_share_name, rankInfoBeans.get(0).getUserinfo().getTarget().getNick_name(), rankInfoBeans.get(0).getUserinfo().getSelf().getNick_name())));
                        } else {
                            startActivity(new Intent(LoveSpacePinkActivity.this, MainTogetherActivity.class));
                        }
                    } else {
                        startActivityForResult(new Intent(LoveSpacePinkActivity.this, FootPrintSelectActivity.class), TO_SAVE_CITY);
                    }

                    break;
                case R.id.love_space_period_cl:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_HERMENREMINDSET);
                    startActivity(new Intent(LoveSpacePinkActivity.this, LoveSpaceNotifyStatusActivity.class).putExtra(CommonArgs.FROM_TYPE, LoveSpaceNotifyStatusActivity.PERIOD_TYPE));
                    break;

                case R.id.love_space_totwoo_more:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_ALLRECORDS);
                    if (loveSpaceInfo != null && loveSpaceInfo.getTotwoo() != null) {
                        startActivity(new Intent(LoveSpacePinkActivity.this, HistoryTwooActivity.class)
                                .putExtra(HistoryTwooActivity.TWOO_DAY, loveSpaceInfo.getFirst_consonance())
                                .putExtra(HistoryTwooActivity.TWOO_TIMES, loveSpaceInfo.getConsonance_count())
                                .putExtra(HistoryTwooActivity.RECEIVER_COUNT, String.valueOf(loveSpaceInfo.getTotwoo().getReceive_num()))
                                .putExtra(HistoryTwooActivity.SEND_COUNT, String.valueOf(loveSpaceInfo.getTotwoo().getSend_num()))
                        );
                    } else {
                        startActivity(new Intent(LoveSpacePinkActivity.this, HistoryTwooActivity.class));
                    }
                    break;
                case R.id.love_space_care_about_her:
                case R.id.love_space_sleep_care:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_HERMEN_CAREHIM);
                    TimInitBusiness.navToChat();
                    break;
//                case R.id.love_space_share_tv:
//                    showRankShareDialog();
//                    break;
                case R.id.love_space_share_cancel:
                    goneShare();
                    break;
                case R.id.love_space_level_progress_cl:
                case R.id.ll_love_space_count:
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CERTIFICATION_LOVESPACE);
                    Intent intent = new Intent(LoveSpacePinkActivity.this, LoveCertificationActivity.class);
                    if (rankInfoBeans.get(2).getGet_sign() >= 0) {
                        intent.putExtra(LoveCertificationActivity.RANK_INFO, rankInfoBeans.get(2));
                    }
                    startActivity(intent);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getMainInfo() {
        HttpHelper.commonService.getSpaceInfo(ToTwooApplication.owner.getPairedId())
                .compose(HttpHelper.<HttpBaseBean<LoveSpaceInfo>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<LoveSpaceInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoveSpacePinkActivity.this, R.string.error_net);
                    }

                    @SuppressLint("SetTextI18n")
                    @Override
                    public void onNext(HttpBaseBean<LoveSpaceInfo> loveSpaceInfoHttpBaseBean) {
                        if (loveSpaceInfoHttpBaseBean.getErrorCode() == 0) {
                            setConstellInfo(loveSpaceInfoHttpBaseBean.getData().getUserinfo().getTarget(), loveSpaceInfoHttpBaseBean.getData().getConstellation().getTarget());
                            if (loveSpaceInfoHttpBaseBean.getData().getIsShow_friend_sleep() == 1) {
                                mSleepCv.setVisibility(View.VISIBLE);
                                getSleepData();
                            } else {
                                mSleepCv.setVisibility(View.GONE);
                            }
                            loveSpaceInfo = loveSpaceInfoHttpBaseBean.getData();
                            if (TextUtils.equals(loveSpaceInfoHttpBaseBean.getData().getUserinfo().getTarget().getSex(), "1")) {
                                mConstellationTa.setText(getString(R.string.love_space_her_horoscope));
                                mSleepTextTv.setText(getString(R.string.love_space_her_sleep));
                            } else {
                                mConstellationTa.setText(getString(R.string.love_space_his_horoscope));
                                mSleepTextTv.setText(getString(R.string.love_space_his_sleep));
                            }
                            getLoveNotifyInfo();
                        }
                    }
                });
    }

    private void setConstellInfo(LoveSpaceInfo.UserInfo loveSpaceUserInfo, LoveSpaceInfo.ConstellationInfo loveSpaceConstellationInfo) {
        RequestOptions femaleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        RequestOptions maleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
//        if (Integer.valueOf(loveSpaceUserInfo.getSex()) == 0) {
//            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(loveSpaceUserInfo.getHead_portrait())).apply(maleOptions).into(mHeadIv);
//        } else {
//            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(loveSpaceUserInfo.getHead_portrait())).apply(femaleOptions).into(mHeadIv);
//        }

        String[] birth = loveSpaceUserInfo.getBirthday().split("-");
        int month = Integer.parseInt(birth[1]);
        int day = Integer.parseInt(birth[2]);
        String constellation_name = Apputils.getAstro(ToTwooApplication.baseContext, month, day);
        int index = Arrays.asList(
                        getResources().getStringArray(R.array.constellation_names_cap))
                .indexOf(constellation_name);
        if (index < 0) {
            index = 0;
        }
        int conIcon = conIconRes[index];
        Glide.with(LoveSpacePinkActivity.this).load(conIcon).into(mHeadIv);
        mConstellationTitle.setText(Apputils.getAstro(ToTwooApplication.baseContext, month, day));
        mBirthTv.setText(getString(R.string.love_space_birthday_info, birth[1] + "/" + birth[2]));
        mConstellationRb.setRating(Float.parseFloat(loveSpaceConstellationInfo.getAll()));
        if (Apputils.systemLanguageIsChinese(LoveSpacePinkActivity.this)) {
            mConstellationInfoTv.setText(loveSpaceConstellationInfo.getSummary());
        } else {
            mConstellationInfoTv.setText(loveSpaceConstellationInfo.getSummary_en());
        }
        setBirdayInfo(loveSpaceUserInfo.getBirthday_day());
    }

    //如果是当天生日。自己的是跳转一个链接，对方的跳转到发送寄语
    //如果是30天到0天。显示还有多少天生日
    //如果是大于30天显示别忘记了
    private void setBirdayInfo(int day) {
        if (day == 0) {
            if (isSelectMe) {
                mGiftInfo.setText(getString(R.string.love_space_birthday));
                if (Apputils.systemLanguageIsChinese(LoveSpacePinkActivity.this)) {
                    mGiftInfo.setOnClickListener(v -> WebViewActivity.loadUrl(LoveSpacePinkActivity.this, HttpHelper.URL_GIFT_BIRTHDAY, false));
                } else {
                    mGiftInfo.setOnClickListener(v -> {

                    });
                }
            } else {
                mGiftInfo.setText(getString(R.string.love_space_birthday_send_card));
                mGiftInfo.setOnClickListener(v -> startActivity(new Intent(LoveSpacePinkActivity.this, SendGiftGalleryActivity.class)));
            }
            mGiftInfo.setVisibility(View.VISIBLE);
            mBirthInfo.setVisibility(View.GONE);
        } else if (day < 30) {
            mBirthInfo.setText(setNumberSpan(getString(R.string.love_space_birthday_not_yet, day + ""), day, 12));
            mGiftInfo.setVisibility(View.GONE);
            mBirthInfo.setVisibility(View.VISIBLE);
        } else {
            mBirthInfo.setText(getString(R.string.love_space_birthday_do_not_forget));
            mGiftInfo.setVisibility(View.GONE);
            mBirthInfo.setVisibility(View.VISIBLE);
        }
    }

    private void setIndicatorMe(boolean isMe) {
        if (isMe) {
            mConstellationMe.setBackground(null);
            mConstellationMe.setTextSize(17);
            mConstellationMe.setTypeface(null, Typeface.BOLD);
            mConstellationMe.setTextColor(getResources().getColor(R.color.color_main));
            mConstellationTa.setBackground(getResources().getDrawable(R.drawable.shape_love_space_bg_left));
            mConstellationTa.setTextSize(15);
            mConstellationTa.setTypeface(null, Typeface.NORMAL);
            mConstellationTa.setTextColor(getResources().getColor(R.color.love_space_green_color));
            isSelectMe = true;
        } else {
            mConstellationTa.setBackground(null);
            mConstellationTa.setTextSize(17);
            mConstellationTa.setTypeface(null, Typeface.BOLD);
            mConstellationTa.setTextColor(getResources().getColor(R.color.color_main));
            mConstellationMe.setBackground(getResources().getDrawable(R.drawable.shape_love_space_bg_right));
            mConstellationMe.setTextSize(15);
            mConstellationMe.setTypeface(null, Typeface.NORMAL);
            mConstellationMe.setTextColor(getResources().getColor(R.color.love_space_green_color));
            isSelectMe = false;
        }
    }

    private void getLoveNotifyInfo() {
        HttpHelper.customService.getCustomList(ToTwooApplication.owner.getPairedId(), ToTwooApplication.otherPhone)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<List<CustomItemBean>>>() {
                    @Override
                    public void onCompleted() {
                        AlarmCustomNotifyLogic.getInstance().notifyAlarm();
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoveSpacePinkActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<List<CustomItemBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            beans = listHttpBaseBean.getData();
                            if (beans != null && beans.size() != 0) {
                                mNotifyInfo.setText(setStringStyle(getString(R.string.love_space_check_info, beans.size()),
                                        String.valueOf(beans.size())));
                            }
                        }
                    }
                });
    }

    private void setTime(int time, TextView tvHour, TextView tvHourText, TextView tvMin, TextView tvMinText) {
        int hour = time / 60;
        int min = time % 60;
        if (hour == 0) {
            tvHour.setVisibility(View.GONE);
            tvHourText.setVisibility(View.GONE);
            tvMin.setVisibility(View.VISIBLE);
            tvMinText.setVisibility(View.VISIBLE);
            tvMin.setText(min + "");
        } else if (min == 0) {
            tvHour.setVisibility(View.VISIBLE);
            tvHourText.setVisibility(View.VISIBLE);
            tvMin.setVisibility(View.GONE);
            tvMinText.setVisibility(View.GONE);
            tvHour.setText(hour + "");
        } else {
            tvHour.setText(hour + "");
            tvMin.setText(min + "");
            tvHour.setVisibility(View.VISIBLE);
            tvHourText.setVisibility(View.VISIBLE);
            tvMin.setVisibility(View.VISIBLE);
            tvMinText.setVisibility(View.VISIBLE);
        }
    }

    private void getPeriodInfo() {
        //70 -75系列，展示大姨妈提醒
        if (BleParams.isButtonBatteryJewelry()) {
            mPeriodCl.setVisibility(View.VISIBLE);

            HttpHelper.periodSave.getFriendPeriodState()
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Observer<>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<PeriodStateBean> periodStateBeanHttpBaseBean) {
                            if (periodStateBeanHttpBaseBean.getErrorCode() == 0) {
                                setInfo(periodStateBeanHttpBaseBean.getData());
                            }
                        }
                    });
        } else {
            mPeriodCl.setVisibility(View.GONE);
        }
    }

    private void setInfo(final PeriodStateBean periodStateBean) {

        String month = periodStateBean.getTime().substring(5, 7);
        String day = periodStateBean.getTime().substring(8);

        if (TextUtils.equals("SY", periodStateBean.getState())) {
            mPeriodInfo.setText(changeColor(Integer.valueOf(month), Integer.valueOf(day), periodStateBean.getDay(), false));
        } else if (TextUtils.equals("HY", periodStateBean.getState())) {
            mPeriodInfo.setText(changeColor(Integer.valueOf(month), Integer.valueOf(day), periodStateBean.getDay(), false));
        } else if (TextUtils.equals("D", periodStateBean.getState())) {
            String peroidInfo = getString(R.string.home_period_delay_set, periodStateBean.getDay());
            SpannableString spannableString = new SpannableString(peroidInfo);
            spannableString = setStyle(peroidInfo, spannableString, periodStateBean.getDay());
            mPeriodInfo.setText(spannableString);
        } else {
            mPeriodInfo.setText(changeColor(Integer.valueOf(month), Integer.valueOf(day), periodStateBean.getDay(), true));
        }
    }

    /**
     * @param day 取值, 7, 30, 100
     */
    private void getRankInfo(int day) {
        if (rankInfoBeans == null || rankInfoBeans.isEmpty()) {
            rankInfoBeans = new ArrayList<>();
            rankInfoBeans.add(new RankInfoBean());
            rankInfoBeans.add(new RankInfoBean());
            rankInfoBeans.add(new RankInfoBean());
        }

        HttpHelper.commonService.getRankInfo(ToTwooApplication.owner.getPairedId(), day)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<RankInfoBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        LogUtils.e("aab e = " + e);
                    }

                    @Override
                    public void onNext(HttpBaseBean<RankInfoBean> rankInfoBeanHttpBaseBean) {
                        if (rankInfoBeanHttpBaseBean.getErrorCode() == 0
                                && rankInfoBeanHttpBaseBean.getData().getUserinfo() != null) {

                            int index = getIndexByDay(day);
                            rankInfoBeans.set(index, rankInfoBeanHttpBaseBean.getData());
                            setRankInfo(index);
                            setRankInfo(0);
                        }
                    }
                });
    }

    private int getIndexByDay(int day) {
        if (day == 7) {
            return 0;
        } else if (day == 30) {
            return 1;
        } else {
            return 2;
        }
    }

    private void setHeadsInfo(ImageView selfIv, ImageView otherIv) {
        RankUserInfosBean rankUserInfosBean = rankInfoBeans.get(0).getUserinfo();
        if (rankUserInfosBean == null) {
            return;
        }
        RequestOptions femaleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        RequestOptions maleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        if (rankUserInfosBean.getSelf().getSex() == 0) {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getSelf().getHead_portrait())).apply(maleOptions).into(selfIv);
        } else {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getSelf().getHead_portrait())).apply(femaleOptions).into(selfIv);
        }
        aCache.put(CommonArgs.PARTNER_GENDER, String.valueOf(rankUserInfosBean.getTarget().getSex()));
        PreferencesUtils.put(getApplicationContext(), CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, rankUserInfosBean.getTarget().getHead_portrait());
        if (rankUserInfosBean.getTarget().getSex() == 0) {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getTarget().getHead_portrait())).apply(maleOptions).into(otherIv);
        } else {
            Glide.with(ToTwooApplication.baseContext).load(BitmapHelper.checkRealPath(rankUserInfosBean.getTarget().getHead_portrait())).apply(femaleOptions).into(otherIv);
        }
    }

    private void selectRankInfo(int index) {
        mRankWeekTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
        mRankWeekTv.setBackgroundResource(R.drawable.shape_light_gray_15);

        mRankMonthTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
        mRankMonthTv.setBackgroundResource(R.drawable.shape_light_gray_15);

        mRankTotalTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
        mRankTotalTv.setBackgroundResource(R.drawable.shape_light_gray_15);
        weekArrowDown.setVisibility(View.GONE);
        monthArrowDown.setVisibility(View.GONE);
        totalArrowDown.setVisibility(View.GONE);
        switch (index) {
            case 0:
                mRankWeekTv.setTextColor(getResources().getColor(R.color.white));
                mRankWeekTv.setBackgroundResource(R.drawable.shape_red_25);
                weekArrowDown.setVisibility(View.VISIBLE);
                break;
            case 1:
                mRankMonthTv.setTextColor(getResources().getColor(R.color.white));
                mRankMonthTv.setBackgroundResource(R.drawable.shape_red_25);
                monthArrowDown.setVisibility(View.VISIBLE);

                break;
            case 2:

                mRankTotalTv.setTextColor(getResources().getColor(R.color.white));
                mRankTotalTv.setBackgroundResource(R.drawable.shape_red_25);
                totalArrowDown.setVisibility(View.VISIBLE);

                break;
        }
        this.selectedRankIndex = index;
        setRankInfo(selectedRankIndex);
    }

    private void setRankInfo(int index) {
        // rankInfoBeans.get(index).getGet_sign() 用以代表是否已经获取过数据
        if (rankInfoBeans == null || rankInfoBeans.size() < index + 1 || rankInfoBeans.get(index).getGet_sign() < 0) {
            if (index == 0) {
                getRankInfo(7);
            } else if (index == 1) {
                getRankInfo(30);
            } else {
                getRankInfo(100);
            }
            return;
        }

        if (selectedRankIndex != index) {
            return;
        }

        RankInfoBean rankInfoBean = rankInfoBeans.get(index);
        mRankCount.setText(String.valueOf(rankInfoBean.getConsonance_count()));
        mRankRankTv.setText(rankInfoBean.getRanking());
        mRankPercentTv.setText(rankInfoBean.getPercentage());

        setHeadsInfo(mRankMe, mRankOther);

        loveNameTv.setText(getString(R.string.love_space_share_name, rankInfoBean.getUserinfo().getTarget().getNick_name(), rankInfoBean.getUserinfo().getSelf().getNick_name()));

    }

    public static final String FOOT_PRINT_DATA = "foot_print_data";

    private void getSelectedCity() {
        HttpHelper.footPrintService.getSelectedCity(ToTwooApplication.otherPhone, ToTwooApplication.owner.getPairedId())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<TogetherBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        LogUtils.e("e = " + e);
                    }

                    @SuppressLint("StringFormatInvalid")
                    @Override
                    public void onNext(HttpBaseBean<TogetherBean> togetherBeanHttpBaseBean) {
                        if (togetherBeanHttpBaseBean.getErrorCode() == 0) {
                            String info_str = gson.toJson(togetherBeanHttpBaseBean.getData());
                            aCache.put(FOOT_PRINT_DATA, info_str);
                            TogetherBean togetherBean = togetherBeanHttpBaseBean.getData();
                            if (togetherBean.getCity_total() > 0 || togetherBean.getCountry_total() > 0) {
                                mFootInfo.setText(setStringStyle(getString(R.string.love_space_foot_print_info, togetherBean.getCountry_total(),
                                                togetherBean.getProvince_total(), togetherBean.getCity_total()), String.valueOf(togetherBean.getCountry_total()),
                                        String.valueOf(togetherBean.getProvince_total()), String.valueOf(togetherBean.getCity_total())));
                            } else {
                                mFootInfo.setText(R.string.love_space_foot_print_info_default);
                            }
                        }
                    }
                });
    }

    private void getSleepData() {
        HttpHelper.sleepDataService.getDayData(System.currentTimeMillis() / 1000, "friend", ToTwooApplication.otherPhone)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SleepDayData>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<SleepDayData> sleepDayDataHttpBaseBean) {
                        if (sleepDayDataHttpBaseBean.getErrorCode() == 0) {
                            setDayData(sleepDayDataHttpBaseBean.getData());
                        }
                    }
                });
    }

    private void setDayData(SleepDayData sleepDayData) {
        setTime(sleepDayData.getDay_total_sleep(), mDayHourTv, mDayHourText, mDayMinTv, mDayMinText);
        setTime(sleepDayData.getDeep_sleep(), mDeepHourTv, mDeepHourText, mDeepMinTv, mDeepMinText);
        setTime(sleepDayData.getLight_sleep(), mLightHourTv, mLightHourText, mLightMinTv, mLightMinText);
        mDeepSleepPercent.setText(sleepDayData.getDeep_sleep_percentage());
        mLightSleepPercent.setText(sleepDayData.getLight_sleep_percentage());

        ArrayList<SleepUpdateBean> sleepUpdateBeans = sleepDayData.getList();
        if (sleepUpdateBeans != null && !sleepUpdateBeans.isEmpty()) {
            //TODO 补足数据。把小于30分的状态1改为4
            sleepUpdateBeans = SleepCalculateUtil.fillInfos(-1, sleepUpdateBeans);
            int awakeTime = SleepCalculateUtil.countAwake(sleepUpdateBeans);

            mAwakeTv.setText("" + awakeTime);
            mStatementTv.setText(getStatement(-1, sleepUpdateBeans, awakeTime, sleepDayData.getDay_total_sleep()));

            mSleepNoData.setVisibility(View.GONE);
            mSleepInfo.setVisibility(View.VISIBLE);
            mDeepSleepCl.setVisibility(View.VISIBLE);
            mLightSleepCl.setVisibility(View.VISIBLE);
            mAwakeCl.setVisibility(View.VISIBLE);
        } else {
            mSleepNoData.setVisibility(View.VISIBLE);
            mSleepInfo.setVisibility(View.GONE);
            mDeepSleepCl.setVisibility(View.GONE);
            mLightSleepCl.setVisibility(View.GONE);
            mAwakeCl.setVisibility(View.GONE);

        }
    }

    private void getCertificationInfo() {
        int count = PreferencesUtils.getInt(this, CoupleLogic.PAIRED_TOTWOO_COUNT, 0);
        HttpHelper.commonService.getCertificationInfo(count)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<CertificationInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<CertificationInfo> certificationInfoHttpBaseBean) {
                        if (certificationInfoHttpBaseBean.getErrorCode() == 0) {
                            mCertificationPreTv.setText(certificationInfoHttpBaseBean.getData().getTitle());
                            mCertificationSufTv.setText(certificationInfoHttpBaseBean.getData().getTitle_target());
                            int level = certificationInfoHttpBaseBean.getData().getGet_sign();
                            mCertificationCountTv.setText(getString(R.string.love_space_total_count, level));
                            if (level < 10) {
                                mCertificationCurrentIv.setText(String.valueOf(level));
                                mCertificationSufIv.setText(String.valueOf(level + 1));

                                int gapCount = certificationInfoHttpBaseBean.getData().getTarget_num() - count;
                                mCertificationGapTv.setText(getString(R.string.certification_gap_count, gapCount));
                                float percent = (float) certificationInfoHttpBaseBean.getData().getConsonance_count() / certificationInfoHttpBaseBean.getData().getTarget_num();
                                mMpb.setPercent(Math.min(percent, 1));
                            } else {
                                mProgressCl.setVisibility(View.GONE);
                                mFullTv.setVisibility(View.VISIBLE);
                                mheartHurtIv.setVisibility(View.VISIBLE);
                            }
                        }
                    }
                });
    }

    private String getStatement(int diff, ArrayList<SleepUpdateBean> beans, int awakeTime, int totalMin) {
        int type = SleepCalculateUtil.getStatement(diff, beans, awakeTime, totalMin);
        String statement = "";
        switch (type) {
            case 1:
                statement = getString(R.string.love_space_sleep_assessment1);
                break;
            case 2:
                statement = getString(R.string.love_space_sleep_assessment2);
                break;
            case 3:
                statement = getString(R.string.love_space_sleep_assessment3);
                break;
            case 4:
                statement = getString(R.string.love_space_sleep_assessment4);
                break;
            case 5:
                statement = getString(R.string.love_space_sleep_assessment5);
                break;
            default:
                getString(R.string.love_space_sleep_assessment4);
                break;
        }
        return statement;
    }

    public static CharSequence setNumberSpan(String text, int number, int size) {
        Typeface typefaceGithic = ResourcesCompat.getFont(ToTwooApplication.baseContext, R.font.gothicb);
        CustomTypefaceSpan tfspan = new CustomTypefaceSpan(typefaceGithic);

        SpannableString spannableString = new SpannableString(text);
        int index = spannableString.toString().indexOf(number + "");
        spannableString.setSpan(new AbsoluteSizeSpan(size, true), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(tfspan, index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#f18e8f")), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        return spannableString;
    }

    private SpannableString changeColor(int month, int day, int targetday, boolean isBefore) {
        SpannableString spannableString;
        String peroidInfo;
        if (isBefore) {
            peroidInfo = getString(R.string.home_period_before_love_space, month, day, targetday);
            int monthIndex = peroidInfo.indexOf(month + "");
            int dayIndex = peroidInfo.indexOf(day + "", monthIndex + 1);
            int targetIndex = peroidInfo.lastIndexOf(targetday + "");
            spannableString = new SpannableString(peroidInfo);
//            spannableString = setStyle(spannableString, month, monthIndex);
//            spannableString = setStyle(spannableString, day, dayIndex);
            spannableString = setStyle(spannableString, targetday, targetIndex);
        } else {
            peroidInfo = getString(R.string.home_period_delay_love_space, targetday, month, day);
            int targetIndex = peroidInfo.indexOf(targetday + "");
            int monthIndex = peroidInfo.indexOf(month + "", targetIndex + 1);
            int dayIndex = peroidInfo.lastIndexOf(day + "");
            spannableString = new SpannableString(peroidInfo);
//            spannableString = setStyle(spannableString, month, monthIndex);
//            spannableString = setStyle(spannableString, day, dayIndex);
            spannableString = setStyle(spannableString, targetday, targetIndex);
        }

        return spannableString;
    }

    private SpannableString setStyle(SpannableString spannableString, int number, int index) {
        int endIndex = (number + "").length();
        spannableString.setSpan(new AbsoluteSizeSpan(13, true), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_color_black)), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(tfspan, index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private SpannableString setStyle(String string, SpannableString spannableString, int index) {
        int endIndex = (index + "").length();
        index = string.indexOf(index + "");
        spannableString.setSpan(new AbsoluteSizeSpan(13, true), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_color_black)), index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(tfspan, index, index + endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private SpannableString setStringStyle(String string, String insideStr) {
        SpannableString spannableString = new SpannableString(string);
        int startIndex = string.indexOf(insideStr);
        int endIndex = startIndex + insideStr.length();
        setSpan(spannableString, startIndex, endIndex);
        return spannableString;
    }

    private SpannableString setStringStyle(String string, String country_count, String province_count, String city_count) {
        SpannableString spannableString = new SpannableString(string);
        int firstStart = string.indexOf(country_count);
        int firstEnd = firstStart + country_count.length();
        int secondStart = string.indexOf(province_count, firstEnd);
        int secondEnd = secondStart + province_count.length();
        int thirdStart = string.indexOf(city_count, secondEnd);
        int thirdEnd = thirdStart + city_count.length();
        setSpan(spannableString, firstStart, firstEnd);
        setSpan(spannableString, secondStart, secondEnd);
        setSpan(spannableString, thirdStart, thirdEnd);
        return spannableString;
    }

    private SpannableString setSpan(SpannableString spannableString, int startIndex, int endIndex) {
        spannableString.setSpan(new AbsoluteSizeSpan(13, true), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_color_black)), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private SpannableString setIntegerStyle(String string, int number) {
        SpannableString spannableString = new SpannableString(string);
        String insideStr = String.valueOf(number);
        int startIndex = string.indexOf(insideStr);
        int endIndex = startIndex + insideStr.length();
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private boolean isShowDialog;

    private void showRankShareDialog() {
        if (rankInfoBeans == null || rankInfoBeans.size() < 3 || loveSpaceInfo == null) {
            ToastUtils.showShort(LoveSpacePinkActivity.this, R.string.error_net);
            return;
        }
        try {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_SHAREHAPPYNESS);
            isShowDialog = true;
            mShareCl.setVisibility(View.VISIBLE);
            setHeadsInfo(mShareMe, mShareOther);
            mShareNameTv.setText(getString(R.string.love_space_share_name, rankInfoBeans.get(2).getUserinfo().getTarget().getNick_name(), rankInfoBeans.get(2).getUserinfo().getSelf().getNick_name()));
            mShareLvIv.setImageResource(CommonArgs.CERTIFICATION_LEVEL_IMAGES[rankInfoBeans.get(2).getGet_sign() - 1]);
            mShareTitleTv.setText(CommonArgs.CERTIFICATION_LEVEL_INFO_IDS[rankInfoBeans.get(2).getGet_sign() - 1]);
            if (loveSpaceInfo.getConsonance_count() == 0) {
                //在配对日期
                mShareTimeTitle.setText(CommonUtils.getFormatDate(loveSpaceInfo.getFirst_couple(), Apputils.systemLanguageIsChinese(LoveSpacePinkActivity.this)));
                mShareTimeText.setText(R.string.pair_t1);
            } else {
                if (Apputils.systemLanguageIsChinese(LoveSpacePinkActivity.this)) {
                    mShareTimeTitle.setText(CommonUtils.getFormatDate(loveSpaceInfo.getFirst_consonance(), true));
                    mShareTimeText.setText(setStringStyle(getString(R.string.history_first_twoo), "1"));
                } else {
                    mShareTimeTitle.setText(CommonUtils.getFormatDate(loveSpaceInfo.getFirst_consonance(), false));
                    mShareTimeText.setText(R.string.history_first_twoo);
                }
            }
//            ACache aCache = ACache.get(LoveSpacePinkActivity.this);
//            String togetherDay = aCache.getAsString(HistoryTwooActivity.HISTORY_TOGETHER_DAY);
//            if (TextUtils.isEmpty(togetherDay)) {
//                togetherDay = "1";
//            }
            if (ToTwooApplication.cacheData != null) {
                String togetherDay = ToTwooApplication.cacheData.getTogetherDay();
                mShareTotwooTitle.setText(setStringStyle(getString(R.string.love_together, togetherDay), togetherDay));
            }

            String totwooCount = String.valueOf(loveSpaceInfo.getConsonance_count());
            mShareTotwooText.setText(setStringStyle(getString(R.string.love_space_share_love_collect, totwooCount), totwooCount));
            if (TextUtils.isEmpty(rankInfoBeans.get(2).getRanking()) || TextUtils.equals(rankInfoBeans.get(2).getRanking(), "0")) {
                mShareRankTitle.setText(R.string.love_space_rank_default);
            } else {
                mShareRankTitle.setText(setStringStyle(getString(R.string.love_space_share_rank, rankInfoBeans.get(2).getRanking()), rankInfoBeans.get(2).getRanking()));
            }
            if (TextUtils.isEmpty(rankInfoBeans.get(2).getPercentage()) || TextUtils.equals(rankInfoBeans.get(2).getPercentage(), "0")) {
                mShareRankText.setText(R.string.love_space_rank_default_number);
            } else {
                mShareRankText.setText(setStringStyle(getString(R.string.love_space_share_beyond, rankInfoBeans.get(2).getPercentage()), rankInfoBeans.get(2).getPercentage()));
            }
            showShareDialog();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPressed() {
        if (isShowDialog && loveSpaceDialog != null) {
            goneShare();
        } else {
            super.onBackPressed();
        }
    }

    private void goneShare() {
        isShowDialog = false;
        mShareCl.setVisibility(View.GONE);
        loveSpaceDialog.dismiss();
    }

    private void addCity(String name) {
        HttpHelper.footPrintService.addCity(ToTwooApplication.otherPhone, ToTwooApplication.owner.getPairedId(), name)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            cityMiddleDialog.dismiss();
                            if (rankInfoBeans != null && rankInfoBeans.size() > 0) {
                                startActivity(new Intent(LoveSpacePinkActivity.this, MainTogetherActivity.class)
                                        .putExtra(PAIRED_HEAD_URL, rankInfoBeans.get(0).getUserinfo().getTarget().getHead_portrait())
                                        .putExtra(PAIRED_NAMES, getString(R.string.love_space_share_name, rankInfoBeans.get(0).getUserinfo().getTarget().getNick_name(), rankInfoBeans.get(0).getUserinfo().getSelf().getNick_name())));
                            } else {
                                startActivity(new Intent(LoveSpacePinkActivity.this, MainTogetherActivity.class));
                            }
                        }
                    }
                });
    }

    private CommonMiddleDialog cityMiddleDialog;

    @SuppressLint("StringFormatInvalid")
    private void showAddCityDialog(String name) {
        cityMiddleDialog = new CommonMiddleDialog(LoveSpacePinkActivity.this);
        cityMiddleDialog.setMessage(setStringStyle(getString(R.string.foot_print_add, name), name));
        cityMiddleDialog.setSure(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PRINT_ADD_LOCATION_CITY);
            addCity(name);
        });
        cityMiddleDialog.setCancel(R.string.cancel, dialog -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PRINT_CANCEL_LOCATION_CITY);
            cityMiddleDialog.dismiss();
        });
        cityMiddleDialog.show();
    }

    private void setCity(String name) {
        HttpHelper.footPrintService.checkCity(ToTwooApplication.otherPhone, ToTwooApplication.owner.getPairedId(), name)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<FootPrintHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<FootPrintHttpBean> footPrintHttpBeanHttpBaseBean) {
                        if (footPrintHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            showAddCityDialog(name);
                        }
                    }
                });
    }


    private LoveSpaceDialog loveSpaceDialog;

    private void showShareDialog() {
        if (loveSpaceDialog == null) {
            if (Apputils.systemLanguageIsChinese(LoveSpacePinkActivity.this)) {
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                types.add(CommonShareType.WEIBO);
                types.add(CommonShareType.QZONE);
                types.add(CommonShareType.QQ);
                loveSpaceDialog = new LoveSpaceDialog(LoveSpacePinkActivity.this, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath());
                            goneShare();
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareImageToWechat(getPath());
                            goneShare();
                            break;
                        case WEIBO:
                            ShareUtilsSingleton.getInstance().shareImageToWeibo(LoveSpacePinkActivity.this, getPath(), "");
                            goneShare();
                            break;
                        case QZONE:
                            ShareUtilsSingleton.getInstance().shareImageToQzone(getPath(), "");
                            goneShare();
                            break;
                        case QQ:
                            ShareUtilsSingleton.getInstance().shareImageToQQ(getPath());
                            goneShare();
                            break;
                    }

                });
                loveSpaceDialog.setCanceledOnTouchOutside(false);
                loveSpaceDialog.setOnDismissListener(dialog -> mShareCl.setVisibility(View.GONE));
//                loveSpaceDialog.setCustomTitle(CommonUtils.setNumberOrangeSpan(getResources().getString(R.string.share_text_head_info), 88, 17));
            } else {
                facebookCallback = new FacebookCallback<Sharer.Result>() {
                    @Override
                    public void onSuccess(Sharer.Result result) {
                        ToastUtils.showShort(LoveSpacePinkActivity.this, getResources().getString(R.string.share_complete));
                    }

                    @Override
                    public void onCancel() {
                        ToastUtils.showShort(LoveSpacePinkActivity.this, getResources().getString(R.string.share_cancel));
                    }

                    @Override
                    public void onError(FacebookException error) {
                        ToastUtils.showShort(LoveSpacePinkActivity.this, getResources().getString(R.string.share_error));
                    }
                };
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FACEBOOK);
                types.add(CommonShareType.TWITTER);
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                loveSpaceDialog = new LoveSpaceDialog(LoveSpacePinkActivity.this, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath());
                            goneShare();
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareImageToWechat(getPath());
                            goneShare();
                            break;
                        case FACEBOOK:
                            ShareUtilsSingleton.getInstance().shareImageToFacebook(getPath(), LoveSpacePinkActivity.this, facebookCallback);
                            goneShare();
                            break;
                        case TWITTER:
                            ShareUtilsSingleton.getInstance().shareImageToTwitter(getPath(), "");
                            goneShare();
                            break;
                    }

                });
                loveSpaceDialog.setCanceledOnTouchOutside(false);
                loveSpaceDialog.setOnDismissListener(dialog -> mShareCl.setVisibility(View.GONE));
//                loveSpaceDialog.setCustomTitle(getResources().getString(R.string.share_text_head_info));
            }
        }
        loveSpaceDialog.show();
    }

    private String getPath() {
        Bitmap snapShareBitmap = ShareUtilsSingleton.getBitmapByView(mShareInfoCl);
        return FileUtils.saveBitmapFromSDCard(snapShareBitmap,
                "totwoo_cache_img_" + System.currentTimeMillis());
    }

    @EventInject(eventType = S.E.E_CLOSE_HOMEPAGE, runThread = TaskType.UI)
    public void onHomePageClosed(EventData data) {
        this.finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理对话框
        if (loveSpaceDialog != null && loveSpaceDialog.isShowing()) {
            try {
                loveSpaceDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
            loveSpaceDialog = null;
        }

        // 清理新用户礼品对话框
        if (newUserGiftDialog != null && newUserGiftDialog.isShowing()) {
            try {
                newUserGiftDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
            newUserGiftDialog = null;
        }

        // 清理引用
        aCache = null;
        loveSpaceInfo = null;
        beans = null;
        rankInfoBeans = null;
        facebookCallback = null;
        gson = null;

        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        if (isShowing && !isFinishing() && !isDestroyed()) {
            showNewUserGifDialog();
        }
    }

    private boolean isShowing;

    @Override
    protected void onPause() {
        super.onPause();
        isShowing = false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        isShowing = true;
        getLoveNotifyInfo();
    }

    private NewUserGiftDialog newUserGiftDialog;

    private void showNewUserGifDialog() {
        newUserGiftDialog = new NewUserGiftDialog(LoveSpacePinkActivity.this, v -> {
            if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_YESORNO_LUCKY_CLICK);
            } else {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_YESORNO_LUCKY_CLICK);
            }
            WebViewActivity.loadUrl(LoveSpacePinkActivity.this, HttpHelper.URL_GIFT, false);
            newUserGiftDialog.dismiss();
        }, v -> newUserGiftDialog.dismiss(), CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券", 88, 20), "立即抽奖");
        newUserGiftDialog.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == TO_SAVE_CITY && resultCode == SAVE_SELECT_CITY_SUCCESS) {
            if (rankInfoBeans != null && rankInfoBeans.size() > 0) {
                startActivity(new Intent(LoveSpacePinkActivity.this, MainTogetherActivity.class)
                        .putExtra(CommonArgs.FROM_TYPE, "space")
                        .putExtra(PAIRED_HEAD_URL, rankInfoBeans.get(0).getUserinfo().getTarget().getHead_portrait())
                        .putExtra(PAIRED_NAMES, getString(R.string.love_space_share_name, rankInfoBeans.get(0).getUserinfo().getTarget().getNick_name(), rankInfoBeans.get(0).getUserinfo().getSelf().getNick_name())));
            } else {
                startActivity(new Intent(LoveSpacePinkActivity.this, MainTogetherActivity.class)
                        .putExtra(CommonArgs.FROM_TYPE, "space"));
            }
        }
    }
}
