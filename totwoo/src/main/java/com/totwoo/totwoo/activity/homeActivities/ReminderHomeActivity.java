package com.totwoo.totwoo.activity.homeActivities;

import android.content.Intent;
import android.os.Bundle;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.ReminderTouchHintActivity;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.CustomReminderFragment;
import com.totwoo.totwoo.fragment.MeFragment;
import com.totwoo.totwoo.fragment.RemindFragment;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.ArrayList;

public class ReminderHomeActivity extends HomeBaseActivity {

    private static final String IS_HINT_TOUCH_USE = "is_hint_touch_use";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Class<? extends BaseFragment>[] baseFragments = new Class[3];
        baseFragments[0] = RemindFragment.class;
        baseFragments[1] = CustomReminderFragment.class;
        baseFragments[2] = MeFragment.class;

        ArrayList<HomepageBottomInfo> infos = new ArrayList<>();
        infos.add(new HomepageBottomInfo(R.drawable.new_home_fun_un, R.drawable.new_home_fun, R.string.fun));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_reminder_un, R.drawable.new_home_reminder, R.string.reminder));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_me_un, R.drawable.new_home_me, R.string.user));
        super.setBottomInfo(infos);
        super.setFragmentsAndInitViewpager(baseFragments);
        super.setCurrentFromType(5);
        super.setTotwooIndex(-1);
        if (!PreferencesUtils.getBoolean(this, IS_HINT_TOUCH_USE, false)) {
            PreferencesUtils.put(this, IS_HINT_TOUCH_USE, true);
            mHandler.postDelayed(() -> {
                startActivity(new Intent(ReminderHomeActivity.this, ReminderTouchHintActivity.class));
                overridePendingTransition(R.anim.activity_slide_in_bottom, R.anim.activity_slide_out_bottom);
            }, 500);

        }
    }
}
