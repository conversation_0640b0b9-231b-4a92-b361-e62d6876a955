package com.totwoo.totwoo.activity;

import android.animation.Animator;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.etone.framework.event.EventBus;
import com.google.gson.Gson;
import com.jakewharton.rxbinding.widget.RxTextView;
import com.jakewharton.rxbinding.widget.TextViewAfterTextChangeEvent;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.giftMessage.GiftDataActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftInfoAddActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftMessageListActivity;
import com.totwoo.totwoo.bean.GiftMessageCard;
import com.totwoo.totwoo.bean.GiftMessageReceiverInfo;
import com.totwoo.totwoo.bean.LocalContactsBean;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.bean.holderBean.SendGreetingCardResponse;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.StringUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.BaseHeaderAdapter;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderEntity;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderItemDecoration;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

public class ContactsSelectActivity extends BaseActivity implements LoaderManager.LoaderCallbacks<Cursor> {
    @BindView(R.id.contact_search_content_cl)
    ConstraintLayout mSearchContentCl;
    @BindView(R.id.contact_search_et)
    EditText mSearchEt;
    @BindView(R.id.contact_input_cl)
    ConstraintLayout mInputCl;
    @BindView(R.id.contact_input_et)
    EditText mInputEt;
    @BindView(R.id.contact_input_city_tv)
    TextView mCityTv;
    @BindView(R.id.top_bar_back_btn)
    ImageView mBackIv;
    @BindView(R.id.top_bar_right_tv)
    TextView mRightTv;
    @BindView(R.id.top_bar_title_view)
    TextView mTitleTv;
    @BindView(R.id.contact_main_rv)
    RecyclerView mMainContactList;
    @BindView(R.id.contact_select_rv)
    RecyclerView mSelectList;
    @BindView(R.id.contact_select_sending_tv)
    TextView mSendingTv;
    @BindView(R.id.card_store_lv)
    LottieAnimationView mCardStoreLv;
    @BindView(R.id.contact_search_hint_bg)
    View hintBg;
    @BindView(R.id.contacts_send_success_bg)
    View mSendSuccessBg;
    @BindView(R.id.contact_empty_text)
    TextView mEmptyTv;

    private String mCurFilter;
    private List<PinnedHeaderEntity<LocalContactsBean>> allContactBeans;
    private BaseHeaderAdapter<PinnedHeaderEntity<LocalContactsBean>> mAdapter;
    private List<LocalContactsBean> selectBeans;
    private List<String> sortKeys;
    private List<Integer> sortKeysIndex;
    private String countryCode;
    private ContactSelectAdapter contactSelectAdapter;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_contacts_select);
        ButterKnife.bind(this);

        mBackIv.setImageResource(R.drawable.back_icon_black);
        mBackIv.setOnClickListener(v -> finish());
        mTitleTv.setText(R.string.select_contact);
        mRightTv.setText(R.string.confirm);
        mRightTv.setOnClickListener(v -> {
            if (selectBeans == null || selectBeans.size() == 0) {
//                ToastUtils.showLong(ContactsSelectActivity.this, R.string.send_card_no_receive_err);
                mSendingTv.setVisibility(View.VISIBLE);
                mSendingTv.setText(R.string.send_card_no_receive_err);
                mHandler.postDelayed(() -> mSendingTv.setVisibility(View.GONE), 3000);
                return;
            }
            sendGreetingCard();
        });
        mSearchEt.setFocusable(true);
        mSearchEt.setFocusableInTouchMode(true);
        mInputEt.setFocusable(true);
        mInputEt.setFocusableInTouchMode(true);

        allContactBeans = new ArrayList<>();
        sortKeys = new ArrayList<>();
        sortKeysIndex = new ArrayList<>();
        selectBeans = new ArrayList<>();

        countryCode = PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? "86" : "1");
        mCityTv.setText("+" + countryCode);

        mMainContactList.setLayoutManager(new LinearLayoutManager(ContactsSelectActivity.this));
        mMainContactList.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int i) {
                switch (mAdapter.getItemViewType(i)) {
                    case BaseHeaderAdapter.TYPE_DATA:
                        PinnedHeaderEntity<LocalContactsBean> entity = mAdapter.getData().get(i);
                        addBean(entity.getData());
                        if (mSearchContentCl.getVisibility() == View.VISIBLE) {
                            mSearchContentCl.setVisibility(View.GONE);
                            hideKeyboard(mSearchEt);
                            mSearchEt.setText("");
                            mCurFilter = null;
                            getSupportLoaderManager().restartLoader(0, null,
                                    ContactsSelectActivity.this);
                        }
                        break;
                    case BaseHeaderAdapter.TYPE_HEADER:
                        entity = mAdapter.getData().get(i);
                        break;
                }
            }
        });
        mMainContactList.addItemDecoration(new PinnedHeaderItemDecoration.Builder(BaseHeaderAdapter.TYPE_HEADER).create());

        contactSelectAdapter = new ContactSelectAdapter();
        mSelectList.setLayoutManager(new LinearLayoutManager(ContactsSelectActivity.this, RecyclerView.HORIZONTAL, false));
        mSelectList.setAdapter(contactSelectAdapter);

        try {
            getSupportLoaderManager().initLoader(0, null, this);
        } catch (Exception e) {
            e.printStackTrace();
        }

        RxTextView.afterTextChangeEvents(mSearchEt)
                .throttleWithTimeout(400, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
                .subscribe(new Action1<TextViewAfterTextChangeEvent>() {
                    @Override
                    public void call(TextViewAfterTextChangeEvent textViewAfterTextChangeEvent) {
                        if (mSearchContentCl.getVisibility() == View.GONE) {
                            return;
                        }
                        String newFilter = textViewAfterTextChangeEvent.editable().toString();
                        if (TextUtils.equals(newFilter, mCurFilter)) {
                            return;
                        }
                        mCurFilter = newFilter;
                        if (TextUtils.isEmpty(mCurFilter)) {
                            hintBg.setVisibility(View.VISIBLE);
                        } else {
                            hintBg.setVisibility(View.GONE);
                        }
                        LogUtils.e("aab mCurFilter = " + mCurFilter);
                        getSupportLoaderManager().restartLoader(0, null,
                                ContactsSelectActivity.this);
                    }
                });

        mCardStoreLv.setImageAssetsFolder("lottie_card_store/");
        mCardStoreLv.setAnimation("card_store.json");
    }

    private String lastKey;
    private int lastCount;

    private void initContacts(Cursor cursor) {
        //获取所有联系人
        LogUtils.e("aab mCurFilter cursor.getCount() = " + cursor.getCount());
        if (cursor.getCount() == lastCount) {
            return;
        }
        lastCount = cursor.getCount();
        allContactBeans.clear();
        lastKey = null;
        while (cursor.moveToNext()) {
            //获取联系人sortKey
            String sortKey = StringUtils.getSortKey(cursor.getString(4));
            //分组
            if (!TextUtils.equals(lastKey, sortKey)) {
                allContactBeans.add(new PinnedHeaderEntity<>(new LocalContactsBean(), BaseHeaderAdapter.TYPE_HEADER, sortKey));
                lastKey = sortKey;
                sortKeys.add(sortKey);
                sortKeysIndex.add(allContactBeans.size() - 1);
            }
            allContactBeans.add(new PinnedHeaderEntity<>(new LocalContactsBean(cursor.getString(0), getSimplePhone(cursor.getString(1)),
                    sortKey, cursor.getLong(2) > 0 ? cursor.getLong(3) : -1), BaseHeaderAdapter.TYPE_DATA, sortKey));
        }
        //展示
        setInfoToAdapter();
        //联系人右方字母列表
        LogUtils.e("aab sortKeys.size() = " + sortKeys.size());
//        int sortKeyIndex = sortKeys.indexOf(indexName);
//        mMainContactList.scrollToPosition(sortKeysIndex.get(sortKeyIndex));
    }

    private void setInfoToAdapter() {
        if (mAdapter == null) {
            LogUtils.e("aab mCurFilter allContactBeans.size() = " + allContactBeans.size());
            mAdapter = new BaseHeaderAdapter<PinnedHeaderEntity<LocalContactsBean>>(allContactBeans) {
                @Override
                protected void addItemTypes() {
                    addItemType(BaseHeaderAdapter.TYPE_HEADER, R.layout.contact_list_header_item);
                    addItemType(BaseHeaderAdapter.TYPE_DATA, R.layout.contact_list_data_item);
                }

                @Override
                protected void convert(BaseViewHolder helper, PinnedHeaderEntity<LocalContactsBean> item) {
                    switch (helper.getItemViewType()) {
                        case BaseHeaderAdapter.TYPE_HEADER:
                            helper.setText(R.id.contact_header_tv, item.getPinnedHeaderName());
                            break;
                        case BaseHeaderAdapter.TYPE_DATA:
                            helper.setText(R.id.contact_data_name, item.getData().getName());
                            helper.setText(R.id.contact_data_number, item.getData().getNumber());
                            break;
                    }
                }
            };
            mMainContactList.setAdapter(mAdapter);
        } else {
            LogUtils.e("aab mCurFilter allContactBeans.size() = " + allContactBeans.size());
            if (allContactBeans.size() == 0) {
                mEmptyTv.setVisibility(View.VISIBLE);
            } else {
                mEmptyTv.setVisibility(View.GONE);
            }
            mAdapter.notifyDataSetChanged();
        }
    }

    @OnClick({R.id.contact_search_click_iv, R.id.contact_search_hint_bg, R.id.contact_search_cancel_tv, R.id.contacts_add_cl,
            R.id.contact_input_done_tv, R.id.contact_input_city_tv, R.id.contact_input_cl, R.id.contacts_send_success_bg, R.id.contact_select_help_cl})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.contact_search_click_iv:
                goneInputCl();
                mSearchContentCl.setVisibility(View.VISIBLE);
                mSearchEt.requestFocus();
                mSearchEt.setFocusableInTouchMode(true);
                showKeyboard(mSearchEt);
                break;
            case R.id.contact_search_hint_bg:
                goneSearchCl();
                break;
            case R.id.contact_search_cancel_tv:
                goneSearchCl();
                mSearchEt.setText("");
                mCurFilter = null;
                getSupportLoaderManager().restartLoader(0, null,
                        ContactsSelectActivity.this);
                break;
            case R.id.contacts_add_cl:
                goneSearchCl();
                mSearchEt.setText("");
                mCurFilter = null;
                getSupportLoaderManager().restartLoader(0, null,
                        ContactsSelectActivity.this);
                mInputCl.setVisibility(View.VISIBLE);
                mInputEt.requestFocus();
                showKeyboard(mInputEt);
                break;
            case R.id.contact_input_done_tv:
                String inputNumber = mInputEt.getText().toString().trim();
                if (TextUtils.isEmpty(inputNumber)) {
                    ToastUtils.showShort(ContactsSelectActivity.this, R.string.error_invalid_phone);
                    return;
                }
                if (!android.text.TextUtils.isDigitsOnly(inputNumber)) {
                    ToastUtils.showLong(ContactsSelectActivity.this, R.string.error_incorrect_phone);
                    return;
                }
                String number = countryCode + mInputEt.getText().toString().trim();
                number = getSimplePhone(number);

                goneInputCl();
                addBean(new LocalContactsBean(number, number, StringUtils.getSortKey(number), -1));
                mInputEt.setText("");
                break;
            case R.id.contact_input_city_tv:
                Intent intent = new Intent(ContactsSelectActivity.this, CountryCodeListActivity.class);
                startActivityForResult(intent, 0);

                // 切换动画
                overridePendingTransition(R.anim.activity_fade_in,
                        R.anim.activity_fade_out);
                break;
            case R.id.contact_select_help_cl:
                WebViewActivity.loadUrl(ContactsSelectActivity.this, HttpHelper.URL_HELP_TOTWOO, false);
                break;
            case R.id.contact_input_cl:
            case R.id.contacts_send_success_bg:
                //消费点击事件
                break;
        }
    }

    private void goneInputCl() {
        if (mInputCl.getVisibility() == View.VISIBLE) {
            mInputCl.setVisibility(View.GONE);
            hideKeyboard(mInputEt);
        }
    }

    private void goneSearchCl() {
        if (mSearchContentCl.getVisibility() == View.VISIBLE) {
            mSearchContentCl.setVisibility(View.GONE);
            hideKeyboard(mSearchEt);
        }
    }

    private void hideKeyboard(EditText editText) {
        InputMethodManager img = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        img.hideSoftInputFromWindow(editText.getWindowToken(), 0);
    }

    private void showKeyboard(EditText editText) {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
    }

    @NonNull
    @Override
    public Loader<Cursor> onCreateLoader(int id, @Nullable Bundle args) {
        Uri baseUri;
        if (!TextUtils.isEmpty(mCurFilter)) {
            LogUtils.e("aab mCurFilter = " + mCurFilter);
            baseUri = Uri.withAppendedPath(ContactsContract.CommonDataKinds.Phone.CONTENT_FILTER_URI,
                    Uri.encode(mCurFilter));
        } else {
            LogUtils.e("aab mCurFilter == null");
            baseUri = ContactsContract.CommonDataKinds.Phone.CONTENT_URI;
        }

        // Now create and return a CursorLoader that will take care of
        // creating a Cursor for the data being displayed.
        String select = "((" + ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " NOTNULL) AND ("
                + ContactsContract.CommonDataKinds.Phone.HAS_PHONE_NUMBER + "=1))";
        return new CursorLoader(ContactsSelectActivity.this, baseUri,
                new String[]{ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME, ContactsContract.CommonDataKinds.Phone.NUMBER,
                        ContactsContract.Contacts.Photo.PHOTO_ID, ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                        ContactsContract.CommonDataKinds.Phone.SORT_KEY_PRIMARY, ContactsContract.Contacts._ID}, select, null,
                ContactsContract.CommonDataKinds.Phone.SORT_KEY_PRIMARY + " COLLATE LOCALIZED ASC");
    }

    @Override
    public void onLoadFinished(@NonNull Loader<Cursor> loader, Cursor data) {
        LogUtils.e("aab mCurFilter = " + mCurFilter);
        try {
            initContacts(data);
        } catch (Exception e) {
            e.printStackTrace();
            // 权限禁止, 提示开启
            final CustomDialog dialog = new CustomDialog(this);
            dialog.setMessage(R.string.no_contact);
            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    dialog.dismiss();
                }
            });
            dialog.show();
        }

//        if (data.getCount() == 0) {
//            filterNoResultTv.setVisibility(View.VISIBLE);
//            if (TextUtils.isEmpty(mCurFilter)) {
//                filterNoResultTv.setText(R.string.no_contact);
//            } else {
//                filterNoResultTv.setText(getString(R.string.search_no_result, mCurFilter));
//            }
//        } else {
//            filterNoResultTv.setVisibility(View.GONE);
//        }
    }

    @Override
    public void onLoaderReset(@NonNull Loader<Cursor> loader) {
        LogUtils.e("aab mCurFilter = " + mCurFilter);
//        initContacts(data);
    }

    private void addBean(LocalContactsBean bean) {
        bean.setNumber(CommonUtils.checkPhoneNumber(bean.getNumber()));
        if (TextUtils.isEmpty(bean.getNumber())) {
            ToastUtils.showLong(this, getString(R.string.phone_number_invalid));
            return;
        }

        // 根据号码去重
        for (LocalContactsBean localContactsBean : selectBeans) {
            if (TextUtils.equals(localContactsBean.getNumber(), bean.getNumber())) {
                selectBeans.remove(localContactsBean);
                break;
            }
        }
        selectBeans.add(bean);
        contactSelectAdapter.notifyDataSetChanged();
        mSelectList.setVisibility(View.VISIBLE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (resultCode) {
            case 0:
                if (data != null) {
                    countryCode = data.getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                    mCityTv.setText("+" + countryCode);
                    mInputEt.requestFocus();
                    mHandler.postDelayed(() -> {
                        InputMethodManager img = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
                        img.showSoftInput(mInputEt, 0);
                    }, 150);
                }
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 取出手机号码中的无效字符, 返回简单的号码
     *
     * @param phone
     * @return
     */
    private String getSimplePhone(String phone) {
        if (phone == null) {
            return null;
        }
        // 过滤掉号码 中的空格和分割线
        char[] pp = new char[phone.length()];
        int j = 0;
        for (int i = 0; i < phone.length(); i++) {
            char c = phone.charAt(i);
            if (c != '-' && c != ' ') {
                pp[j++] = c;
            }
        }
        return new String(pp).trim();
    }

    private int type;

    /**
     * 发送贺卡操作
     */
    private void sendGreetingCard() {
        type = getIntent().getIntExtra(GiftDataActivity.TYPE, 1);
        switch (type) {
            case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                upload(ContactsSelectActivity.this, CommonArgs.CACHE_GIFT_IMAGE, null, 1);
                break;
            case CommonArgs.COMMON_SEND_TYPE_SOUND:
                upload(ContactsSelectActivity.this, CommonArgs.CACHE_GIFT_AUDIO_PATH, null, 2);
                break;
            case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                uploadCover(ContactsSelectActivity.this, getIntent().getStringExtra(CommonArgs.COVER_PATH));
                break;
        }
    }

    public void uploadCover(final Context context, final String filePath) {
        HttpHelper.card.getQiNiuToken(1, "greetingcard")
                .subscribeOn(Schedulers.io())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        showFailDialog();
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0) {
                            File file = new File(filePath);
                            if (!file.exists()) {
                                ToastUtils.showShort(context, R.string.error_net);
                            }

                            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
//                                            ToastUtils.showLong(context, R.string.upload_filed);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            upload(ContactsSelectActivity.this, getIntent().getStringExtra(CommonArgs.VIDEO_PATH), url, 3);
                                        }
                                    });
                        }
                    }
                });
    }

    public void upload(final Context context, final String filePath, final String coverPath, int qiniuType) {
        mSendingTv.setVisibility(View.VISIBLE);
        mSendingTv.setText(R.string.home_totowo_holder_sending_totwoo);
        mSendSuccessBg.setVisibility(View.VISIBLE);
        HttpHelper.card.getQiNiuToken(qiniuType, "greetingcard")
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        showFailDialog();
                        mSendingTv.setVisibility(View.GONE);
                        mSendSuccessBg.setVisibility(View.GONE);
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0) {
                            File file = new File(filePath);
                            if (!file.exists()) {
                                ToastUtils.showShort(context, R.string.error_net);
                            }

                            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
                                            showFailDialog();
                                            mSendingTv.setVisibility(View.GONE);
                                            mSendSuccessBg.setVisibility(View.GONE);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            switch (type) {
                                                case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                                                    saveInfoServer(url, null, null, null);
                                                    break;
                                                case CommonArgs.COMMON_SEND_TYPE_SOUND:
                                                    saveInfoServer(null, url, null, null);
                                                    break;
                                                case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                                                    saveInfoServer(null, null, url, coverPath);
                                                    break;
                                            }
                                        }
                                    });
                        }
                    }
                });
    }

    /**
     * 根据GreetingCard 数据加载数据类型
     *
     * @return
     */
    public static int loadGreetCardType(GiftMessageCard cardData) {
        int type = 0;
        if (cardData == null) {
            return type;
        }

        if (!TextUtils.isEmpty(cardData.getText())) {
            type += 2 >> 1;
        }

        if (!TextUtils.isEmpty(cardData.getImageUrl())) {
            type += 2;
        }

        if (!TextUtils.isEmpty(cardData.getAudioUrl())) {
            type += 2 << 1;
        }

        if (!TextUtils.isEmpty(cardData.getVedioUrl())) {
            type += 2 << 2;
        }
        return type;
    }

    private void saveInfoServer(String imgUrl, String audioUrl, String vedioUrl, String coverUrl) {
        String name = getIntent().getStringExtra(GiftInfoAddActivity.GIFT_SENDER_NAME);
        String text = getIntent().getStringExtra(GiftInfoAddActivity.GIFT_SENDER_INFO);
        Gson gson = new Gson();
        ArrayList<GiftMessageReceiverInfo> infos = new ArrayList<>();
        for (LocalContactsBean localContactsBean : selectBeans) {
            infos.add(new GiftMessageReceiverInfo(localContactsBean.getName(), localContactsBean.getNumber()));
        }
        GiftMessageCard giftMessageCard = new GiftMessageCard(imgUrl, vedioUrl, coverUrl, audioUrl, text);
        HttpHelper.card.sendCard(ToTwooApplication.owner.getTotwooId(), name, loadGreetCardType(giftMessageCard), gson.toJson(giftMessageCard), gson.toJson(infos))
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SendGreetingCardResponse>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        showFailDialog();
                        mSendingTv.setVisibility(View.GONE);
                        mSendSuccessBg.setVisibility(View.GONE);
                    }

                    @Override
                    public void onNext(HttpBaseBean<SendGreetingCardResponse> sendGreetingCardResponseHttpBaseBean) {
                        mSendingTv.setVisibility(View.GONE);
                        mSendSuccessBg.setBackgroundColor(getResources().getColor(R.color.text_color_black_hint));
                        mCardStoreLv.setVisibility(View.VISIBLE);
                        mCardStoreLv.addAnimatorListener(new Animator.AnimatorListener() {
                            @Override
                            public void onAnimationStart(Animator animation) {

                            }

                            @Override
                            public void onAnimationEnd(Animator animation) {
                                Intent intent = new Intent(ContactsSelectActivity.this, GiftMessageListActivity.class);
                                intent.putExtra(CommonArgs.FROM_TYPE, GiftMessageListActivity.SEND_SUCCESS);
                                startActivity(intent);
                                EventBus.onPostReceived(S.E.E_GIFT_SEND_SUCCEED, null);
                                ContactsSelectActivity.this.finish();
                            }

                            @Override
                            public void onAnimationCancel(Animator animation) {

                            }

                            @Override
                            public void onAnimationRepeat(Animator animation) {

                            }
                        });
                        mCardStoreLv.playAnimation();
                    }
                });
    }

    private CommonMiddleDialog commonMiddleDialog;

    private void showFailDialog() {
//        if (failDialog == null) {
//            failDialog = new CustomDialog(this);
////            failDialog.setTitle(getString(R.string.warm_tips));
//            failDialog.setMessage(getString(R.string.send_card_failed));
//            failDialog.setPositiveButton(v -> failDialog.dismiss());
//        }
//        failDialog.show();

        if (commonMiddleDialog == null) {
            commonMiddleDialog = new CommonMiddleDialog(this);
            commonMiddleDialog.setMessage(getString(R.string.send_card_failed));
            commonMiddleDialog.setSure(v -> commonMiddleDialog.dismiss());
        }
        commonMiddleDialog.show();
    }

    private class ContactSelectAdapter extends RecyclerView.Adapter<ContactSelectAdapter.ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.contacts_select_item, parent, false);
            return new ContactSelectAdapter.ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.mNameTv.setText(selectBeans.get(position).getName());
            holder.mDeleteIv.setOnClickListener(v -> {
                selectBeans.remove(position);
                contactSelectAdapter.notifyDataSetChanged();
                if (selectBeans.size() == 0) {
                    mSelectList.setVisibility(View.GONE);
                }
            });
        }

        @Override
        public int getItemCount() {
            return selectBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mDeleteIv;
            TextView mNameTv;

            public ViewHolder(View itemView) {
                super(itemView);
                mDeleteIv = (ImageView) itemView.findViewById(R.id.contact_select_delete_iv);
                mNameTv = (TextView) itemView.findViewById(R.id.contact_select_name_tv);
            }
        }
    }
}
