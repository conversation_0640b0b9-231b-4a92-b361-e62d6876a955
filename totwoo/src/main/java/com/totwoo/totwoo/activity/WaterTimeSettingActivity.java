package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.adapter.ColorLibraryAdapter;
import com.totwoo.totwoo.adapter.WaterTimeAdapter;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.WaterInfoBean;
import com.totwoo.totwoo.bean.WaterTimeBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.data.AlarmLogic;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.WaterTimeDbHelper;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.FullyLinearLayoutManager;
import com.totwoo.totwoo.widget.WheelView;

import java.util.ArrayList;
import java.util.Arrays;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/2/8.
 */

public class WaterTimeSettingActivity extends BaseActivity implements WaterTimeAdapter.WaterTimeDrinkTimeClickListener {

    @BindView(R.id.water_time_setting_content)
    LinearLayout mWaterTimeSettingContent;
    @BindView(R.id.repeat_remind_ll)
    LinearLayout repeat_remind_ll;
    @BindView(R.id.water_time_setting_recyclerView)
    RecyclerView mWaterTimeRecyclerView;

    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    //    @BindView(R.id.unit_conversion_tv)
//    TextView mUnitConversionTv;
    @BindView(R.id.water_time_long_vibration_tv)
    TextView mLongVibrationTv;
    @BindView(R.id.water_time_short_vibration_tv)
    TextView mShortVibrationTv;
    @BindView(R.id.call_switch_info_tv)
    TextView mCallSwitchInfoTv;

    @BindView(R.id.water_time_setting_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    ColorLibraryAdapter colorLibraryAdapter;

    private boolean[] repeat_remind_date_record = new boolean[7];

    private JewelryNotifyModel nowSetModel;
    private WaterTimeAdapter waterTimeAdapter;
    private ArrayList<WaterTimeBean> beans;

    private CustomBottomDialog dialog;
    private WheelView hh_wl = null;
    private WheelView mm_wl = null;
    private ArrayList<Integer> integers;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_water_time_setting);
        ButterKnife.bind(this);
        initView();
        BluetoothManage.getInstance().connectedStatus();
    }

    private void initView() {

        integers = new ArrayList<>();
        String repeatRemind = PreferencesUtils.getString(WaterTimeSettingActivity.this, WaterInfoBean.WATER_TIME_REPEAT_REMIND, "01234");
        LogUtils.e("aab repeatRemind = " + repeatRemind);
        char[] c = repeatRemind.toCharArray();
        for (int i = 0; i < c.length; i++) {
            int parseInt = Integer.parseInt(String.valueOf(c[i]));
            repeat_remind_date_record[parseInt] = true;
            integers.add(parseInt + 1);
            ((CheckBox) repeat_remind_ll.getChildAt(parseInt)).setChecked(true);
            ((CheckBox) repeat_remind_ll.getChildAt(parseInt)).setTextColor(getResources().getColor(R.color.white));
        }
        mCallSwitchInfoTv.setText(R.string.water_time_setting_hint);
        nowSetModel = NotifyUtil.getWaterTimeNotifyModel(this);

        if (nowSetModel == null) {
            return;
        }

        mCallSwitchCb.setChecked(nowSetModel.isNotifySwitch());
        mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);

        if (!nowSetModel.isNotifySwitch()) {
            mWaterTimeSettingContent.setVisibility(View.GONE);
        }

        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));

                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                setTextColorBtn(false);
                break;
        }

        colorLibraryRecyclerView.setLayoutManager(new LinearLayoutManager(WaterTimeSettingActivity.this, LinearLayoutManager.HORIZONTAL, false));
        colorLibraryAdapter = new ColorLibraryAdapter(nowSetModel.getFlashColor(), WaterTimeSettingActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                nowSetModel.setFlashColor((String) v.getTag());
                colorLibraryAdapter.setSelectColor((String) v.getTag());
                saveNowModel(false);
            }
        });

        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
        colorLibraryRecyclerView.scrollToPosition(colorLibraryAdapter.getIndex(nowSetModel.getFlashColor()));

        String defaultTime = PreferencesUtils.getString(WaterTimeSettingActivity.this, WaterInfoBean.DEFAULT_WATER_TIMES, "08:00,09:00,11:30,13:30,15:30,17:30,19:30,20:15");
        LogUtils.e("aab defaultTime = " + defaultTime);
        String[] defaultTimes = defaultTime.split(",");
        if (defaultTimes == null || defaultTimes.length != 8) {
            defaultTimes = new String[]{"08:00", "09:00", "11:30", "13:30", "15:30", "17:30", "19:30", "20:15"};
        }
        mWaterTimeRecyclerView.setLayoutManager(new FullyLinearLayoutManager(WaterTimeSettingActivity.this));
        String repeatCount = PreferencesUtils.getString(WaterTimeSettingActivity.this, WaterInfoBean.WATER_TIME_REPEAT_COUNT, "11111111");
        LogUtils.e("aab repeatCount = " + repeatCount);
        char[] count = repeatCount.toCharArray();
        beans = new ArrayList<>();
        for (int i = 0; i < 8; i++) {
            WaterTimeBean bean = new WaterTimeBean();
            bean.setOpen(Integer.valueOf(count[i] + "") == 1);
            bean.setPosition(i);
            bean.setDrinkTime(defaultTimes[i]);
            beans.add(bean);
        }
        waterTimeAdapter = new WaterTimeAdapter(beans, WaterTimeSettingActivity.this);
        waterTimeAdapter.setWaterTimeDrinkTimeListener(this);
        mWaterTimeRecyclerView.setAdapter(waterTimeAdapter);

        if (!PreferencesUtils.getBoolean(WaterTimeSettingActivity.this, WaterTimeDbHelper.WATER_TIME_DATA_INIT, false)) {
            WaterTimeDbHelper.getInstance().save(beans, integers);
            PreferencesUtils.put(WaterTimeSettingActivity.this, WaterTimeDbHelper.WATER_TIME_DATA_INIT, true);
        }

    }

    /**
     * 提交重复提醒日期数据到Sp
     */
    private void commitRepeatRemindToSp() {
        StringBuffer sb = new StringBuffer();
        integers.clear();
        for (int i = 0; i < repeat_remind_date_record.length; i++) {
            if (repeat_remind_date_record[i]) {
                sb.append(i);
                integers.add(i + 1);
            }
        }
        LogUtils.e("aab sb.toString() = " + sb.toString());
        PreferencesUtils.put(WaterTimeSettingActivity.this, WaterInfoBean.WATER_TIME_REPEAT_REMIND, sb.toString());
    }

    private void commitRepeatCountToSp() {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 8; i++) {
            if (beans.get(i).isOpen())
                sb.append(1);
            else
                sb.append(0);
        }
        LogUtils.e("aab sb.toString() = " + sb.toString());
        PreferencesUtils.put(WaterTimeSettingActivity.this, WaterInfoBean.WATER_TIME_REPEAT_COUNT, sb.toString());
    }

    private void commitTimesToSp() {
        StringBuffer sb = new StringBuffer();
        for (WaterTimeBean bean : beans) {
            sb.append(bean.getDrinkTime());
            sb.append(",");
        }
        sb.substring(0, sb.length() - 1);
        PreferencesUtils.put(WaterTimeSettingActivity.this, WaterInfoBean.DEFAULT_WATER_TIMES, sb.toString());
    }

    @OnClick({R.id.water_time_long_vibration_tv, R.id.water_time_short_vibration_tv,
            R.id.notify_switch_click_item, R.id.repeat_remind_cb_mon, R.id.repeat_remind_cb_tue,
            R.id.repeat_remind_cb_wed, R.id.repeat_remind_cb_thur, R.id.repeat_remind_cb_fri,
            R.id.repeat_remind_cb_sat, R.id.repeat_remind_cb_sun})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.notify_switch_click_item:
                mCallSwitchCb.setChecked(!mCallSwitchCb.isChecked());
                nowSetModel.setNotifySwitch(mCallSwitchCb.isChecked());
                saveNowModel(true);
                break;
            case R.id.repeat_remind_cb_mon:
            case R.id.repeat_remind_cb_tue:
            case R.id.repeat_remind_cb_wed:
            case R.id.repeat_remind_cb_thur:
            case R.id.repeat_remind_cb_fri:
            case R.id.repeat_remind_cb_sat:
            case R.id.repeat_remind_cb_sun:
                repeatRemindCheckBoxClick(view);
                break;

            case R.id.water_time_long_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(true);
                break;

            case R.id.water_time_short_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(false);
                break;
        }
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveInfo();
            }
        });
//        setTopRightIcon(R.drawable.back_icon_black);
//        setTopRightOnClick(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                WaterTimeDbHelper.getInstance().getAllInfo();
//            }
//        });
        setTopTitle(R.string.water_time_reminder);
//        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
    }

    private void saveInfo() {
        int isOpen = nowSetModel.isNotifySwitch() ? 1 : 0;

        StringBuffer sb = new StringBuffer();
        integers.clear();
        for (int i = 0; i < repeat_remind_date_record.length; i++) {
            if (repeat_remind_date_record[i]) {
                sb.append(i + 1);
                sb.append(",");
            }
        }
        String repeat_notify = sb.substring(0, sb.length() - 1);

        HttpHelper.waterTimeService.saveWaterInfo(isOpen, beans.get(0).getDrinkTime(), beans.get(1).getDrinkTime(), beans.get(2).getDrinkTime(),
                beans.get(3).getDrinkTime(), beans.get(4).getDrinkTime(), beans.get(5).getDrinkTime(), beans.get(6).getDrinkTime(), beans.get(7).getDrinkTime(),
                getIntState(beans.get(0).isOpen()), getIntState(beans.get(1).isOpen()), getIntState(beans.get(2).isOpen()), getIntState(beans.get(3).isOpen()),
                getIntState(beans.get(4).isOpen()), getIntState(beans.get(5).isOpen()), getIntState(beans.get(6).isOpen()), getIntState(beans.get(7).isOpen()),
                repeat_notify, nowSetModel.getVibrationHttpString(), nowSetModel.getFlashColor())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.newThread())
                .subscribe(new Observer<HttpBaseBean<WaterInfoBean>>() {
                    @Override
                    public void onCompleted() {
                        finish();
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(WaterTimeSettingActivity.this, R.string.error_net);
                        finish();
                    }

                    @Override
                    public void onNext(HttpBaseBean<WaterInfoBean> waterInfoBeanHttpBaseBean) {

                    }
                });
    }

    private int getIntState(boolean state) {
        return state ? 1 : 0;
    }

    private void repeatRemindCheckBoxClick(View v) {
        CheckBox checkBox = (CheckBox) findViewById(v.getId());
        if (isAllCancel() && !checkBox.isChecked()) {
            ToastUtils.showShort(WaterTimeSettingActivity.this, getString(R.string.water_time_repeat_not_null));
            checkBox.setChecked(true);
            return;
        }

        if (checkBox.isChecked()) {
            checkBox.setTextColor(getResources().getColor(R.color.white));
        } else {
            checkBox.setTextColor(getResources().getColor(R.color.text_color_gray_99));
        }

        if (repeat_remind_date_record[Integer.parseInt((String) v.getTag())]) {
            WaterTimeDbHelper.getInstance().deleteIndexOfWeek(Integer.parseInt((String) v.getTag()) + 1);
        } else {
            WaterTimeDbHelper.getInstance().addIndexOfWeek(beans, Integer.parseInt((String) v.getTag()) + 1);
        }

        repeat_remind_date_record[Integer.parseInt((String) v.getTag())] = !repeat_remind_date_record[Integer
                .parseInt((String) v.getTag())];

        commitRepeatRemindToSp();

        AlarmLogic.getInstance().notifyAlarm();
    }

    private boolean isAllCancel() {
        int count = 0;
        for (int i = 0; i < repeat_remind_date_record.length; i++) {
            if (repeat_remind_date_record[i]) {
                count++;
            }
        }
        return count <= 1;
    }

    private boolean isAllCountCancel() {
        int count = 0;
        for (int i = 0; i < 8; i++) {
            if (beans.get(i).isOpen()) {
                count++;
            }
        }
        return count <= 1;
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationTv.setTextColor(0xffffffff);
            mShortVibrationTv.setTextColor(0xde000000);
        } else {
            mShortVibrationTv.setTextColor(0xffffffff);
            mLongVibrationTv.setTextColor(0xde000000);
        }
    }


    /**
     * 保存对应的数据, 根据不同的类型, 做相应的首饰反馈
     *
     * @param isSwitch 是否是开关操作
     */
    private void saveNowModel(boolean isSwitch) {

        if (!isSwitch) {
            BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
        }
        NotifyUtil.setWaterTimeNotify(this, nowSetModel);
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_WATER_TIME_STATUS, null);
        if (isSwitch) {
            mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
            //切换时候的动画
            Animation anim = AnimationUtils.loadAnimation(this, nowSetModel.isNotifySwitch() ? R.anim.layout_open : R.anim.layout_close);
            if (nowSetModel.isNotifySwitch()) {
                mWaterTimeSettingContent.setVisibility(View.VISIBLE);
            } else {
                anim.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        mWaterTimeSettingContent.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
            }
            mWaterTimeSettingContent.startAnimation(anim);
        }
    }

    int tempPosition;

    //点击recycleview中的时间。弹出选择时间的Dialog
    @Override
    public void onClickItem(View view, int position) {
        long time = DateUtil.getStringToDate("HH:mm", beans.get(position).getDrinkTime());
        this.tempPosition = position;
        if (dialog == null) {
            dialog = new CustomBottomDialog(this);
            dialog.setTitle(getString(R.string.water_time_select_title));
            RelativeLayout layout = (RelativeLayout) View.inflate(this,
                    R.layout.sedentary_reminder_start_time_dialog, null);
            hh_wl = (WheelView) layout.findViewById(R.id.hh_wl);
            mm_wl = (WheelView) layout.findViewById(R.id.mm_wl);
            hh_wl.setItems(Arrays
                            .asList(ConfigData.SEDENTARY_REMINDER_START_TIME_HH_ARRAY), 3,
                    null);
            mm_wl.setItems(Arrays
                            .asList(ConfigData.SEDENTARY_REMINDER_START_TIME_MM_ARRAY), 3,
                    null);
            hh_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("HH", time)));
            mm_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("mm", time)));
            dialog.setMainView(layout);
            dialog.setSaveClick(v -> {
                String text = hh_wl.getSeletedItem() + ":" + mm_wl.getSeletedItem();
                if (saveSelectTime(text, tempPosition)) {
                    WaterTimeBean bean = beans.get(tempPosition);
                    bean.setDrinkTime(text);
                    beans.set(tempPosition, bean);
                    waterTimeAdapter.updateBeans(beans);
                    commitTimesToSp();
                    WaterTimeDbHelper.getInstance().updateIndexOfDay(bean, integers);
                    AlarmLogic.getInstance().notifyAlarm();
                    dialog.dismiss();
                }
            });
        } else {
            hh_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("HH", time)));
            mm_wl.setSeletion(Integer.parseInt(DateUtil.getDateToString("mm", time)));
        }
        dialog.show();
    }

    //点击recycleview中的checkBox，设置是否开启提醒。
    @Override
    public void onClickCheckBox(View view, int position) {
        CheckBox checkBox = (CheckBox) view;
        if (isAllCountCancel() && !checkBox.isChecked()) {
            ToastUtils.showShort(WaterTimeSettingActivity.this, getString(R.string.water_time_count_not_null));
            checkBox.setChecked(true);
            return;
        }
        beans.get(position).setOpen(!beans.get(position).isOpen());
        WaterTimeDbHelper.getInstance().updateIndexOfDay(beans.get(position), integers);

        commitRepeatCountToSp();
        AlarmLogic.getInstance().notifyAlarm();
    }

    private boolean saveSelectTime(String tragetTime, int item) {
        if (item == 0) {
            return isSelectAble(tragetTime, "00:00", beans.get(item + 1).getDrinkTime());
        } else if (item == beans.size() - 1) {
            return isSelectAble(tragetTime, beans.get(item - 1).getDrinkTime(), "24:00");
        } else {
            return isSelectAble(tragetTime, beans.get(item - 1).getDrinkTime(), beans.get(item + 1).getDrinkTime());
        }
    }

    private boolean isSelectAble(String targetStr, String beforeStr, String nextStr) {
        long targetLong = DateUtil.getStringToDate("HH:mm", targetStr);
        long beforeLong = DateUtil.getStringToDate("HH:mm", beforeStr);
        long nextLong = DateUtil.getStringToDate("HH:mm", nextStr);

        if (targetLong > beforeLong && targetLong < nextLong) {
            return true;
        } else {
            if (targetLong == beforeLong || targetLong == nextLong) {
                ToastUtils.showShort(WaterTimeSettingActivity.this, getString(R.string.water_time_equals));
            } else if (targetLong > beforeLong) {
                ToastUtils.showShort(WaterTimeSettingActivity.this, getString(R.string.water_time_delay_than_next));
            } else {
                ToastUtils.showShort(WaterTimeSettingActivity.this, getString(R.string.water_time_ealry_than_last));
            }
            return false;
        }
    }

}
