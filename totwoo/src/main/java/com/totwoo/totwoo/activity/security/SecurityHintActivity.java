package com.totwoo.totwoo.activity.security;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SecurityHintActivity extends BaseActivity {
    @BindView(R.id.security_hint_vvp)
    ViewPager2 verticalViewPager;
    @BindView(R.id.security_total_count_tv)
    TextView tvTotal;
    @BindView(R.id.security_count_tv)
    TextView tvCount;
    @BindView(R.id.security_hint_lav)
    LottieAnimationView mSecurityHintLav;

    private int currentPosition;
    private CommonMiddleDialog backDialog;
    private boolean justLook;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_security_hint);
        ButterKnife.bind(this);
        justLook = getIntent().getBooleanExtra(CommonArgs.FROM_TYPE, false);
        HintAdapter hintAdapter = new HintAdapter();
        verticalViewPager.setAdapter(hintAdapter);
        verticalViewPager.setOrientation(ViewPager2.ORIENTATION_VERTICAL);
        verticalViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                currentPosition = position;
                if (position == 0) {
                    tvTotal.setTextColor(getResources().getColor(R.color.text_color_white_54));
                    tvCount.setTextColor(getResources().getColor(R.color.white));
                    setTopRightIcon(R.drawable.close_icon_white_new);
                    tvCount.setText("1");
                    mSecurityHintLav.setImageAssetsFolder("lottie_security_white/");
                    mSecurityHintLav.setAnimation("security_white.json");
                    mSecurityHintLav.playAnimation();
                } else {
                    tvTotal.setTextColor(getResources().getColor(R.color.text_color_gray_cc));
                    tvCount.setTextColor(getResources().getColor(R.color.black));
                    setTopRightIcon(R.drawable.close_icon_new);
                    tvCount.setText((position + 1) + "");
                    mSecurityHintLav.setImageAssetsFolder("lottie_security_green/");
                    mSecurityHintLav.setAnimation("security_green.json");
                    mSecurityHintLav.playAnimation();
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        if (position == 7 && justLook) {
                            mSecurityHintLav.setVisibility(View.GONE);
                        } else if (position == 8) {
                            mSecurityHintLav.setVisibility(View.GONE);
                        } else {
                            mSecurityHintLav.setVisibility(View.VISIBLE);
                        }
                    } else {
                        if (position == 6 && justLook) {
                            mSecurityHintLav.setVisibility(View.GONE);
                        } else if (position == 7) {
                            mSecurityHintLav.setVisibility(View.GONE);
                        } else {
                            mSecurityHintLav.setVisibility(View.VISIBLE);
                        }
                    }
                }
            }
        });
//        ViewPager2.PageTransformer pageTransformer = new ViewPager2.PageTransformer() {
//            @Override
//            public void transformPage(@NonNull View page, float position) {
//                if (position > -1 && position < 0) {
//                    page.setAlpha(1 + position);
//                } else if(position > 0 && position <= 1){
//                    page.setAlpha(1 - position);
//                }
//            }
//        };
//        verticalViewPager.setPageTransformer(pageTransformer);
        setSpinState(false);
        mSecurityHintLav.setImageAssetsFolder("lottie_security_white/");
        mSecurityHintLav.setAnimation("security_white.json");
        mSecurityHintLav.setRepeatCount(LottieDrawable.INFINITE);
        mSecurityHintLav.playAnimation();
        mSecurityHintLav.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                verticalViewPager.setCurrentItem(currentPosition + 1, true);
            }
        });
        setTopRightIcon(R.drawable.close_icon_white);
    }

    @Override
    protected void initTopBar() {
        setTopRightOnClick(v -> skipActivity());
    }

    public class HintAdapter extends RecyclerView.Adapter<HintAdapter.ViewHolder> {
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.security_hint_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            LogUtils.e("aab position = " + position);
            switch (position) {
                case 0:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_1);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_1_en);
                    }
                    break;
                case 1:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_2);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_2_en);
                    }
                    break;
                case 2:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_3);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_3_en);
                    }
                    break;
                case 3:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_4);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_4_en);
                    }
                    break;
                case 4:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_5);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_5_en);
                    }
                    break;
                case 5:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_6);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_6_en);
                    }
                    break;
                case 6:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_7);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_7_en);
                    }
                    break;
                case 7:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_8);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_8_en);
                    }
                    break;
                case 8:
                    if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_9);
                    } else {
                        holder.mInfoIv.setImageResource(R.drawable.security_hint_8_en);
                    }
                    holder.mTextIv.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            saveState();
                        }
                    });
                    break;
            }
        }

        @Override
        public int getItemCount() {
            if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
                return justLook ? 8 : 9;
            } else {
                return justLook ? 7 : 8;
            }
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mInfoIv;
            ImageView mTextIv;

            public ViewHolder(View itemView) {
                super(itemView);
                mInfoIv = (ImageView) itemView.findViewById(R.id.security_info);
                mTextIv = (ImageView) itemView.findViewById(R.id.security_text);
            }
        }
    }

    private void showBackDialog() {
        backDialog = new CommonMiddleDialog(this);
        backDialog.setTitle(getString(R.string.warm_tips));
        backDialog.setMessage(getString(R.string.safe_security_hint_info));
        backDialog.setSure(R.string.safe_security_hint_cancel, v -> {
            backDialog.dismiss();
        });
        backDialog.setCancel(R.string.safe_security_hint_close, v -> {
            saveState();
            backDialog.dismiss();
        });
        backDialog.show();
    }

    private void skipActivity() {
        if (Apputils.systemLanguageIsChinese(SecurityHintActivity.this)) {
            if (currentPosition == 7 && justLook) {
                saveState();
            } else if (currentPosition == 8) {
                saveState();
            } else {
                showBackDialog();
            }
        } else {
            if (currentPosition == 6 && justLook) {
                saveState();
            } else if (currentPosition == 7) {
                saveState();
            } else {
                showBackDialog();
            }
        }
    }

    private void saveState() {
        if (!justLook) {
            PreferencesUtils.put(SecurityHintActivity.this, SecurityHomeActivity.HAS_READ_HINT, true);
            startActivity(new Intent(SecurityHintActivity.this, SecurityNewListActivity.class).putExtra(CommonArgs.FROM_TYPE, SecurityNewListActivity.INIT_STATUS));
        }
        finish();
    }

}
