package com.totwoo.totwoo.activity.memory;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.component.bitmap.BitmapUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;

import java.text.SimpleDateFormat;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/4.
 */

public class MemoryPhotoShowActivity extends BaseActivity implements SubscriberListener, ViewPager.OnPageChangeListener
{
    @BindView(R.id.memory_photo_show_vp)
    public ViewPager vp;

    @BindView(R.id.memory_photo_show_tv)
    public TextView tv;

    private MemoryBean mb;
    private boolean isSingleMode;
    private ImageAdapter adapter;
    private CustomDialog dialog;

    private Dialog dialog1;

    private boolean isShowTitle = true;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_photo_show1);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        initData();
        initTopBar2();
        initViewPager();
        setContent(0);
    }

    private void setContent(int position)
    {
        if (isSingleMode)
        {
            tv.setVisibility(View.GONE);
            return;
        }

        tv.setVisibility(isShowTitle ? View.VISIBLE : View.GONE);
        String src = (position+1) + "/" + mb.img_url.length + "  " + mb.content;
        tv.setText(src);
    }

    private void initData()
    {
        Intent i = this.getIntent();
        mb = (MemoryBean) i.getSerializableExtra(S.M.M_IMAGES);
        isSingleMode = i.getBooleanExtra(S.M.M_SINGLE, false);
    }

    private void initTopBar2()
    {
        if (isSingleMode)
            return;

        setTopBackIcon(R.drawable.back_icon_white);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_white_important));
        /*setTopRightIcon(R.drawable.memory_photo_show_delete);
        getTopBar().setBackgroundColor(getResources().getColor(R.color.text_color_black_note));
        setTopRightOnClick(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                getPhotoDialog();
            }
        });*/
    }

    private void initViewPager()
    {
        vp.addOnPageChangeListener(this);
        ImageView[] ivs = new ImageView[mb.img_url.length];
        for (int i = 0; i < ivs.length; i++)
        {
            ivs[i] = new ImageView(this);
            ivs[i].setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
            ivs[i].setLayoutParams(lp);
            ivs[i].setOnClickListener(new View.OnClickListener()
            {
                @Override
                public void onClick(View v)
                {
                    if (isSingleMode)
                        MemoryPhotoShowActivity.this.finish();
                    else
                        showOrHideTitle();
                }
            });
        }
        adapter = new ImageAdapter(ivs);
        vp.setAdapter(adapter);
    }

    private void showOrHideTitle()
    {
        isShowTitle = !isShowTitle;
        getTopBar().setVisibility(isShowTitle ? View.VISIBLE : View.GONE);
        tv.setVisibility(isShowTitle ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        if (dialog1 != null)
            dialog1.dismiss();
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels)
    {

    }

    @Override
    public void onPageSelected(int position)
    {
        setContent(position);
    }

    @Override
    public void onPageScrollStateChanged(int state)
    {

    }

    private class ImageAdapter extends PagerAdapter
    {
        private ImageView[] viewlist;
        BitmapUtils bu;
        public ImageAdapter(ImageView[] viewlist)
        {
            this.viewlist = viewlist;
            bu = new BitmapUtils(MemoryPhotoShowActivity.this);
            bu.configDefaultLoadingImage(R.mipmap.ic_launcher);
        }

        @Override
        public int getCount()
        {
            return viewlist.length;
        }

        @Override
        public boolean isViewFromObject(View view, Object object)
        {
            return view == object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position)
        {
            View v = viewlist[position];
            ((ViewPager)container).addView(v);
            String url = mb.img_url[position];
            /*File f = new File(url);
            if (f.exists())
                BitmapHelper.display(MemoryPhotoShowActivity.this, viewlist[position], f, R.drawable.ic_launcher);
            else
                BitmapHelper.display(MemoryPhotoShowActivity.this, viewlist[position], url);*/
            bu.display(viewlist[position], url);
//            BitmapHelper.display(MemoryPhotoShowActivity.this, viewlist[position], new File(url), R.drawable.memory_set_edit);
            return v;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object)
        {
            ((ViewPager)container).removeView(viewlist[position]);
        }
    }

    public void getPhotoDialog()
    {
        dialog = new CustomDialog(this);
        dialog.setTitle("");
        dialog.setMessage(R.string.memory_delete);
        dialog.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                MemoryController.getInstance().delete(mb);
            }
        });
        dialog.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void onDeleteMemorySuccessed(EventData data)
    {
        ToastUtils.showShort(this, R.string.memory_delete_success);
        if (dialog1 != null)
            dialog1.dismiss();
        this.finish();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_FAILED, runThread = TaskType.UI)
    public void onDeleteMemoryFailed(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        String msg = hv.errorMesg;
        ToastUtils.showShort(this, msg);
        if (dialog1 != null)
            dialog1.dismiss();
    }
}