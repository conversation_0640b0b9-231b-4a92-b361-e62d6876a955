package com.totwoo.totwoo.activity.homeActivities;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.security.ImeiInfoUpdateActivity;
import com.totwoo.totwoo.activity.security.SecurityHintActivity;
import com.totwoo.totwoo.activity.security.SecurityNewListActivity;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.DiscoverSafeFragment;
import com.totwoo.totwoo.fragment.MeFragment;
import com.totwoo.totwoo.fragment.SafeNewFragment;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.ArrayList;

public class SecurityHomeActivity extends HomeBaseActivity {
    public static final String IS_IMEI_SENT = "is_imei_sent";
    public static final String HAS_READ_HINT = "has_read_hint";
    public static final String READ_CONTACT_HINT = "read_contact_hint";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Class<? extends BaseFragment>[] baseFragments = new Class[3];

        baseFragments[0] = SafeNewFragment.class;
        baseFragments[1] = DiscoverSafeFragment.class;
        baseFragments[2] = MeFragment.class;

        ArrayList<HomepageBottomInfo> infos = new ArrayList<>();
        infos.add(new HomepageBottomInfo(R.drawable.new_home_safe_un, R.drawable.new_home_safe, R.string.safe));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_discover_un, R.drawable.new_home_discover, R.string.discover));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_me_un, R.drawable.new_home_me, R.string.user));
        super.setBottomInfo(infos);
        super.setTextColor(getResources().getColor(R.color.bottom_green_select_color), getResources().getColor(R.color.bottom_green_default_color));
        super.setFragmentsAndInitViewpager(baseFragments);
//        super.setCurrentFromType(1);
        super.setTotwooIndex(-1);

        if (!PreferencesUtils.getBoolean(this, IS_IMEI_SENT, false)) {
            mHandler.postDelayed(() -> {
                startActivity(new Intent(SecurityHomeActivity.this, ImeiInfoUpdateActivity.class));
                overridePendingTransition(R.anim.activity_slide_in_bottom, R.anim.activity_slide_out_bottom);
            }, 500);
        } else if (!PreferencesUtils.getBoolean(this, HAS_READ_HINT, false) && Apputils.systemLanguageIsChinese(SecurityHomeActivity.this)) {
            startActivity(new Intent(SecurityHomeActivity.this, SecurityHintActivity.class));
            overridePendingTransition(R.anim.activity_slide_in_bottom, R.anim.activity_slide_out_bottom);
        } else if (!PreferencesUtils.getBoolean(this, READ_CONTACT_HINT, false)) {
            startActivity(new Intent(SecurityHomeActivity.this, SecurityNewListActivity.class).putExtra(CommonArgs.FROM_TYPE, SecurityNewListActivity.INIT_STATUS));
        }
        super.setBottomLlColor(R.color.white);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
