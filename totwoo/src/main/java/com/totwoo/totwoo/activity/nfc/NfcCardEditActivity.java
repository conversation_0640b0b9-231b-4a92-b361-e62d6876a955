package com.totwoo.totwoo.activity.nfc;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.widget.EditText;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.data.nfc.SecretInfoBean;
import com.totwoo.totwoo.data.nfc.SecretInfoManager;
import com.totwoo.totwoo.data.nfc.SecretType;
import com.totwoo.totwoo.utils.BlackAlertDialogUtil;
import com.totwoo.totwoo.utils.BlackBottomSheetDialog;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.RoundImageView;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * 名片编辑模块, 包括商务名片和个性化名片
 */
public class NfcCardEditActivity extends BaseActivity {
    private static final int CARD_NOTE_MAX_LENGTH = 50;

    /**
     * extra data type: 名片数据
     */
    public static final String EXTRA_NFC_SECRET_CARD_DATA = "pref_nfc_secret_card_data";

    private HashMap<String, EditText> valueViews;

    private SecretInfoBean beanData;

    private TextView noteCountTv;

    private EditText noteTv;

    private TextWatcher checkChangeWatcher;

    /**
     * 标识截止到上次保存, 是否有变更内容
     */
    private boolean hasChangeNotSave;

    private RoundImageView headIcon;
    private Uri userHeadPortrait;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        beanData = (SecretInfoBean) getIntent().getSerializableExtra(EXTRA_NFC_SECRET_CARD_DATA);

        if (beanData == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        setContentView(isBusinessCard() ? R.layout.activity_card_edit_business : R.layout.activity_card_edit_social);
        CommonUtils.setStateBarTransparentForBlackUI(this);

        checkChangeWatcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                hasChangeNotSave = true;
            }
        };
        valueViews = new HashMap<>();

        noteTv = (EditText) findViewById(R.id.nfc_card_edit_note);
        // 汇总两个卡片所需的字段
        if (isBusinessCard()) {
            //姓名
            valueViews.put("fullname", (EditText) findViewById(R.id.nfc_card_edit_name));
            //公司
            valueViews.put("company", (EditText) findViewById(R.id.nfc_card_edit_company));
            //电话
            valueViews.put("tel", (EditText) findViewById(R.id.nfc_card_edit_telephone));
            //职位
            valueViews.put("position", (EditText) findViewById(R.id.nfc_card_edit_job));
            //地址
            valueViews.put("address", (EditText) findViewById(R.id.nfc_card_edit_address));
            //邮箱
            valueViews.put("email", (EditText) findViewById(R.id.nfc_card_edit_email));
//            //微信
//            valueViews.put("wechat", (EditText) findViewById(R.id.nfc_card_edit_wechat));
//            //qq
//            valueViews.put("qq", (EditText) findViewById(R.id.nfc_card_edit_qq));
            //备注
            valueViews.put("remarks", noteTv);
        } else {
            valueViews.put("city", (EditText) findViewById(R.id.nfc_card_edit_city));

            valueViews.put("tel", (EditText) findViewById(R.id.nfc_card_edit_phone));

            valueViews.put("nickname", (EditText) findViewById(R.id.nfc_card_edit_nickname));

//            valueViews.put("constellation", (EditText) findViewById(R.id.nfc_card_edit_constellation));

            // 爱好
            valueViews.put("hobby", (EditText) findViewById(R.id.nfc_card_edit_hobbies));
            // 介绍
            valueViews.put("introduce", noteTv);
//            // 微信
//            valueViews.put("wechat", (EditText) findViewById(R.id.nfc_card_edit_wechat));
//            // QQ
//            valueViews.put("qq", (EditText) findViewById(R.id.nfc_card_edit_qq));
        }

        for (EditText editText : valueViews.values()) {
            editText.addTextChangedListener(checkChangeWatcher);
        }

        // 计数更新
        noteCountTv = findViewById(R.id.nfc_card_edit_note_count);
        if (noteTv != null) {
            noteTv.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                }

                @Override
                public void afterTextChanged(Editable s) {
                    int max = Apputils.systemLanguageIsChinese(NfcCardEditActivity.this) ? CARD_NOTE_MAX_LENGTH : CARD_NOTE_MAX_LENGTH * 2;
                    noteCountTv.setText(String.format(Locale.getDefault(), "%d/%d", s.length(), max));

                    if (s.length() > max) {
                        noteTv.setText(s.subSequence(0, max));
                        noteTv.setSelection(noteTv.getSelectionEnd());
                    }
                }
            });
        }

        findViewById(R.id.nfc_card_edit_save).setOnClickListener(v -> checkAndSave());
//        findViewById(R.id.nfc_card_edit_basic_layout).setOnClickListener(v -> goForBasicInfo());
        findViewById(R.id.nfc_card_edit_back).setOnClickListener(v -> backAndCheckSave());
        findViewById(R.id.nfc_secret_card_head_icon).setOnClickListener(v -> showHeadIconDialog());

        headIcon = findViewById(R.id.nfc_secret_card_head_icon);

        // 根据数据刷新 UI
        refreshUI();
    }

    private void refreshUI() {
        for (String key : valueViews.keySet()) {
            if (beanData.getExtraData() != null && beanData.getExtraData().get(key) != null) {
                valueViews.get(key).setText(beanData.getExtraData().get(key));
            }
        }

        importBasicInfoAndSetHeadIcon(beanData);


        mHandler.post(() -> hasChangeNotSave = false);
    }


    /**
     * 是否商务卡片
     *
     * @return
     */
    private boolean isBusinessCard() {
        return SecretType.TYPE_BUSINESS.equals(beanData.getType().type);
    }

    /**
     * 刷新页面的基础信息
     *
     * @param beanData
     */
    private void importBasicInfoAndSetHeadIcon(SecretInfoBean beanData) {
        RequestOptions options = new RequestOptions()
                .error(ToTwooApplication.owner.getGender() == 0 ? R.drawable.default_head_yellow : R.drawable.default_head_yellow);

        String headerUrl;
        if (beanData.getExtraData() == null || TextUtils.isEmpty(headerUrl = beanData.getExtraData().get("head_portrait"))) {
            headerUrl = owner.getHeaderUrl();
            if (beanData.getExtraData() == null) {
                beanData.setExtraData(new HashMap<>());
            }
            beanData.getExtraData().put("head_portrait", headerUrl);
        }
        Glide.with(this).load(BitmapHelper.checkRealPath(headerUrl)).apply(options).into(headIcon);

        if (!isBusinessCard()) {
            if (valueViews.get("city").getText().length() == 0 && !TextUtils.isEmpty(ToTwooApplication.owner.getCity())) {
                ((TextView) findViewById(R.id.nfc_card_edit_city)).setText(ToTwooApplication.owner.getCity());
            }

//            if (valueViews.get("constellation").getText().length() == 0 && owner.getBirthday() != null) {
//                String[] birth = owner.getBirthday().split("-");
//                int month = Integer.parseInt(birth[1]);
//                int day = Integer.parseInt(birth[2]);
//                ((TextView) findViewById(R.id.nfc_card_edit_constellation)).setText(Apputils.getAstro(this, month, day));
//            }

            if (valueViews.get("nickname").getText().length() == 0 && owner.getNickName() != null) {
                valueViews.get("nickname").setText(owner.getNickName());
            }

            if (valueViews.get("tel").getText().length() == 0 && owner.getPhone() != null) {
                valueViews.get("tel").setText(owner.getPhone());
            }
        }
    }


    private void showHeadIconDialog() {
        BlackBottomSheetDialog dialog = new BlackBottomSheetDialog(this);
        dialog.setContentView(R.layout.dialog_inside_pic_black);

        TextView mAlbum = dialog.getWindow().findViewById(R.id.pic_album_tv);
        TextView mCamera = dialog.getWindow().findViewById(R.id.pic_camera_tv);
        dialog.getWindow().findViewById(R.id.dialog_close).setOnClickListener(v -> dialog.dismiss());
        mAlbum.setOnClickListener(v -> {
            PictureSelectUtil.with(this).gallery().crop().shapeOval(true).setCallback(new PictureSelectUtil.OnCallback() {
                @Override
                public void onCallback(Uri uri) {
                    Glide.with(NfcCardEditActivity.this).load(uri).into(headIcon);
                    userHeadPortrait = uri;
                    dialog.dismiss();
                }
            }).select();
        });
        // 相机tv监听点击开启拍照app
        mCamera.setOnClickListener(v -> {
            PictureSelectUtil.with(this).camera().crop().shapeOval(true).setCallback(new PictureSelectUtil.OnCallback() {
                @Override
                public void onCallback(Uri uri) {
                    Glide.with(NfcCardEditActivity.this).load(uri).into(headIcon);
                    userHeadPortrait = uri;
                    dialog.dismiss();
                }
            }).select();
            dialog.dismiss();
        });
        dialog.show();
    }


    // 把bitmap转成file上传服务器
    public void postUserHeadPortrait(Uri uri) {
        NfcLoading.show(this);
        // 上传图片
        HttpHelper.card.getQiNiuToken(1, "nfc_card_head_" + beanData.getType().type).subscribeOn(Schedulers.io()).subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {
                ToastUtils.showLong(NfcCardEditActivity.this, R.string.upload_filed);
            }

            @Override
            public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                byte[] fileBytes = CommonUtils.getFileBytesFromUri(NfcCardEditActivity.this, uri);
                if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0 && fileBytes != null) {
                    RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), fileBytes);
                    MultipartBody.Part part = MultipartBody.Part.createFormData("file", "card_head_icon", requestFile);
                    HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Subscriber<QiNiuResponse>() {
                                @Override
                                public void onCompleted() {

                                }

                                @Override
                                public void onError(Throwable e) {
                                    ToastUtils.showLong(NfcCardEditActivity.this, R.string.upload_filed);
                                }

                                @Override
                                public void onNext(QiNiuResponse qiNiuResponse) {
                                    // 上传成功
                                    LogUtils.i("upload image success!" + qiNiuResponse.getKey());
                                    beanData.getExtraData().put("head_portrait", qiNiuResponse.getKey());
                                    userHeadPortrait = null;
                                    checkAndSave();
                                }
                            });
                }
            }
        });
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            backAndCheckSave();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 返回, 并确认保存
     */
    private void backAndCheckSave() {
        if (hasChangeNotSave) {
            BlackAlertDialogUtil.showCommonDialog(this, R.string.not_save_warn, R.string.confirm, this::finish);
        } else {
            finish();
        }
    }

    /**
     * 检查必选项, 并执行保存逻辑
     */
    private void checkAndSave() {
        // 检查
        if (isBusinessCard()) {
            if (valueViews.get("fullname").getText().length() == 0 ||
                    valueViews.get("company").getText().length() == 0 ||
                    valueViews.get("tel").getText().length() == 0) {
                ToastUtils.showLong(this, R.string.no_must_info_warning);
                return;
            }
        }

        // 如果有头像的变更, 优先上传图片, 成功后会重新触发此方法的执行
        if (userHeadPortrait != null) {
            postUserHeadPortrait(userHeadPortrait);
            return;
        }

        final boolean isAdd = beanData.getId() == null;

        if (beanData.getExtraData() == null) {
            beanData.setExtraData(new HashMap<>());
        }

        beanData.getExtraData().put("app_name", beanData.getType().type);

        for (Map.Entry<String, EditText> entry : valueViews.entrySet()) {
            beanData.getExtraData().put(entry.getKey(), entry.getValue().getText().toString());
        }

        NfcLoading.show(this);
        SecretInfoManager.getInstance().saveSecretCard(beanData, code -> {
            NfcLoading.dismiss();
            if (code == 0) {
                hasChangeNotSave = false;

                startActivity(new Intent(this, NfcCardPrewActivity.class)
                        .putExtra(NfcCardPrewActivity.EXTRA_NFC_SECRET_CARD_DATA, beanData)
                        .putExtra(NfcCardPrewActivity.EXTRA_SHOW_SUCCESS_NOTIFY, true));
                if (isAdd) {
                    SecretInfoManager.getInstance().getSavedInfos().add(beanData);
                }
                setResult(RESULT_OK, new Intent().putExtra(EXTRA_NFC_SECRET_CARD_DATA, beanData));
                finish();
            } else {
                ToastUtils.showLong(this, HttpHelper.getUnknownErrorMessage(this, String.valueOf(code)));
            }
        });
    }
}
