package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.bean.SleepEventTestData;
import com.totwoo.totwoo.ble.BluetoothManage;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class TestSleepActivity extends BaseActivity implements SubscriberListener {

    private ArrayList<String> strings = new ArrayList<>();
    private TestSleepAdapter testSleepAdapter;
    @BindView(R.id.sleep_test_rv)
    RecyclerView testSleepRv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_test_sleep);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        testSleepAdapter = new TestSleepAdapter();
        testSleepRv.setLayoutManager(new LinearLayoutManager(TestSleepActivity.this, RecyclerView.VERTICAL, false));
        testSleepRv.setAdapter(testSleepAdapter);
    }

    @OnClick({R.id.sleep_test_clear, R.id.sleep_test_2880, R.id.sleep_test_65, R.id.sleep_test_600})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.sleep_test_clear:
                strings.clear();
                testSleepAdapter.notifyDataSetChanged();
                break;
            case R.id.sleep_test_2880:
                BluetoothManage.getInstance().writeSleepMessage(2880);
                break;
            case R.id.sleep_test_65:
                BluetoothManage.getInstance().writeSleepMessage(65);
                break;
            case R.id.sleep_test_600:
                BluetoothManage.getInstance().writeSleepMessage(600);
                break;
        }
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    protected class TestSleepAdapter extends RecyclerView.Adapter<TestSleepAdapter.ViewHolder> {
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.test_sleep_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.mInfoTv.setText(strings.get(position));
        }

        @Override
        public int getItemCount() {
            return strings == null ? 0 : strings.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.sleep_test_item_info_tv)
            TextView mInfoTv;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    @EventInject(eventType = S.E.E_SLEEP_TEST_DATA_RECEIVE, runThread = TaskType.UI)
    public void onEventSleepDataReceive(EventData data) {
        SleepEventTestData sleepEventTestData = (SleepEventTestData) data;
        strings.add(sleepEventTestData.getData());
        testSleepAdapter.notifyDataSetChanged();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }
}
