package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import butterknife.BindView;
import butterknife.ButterKnife;

public class ReminderTouchHintActivity extends BaseActivity {
    @BindView(R.id.reminder_touch_hint_close_iv)
    ImageView mCloseIv;
    @BindView(R.id.reminder_touch_hint_iv)
    ImageView mTouchHintIv;
    @BindView(R.id.reminder_touch_hint_tv1)
    TextView mTouchHintTv1;
    private CommonMiddleDialog commonMiddleDialog;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_reminder_touch_hint);
        ButterKnife.bind(this);
        setSpinState(false);

        if(!Apputils.systemLanguageIsChinese(ReminderTouchHintActivity.this)){
            mTouchHintIv.setImageResource(R.drawable.reminder_touch_hint_top_en);
            mTouchHintTv1.setVisibility(View.GONE);
        }

//        int width = CommonUtils.getScreenWidth()*4/5;
//        ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(width,width);
//        layoutParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
//        layoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
//        layoutParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
//        layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
//        layoutParams.verticalBias = 0.35f;
//        lottieAnimationView.setLayoutParams(layoutParams);

        commonMiddleDialog = new CommonMiddleDialog(this);
        commonMiddleDialog.setInfo(R.string.reminder_hint_close_hint);
        commonMiddleDialog.setTitle(R.string.warm_tips);
        commonMiddleDialog.setSure(R.string.confirm_resume,v -> {
            finish();
            overridePendingTransition(R.anim.activity_slide_in_bottom, R.anim.activity_slide_out_bottom);});
        commonMiddleDialog.setCancel(R.string.cancel_resume);

        mCloseIv.setOnClickListener(v -> showDialog());
    }

    @Override
    public void onBackPressed() {
        showDialog();
    }

    private void showDialog(){
        commonMiddleDialog.show();
    }
}
