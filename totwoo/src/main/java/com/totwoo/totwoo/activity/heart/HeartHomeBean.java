package com.totwoo.totwoo.activity.heart;

import com.etone.framework.utils.JSONUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/5/26.
 */

public class HeartHomeBean
{
    //背景图片网址
    public String backgroundPictureUrl;

    //配对对方首饰连接状态，0：未匹配，1：已匹配
    public int target_jewelry_status;

    //在一起的时间
    public int together_data;

    //一起走过的路
    public double walk_data;

    //我的运程
    public YunCheng self;

    //它的运程
    public YunCheng other;

    //心有灵犀的次数
    public int xinCount;

    //对方昵称
    public String otherNickName;

    //对方电话号码
    public String otherPhone;

    //对方头像
    public String otherPortarit;

    public String lastMsgId;

    public HeartHomeBean()
    {

    }

    public HeartHomeBean(String json)
    {
        this.backgroundPictureUrl = JSONUtils.getString(json, "backgroundPictureUrl", "");
        if (!this.backgroundPictureUrl.contains("http://"))
            this.backgroundPictureUrl = "http://image.totwoo.com/" + this.backgroundPictureUrl;
        this.target_jewelry_status = JSONUtils.getInt(json, "target_jewelry_status", 0);
        this.together_data = JSONUtils.getInt(json, "together_data", 0);
        this.walk_data = JSONUtils.getInt(json, "walk_data", 0);

        String tmp = JSONUtils.getString(json, "yuncheng_data", "");
        self = new YunCheng(JSONUtils.getString(tmp, "self", ""));
        other = new YunCheng(JSONUtils.getString(tmp, "target", ""));
    }

    public class YunCheng
    {
        int stars;
        String txt;
        String txtEn;

        public YunCheng(String json)
        {
            String res = JSONUtils.getString (json, "today", "");
            this.stars = JSONUtils.getInt (res, "all", 0);
            this.txt = JSONUtils.getString (res, "summary", "");
            this.txtEn = JSONUtils.getString (res, "summary_en", "");
        }
    }
}
