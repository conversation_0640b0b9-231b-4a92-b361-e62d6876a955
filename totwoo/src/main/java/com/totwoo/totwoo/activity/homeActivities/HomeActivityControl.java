package com.totwoo.totwoo.activity.homeActivities;

import static com.totwoo.totwoo.activity.BaseActivity.KILLACTIVITYS;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.AutoLoginActivity;
import com.totwoo.totwoo.activity.PasswordLoginActivity;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.PreferencesUtils;

public class HomeActivityControl {
    private static volatile HomeActivityControl instance;

    private int homeActivityType = -1;
    private int lastType;

    private HomeActivityControl() {

    }

    public static HomeActivityControl getInstance() {
        if (instance == null) {
            synchronized (HomeActivityControl.class) {
                instance = new HomeActivityControl();
            }
        }
        return instance;
    }

    /**
     * Two09是记忆
     * Two10是天使
     * Two40是安全
     * Two33、34、35是提醒
     * Two24、31是许愿
     * 默认和其他都是love
     */
    public void openHomeActivity(Context activity) {
        activity.startActivity(new Intent(activity, getTagertClass()));
        LogUtils.e("HomeActivityControl lastType = " + lastType);
        lastType = homeActivityType;
    }

    public void openLoginActivity(Context context) {
        if (PreferencesUtils.getString(context, CommonArgs.PREF_LAST_ENCODE_PASSWORD, null) != null
                && PreferencesUtils.getString(context, CommonArgs.PREF_LAST_PHONE, null) != null) {

            context.startActivity(new Intent(context, AutoLoginActivity.class));
        } else {
            context.startActivity(new Intent(context, PasswordLoginActivity.class));
        }
    }

    public Class<? extends  HomeBaseActivity> getTagertClass() {
        String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        LogUtils.e("HomeActivityControl jewName = " + jewName);
        if (TextUtils.isEmpty(jewName)) {
//            // 对于为空的情况, 重新查询一下本地的数据, 如果有本地数据的话, 进行一次数据的补充
//            try {
//                List<String> names = LocalJewelryDBHelper.getInstance().getAllNames();
//                if (names.size() > 0 && !TextUtils.isEmpty(names.get(0))) {
//                    jewName = names.get(0);
//                    PreferencesUtils.put(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, jewName);
//                    return getTagertClass();
//                }
//            } catch (DbException e) {
//                e.printStackTrace();
//            }
            homeActivityType = 1;
            return LoveHomeActivity.class;
        }
        if (BleParams.isMemoryJewelry()) {
            homeActivityType = 2;
            return MemoryHomeActivity.class;
        } else if (TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_SL)) {
            homeActivityType = 3;
            return AngelHomeActivity.class;
        } else if (BleParams.isSecurityJewlery()) {
            homeActivityType = 4;
            return SecurityHomeActivity.class;
        } else if (BleParams.isMWJewlery()) {
            homeActivityType = 5;
            return LoveHomeActivity.class;
        } else if (BleParams.isWishJewlery()) {
            homeActivityType = 6;
            return WishHomeActivity.class;
        } else if (BleParams.isLollipopJewelry()) {
            homeActivityType = 7;
            return LollipopHomeActivity.class;
        } else if (BleParams.isNfcJewelry(jewName)) {
            return NFCHomeActivity.class;
        } else {
            homeActivityType = 1;
            return LoveHomeActivity.class;
        }
    }

    public void connectJew(Activity activity) {
        Class<?> cls = getTagertClass();
        LogUtils.e("HomeActivityControl lastType = " + lastType);
        LogUtils.e("HomeActivityControl homeActivityType = " + homeActivityType);

        activity.startActivity(new Intent(activity, cls).putExtra("PAGE_FROM", "connectJew"));

//        if (lastType != homeActivityType) {
            LogUtils.e("HomeActivityControl kill others");
            Intent intent = new Intent();
            intent.setAction(KILLACTIVITYS);
            intent.putExtra("is_token", false);
            activity.sendBroadcast(intent);
//        }
        lastType = homeActivityType;
    }
}
