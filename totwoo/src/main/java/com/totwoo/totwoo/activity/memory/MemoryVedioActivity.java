package com.totwoo.totwoo.activity.memory;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.ThumbnailUtils;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.camera.view.PreviewView;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.VideoSelectActivity;
import com.totwoo.totwoo.record.CameraXRecorder;
import com.totwoo.totwoo.record.RecorderConfig;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

import java.io.File;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

/**
 * Created by xinyoulingxi on 2017/8/7.
 */

public class MemoryVedioActivity extends BaseActivity implements View.OnClickListener, CameraXRecorder.OnRecordStateChangeListener {
    public static final String MAKE_CARD_RE_VIDEO_PREVIEW_NAME = "make_card_re_video_preview";
    public static final String MAKE_CARD_RE_VIDEO_NAME = "memory_video_name";
    public static final String MAKE_CARD_RE_VIDEO_PATH = FileUtils.getCacheDir() + File.separator + MAKE_CARD_RE_VIDEO_NAME;
    public static final String PREF_LOCAL_VIDEO_PATH = "pref_local_path";

    public static final String IS_LOCAL_VIDEO = "is_local_video";

    private static final int SELECT_LOCAL_VIDEO = 303;
    public static int VIDEO_RE_COMPLETE = 1;
    public static int VIDEO_RE_CANCEL = 0;

    @BindView(R.id.progress_left_iv)
    ImageView mProgressLeftIv;
    @BindView(R.id.progress_right_iv)
    ImageView mProgressRightIv;
    @BindView(R.id.guide_text_tv)
    TextView mGuideTextTv;
    @BindView(R.id.re_video_iv)
    ImageView mReVideoIv;
    @BindView(R.id.cancel_action_rect_ll)
    LinearLayout mCancelActionRectLl;
    @BindView(R.id.video_preview)
    PreviewView video_preview;

    @BindView(R.id.activity_memory_secs)
    TextView secs;

    int[] cancelPostion = new int[2];

    int cancelTop;

    int cancelBottom;

    private CameraXRecorder mRecorder;

    private int cameraPosition = 0;

    private boolean reVideoing;
    private SecsRunnable runnable;

//    private MediaRecorder mMediaRecorder = new MediaRecorder();


    private long reSec;

    private boolean islock;

    Subscription reVideoStart;

    // 输出宽度
    private static final int OUTPUT_WIDTH = 720;
    // 输出高度
    private static final int OUTPUT_HEIGHT = 720;

    private String content;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_vedio);
        ButterKnife.bind(this);
        setResult(VIDEO_RE_CANCEL);
        init();
        CommonUtils.setStateBar(this, true);
        runnable = new SecsRunnable();
        content = getIntent().getStringExtra(S.M.M_CONTENT);

        PermissionUtil.hasAudioPermission(this);
    }

    @OnClick({R.id.top_bar_right_icon, R.id.upload_local_video_iv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.top_bar_right_icon:
                if (mRecorder != null) {
                    mRecorder.switchCamera();
                }
                break;
            case R.id.upload_local_video_iv:
                Intent intent = new Intent(Intent.ACTION_PICK, null);
                intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "video/*");
                startActivityForResult(intent, SELECT_LOCAL_VIDEO);
//                startActivityForResult(new Intent(MemoryVedioActivity.this, VideoSelectActivity.class), 0);

                Toast.makeText(this, R.string.upload_local_video_pre_info, Toast.LENGTH_LONG).show();
                break;
        }
    }

//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        if (requestCode == SELECT_LOCAL_VIDEO && resultCode == RESULT_OK) {
//            if (data != null && data.getData() != null) {
//                String path;
//                long size;
//                Uri uri = data.getData();
//                if (uri.getScheme().equals("content")) {
//                    ContentResolver cr = getBaseContext().getContentResolver();
//                    Cursor cursor = cr.query(uri, null, null, null, null);
//                    if (cursor != null) {
//                        cursor.moveToFirst();
//                        path = cursor.getString(cursor.getColumnIndex("_data"));
//                        size = cursor.getLong(cursor.getColumnIndex("_size"));
//                        checkVideoSize(path, size);
//                    }
//                    cursor.close();
//                } else if (uri.getScheme().equals("file")) {
//                    File file = new File(uri.getPath());
//                    if (file.exists()) {
//                        path = uri.getPath();
//                        size = file.length();
//                        checkVideoSize(path, size);
//                    }
//                }
//            }
//        }
//
//        super.onActivityResult(requestCode, resultCode, data);
//    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == VideoSelectActivity.VIDEO_SELECT_SUCCESS) {
            if (data != null) {
                LogUtils.e("aab getPath = " + data.getStringExtra(VideoSelectActivity.APP_SELECT_VIDEO_PATH));
                LogUtils.e("aab getSize = " + data.getLongExtra(VideoSelectActivity.APP_SELECT_VIDEO_SIZE, 0));
                checkVideoSize(data.getStringExtra(VideoSelectActivity.APP_SELECT_VIDEO_PATH), data.getLongExtra(VideoSelectActivity.APP_SELECT_VIDEO_SIZE, 0));
            }
        } else if (requestCode == SELECT_LOCAL_VIDEO && resultCode == RESULT_OK && data != null) {
            // 系统相册获取的视频
            CommonUtils.copyUriToPath(this, data.getData(), CommonArgs.CACHE_GIFT_VIDEO_PATH);
            checkVideoSize(CommonArgs.CACHE_GIFT_VIDEO_PATH, 0);
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 检测视频是否合法
     *
     * @param path
     * @param size
     */
    private void checkVideoSize(String path, long size) {
        if (size > RecorderConfig.DEFAULT_MAX_VIDEO_FILE_SIZE_LIMIT) {
            Toast.makeText(this, getString(R.string.error_video_size, RecorderConfig.DEFAULT_MAX_VIDEO_FILE_SIZE_LIMIT / 1024 / 1024), Toast.LENGTH_LONG).show();
            return;
        }

        Bitmap videoThumbnail = ThumbnailUtils.createVideoThumbnail(path, MediaStore.Video.Thumbnails.MICRO_KIND);
        FileUtils.saveBitmapFromSDCard(videoThumbnail, MemoryVedioActivity.MAKE_CARD_RE_VIDEO_PREVIEW_NAME);

        PreferencesUtils.put(this, PREF_LOCAL_VIDEO_PATH, path);

        Intent intent = new Intent(this, MemoryVedioActivity2.class);
        intent.putExtra(S.M.M_CONTENT, content);
        intent.putExtra(IS_LOCAL_VIDEO, true);
        startActivity(intent);
        this.finish();
    }


    @SuppressLint("ClickableViewAccessibility")
    private void init() {
        // 初始化录像机
        mRecorder = new CameraXRecorder(this);
        mRecorder.recorderAudio(true);
        mRecorder.setSuggestOutputSize(OUTPUT_WIDTH, OUTPUT_HEIGHT);
        mRecorder.setCameraPreviewView(video_preview);
        mRecorder.setStateChangeListener(this);
        mRecorder.setFileSizeLimit(50 * 1024 * 1024);
        mRecorder.init(this);

        mReVideoIv.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    //不显示拿不到区域
                    mReVideoIv.setBackgroundResource(R.drawable.memory_vedioi_reing);
                    if (mCancelActionRectLl.getVisibility() == View.GONE) {
                        mCancelActionRectLl.setAlpha(0);
                        mCancelActionRectLl.setVisibility(View.VISIBLE);
                    }
                    reSec = System.currentTimeMillis();
                    mGuideTextTv.setText(R.string.up_drag_cancel_video);
                    secs.setText("30s");
                    runnable.secs = 30;
                    mHandler.postDelayed(runnable, 1000);
                    secs.setVisibility(View.VISIBLE);
                    reVideoStart = Observable.timer(300, TimeUnit.MILLISECONDS, Schedulers.newThread())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Action1<Long>() {
                                @Override
                                public void call(Long aLong) {
                                    if (!reVideoing) {
                                        startRecord();
                                        startPrgAnim();
                                    }
                                    reVideoing = true;
                                }
                            });
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (cancelTop == 0 || cancelBottom == 0) {
                        mCancelActionRectLl.getLocationOnScreen(cancelPostion);
                        cancelTop = cancelPostion[1];
                        cancelBottom = cancelTop + mCancelActionRectLl.getHeight();
                    }
                    //在区域内则取消
                    if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom) {
                        if (mCancelActionRectLl.getAlpha() == 0) {
                            mCancelActionRectLl.setAlpha(1);
                        }
                    } else {
                        if (mCancelActionRectLl.getAlpha() == 1) {
                            mCancelActionRectLl.setAlpha(0);
                        }
                    }
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    mProgressRightIv.clearAnimation();
                    mProgressLeftIv.clearAnimation();
                    if (!reVideoing) {
                        reVideoStart.unsubscribe();
                    }
                    mReVideoIv.setImageResource(R.drawable.memory_vedio_re);
                    reSec = System.currentTimeMillis() - reSec;
                    secs.setVisibility(View.INVISIBLE);
                    if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom) {
                    } else if (reSec < 1300) {
                        ToastUtils.showShort(MemoryVedioActivity.this, getString(R.string.audio_re_time_low_toast));
                    } else {
                        if (reVideoing) {
                            mReVideoIv.postDelayed(this::stopRecord, 1000);
                        }
                        break;
                    }

                    if (reVideoing) {
                        stopRecord();
                    }

                    mCancelActionRectLl.setVisibility(View.GONE);
                    mGuideTextTv.setText(R.string.medio_guide_text);
                    break;
            }
            return true;
        });
    }

    private void startPrgAnim() {
        TranslateAnimation leftTa = new TranslateAnimation(0, -mProgressLeftIv.getWidth(), 0, 0);
        leftTa.setDuration(30000);
        mProgressLeftIv.startAnimation(leftTa);
        TranslateAnimation rightTa = new TranslateAnimation(0, mProgressLeftIv.getWidth(), 0, 0);
        rightTa.setDuration(30000);
        mProgressRightIv.startAnimation(rightTa);
        rightTa.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (reVideoing) {
                    try {
                        stopRecord();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.memory_vedio_back);
        setTopRightIcon(R.drawable.memory_vedio_camera);
        setTopRightOnClick(this);

        super.initTopBar();
    }


    /**
     * 开始录制
     */
    private void startRecord() {
        if (mRecorder.isRecording()) {
            Toast.makeText(this, R.string.make_card_reing_video, Toast.LENGTH_SHORT).show();
            return;
        }

        // initialize video camera
        // 录制视频
        mRecorder.startRecording(MAKE_CARD_RE_VIDEO_PATH);
    }

    @Override
    public void onStateChange(int state) {
        if (state == CameraXRecorder.RECORDER_STATE_SUCCESS) {
            checkVideoSize(MAKE_CARD_RE_VIDEO_PATH, new File(MAKE_CARD_RE_VIDEO_PATH).length());
        }
    }

    private class SecsRunnable implements Runnable {
        public int secs = 30;

        public SecsRunnable() {
        }

        @Override
        public void run() {
            MemoryVedioActivity.this.secs.setText(secs + "s");
            this.secs--;
            mHandler.postDelayed(this, 1000);
        }
    }

    /**
     * 停止录制
     */
    private void stopRecord() {
        mRecorder.stopRecording();
        reVideoing = false;
        LogUtils.i("stop");
    }


    public void toCommonVideoRecorderActivity(Context context) {
        RecorderConfig config = new RecorderConfig("MEMORY");
        config.setSuggestWidth(1280);
        config.setSuggestHeight(720);
        config.setTarget((videoPath, coverPath) -> {
            PreferencesUtils.put(context, PREF_LOCAL_VIDEO_PATH, videoPath);

            Intent intent = new Intent(context, MemoryVedioActivity2.class);
            intent.putExtra(IS_LOCAL_VIDEO, true);
            context.startActivity(intent);
        });
        config.goRecorder(context);
    }
}