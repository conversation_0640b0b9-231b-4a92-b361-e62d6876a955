//package com.totwoo.totwoo.activity;
//
//import android.annotation.SuppressLint;
//import android.bluetooth.BluetoothDevice;
//import android.content.Intent;
//import android.os.Build;
//import android.os.Bundle;
//import android.text.TextUtils;
//import android.view.KeyEvent;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//
//import com.airbnb.lottie.LottieAnimationView;
//import com.airbnb.lottie.LottieComposition;
//import com.airbnb.lottie.OnCompositionLoadedListener;
//import com.totwoo.library.net.FileDownloadCallback;
//import com.totwoo.library.net.HttpRequest;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.bean.JewUpdate;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.ble.BleParams;
//import com.totwoo.totwoo.ble.BluetoothManage;
//import com.totwoo.totwoo.ble.BluetoothWrapper;
//import com.totwoo.totwoo.service.DfuService;
//import com.totwoo.totwoo.utils.CommonArgs;
//import com.totwoo.totwoo.utils.CommonUtils;
//import com.totwoo.totwoo.utils.FileUtils;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.PermissionUtil;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.utils.TrackEvent;
//import com.totwoo.totwoo.widget.CustomDialog;
//import com.umeng.analytics.MobclickAgent;
//
//import java.io.File;
//import java.util.Locale;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import no.nordicsemi.android.dfu.DfuProgressListener;
//import no.nordicsemi.android.dfu.DfuProgressListenerAdapter;
//import no.nordicsemi.android.dfu.DfuServiceInitiator;
//import no.nordicsemi.android.dfu.DfuServiceListenerHelper;
//import rx.Observer;
//import rx.android.schedulers.AndroidSchedulers;
//import rx.schedulers.Schedulers;
//
//public class JewelryNoConnectOTAActivity extends BaseActivity implements BluetoothWrapper.BluetoothOTAListener {
//
//    @BindView(R.id.ota_detail_tv)
//    TextView ota_detail_tv;
//    @BindView(R.id.ota_info_tv)
//    TextView ota_info_tv;
//    @BindView(R.id.ota_main_iv)
//    ImageView ota_main_iv;
//    @BindView(R.id.animation_view)
//    LottieAnimationView animation_view;
//    @BindView(R.id.ota_progress_tv)
//    TextView ota_progress_tv;
//
//    private String path;
//    private String updateString;
//    private boolean otaOK;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_jewelry_ota_not_connect);
//        ButterKnife.bind(this);
//        getUpdatePackage();
//    }
//
//    private void getUpdatePackage() {
//        if (!PermissionUtil.hasStoragePermission(this)) {
//            return;
//        }
//        if (!PermissionUtil.hasLocationPermission(this)) {
//            return;
//        }
//
//        if (!PermissionUtil.hasBluetoothPermission(this)) {
//            return;
//        }
//        String jew = BluetoothManage.getInstance().getOTAName();
//        if (TextUtils.isEmpty(jew)) {
//            return;
//        }
//
//        String device_info = PreferencesUtils.getString(JewelryNoConnectOTAActivity.this, BleParams.TOTWOO_DEVICE_INFO, "");
//        HttpHelper.update.checkJewUpdate(jew, "1.0.0", Apputils.getVersionName(JewelryNoConnectOTAActivity.this), device_info, "android")
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(new Observer<HttpBaseBean<JewUpdate>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        e.printStackTrace();
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<JewUpdate> httpBaseBean) {
//                        if (httpBaseBean.getErrorCode() == 0) {
//                            JewUpdate ju = httpBaseBean.getData();
//                            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_PREPARE);
//                            downloadOtaFile(ju);
//                            Locale locale = getResources().getConfiguration().locale;
//                            String language = locale.getLanguage();
//                            if (language.endsWith("zh"))
//                                updateString = ju.getCn_text();
//                            else
//                                updateString = ju.getEn_text();
//                        }
//                    }
//                });
//    }
//
//    /**
//     * 下载最新固件
//     *
//     * @param jewUpdate
//     */
//    public void downloadOtaFile(JewUpdate jewUpdate) {
//        if (jewUpdate == null || TextUtils.isEmpty(jewUpdate.getUrl())) {
//            return;
//        }
//        final String target = FileUtils.getDownloadDir() + File.separator
//                + ("totwoo_" + jewUpdate.getFirmware()).hashCode() + ".zip";
//        HttpRequest.download(jewUpdate.getUrl(), new File(target),
//                new FileDownloadCallback() {
//                    @Override
//                    public void onDone() {
//                        path = target;
//                        startScan();
//                        ota_info_tv.setText(R.string.bluetooth_searching);
//                    }
//
//                    @Override
//                    public void onFailure() {
//                        LogUtils.e("download ota file error! ");
//                    }
//                });
//    }
//
//    private boolean checkVersion(String name) {
//        return BleParams.isOtaNameMatch(name);
//    }
//
//    /**
//     * 开始 OTA 升级
//     */
//    private void startOTA(BluetoothDevice mSelectedDevice) {
//        DfuServiceListenerHelper.registerProgressListener(this,
//                mDfuProgressListener);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            DfuServiceInitiator.createDfuNotificationChannel(JewelryNoConnectOTAActivity.this);
//        }
//        DfuServiceInitiator starter = new DfuServiceInitiator(
//                mSelectedDevice.getAddress())
//                .setKeepBond(true)
//                .setForceDfu(true)
//                .setPacketsReceiptNotificationsEnabled(true)
//                .setUnsafeExperimentalButtonlessServiceInSecureDfuEnabled(true)
//                .setForeground(false);
//
//        starter.setDisableNotification(true);
//
//        starter.setZip(CommonUtils.getUriForFile(JewelryNoConnectOTAActivity.this, new File(path)));
//
//        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.O) {
//            //	starter.setPacketsReceiptNotificationsValue(DfuServiceInitiator.DEFAULT_PRN_VALUE);
//            starter.setPacketsReceiptNotificationsValue(10);
//        } else {
//            starter.setPacketsReceiptNotificationsValue(4);
//        }
//
//        starter.start(JewelryNoConnectOTAActivity.this, DfuService.class);
////        BluetoothManage.getInstance().setOTAScanned(false);
////        Intent otaIntent = new Intent(JewelryNoConnectOTAActivity.this, JewelryOTAActivity.class);
////        otaIntent.putExtra(JewelryOTAActivity.BLE_OTA_FILE_PATH_TAG, path);
////        otaIntent.putExtra(JewelryOTAActivity.EXTRA_DFU_ADDRESS, mSelectedDevice.getAddress());
////        startActivity(otaIntent);
//    }
//
//    /**
//     * 设置蓝牙搜索状态， 开始搜索自动设置倒计时
//     */
//    public void startScan() {
//        LogUtils.e("aab startScan");
//        BluetoothManage.getInstance().setOTA(true);
//        BluetoothManage.getInstance().setOTAScanListener(JewelryNoConnectOTAActivity.this);
//        BluetoothManage.getInstance().scanOTA();
//    }
//
//    private boolean isLottieChanged;
//
//    private final DfuProgressListener mDfuProgressListener = new DfuProgressListenerAdapter() {
//        @Override
//        public void onDeviceConnecting(final String deviceAddress) {
//        }
//
//        @Override
//        public void onDfuProcessStarting(final String deviceAddress) {
//        }
//
//        @Override
//        public void onEnablingDfuMode(final String deviceAddress) {
//        }
//
//        @Override
//        public void onFirmwareValidating(final String deviceAddress) {
//        }
//
//        @Override
//        public void onDeviceDisconnecting(final String deviceAddress) {
//            //TODO 退出升级模式的文案
//            if (!isLottieChanged) {
//                PreferencesUtils.put(JewelryNoConnectOTAActivity.this, CommonArgs.JEWELRY_OTA_STOP,true);
//                showFailDialog(getString(R.string.ota_failed));
//            }
//        }
//
//        @Override
//        public void onDfuCompleted(final String deviceAddress) {
//            // let's wait a bit until we cancel the notification. When canceled
//            // immediately it will be recreated by service again.
//            ota_progress_tv.setText(100 + "%");
//            // 固件升级完成
//            otaOK = true;
//            BluetoothManage.getInstance().setOTA(false);
//            finish();
//        }
//
//        @Override
//        public void onDfuAborted(final String deviceAddress) {
//            // let's wait a bit until we cancel the notification. When canceled
//            // immediately it will be recreated by service again.
//            otaError();
//        }
//
//        @Override
//        public void onProgressChanged(final String deviceAddress,
//                                      final int percent, final float speed, final float avgSpeed,
//                                      final int currentPart, final int partsTotal) {
//            if (!isLottieChanged) {
//                LottieComposition.Factory.fromAssetFileName(JewelryNoConnectOTAActivity.this, "updating_bluetooth.json", new OnCompositionLoadedListener() {
//
//                    @Override
//                    public void onCompositionLoaded(LottieComposition composition) {
//                        animation_view.setComposition(composition);
//                        animation_view.setProgress(0.0f);
//                        animation_view.playAnimation();
//                    }
//                });
//                ota_progress_tv.setVisibility(View.VISIBLE);
//                isLottieChanged = true;
//            }
//            ota_progress_tv.setText(percent + "%");
//
//            LogUtils.e("aab OTA Progress: " + percent + "% speed:" + speed
//                    + "B/S avgSpeed:" + avgSpeed + "B/S  currentPart:"
//                    + currentPart + "  partsTotal:" + partsTotal);
//        }
//
//        @Override
//        public void onError(final String deviceAddress, final int error,
//                            final int errorType, final String message) {
//            // We have to wait a bit before canceling notification. This
//            // iscalled before DfuService creates the last notification.
//            LogUtils.e("aab error:" + error + "  errorType:" + errorType
//                    + " message:" + message);
//            otaError();
//        }
//    };
//
//    private boolean isShowFailDialog;
//
//    private void otaError() {
//        if (!isShowFailDialog) {
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_FAIL);
//            showFailDialog(getString(R.string.ota_failed_i_see));
//            isShowFailDialog = true;
//            animation_view.setVisibility(View.GONE);
//            ota_progress_tv.setVisibility(View.GONE);
//            ota_main_iv.setVisibility(View.VISIBLE);
//            ota_main_iv.setImageResource(R.drawable.ota_update_fail);
//        }
//    }
//
//    private void showFailDialog(String failMessage) {
//        final CustomDialog customDialog = new CustomDialog(JewelryNoConnectOTAActivity.this);
//        customDialog.setMessage(failMessage);
//        customDialog.setTitle(R.string.tips);
//        customDialog.setCanceledOnTouchOutside(false);
//        customDialog.setPositiveButton(R.string.tim_i_see, v -> {
//            customDialog.dismiss();
//            finish();
//        });
//        customDialog.show();
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
//    }
//
//    @SuppressLint("MissingPermission")
//    @Override
//    public void onOTADeviceScanned(BluetoothDevice device) {
//        BluetoothManage.getInstance().getBondedDevices();
//        BluetoothManage.getInstance().unBonded(device);
//
//        if (checkVersion(device.getName())) {
//            mHandler.postDelayed(() -> startOTA(device), 1000);
//            ota_info_tv.setBackground(null);
//            ota_info_tv.setText(R.string.ota_title_info);
//            ota_info_tv.setTextColor(getResources().getColor(R.color.text_color_black_hint));
//            ota_info_tv.setVisibility(View.VISIBLE);
//            if (!TextUtils.isEmpty(updateString)) {
//                ota_detail_tv.setText(updateString);
//            } else {
//                ota_detail_tv.setText(R.string.ota_title_info0);
//            }
//        } else {
//            showFailDialog(getString(R.string.ota_failed_time_out));
//        }
//    }
//
//    @Override
//    public void onOTATimeOut() {
//        showFailDialog(getString(R.string.ota_failed_time_out));
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//        if (isFinishing()) {
//            if (otaOK) {
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_SUCCESS);
//                ToastUtils.showLong(this, R.string.ota_success);
//            }
//            BluetoothManage.getInstance().setOTAScanned(false);
//            stopService(new Intent(JewelryNoConnectOTAActivity.this, DfuService.class));
//            DfuServiceListenerHelper.unregisterProgressListener(this,
//                    mDfuProgressListener);
//            BluetoothManage.getInstance().setOTA(false);
//        }
//    }
//
//    /**
//     * 屏蔽返回键
//     */
//    public boolean onKeyDown(int keyCode, KeyEvent event) {
//        if (keyCode == KeyEvent.KEYCODE_BACK) {
//            return true;
//        }
//        return super.onKeyDown(keyCode, event);
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        BluetoothManage.getInstance().setOTA(false);
//    }
//}
