package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;
import android.widget.Toast;

import com.etone.framework.event.EventBus;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.LoginInfoBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.ApiException;
import com.totwoo.totwoo.utils.CaptchaController;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.codeInput.VerificationCodeInput;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class ForgetPasswordCodeActivity extends BaseActivity {
    public static final String PHONENUMBER = "phoneNumber";
    public static final String COUNTRY_CODE_VALUE = "country_code_value";
    public static final String COUNTRY_TEMP_CODE = "country_temp_code";
    @BindView(R.id.forget_password_number_tv)
    TextView forget_password_number_tv;
    @BindView(R.id.forget_password_count_down_tv)
    TextView forget_password_count_down_tv;
    @BindView(R.id.verificationCodeInput)
    VerificationCodeInput verificationCodeInput;

    @BindView(R.id.forget_get_voice_tv)
    TextView forget_get_voice_tv;

    private boolean isMsgHaving;
    private boolean isVoiceHaving;
    private String phoneNumber;
    private boolean nextClickable = false;
    /**
     * 标记验证码可重复获取的倒计时器，界面销毁是要停止计时
     */
    private CountDownTimer cuntTimer;
    private String country_code_value;
    private String vercode;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_forget_password_code);

        ButterKnife.bind(this);

        phoneNumber = getIntent().getStringExtra(PHONENUMBER);
        country_code_value = getIntent().getStringExtra(COUNTRY_CODE_VALUE);
        String showNumber = "+" + country_code_value + " " + phoneNumber;
        forget_password_number_tv.setText(getString(R.string.code_has_been_sent) + showNumber);
        startCountdown();

        verificationCodeInput.setOnCompleteListener(content -> {
            vercode = content;
            verify();
        });

        // 如果有临时验证码, 直接填充
        String tempCode = getIntent().getStringExtra(COUNTRY_TEMP_CODE);
        if (tempCode != null) {
            verificationCodeInput.fillInCode(tempCode);
        } else {
            mHandler.postDelayed(() -> {
                InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

                inputMethodManager.toggleSoftInput(0,
                        InputMethodManager.HIDE_NOT_ALWAYS);
                verificationCodeInput.getFirstRequest();
            }, 100);
        }
    }

    @OnClick({R.id.forget_password_count_down_tv, R.id.forget_get_voice_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.forget_password_count_down_tv:
                getMsgCode();
                break;

            case R.id.forget_get_voice_tv:
                getVoiceCode();
                break;
        }
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
        setTopbarBackground(R.color.transparent);
//        if (TextUtils.equals(country_code_value, "86")) {
//            setTopRightString(R.string.voice_code);
//            setTopRightOnClick(v -> getVoiceCode());
//        }
    }

    /**
     * 开始获取验证码的倒计时显示， 及时期间禁止点击
     */
    protected void startCountdown() {
        isMsgHaving = true;
        cuntTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                forget_password_count_down_tv.setText(getString(R.string.the_second_can_repeat_long, (int) (millisUntilFinished / 1000)));
            }

            @Override
            public void onFinish() {
                forget_password_count_down_tv.setText(getString(R.string.get_code_again));
                isMsgHaving = false;
            }
        };
        cuntTimer.start();
    }

    /**
     * 开始获取验证码的倒计时显示， 及时期间禁止点击
     */
    protected void startCountdownVoice() {
        isVoiceHaving = true;
        cuntTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                forget_get_voice_tv.setText((millisUntilFinished / 1000)+"s");
            }

            @Override
            public void onFinish() {
                forget_get_voice_tv.setText(getString(R.string.voice_code));
                isVoiceHaving = false;
            }
        };
        cuntTimer.start();
    }

    private void getMsgCode() {
        if (isVoiceHaving) {
            ToastUtils.show(ForgetPasswordCodeActivity.this, R.string.voice_is_having, Toast.LENGTH_SHORT);
            return;
        }
        if (isMsgHaving) {
            ToastUtils.showLong(ForgetPasswordCodeActivity.this, R.string.verification_too_ofen);
            return;
        }
        String phone = phoneNumber;


        CommonUtils.phoneSmsPre0CheckAndDo(this, country_code_value, phone, () -> {
            // 发送请求获取验证码
            isMsgHaving = true;
            CaptchaController.getInstance().checkAndShowCaptcha(this, "pwd",country_code_value + phone, country_code_value, (ret, ticket, randomStr) -> {
                if (ret == 0) {
                    requestSms(country_code_value + phone, ticket, randomStr);
                } else {
                    isMsgHaving = false;
                    if (ret != -10001){
                        ToastUtils.showLong(this, R.string.verification_failed);
                    }
                }
            });
        });
    }

    private void requestSms(String phone, String ticket, String randomStr) {
        int time = (int) (System.currentTimeMillis() / 1000);
        String sourceStr = time + "+" + phone + "+" + "totwoo_safe_202311";

        String firmwareType = null;
        if (ticket != null && ticket.startsWith(BleParams.COMMON_JEWELEY_PRE)) {
            firmwareType = ticket;
            ticket = "";
        }

        HttpHelper.loginV3Service.getSmsT(phone, time, "pwd", CommonUtils.md5(sourceStr), firmwareType, ticket, randomStr)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showLong(ForgetPasswordCodeActivity.this, R.string.error_net);
                        isMsgHaving = false;
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            startCountdown();

                            ToastUtils.showLong(ForgetPasswordCodeActivity.this,
                                    R.string.vercode_has_send);
                        } else if (stringHttpBaseBean.getErrorCode() == 800) {
                            // 临时验证码
                            String msg = stringHttpBaseBean.getErrorMsg();
                            ToastUtils.showLong(ForgetPasswordCodeActivity.this, msg);

                            String code = CommonUtils.extractVerCode(msg);

                            if (!TextUtils.isEmpty(code)) {
                                verificationCodeInput.fillInCode(code);
                            }
                        } else if (stringHttpBaseBean.getErrorCode() == 108) {
                            ToastUtils.showLong(ForgetPasswordCodeActivity.this, R.string.error_incorrect_vercode);
                            isMsgHaving = false;
                        } else {
                            String errMsg = ApiException.getHttpErrMessage(stringHttpBaseBean.getErrorCode(), stringHttpBaseBean.getErrorMsg());
                            ToastUtils.showLong(ForgetPasswordCodeActivity.this, errMsg);
                            isMsgHaving = false;
                        }
                    }
                });
    }

    private void getVoiceCode() {
        if (isVoiceHaving) {
            ToastUtils.show(ForgetPasswordCodeActivity.this, R.string.verification_too_ofen, Toast.LENGTH_SHORT);
            return;
        }
        if (isMsgHaving) {
            ToastUtils.show(ForgetPasswordCodeActivity.this, R.string.msg_is_having, Toast.LENGTH_SHORT);
            return;
        }
        final String phone = phoneNumber;

        CommonUtils.phoneSmsPre0CheckAndDo(this, country_code_value, phone, () -> {
            CaptchaController.getInstance().checkAndShowCaptcha(this, "pwd",country_code_value + phone, country_code_value, (ret, ticket, randomStr) -> {
                if (ret == 0) {
                    requestVoiceSms(country_code_value + phone, ticket, randomStr);
                } else {
                    if (ret != -10001){
                        ToastUtils.showLong(this, R.string.verification_failed);
                    }
                }
            });
        });
    }

    private void requestVoiceSms(String phone, String ticket, String randomStr) {
        int time = (int) (System.currentTimeMillis() / 1000);
        showProgressDialog();
        HttpHelper.loginV3Service.getVoiceSmsT(phone, time, HttpHelper.genNewSign(time, phone), ticket, randomStr)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(ForgetPasswordCodeActivity.this, R.string.error_net);
                        dismissProgressDialog();
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        dismissProgressDialog();
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            ToastUtils.showLong(ForgetPasswordCodeActivity.this, R.string.vercode_voice_send);
                            startCountdownVoice();
                        } else {
                            if (stringHttpBaseBean.getErrorMsg() == null || stringHttpBaseBean.getErrorMsg().isEmpty())
                                ToastUtils.show(ForgetPasswordCodeActivity.this, getString(R.string.error_net), Toast.LENGTH_SHORT);
                            else
                                ToastUtils.show(ForgetPasswordCodeActivity.this, stringHttpBaseBean.getErrorMsg(), Toast.LENGTH_SHORT);
                        }
                    }
                });
    }

    /**
     * 验证验证码是否有效
     *
     * @param vercode
     * @return
     */
    private boolean isVerCodeValid(String vercode) {
        Pattern p = Pattern.compile("^\\d{4}$");
        Matcher m = p.matcher(vercode);
        return m.matches();
    }

    private void verify() {
        if (TextUtils.isEmpty(vercode)) {
            ToastUtils.showShort(this, R.string.error_invalid_vercode);
            return;
        } else if (!isVerCodeValid(vercode)) {
            ToastUtils.showShort(this, R.string.error_incorrect_vercode);
            return;
        }
        int time = (int) (System.currentTimeMillis() / 1000);
        HttpHelper.loginV3Service.versionSms(country_code_value + phoneNumber, time, vercode, HttpHelper.genNewSign(time, country_code_value + phoneNumber))
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<LoginInfoBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(ForgetPasswordCodeActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<LoginInfoBean> loginInfoBeanHttpBaseBean) {
                        if (loginInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            ToTwooApplication.owner.setPhone(country_code_value + phoneNumber);
                            startActivity(new Intent(ForgetPasswordCodeActivity.this, SetPasswordActivity.class));
                            EventBus.onPostReceived(S.E.E_LOGIN_VERIFY_SUCCESS, null);
                            finish();
                        } else {
                            String errMsg = ApiException.getHttpErrMessage(loginInfoBeanHttpBaseBean.getErrorCode(), loginInfoBeanHttpBaseBean.getErrorMsg());
                            ToastUtils.showShort(ForgetPasswordCodeActivity.this, errMsg);
                        }
                    }
                });
    }
}
