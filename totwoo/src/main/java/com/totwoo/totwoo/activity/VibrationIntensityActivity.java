package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.PreferencesUtils;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 默认首饰振动强度设置页
 * <p>
 * Created by lixingmao on 2017/5/11.
 */

public class VibrationIntensityActivity extends BaseActivity
{
    @BindView(R.id.vibration_setting_progress_bar)
    SeekBar mVibrationSettingProgressBar;

    @BindView (R.id.activity_vibration_intensity_content)
    TextView tv1;

    private int tempProgress;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_vibration_intensity);
        ButterKnife.bind(this);

        tempProgress = PreferencesUtils.getInt(this, BleParams.SELECT_VIBRATION_INDEX_TAG, 90);

        if (tempProgress > 0)
        {
            mVibrationSettingProgressBar.setProgress(tempProgress);
        }

        if (!Apputils.systemLanguageIsChinese(this))
            tv1.setVisibility(View.GONE);

        mVibrationSettingProgressBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener()
        {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                tempProgress = mVibrationSettingProgressBar.getProgress();
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    Toast.makeText(ToTwooApplication.baseContext, R.string.error_jewelry_connect, Toast.LENGTH_SHORT).show();
                    return;
                }
                BluetoothManage.getInstance().setVibrationIntensity(tempProgress);
                PreferencesUtils.put(VibrationIntensityActivity.this, BleParams.SELECT_VIBRATION_INDEX_TAG, tempProgress);
            }
        });
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.set_jewelry_vibrantion);
    }

}
