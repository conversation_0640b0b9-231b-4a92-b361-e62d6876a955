package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ble.BleParams;

/**
 * Created by lixingmao on 2017/1/6.
 */

public class ConnectHelpActivity extends BaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_connect_help);

        TextView tipsTv = findViewById(R.id.help_tips_tv);
        if (BleParams.isButtonBatteryJewelry()) {
            tipsTv.setVisibility(View.GONE);
        } else if (BleParams.isSecurityJewlery()) {
            tipsTv.setText(R.string.safe_connect_help_tips);
        } else {
            tipsTv.setText(R.string.connect_help_tips);
        }

        setTopBackIcon(R.drawable.back_icon_black);

        if (!Apputils.systemLanguageIsChinese(this)) {
            findViewById(R.id.kefu_info_tv).setVisibility(View.GONE);
        }

        findViewById(R.id.kefu_info_tv).setOnClickListener(v -> startActivity(new Intent(Intent.ACTION_DIAL).setData(Uri.parse(getString(R.string.customer_service_phone_number)))));

    }
}
