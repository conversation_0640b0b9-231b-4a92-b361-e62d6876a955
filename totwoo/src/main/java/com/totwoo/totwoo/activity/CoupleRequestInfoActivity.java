package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Toast;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.ContactsBean;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.databinding.ActivityCoupleRequestInfoBinding;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * 新增独立的选择配对页面
 */
public class CoupleRequestInfoActivity extends BaseActivity {
    public static final String EXTRA_COUPLE_REQUESTED_BEAN = "couple_requested_bean";

    private ContactsBean requestedBean;

    private ActivityCoupleRequestInfoBinding binding;

    private CoupleLogic coupleLogic;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCoupleRequestInfoBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        EventBus.getDefault().register(this);

        requestedBean = getIntent().getParcelableExtra(EXTRA_COUPLE_REQUESTED_BEAN);

        if (requestedBean == null) {
            Toast.makeText(this, R.string.data_error, Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        binding.infoPhoneDetailTv.setText(getString(R.string.couple_request_info_detail, requestedBean.getPhoneNumber()));

        coupleLogic = new CoupleLogic(this);

        binding.infoCancelRequestTv.setOnClickListener(v -> {
            // 取消配对请求
            coupleLogic.cancelRequest(requestedBean, success -> {

                if (success) {
                    startActivity(new Intent(this, SelectCoupleActivity.class));
                    finish();
                }
            });
        });

        binding.infoRequestAgainTv.setOnClickListener(v -> {
//            startActivity(new Intent(this, SelectCoupleActivity.class));
            finish();
        });
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.select_paired);
        setTopbarBackground(R.color.transparent);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void replyRequest(TotwooMessage message) {
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
