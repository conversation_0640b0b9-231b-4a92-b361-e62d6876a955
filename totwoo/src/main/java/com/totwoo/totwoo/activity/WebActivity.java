package com.totwoo.totwoo.activity;

import android.app.Activity;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ArrayAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;

import com.bumptech.glide.Glide;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.utils.DESCoder;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.ShareUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.CustomWebView;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLEncoder;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 全局WebActivity类，统一风格， 展示所有的Web页面
 *
 * <AUTHOR>
 * @date 2015-2015年7月10日
 */
public class WebActivity extends BaseActivity implements OnClickListener {

    public static final String WEB_URL_TAG = "web_url_tag";

    // 对于需要返回数据的返回码
    public static final int WEB_RESULT_CODE = 1000;

    /**
     * 核心WebView
     */
    private CustomWebView mWebView;

    @BindView(R.id.web_loadding_img)
    ImageView loaddingView;

    @BindView(R.id.web_menu_layout)
    RelativeLayout menuLayout;

    @BindView(R.id.web_menu_list)
    ListView menuListView;

    /**
     * 要连接的 URL
     */
    private String mUrl;

    /**
     * 当前网页标题
     */
    private String mWebTitle;

    private ShareUtils shareUtils;

    /**
     * 分享对话框
     */
    private CustomDialog shareDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mUrl = getIntent().getStringExtra(WEB_URL_TAG);

        if (TextUtils.isEmpty(mUrl)) {
            ToastUtils.showLong(this, R.string.incorrect_url);
            finish();
            return;
        }

        try {
            setContentView(R.layout.activity_web);
            ButterKnife.bind(this);
            initWebView();
        } catch (Exception e) {
            // WebView 加载异常, 直接跳转外部浏览器
            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(mUrl)));

            finish();
            return;
        }

        if (TextUtils.isEmpty(mUrl)) {
            ToastUtils.showLong(this, R.string.incorrect_url);
            finish();
        }

        mWebView.loadUrl(mUrl);

        Glide.with(this).load(R.drawable.loading).into(loaddingView);
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.close_icon);
//        setTopRightIcon(R.drawable.top_bar_more_icon);
//        setTopRightOnClick(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                showMenu(v);
//            }
//        });
    }

    /**
     * 展示菜单
     */
    protected void showMenu(View v) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            menuLayout.setPadding(0, Apputils.getStatusHeight(this), 0, 0);
        }

        menuLayout.setVisibility(View.VISIBLE);
        menuLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                v.setVisibility(View.GONE);
            }
        });

        menuListView.setAdapter(new ArrayAdapter<String>(this,
                R.layout.web_menu_item, getResources().getStringArray(
                R.array.web_menu_items)));

        menuListView.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view,
                                    int position, long id) {
                switch (position) {
                    case 0:
                        ClipboardManager cmb = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                        cmb.setText(mWebView.getUrl());
                        ToastUtils.showLong(WebActivity.this,
                                R.string.link_has_copyed);
                        break;
                    case 1:
                        showShareDialog(mUrl);
                        break;
                    case 2:
                        if (mWebView != null) {
                            mWebView.reload();
                        }
                        break;
                }
                menuLayout.setVisibility(View.GONE);
            }
        });

        // PopupMenu popup = new PopupMenu(this, v);
        // popup.getMenuInflater().inflate(R.menu.menu_web_activity,
        // popup.getMenu());
        //
        // popup.setOnMenuItemClickListener(new
        // PopupMenu.OnMenuItemClickListener() {
        // public boolean onMenuItemClick(MenuItem item) {
        // switch (item.getItemId()) {
        // case R.id.menu_action_copy_link:
        // ClipboardManager cmb = (ClipboardManager)
        // getSystemService(Context.CLIPBOARD_SERVICE);
        // cmb.setText(mWebView.getUrl());
        // ToastUtils.showLong(WebActivity.this,
        // R.string.link_has_copyed);
        // break;
        // case R.id.menu_action_share:
        // showShareDialog(StringUtils.isEmpty(mWebTitle) ? "" : mUrl);
        // break;
        // case R.id.menu_action_refresh:
        // if (mWebView != null) {
        // mWebView.reload();
        // }
        // break;
        // }
        // return true;
        // }
        // });
        // popup.show();
    }

    /**
     * 展示分享的对话框
     *
     * @param url
     */
    private void showShareDialog(String url) {

        shareUtils = new ShareUtils(WebActivity.this);
        shareUtils.setShareUrl(url);
        shareUtils.setShareImgPath(FileUtils.saveBitmapFromSDCard(
                snapShareBitmap(), "totwoo_cache_img"));
        shareUtils.setShareTitle(mWebTitle);
        shareDialog = new CustomDialog(this);
        shareDialog.setTitle(R.string.share);
        View view = LayoutInflater.from(this).inflate(
                R.layout.share_black_layout, null);

        View facebook = view.findViewById(R.id.share_facebook);
        View twitter = view.findViewById(R.id.share_twitter);
        View wechatComment = view.findViewById(R.id.share_wechar_comment);
        View wechat = view.findViewById(R.id.share_wechar);
        View weibo = view.findViewById(R.id.share_weibo);
        View qq = view.findViewById(R.id.share_qq);
        View qzone = view.findViewById(R.id.share_qzone);

        if (!Apputils.systemLanguageIsChinese(this)) {
            facebook.setVisibility(View.VISIBLE);
            twitter.setVisibility(View.VISIBLE);
            wechatComment.setVisibility(View.VISIBLE);

            facebook.setOnClickListener(this);
            twitter.setOnClickListener(this);
            wechatComment.setOnClickListener(this);
        } else {
            wechatComment.setVisibility(View.VISIBLE);
            wechat.setVisibility(View.VISIBLE);
            weibo.setVisibility(View.VISIBLE);
            qq.setVisibility(View.VISIBLE);
            qzone.setVisibility(View.VISIBLE);

            wechatComment.setOnClickListener(this);
            wechat.setOnClickListener(this);
            weibo.setOnClickListener(this);
            qq.setOnClickListener(this);
            qzone.setOnClickListener(this);
        }

        shareDialog.setMainLayoutView(view);
        shareDialog.setNegativeButton(R.string.cancel);
        shareDialog.show();
    }

    /**
     * 获取当前屏幕截图，包含状态栏
     *
     * @return
     */
    public Bitmap snapShareBitmap() {
        mWebView.setDrawingCacheEnabled(true);
        mWebView.buildDrawingCache();
        Bitmap bmp = mWebView.getDrawingCache();

        return bmp;
    }

    @Override
    public void onClick(View v) {

        if (shareUtils == null) {
            return;
        }
        switch (v.getId()) {
            case R.id.share_wechar_comment:
                shareUtils.shareToWeCharComment(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_wechar:
                shareUtils.shareToWeChar(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_weibo:
                shareUtils.shareToSinaWeibo(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_qq:
                shareUtils.shareToQQ(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_qzone:
                shareUtils.shareToQzone(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_facebook:
                shareUtils.shareToFackBook(ShareUtils.SHARE_WEBPAGE, this);
                break;
            case R.id.share_twitter:
                shareUtils.shareToTwitter(ShareUtils.SHARE_WEBPAGE);
                break;
        }

        if (shareDialog != null && shareDialog.isShowing()) {
            shareDialog.dismiss();
        }
    }

    /**
     * 使用内链打开指定的Web页面
     *
     * @param context
     * @param url     指定的url
     */
    public static void showWeb(Context context, String url) {
        Intent intent = new Intent(context, WebActivity.class);
        intent.putExtra(WEB_URL_TAG, url);
        context.startActivity(intent);
    }

    /**
     * 使用内链打开指定的Web页面
     *
     * @param url 指定的url
     */
    public static void showWeb(Activity activity, String url, int resultCode) {
        Intent intent = new Intent(activity, WebActivity.class);
        intent.putExtra(WEB_URL_TAG, url);
        activity.startActivityForResult(intent, resultCode);
    }

    /**
     * 使用内链打开指定的Web页面
     *
     * @param url       指定的url
     * @param needToken
     */
    public static void showWeb(Activity activity, String url, boolean needToken) {
        // 组织领奖的URL

        if (needToken) {
            try {
                JSONObject json = new JSONObject();
                json.put("uid", ToTwooApplication.owner.getTotwooId());
                json.put("token", ToTwooApplication.owner.getToken());
                json.put("time", System.currentTimeMillis());

                String skip = null;
                // 如果包含界面跳转的后缀, 需要讲跳转符改到最后..
                if (url.contains("#")) {
                    skip = url.substring(url.lastIndexOf("#"));
                    url = url.substring(0, url.lastIndexOf("#"));
                }

                // 插入token 参数
                if (url.contains("?")) {
                    url += "&par="
                            + URLEncoder.encode(DESCoder.encrypt(
                            json.toString(), DESCoder.DES_KEY_STRING));
                } else {
                    url += "?par="
                            + URLEncoder.encode(DESCoder.encrypt(
                            json.toString(), DESCoder.DES_KEY_STRING));
                }

                if (skip != null) {
                    url += skip;
                }

            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        Intent intent = new Intent(activity, WebActivity.class);
        intent.putExtra(WEB_URL_TAG, url);
        activity.startActivity(intent);
    }

    /**
     * 设置 WebView 相关参数
     */
    private void initWebView() {

        mWebView = new CustomWebView(this);

//		mWebView.setBackgroundResource(R.color.layer_bg_white);
        mWebView.setScrollBarSize(0);
        ((FrameLayout) findViewById(R.id.web_view_layout)).addView(mWebView);

        // 加载需要显示的网页
        mWebView.setWebViewClient(new TwoWebViewClient());
        // 设置Web视图
//        mWebView.setWebChromeClient(new MyWebChromeClient());
        mWebView.setWebChromeClient(new WebChromeClient());

        // 设置WebView属性，能够执行Javascript脚本
        mWebView.getSettings().setJavaScriptEnabled(true);
        mWebView.getSettings().setPluginState(WebSettings.PluginState.ON);
        mWebView.getSettings().setDomStorageEnabled(true);
        // 无限缩放
        mWebView.getSettings().setUseWideViewPort(true);
        mWebView.getSettings().setAllowFileAccess(true);
        mWebView.getSettings().setSupportZoom(true);
        // 重要的一部
        mWebView.getSettings().setLoadWithOverviewMode(true);
        mWebView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE); // 不加载缓存内容

        mWebView.getSettings().setBuiltInZoomControls(true);
        mWebView.getSettings().setDisplayZoomControls(false);
        mWebView.setMixedContentAllowed(true);

        mWebView.addJavascriptInterface(this, "totwoo");

        // 5x 以下的系统可以通过此方法设置WebView透明, 5X 系统, 此方法会引起整个应用界面混乱
        // if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
        // mWebView.setBackgroundColor(0);
        // mWebView.getBackground().setAlpha(2);
        // }
    }

    /*****************
     * 开放给Web 调用的公开方法
     ********************/
    // 关闭当前浏览器
    @JavascriptInterface
    public void webClose() {
        finish();
    }

    // 调用客户端web浏览器通用的分享
    @JavascriptInterface
    public void webShare() {
        showShareDialog(TextUtils.isEmpty(mWebTitle) ? "" : mUrl);
    }

    // 无参数调用
    // contentWebView.loadUrl("javascript:javacalljs()");
    // 含参数调用
    // mWebView.loadUrl("javascript:javacalljswithargs('" + aa+ "')");
    // //aa是js的函数test()的参数

    /**********************************************************/

    public class MyWebChromeClient extends WebChromeClient {
        public void openFileChooser(ValueCallback<Uri> uploadFile) {
            openFileChooser(uploadFile, "");
        }

        public void openFileChooser(ValueCallback<Uri> uploadFile, String acceptType) {
            if (mUploadMessage != null)
                return;
            mUploadMessage = uploadFile;
            Intent i = new Intent();
            i.setAction(Intent.ACTION_GET_CONTENT);
            i.addCategory(Intent.CATEGORY_OPENABLE);
            // i.setType("image/*");
            i.setType("*/*");
            startActivityForResult(Intent.createChooser(i, "请选择图片"), 111);
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
            super.onReceivedTitle(view, title);
            mWebTitle = title;
        }

        public void openFileChooser(ValueCallback<Uri> uploadFile, String acceptType, String capture) {
            openFileChooser(uploadFile, acceptType);
        }

        public boolean onShowFileChooser(WebView webView,
                                         ValueCallback<Uri[]> filePathCallback,
                                         FileChooserParams fileChooserParams) {
            Intent i = new Intent();
            i.setAction(Intent.ACTION_GET_CONTENT);
            i.addCategory(Intent.CATEGORY_OPENABLE);
            // i.setType("image/*");
            i.setType("*/*");
            startActivityForResult(Intent.createChooser(i, "请选择图片"), 111);
            return false;
        }

        public abstract class FileChooserParams {
            /**
             * Open single file. Requires that the file exists before allowing
             * the user to pick it.
             */
            public static final int MODE_OPEN = 0;
            /**
             * Like Open but allows multiple files to be selected.
             */
            public static final int MODE_OPEN_MULTIPLE = 1;
            /**
             * Like Open but allows a folder to be selected. The implementation
             * should enumerate all files selected by this operation. This
             * feature is not supported at the moment.
             *
             * @hide
             */
            public static final int MODE_OPEN_FOLDER = 2;
            /**
             * Allows picking a nonexistent file and saving it.
             */
            public static final int MODE_SAVE = 3;

            /**
             * Parse the result returned by the file picker activity. This
             * method should be used with {@link #createIntent}. Refer to
             * {@link #createIntent} for how to use it.
             *
             * @param resultCode
             *            the integer result code returned by the file picker
             *            activity.
             * @param data
             *            the intent returned by the file picker activity.
             * @return the Uris of selected file(s) or null if the resultCode
             *         indicates activity canceled or any other error.
             */
            // public static Uri[] parseResult(int resultCode, Intent data) {
            // return WebViewFactory.getProvider().getStatics()
            // .parseFileChooserResult(resultCode, data);
            // }

            /**
             * Returns file chooser mode.
             */
            public abstract int getMode();

            /**
             * Returns an array of acceptable MIME types. The returned MIME type
             * could be partial such as audio/*. The array will be empty if no
             * acceptable types are specified.
             */
            public abstract String[] getAcceptTypes();

            /**
             * Returns preference for a live media captured value (e.g. Camera,
             * Microphone). True indicates capture is enabled, false disabled.
             * <p>
             * Use <code>getAcceptTypes</code> to determine suitable capture
             * devices.
             */
            public abstract boolean isCaptureEnabled();

            /**
             * Returns the title to use for this file selector, or null. If null
             * a default title should be used.
             */
            public abstract CharSequence getTitle();

            /**
             * The file name of a default selection if specified, or null.
             */
            public abstract String getFilenameHint();

            /**
             * Creates an intent that would start a file picker for file
             * selection. The Intent supports choosing files from simple file
             * sources available on the device. Some advanced sources (for
             * example, live media capture) may not be supported and
             * applications wishing to support these sources or more advanced
             * file operations should build their own Intent.
             * <p>
             * <pre>
             * How to use:
             * 1. Build an intent using {@link #createIntent}
             * 2. Fire the intent using {@link android.app.Activity#startActivityForResult}.
             * 3. Check for ActivityNotFoundException and take a user friendly action if thrown.
             * 4. Listen the result using {@link android.app.Activity#onActivityResult}
             * 5. Parse the result using {@link # } only if media capture was not requested.
             * 6. Send the result using filePathCallback of {@link WebChromeClient#onShowFileChooser}
             * </pre>
             *
             * @return an Intent that supports basic file chooser sources.
             */
            public abstract Intent createIntent();
        }
    }

    // Web视图
    public class TwoWebViewClient extends WebViewClient {
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            // 重要的一部
            view.loadUrl(url);
            return true;
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            loaddingView.setVisibility(View.GONE);
        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            // loaddingView.setVisibility(View.VISIBLE);
        }
    }

    @Override
    // 设置回退
    // 覆盖Activity类的onKeyDown(int keyCoder,KeyEvent event)方法
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if ((keyCode == KeyEvent.KEYCODE_BACK) && mWebView.canGoBack()) {
            mWebView.goBack(); // goBack()表示返回WebView的上一页面
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_BACK) {
            this.finish();
            return true;
        }
        return false;
    }

    private ValueCallback<Uri> mUploadMessage;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == 111) {
            if (null == mUploadMessage) {
                return;
            }

            Uri result = data == null || resultCode != RESULT_OK ? null : data.getData();

            mUploadMessage.onReceiveValue(result);
            mUploadMessage = null;
        } else {
            super.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void onPause() {
        if (shareUtils != null) {
            shareUtils.showLoadingDialog(false);
        }

        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mWebView != null) {
            mWebView.removeAllViews();
//            mWebView.onDestroy();
        }
    }
}
