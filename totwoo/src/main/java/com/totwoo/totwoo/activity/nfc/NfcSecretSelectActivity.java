package com.totwoo.totwoo.activity.nfc;

import android.os.Bundle;
import android.view.KeyEvent;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.data.nfc.SecretInfoBean;
import com.totwoo.totwoo.data.nfc.SecretInfoManager;
import com.totwoo.totwoo.utils.BlackAlertDialogUtil;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.SecretGridView;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * NFC 用户信息排序页面
 */
public class NfcSecretSelectActivity extends BaseActivity {
    private SecretGridView gridView;
    private SecretInfoManager secretInfoManager;

    /**
     * 备份原始的选中数据, 如果不保存, 或者网络同步失败, 需要还原回去
     */
    private LinkedHashSet<String> selectDataBackup;

    /**
     * 标识截止到上次保存, 是否有变更内容
     */
    private boolean hasChangeNotSave;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_nfc_select);

        secretInfoManager = SecretInfoManager.getInstance();

        List<SecretInfoBean> savedInfos = secretInfoManager.getSavedInfos();
        if (savedInfos == null || savedInfos.size() == 0) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        backUpSelectData(savedInfos);
        initUI();
    }

    /**
     * 当前用户是否为未选中任何数据的默认状态
     *
     * @return
     */
    private boolean defaultStatus() {
        return selectDataBackup.isEmpty();
    }


    /**
     * 备份原始的选中数据
     *
     * @param savedInfos
     */
    private void backUpSelectData(List<SecretInfoBean> savedInfos) {
        if (selectDataBackup == null) {
            selectDataBackup = new LinkedHashSet<>();
        } else {
            selectDataBackup.clear();
        }

        for (SecretInfoBean info : savedInfos) {
            if (info.isSelected()) {
                selectDataBackup.add(info.getId());
            }
        }
    }

    /**
     * 还原选择数据
     */
    private void restoreSelectData() {
        if (selectDataBackup == null) {
            return;
        }
        List<SecretInfoBean> savedInfos = secretInfoManager.getSavedInfos();
        for (SecretInfoBean savedInfo : savedInfos) {
            savedInfo.setSelected(selectDataBackup.contains(savedInfo.getId()));
        }
    }


    private void initUI() {
        CommonUtils.setStateBarTransparentForBlackUI(this);

        gridView = findViewById(R.id.nfc_sort_info_gridview);
        findViewById(R.id.nfc_sort_back).setOnClickListener(v -> backAndCheckSave());
        findViewById(R.id.nfc_sort_save).setOnClickListener(v -> save());

        gridView.toggleSelectMode(true);

        // 如果没有数据, 则默认全部选中
        if (defaultStatus()) {
            for (SecretInfoBean savedInfo : secretInfoManager.getSavedInfos()) {
                savedInfo.setSelected(true);
            }
        }

        gridView.setSecretInfoList(secretInfoManager.getSavedInfos());
        gridView.setSecretInfoClickListener(new SecretGridView.SecretInfoClickListener() {
            @Override
            public void onClick(SecretInfoBean bean) {
                hasChangeNotSave = true;
                bean.setSelected(!bean.isSelected());
                gridView.notifyItemViewer(bean);
            }

            @Override
            public void onAdditionalClick(int additionalType) {
            }
        });
    }

    private void save() {
        ArrayList<String> ids = new ArrayList<>();
        for (SecretInfoBean savedInfo : secretInfoManager.getSavedInfos()) {
            if (savedInfo.isSelected()) {
                ids.add(savedInfo.getId());
            }
        }

        if (ids.isEmpty()) {
            ToastUtils.showLong(this, R.string.at_least_one_data_error);
            return;
        }
        NfcLoading.show(this);
        secretInfoManager.saveSelectSecretData(ids, code -> {
            NfcLoading.dismiss();
            if (code == 0) {
                backUpSelectData(secretInfoManager.getSavedInfos());
                hasChangeNotSave = false;
                ToastUtils.showLong(this, R.string.saved_success);
            } else {
                ToastUtils.showLong(this, HttpHelper.getUnknownErrorMessage(this, String.valueOf(code)));
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            backAndCheckSave();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 返回, 并确认保存
     */
    private void backAndCheckSave() {
        if (hasChangeNotSave) {
            BlackAlertDialogUtil.showCommonDialog(this, R.string.not_save_warn, R.string.confirm, () -> {
                restoreSelectData();
                finish();
            });
        } else {
            restoreSelectData();
            finish();
        }
    }
}
