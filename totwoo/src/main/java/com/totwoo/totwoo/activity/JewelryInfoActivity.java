package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.ToTwooApplication.baseContext;

import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.blankj.utilcode.util.Utils;
import com.bumptech.glide.Glide;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.activity.security.SafeJewSettingActivity;
import com.totwoo.totwoo.bean.JewelryOnlineDataBean;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BleUtils;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class JewelryInfoActivity extends BaseActivity implements SubscriberListener {
    public static final String CONNECT_SUCCESS = "CONNECT_SUCCESS";
    @BindView(R.id.jewelry_info_name_tv)
    TextView mNameTv;
    @BindView(R.id.jewelry_info_main_iv)
    ImageView mProductIv;

    @BindView(R.id.jewelry_info_hint_cl)
    ConstraintLayout mInfoCl;
    @BindView(R.id.jewelry_info_set_cl)
    ConstraintLayout mSetCl;
    //    @BindView(R.id.jewelry_info_loading_cl)
//    ConstraintLayout mLoadingCl;
    @BindView(R.id.jewelry_info_fee_cl)
    ConstraintLayout mFeeCl;
    @BindView(R.id.jewelry_info_imei_cl)
    ConstraintLayout mImeiCl;
    @BindView(R.id.jewelry_info_hw_add_cl)
    ConstraintLayout jewelryAddCl;
    @BindView(R.id.jewelry_info_imei_tv)
    TextView mImeiTv;

    @BindView(R.id.jewelry_info_hw_name_cl)
    ConstraintLayout mHwNameCl;
    @BindView(R.id.jewelry_info_hw_name_key_tv)
    TextView mHwKeyNameTv;
    @BindView(R.id.jewelry_info_hw_name_tv)
    TextView mHwNameTv;
    @BindView(R.id.jewelry_info_hw_add_tv)
    TextView macAddresstv;

    @BindView(R.id.jewelry_info_ota_tv)
    TextView mOTATv;
    @BindView(R.id.jewelry_info_ota_cl)
    ConstraintLayout mOtaCl;
    @BindView(R.id.jewelry_info_set_tv)
    TextView mInfoSetTv;

    @BindView(R.id.jewelry_apart_tv)
    TextView upPairTv;

    private String jew;
    private String ver;
    private String device_info;
    private int downClickCount;
    private long lastDownClickTime;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_jewelry_info);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        String jewName = getIntent().getStringExtra(JewelryPairedListActivity.SELECTED_JEWELRY);
        if (TextUtils.isEmpty(jewName)) {
            jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        }

        JewelryOnlineDataBean jewData = JewelryOnlineDataManager.getInstance().getJewInfoByName(jewName);
        mProductIv.setImageResource(BleParams.getJewelryResourceId(jewName));
        Glide.with(this).load(jewData.getImgUrl()).placeholder(BleParams.getJewelryResourceId(jewName)).error(BleParams.getJewelryResourceId(jewName)).into(mProductIv);
        mNameTv.setText(TextUtils.isEmpty(jewData.getTitle()) ? BleParams.getTranName(jewName, JewelryInfoActivity.this) : jewData.getTitle());

        CommonUtils.setTestMultiClick(mNameTv, v -> testEnterOTAMode(), 3);

        mImeiTv.setText(PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, ""));
        ver = PreferencesUtils.getString(baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "");
        mOTATv.setText(ver);
        mHwNameTv.setText(jewName);

        macAddresstv.setText(PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_BLE_ADRESS_TAG, ""));

        if (BleParams.isButtonBatteryJewelry()) {
            mInfoSetTv.setText(R.string.jewelry_info_set_no_vibrate);
        }

//        currentBleAddress = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_BLE_ADRESS_TAG, "");
//        if (!TextUtils.equals(currentBleAddress, getIntent().getStringExtra(JewelryPairedListActivity.SELECTED_ADDRESS))) {
//            mInfoCl.setVisibility(View.GONE);
//            mSetCl.setVisibility(View.GONE);
//        }
//        if (BleParams.isSecurityJewlery()) {
//            mFeeCl.setVisibility(View.VISIBLE);
//            mImeiCl.setVisibility(View.VISIBLE);
//            if (!PreferencesUtils.getBoolean(this, IS_IMEI_SENT, false)) {
//                mOtaCl.setVisibility(View.GONE);
//            }
//        }

        // NFC 首饰样式
        if (BleParams.isNfcJewelry(jewName)) {
            mSetCl.setVisibility(View.GONE);
            mInfoCl.setVisibility(View.GONE);
            mOtaCl.setVisibility(View.GONE);
            jewelryAddCl.setVisibility(View.GONE);
            mImeiCl.setVisibility(View.VISIBLE);

            mHwKeyNameTv.setText(R.string.safe_security_info_hw_name);

            String uid = getIntent().getStringExtra(JewelryPairedListActivity.SELECTED_ADDRESS);
            mImeiTv.setText(uid == null ? "" : uid);
            upPairTv.setText(R.string.nfc_unbound);
        }
    }

    private void testEnterOTAMode() {
        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
            if (JewInfoSingleton.getInstance().getBattery() > 20) {
                BluetoothManage.getInstance().enterOtaMode();
                BluetoothManage.getInstance().setOTA(false);
            } else {
                ToastUtils.showLong(this, R.string.low_batter_cant_ota_battery);
            }
        } else {
            ToastUtils.showLong(this, R.string.error_jewelry_connect);
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> goNext());
//        setTopTitle(R.string.jewelry_info_totwoo);
        setSpinState(false);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        goNext();
    }

    private void goNext() {
        if (TextUtils.equals(CONNECT_SUCCESS, getIntent().getStringExtra(CommonArgs.FROM_TYPE))) {
            LogUtils.e("HomeActivityControl");
            HomeActivityControl.getInstance().connectJew(JewelryInfoActivity.this);
        }
        finish();
    }

    @OnClick({R.id.jewelry_info_hint_cl, R.id.jewelry_info_set_cl, R.id.jewelry_apart_tv, R.id.jewelry_info_loading_cl,
            R.id.jewelry_info_fee_cl, R.id.jewelry_info_ota_cl, R.id.jewelry_info_main_iv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.jewelry_info_hint_cl:
                startActivity(new Intent(JewelryInfoActivity.this, SetPermissionActivity.class));
//                WebViewActivity.loadUrl(JewelryInfoActivity.this, HttpHelper.getSelfStartPageUrl(), false);
                break;
            case R.id.jewelry_info_set_cl:
                if (BleParams.isSecurityJewlery()) {
                    startActivity(new Intent(JewelryInfoActivity.this, SafeJewSettingActivity.class));
                    return;
                }
//                if (BleParams.isMWJewlery()|| BleParams.isCtJewlery()) {
                startActivity(new Intent(JewelryInfoActivity.this, ReminderSettingsActivity.class));
//                }else {
//                    startActivity(new Intent(JewelryInfoActivity.this, NotifyActivity.class));
//                }
                break;
            case R.id.jewelry_apart_tv:
                unPair();
                break;
            case R.id.jewelry_info_loading_cl:
                break;
            case R.id.jewelry_info_fee_cl:
//                startActivity(new Intent(JewelryInfoActivity.this, SecurityPayActivity.class));
                break;
//            case R.id.jewelry_info_main_iv:
//                startActivity(new Intent(JewelryInfoActivity.this, DebugActivity.class));
//                break;
            case R.id.jewelry_info_ota_cl:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.JEWERLY_UPGRADE);
                if (!BleUtils.isBlEEnable(baseContext)) {
                    showBluetoothDialog();
                    return;
                }

                // 连击 6 次
                if (SystemClock.elapsedRealtime() - lastDownClickTime < 1000) {
                    if (++downClickCount == 9) {
                        checkOTA(true);
                    }
                } else {
                    downClickCount = 0;
                    checkOTA(false);
                }
                lastDownClickTime = SystemClock.elapsedRealtime();

                break;
        }
    }

    private void checkSercuityStatus() {
        // 使用新的网络请求接口 - 灵活的错误处理
        launchRequest(
                HttpHelper.safeService.getSafeState(2001),
                safeData -> {
                    if (TextUtils.equals(safeData.getType(), "N")) {
                        checkAndUnPair();
                    } else if (TextUtils.equals(safeData.getType(), "Y")) {
                        checkAndUnPair();
                    } else if (TextUtils.equals(safeData.getType(), "GUARD")) {
                        showCancelGuardDialog();
                    } else if (TextUtils.equals(safeData.getType(), "HELP")) {
                        showCancelEmergencyDialog();
                    }
                }, true
        );
    }

    private void unPair() {
        if (!NetUtils.isConnected(this)) {
            ToastUtils.showShort(this, R.string.error_net);
            return;
        }
        if (!BleParams.isSecurityJewlery()) {
            checkAndUnPair();
        } else {
            checkSercuityStatus();
        }
    }

    private boolean isUnPair = false;

    private void checkAndUnPair() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(this);
        commonMiddleDialog.setMessage(BleParams.isNfcJewelry(null) ? R.string.jewelry_nfc_unbind_warning : R.string.jewelry_list_unbind_hint);
        commonMiddleDialog.setSure(R.string.jewelry_list_unbind_confirm, v -> {
            isUnPair = true;
            showProgressDialog();
            BluetoothManage.getInstance().unPair();
            PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI);
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_READ_VERSION, runThread = TaskType.UI)
    public void jewrlyReadVersion(EventData data) {
        ver = PreferencesUtils.getString(baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "");
        mOTATv.setText(ver);
    }

    //切换，解绑成功
    @EventInject(eventType = S.E.E_JEWERLY_APART_FAIL, runThread = TaskType.UI)
    public void jewrlyApartFail(EventData data) {
        dismissProgressDialog();
        ToastUtils.showLong(Utils.getApp(), "unbind fail,try again");
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_APART, runThread = TaskType.UI)
    public void jewrlyApartSuccess(EventData data) {
        if (!isUnPair) {
            return;
        }
        isUnPair = false;
        String address = getIntent().getStringExtra(JewelryPairedListActivity.SELECTED_ADDRESS);
        String name = getIntent().getStringExtra(JewelryPairedListActivity.SELECTED_JEWELRY);

        if (BleParams.isNfcJewelry(name)) {
            // 使用新的网络请求接口 - NFC首饰解绑
            launchRequest(
                    HttpHelper.commonService.bindState(name, address, address, "relieve"),
                    bindData -> {
                        doMultiJewelryDelete(name, address);
                    },
                    true
            );
        } else {
            doMultiJewelryDelete(name, address);
        }
    }

    private void doMultiJewelryDelete(String name, String address) {
        showProgressDialog();
        // 使用新的网络请求接口 - 删除首饰 relieve 在del
        launchRequestWithFlexibleError(
                HttpHelper.commonService.bindState("", "", "", "relieve"),
                relieveData -> {
                    //nfc
                    if (BleParams.isSecurityJewlery(getIntent().getStringExtra(JewelryPairedListActivity.SELECTED_JEWELRY))) {
                        launchRequestWithFlexibleError(
                                HttpHelper.multiJewelryService.deleteIMEI(2001),
                                imeiData -> {
                                    afterDel(name, address);

                                }, fail -> {
                                    dismissProgressDialog();
                                    return false;
                                },
                                false // 不显示加载框
                        );
                    } else {
                        //蓝牙
                        launchRequestWithFlexibleError(
                                HttpHelper.multiJewelryService.deleteJewelry(address, "", name),
                                deleteData -> {
                                    // 如果是安全首饰，需要删除IMEI
                                    afterDel(name, address);
                                }, fail -> {
                                    dismissProgressDialog();
                                    return false;
                                },
                                false
                        );
                    }
                },
                error -> {
                    dismissProgressDialog();
                    return false;
                }, false
        );
    }

    private void afterDel(String name, String address) {
        // 删除本地数据库
        LocalJewelryDBHelper.getInstance().deleteBean(address);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_BLE_ADRESS_TAG);
        JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_UNPAIRED);
        ToastUtils.showLong(JewelryInfoActivity.this, R.string.unbind_paired_success);
        Log.e("xLog", "delete " + address);
        try {
            if (LocalJewelryDBHelper.getInstance().getAllBeans().size() > 0) {
                // 还有其他首饰，选择第一个作为当前首饰
                LocalJewelryDBHelper.getInstance().setFirstSelected();
                LocalJewelryInfo info = LocalJewelryDBHelper.getInstance().getSelectedBean();
                changeJewelry(info);
            } else {
                next();
            }
        } catch (DbException e) {
            dismissProgressDialog();
            Log.e("xLog", "delete  sql error" );
            e.printStackTrace();
        }
    }

    private void changeJewelry(LocalJewelryInfo info) {
        // 绑定新的首饰状态
        launchRequestWithFlexibleError(
                HttpHelper.commonService.bindState(info.getName(), BleParams.isNfcJewelry(info.getName()) ? info.getMac_address() : "", info.getMac_address(), "connect"),
                bindData -> {
                    // 更新首饰信息
                    launchRequestWithFlexibleError(
                            HttpHelper.multiJewelryService.updateJewelry(BleParams.isNfcJewelry(info.getName()) ? info.getMac_address() : "", info.getMac_address(), info.getName(), "", "", 1, ""),
                            updateData -> {

                                Log.e("xLog", "change:" + info.toString());
                                PreferencesUtils.put(JewelryInfoActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, info.getMac_address());
                                PreferencesUtils.put(JewelryInfoActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, info.getName());
                                PreferencesUtils.put(JewelryInfoActivity.this, BleParams.PAIRED_CHANGE_CONNECT, true);
                                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
                                BluetoothManage.getInstance().startBackgroundScan();
                                BluetoothManage.getInstance().reconnect(false);

                                next();
                            },
                            fail -> {
                                dismissProgressDialog();
                                return false;
                            },
                            false // 不显示加载框
                    );
                },
                fail -> {
                    dismissProgressDialog();
                    return false;
                },
                false // 不显示加载框
        );
    }

    private void next() {
        dismissProgressDialog();

        HomeActivityControl.getInstance().connectJew(JewelryInfoActivity.this);
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ORDER_UPDATE, null);
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_EMOTION, null);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    private void showCancelGuardDialog() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(this);
        commonMiddleDialog.setMessage(R.string.jewelry_unbind_guard_hint);
        commonMiddleDialog.setSure(R.string.jewelry_list_unbind_confirm, v -> {
            HttpHelper.safeService.cancelGuard(2001)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(JewelryInfoActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                            isUnPair = true;
                            BluetoothManage.getInstance().unPair();
                            PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI);
                        }
                    });
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    private void showCancelEmergencyDialog() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(this);
        commonMiddleDialog.setMessage(R.string.jewelry_unbind_emergency_hint);
        commonMiddleDialog.setSure(R.string.jewelry_list_unbind_confirm, v -> {
            HttpHelper.safeService.cancelEmergency(2001)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(JewelryInfoActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                            isUnPair = true;
                            BluetoothManage.getInstance().unPair();
                            PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI);
                        }
                    });
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    /**
     * 展示请求蓝牙开启的对话框
     */
    public void showBluetoothDialog() {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(JewelryInfoActivity.this);
        dialog.setMessage(R.string.request_open_bluetooth);
        dialog.setSure(R.string.allow, v -> {
            BleUtils.enableBlueTooth(JewelryInfoActivity.this);
            dialog.dismiss();
        });
        dialog.setCancel(R.string.cancel);

        dialog.show();
    }

    private void checkOTA(boolean isDowngrade) {
        if (!PermissionUtil.hasBluetoothPermission(this)) {
            return;
        }

        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showShort(baseContext, R.string.error_jewelry_connect);
            return;
        }
        jew = BluetoothManage.getInstance().getOTAName();

        device_info = PreferencesUtils.getString(baseContext, BleParams.TOTWOO_DEVICE_INFO, "");
        if (TextUtils.isEmpty(ver)) {
            BluetoothManage.getInstance().readFirmwareVersion();

            // 做一次重试的尝试, 失败则直接返回
            mHandler.postDelayed(() -> {
                jew = BluetoothManage.getInstance().getOTAName();
                if (TextUtils.isEmpty(ver)) {
                    ToastUtils.showLong(JewelryInfoActivity.this, R.string.jewelry_info_ota_no_need);
                } else {
                    BluetoothManage.getInstance().checkOTA(true, isDowngrade);
                }
            }, 2000);
        } else {
            BluetoothManage.getInstance().checkOTA(true, isDowngrade);
        }
    }
}
