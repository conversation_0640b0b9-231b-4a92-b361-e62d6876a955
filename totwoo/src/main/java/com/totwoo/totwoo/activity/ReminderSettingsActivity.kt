package com.totwoo.totwoo.activity

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.Toast
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.Utils
import com.etone.framework.annotation.EventInject
import com.etone.framework.annotation.InjectUtils
import com.etone.framework.event.EventData
import com.etone.framework.event.SubscriberListener
import com.etone.framework.event.TaskType
import com.hjq.shape.view.ShapeView
import com.totwoo.library.util.Apputils
import com.totwoo.totwoo.R
import com.totwoo.totwoo.S
import com.totwoo.totwoo.ToTwooApplication
import com.totwoo.totwoo.bean.CallRemindContact
import com.totwoo.totwoo.bean.JewSettings
import com.totwoo.totwoo.bean.JewelryNotifyModel
import com.totwoo.totwoo.bean.Sedentary
import com.totwoo.totwoo.bean.UserOption
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.ble.JewInfoSingleton
import com.totwoo.totwoo.databinding.ActivityReminderSettingsBinding
import com.totwoo.totwoo.utils.HttpHelper
import com.totwoo.totwoo.utils.NotifyUtil
import com.totwoo.totwoo.utils.PreferencesUtils
import com.totwoo.totwoo.widget.JewelryGlitterConfirmDialog
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import rx.Observer
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers

/**
 * 皓月33设备新设置界面
 */
class ReminderSettingsActivity : BaseActivity(), SubscriberListener {

    private lateinit var binding: ActivityReminderSettingsBinding
    private var tempProgress: Int = 0
    private var currentTouchColor: String = "RED" // 当前触摸颜色
    private var isJewelryGlitterEnabled: Boolean = true // 首饰通知闪光开关状态

    // Loading超时处理
    private val loadingTimeoutRunnable = Runnable {
        dismissProgressDialog()
    }

    // 防抖处理：避免频繁操作导致状态混乱
    private var lastGlitterSwitchTime = 0L
    private val GLITTER_SWITCH_DEBOUNCE_TIME = 300L // 300ms防抖

    // 所有需要动画的图标列表
    private val glitterIcons by lazy {
        ArrayList<View>(6).apply {
            add(binding.callNotifyIco1)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReminderSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        EventBus.getDefault().register(this)
        InjectUtils.injectOnlyEvent(this)


        //长条皓月存蓝牙
        if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {
            loadJewelrySettings()
        } else {//其他存本地
            updateUIFromSP()
        }

        initView()
        initListener()
    }



    /**
     * 使用协程和Flow方式读取固件配置 - 使用collect持续监听，带超时保护
     */
    private fun loadJewelrySettings() {
        showProgressDialog()
        // 先启动监听
      lifecycleScope.launch {
            BluetoothManage.getInstance().jewSettingsFlow.collect { settings ->
                mHandler.removeCallbacksAndMessages(null)
                dismissProgressDialog()
                updateUI(settings)
            }
        }

        showProgressDialog()
        BluetoothManage.getInstance().readJewSettings()
        mHandler.postDelayed({
            dismissProgressDialog()
        },2000)
    }

    /**
     * 更新UI显示
     */
    private fun updateUI(settings: JewSettings) {
        settings.run {
            // 更新首饰通知闪光状态
            isJewelryGlitterEnabled = isGlitterEnabled
            binding.switchJewelryGlitter.isChecked = isJewelryGlitterEnabled

            // 更新振动强度
            tempProgress = vibrationIntensity
            val mappedProgress = when {
                tempProgress <= 10 -> 0    // 无振动
                tempProgress <= 30 -> 20   // 最弱振动
                tempProgress <= 50 -> 40   // 弱振动
                tempProgress <= 70 -> 60   // 中振动
                tempProgress <= 90 -> 80   // 强振动
                else -> 100               // 最强振动
            }
            binding.seekbarVibration.progress = mappedProgress
            displayCheckView(mappedProgress)

            // 更新触摸颜色
            currentTouchColor = NotifyUtil.getJewColorName(touchColor)
            setSelectColor(
                binding.ivCustomizeTouchColor,
                currentTouchColor
            )

            // 刷新所有提醒项的显示状态，确保与闪光开关状态一致
            refreshAllNotifyItems()
        }
    }

    /**
     * 显示错误信息
     */
    private fun showError(error: String) {
        Toast.makeText(this, "读取固件配置失败: $error", Toast.LENGTH_SHORT).show()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 取消loading超时任务
        binding.root.removeCallbacks(loadingTimeoutRunnable)
        EventBus.getDefault().unregister(this)
        InjectUtils.injectUnregisterListenerAll(this)
    }

    override fun initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black)
        setTopTitle(R.string.me_t9)

        setTopLeftOnclik { v: View? -> saveUserOption() }
    }

    private fun saveUserOption() {
        launchRequest(
            HttpHelper.userOption.saveUseroption(
                if (NotifyUtil.getTotwooNotifyModel(
                        this
                    ).isNotifySwitch
                ) 1 else 0, if (NotifyUtil.getFortuneNotifyModel(
                        this
                    ).isNotifySwitch
                ) 1 else 0
            ),
            { _ ->
                finish()
            }
        )
    }

    override fun onBackPressed() {
        saveUserOption()
    }


    private fun selectColor(view: ShapeView, b: Boolean) {
        view.shapeDrawableBuilder.run {
            solidColor = Color.parseColor(if (b) "#000000" else "#f5f5f5")
            intoBackground()
        }
    }

    private fun initView() {

        //皓月、 长条
        if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {
            binding.rlJewelryGlitter.visibility = View.VISIBLE
            binding.rlStepTarget.visibility = View.GONE
            binding.notifyStepLine.visibility = View.GONE
            binding.rlPeriodReminder.visibility = View.GONE
            binding.notifyPeriodLine.visibility = View.GONE
            binding.llSedentary.visibility = View.GONE
            binding.rlCustomizeTouchColor.visibility = View.VISIBLE

        } else if (BleParams.isButtonBatteryJewelry()) {
            binding.rlJewelryGlitter.visibility = View.GONE
            binding.rlStepTarget.visibility = View.GONE
            binding.notifyStepLine.visibility = View.GONE
            // 根据用户性别显示/隐藏大姨妈提醒
            if (ToTwooApplication.owner.gender == 1) {
                binding.rlPeriodReminder.visibility = View.VISIBLE
                binding.notifyPeriodLine.visibility = View.VISIBLE
            } else {
                binding.rlPeriodReminder.visibility = View.GONE
                binding.notifyPeriodLine.visibility = View.GONE
            }

            binding.rlVibration.visibility = View.GONE
            binding.vibrationLayoutBottom.visibility = View.GONE
            binding.llSedentary.visibility = View.GONE
            binding.notifySedentaryLine.visibility = View.GONE
        } else if (BleParams.isSM2()) {
            binding.rlJewelryGlitter.visibility = View.GONE

            binding.llSedentary.visibility = View.GONE
            binding.notifySedentaryLine.visibility = View.GONE

            binding.rlPeriodReminder.visibility = View.GONE
            binding.notifyPeriodLine.visibility = View.GONE
            binding.rlStepTarget.visibility = View.GONE
            binding.notifyStepLine.visibility = View.GONE
        } else if (BleParams.isJewlery32() || BleParams.isJewlery23()) {
            binding.rlPeriodReminder.visibility = View.GONE
            binding.rlJewelryGlitter.visibility = View.GONE

        }

        // 根据系统语言中英文显示/隐藏幸运日
        binding.rlLuckyDay.visibility = if (Apputils.systemLanguageIsOther(this))
            View.GONE
        else
            View.VISIBLE



        HttpHelper.userOption.useroption
            .subscribeOn(Schedulers.newThread())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : Observer<HttpBaseBean<UserOption>> {
                override fun onCompleted() {
                }

                override fun onError(e: Throwable) {
//                    refreshAllNotifyItems()
                }

                override fun onNext(userOptionHttpBaseBean: HttpBaseBean<UserOption>) {
                    if (userOptionHttpBaseBean.errorCode == 0) {
                        NotifyUtil.setTotwooSwitchKey(
                            this@ReminderSettingsActivity,
                            userOptionHttpBaseBean.data.totwoo_notify != 0
                        )
                        NotifyUtil.setFortuneSwitchKey(
                            this@ReminderSettingsActivity,
                            userOptionHttpBaseBean.data.constellation_notify != 0
                        )
                    }

//                    refreshAllNotifyItems()
                }
            })
    }

    private fun displayCheckView(mappedProgress: Int) {
        when (mappedProgress) {
            0 -> {
                selectColor(binding.point1, true)
                selectColor(binding.point2, false)
                selectColor(binding.point3, false)
                selectColor(binding.point4, false)
                selectColor(binding.point5, false)
                selectColor(binding.point6, false)
            }

            20 -> {
                selectColor(binding.point1, true)
                selectColor(binding.point2, true)
                selectColor(binding.point3, false)
                selectColor(binding.point4, false)
                selectColor(binding.point5, false)
                selectColor(binding.point6, false)
            }

            40 -> {
                selectColor(binding.point1, true)
                selectColor(binding.point2, true)
                selectColor(binding.point3, true)
                selectColor(binding.point4, false)
                selectColor(binding.point5, false)
                selectColor(binding.point6, false)
            }

            60 -> {
                selectColor(binding.point1, true)
                selectColor(binding.point2, true)
                selectColor(binding.point3, true)
                selectColor(binding.point4, true)
                selectColor(binding.point5, false)
                selectColor(binding.point6, false)
            }

            80 -> {
                selectColor(binding.point1, true)
                selectColor(binding.point2, true)
                selectColor(binding.point3, true)
                selectColor(binding.point4, true)
                selectColor(binding.point5, true)
                selectColor(binding.point6, false)
            }

            100 -> {
                selectColor(binding.point1, true)
                selectColor(binding.point2, true)
                selectColor(binding.point3, true)
                selectColor(binding.point4, true)
                selectColor(binding.point5, true)
                selectColor(binding.point6, true)
            }
        }
    }

    private fun initListener() {
        // 震动强度调节
        binding.seekbarVibration.setOnSeekBarChangeListener(object :
            SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    // 实时吸附到最近档位
                    val snapProgress = when {
                        progress < 10 -> 0     // 无振动
                        progress < 30 -> 20    // 最弱振动
                        progress < 50 -> 40    // 弱振动
                        progress < 70 -> 60    // 中振动
                        progress < 90 -> 80    // 强振动
                        else -> 100           // 最强振动
                    }
                    seekBar?.progress = snapProgress
                    displayCheckView(snapProgress)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                tempProgress = binding.seekbarVibration.progress

                // 将SeekBar进度直接作为震动强度值
                val vibrationIntensity = tempProgress
                PreferencesUtils.put(
                    this@ReminderSettingsActivity,
                    BleParams.SELECT_VIBRATION_INDEX_TAG,
                    vibrationIntensity
                )

                // 写入振动强度到固件
                if (JewInfoSingleton.getInstance().connectState != JewInfoSingleton.STATE_CONNECTED) {
                    Toast.makeText(
                        this@ReminderSettingsActivity,
                        R.string.error_jewelry_connect,
                        Toast.LENGTH_SHORT
                    ).show()
                    return
                }
                BluetoothManage.getInstance().setVibrationIntensity(vibrationIntensity)

            }
        })

        // 自定义触摸颜色点击事件
        binding.rlCustomizeTouchColor.setOnClickListener {
            startActivityForResult(
                Intent(this, CustomTouchColorActivity::class.java)
                    .putExtra("current_color", currentTouchColor),
                REQUEST_CODE_CUSTOM_COLOR
            )
        }

        // 首饰通知闪光开关
        binding.rlJewelryGlitter.setOnClickListener {
            val newState = !binding.switchJewelryGlitter.isChecked
            handleJewelryGlitterStateChange(newState)
        }

//        binding.switchJewelryGlitter.setOnCheckedChangeListener { _, isChecked ->
//            handleJewelryGlitterStateChange(isChecked)
//        }

        // 来电提醒
        binding.rlCallReminder.setOnClickListener {
            startActivityForResult(
                Intent(this, CallRemindSetActivity::class.java),
                REQUESTCODE_CALL
            )
        }

        if (Apputils.systemLanguageIsOther(Utils.getApp())) {
            binding.rlLuckyDay.visibility = View.GONE
        } else {
            binding.rlLuckyDay.visibility = View.VISIBLE
        }

        // 幸运日将至
        binding.rlLuckyDay.setOnClickListener {
            startActivityForResult(
                Intent(this, NotifySettingActivity::class.java)
                    .putExtra(
                        NotifySettingActivity.NOTIFY_TYPE_TAG,
                        NotifySettingActivity.TYPE_FORTUNE
                    ),
                REQUESTCODE_FORTUNE
            )
        }

        // 久坐提醒
        binding.llSedentary.setOnClickListener {
            startActivityForResult(
                Intent(this, NotifySettingActivity::class.java)
                    .putExtra(
                        NotifySettingActivity.NOTIFY_TYPE_TAG,
                        NotifySettingActivity.TYPE_SEDENTARY
                    ),
                REQUESTCODE_SEDENTARY
            )
        }

        // 收到情书
        binding.rlReceivedLoveLetter.setOnClickListener {
            startActivityForResult(
                Intent(this, NotifySettingActivity::class.java)
                    .putExtra(
                        NotifySettingActivity.NOTIFY_TYPE_TAG,
                        NotifySettingActivity.TYPE_GIFT
                    ),
                REQUESTCODE_GIFT
            )
        }

        // 行走目标达到
        binding.rlStepTarget.setOnClickListener {
            val height = ToTwooApplication.owner.height
            val weight = ToTwooApplication.owner.weight
            if (height == 0 || weight == 0) {
                // 身高体重未设置，跳转到信息设置页面
                startActivity(
                    Intent(this, InitInfoActivity::class.java)
                        .putExtra(InitInfoActivity.INIT_INFO, false)
                )
            } else {
                // 身高体重已设置，跳转到行走目标设置页面
                startActivityForResult(
                    Intent(this, NotifySettingActivity::class.java)
                        .putExtra(
                            NotifySettingActivity.NOTIFY_TYPE_TAG,
                            NotifySettingActivity.TYPE_STEP
                        ),
                    REQUESTCODE_STEP
                )
            }
        }

        // 大姨妈提醒
        binding.rlPeriodReminder.setOnClickListener {
            startActivityForResult(
                Intent(this, PeriodSettingActivity::class.java),
                REQUESTCODE_PERIOD
            )
        }
    }


    private fun initCallItem(model: JewelryNotifyModel) {
        if (!model.isNotifySwitch) {
            switchCallIcos(false)
            binding.callNotifyLayout.visibility = View.GONE
            binding.callNotifyTv.visibility = View.VISIBLE
            binding.callNotifyTv.setText(R.string.notify_off)
            binding.notifyCallArrow.visibility = View.GONE
        } else {
            binding.callNotifyLayout.visibility = View.VISIBLE
            binding.callNotifyTv.visibility = View.GONE
            binding.notifyCallArrow.visibility = View.VISIBLE

            // 重要联系人模式
            if (PreferencesUtils.getBoolean(
                    this,
                    CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_SWITCH_KEY,
                    false
                )
            ) {
                val dataJson = PreferencesUtils.getString(
                    this,
                    CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_DATA_KEY,
                    ""
                )
                val mCallRamindContacts =
                    com.google.gson.Gson().fromJson<List<CallRemindContact>>(
                        dataJson,
                        object :
                            com.google.gson.reflect.TypeToken<List<CallRemindContact>>() {}.type
                    )

                if (mCallRamindContacts == null || mCallRamindContacts.isEmpty()) {
                    switchCallIcos(false)
                    binding.callNotifyLayout.visibility = View.GONE
                    binding.callNotifyTv.visibility = View.VISIBLE
                    binding.callNotifyTv.setText(R.string.dont_select_contact1)
                    binding.notifyCallArrow.visibility = View.GONE
                } else {
                    binding.callNotifyLayout.visibility = View.VISIBLE
                    switchCallIcos(false)

                    // 根据重要联系人数量显示对应数量的图标
                    for (i in 0 until mCallRamindContacts.size) {
                        binding.callNotifyPeo1.visibility = View.VISIBLE

                        when (i) {
                            0 -> {
                                // 防止重复添加
                                if (!glitterIcons.contains(binding.callNotifyIco1)) {
                                    glitterIcons.add(binding.callNotifyIco1)
                                }
                                binding.callNotifyIco1.visibility =
                                    if (isJewelryGlitterEnabled) View.VISIBLE else View.GONE
                                setSelectColor(
                                    binding.callNotifyIco1,
                                    mCallRamindContacts[0].flashColor
                                )
                                binding.callNotifyPeo1.visibility = View.VISIBLE
                                binding.callNotifyPeo1.setImageResource(R.drawable.people_one_33)
                            }

                            1 -> {
                                // 防止重复添加
                                if (!glitterIcons.contains(binding.callNotifyIco2)) {
                                    glitterIcons.add(binding.callNotifyIco2)
                                }
                                binding.callNotifyIco2.visibility =
                                    if (isJewelryGlitterEnabled) View.VISIBLE else View.GONE
                                setSelectColor(
                                    binding.callNotifyIco2,
                                    mCallRamindContacts[1].flashColor
                                )
                                binding.callNotifyPeo1.setImageResource(R.drawable.people_two_33)
                            }

                            2 -> {
                                // 防止重复添加
                                if (!glitterIcons.contains(binding.callNotifyIco3)) {
                                    glitterIcons.add(binding.callNotifyIco3)
                                }
                                binding.callNotifyIco3.visibility =
                                    if (isJewelryGlitterEnabled) View.VISIBLE else View.GONE
                                setSelectColor(
                                    binding.callNotifyIco3,
                                    mCallRamindContacts[2].flashColor
                                )
                                binding.callNotifyPeo1.setImageResource(R.drawable.people_three_33)
                            }
                        }
                    }
                }
            }
            // 全部来电模式
            else if (PreferencesUtils.getBoolean(
                    this,
                    CallRemindSetActivity.ALL_CALL_REMIND_SWITCH_KEY,
                    true
                )
            ) {
                binding.callNotifyLayout.visibility = View.VISIBLE
                switchCallIcos(false)

                // 防止重复添加
                if (!glitterIcons.contains(binding.callNotifyIco1)) {
                    glitterIcons.add(binding.callNotifyIco1)
                }
                binding.callNotifyIco1.visibility =
                    if (isJewelryGlitterEnabled) View.VISIBLE else View.GONE
                setSelectColor(
                    binding.callNotifyIco1,
                    PreferencesUtils.getString(
                        this,
                        CallRemindSetActivity.ALL_CALL_REMIND_FLASH_KEY,
                        "RED"
                    )
                )
                binding.callNotifyPeo1.visibility = View.VISIBLE
                binding.callNotifyPeo1.setImageResource(R.drawable.people_all_33)
            }
            // 仅通讯录模式
            else if (PreferencesUtils.getBoolean(
                    this,
                    CallRemindSetActivity.ALL_CONTACT_REMIND_SWITCH_KEY,
                    false
                )
            ) {
                binding.callNotifyLayout.visibility = View.VISIBLE
                switchCallIcos(false)

                // 防止重复添加
                if (!glitterIcons.contains(binding.callNotifyIco1)) {
                    glitterIcons.add(binding.callNotifyIco1)
                }
                binding.callNotifyIco1.visibility =
                    if (isJewelryGlitterEnabled) View.VISIBLE else View.GONE
                setSelectColor(
                    binding.callNotifyIco1,
                    PreferencesUtils.getString(
                        this,
                        CallRemindSetActivity.ALL_CONTACT_REMIND_FLASH_KEY,
                        "RED"
                    )
                )
                binding.callNotifyPeo1.visibility = View.VISIBLE
                binding.callNotifyPeo1.setImageResource(R.drawable.notify_txl_friend_33)
            }
            // 未选择任何模式
            else {
                switchCallIcos(false)
                binding.callNotifyLayout.visibility = View.GONE
                binding.callNotifyTv.visibility = View.VISIBLE
                binding.callNotifyTv.setText(R.string.notify_off)
            }
        }
    }

    private fun switchCallIcos(visible: Boolean) {
        val visibility = if (visible) View.VISIBLE else View.GONE
        binding.callNotifyIco1.visibility = visibility
        binding.callNotifyIco2.visibility = visibility
        binding.callNotifyIco3.visibility = visibility
        binding.callNotifyPeo1.visibility = visibility
    }

    private fun initCommonItem(
        textView: android.widget.TextView,
        ico1: ImageView,
        ico2: ImageView,
        ico3: ImageView,
        model: JewelryNotifyModel
    ) {
        ico1.visibility = View.GONE
        ico2.visibility = View.GONE
        ico3.visibility = View.GONE

        if (model.isNotifySwitch) {
            // 防止重复添加：先检查是否已存在，不存在才添加
            if (!glitterIcons.contains(ico1)) {
                glitterIcons.add(ico1)
            }

            ico1.visibility = if (isJewelryGlitterEnabled) View.VISIBLE else View.GONE
            ico2.visibility = View.VISIBLE
            ico3.visibility = View.VISIBLE
            textView.visibility = View.GONE
            setSelectColor(ico1, model.flashColor)
            ico2.setImageResource(model.vibrationInteger)
        } else {
            // 提醒关闭时，从列表中移除并隐藏
            glitterIcons.remove(ico1)
            ico1.visibility = View.GONE
            ico2.visibility = View.GONE
            ico3.visibility = View.GONE
            textView.visibility = View.VISIBLE
            textView.setTextColor(resources.getColor(R.color.text_color_gray_99))
            textView.setText(R.string.notify_off)
        }
    }

    /**
     * 闪光通知
     */
    private fun handleJewelryGlitterStateChange(isChecked: Boolean) {
        // 防抖处理：避免频繁操作导致状态混乱
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastGlitterSwitchTime < GLITTER_SWITCH_DEBOUNCE_TIME) {
            return
        }
        lastGlitterSwitchTime = currentTime

        // 只在开启状态下点击关闭时才显示弹窗
        if (!isChecked) {
            // 显示关闭确认弹窗
            JewelryGlitterConfirmDialog(
                this,
                onConfirm = {
                    // 用户点击确认,执行关闭逻辑
                    binding.switchJewelryGlitter.isChecked = false
                    updateJewelryGlitterState(false)
                },
                onCancel = {
                    // 用户点击取消,保持开启状态
                    binding.switchJewelryGlitter.isChecked = true
                }
            ).show()
        } else {
            // 从关闭状态切换到开启状态,直接执行
            binding.switchJewelryGlitter.isChecked = true
            updateJewelryGlitterState(true)
        }
    }

    private fun updateJewelryGlitterState(isChecked: Boolean) {
        // 立即更新状态，避免异步导致的状态不一致
        isJewelryGlitterEnabled = isChecked
        PreferencesUtils.put(this, "jewelry_glitter_enabled", isChecked)

        // 先对当前已有的闪光图标执行动画
        updateGlitterIconsVisibility(isChecked, true)

        refreshAllNotifyItems()

        if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {
            if (JewInfoSingleton.getInstance().connectState == JewInfoSingleton.STATE_CONNECTED) {
                BluetoothManage.getInstance().setJewelryGlitter(isChecked)
            }
        }
    }

    /**
     * 刷新所有提醒项的显示状态，确保与闪光开关状态一致
     */
    private fun refreshAllNotifyItems() {
        // 清理所有闪光图标列表，避免重复添加
        glitterIcons.clear()

        // 来电提醒状态
        initCallItem(NotifyUtil.getCallNotifyModel(this))

        // 幸运日将至状态
        initCommonItem(
            binding.notifyRemindFortuneWay,
            binding.notifyRemindFortuneLight,
            binding.notifyRemindFortuneVirbation,
            binding.notifyRemindFortuneArrow,
            NotifyUtil.getFortuneNotifyModel(this)
        )

        // 久坐提醒状态
        initCommonItem(
            binding.notifySedentaryWay,
            binding.notifySedentaryLight,
            binding.notifySedentaryVirbation,
            binding.notifySedentaryArrow,
            NotifyUtil.getSedentaryNotifyModel(this)
        )

        // 收到情书状态
        initCommonItem(
            binding.notifyRemindGiftWay,
            binding.notifyRemindGiftLight,
            binding.notifyRemindGiftVirbation,
            binding.notifyRemindGiftArrow,
            NotifyUtil.getGiftNotifyModel(this)
        )

        // 行走目标达到状态
        initCommonItem(
            binding.notifyRemindStepWay,
            binding.notifyStepLight,
            binding.notifyStepVirbation,
            binding.notifyStepArrow,
            NotifyUtil.getStepNotifyModel(this)
        )

        // 大姨妈提醒状态
        initCommonItem(
            binding.notifyRemindPeriodWay,
            binding.notifyPeriodLight,
            binding.notifyPeriodVirbation,
            binding.notifyPeriodArrow,
            NotifyUtil.getPeriodNotifyModel(this)
        )
    }

    /**
     * 更新闪光图标的显示状态
     * @param show 是否显示
     * @param animate 是否使用动画
     */
    private fun updateGlitterIconsVisibility(show: Boolean, animate: Boolean) {
//        if (!animate) {
//            // 无动画直接设置可见性
//            glitterIcons.forEach { it.visibility = if (show) View.VISIBLE else View.GONE }
//            return
//        }

        // 使用动画效果
        glitterIcons.forEachIndexed { index, imageView ->
            if (show) {
                // 显示动画：从左到右依次显示
                imageView.animate()
                    .alpha(1f)
                    .translationX(0f)
                    .setDuration(200)
                    .start()
            } else {
                // 隐藏动画：移动到不可见位置，不设置visibility = GONE
                // 因为动画移动后自然就看不见了，没必要控制隐藏显示
                imageView.animate()
                    .alpha(0f)
                    .translationX(50f)
                    .setDuration(200)
                    .start()
            }
        }
    }


    /**
     * 将蓝牙配置同步到SharedPreferences
     */
    private fun syncJewSettingsToSP(settings: JewSettings) {
        // 同步首饰通知闪光开关状态
        PreferencesUtils.put(this, "jewelry_glitter_enabled", settings.isGlitterEnabled)

        // 同步振动强度
        PreferencesUtils.put(
            this,
            BleParams.SELECT_VIBRATION_INDEX_TAG,
            settings.vibrationIntensity
        )

        // 同步触摸颜色
        val colorName = NotifyUtil.getJewColorName(settings.touchColor)
        PreferencesUtils.put(this, NotifyUtil.CUSTOM_TOUCH_COLOR_KEY, colorName)
    }

    /**
     * 根据SharedPreferences更新UI
     */
    private fun updateUIFromSP() {
        // 更新首饰通知闪光状态，只有长条皓月有
        isJewelryGlitterEnabled =
            !(BleParams.isCtJewlery() || BleParams.isMWJewlery()) || PreferencesUtils.getBoolean(
                this, "jewelry_glitter_enabled", true
            );

        binding.switchJewelryGlitter.isChecked = isJewelryGlitterEnabled

        // 更新振动强度
        tempProgress = PreferencesUtils.getInt(this, BleParams.SELECT_VIBRATION_INDEX_TAG, 60)
        val mappedProgress = when {
            tempProgress <= 10 -> 0    // 无振动
            tempProgress <= 30 -> 20   // 最弱振动
            tempProgress <= 50 -> 40   // 弱振动
            tempProgress <= 70 -> 60   // 中振动
            tempProgress <= 90 -> 80   // 强振动
            else -> 100               // 最强振动
        }
        binding.seekbarVibration.progress = mappedProgress
        displayCheckView(mappedProgress)

        // 更新触摸颜色
        currentTouchColor =
            PreferencesUtils.getString(this, NotifyUtil.CUSTOM_TOUCH_COLOR_KEY, "RED")
        setSelectColor(
            binding.ivCustomizeTouchColor,
            currentTouchColor
        )

        // 刷新所有提醒项的显示状态，确保与闪光开关状态一致
        refreshAllNotifyItems()
    }


    private fun setSelectColor(imageView: ImageView, colorName: String) {
        val resId = NotifyUtil.getColorImageResId(colorName)
        imageView.setImageResource(resId)
        if (resId == R.drawable.custom_color_normal) {
            val displayColorByColorName = NotifyUtil.getDisplayColorByColorName(colorName)
            imageView.setColorFilter(displayColorByColorName.toColorInt())
        } else {
            imageView.colorFilter = null
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_CUSTOM_COLOR && resultCode == RESULT_OK) {
            // 更新自定义触摸颜色
            data?.getStringExtra("selected_color")?.let { color ->
                currentTouchColor = color
                setSelectColor(
                    binding.ivCustomizeTouchColor,
                    color
                )
            }
        }
        when (requestCode) {
            REQUESTCODE_CALL -> {
                // 来电提醒状态
                initCallItem(NotifyUtil.getCallNotifyModel(this))
            }

            REQUESTCODE_FORTUNE -> {
                // 幸运日将至状态
                initCommonItem(
                    binding.notifyRemindFortuneWay,
                    binding.notifyRemindFortuneLight,
                    binding.notifyRemindFortuneVirbation,
                    binding.notifyRemindFortuneArrow,
                    NotifyUtil.getFortuneNotifyModel(this)
                )
            }

            REQUESTCODE_SEDENTARY -> {
                // 久坐提醒状态
                initCommonItem(
                    binding.notifySedentaryWay,
                    binding.notifySedentaryLight,
                    binding.notifySedentaryVirbation,
                    binding.notifySedentaryArrow,
                    NotifyUtil.getSedentaryNotifyModel(this)
                )
            }

            REQUESTCODE_STEP -> {
                // 行走目标达到状态
                initCommonItem(
                    binding.notifyRemindStepWay,
                    binding.notifyStepLight,
                    binding.notifyStepVirbation,
                    binding.notifyStepArrow,
                    NotifyUtil.getStepNotifyModel(this)
                )
            }

            REQUESTCODE_PERIOD -> {
                // 大姨妈提醒状态
                initCommonItem(
                    binding.notifyRemindPeriodWay,
                    binding.notifyPeriodLight,
                    binding.notifyPeriodVirbation,
                    binding.notifyPeriodArrow,
                    NotifyUtil.getPeriodNotifyModel(this)
                )
            }

            REQUESTCODE_GIFT -> {
                // 收到情书状态
                initCommonItem(
                    binding.notifyRemindGiftWay,
                    binding.notifyRemindGiftLight,
                    binding.notifyRemindGiftVirbation,
                    binding.notifyRemindGiftArrow,
                    NotifyUtil.getGiftNotifyModel(this)
                )
            }
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun changeSedData(sedentary: Sedentary?) {
        if (this != null) {
            // 只更新久坐提醒项
            initCommonItem(
                binding.notifySedentaryWay,
                binding.notifySedentaryLight,
                binding.notifySedentaryVirbation,
                binding.notifySedentaryArrow,
                NotifyUtil.getSedentaryNotifyModel(this)
            )
        }
    }

    @EventInject(eventType = S.E.E_STEP_NOTIFY_CHANGE, runThread = TaskType.UI)
    fun onStepNotify(data: EventData?) {
        // 只更新步数提醒项
        initCommonItem(
            binding.notifyRemindStepWay,
            binding.notifyStepLight,
            binding.notifyStepVirbation,
            binding.notifyStepArrow,
            NotifyUtil.getStepNotifyModel(this)
        )
    }

    override fun onEventException(eventType: String?, data: EventData?, e: Throwable?) {
    }

    companion object {
        const val REQUEST_CODE_CUSTOM_COLOR = 100

        const val REQUESTCODE_TOTWOO: Int = 1

        const val REQUESTCODE_CALL: Int = 2

        const val REQUESTCODE_STEP: Int = 3

        const val REQUESTCODE_FORTUNE: Int = 4

        const val REQUESTCODE_SEDENTARY: Int = 5

        const val REQUESTCODE_APP: Int = 6

        const val REQUESTCODE_PERIOD: Int = 7

        const val REQUESTCODE_WATER: Int = 8

        const val REQUESTCODE_WISH: Int = 9

        const val REQUESTCODE_GIFT: Int = 10
    }
}