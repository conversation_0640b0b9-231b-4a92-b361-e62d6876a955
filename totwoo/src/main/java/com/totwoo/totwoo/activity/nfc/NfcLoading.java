package com.totwoo.totwoo.activity.nfc;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.totwoo.totwoo.widget.LoadingDialog;

/**
 * 统一封装 NFC 模块相关加载框, 便于统一风格以及修改
 */
public class NfcLoading {
    private static LoadingDialog dialog;

    /**
     * 默认延时显示的延时时间
     */
    private final static int DEFAULT_SHOW_OFFSET_TIME = 400;

    /**
     * 展示Toast 的Runnable, 未显示时直接取消
     */
    private static Runnable showRunnable;
    private static Handler mainHandler = new Handler(Looper.getMainLooper());


    public static void show(Context context) {
        showRunnable = () -> showImpl(context);
        mainHandler.postDelayed(showRunnable, DEFAULT_SHOW_OFFSET_TIME);
    }

    public static void show(Context context, int offset_time) {
        showRunnable = () -> showImpl(context);
        mainHandler.postDelayed(showRunnable, offset_time);
    }

    private static void showImpl(Context context) {
        if (context == null) {
            return;
        }
        if (dialog == null) {
            dialog = new LoadingDialog(context, null);
        }
        try {
            dialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            try {
                dialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (showRunnable != null) {
            mainHandler.removeCallbacks(showRunnable);
        }
    }

    private static boolean isShow() {
        return dialog != null && dialog.isShowing();
    }
}
