package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.content.Intent;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.widget.BirthSettingView;
import com.totwoo.totwoo.widget.HeightCenterView;
import com.totwoo.totwoo.widget.WeightCenterView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class InitInfoActivity extends BaseActivity {
    public static final String INIT_INFO = "init_info";
    @BindView(R.id.init_info_title)
    TextView mTitle;
    @BindView(R.id.init_info_text)
    TextView mInfo;
    @BindView(R.id.init_gender_cl)
    ConstraintLayout mGenderContentView;
    @BindView(R.id.init_birthday_ll)
    LinearLayout mBirthdayContentView;
    @BindView(R.id.init_height_ll)
    LinearLayout mHeightContentView;
    @BindView(R.id.init_weight_ll)
    LinearLayout mWeightContentView;
    @BindView(R.id.init_info_next)
    TextView mNext;
    @BindView(R.id.init_info_last)
    TextView mLast;
    @BindView(R.id.init_info_done)
    TextView mDone;
    @BindView(R.id.gender_female_tv)
    TextView mFemaleSelectTv;
    @BindView(R.id.gender_male_tv)
    TextView mMaleSelectTv;
    @BindView(R.id.gender_other_tv)
    TextView mOtherSelectTv;
    @BindView(R.id.birthday_view)
    BirthSettingView brightView;
    @BindView(R.id.height_view)
    HeightCenterView heightCenterView;
    @BindView(R.id.weight_view)
    WeightCenterView weightCenterView;

    private boolean isInitInfo;
    private int gender = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_info_init);
        ButterKnife.bind(this);
        isInitInfo = getIntent().getBooleanExtra(INIT_INFO, false);
        if (isInitInfo) {
            showGenderStatus();
            brightView.setDefaultValue();
        } else {
            showHeightStatus();
        }
    }

    @OnClick({R.id.init_info_next, R.id.init_info_last, R.id.init_info_done, R.id.gender_male_tv, R.id.gender_female_tv,
            R.id.gender_other_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.init_info_next:
                if (isInitInfo) {
                    showBirthdayStatus();
                } else {
                    showWeightStatus();
                }
                break;
            case R.id.init_info_last:
                if (isInitInfo) {
                    showGenderStatus();
                } else {
                    showHeightStatus();
                }
                break;
            case R.id.init_info_done:
                if (isInitInfo) {
                    owner.setGender(gender);
                    owner.setBirthday(brightView.getN_year()
                            + "-" + CommonUtils.getZeroStart(brightView.getN_month()) + "-"
                            + CommonUtils.getZeroStart(brightView.getN_day()));
                    toSaveGenderAndBirth();
                } else {
                    owner.setHeight(heightCenterView.getN_height());
                    owner.setWeight(weightCenterView.getN_weight());
                    toSaveHeightAndWeight();
                    finish();
                }
                break;
            case R.id.gender_female_tv:
                if (gender != 1) {
                    gender = 1;
                    notifyGenderChanged();
                }
                break;
            case R.id.gender_male_tv:
                if (gender != 0) {
                    gender = 0;
                    notifyGenderChanged();
                }
                break;
            case R.id.gender_other_tv:
                if (gender != 2) {
                    gender = 2;
                    notifyGenderChanged();
                }
                break;
        }
    }

    private void notifyGenderChanged() {
        if (gender == 0) {
            mFemaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mMaleSelectTv.setBackgroundResource(R.drawable.shape_sex_selected_bg);
            mOtherSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
        } else if (gender == 1) {
            mFemaleSelectTv.setBackgroundResource(R.drawable.shape_sex_selected_bg);
            mMaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mOtherSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
        } else {
            mFemaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mMaleSelectTv.setBackgroundResource(R.drawable.shape_sex_bg);
            mOtherSelectTv.setBackgroundResource(R.drawable.shape_sex_selected_bg);
        }
    }

    private void showGenderStatus() {
        mTitle.setText(setStyle(new SpannableString(getString(R.string.user_info_setting_gender_title))));
        mInfo.setText(R.string.user_info_setting_gender_info);
        mGenderContentView.setVisibility(View.VISIBLE);
        mBirthdayContentView.setVisibility(View.INVISIBLE);
        mHeightContentView.setVisibility(View.INVISIBLE);
        mWeightContentView.setVisibility(View.INVISIBLE);
        mNext.setVisibility(View.VISIBLE);
        mLast.setVisibility(View.GONE);
        mDone.setVisibility(View.GONE);
    }

    private SpannableString setStyle(SpannableString spannableString) {
        int endIndex = spannableString.length();
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_main)), endIndex - 2, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private void showBirthdayStatus() {
        //不选中，提醒
        if (gender == -1) {
            mNext.startAnimation(AnimationUtils.loadAnimation(this, R.anim.shake_anim));
            return;
        }

        mTitle.setText(setStyle(new SpannableString(getString(R.string.user_info_setting_birth_title))));
        mInfo.setText(R.string.user_info_setting_birthday_info);
        mGenderContentView.setVisibility(View.INVISIBLE);
        mBirthdayContentView.setVisibility(View.VISIBLE);
        mHeightContentView.setVisibility(View.INVISIBLE);
        mWeightContentView.setVisibility(View.INVISIBLE);
        mNext.setVisibility(View.GONE);
        mLast.setVisibility(View.VISIBLE);
        mDone.setVisibility(View.VISIBLE);
    }

    private void showHeightStatus() {
        mTitle.setText(setStyle(new SpannableString(getString(R.string.user_info_setting_height_title))));
        mInfo.setText(R.string.user_info_setting_height_info);
        mGenderContentView.setVisibility(View.INVISIBLE);
        mBirthdayContentView.setVisibility(View.INVISIBLE);
        mHeightContentView.setVisibility(View.VISIBLE);
        mWeightContentView.setVisibility(View.INVISIBLE);
        mNext.setVisibility(View.VISIBLE);
        mLast.setVisibility(View.GONE);
        mDone.setVisibility(View.GONE);
    }

    private void showWeightStatus() {
        mTitle.setText(setStyle(new SpannableString(getString(R.string.user_info_setting_weight_title))));
        mInfo.setText(R.string.user_info_setting_weight_info);
        mGenderContentView.setVisibility(View.INVISIBLE);
        mBirthdayContentView.setVisibility(View.INVISIBLE);
        mHeightContentView.setVisibility(View.INVISIBLE);
        mWeightContentView.setVisibility(View.VISIBLE);
        mNext.setVisibility(View.GONE);
        mLast.setVisibility(View.VISIBLE);
        mDone.setVisibility(View.VISIBLE);
    }

    private void toSaveGenderAndBirth() {
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("sex", owner.getGender());
        params.addFormDataPart("birthday", owner.getBirthday());

        HttpRequest.post(HttpHelper.URL_UPDATE_USER_INFO, params,
                new RequestCallBack<String>(InitInfoActivity.this) {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
//                        if(PreferencesUtils.getBoolean(InitInfoActivity.this, CommonArgs.NEW_USER_BENFIT_SHARE,false) && Apputils.systemLanguageIsChinese(InitInfoActivity.this)){
//                            startActivity(new Intent(InitInfoActivity.this,NewUserBenfitActivity.class));
//                            ToTwooApplication.owner.setNew(false);
//                        }else{

                    }

                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {

                    }

                    @Override
                    public void onFailure(int errorCode, String msg) {
                        super.onFailure(errorCode, msg);
                    }
                });

        HomeActivityControl.getInstance().openHomeActivity(InitInfoActivity.this);
        overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);

        ToTwooApplication.owner.setNew(false);

        //
//        if (TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN)) {
        startActivity(new Intent(this, JewelrySelectActivity.class));
//        } else {
//            HomeActivityControl.getInstance().openHomeActivity(InitInfoActivity.this);
//        }
        finish();
    }

    private void toSaveHeightAndWeight() {
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("height", owner.getHeight() + "");
        params.addFormDataPart("weight", owner.getWeight() + "");

        HttpRequest.post(HttpHelper.URL_UPDATE_USER_INFO, params,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
                    }

                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {

                    }
                });
    }

    @Override
    public void onBackPressed() {
        if (!isInitInfo) {
            super.onBackPressed();
        }
    }
}
