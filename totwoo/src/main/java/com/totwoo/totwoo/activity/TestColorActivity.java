package com.totwoo.totwoo.activity;

import android.content.Context;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.IBinder;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.internal.telephony.ITelephony;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.SleepDayViewGroup;
import com.totwoo.totwoo.widget.SleepWeekView;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class TestColorActivity extends BaseActivity {
    @BindView(R.id.color_r_sb)
    AppCompatSeekBar sbr;
    @BindView(R.id.color_r_text)
    TextView rTv;

    private int currentR;

    @BindView(R.id.color_g_sb)
    AppCompatSeekBar sbg;
    @BindView(R.id.color_g_text)
    TextView gTv;

    private int currentG;

    @BindView(R.id.color_b_sb)
    AppCompatSeekBar sbb;
    @BindView(R.id.color_b_text)
    TextView bTv;

    private int currentB;

    @BindView(R.id.color_rgb_text)
    TextView rgbTv;

    @BindView(R.id.color_go)
    TextView go;

    @BindView(R.id.color_r_et)
    EditText color_r_et;
    @BindView(R.id.color_g_et)
    EditText color_g_et;
    @BindView(R.id.color_b_et)
    EditText color_b_et;

    @BindView(R.id.color_add)
    TextView color_add;
    @BindView(R.id.color_rv)
    RecyclerView color_rv;

    @BindView(R.id.sleep_day_state_info_cl)
    ConstraintLayout mDayStateInfoCl;

    private ArrayList<ColorItem> colors = new ArrayList<>();
    private TestColorAdapter testColorAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_test_color);
        ButterKnife.bind(this);
        PermissionUtil.hasCallState(this);
        testColorAdapter = new TestColorAdapter();
        sbr.setMax(255);
        sbr.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                rTv.setText(customHex(progress));
                color_r_et.setText(progress + "");
                currentR = progress;
                notifyRGB();
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
        sbg.setMax(255);
        sbg.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                gTv.setText(customHex(progress));
                color_g_et.setText(progress + "");
                currentG = progress;
                notifyRGB();
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
        sbb.setMax(255);
        sbb.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                bTv.setText(customHex(progress));
                color_b_et.setText(progress + "");
                currentB = progress;
                notifyRGB();
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
        go.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int color = (currentR<<16) + (currentG<<8) + currentB;
                BluetoothManage.getInstance().notifyJewelry(6,color);
            }
        });
        color_r_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    sbr.setProgress(Integer.valueOf(s.toString()));
                    if (!TextUtils.isEmpty(s.toString())) {
                        color_r_et.setSelection(s.length());
                    }
                } catch (NumberFormatException e) {

                }
            }
        });

        color_g_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    sbg.setProgress(Integer.valueOf(s.toString()));
                    if (!TextUtils.isEmpty(s.toString())) {
                        color_g_et.setSelection(s.length());
                    }
                } catch (NumberFormatException e) {

                }
            }
        });

        color_b_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    sbb.setProgress(Integer.valueOf(s.toString()));
                    if (!TextUtils.isEmpty(s.toString())) {
                        color_b_et.setSelection(s.length());
                    }
                } catch (NumberFormatException e) {

                }
            }
        });
        color_add.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                rejectCall();
                String colorRes = rgbTv.getText().toString().trim();
                if(colorRes.length() != 6){
                    ToastUtils.showShort(TestColorActivity.this,"输入的位数不足");
                }else{
                    try {
                        int temp = Integer.parseInt(colorRes,16);
                        colors.add(getItem(colorRes));
                        testColorAdapter.notifyDataSetChanged();
                    } catch (NumberFormatException e) {
                        ToastUtils.showShort(TestColorActivity.this,"输入格式有误");
                    }
                }
            }
        });
        colors.add(getItem("FF37FF"));
        colors.add(getItem("FA8842"));
        colors.add(getItem("E8D6D7"));
        colors.add(getItem("2DD1D2"));
        colors.add(getItem("C81F64"));
        colors.add(getItem("654723"));
        colors.add(getItem("3A35EB"));
        colors.add(getItem("2B71DF"));

        color_rv.setLayoutManager(new GridLayoutManager(TestColorActivity.this,2));
        color_rv.setAdapter(testColorAdapter);

        SleepDayViewGroup linearLayout = new SleepDayViewGroup(TestColorActivity.this);
//        LinearLayout linearLayout = new LinearLayout(TestColorActivity.this);
        ConstraintLayout.LayoutParams dayViewGroupLayout = new ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT);
        linearLayout.setOrientation(LinearLayout.HORIZONTAL);

        LinearLayout.LayoutParams dayViewLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.MATCH_PARENT);

//        SleepDayView sleepDayView = new SleepDayView(TestColorActivity.this);
//        sleepDayView.setViewInfo(0,50);
//        linearLayout.addView(sleepDayView,dayViewLayout);
//
//        SleepDayView sleepDayView1 = new SleepDayView(TestColorActivity.this);
//        sleepDayView1.setViewInfo(1,50);
//        linearLayout.addView(sleepDayView1,dayViewLayout);
//
//        SleepDayView sleepDayView2 = new SleepDayView(TestColorActivity.this);
//        sleepDayView2.setViewInfo(2,50);
//        linearLayout.addView(sleepDayView2,dayViewLayout);
//
//        SleepDayView sleepDayView3 = new SleepDayView(TestColorActivity.this);
//        sleepDayView3.setViewInfo(3,50);
//        linearLayout.addView(sleepDayView3,dayViewLayout);

        LinearLayout.LayoutParams weekViewLayout = new LinearLayout.LayoutParams(CommonUtils.dip2px(TestColorActivity.this,40), LinearLayout.LayoutParams.MATCH_PARENT);
        weekViewLayout.gravity = Gravity.BOTTOM;

        SleepWeekView sleepWeekView = new SleepWeekView(TestColorActivity.this);
        sleepWeekView.setInfo(20,40,80);
        linearLayout.addView(sleepWeekView,weekViewLayout);

        SleepWeekView sleepWeekView1 = new SleepWeekView(TestColorActivity.this);
        sleepWeekView1.setInfo(30,40,80);
        linearLayout.addView(sleepWeekView1,weekViewLayout);

        SleepWeekView sleepWeekView2 = new SleepWeekView(TestColorActivity.this);
        sleepWeekView2.setInfo(0,40,80);
        linearLayout.addView(sleepWeekView2,weekViewLayout);

        SleepWeekView sleepWeekView3 = new SleepWeekView(TestColorActivity.this);
        sleepWeekView3.setInfo(30,0,80);
        linearLayout.addView(sleepWeekView3,weekViewLayout);

        mDayStateInfoCl.addView(linearLayout,dayViewGroupLayout);

        ArrayList<ScanResult> scanResults = (ArrayList<ScanResult>) getWifiList();
        for (ScanResult scanResult : scanResults) {

        }
    }

    public List<ScanResult> getWifiList() {
        WifiManager wifiManager = (WifiManager) ToTwooApplication.baseContext.getSystemService(WIFI_SERVICE);
        List<ScanResult> scanWifiList = wifiManager.getScanResults();
        List<ScanResult> wifiList = new ArrayList<>();
        if (scanWifiList != null && scanWifiList.size() > 0) {
            HashMap<String, Integer> signalStrength = new HashMap<String, Integer>();
            for (int i = 0; i < scanWifiList.size(); i++) {
                ScanResult scanResult = scanWifiList.get(i);
                if (!scanResult.SSID.isEmpty()) {
                    String key = scanResult.SSID + " " + scanResult.capabilities;
                    if (!signalStrength.containsKey(key)) {
                        signalStrength.put(key, i);
                        wifiList.add(scanResult);
                    }
                }
            }
        }
        return wifiList;
    }

    private ColorItem getItem(String res){
        int color = Integer.parseInt(res,16);
        return new ColorItem(res,color,(255<<24) + color);
    }

    private String customHex(int progress){
        if(progress < 0){
            return "00";
        }else if(progress < 15){
            return "0" + Integer.toHexString(progress);
        }else{
            return Integer.toHexString(progress);
        }
    }

    private void notifyRGB(){
        rgbTv.setText(customHex(currentR) + customHex(currentG) + customHex(currentB));
    }

    public class TestColorAdapter extends RecyclerView.Adapter<TestColorAdapter.ViewHolder>{
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = getLayoutInflater().inflate(R.layout.color_text_item,null);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.color_text.setText(colors.get(position).getColorText());
            holder.color_text.setTextColor(colors.get(position).getTextColor());
            holder.color_text.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    BluetoothManage.getInstance().notifyJewelry(6,colors.get(position).getColor());
                }
            });
            holder.color_text.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    colors.remove(position);
                    testColorAdapter.notifyDataSetChanged();
                    return true;
                }
            });
        }

        @Override
        public int getItemCount() {
            return colors.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder{
            @BindView(R.id.color_text)
            TextView color_text;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                ButterKnife.bind(this,itemView);
            }
        }
    }

    private class ColorItem{
        private String colorText;
        private int color;
        private int textColor;

        public ColorItem(String colorText, int color, int textColor) {
            this.colorText = colorText;
            this.color = color;
            this.textColor = textColor;
        }

        public String getColorText() {
            return colorText;
        }

        public void setColorText(String colorText) {
            this.colorText = colorText;
        }

        public int getColor() {
            return color;
        }

        public void setColor(int color) {
            this.color = color;
        }

        public int getTextColor() {
            return textColor;
        }

        public void setTextColor(int textColor) {
            this.textColor = textColor;
        }
    }

    public void rejectCall(){
        LogUtils.e("e = rejectCall");
        try {
            Method method = Class.forName("android.os.ServiceManager")
                    .getMethod("getService", String.class);
            IBinder binder = (IBinder) method.invoke(null, new Object[]{Context.TELEPHONY_SERVICE});
            ITelephony telephony = ITelephony.Stub.asInterface(binder);
            telephony.endCall();
        } catch (NoSuchMethodException e) {
            LogUtils.e("e = " + e);
        } catch (ClassNotFoundException e) {
            LogUtils.e("e = " + e);
        } catch (Exception e) {
            LogUtils.e("e = " + e);
        }

    }

}
