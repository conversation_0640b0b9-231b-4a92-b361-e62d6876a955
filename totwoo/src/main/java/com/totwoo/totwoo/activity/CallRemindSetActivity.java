package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.activity.ContactsActivityForCall.ALLOW_ADD_COUNT;
import static com.totwoo.totwoo.utils.CommonArgs.NOTIFY_CALL_COLOR_TAG;

import android.Manifest;
import android.content.ContentUris;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.provider.Settings;
import android.util.SparseArray;
import android.view.View;
import android.view.Window;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ClickUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.CallRemindContact;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.holderBean.CallSwitch;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.CustomMiddleTextDialog;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnCheckedChanged;
import butterknife.OnClick;
import rx.Observable;

/**
 * Created by huanggaowei on 16/9/2.
 */
public class CallRemindSetActivity extends BaseActivity {
    private static final String HAVE_SHOW_PERMISSION_DIALOG = "have_show_permission_dialog";

    public static String ALL_CALL_REMIND_FLASH_KEY = "all_call_remind_flash_key";

    public static String ALL_CALL_REMIND_MUSIC_TYPE_KEY = "all_call_remind_music_type_key";

    public static String ALL_CALL_REMIND_VIBRATION_SEC_KEY = "all_call_remind_vibration_sec_key";

    public static String ALL_CALL_REMIND_SWITCH_KEY = "all_call_remind_switch_key";

    public static String ALL_CONTACT_REMIND_FLASH_KEY = "all_contact_remind_flash_key";

    public static String ALL_CONTACT_REMIND_MUSIC_TYPE_KEY = "all_contact_remind_music_type_key";

    public static String ALL_CONTACT_REMIND_VIBRATION_SEC_KEY = "all_contact_remind_vibration_sec_key";

    public static String ALL_CONTACT_REMIND_SWITCH_KEY = "all_contact_remind_switch_key";

    public static String IMPORTANT_CONTACT_REMIND_SWITCH_KEY = "important_contact_remind_switch_key";

    //重要联系人数据 一个json串
    public static String IMPORTANT_CONTACT_REMIND_DATA_KEY = "important_contact_remind_data_key";


    public static String NOTIFY_SETTING_INDEX = "important_contact_remind_data_key";
    public static String NOTIFY_SETTING_MUSIC_TYPE = "notify_setting_music_type";


    @BindView(R.id.all_call_ramind_type_iv)
    ImageView mAllCallRamindTypeIv;
    @BindView(R.id.all_notify_cb)
    CheckBox mAllNotifyCb;
    @BindView(R.id.all_call_fl)
    FrameLayout mAllCallFl;
    @BindView(R.id.cantact_call_ramind_type_iv)
    ImageView mCantactCallRamindTypeIv;
    @BindView(R.id.contact_notify_cb)
    CheckBox mContactNotifyCb;
    @BindView(R.id.all_contact_fl)
    FrameLayout mAllContactFl;
    @BindView(R.id.important_notify_cb)
    CheckBox mImportantNotifyCb;
    @BindView(R.id.important_contact_fl)
    LinearLayout mImportantContactFl;
    @BindView(R.id.important_contact_fl_2)
    View important_contact_fl_2;
    @BindView(R.id.call_remind_add_important_contact_only_cl)
    ConstraintLayout mCallRemindAddImportantContactClOnly;
    @BindView(R.id.important_call_ramind_contact_ll)
    LinearLayout mImportantCallRamindContactLl;
    @BindView(R.id.make_card_sample_subtitle)
    TextView mMakeCardSampleSubtitle;
//    @BindView(R.id.important_contact_only_fl)
//    LinearLayout mImportantOnlyFl;

    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    @BindView(R.id.notify_switch_click_item)
    RelativeLayout mNotifySwitchClickItem;
    @BindView(R.id.call_setting_content)
    LinearLayout mCallSettingContent;

    JewelryNotifyModel callSwitchModel;

    public final int allCall = 1111;

    public final int allContact = 2222;

    SparseArray<JewelryNotifyModel> mCallRemindSets = new SparseArray<>();

    boolean importantContactSwitch;
    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.call_switch_info_tv)
    TextView mCallSwitchInfoTv;

    private List<CallRemindContact> mCallRemindContacts;

    private Gson gson = new Gson();

    //最大联系人
    private int maxImportantContact = 3;

    private boolean needSkipContact = false;

//    private CustomDialog colorDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_call_remind_set_temp);
        ButterKnife.bind(this);
        if (BleParams.isButtonBatteryJewelry()) {
            mAllCallFl.setVisibility(View.GONE);
            mAllContactFl.setVisibility(View.GONE);
            mImportantContactFl.setVisibility(View.GONE);
//            mImportantOnlyFl.setVisibility(View.VISIBLE);
        }
        initData();
        if (PermissionUtil.hasPermission(this, new String[]{Manifest.permission.READ_CALL_LOG, Manifest.permission.READ_PHONE_STATE}, PermissionUtil.RESULT_PHONESTATE)) {
            checkXiaomiPermission();
        }

        ClickUtils.expandClickArea(mAllCallRamindTypeIv, 30);
        ClickUtils.expandClickArea(mCantactCallRamindTypeIv, 30);

    }


    private void showColorDialog(String selectColor, String setIndex) {

        // 初始化首饰通知闪光开关
        boolean jewelryGlitterEnabled =  !(BleParams.isCtJewlery() || BleParams.isMWJewlery()) || PreferencesUtils.getBoolean(this, "jewelry_glitter_enabled", true);
        if (!jewelryGlitterEnabled) {
            ToastUtils.showLong(this,R.string.enable_notification_can_flash);
            return;
        }

        CustomDialog colorDialog = new CustomDialog(this, R.style.send_totwoo_bg_dialog);
        View v = View.inflate(this, R.layout.call_color_dialog, null);
        colorDialog.setRootView(v);
        Window window = colorDialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);
        }

        RecyclerView colorLibraryRecyclerView = v.findViewById(R.id.notify_setting_color_library_rv);
        colorLibraryRecyclerView.setHasFixedSize(true);

        int spanCount =  BleParams.isCtJewlery() ? 7 : 6;
        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(this, spanCount));

        CustomColorLibraryAdapter colorLibraryAdapter = new CustomColorLibraryAdapter(selectColor,spanCount, false,false);
        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
        colorLibraryAdapter.setOnItemClickListener((adapter, view, position) -> {
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);
            if (colorLibraryBean != null) {
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                notifyJewelryCustomBQ(colorLibraryBean.getColor());
            }
        });

        v.findViewById(R.id.the_heart_bq_dialog_save).setOnClickListener(v1 -> {
            setColor(setIndex, colorLibraryAdapter.getSelectColor(), 0);
            colorDialog.dismiss();
        });
        colorDialog.show();
    }

    private void notifyJewelryCustomBQ(String color) {
        if (!ToTwooApplication.isDebug && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_LONG);
            return;
        }
        if (BleParams.isCodeJewelry()) {
            BluetoothManage.getInstance().notifyMorseCode(NotifyUtil.LONG_VIBRATION_SEC, NotifyUtil.getColorValue(color));
        } else {
            BluetoothManage.getInstance().notifyJewelry(NotifyUtil.LONG_VIBRATION_SEC, NotifyUtil.getColorValue(color));
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean deny = false;
        for (int grantResult : grantResults) {
            if (grantResult == PackageManager.PERMISSION_DENIED) {
                deny = true;
                break;
            }
        }

        if (!deny) {
            if (requestCode == PermissionUtil.RESULT_CONTACTS) {
                if (needSkipContact) {
                    openContacts();
                    mContactNotifyCb.setChecked(false);
                    mAllNotifyCb.setChecked(false);
                    mImportantNotifyCb.setChecked(true);
                    mImportantCallRamindContactLl.setVisibility(View.VISIBLE);
                } else {
                    mContactNotifyCb.setChecked(true);

                    mAllNotifyCb.setChecked(false);
                    mImportantNotifyCb.setChecked(false);
                    mImportantCallRamindContactLl.setVisibility(View.GONE);
                    important_contact_fl_2.setVisibility(View.GONE);

                }
            } else if (requestCode == PermissionUtil.RESULT_PHONESTATE) {
                checkXiaomiPermission();
            }
        } else {
            PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
        }
    }

    private void checkXiaomiPermission() {
        if (Build.MANUFACTURER.equalsIgnoreCase("Xiaomi") && !PreferencesUtils.getBoolean(this, HAVE_SHOW_PERMISSION_DIALOG, false)) {
            // 小米手机需要引导用户手动开启电话权限.
            final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(this);
            commonMiddleDialog.setCancel(R.string.do_it_later);
            commonMiddleDialog.setSure(R.string.set_hint, v -> {
                startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                PreferencesUtils.put(this, HAVE_SHOW_PERMISSION_DIALOG, true);
                commonMiddleDialog.dismiss();
            });
            commonMiddleDialog.setMessage(R.string.IMEI_request_hint);
            commonMiddleDialog.show();
        }
    }

    private void initData() {

        callSwitchModel = NotifyUtil.getCallNotifyModel(this);

        mCallSwitchCb.setChecked(callSwitchModel.isNotifySwitch());

        mCallSwitchTitleTv.setText(callSwitchModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);

        if (callSwitchModel.isNotifySwitch()) {
            mCallSwitchInfoTv.setVisibility(View.VISIBLE);
        } else {
            mCallSwitchInfoTv.setVisibility(View.GONE);
        }

        if (!callSwitchModel.isNotifySwitch()) {
            mCallSettingContent.setVisibility(View.GONE);
        }

//        if (!Apputils.systemLanguageIsChinese(getBaseContext())) {
//            mMakeCardSampleSubtitle.setVisibility(View.GONE);
//        }
        mCallRemindSets.put(allCall, new JewelryNotifyModel(
                PreferencesUtils.getBoolean(this, ALL_CALL_REMIND_SWITCH_KEY, true),
                PreferencesUtils.getString(this, ALL_CALL_REMIND_FLASH_KEY, "RED"),
                PreferencesUtils.getInt(this, ALL_CALL_REMIND_VIBRATION_SEC_KEY, NotifyUtil.LONG_VIBRATION_SEC)
        ));
        setSelectColor(mAllCallRamindTypeIv, mCallRemindSets.get(allCall).getFlashColor());

        mCallRemindSets.put(allContact, new JewelryNotifyModel(
                PreferencesUtils.getBoolean(this, ALL_CONTACT_REMIND_SWITCH_KEY, false),
                PreferencesUtils.getString(this, ALL_CONTACT_REMIND_FLASH_KEY, "RED"),
                PreferencesUtils.getInt(this, ALL_CONTACT_REMIND_VIBRATION_SEC_KEY, NotifyUtil.LONG_VIBRATION_SEC)
        ));
        setSelectColor(mCantactCallRamindTypeIv, mCallRemindSets.get(allContact).getFlashColor());
        importantContactSwitch = PreferencesUtils.getBoolean(this, IMPORTANT_CONTACT_REMIND_SWITCH_KEY, false);
        mAllNotifyCb.setChecked(mCallRemindSets.get(allCall).isNotifySwitch());
        mContactNotifyCb.setChecked(mCallRemindSets.get(allContact).isNotifySwitch());
        mImportantNotifyCb.setChecked(importantContactSwitch);

        if (!importantContactSwitch) {
            mImportantCallRamindContactLl.setVisibility(View.GONE);
            important_contact_fl_2.setVisibility(View.GONE);
        }

        String dataJson = PreferencesUtils.getString(this, IMPORTANT_CONTACT_REMIND_DATA_KEY, "");
        mCallRemindContacts = gson.fromJson(dataJson, new TypeToken<List<CallRemindContact>>() {
        }.getType());
        if (mCallRemindContacts != null && mCallRemindContacts.size() == 3) {
            important_contact_fl_2.setVisibility(View.GONE);
            mCallRemindAddImportantContactClOnly.setVisibility(View.GONE);
        }
        setImportantContactData();
    }

    private void setImportantContactData() {
        if (mCallRemindContacts != null) {
            Observable.from(mCallRemindContacts)
                    .subscribe(callRemindContact -> {
                        final View contactItem = View.inflate(CallRemindSetActivity.this, R.layout.call_ramind_important_contact, null);
                        final ViewHolder viewHolder = new ViewHolder(contactItem);
//                        if (callRemindContact.getHeadIcon() > 0) {
//                            viewHolder.contact_head_icon_iv.setImageBitmap(getPhotoById(callRemindContact.getHeadIcon()));
//                        } else {
                        viewHolder.contact_head_icon_iv.setImageResource(R.drawable.default_head_yellow);
//                        }
                        viewHolder.cantact_name_tv.setText(callRemindContact.getName());
                        setSelectColor(viewHolder.cantact_ramind_type_tv, callRemindContact.getFlashColor());

                        // 删除重要联系人
                        viewHolder.call_remind_contact_delete_iv.setOnClickListener(v -> {
                            mImportantCallRamindContactLl.removeView(contactItem);
                            CallRemindSetActivity.this.mCallRemindContacts.remove(callRemindContact);
                            important_contact_fl_2.setVisibility(View.VISIBLE);
                            mCallRemindAddImportantContactClOnly.setVisibility(View.VISIBLE);
                            PreferencesUtils.put(getBaseContext(), IMPORTANT_CONTACT_REMIND_DATA_KEY, gson.toJson(mCallRemindContacts));
                        });
                        contactItem.setOnClickListener(v -> {
                            showColorDialog(callRemindContact.getFlashColor(),callRemindContact.getPhoneNumber());

//                            Intent intent = new Intent(CallRemindSetActivity.this, NotifyCallActivity.class);
//                            intent.putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_CALL);
//                            intent.putExtra(NOTIFY_CALL_COLOR_TAG, callRemindContact.getFlashColor());
//                            intent.putExtra(NOTIFY_SETTING_MUSIC_TYPE, callRemindContact.getMusic_type());
//                            intent.putExtra(NOTIFY_SETTING_INDEX, callRemindContact.getPhoneNumber());
//                            startActivityForResult(intent, 1);
                        });
                        mImportantCallRamindContactLl.addView(contactItem);
                    });
        }
    }


    public class ViewHolder {
        @BindView(R.id.contact_head_icon_iv)
        ImageView contact_head_icon_iv;
        @BindView(R.id.cantact_name_tv)
        TextView cantact_name_tv;
        @BindView(R.id.cantact_ramind_type_tv)
        ImageView cantact_ramind_type_tv;
        @BindView(R.id.call_remind_contact_delete_iv)
        ImageView call_remind_contact_delete_iv;

        ViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }

    CustomMiddleTextDialog customMiddleTextDialog;

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(getString(R.string.home_call_holder_title));
        setTopLeftOnclik(v -> notifyAndFinish());
        if (Apputils.systemLanguageIsChinese(CallRemindSetActivity.this)) {
            setTopRightString("不提醒?");
            setTopRightOnClick(v -> customMiddleTextDialog.show());
            customMiddleTextDialog = new CustomMiddleTextDialog(this);
            customMiddleTextDialog.setTitleTv("请依次检查确认", true);
            customMiddleTextDialog.setInfoText("1.兔兔和APP当前是已连接状态\n" +
                    "\n" +
                    "2.来电号码属于已设置的“需要提醒的联系人”范围内，且添加的联系人姓名中不能有表情符号\n" +
                    "\n" +
                    "3.手机来电时，totwoo APP已打开并且保持在后台运行\n" +
                    "\n" +
                    "4.以上都没问题，请尝试重新选择设置来电提醒联系人");
            customMiddleTextDialog.setConfirmTv("我知道了", v -> customMiddleTextDialog.dismiss());
            customMiddleTextDialog.setTextAdd("还是不提醒怎么办？>", v -> {
                WebViewActivity.loadUrl(CallRemindSetActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_HELP), false);
                customMiddleTextDialog.dismiss();
            });
        }
        super.initTopBar();
    }

    @OnCheckedChanged({R.id.all_notify_cb, R.id.contact_notify_cb, R.id.important_notify_cb})
    public void onCheckedChange(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.all_notify_cb:
                PreferencesUtils.put(this, ALL_CALL_REMIND_SWITCH_KEY, isChecked);
                break;
            case R.id.contact_notify_cb:
                PreferencesUtils.put(this, ALL_CONTACT_REMIND_SWITCH_KEY, isChecked);
                break;
            case R.id.important_notify_cb:
                PreferencesUtils.put(this, IMPORTANT_CONTACT_REMIND_SWITCH_KEY, isChecked);
                break;
        }
    }

    @OnClick({R.id.all_call_fl, R.id.all_contact_fl, R.id.important_contact_fl, R.id.cantact_call_ramind_type_iv,
            R.id.all_call_ramind_type_iv, R.id.important_contact_fl_2, R.id.notify_switch_click_item,
            R.id.call_remind_add_important_contact_only_cl})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.all_call_fl:
                mAllNotifyCb.setChecked(true);

                mContactNotifyCb.setChecked(false);
                mImportantNotifyCb.setChecked(false);
                mImportantCallRamindContactLl.setVisibility(View.GONE);
                important_contact_fl_2.setVisibility(View.GONE);

                break;
            case R.id.all_call_ramind_type_iv:
                showColorDialog(mCallRemindSets.get(allCall).getFlashColor(), allCall + "");
//                Intent intent = new Intent(CallRemindSetActivity.this, NotifyCallActivity.class);
//                intent.putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_CALL);
//                intent.putExtra(NOTIFY_CALL_COLOR_TAG, mCallRemindSets.get(allCall).getFlashColor());
//                intent.putExtra(NOTIFY_SETTING_MUSIC_TYPE, PreferencesUtils.getInt(CallRemindSetActivity.this, ALL_CALL_REMIND_MUSIC_TYPE_KEY, 0));
//                intent.putExtra(NOTIFY_SETTING_INDEX, allCall + "");
//                startActivityForResult(intent, 1);

                break;

            case R.id.all_contact_fl:
                isContacts = false;
                if (!PermissionUtil.hasContactsPermission(this)) {
                    needSkipContact = false;
                    return;
                }
                if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_PHONE_ADDRESS_CLICK);
                } else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_PHONE_ADDRESS_CLICK);
                }
                mContactNotifyCb.setChecked(true);

                mAllNotifyCb.setChecked(false);
                mImportantNotifyCb.setChecked(false);
                mImportantCallRamindContactLl.setVisibility(View.GONE);
                important_contact_fl_2.setVisibility(View.GONE);

                break;
            case R.id.cantact_call_ramind_type_iv:
//            case R.id.cantact_call_ramind_type_iv_:
                showColorDialog(mCallRemindSets.get(allContact).getFlashColor(), allContact + "");
//                intent = new Intent(CallRemindSetActivity.this, NotifyCallActivity.class);
//                intent.putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_CALL);
//                intent.putExtra(NOTIFY_CALL_COLOR_TAG, mCallRemindSets.get(allContact).getFlashColor());
//                intent.putExtra(NOTIFY_SETTING_MUSIC_TYPE, PreferencesUtils.getInt(CallRemindSetActivity.this, ALL_CONTACT_REMIND_MUSIC_TYPE_KEY, 0));
//                intent.putExtra(NOTIFY_SETTING_INDEX, allContact + "");
//                startActivityForResult(intent, 1);
                break;

            case R.id.important_contact_fl:
                isContacts = false;
                if (!PermissionUtil.hasContactsPermission(this)) {
                    needSkipContact = true;
                    return;
                }
                if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_PHONE_THREE_CLICK);
                } else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_PHONE_THREE_CLICK);
                }
                mImportantNotifyCb.setChecked(true);

                mAllNotifyCb.setChecked(false);
                mContactNotifyCb.setChecked(false);
                if (mCallRemindContacts == null || mCallRemindContacts.size() < 3) {
                    important_contact_fl_2.setVisibility(View.VISIBLE);
                    mCallRemindAddImportantContactClOnly.setVisibility(View.VISIBLE);
                }
                mImportantCallRamindContactLl.setVisibility(View.VISIBLE);

                break;
            case R.id.important_contact_fl_2:
            case R.id.call_remind_add_important_contact_only_cl:
//                if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.ALLOW_CONTACT_GET, false)) {
//                    showAllowContactsDialog();
//                } else {
                    openContacts();
//                }
                break;

            case R.id.notify_switch_click_item:
                callSwitchModel.setNotifySwitch(!callSwitchModel.isNotifySwitch());
                mCallSwitchCb.setChecked(!mCallSwitchCb.isChecked());
                if (mCallSwitchCb.isChecked()) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_CALL_TURN_ON);
                }

                NotifyUtil.setCallNotify(this, callSwitchModel);
                mCallSwitchTitleTv.setText(callSwitchModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
                if (callSwitchModel.isNotifySwitch()) {
                    mCallSwitchInfoTv.setVisibility(View.VISIBLE);
                } else {
                    mCallSwitchInfoTv.setVisibility(View.GONE);
                }

                Animation anim = AnimationUtils.loadAnimation(this, callSwitchModel.isNotifySwitch() ? R.anim.layout_open : R.anim.layout_close);
                if (callSwitchModel.isNotifySwitch()) {
                    mCallSettingContent.setVisibility(View.VISIBLE);
                } else {
                    anim.setAnimationListener(new Animation.AnimationListener() {
                        @Override
                        public void onAnimationStart(Animation animation) {
                        }

                        @Override
                        public void onAnimationEnd(Animation animation) {
                            mCallSettingContent.setVisibility(View.GONE);
                        }

                        @Override
                        public void onAnimationRepeat(Animation animation) {

                        }
                    });
                }
                mCallSettingContent.startAnimation(anim);

                break;
        }
    }

    private void showAllowContactsDialog() {
        CommonMiddleDialog allowDialog = new CommonMiddleDialog(CallRemindSetActivity.this);
        allowDialog.setTitle(R.string.allow_dialog_title);
        allowDialog.setInfo(R.string.allow_dialog_info);
        allowDialog.setCancel(R.string.allow_dialog_deny);
        allowDialog.setSure(R.string.allow_dialog_allow, v -> {
            PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.ALLOW_CONTACT_GET, true);
            openContacts();
            allowDialog.dismiss();
        });
        allowDialog.show();
    }

    private boolean isContacts = false;

    private void openContacts() {
        isContacts = true;
        if (!PermissionUtil.hasContactsPermission(this)) {
            return;
        }
        if (mCallRemindContacts != null && mCallRemindContacts.size() >= maxImportantContact) {
            ToastUtils.show(getBaseContext(), getString(R.string.important_contact_exceed), Toast.LENGTH_LONG);
            return;
        }

        Intent intent = new Intent(getBaseContext(), ContactsActivityForCall.class);
        if (mCallRemindContacts != null) {
            intent.putExtra(ALLOW_ADD_COUNT, maxImportantContact - mCallRemindContacts.size());
        } else {
            intent.putExtra(ALLOW_ADD_COUNT, maxImportantContact);
        }
        startActivityForResult(intent, 0);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) {
            return;
        }

        switch (requestCode) {
            case 0:
                ArrayList<CallRemindContact> contacts = data.getParcelableArrayListExtra("contact");
                if (mCallRemindContacts == null) {
                    mCallRemindContacts = new ArrayList<>();
                }
                mCallRemindContacts.addAll(contacts);
                mImportantCallRamindContactLl.removeAllViews();

                setImportantContactData();

                if (mCallRemindContacts.size() == 3) {
                    important_contact_fl_2.setVisibility(View.GONE);
                    mCallRemindAddImportantContactClOnly.setVisibility(View.GONE);
                }
                PreferencesUtils.put(this, IMPORTANT_CONTACT_REMIND_DATA_KEY, gson.toJson(mCallRemindContacts));
                break;
            case 1:
                String index = data.getStringExtra(NOTIFY_SETTING_INDEX);
                String color = data.getStringExtra(NOTIFY_CALL_COLOR_TAG);
                int music_type = data.getIntExtra(NOTIFY_SETTING_MUSIC_TYPE, 0);
                if (index == null || color == null) {
                    return;
                }

                setColor(index, color, music_type);

                break;
        }
    }

    /**
     * @param index
     * @param color
     * @param music_type 无用参
     */
    private void setColor(String index, String color, int music_type) {
        if (index.equals(allCall + "")) {
            setSelectColor(mAllCallRamindTypeIv, color);
            mCallRemindSets.get(allCall).setFlashColor(color);
            PreferencesUtils.put(getBaseContext(), ALL_CALL_REMIND_FLASH_KEY, color);
            PreferencesUtils.put(getBaseContext(), ALL_CALL_REMIND_MUSIC_TYPE_KEY, music_type);

        } else if (index.equals(allContact + "")) {
            setSelectColor(mCantactCallRamindTypeIv, color);
            mCallRemindSets.get(allContact).setFlashColor(color);
            PreferencesUtils.put(getBaseContext(), ALL_CONTACT_REMIND_FLASH_KEY, color);
            PreferencesUtils.put(getBaseContext(), ALL_CONTACT_REMIND_MUSIC_TYPE_KEY, music_type);

        } else if (mCallRemindContacts != null) {
            for (int i = 0; i < mCallRemindContacts.size(); i++) {
                CallRemindContact can = mCallRemindContacts.get(i);
                if (can.getPhoneNumber().equals(index)) {
                    can.setFlashColor(color);
                    can.setMusic_type(music_type);
                    try {
                        setSelectColor(mImportantCallRamindContactLl.getChildAt(i).findViewById(R.id.cantact_ramind_type_tv), color);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                PreferencesUtils.put(getBaseContext(), IMPORTANT_CONTACT_REMIND_DATA_KEY, gson.toJson(mCallRemindContacts));
            }
        }
    }

    private void setSelectColor(ImageView imageView, String colorName) {
        int resId = NotifyUtil.getColorImageResId(colorName);
        imageView.setImageResource(resId);
        if (resId == R.drawable.custom_color_normal) {
            imageView.setColorFilter(Color.parseColor(NotifyUtil.getDisplayColorByColorName(colorName)));
        }else {
            imageView.setColorFilter(null);
        }
    }

    private Bitmap getPhotoById(long contactid) {

        Uri uri = ContentUris.withAppendedId(
                ContactsContract.Contacts.CONTENT_URI, contactid);
        InputStream input = ContactsContract.Contacts
                .openContactPhotoInputStream(getContentResolver(),
                        uri);
        return BitmapFactory.decodeStream(input);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        notifyAndFinish();
    }

    private void notifyAndFinish() {
        finish();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing()) {
            EventBus.getDefault().post(new CallSwitch(callSwitchModel.isNotifySwitch()));
        }
    }

}
