package com.totwoo.totwoo.activity;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ConstellationDataModel;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.totwoo.totwoo.widget.RoundImageView;
import com.umeng.analytics.MobclickAgent;

import java.util.Calendar;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 星座运势的分享页面
 *
 * <AUTHOR>
 * @date 2016年1月14日
 */
public class ShareConstellationActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.share_con_head_layout_image)
    ImageView mShareConHeadLayoutImage;
    @BindView(R.id.share_con_head_icon)
    RoundImageView mShareConHeadIcon;
    @BindView(R.id.share_con_nick_name_tv)
    TextView mShareConNickNameTv;
    @BindView(R.id.share_con_date_tv)
    TextView mShareConDateTv;
    @BindView(R.id.share_con_name_tv)
    TextView mShareConNameTv;
    @BindView(R.id.share_con_index_ratingbar)
    RatingBar mShareConIndexRatingbar;
    @BindView(R.id.share_con_mood_title_tv)
    TextView mShareConMoodTitleTv;
    @BindView(R.id.share_con_mood_tv)
    TextView mShareConMoodTv;
    @BindView(R.id.con_love_lucky_index_tv)
    TextView mConLoveLuckyIndexTv;
    @BindView(R.id.con_love_lucky_index_ratingbar)
    RatingBar mConLoveLuckyIndexRatingbar;
    @BindView(R.id.con_lucky_color_tv)
    TextView mConLuckyColorTv;
    @BindView(R.id.con_lucky_color_value_tv)
    TextView mConLuckyColorValueTv;
    @BindView(R.id.con_wark_lucky_index_tv)
    TextView mConWarkLuckyIndexTv;
    @BindView(R.id.con_wark_lucky_index_ratingbar)
    RatingBar mConWarkLuckyIndexRatingbar;
    @BindView(R.id.con_lucky_number_tv)
    TextView mConLuckyNumberTv;
    @BindView(R.id.con_lucky_number_value_tv)
    TextView mConLuckyNumberValueTv;
    @BindView(R.id.con_money_lucky_index_tv)
    TextView mConMoneyLuckyIndexTv;
    @BindView(R.id.con_money_lucky_index_ratingbar)
    RatingBar mConMoneyLuckyIndexRatingbar;
    @BindView(R.id.con_offirend_tv)
    TextView mConOffirendTv;
    @BindView(R.id.con_offirend_value_tv)
    TextView mConOffirendValueTv;
    @BindView(R.id.share_con_text_data_layout)
    ViewGroup mShareConTextDataLayout;
    @BindView(R.id.share_con_bottom_image)
    ImageView bottomImg;

    /**
     * 星座数据
     */
    ConstellationDataModel consData;

    /**
     * 当前数据类型, 从 0 开始依次为今天, 明天, 周, 月, 年
     */
    private int conType;

    /**
     * 星座名字
     */
    private String conName;

    private FacebookCallback<Sharer.Result> facebookCallback;

    private NewUserGiftDialog newUserGiftDialog;

    private String tt;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_share_constellation);
        ButterKnife.bind(this);

        InjectUtils.injectOnlyEvent(this);

        try {
            consData = (ConstellationDataModel) getIntent().getSerializableExtra(ConstellationActivity.TAG_CONSTELLATION_DATA);
            conType = getIntent().getIntExtra(ConstellationActivity.TAG_CONSTELLATION_DATA_TYPE, 0);
            conName = getIntent().getStringExtra(ConstellationActivity.TAG_CONSTELLATION_NAME);
        } catch (Exception e) {
            e.printStackTrace();
        }

        switch (conType) {
            case 0:
                tt = getString(R.string.todays);
                break;
            case 1:
                tt = getString(R.string.tomorrows);
                break;
            case 2:
                tt = getString(R.string.weeks);
                break;
            case 3:
                tt = getString(R.string.months);
                break;
        }

        initShare();

        showData();
    }

    private void initShare(){
        facebookCallback = new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                ToastUtils.showShort(ShareConstellationActivity.this,getResources().getString(R.string.share_complete));
            }

            @Override
            public void onCancel() {
                ToastUtils.showShort(ShareConstellationActivity.this,getResources().getString(R.string.share_cancel));
            }

            @Override
            public void onError(FacebookException error) {
                ToastUtils.showShort(ShareConstellationActivity.this,getResources().getString(R.string.share_error));
            }
        };

        if(Apputils.systemLanguageIsChinese(ShareConstellationActivity.this)){
            findViewById(R.id.common_share_title_tv).setVisibility(View.VISIBLE);
            ((TextView)findViewById(R.id.common_share_title_tv)).setText(CommonUtils.setNumberGoldenSpan(getResources().getString(R.string.share_text_head_info),88,16));
        }else{
            findViewById(R.id.common_share_title_tv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_facebook_iv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_twitter_iv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_qq_iv).setVisibility(View.GONE);
            findViewById(R.id.common_share_qzone_iv).setVisibility(View.GONE);
            findViewById(R.id.common_share_weibo_iv).setVisibility(View.GONE);
        }

        newUserGiftDialog = new NewUserGiftDialog(ShareConstellationActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(getIntent().getIntExtra(CommonArgs.FROM_TYPE,1) == 1){
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_HEROSCOPE_LUCKY_CLICK);
                }else{
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_HEROSCOPE_LUCKY_CLICK);
                }
                WebViewActivity.loadUrl(ShareConstellationActivity.this, HttpHelper.URL_GIFT, false);
                newUserGiftDialog.dismiss();
            }
        }, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                newUserGiftDialog.dismiss();
            }
        },CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券",88,20),"立即抽奖");

        findViewById(R.id.common_share_friend_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath());
            }
        });
        findViewById(R.id.common_share_wechat_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWechat(getPath());
            }
        });

        findViewById(R.id.common_share_qq_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToQQ(getPath());
            }
        });
        findViewById(R.id.common_share_qzone_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToQzone(getPath(),getString(R.string.share_con_title, tt).replace(":", ""));
            }
        });

        findViewById(R.id.common_share_weibo_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWeibo(ShareConstellationActivity.this,getPath(),getString(R.string.share_con_title, tt).replace(":", ""));
            }
        });
        findViewById(R.id.common_share_facebook_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToFacebook(getPath(),ShareConstellationActivity.this,facebookCallback);
            }
        });
        findViewById(R.id.common_share_twitter_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToTwitter(getPath(),getString(R.string.share_con_title, tt).replace(":", ""));
            }
        });
    }

    private String getPath(){
        Bitmap snapShareBitmap = ShareUtilsSingleton.getBitmapByView(findViewById(R.id.share_con_content_layout));
        return FileUtils.saveBitmapFromSDCard(snapShareBitmap,
                "totwoo_cache_img_" + System.currentTimeMillis());
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
    }

    /**
     * 进行数据展示
     */
    private void showData() {

        // 用户数据
        BitmapHelper.display(this, mShareConHeadIcon,
                ToTwooApplication.owner.getHeaderUrl());

        mShareConNameTv.setText(conName.toUpperCase());
        mShareConNickNameTv.setText(ToTwooApplication.owner.getNickName());

        try {
            // 对于周, 月, 年的数据隐藏幸运颜色, 幸运数字, 速配星座相关条目
            if (conType > 1) {
                mConLuckyColorTv.setVisibility(View.GONE);
                mConLuckyColorValueTv.setVisibility(View.GONE);
                mConLuckyNumberTv.setVisibility(View.GONE);
                mConLuckyNumberValueTv.setVisibility(View.GONE);
                mConOffirendTv.setVisibility(View.GONE);
                mConOffirendValueTv.setVisibility(View.GONE);
            }

            Calendar cal = Calendar.getInstance();
            // 左上角日期
            switch (conType) {
                case 0:
                    mShareConDateTv.setText(formatDateString(cal));
                    break;
                case 1:
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                    mShareConDateTv.setText(formatDateString(cal));
                    break;
                case 2:
                    cal.add(Calendar.DAY_OF_MONTH, -(cal.get(Calendar.DAY_OF_WEEK) - 1));
                    String first = formatDateString(cal);
                    cal.add(Calendar.DAY_OF_MONTH, 6);
                    mShareConDateTv.setText(first + " - " + formatDateString(cal));
                    break;
                case 3:
                    mShareConDateTv.setText(formatDateStringMonth(cal));
                    break;
            }


            if (consData != null) {
                // 主体框内内容
                mShareConIndexRatingbar
                        .setRating(Integer.parseInt(consData.getAll()));

                if (Apputils.systemLanguageIsChinese(this)) {
                    mShareConMoodTv.setText(consData.getSummary());
                } else {
                    mShareConMoodTv.setText(consData.getSummary_en());
                }

                // 今日运势
                mConLoveLuckyIndexRatingbar.setRating(Integer.parseInt(consData.getLove()));
                mConWarkLuckyIndexRatingbar.setRating(Integer.parseInt(consData.getCause()));

                mConLuckyNumberValueTv
                        .setText(consData.getLuck_number());
                mConMoneyLuckyIndexRatingbar
                        .setRating(Integer.parseInt(consData.getMoney()));

                if (Apputils.systemLanguageIsChinese(this)) {
                    mConOffirendValueTv
                            .setText(consData.getMatch_friend());
                } else {
                    mConOffirendValueTv
                            .setText(consData.getMatch_friend_en().toUpperCase());
                }

                if (!Apputils.systemLanguageIsChinese(this)) {
                    mConLuckyColorTv.setVisibility(View.GONE);
                    mConLuckyColorValueTv.setVisibility(View.GONE);
                } else {
                    mConLuckyColorValueTv
                            .setText(consData.getLuck_color());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        // 对于周, 月, 年的数据隐藏幸运颜色, 幸运数字, 速配星座相关条目
        if (conType > 1) {
            findViewById(R.id.con_lucky_color_tv).setVisibility(View.GONE);
            findViewById(R.id.con_lucky_color_value_tv).setVisibility(
                    View.INVISIBLE);
            findViewById(R.id.con_lucky_number_tv)
                    .setVisibility(View.GONE);
            findViewById(R.id.con_lucky_number_value_tv).setVisibility(
                    View.INVISIBLE);
            findViewById(R.id.con_offirend_tv).setVisibility(View.GONE);
            findViewById(R.id.con_offirend_value_tv).setVisibility(
                    View.INVISIBLE);
        }


        ((TextView) findViewById(R.id.share_con_mood_title_tv))
                .setText(getString(R.string.share_con_title, tt));

        // 底部分享图片
        if (!Apputils.systemLanguageIsChinese(this)) {
            bottomImg.setImageResource(R.drawable.share_bottom_img_en);
        }
    }

    /**
     * 格式化 到指定的 日期格式
     */
    private String formatDateString(Calendar cal)
    {
        return getResources().getStringArray(R.array.month_names_en)[cal
                .get(Calendar.MONTH)]
                + " "
                + String.format("%02d", cal.get(Calendar.DAY_OF_MONTH));
    }

    private String formatDateStringMonth(Calendar cal)
    {
        return getResources().getStringArray(R.array.month_names_en)[cal.get(Calendar.MONTH)];
    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        newUserGiftDialog.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
