package com.totwoo.totwoo.activity;

import android.app.Service;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Vibrator;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.exception.DbException;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomOrderAdapter;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CustomOrderDbHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.DividerItemDecoration;
import com.totwoo.totwoo.widget.FullyLinearLayoutManager;
import com.totwoo.totwoo.widget.OnRecyclerItemClickListener;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.Collections;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by totwoo on 2018/9/17.
 */

@Deprecated
public class CustomOrderActivity extends BaseActivity {
    private ArrayList<CustomOrderBean> selectBeans;
    private ArrayList<CustomOrderBean> unSelectBeans;
    private ItemTouchHelper mItemTouchHelper;
    private CustomOrderAdapter customOrderSelectAdapter;
    private CustomOrderAdapter customOrderUnSelectAdapter;

    private int from_type;
    private boolean isChanged = false;

    @BindView(R.id.custom_order_selected_rv)
    RecyclerView mRecyclerView;
    @BindView(R.id.custom_order_unselected_rv)
    RecyclerView mUnSelectedRecyclerView;
    @BindView(R.id.custom_order_unselected_line)
    View unSelectedLine;
    @BindView(R.id.custom_order_unselected_title)
    RelativeLayout unSelectedTitle;
    @BindView(R.id.custom_order_hint_tv)
    TextView custom_order_hint_tv;

//    private OrderHintDialog orderHintDialog;

    private boolean needHideGift;

//    private static final String HAS_CUSTOM_ORDER_DIALOG_SHOW = "has_custom_order_dialog_show";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_custom_order);
        ButterKnife.bind(this);
        from_type = getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1);
        needHideGift = getIntent().getBooleanExtra(CommonArgs.NEED_HIDE_GIFT, false);
        initData();
        initView();
        checkUnSelectShow();
        setSpinState(false);

//        if (!PreferencesUtils.getBoolean(CustomOrderActivity.this, HAS_CUSTOM_ORDER_DIALOG_SHOW, false)) {
//            orderHintDialog = new OrderHintDialog(CustomOrderActivity.this, v -> orderHintDialog.dismiss());
//            orderHintDialog.show();
//            PreferencesUtils.put(CustomOrderActivity.this, HAS_CUSTOM_ORDER_DIALOG_SHOW, true);
//        }
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> notifyAndFinish());
        setTopTitle(R.string.custom_order_title);
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopRightString(R.string.chat_save);
        setTopRightOnClick(v -> save());
        //空click。没有加上的时候，点击顶部，会有影响列表颜色。
        getTopBar().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }

    @Override
    public void onBackPressed() {
        notifyAndFinish();
    }

    private void notifyAndFinish() {
        if (isChanged) {
            showNotSaveDialog();
        } else {
            finish();
        }
    }

    private void showNotSaveDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(CustomOrderActivity.this);
        commonMiddleDialog.setMessage(R.string.custom_order_tips);
        commonMiddleDialog.setSure(v -> finish());
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    private void save() {
        CustomOrderDbHelper.getInstance().deleteType(from_type);
        ArrayList<CustomOrderBean> saveBeans = new ArrayList<>();
        for (int i = 0; i < selectBeans.size(); i++) {
            CustomOrderBean bean = new CustomOrderBean(i, selectBeans.get(i).getType(), from_type);
            saveBeans.add(bean);
        }
        if (!BleParams.isPendant(PreferencesUtils.getString(CustomOrderActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))) {
            CustomOrderBean bean = new CustomOrderBean(selectBeans.size(), NotifyUtil.CAMERA_TYPE, from_type);
            saveBeans.add(bean);
        }
        boolean isSingle = PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.LOVE_STATUS_SINGLE, false);
        String jewName = PreferencesUtils.getString(CustomOrderActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (BleParams.isMemoryJewelry()) {
            CustomOrderBean bean = new CustomOrderBean(saveBeans.size(), NotifyUtil.MEMORY_TYPE, from_type);
            saveBeans.add(bean);
            CustomOrderBean bean1 = new CustomOrderBean(saveBeans.size(), NotifyUtil.SLEEP_TYPE, from_type);
            saveBeans.add(bean1);
        } else if (BleParams.isCodeBangle()) {
            CustomOrderBean bean = new CustomOrderBean(saveBeans.size(), NotifyUtil.MEMORY_TYPE, from_type);
            saveBeans.add(bean);
            CustomOrderBean bean1 = new CustomOrderBean(saveBeans.size(), NotifyUtil.CAMERA_TYPE, from_type);
            saveBeans.add(bean1);
        } else if (BleParams.isCodeJewelry()) {
            CustomOrderBean bean = new CustomOrderBean(saveBeans.size(), NotifyUtil.MEMORY_TYPE, from_type);
            saveBeans.add(bean);
            CustomOrderBean bean1 = new CustomOrderBean(saveBeans.size(), NotifyUtil.SLEEP_TYPE, from_type);
            saveBeans.add(bean1);
        } else if (BleParams.isLoveLetter()) {
            CustomOrderBean bean = new CustomOrderBean(saveBeans.size(), NotifyUtil.MEMORY_TYPE, from_type);
            saveBeans.add(bean);
            CustomOrderBean bean1 = new CustomOrderBean(saveBeans.size(), NotifyUtil.SLEEP_TYPE, from_type);
            saveBeans.add(bean1);
            CustomOrderBean bean2 = new CustomOrderBean(saveBeans.size(), NotifyUtil.CAMERA_TYPE, from_type);
            saveBeans.add(bean2);
        } else if (TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_SC) || TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_WL)) {
            CustomOrderBean bean = new CustomOrderBean(saveBeans.size(), NotifyUtil.FORTUNE_TYPE, from_type);
            saveBeans.add(bean);
            CustomOrderBean bean1 = new CustomOrderBean(saveBeans.size(), NotifyUtil.QIAN_TYPE, from_type);
            saveBeans.add(bean1);
            CustomOrderBean bean2 = new CustomOrderBean(saveBeans.size(), NotifyUtil.MEMORY_TYPE, from_type);
            saveBeans.add(bean2);
            CustomOrderBean bean3 = new CustomOrderBean(saveBeans.size(), NotifyUtil.SLEEP_TYPE, from_type);
            saveBeans.add(bean3);
        } else if (isSingle) {
            CustomOrderBean bean = new CustomOrderBean(saveBeans.size(), NotifyUtil.FORTUNE_TYPE, from_type);
            saveBeans.add(bean);
            CustomOrderBean bean1 = new CustomOrderBean(saveBeans.size(), NotifyUtil.QIAN_TYPE, from_type);
            saveBeans.add(bean1);
            CustomOrderBean bean2 = new CustomOrderBean(saveBeans.size(), NotifyUtil.SLEEP_TYPE, from_type);
            saveBeans.add(bean2);
        }
        if (ToTwooApplication.owner.getGender() == 0) {
            CustomOrderBean bean = new CustomOrderBean(saveBeans.size(), NotifyUtil.PERIOD_TYPE, from_type);
            saveBeans.add(bean);
        }

        if (unSelectBeans != null && unSelectBeans.size() > 0) {
            for (int i = 0; i < unSelectBeans.size(); i++) {
                CustomOrderBean bean = new CustomOrderBean(-1, unSelectBeans.get(i).getType(), from_type);
                saveBeans.add(bean);
            }
        }
        CustomOrderDbHelper.getInstance().addAllBeans(saveBeans);
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ORDER_UPDATE, null);
        finish();
    }

    private void initData() {
        selectBeans = new ArrayList<>();
        unSelectBeans = new ArrayList<>();

        try {
            if (BleParams.isMemoryJewelry()) {
                selectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(from_type, 2);
                unSelectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getUnSelect(from_type, 2);
            } else if (BleParams.isWishJewlery()) {
                selectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(from_type, 3);
                unSelectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getUnSelect(from_type, 3);
            } else if (BleParams.isCodeBangle()) {
                selectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(from_type, 5);
                unSelectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getUnSelect(from_type, 5);
            } else if (BleParams.isCodeJewelry()) {
                selectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(from_type, 4);
                unSelectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getUnSelect(from_type, 4);
            } else if (BleParams.isLoveLetter()) {
                selectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(from_type, 6);
                unSelectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getUnSelect(from_type, 6);
            } else {
                selectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(from_type, BleParams.isPendant(PreferencesUtils.getString(CustomOrderActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "")) ? 0 : 1);
                unSelectBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getUnSelect(from_type, BleParams.isPendant(PreferencesUtils.getString(CustomOrderActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "")) ? 0 : 1);
            }

        } catch (DbException e) {
            e.printStackTrace();
        }

        if (needHideGift) {
            // REMINDER 页面的情书已经上移到顶部 banner 位置, 需要移除.
            for (CustomOrderBean bean : selectBeans) {
                if (bean.getType() == NotifyUtil.SERCET_TYPE) {
                    selectBeans.remove(bean);
                    break;
                }
            }

            for (CustomOrderBean bean : unSelectBeans) {
                if (bean.getType() == NotifyUtil.SERCET_TYPE) {
                    unSelectBeans.remove(bean);
                    break;
                }
            }
        }

        customOrderSelectAdapter = new CustomOrderAdapter(selectBeans, position -> {

            if (selectBeans != null && selectBeans.size() > 2) {
                isChanged = true;
                CustomOrderBean bean = selectBeans.get(position);
                bean.setSelect(false);
                unSelectBeans.add(bean);
                selectBeans.remove(position);

                customOrderSelectAdapter.notifyDataSetChanged();
                customOrderUnSelectAdapter.notifyDataSetChanged();
                checkUnSelectShow();
            } else {
                ToastUtils.showShort(CustomOrderActivity.this, CustomOrderActivity.this.getResources().getString(R.string.custom_order_least_two));
            }
        });
        customOrderUnSelectAdapter = new CustomOrderAdapter(unSelectBeans, new CustomOrderAdapter.CustomerOrderClick() {
            @Override
            public void customItemClick(int position) {
                isChanged = true;
                CustomOrderBean bean = unSelectBeans.get(position);
                bean.setSelect(true);
                selectBeans.add(bean);
                unSelectBeans.remove(position);

                customOrderSelectAdapter.notifyDataSetChanged();
                customOrderUnSelectAdapter.notifyDataSetChanged();
                checkUnSelectShow();
            }
        });
    }

    private void checkUnSelectShow() {
        if (unSelectBeans != null && unSelectBeans.size() > 0) {
            mUnSelectedRecyclerView.setVisibility(View.VISIBLE);
            unSelectedLine.setVisibility(View.VISIBLE);
            unSelectedTitle.setVisibility(View.VISIBLE);
        } else {
            mUnSelectedRecyclerView.setVisibility(View.GONE);
            unSelectedLine.setVisibility(View.GONE);
            unSelectedTitle.setVisibility(View.GONE);
        }
    }

    private void initView() {
//        mRecyclerView.setHasFixedSize(true);
//        mRecyclerView.setNestedScrollingEnabled(false);
        mRecyclerView.setLayoutManager(new FullyLinearLayoutManager(this));
        mRecyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL_LIST));
        mRecyclerView.setAdapter(customOrderSelectAdapter);

//        mUnSelectedRecyclerView.setHasFixedSize(true);
//        mUnSelectedRecyclerView.setNestedScrollingEnabled(false);
        mUnSelectedRecyclerView.setLayoutManager(new FullyLinearLayoutManager(this));
        mUnSelectedRecyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL_LIST));
        mUnSelectedRecyclerView.setAdapter(customOrderUnSelectAdapter);

        mRecyclerView.addOnItemTouchListener(new OnRecyclerItemClickListener(mRecyclerView) {
            @Override
            public void onItemClick(RecyclerView.ViewHolder vh) {

            }

            @Override
            public void onItemLongClick(RecyclerView.ViewHolder vh) {
                //判断被拖拽的是否是前两个，如果不是则执行拖拽
//                if (vh.getLayoutPosition() != 0 && vh.getLayoutPosition() != 1) {
                mItemTouchHelper.startDrag(vh);

                //获取系统震动服务
                Vibrator vib = (Vibrator) getSystemService(Service.VIBRATOR_SERVICE);//震动70毫秒
                vib.vibrate(70);
                isChanged = true;
//                }
            }
        });

        mItemTouchHelper = new ItemTouchHelper(new ItemTouchHelper.Callback() {

            /**
             * 是否处理滑动事件 以及拖拽和滑动的方向 如果是列表类型的RecyclerView的只存在UP和DOWN，如果是网格类RecyclerView则还应该多有LEFT和RIGHT
             * @param recyclerView
             * @param viewHolder
             * @return
             */
            @Override
            public int getMovementFlags(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
                if (recyclerView.getLayoutManager() instanceof GridLayoutManager) {
                    final int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN |
                            ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
                    final int swipeFlags = 0;
                    return makeMovementFlags(dragFlags, swipeFlags);
                } else {
                    final int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
                    final int swipeFlags = 0;
//                    final int swipeFlags = ItemTouchHelper.START | ItemTouchHelper.END;
                    return makeMovementFlags(dragFlags, swipeFlags);
                }
            }

            @Override
            public boolean onMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, RecyclerView.ViewHolder target) {
                //得到当拖拽的viewHolder的Position
                int fromPosition = viewHolder.getAdapterPosition();
                //拿到当前拖拽到的item的viewHolder
                int toPosition = target.getAdapterPosition();
                if (fromPosition < toPosition) {
                    for (int i = fromPosition; i < toPosition; i++) {
                        Collections.swap(selectBeans, i, i + 1);
                    }
                } else {
                    for (int i = fromPosition; i > toPosition; i--) {
                        Collections.swap(selectBeans, i, i - 1);
                    }
                }
                customOrderSelectAdapter.notifyItemMoved(fromPosition, toPosition);
                return true;
            }

            @Override
            public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {

            }

            /**
             * 重写拖拽可用
             * @return
             */
            @Override
            public boolean isLongPressDragEnabled() {
                return false;
            }

            /**
             * 长按选中Item的时候开始调用
             *
             * @param viewHolder
             * @param actionState
             */
            @Override
            public void onSelectedChanged(RecyclerView.ViewHolder viewHolder, int actionState) {
                if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
                    viewHolder.itemView.setBackgroundColor(Color.LTGRAY);
                }
                super.onSelectedChanged(viewHolder, actionState);
            }

            /**
             * 手指松开的时候还原
             * @param recyclerView
             * @param viewHolder
             */
            @Override
            public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
                super.clearView(recyclerView, viewHolder);
                viewHolder.itemView.setBackgroundColor(0);
            }
        });

        mItemTouchHelper.attachToRecyclerView(mRecyclerView);
        custom_order_hint_tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (from_type == 1) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_BOTTOM_SORT_CLICK);
                } else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_TOP_SORT_CLICK);
                }
                startActivity(new Intent(CustomOrderActivity.this, NotifyActivity.class));
            }
        });
        String html = "<font color=\"#a9a9a9\">" + getResources().getString(R.string.custom_order_hint) + "</font><font color=\"#555555\">" + getResources().getString(R.string.custom_order_hint_click);
        custom_order_hint_tv.setText(Html.fromHtml(html));
    }
}
