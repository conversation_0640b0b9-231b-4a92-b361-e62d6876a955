//package com.totwoo.totwoo.activity;
//
//import android.animation.ObjectAnimator;
//import android.app.Activity;
//import android.content.Context;
//import android.content.Intent;
//import android.graphics.Bitmap;
//import android.graphics.BitmapFactory;
//import android.graphics.Matrix;
//import android.graphics.Point;
//import android.graphics.Rect;
//import android.net.Uri;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.Handler;
//import android.provider.MediaStore;
//import android.text.TextUtils;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.View.OnClickListener;
//import android.view.ViewGroup;
//import android.widget.BaseAdapter;
//import android.widget.FrameLayout;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.ListAdapter;
//import android.widget.ListView;
//import android.widget.RatingBar;
//import android.widget.RelativeLayout;
//import android.widget.TextView;
//
//import com.etone.framework.annotation.EventInject;
//import com.etone.framework.annotation.InjectUtils;
//import com.etone.framework.component.bitmap.BitmapUtils;
//import com.etone.framework.event.EventData;
//import com.etone.framework.event.SubscriberListener;
//import com.etone.framework.event.TaskType;
//import com.etone.framework.utils.JSONUtils;
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import com.totwoo.library.bitmap.BitmapHelper;
//import com.totwoo.library.db.sqlite.WhereBuilder;
//import com.totwoo.library.exception.DbException;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.S;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.adapter.NewTotwooListAdapter;
//import com.totwoo.totwoo.bean.MessageBean;
//import com.totwoo.totwoo.bean.TotwooListBean;
//import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.ble.BleWrapper;
//import com.totwoo.totwoo.controller.HttpValues;
//import com.totwoo.totwoo.data.CoupleLogic;
//import com.totwoo.totwoo.data.TotwooLogic;
//import com.totwoo.totwoo.newConrtoller.UpdatePictureController;
//import com.totwoo.totwoo.utils.ApiException;
//import com.totwoo.totwoo.utils.CommonUtils;
//import com.totwoo.totwoo.utils.DbHelper;
//import com.totwoo.totwoo.utils.FileUtils;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//import com.totwoo.totwoo.utils.RequestCallBack;
//import com.totwoo.totwoo.utils.StringUtils;
//import com.totwoo.totwoo.utils.TrackEvent;
//import com.totwoo.totwoo.widget.CustomDialog;
//import com.totwoo.totwoo.widget.CustomProgressBarDialog;
//
//import org.greenrobot.eventbus.EventBus;
//import org.greenrobot.eventbus.Subscribe;
//import org.greenrobot.eventbus.ThreadMode;
//import org.json.JSONArray;
//import org.json.JSONObject;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.math.BigDecimal;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.Locale;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import com.totwoo.library.net.HttpRequest;
//import com.totwoo.library.net.RequestParams;
//import com.totwoo.totwoo.widget.PullZoomScrollView;
//import com.totwoo.totwoo.widget.RoundImageView;
//import com.totwoo.totwoo.widget.SendBQDialogController;
//import com.totwoo.totwoo.widget.TimeLineImageView;
//import com.totwoo.totwoo.widget.fuCardView.CenterViewPager;
//import com.totwoo.totwoo.widget.fuCardView.CenterViewPagerAdapter;
//import com.totwoo.totwoo.widget.fuCardView.ZoomOutPageTransformer;
//import com.umeng.analytics.MobclickAgent;
//
//import rx.Observable;
//import rx.functions.Action1;
//import rx.functions.Func1;
//
//import static com.totwoo.totwoo.R.string.totwoo;
//
///**
// * 心有灵犀界面
// *
// * <AUTHOR>
// * @date 2015-2015年7月10日
// */
//public class TheHeartActivity extends BaseActivity implements OnClickListener, SubscriberListener
//{
//    public static final String TOTWOO_IMAGE_JPG = "totwoo_image_custombg";
//    private final int SELECT_PHOTO = 0;
//    private final int SHOOTING_PHOTO = 1;
//    public final static int CROP_PHOTO = 2;
//    public static final String USER_HEAD_PORTRAIT = FileUtils.getImageDir() + File.separator + TOTWOO_IMAGE_JPG;
//    private CustomDialog dialog;
//    private TotwooLogic totwooLogic;
//    private static String backgroundUrl;
//
//    /**
//     * 标记是否需要进入首次进入 心有灵犀模块的 标识tag
//     */
//    public static final String FIRST_ENTER_HEART_TAG = "first_enter_heart";
//
//
//    /**
//     * 配对人数据传输对应的TAG
//     */
//    public static final String PAIRED_PERSON_DATA_TAG = "paired_person_data";
//
//    /**
//     * 配对对象的头像
//     */
//    private RoundImageView pairHeadIcon;
//
//    /**
//     * 自己的头像
//     */
//    private RoundImageView myHeadIcon;
//
//    /**
//     * 配对双发的名字
//     */
//    private TextView nameTv;
//
//    /**
//     * 断开配对的按钮
//     */
//    private TextView apartPairTv;
//
//    /**
//     * 心有灵犀寄语 标题
//     */
//    private TextView heartCountTv;
//
//    /**
//     * 心有灵犀次数的细分数据说明
//     */
//    private TextView heartCountInfo;
//
//    /**
//     * 删除totwoo消息记录
//     */
//    private ImageView clearHistoryBtn;
//
//
//    /**
//     * totwoo消息的整体布局
//     */
//    @BindView(R.id.the_heart_content_layout)
//    ListView historyListView;
//
//    @BindView(R.id.user_content_scrollview)
//    PullZoomScrollView sc;
//
//    @BindView(R.id.activity_the_heart_list_header_line)
//    TextView theLine;
//
//    @BindView(R.id.activity_the_heart_list_header_line1)
//    FrameLayout theLine1;
//
//    @BindView(R.id.user_top_bottom_img)
//    TimeLineImageView topBackground;
//
//    @BindView(R.id.the_heart_pager)
//    CenterViewPager viewPager;
//
//    @BindView(R.id.the_heart_walk_data)
//    TextView walkData;
//
//    @BindView(R.id.the_heart_together_day_tv)
//    TextView togetherData;
//
//    @BindView (R.id.the_heart_send_view)
//    LinearLayout sendView;
//
//    @BindView (R.id.user_top_bottom_img_fv)
//    RelativeLayout topBackgroundFv;
//
//    @BindView (R.id.user_top_bottom_mask)
//    View topBackgroundMask;
//
//    Bitmap topBackgroundImg;
//
//    boolean canSee1 = false;
//
//    @BindView (R.id.totwoo_haveyougood)
//    TextView totwoo_haveyougood;
//
//    @BindView (R.id.activity_the_heart_walked_together_not)
//    TextView jewelryNotBind;
//
//    @BindView (R.id.activity_the_heart_walked_together_view)
//    LinearLayout jewelryBindLayout;
//
//    BitmapUtils bu;
//
//    /**
//     * 配对管理操作类
//     */
//    private CoupleLogic mCoupleLogic;
//
//    /**
//     * totwoo
//     */
//    private NewTotwooListAdapter mAdapter;
//
//    /**
//     * totwoo 历史数据
//     */
//    private ArrayList<TotwooListBean.TotwooBean> totwooListData;
//
//    /**
//     * 进度框
//     */
//    private CustomProgressBarDialog progressBar;
//
//    private String pairedPhone;
//
//    /**
//     * 历史记录上一条 Totwoo 消息的 Totwoo 标识
//     */
//    private int tempFlag;
//
//    private TotwooListBean.TotwooBean tempTo;
//
//
//    private int currPage = 0;
//
//    private boolean isLoading;
//
//    private int firstListViewHeight = 0;
//
//    private ArrayList<View> pagerViews;
//
//    private SendBQDialogController sendBQDialogController;
//
//    private int topBarHeight;
//
//    private void initTotwooLogic()
//    {
//        totwooLogic = new TotwooLogic(this);
//        totwooLogic.setTotwooSendCallBack(new TotwooLogic.TotwooSendCallBack()
//        {
//            @Override
//            public void onSuccess()
//            {
//                // 发送成功的逻辑, 统一按照消息处理
//                if (mAdapter != null)
//                {
//                    currPage = 0;
//                    getTheHeartData();
//                }
//            }
//
//            @Override
//            public void onFailed(String error_msg) {}
//        });
//
//        sendBQDialogController = new SendBQDialogController(this, totwooLogic);
//    }
//
//    private void initPager() {
//        LayoutInflater inflater = LayoutInflater.from(this);
//        View v1 = inflater.inflate(R.layout.pager_heart_item, null);
//
//        pagerViews = new ArrayList<>();
//        pagerViews.add(v1);
//        v1.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v)
//            {
//                if (viewPager.getCurrentItem() == 0)
//                {
//                    Intent i = new Intent(TheHeartActivity.this, ConstellationActivity.class);
//                    i.putExtra("phone", pairedPhone);
//                    TheHeartActivity.this.startActivity(i);
//                }
//                else
//                    viewPager.setCurrentItemInCenter(0);
//            }
//        });
//        v1 = inflater.inflate(R.layout.pager_heart_item, null);
//        pagerViews.add(v1);
//        v1.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v)
//            {
//                if (viewPager.getCurrentItem() == 1)
//                {
//                    Intent i = new Intent(TheHeartActivity.this, ConstellationActivity.class);
//                    TheHeartActivity.this.startActivity(i);
//                }
//                else
//                    viewPager.setCurrentItemInCenter(1);
//            }
//        });
//        CenterViewPagerAdapter adapter = new CenterViewPagerAdapter(this, pagerViews);
//        viewPager.setAdapter(adapter);
//        viewPager.enableCenterLockOfChilds();
//        viewPager.setPageTransformer(true, new ZoomOutPageTransformer());
//        bu = new BitmapUtils(this);
//        bu.configDefaultLoadFailedImage(R.drawable.the_heart_banner);
//    }
//
//    private void initTopTitleBar()
//    {
//        View v = getTopBarBackgroundObject();
//        v.setVisibility(View.VISIBLE);
//        v.setAlpha(0);
//
//        setTopTitleColor(0xffffffff);
//        setTopBackIcon(R.drawable.back_icon_white);
//        setTopRightIcon(R.drawable.the_heart_manage_icon_white);
//
//        Rect rectangle = new Rect();
//        getWindow().getDecorView().getWindowVisibleDisplayFrame(rectangle);
//        topBarHeight = rectangle.top;
//    }
//
////    SimpleTarget target = new SimpleTarget<Bitmap>()
////    {
////        @Override
////        public void onResourceReady(Bitmap resource, GlideAnimation glideAnimation)
////        {
////            System.out.println("onResourceReady");
////            topBackground.setImageBitmap(resource);
////            //topBackground.setImageResource(R.drawable.home_bright_holder_bg_male);
////        }
////    };
//
//    private void initBackground()
//    {
//        if (!com.etone.framework.utils.StringUtils.isEmpty(backgroundUrl))
//        {
//            bu.display(topBackground, backgroundUrl);
//            //BitmapHelper.display1(this, backgroundUrl, R.drawable.the_heart_banner, target);
//            //BitmapHelper.display(this, topBackground, backgroundUrl, R.drawable.the_heart_banner);
//            //Glide.with(this).load(backgroundUrl).asBitmap().into(topBackground);
//            /*System.out.println("url:" + backgroundUrl);
//            bu.display(topBackground, backgroundUrl);
//            bu.*/
//        }
//    }
//
//    public void initViews()
//    {
//        if (!Apputils.systemLanguageIsChinese(this))
//        {
//            //totwoo_haveyougood.setVisibility(View.GONE);
//        }
//    }
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_the_heart);
//        LogUtils.e("aab");
//        ButterKnife.bind(this);
//        // 加载逻辑界面
//        initHeadView();
//        InjectUtils.injectOnlyEvent(this);
//        initTopTitleBar();
//        initPager();
//        initTotwooLogic();
//        initViews();
//        // 加载缓存数据
//        try {
//            totwooListData = HttpHelper.getDataCache(HttpHelper.URL_TOTWOO_LIST, new TypeToken<ArrayList<TotwooListBean.TotwooBean>>() {
//            }.getType());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        sendView.setOnClickListener(this);
//
////        RelativeLayout rl = getTopBar();
////        rl.setBackgroundColor(Color.BLACK);
//
//        mCoupleLogic = new CoupleLogic(this);
//        mHandler = new Handler();
//
//        totwooListData = new ArrayList<>();
//        mAdapter = new NewTotwooListAdapter(this, totwooListData);
//        historyListView.setAdapter(mAdapter);
//        sc.setZooViews(topBackground, topBackgroundFv, topBackgroundMask);
//
//        sc.setScrollViewListener(new PullZoomScrollView.ScrollViewListener()
//        {
//            @Override
//            public void onScrollViewChanged(PullZoomScrollView sc, int x, int y, int oldx, int oldy)
//            {
//                View view = sc.getChildAt(sc.getChildCount()-1);
//                int d = (int)view.getY() + view.getHeight();
//
//                int scrollY = sc.getScrollY();
//                d -= (sc.getHeight()+scrollY);
//                //System.out.println("vv:"+d + ", scrollY:" + sc.getScrollY() + "list:" + ( - firstListViewHeight));
//                if(d == 0)
//                {
//                    if (currPage > 0 && !isLoading)
//                        getTheHeartData();
//                }
//
//                int height = topBackground.getHeight() - getTopBarBackgroundObject().getMeasuredHeight() - topBarHeight;
//                View v = getTopBarBackgroundObject();
//                if (scrollY > height)
//                {
//                    setTopTitleColor(0xff000000);
//                    setTopBackIcon(R.drawable.back_icon_black);
//                    setTopRightIcon(R.drawable.the_heart_manage_icon);
//                    v.setAlpha(1);
//                }
//                else
//                {
//                    setTopTitleColor(0xffffffff);
//                    setTopBackIcon(R.drawable.back_icon_white);
//                    setTopRightIcon(R.drawable.the_heart_manage_icon_white);
//                    float alaph = 1 - (((float)height) - ((float)scrollY)) / ((float)height);
//                    v.setAlpha(alaph);
//                }
//
//                if (scrollY == 0)
//                {
//
//                }
//                else
//                {
//
//                }
//                /*else
//                {
//                    float alaph = 1 - (((float)height) - ((float)scrollY)) / ((float)height);
//                    if (alaph > 1)
//                        alaph = 1;
//                    System.out.println("alaph:" + alaph + ", scrollY:" + scrollY + ", height:" + height);
//                    v.setAlpha(alaph);
//
//                    *//*if (canSee != true)
//                    {
//                        canSee = true;
//
//                        setTopTitleColor(0xff000000);
//                        setTopBackIcon(R.drawable.back_icon_black);
//                        setTopRightIcon(R.drawable.the_heart_manage_icon);
//                    }*//*
//                }*/
//
//                boolean can = canSee(TheHeartActivity.this, theLine);
//                /*if (can != canSee)
//                {
//                    setAnim(can);
//                    canSee = can;
//                }*/
//
//                can = canSee(TheHeartActivity.this, theLine1);
//                if (can != canSee1)
//                {
//                    setAnim1(can);
//                    canSee1 = can;
//                }
//            }
//        });
//
////        historyListView.setOnScrollListener(new AbsListView.OnScrollListener() {
////            @Override
////            public void onScrollStateChanged(AbsListView view, int scrollState) {
////            }
////
////            @Override
////            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
////                if (currPage > 0 && firstVisibleItem + visibleItemCount > totalItemCount - 3 && !isLoading) {
////                    getTheHeartData();
////                }
////            }
////        });
//
//        // 每次进入心有灵犀界面, 后台刷新配对人的信息, 刷新心有灵犀次数数据
//        //getTheHeartData();
//        getHomeData();
//    }
//
//    private void setAnim1(boolean canSee)
//    {
//        ObjectAnimator anim;
//        if (canSee)
//        {
//            sendView.setVisibility(View.VISIBLE);
//            anim = ObjectAnimator.ofFloat(sendView, "alpha", 0f, 1f);
//            sendView.setClickable(true);
//        }
//        else
//        {
//            anim = ObjectAnimator.ofFloat(sendView, "alpha", 1f, 0f);
//            //sendView.setVisibility(View.GONE);
//            sendView.setClickable(false);
//        }
//
//        anim.setDuration(500);
//        anim.start();
//    }
//
//    private void setAnim(boolean canSee)
//    {
//        ObjectAnimator anim;
//        View v = getTopBarBackgroundObject();
//        if (canSee)
//        {
//            anim = ObjectAnimator.ofFloat(v, "alpha", 1f, 0f);
//
//            setTopTitleColor(0xffffffff);
//            setTopBackIcon(R.drawable.back_icon_black);
//            setTopRightIcon(R.drawable.the_heart_manage_icon);
//        }
//        else
//        {
//            v.setVisibility(View.VISIBLE);
//            anim = ObjectAnimator.ofFloat(v, "alpha", 0f, 1f);
//
//            setTopTitleColor(0xff000000);
//            setTopBackIcon(R.drawable.back_icon_black);
//            setTopRightIcon(R.drawable.the_heart_manage_icon);
//        }
//
//        anim.setDuration(500);
//        anim.start();
//    }
//
//    @Override
//    protected void onResume() {
//        super.onResume();
//        EventBus.getDefault().register(this);
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//        EventBus.getDefault().unregister(this);
//    }
//
//    private void setTextData() {
//        ArrayList<MessageBean> list = new ArrayList<MessageBean>();
//        for (int i = 0; i < 100; i++) {
//            MessageBean bean = new MessageBean();
//            bean.setId(i + 1000);
//            bean.setSendTime(System.currentTimeMillis() - (i * 3600000 * 6));
//            bean.setContent("totwoo");
//            if (i % 3 == 1) {
//                bean.setMsgType(MessageBean.MSG_TYPE_TOTWOO_OUT);
//                bean.setPicUrl("http://img4q.duitang.com/uploads/item/201502/25/20150225172743_x2hfW.jpeg");
//            } else {
//                bean.setMsgType(MessageBean.MSG_TYPE_TOTWOO_IN);
//                bean.setPicUrl("http://img3.imgtn.bdimg.com/it/u=2117727038,2641018931&fm=21&gp=0.jpg");
//            }
//            list.add(bean);
//        }
//        try {
//            DbHelper.getDbUtils().saveOrUpdateAll(list);
//        } catch (DbException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void makePagerViewData(View parent, boolean isSelf, String json)
//    {
//        TextView name = (TextView) parent.findViewById(R.id.the_heart_item_name);
//        RatingBar rating = (RatingBar) parent.findViewById(R.id.the_heart_item_rating);
//        TextView content = (TextView) parent.findViewById(R.id.the_heart_item_content);
//
//        if (isSelf)
//            name.setText(getString(R.string.constellation));
//        else
//            name.setText(getString(R.string.constellation1));
//
//        int rate = 0;
//        String summary = "";
//        String summary_en = "";
//        try
//        {
//            JSONObject jsonObject1 = new JSONObject(json);
//            String res = jsonObject1.getString("today");
//            JSONObject jsonObject = new JSONObject(res);
//            rate = jsonObject.getInt("all");
//            summary = jsonObject.getString("summary");
//            summary_en = jsonObject.getString("summary_en");
//        }
//        catch (Exception e)
//        {
//            e.printStackTrace();
//        }
//
//        String _summary = Apputils.systemLanguageIsChinese(this) ? summary : summary_en;
//
//        rating.setRating(rate);
//        content.setText(_summary);
//    }
//
//    private void getHomeData()
//    {
//        System.out.println("getHomeData");
//        if (isLoading)
//            return;
//        isLoading = true;
//
//        RequestParams params = HttpHelper.getBaseParams(true);
//        params.addFormDataPart("perpage", 20);
//        params.addFormDataPart("page", currPage + 1);
//        params.addFormDataPart("talkId", ToTwooApplication.owner.getPairedId());
//        LogUtils.e(HttpHelper.URL_TOTWOO_HOME);
//        HttpRequest.get(HttpHelper.URL_TOTWOO_HOME, params, new HomeDataRequestCallBack());
//    }
//
//    @EventInject(eventType = S.E.E_BACKGROUND_UPDATED_SUCCESSED, runThread = TaskType.UI)
//    public void onUpdateBackgroundSuccessed(EventData data)
//    {
//        HttpValues hv = (HttpValues) data;
//        backgroundUrl = (String) hv.getUserDefine("updateUrl");
//        initBackground();
//    }
//
//    @EventInject(eventType = S.E.E_BACKGROUND_UPDATED_FAILED, runThread = TaskType.UI)
//    public void onUpdateBackgroundFailed(EventData data)
//    {
//
//    }
//
//    @Override
//    public void onEventException(String eventType, EventData data, Throwable e)
//    {
//
//    }
//
//    private class HomeDataRequestCallBack extends RequestCallBack<String>
//    {
//        @Override
//        public void onLogicSuccess(String data)
//        {
//            super.onLogicSuccess(data);
//            LogUtils.e("aaaaa", data);
//            try
//            {
//                JSONObject json = new JSONObject(data);
//                String totwooData = json.getString("totwoo_data");
//                backgroundUrl = json.getString("backgroundPictureUrl");
//                //String target_jewelry_status = json.getString("target_jewelry_status"); //0:对方未绑定，1:对方已绑定
//                String target_jewelry_status = JSONUtils.getString(json, "target_jewelry_status", "0");
//                if (target_jewelry_status.equals("0"))
//                {
//                    jewelryNotBind.setVisibility(View.VISIBLE);
//                    jewelryBindLayout.setVisibility(View.GONE);
//                }
//                else
//                {
//                    if (ToTwooApplication.jewInfo.getConnectState() > 0)
//                    {
//                        jewelryNotBind.setVisibility(View.GONE);
//                        jewelryBindLayout.setVisibility(View.VISIBLE);
//                    }
//                    else
//                    {
//                        jewelryNotBind.setVisibility(View.VISIBLE);
//                        jewelryBindLayout.setVisibility(View.GONE);
//                    }
//                }
//                initBackground();
//                makeListData(totwooData);
//                int togetherData = json.getInt("together_data");
//                //togetherData += 86400;
//                //TheHeartActivity.this.togetherData.setText(" " + (togetherData/86400) + " ");
//                TheHeartActivity.this.togetherData.setVisibility(View.VISIBLE);
//                TheHeartActivity.this.togetherData.setText(" " + togetherData + " ");
//                double walkData = json.getDouble("walk_data");
//                BigDecimal b = new BigDecimal(walkData);
//                double f1 = b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
//                TheHeartActivity.this.walkData.setVisibility(View.VISIBLE);
//                TheHeartActivity.this.walkData.setText(" " + f1 + " ");
//                String yunchengData = json.getString("yuncheng_data");
//                JSONObject yunchengJson = new JSONObject(yunchengData);
//                String yunchengSelf = yunchengJson.getString("self");
//                String yunchengTarget = yunchengJson.getString("target");
//                makePagerViewData(pagerViews.get(0), false, yunchengTarget);
//                makePagerViewData(pagerViews.get(1), true, yunchengSelf);
//            }
//            catch (Exception e)
//            {
//                e.printStackTrace();
//            }
//
//            isLoading = false;
//        }
//
//        @Override
//        public void onFinish()
//        {
//            super.onFinish();
//            isLoading = false;
//            System.out.println("onFinish");
//            LogUtils.e("aaaaa", "this is finishUtil");
//        }
//
//        @Override
//        public void onLogicFailure(HttpBaseBean<String> t) {
//            String errMsg = ApiException.getHttpErrMessage(t.getErrorCode(), t.getErrorMsg());
//            LogUtils.e("aaaa", errMsg);
//        }
//    }
//
//    private void makeListData(String data)
//    {
//        LogUtils.e("paired1:" + ToTwooApplication.owner.getPairedId());
//        TotwooListBean o = new Gson().fromJson(data, TotwooListBean.class);
//        LogUtils.e("paired2:" + o.getTalkId());
//
//        if (o.getUserinfo() != null){
//            BitmapHelper.display(TheHeartActivity.this,
//                    pairHeadIcon, o.getUserinfo().getHead_portrait()
//            );
//            nameTv.setText(o.getUserinfo().getNick_name()
//                    + getString(R.string.and_me));
//
//            PreferencesUtils.put(TheHeartActivity.this,
//                    CoupleLogic.PAIRED_PERSON_NICK_NAME, o.getUserinfo().getNick_name());
//            PreferencesUtils.put(TheHeartActivity.this,
//                    CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, o.getUserinfo().getHead_portrait());
//
//            ToTwooApplication.owner.setPairedId(o.getTalkId());
//            pairedPhone = o.getUserinfo().getTotwoo_id();
//        }
//
//        if (currPage == 0 && totwooListData != null) {
//            totwooListData.clear();
//        }
//
//        treatTotwooListData(o);
//
//        if (o.getTotwooList() != null && o.getTotwooList().size() < 20) {
//            currPage = -1; // 标识没有更多数据了
//        } else {
//            currPage += 1;
//        }
//    }
//
//    /**
//     * 刷新心有灵犀次数数据 后台刷新配对人的头像信息
//     */
//    private void getTheHeartData() {
//        if (isLoading){
//            return;
//        }
//
//        isLoading = true;
//
//        RequestParams params = HttpHelper.getBaseParams(true);
//        params.addFormDataPart("perpage", 20);
//        params.addFormDataPart("page", currPage + 1);
//        params.addFormDataPart("talkId", ToTwooApplication.owner.getPairedId());
//
//        HttpRequest.get(HttpHelper.URL_TOTWOO_LIST, params, new RequestCallBack<String>(){
//            @Override
//            public void onLogicSuccess(String data) {
//                super.onLogicSuccess(data);
//                LogUtils.e(data);
//                makeListData(data);
//
//                isLoading = false;
//            }
//
//            @Override
//            public void onFinish() {
//                super.onFinish();
//                isLoading = false;
//            }
//        });
//    }
//
//    private class TreatListData implements Runnable
//    {
//        private TotwooListBean listBean;
//        public TreatListData(TotwooListBean listBean)
//        {
//            this.listBean = listBean;
//        }
//
//        public void run()
//        {
//            if (totwooListData == null) {
//                totwooListData = new ArrayList<>();
//            }
//
//            String json = new Gson().toJson(listBean);
//            JSONObject object = HttpHelper.parserStringResponse(json);
//
//            //提供给wear的数据 APP内不使用
//            combineTotwooData(object.optJSONArray("totwooList"));
//
//            Observable.from(listBean.getTotwooList())
//                    .filter(new Func1<TotwooListBean.TotwooBean, Boolean>() {
//                        @Override
//                        public Boolean call(TotwooListBean.TotwooBean totwoo) {
//                            if (tempFlag == 0) {
//                                tempFlag = totwoo.getConsonance();
//                                tempTo = totwoo;
//                                return true;
//                            } else if (tempFlag == totwoo.getConsonance()) {
//                                tempFlag = 0;
//                                tempTo.setCreateTime(totwoo.getCreateTime());
//                                tempTo.otherContent = totwoo.content;
//                                return false;
//                            }
//                            return true;
//                        }
//                    })
//                    .subscribe(new Action1<TotwooListBean.TotwooBean>() {
//                        @Override
//                        public void call(TotwooListBean.TotwooBean totwoo) {
//                            totwooListData.add(totwoo);
//                        }
//                    });
//
//            if (listBean.getCreateCouple() != null && listBean.getCreateCouple().getCreateTime() != 0){
//                TotwooListBean.TotwooBean bean = new TotwooListBean.TotwooBean();
//                bean.setCreateTime(listBean.getCreateCouple().getCreateTime());
//                bean.setConsonance(Integer.MIN_VALUE); // 表示配对信息
//                bean.setReceiverTotwoo_id(ToTwooApplication.owner.getTotwooId());
//                bean.setSenderTotwoo_id(ToTwooApplication.owner.getPairedId());
//                totwooListData.add(bean);
//            }
//            HttpHelper.saveDataCache(HttpHelper.URL_TOTWOO_LIST, totwooListData);
//            mHandler.post(new Runnable(){
//                public void run()
//                {
//                    mAdapter.notifyDataSetChanged();
//                    int res = setListViewHeightBasedOnChild(historyListView);
//                    if (firstListViewHeight == 0)
//                        firstListViewHeight = res;
//                }
//            });
//        }
//    }
//
//    private void treatTotwooListData(TotwooListBean listBean) {
//        if (listBean == null) {
//            return;
//        }
//
//        setTotwooInfo(listBean.getSenderCount(), listBean.getReceiverCount(), listBean.getXinCount());
//        new Thread(new TreatListData(listBean)).start();
//    }
//
//    /**
//     * 合并本地跟服务其返回的数据
//     */
//    protected void combineTotwooData(JSONArray array) {
//        if (array == null) {
//            return;
//        }
//
//        MessageBean bean = null;
//        // 合并数据
//        for (int i = array.length() - 1; i >= 0; i--) {
//            JSONObject json = array.optJSONObject(i);
//            if (json == null) {
//                continue;
//            }
//
//            if (json.optString("to").equals(
//                    ToTwooApplication.owner.getTotwooId())) {
//                bean = new MessageBean();
//                bean.setMsgType(MessageBean.MSG_TYPE_TOTWOO_IN);
//            } else if (json.optString("from").equals(
//                    ToTwooApplication.owner.getTotwooId())) {
//                bean = new MessageBean();
//                bean.setMsgType(MessageBean.MSG_TYPE_TOTWOO_OUT);
//            }
//
//            if (bean != null) {
//                bean.setSendTime(json.optLong("ts") * 1000);
//                bean.setSendUid(json.optString("from"));
//                bean.setHasShow(true);
//
//                // 删除本地 晚于 服务器返回最后一条数据时间的所有数据
//                if (i == array.length() - 1) {
//                    try {
//                        DbHelper.getDbUtils()
//                                .delete(MessageBean.class,
//                                        WhereBuilder
//                                                .b("msg_type",
//                                                        "in",
//                                                        new int[]{
//                                                                MessageBean.MSG_TYPE_TOTWOO_IN,
//                                                                MessageBean.MSG_TYPE_TOTWOO_OUT})
//                                                .and("send_time", ">=",
//                                                        bean.getSendTime())
//                                                .and("content", "==", null));
//
//                    } catch (DbException e) {
//                        e.printStackTrace();
//                    }
//                }
//
//                // 保存数据
//                try {
//                    DbHelper.getDbUtils().save(bean);
//                } catch (DbException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
//
//    /**
//     * 初始化相关监听事件
//     */
//    private void initHeadView() {
//
////        View headView = LayoutInflater.from(this).inflate(
////                R.layout.activity_the_heart_list_header, null);
//        pairHeadIcon = (RoundImageView) this
//                .findViewById(R.id.the_heart_head_mate_icon);
//        myHeadIcon = (RoundImageView) this
//                .findViewById(R.id.the_heart_head_me_icon);
//        nameTv = (TextView) this.findViewById(R.id.the_heart_name_tv);
//        apartPairTv = (TextView) this
//                .findViewById(R.id.the_heart_matched_tv);
//        heartCountTv = (TextView) this
//                .findViewById(R.id.the_heart_count_tv);
//        heartCountInfo = (TextView) this
//                .findViewById(R.id.the_heart_count_info_tv);
//        clearHistoryBtn = (ImageView) this
//                .findViewById(R.id.the_heart_clear_history_btn);
//        //handlePhoto(USER_HEAD_PORTRAIT);
////        BitmapHelper.display(this, topBackground, new File(USER_HEAD_PORTRAIT), R.drawable.home_constellation_holder_bg);
//
////        historyListView.addHeaderView(headView);
//
////		if (PreferencesUtils.getString(this,
////				CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "").equals("")
////				|| PreferencesUtils.getString(this,
////						CoupleLogic.PAIRED_PERSON_NICK_NAME, "").equals("")) {
////        updatePairedInfo();
////		}
//
//        if (!Apputils.systemLanguageIsChinese(this))
//        {
//            try
//            {
//                this.findViewById(R.id.totwoo_sub_title).setVisibility(View.GONE);
//                this.findViewById(R.id.history_sub_title).setVisibility(View.GONE);
//            }
//            catch (Exception e)
//            {
//                e.printStackTrace();
//            }
//        }
//
//        // 加载头像
//        BitmapHelper.display(TheHeartActivity.this,
//                pairHeadIcon,
//                PreferencesUtils.getString(this,
//                        CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, ""));
//
//        BitmapHelper.display(TheHeartActivity.this, myHeadIcon,
//                ToTwooApplication.owner.getHeaderUrl());
//
//        nameTv.setText(PreferencesUtils.getString(this,
//                CoupleLogic.PAIRED_PERSON_NICK_NAME, "")
//                + getString(R.string.and_me));
//
//        // 填充totwoo 消息数数据
//        setTotwooInfo(0, 0, 0);
//
////        // 清空totwoo 记录
////        clearHistoryBtn.setOnClickListener(new OnClickListener() {
////            @Override
////            public void onClick(View v) {
////                final CustomDialog dialog = new CustomDialog(
////                        TheHeartActivity.this);
//////				dialog.setTitle(R.string.prompt);
////                dialog.setMessage(R.string.clear_totwoo_history_info);
////                dialog.setNegativeButton(R.string.give_up);
////                dialog.setPositiveButton(new OnClickListener() {
////                    @Override
////                    public void onClick(View v) {
////                        dialog.dismiss();
////                        deleteTotwooHistory();
////                    }
////
////                });
////
////                dialog.show();
////            }
////        });
//        topBackground.setOnClickListener(this);
//    }
//
//    public void getPhotoDialog()
//    {
//        dialog = new CustomDialog(this);
//        dialog.setTitle(R.string.modify_background);
//        LinearLayout modify_head_dialog_ll = new LinearLayout(this);
//        modify_head_dialog_ll
//                .setLayoutParams(new LinearLayout.LayoutParams(
//                        LinearLayout.LayoutParams.MATCH_PARENT,
//                        LinearLayout.LayoutParams.WRAP_CONTENT));
//        modify_head_dialog_ll.setOrientation(LinearLayout.VERTICAL);
//        TextView album_tv = new TextView(this);
//        TextView camera_tv = new TextView(this);
//        TextView default_tv = new TextView(this);
//        modify_head_dialog_ll.addView(album_tv);
//        modify_head_dialog_ll.addView(camera_tv);
//        modify_head_dialog_ll.addView(default_tv);
//
//        dialog.setMainLayoutView(modify_head_dialog_ll);
//        album_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
//        camera_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
//        default_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
//        album_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20),Apputils.dp2px(this, 15));
//        camera_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20), Apputils.dp2px(this, 15));
//        default_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20),Apputils.dp2px(this, 15));
//        album_tv.setBackgroundResource(R.drawable.item_bg);
//        camera_tv.setBackgroundResource(R.drawable.item_bg);
//        default_tv.setBackgroundResource(R.drawable.item_bg);
//        album_tv.setText(R.string.album_select_usericon);
//        camera_tv.setText(R.string.use_camera);
//        default_tv.setText(R.string.restore_default);
//        album_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
//        camera_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
//        default_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
//        album_tv.setTextSize(16);
//        camera_tv.setTextSize(16);
//        default_tv.setTextSize(16);
//        dialog.setNegativeButtonText(R.string.cancel);
//        // 相册tv监听点击开启选择图片app
//        album_tv.setOnClickListener(new OnClickListener() {
//
//            @Override
//            public void onClick(View v) {
//
//                // Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
//                // intent.addCategory(Intent.CATEGORY_OPENABLE);
//                // intent.setType("image/*");
//                Intent intent = new Intent(Intent.ACTION_PICK, null);
//                intent.setDataAndType(
//                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
//                        "image/*");
//                startActivityForResult(intent, SELECT_PHOTO);
//            }
//        });
//        // 相机tv监听点击开启拍照app
//        camera_tv.setOnClickListener(new OnClickListener() {
//
//            @Override
//            public void onClick(View v) {
//                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
//
//                Uri imageUri = Uri.fromFile(new File(USER_HEAD_PORTRAIT));
//                // 指定照片保存路径（SD卡），USER_HEAD_PORTRAIT为一个临时文件，每次拍照后这个图片都会被替换
//                intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
//                startActivityForResult(intent, SHOOTING_PHOTO);
//            }
//        });
//        default_tv.setOnClickListener(new OnClickListener()
//        {
//            @Override
//            public void onClick(View v)
//            {
//                UpdatePictureController.getInstance().updateBackgroundPic("", 1);
//                topBackground.setImageResource(R.drawable.the_heart_banner);
//                dialog.dismiss();
//            }
//        });
//    }
//
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//
//        switch (requestCode) {
//            case SHOOTING_PHOTO:// 相机拍摄
//                if (resultCode == -1){
//                    CommonUtils.startPhotoZoom(CommonUtils.getUriForFile(TheHeartActivity.this,new File(USER_HEAD_PORTRAIT)),
//                            this, CROP_PHOTO, USER_HEAD_PORTRAIT, 36, 25);
//                }
//                break;
//            case SELECT_PHOTO:// 相册选取
//                Uri uri = null;
//                if (data != null) {
//                    uri = data.getData();
//                }
//
//                if (uri != null) {
//                        CommonUtils.startPhotoZoom(uri, this, CROP_PHOTO, USER_HEAD_PORTRAIT, 36, 25);
//                }
//                break;
//            case CROP_PHOTO://
//
//                if (resultCode == -1){
//                    if (dialog != null) {
//                        dialog.dismiss();
//                    }
//
//                    handlePhoto(USER_HEAD_PORTRAIT);
//                }
//                break;
//        }
//    }
//
//    // 处理图片
//    private void handlePhoto(String path) {
//        if (topBackgroundImg != null) {// 如果不释放的话，不断取图片，将会内存不够
//            topBackgroundImg.recycle();
//        }
//        if (!new File(path).exists())
//        {
//            topBackground.setImageResource(R.drawable.home_constellation_holder_bg);
//            return;
//        }
//
//        //bu.display(topBackground, path);
//                // 为了防止Oom先加载个缩略图
//        BitmapFactory.Options options = new BitmapFactory.Options();
//        options.inJustDecodeBounds = false;
//        options.inSampleSize = 3;
//
//        topBackgroundImg = BitmapFactory.decodeFile(path, options);
//
//        //如果图片尺寸小，就放大
//        if (topBackgroundImg.getWidth() < Apputils.getScreenWidth(this))
//        {
//            Bitmap newBitmap = resizeImage(this, topBackgroundImg);
//            topBackgroundImg.recycle();
//            if (newBitmap == null)
//            {
//                return;
//            }
//            topBackgroundImg = newBitmap;
//        }
//        topBackground.setImageBitmap(topBackgroundImg);
//        File f = new File(USER_HEAD_PORTRAIT);
//        try
//        {
//            if (f.exists())
//                f.delete();
//            f.createNewFile();
//            topBackgroundImg.compress(Bitmap.CompressFormat.JPEG, 100, new FileOutputStream(new File(USER_HEAD_PORTRAIT)));
//        }
//        catch (Exception e)
//        {
//            e.printStackTrace();
//            f.deleteOnExit();
//        }
//
//        UpdatePictureController.getInstance().uploadPictures(this, USER_HEAD_PORTRAIT);
//        /*LogUtils.i("bitmapsize", topBackgroundImg.getByteCount() + "");
//        // 如果缩略图比较小就加载原图
//        if (topBackgroundImg.getByteCount() < 500000) {
//            topBackgroundImg.recycle();
//            topBackgroundImg = BitmapFactory.decodeFile(path);
//            topBackground.setImageBitmap(topBackgroundImg);
//        }
//
//        LogUtils.i("bitmapsize", topBackgroundImg.getByteCount() + "");
//        // 压缩图片
//        if (topBackgroundImg.getHeight() >= topBackgroundImg.getWidth()) {
//            topBackgroundImg = BitmapHelper.compressBySize(
//                    topBackgroundImg,
//                    300,
//                    300 * topBackgroundImg.getHeight()
//                            / topBackgroundImg.getWidth());
//        } else {
//            topBackgroundImg = BitmapHelper.compressBySize(
//                    topBackgroundImg,
//                    300 * topBackgroundImg.getWidth()
//                            / topBackgroundImg.getHeight(), 300);
//        }
//
//        LogUtils.i("bitmapsize", topBackgroundImg.getByteCount() + "");
//        //postUserHeadPortrait(userHeadPortrait);
//
//
//        topBackground.setImageBitmap(topBackgroundImg);*/
//    }
//
//    /*将图片放大到屏幕宽度尺寸到比例*/
//    public static Bitmap resizeImage(Context context, Bitmap bitmap)
//    {
//        Bitmap resizedBitmap = null;
//        try
//        {
//            Bitmap BitmapOrg = bitmap;
//            int width = BitmapOrg.getWidth();
//            int height = BitmapOrg.getHeight();
//            int newWidth = Apputils.getScreenWidth(context);
//
//            float scale = ((float) newWidth) / width;
//
//            Matrix matrix = new Matrix();
//            matrix.postScale(scale, scale);
//            // if you want to rotate the Bitmap
//            // matrix.postRotate(45);
//            resizedBitmap = Bitmap.createBitmap(BitmapOrg, 0, 0, width, height, matrix, true);
//        }
//        catch (Exception e)
//        {
//            e.printStackTrace();
//        }
//
//        return resizedBitmap;
//    }
//
//    /**
//     * 设置心有灵犀的相关数字信息
//     */
//    private void setTotwooInfo(int send, int rece, int heart) {
//        heartCountTv.setText(" " + heart + " ");
////        heartCountInfo
////                .setText(getString(R.string.totwoo_count_info, send, rece));
//    }
//
//    /**
//     * 初始化顶部栏
//     */
//    protected void initTopBar() {
//        setTopTitle(R.string.the_heart);
//        setTopRightOnClick(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                startActivity(new Intent(TheHeartActivity.this,
//                        TheHeartManageActivity.class));
//            }
//        });
//    }
//
//    /**
//     * 删除totwoo记录数据, 包括本地数据库的删除, 以及服务器的删除
//     */
//    private void deleteTotwooHistory() {
//        RequestParams params = HttpHelper.getBaseParams(true);
//        params.addFormDataPart("talkId", ToTwooApplication.owner.getPairedId());
//        HttpRequest.post(
//                HttpHelper.URL_DELETE_TOTWOO_LIST, params,
//                new RequestCallBack<String>(TheHeartActivity.this) {
//                    @Override
//                    public void onStart() {
//                    }
//
//                    @Override
//                    public void onLogicSuccess(String s) {
//                        super.onLogicSuccess(s);
//                        currPage = 0;
//                        totwooListData.clear();
//                        if (mAdapter != null){
//                            mAdapter.notifyDataSetChanged();
//                        }
//
//                        // 删除本地数据库数据
//                        try {
//                            DbHelper.getDbUtils().delete(
//                                    MessageBean.class,
//                                    WhereBuilder.b("msg_type", "in", new int[]{
//                                            MessageBean.MSG_TYPE_TOTWOO_IN,
//                                            MessageBean.MSG_TYPE_TOTWOO_OUT}));
//
////                            sendBroadcast(new Intent(
////                                    ACTION_TOTWOO_DATA_CHANGEED));
//                        } catch (DbException e) {
//                            e.printStackTrace();
//                        }
//                    }
//                });
//    }
//
//    /**
//     * 心有灵犀界面进入的判断逻辑：<br>
//     * 1， 首次进入该界面， 进入心有灵犀介绍页，<br>
//     * 2， 非首次进入，已配对用户进入配对成功页；未配对成功用户， 进入配对管理界面
//     */
//    public static void enter(Context context) {
//        Intent intent = null;
//        if (PreferencesUtils.getBoolean(context, FIRST_ENTER_HEART_TAG, true)) {
////            intent = new Intent(context, TheHeartHelpActivity.class);
//        } else if (TextUtils.isEmpty(ToTwooApplication.owner.getPairedId())) {
//            intent = new Intent(context, TheHeartManageActivity.class);
//        } else {
//            intent = new Intent(context, NewHeartActivity.class);
//        }
//        context.startActivity(intent);
//    }
//
//    public static void enter1(Context context) {
//        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_COZONE);
//        Intent intent = null;
//        if (PreferencesUtils.getBoolean(context, FIRST_ENTER_HEART_TAG, true)) {
//            intent = new Intent(context, NewHeartActivity.class);
//        } else if (TextUtils.isEmpty(ToTwooApplication.owner.getPairedId())) {
//            intent = new Intent(context, TheHeartManageActivity.class);
//        } else {
//            intent = new Intent(context, NewHeartActivity.class);
//        }
//        context.startActivity(intent);
//    }
//
//    /**
//     * 展示进度框
//     *
//     * @param show true 为展示, false 为隐藏
//     */
//    private void showProgressBar(boolean show) {
//        if (show) {
//            if (progressBar == null) {
//                progressBar = new CustomProgressBarDialog(TheHeartActivity.this);
//            }
//            progressBar.show();
//        } else {
//
//            if (progressBar != null && progressBar.isShowing()) {
//                progressBar.dismiss();
//            }
//        }
//    }
//
//    @Override
//    public void onClick(View v)
//    {
//        switch (v.getId())
//        {
//            case R.id.user_top_bottom_img:
//                OnChangeBackgroundClicked();
//                break;
//            case R.id.the_heart_send_view:
//                sendBQDialogController.showBQDialog(false);
//                //UpdatePictureController.getInstance().getQiniuToken(this);
//                break;
//            default:
//                break;
//        }
//    }
//
//    private void OnChangeBackgroundClicked()
//    {
//        getPhotoDialog();
//        dialog.show();
//    }
//
//
//    /**
//     * totwoo 历史记录的列表Adapter, 自定义的CursorAdapter, 会随数据库更新, 及时更新列表
//     *
//     * <AUTHOR>
//     * @date 2015-2015年8月28日
//     */
//    class TotwooListAdapter extends BaseAdapter {
//        // /** totwoo 历史数据 */
//        // private List<MessageBean> tempData;
//        private Context mContext;
//
//        public TotwooListAdapter(Context mContext) {
//            this.mContext = mContext;
//        }
//
//        @Override
//        public int getCount() {
//            return totwooListData == null ? 0 : totwooListData.size();
//        }
//
//        @Override
//        public Object getItem(int position) {
//            return totwooListData.get(position);
//        }
//
//        @Override
//        public long getItemId(int position) {
//            return position;
//        }
//
//        @Override
//        public View getView(int position, View convertView, ViewGroup parent) {
//            ViewHolder holder;
//            if (convertView == null) {
//                convertView = LayoutInflater.from(mContext).inflate(
//                        R.layout.totwoo_message_item, null);
//                holder = new ViewHolder();
//                holder.dateLine = (LinearLayout) convertView
//                        .findViewById(R.id.totwoo_message_date_line_layout);
//                holder.dateLineTextTv = (TextView) convertView
//                        .findViewById(R.id.totwoo_message_date_line_text);
//                holder.msgLayout = (RelativeLayout) convertView
//                        .findViewById(R.id.totwoo_message_message_text_layout);
//                holder.headIcon = (ImageView) convertView
//                        .findViewById(R.id.totwoo_message_head_icon);
//                holder.dateView = (TextView) convertView
//                        .findViewById(R.id.totwoo_message_totwoo_message_date_tv);
//                holder.msgTv = (TextView) convertView
//                        .findViewById(R.id.totwoo_message_totwoo_message_tv);
//                holder.parDateView = (TextView) convertView
//                        .findViewById(R.id.totwoo_message_totwoo_message_date_tv_out);
//                holder.parHeadIcon = (ImageView) convertView
//                        .findViewById(R.id.totwoo_message_head_icon_out);
//                holder.parMsgTv = (TextView) convertView
//                        .findViewById(R.id.totwoo_message_totwoo_message_tv_out);
//                holder.parMsgLayout = (RelativeLayout) convertView
//                        .findViewById(R.id.totwoo_message_message_text_layout_out);
//                holder.totwooIv = (ImageView) convertView.
//
//                        findViewById(R.id.totwoo_success_iv);
//                convertView.setTag(holder);
//            } else {
//                holder = (ViewHolder) convertView.getTag();
//            }
//
//            TotwooListBean.TotwooBean bean = totwooListData.get(position);
//
//            if (bean.getConsonance() > 0) {
//                holder.parHeadIcon.setVisibility(View.VISIBLE);
//                holder.totwooIv.setVisibility(View.VISIBLE);
//                holder.parMsgLayout.setVisibility(View.VISIBLE);
//            } else {
//                holder.parHeadIcon.setVisibility(View.GONE);
//                holder.totwooIv.setVisibility(View.GONE);
//                holder.parMsgLayout.setVisibility(View.GONE);
//            }
//
//
//            // 根据消息类型, 重新布局界面
//            if (bean.getSenderTotwoo_id().equals(ToTwooApplication.owner.getTotwooId())) {
//                // 设置头像靠右
//                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) holder.headIcon
//                        .getLayoutParams();
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                    params.removeRule(RelativeLayout.ALIGN_PARENT_LEFT);
//                }
//                params.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
//                holder.headIcon.setLayoutParams(params);
//
//                // 设置消息体位于头像左侧
//                params = (RelativeLayout.LayoutParams) holder.msgLayout
//                        .getLayoutParams();
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                    params.removeRule(RelativeLayout.RIGHT_OF);
//                    params.removeRule(RelativeLayout.END_OF);
//                }
//
//                params.addRule(RelativeLayout.LEFT_OF,
//                        R.id.totwoo_message_head_icon);
//                params.addRule(RelativeLayout.START_OF,
//                        R.id.totwoo_message_head_icon);
//                holder.msgLayout.setLayoutParams(params);
//                holder.msgLayout
//                        .setBackgroundResource(R.drawable.totwoo_message_bg_out);
//
//                holder.msgTv.setTextColor(mContext.getResources().getColor(
//                        R.color.the_totwoo_out_text_color));
//                BitmapHelper.display(TheHeartActivity.this, holder.headIcon,
//                        ToTwooApplication.owner.getHeaderUrl());
//
//                if (bean.getConsonance() != 0) {
//                    holder.totwooIv.setImageResource(R.drawable.totwoo_list_in);
//                    /*params = (RelativeLayout.LayoutParams) holder.totwooIv.getLayoutParams();
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                        params.removeRule(RelativeLayout.RIGHT_OF);
//                    }
//                    params.addRule(RelativeLayout.LEFT_OF, R.id.totwoo_message_head_icon);
//                    holder.totwooIv.setLayoutParams(params);*/
//
//                    params = (RelativeLayout.LayoutParams) holder.parHeadIcon.getLayoutParams();
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                        params.removeRule(RelativeLayout.ALIGN_PARENT_RIGHT);
//                    }
//                    params.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
//                    holder.parHeadIcon.setLayoutParams(params);
//
//                    params = (RelativeLayout.LayoutParams) holder.parMsgLayout.getLayoutParams();
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                        params.removeRule(RelativeLayout.LEFT_OF);
//                        params.removeRule(RelativeLayout.START_OF);
//                    }
//                    params.addRule(RelativeLayout.RIGHT_OF,
//                            R.id.totwoo_message_head_icon_out);
//                    params.addRule(RelativeLayout.END_OF,
//                            R.id.totwoo_message_head_icon_out);
//                    holder.parMsgLayout.setLayoutParams(params);
//                    holder.parMsgLayout.setBackgroundResource(R.drawable.totwoo_message_bg_in);
//
//                    BitmapHelper.display(TheHeartActivity.this,
//                            holder.parHeadIcon,
//                            PreferencesUtils.getString(mContext,
//                                    CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, ""));
//                }
//            } else {
//                // 设置头像靠左
//                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) holder.headIcon
//                        .getLayoutParams();
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                    params.removeRule(RelativeLayout.ALIGN_PARENT_RIGHT);
//                    params.removeRule(RelativeLayout.ALIGN_PARENT_END);
//                }
//
//                //根据totwoo状态控制 totwoo图片显示 1是totwoo成功 0是totwoo失败
//                params.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
//                holder.headIcon.setLayoutParams(params);
//
//                // 设置消息体位于头像右侧
//                params = (RelativeLayout.LayoutParams) holder.msgLayout
//                        .getLayoutParams();
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                    params.removeRule(RelativeLayout.LEFT_OF);
//                    params.removeRule(RelativeLayout.START_OF);
//                }
//                params.addRule(RelativeLayout.RIGHT_OF,
//                        R.id.totwoo_message_head_icon);
//                params.addRule(RelativeLayout.END_OF,
//                        R.id.totwoo_message_head_icon);
//                holder.msgLayout.setLayoutParams(params);
//                holder.msgLayout
//                        .setBackgroundResource(R.drawable.totwoo_message_bg_in);
//
//                holder.msgTv.setTextColor(mContext.getResources().getColor(
//                        R.color.the_totwoo_in_text_color));
//                BitmapHelper.display(TheHeartActivity.this,
//                        holder.headIcon,
//                        PreferencesUtils.getString(mContext,
//                                CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, ""));
//
//
//                if (bean.getConsonance() != 0) {
//                    holder.totwooIv.setImageResource(R.drawable.totwoo_list_out);
//                    /*params = (RelativeLayout.LayoutParams) holder.totwooIv.getLayoutParams();
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                        params.removeRule(RelativeLayout.LEFT_OF);
//                    }
//                    params.addRule(RelativeLayout.RIGHT_OF, R.id.totwoo_message_head_icon);
//                    holder.totwooIv.setLayoutParams(params);*/
//                    params = (RelativeLayout.LayoutParams) holder.parHeadIcon.getLayoutParams();
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                        params.removeRule(RelativeLayout.ALIGN_PARENT_LEFT);
//                    }
//                    params.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
//                    holder.parHeadIcon.setLayoutParams(params);
//
//                    params = (RelativeLayout.LayoutParams) holder.parMsgLayout.getLayoutParams();
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//                        params.removeRule(RelativeLayout.RIGHT_OF);
//                        params.removeRule(RelativeLayout.END_OF);
//                    }
//                    params.addRule(RelativeLayout.LEFT_OF,
//                            R.id.totwoo_message_head_icon_out);
//                    params.addRule(RelativeLayout.START_OF,
//                            R.id.totwoo_message_head_icon_out);
//                    holder.parMsgLayout.setLayoutParams(params);
//                    holder.parMsgLayout.setBackgroundResource(R.drawable.totwoo_message_bg_out);
//                    BitmapHelper.display(TheHeartActivity.this,
//                            holder.parHeadIcon,
//                            ToTwooApplication.owner.getHeaderUrl());
//                }
//            }
//
//            if (bean.getConsonance() == Integer.MIN_VALUE){
//                holder.msgTv.setText(R.string.paired_frist_message);
//                holder.parMsgTv.setText(R.string.paired_frist_message);
//            } else {
//                holder.msgTv.setText(totwoo);
//                holder.parMsgTv.setText(totwoo);
//            }
//
//            // 发送时间
//            SimpleDateFormat format = new SimpleDateFormat("HH:mm",
//                    Locale.getDefault());
//            holder.dateView.setText(format.format(new Date(bean.getCreateTime() * 1000L)));
//
//            if (bean.getConsonance() != 0) {
//                holder.parDateView.setText(format.format(new Date(bean.getCreateTime() * 1000L)));
//            }
//
//
//            // 设置日期分割线
//            String dateText = getDateText(bean.getCreateTime() * 1000L);
//
//            if (position == 0
//                    || !dateText.equals(getDateText(totwooListData.get(
//                    position - 1).getCreateTime() * 1000L))) {
//                holder.dateLine.setVisibility(View.VISIBLE);
//                holder.dateLineTextTv.setText(dateText);
//            } else {
//                holder.dateLine.setVisibility(View.GONE);
//            }
//
//            return convertView;
//        }
//    }
//
//    class ViewHolder {
//        public LinearLayout dateLine;
//        public TextView dateLineTextTv;
//        public RelativeLayout msgLayout;
//        public ImageView headIcon;
//        public TextView dateView;
//        public TextView msgTv;
//        public ImageView parHeadIcon;
//        public TextView parDateView;
//        public TextView parMsgTv;
//        public ImageView totwooIv;
//        public RelativeLayout parMsgLayout;
//    }
//
//    /**
//     * 获得指定时间点对应的日期格式<br>
//     * 今天: today, 其他:00-00
//     *
//     * @param time
//     * @return
//     */
//    public String getDateText(long time) {
//        Calendar cal = Calendar.getInstance();
//        cal.set(Calendar.HOUR_OF_DAY, 0);
//        cal.set(Calendar.MINUTE, 0);
//        cal.set(Calendar.SECOND, 0);
//        cal.set(Calendar.MILLISECOND, 0);
//        // 获得指定时间, 今天0点的差值
//        long off = time - cal.getTimeInMillis();
//
//        if (off >= 0) {
//            return getString(R.string.today_upper_case);
//        } else {
//            SimpleDateFormat format = new SimpleDateFormat("MM-dd", Locale.getDefault());
//            return format.format(new Date(time));
//        }
//    }
//
//    /**
//     * 接收totwoo数据变化的广播接收器, 及时刷新数据列表
//     *
//     * <AUTHOR>
//     * @date 2015-2015年8月30日
//     */
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onReceive(TotwooMessage msg) {
//        if (msg.getTotwooState().equals(ACTION_TOTWOO_DATA_CHANGEED)
//                || msg.getTotwooState().equals(
//                BleWrapper.ACTION_BLE_SEND_TOTWOO)) {
//            runOnUiThread(new Runnable() {
//                public void run() {
//                    if (mAdapter != null) {
//                        currPage = 0;
//                        getTheHeartData();
//                    }
//                }
//            });
//        } else if (msg.getTotwooState().equals(
//                CoupleLogic.COUPLE_STATE_APART+"")) {
//            // 如果对方接触绑定, 直接跳转配对管理页面
//
//            startActivity(new Intent(TheHeartActivity.this,
//                    TheHeartManageActivity.class));
//            finish();
//        }
//    }
//
//    public static int setListViewHeightBasedOnChild(ListView listView)
//    {
//        if (listView == null)
//            return 0;
//
//        ListAdapter adapter = listView.getAdapter();
//        if (adapter == null)
//            return 0;
//
//        int totalHeight = 0;
//        for (int i=0; i<adapter.getCount(); i++)
//        {
//            View listItem = adapter.getView(i, null, listView);
//            listItem.measure(0, 0);
//            totalHeight += listItem.getMeasuredHeight();
//        }
//
//        ViewGroup.LayoutParams params = listView.getLayoutParams();
//        params.height = totalHeight + (listView.getDividerHeight() * (adapter.getCount() - 1));
//        listView.setLayoutParams(params);
//        return totalHeight;
//    }
//
//    public static boolean canSee(Activity activity, View view)
//    {
//        Point p = new Point();
//        activity.getWindowManager().getDefaultDisplay().getSize(p);
//        int screenWidth = p.x;
//        int screenHeight = p.y;
//        Rect rect = new Rect(0, 0, screenWidth, screenHeight);
//        int[] location = new int[2];
//        view.getLocationInWindow(location);
//        if (view.getLocalVisibleRect(rect))
//            return true;
//        else
//            return false;
//    }
//}