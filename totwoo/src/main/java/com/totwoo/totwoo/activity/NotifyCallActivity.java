package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.activity.CallRemindSetActivity.NOTIFY_SETTING_MUSIC_TYPE;
import static com.totwoo.totwoo.utils.CommonArgs.NOTIFY_CALL_COLOR_TAG;
import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.ColorLibraryAdapter;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.MusicBrightBean;
import com.totwoo.totwoo.bean.MusicIndexBean;
import com.totwoo.totwoo.bean.MusicItemBeans;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.service.BrightMusicPlayService;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.FullyLinearLayoutManager;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class NotifyCallActivity extends BaseActivity {
    @BindView(R.id.notify_setting_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    @BindView(R.id.make_card_sample_subtitle)
    TextView mMakeCardSampleSubtitle;
    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    @BindView(R.id.notify_bright_type_layout)
    LinearLayout mBrightTypeLayout;
    @BindView(R.id.notify_vibration_layout)
    LinearLayout mNotifyVibrationLayout;
    @BindView(R.id.long_bright_vibration_tv)
    TextView mLongVibrationTv;
    @BindView(R.id.short_bright_vibration_tv)
    TextView mShortVibrationTv;
    @BindView(R.id.notify_bright_vibration_layout)
    LinearLayout mBrightVibrationLayout;
    @BindView(R.id.notify_bright_music_breath_tv_call)
    TextView mBrightBreathTv;
    @BindView(R.id.notify_bright_music_breath_iv_call)
    ImageView mBrightBreathIv;
    @BindView(R.id.notify_bright_music_breath_layout_call)
    ConstraintLayout mBrightLayout;
    @BindView(R.id.notify_switch_click_fill_view)
    View mNotifySwitchFillView;
    @BindView(R.id.notify_switch_click_item)
    RelativeLayout mNotifySwitchClickItem;

    @BindView(R.id.notify_bright_music_rv)
    RecyclerView mBrightMusicRv;

    private ColorLibraryAdapter colorLibraryAdapter;
    private BrightMusicAdapter brightMusicAdapter;

    private JewelryNotifyModel nowSetModel;
    private Intent resultData;
    private int music_type;
    public ArrayList<MusicBrightBean> musicBrightBeans;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notify_setting);
        ButterKnife.bind(this);

        startService(new Intent(this, BrightMusicPlayService.class));
        resultData = getIntent();
        music_type = resultData.getIntExtra(NOTIFY_SETTING_MUSIC_TYPE, 0);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.jewelry_message_notification_mode);
        if (BleParams.isNoneMusicJewelry() || BleParams.isButtonBatteryJewelry()) {
            mBrightTypeLayout.setVisibility(View.GONE);
        } else {
            mBrightTypeLayout.setVisibility(View.VISIBLE);
        }

        mBrightLayout.setVisibility(View.VISIBLE);
        mNotifyVibrationLayout.setVisibility(View.GONE);
        mNotifySwitchFillView.setVisibility(View.GONE);
        mNotifySwitchClickItem.setVisibility(View.GONE);
        mBrightVibrationLayout.setVisibility(View.GONE);
        mMakeCardSampleSubtitle.setText(R.string.call_remind_dialog_title);

        musicBrightBeans = MusicItemBeans.getMusicBrightBeans();

        mBrightMusicRv.setLayoutManager(new FullyLinearLayoutManager(this));
        brightMusicAdapter = new BrightMusicAdapter();
        mBrightMusicRv.setAdapter(brightMusicAdapter);

        selectMusicType(music_type);

        nowSetModel = new JewelryNotifyModel(true, getIntent().getStringExtra(NOTIFY_CALL_COLOR_TAG), 0);

        colorLibraryRecyclerView.setLayoutManager(new FullyLinearLayoutManager(NotifyCallActivity.this, LinearLayoutManager.HORIZONTAL, false));
        colorLibraryAdapter = new ColorLibraryAdapter(nowSetModel.getFlashColor(), NotifyCallActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                nowSetModel.setFlashColor((String) v.getTag());
                colorLibraryAdapter.setSelectColor((String) v.getTag());
                saveNowModel(music_type);
            }
        });
        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
        colorLibraryRecyclerView.scrollToPosition(colorLibraryAdapter.getIndex(nowSetModel.getFlashColor()));
    }

    @OnClick({R.id.notify_bright_music_breath_layout_call})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.notify_bright_music_breath_layout_call:
                selectMusicType(0);
                music_type = 0;
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
                BluetoothManage.getInstance().changeBirghtMode(-1, false);
                saveNowModel(0);
                break;
        }
    }

    private void saveNowModel(int index) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.show(NotifyCallActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
            return;
        }
        resultData.putExtra(NOTIFY_CALL_COLOR_TAG, nowSetModel.getFlashColor());
        resultData.putExtra(NOTIFY_SETTING_MUSIC_TYPE, music_type);
        setResult(RESULT_OK, resultData);

        if (index == 0) {
            BluetoothManage.getInstance().notifyJewelry(LONG_VIBRATION_SEC, nowSetModel.getFlashColorValue());
        } else {
            int music_index = musicBrightBeans.get(index - 1).getMusic_index();
            BluetoothManage.getInstance().notifyBrightMusicOnce(music_index, NotifyUtil.getColorValue(nowSetModel.getFlashColor()));
            MusicIndexBean musicIndexBean = new MusicIndexBean(music_index);
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_ONCE, musicIndexBean);
        }

    }

    private void selectMusicType(int index) {
        for (int i = 0; i < musicBrightBeans.size(); i++) {
            MusicBrightBean musicBrightBean = musicBrightBeans.get(i);
            musicBrightBean.setSelect(false);
            musicBrightBeans.set(i, musicBrightBean);
        }
        if (index == 0) {
            mBrightBreathTv.setTextColor(getResources().getColor(R.color.color_main));
            mBrightBreathTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            mBrightBreathIv.setVisibility(View.VISIBLE);
        } else {
            mBrightBreathTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
            mBrightBreathTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            mBrightBreathIv.setVisibility(View.GONE);

            try {
                MusicBrightBean musicBrightBean = musicBrightBeans.get(index - 1);
                musicBrightBean.setSelect(true);
                musicBrightBeans.set(index - 1, musicBrightBean);
            } catch (Exception e) {
                MusicBrightBean musicBrightBean = musicBrightBeans.get(0);
                musicBrightBean.setSelect(true);
                musicBrightBeans.set(0, musicBrightBean);
            }
        }
        brightMusicAdapter.notifyDataSetChanged();
    }

    protected class BrightMusicAdapter extends RecyclerView.Adapter<BrightMusicAdapter.ViewHolder> {
        @NonNull
        @Override
        public BrightMusicAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new BrightMusicAdapter.ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.music_bright_item, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull BrightMusicAdapter.ViewHolder holder, int position) {
            holder.mNameTv.setText(musicBrightBeans.get(position).getName());
            if (musicBrightBeans.get(position).isSelect()) {
                holder.mSelectIv.setVisibility(View.VISIBLE);
                holder.mNameTv.setTextColor(getResources().getColor(R.color.color_main));
                holder.mNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            } else {
                holder.mSelectIv.setVisibility(View.INVISIBLE);
                holder.mNameTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
                holder.mNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            }
            holder.mLayout.setOnClickListener(v -> {
                switch (position){
                    case 0:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.COMING_ROMANTIC);
                        break;
                    case 1:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.COMING_PARTY);
                        break;
                    case 2:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.COMING_PHONEONE);
                        break;
                    case 3:
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.COMING_PHONETWO);
                        break;
                }
                selectMusicType(position + 1);
                music_type = position + 1;
                saveNowModel(position + 1);
            });
        }

        @Override
        public int getItemCount() {
            return musicBrightBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.notify_bright_music_layout)
            ConstraintLayout mLayout;
            @BindView(R.id.notify_bright_music_tv)
            TextView mNameTv;
            @BindView(R.id.notify_bright_music_iv)
            ImageView mSelectIv;

            public ViewHolder(View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing() && music_type > 0) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
            BluetoothManage.getInstance().changeBirghtMode(-1, false);
        }
    }
}
