package com.totwoo.totwoo.activity.together;

import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.activity.LoveSpacePinkActivity.PAIRED_HEAD_URL;
import static com.totwoo.totwoo.activity.LoveSpacePinkActivity.PAIRED_NAMES;
import static com.totwoo.totwoo.activity.together.MainTogetherActivity.TOGETHER_INFO;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.SizeUtils;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.google.gson.Gson;
import com.huawei.hms.maps.CameraUpdate;
import com.huawei.hms.maps.CameraUpdateFactory;
import com.huawei.hms.maps.HuaweiMap;
import com.huawei.hms.maps.MapView;
import com.huawei.hms.maps.OnMapReadyCallback;
import com.huawei.hms.maps.model.BitmapDescriptorFactory;
import com.huawei.hms.maps.model.LatLng;
import com.huawei.hms.maps.model.LatLngBounds;
import com.huawei.hms.maps.model.MarkerOptions;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.bean.TogetherBean;
import com.totwoo.totwoo.bean.TogetherSelectBean;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonShareDialog;
import com.totwoo.totwoo.widget.CommonShareType;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class FootPrintShareActivity extends BaseActivity implements SubscriberListener, OnMapReadyCallback {
    @BindView(R.id.together_share_cl)
    ConstraintLayout mShareInfoCl;
    @BindView(R.id.together_share_map)
    MapView mapView;
    @BindView(R.id.together_me)
    ImageView selfIv;
    @BindView(R.id.together_other)
    ImageView otherIv;
    @BindView(R.id.together_share_country_tv)
    TextView mNationTv;
    @BindView(R.id.together_share_province_tv)
    TextView mProvinceTv;
    @BindView(R.id.together_share_city_tv)
    TextView mCityTv;
    @BindView(R.id.together_share_beyond_tv)
    TextView mBeyondTv;
    @BindView(R.id.together_share_name_tv)
    TextView mNameTv;
    @BindView(R.id.together_share_bottom_cl)
    ConstraintLayout mBottomShareCl;
    private LatLng myLocationLatLng;

    private HuaweiMap hMap;

    private TogetherBean togetherBean;

    @SuppressLint("StringFormatInvalid")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_foot_print_share);
        ButterKnife.bind(this);
        InjectUtils.injectActivity(this);

        ACache aCache = ACache.get(FootPrintShareActivity.this);
        String pairedHeadUrl = getIntent().getStringExtra(PAIRED_HEAD_URL);
        int partner_gender = TextUtils.isEmpty(aCache.getAsString(CommonArgs.PARTNER_GENDER)) ? (1 - owner.getGender()) : Integer.valueOf(aCache.getAsString(CommonArgs.PARTNER_GENDER));
        BitmapHelper.setHead(ToTwooApplication.baseContext, selfIv, ToTwooApplication.owner.getHeaderUrl(), owner.getGender());
        BitmapHelper.setHead(ToTwooApplication.baseContext, otherIv, pairedHeadUrl, partner_gender);

        String info = getIntent().getStringExtra(TOGETHER_INFO);
        LogUtils.e("aab info_str = " + info);
        Gson gson = new Gson();
        togetherBean = gson.fromJson(info, TogetherBean.class);

        if (togetherBean == null) {
            finish();
            return;
        }
        mNationTv.setText(String.valueOf(togetherBean.getCountry_total()));
        mProvinceTv.setText(String.valueOf(togetherBean.getProvince_total()));
        mCityTv.setText(String.valueOf(togetherBean.getCity_total()));
        mBeyondTv.setText(setStyle(getString(R.string.foot_print_beyond_share, togetherBean.getPercentage()), togetherBean.getPercentage()));
        mNameTv.setText(getIntent().getStringExtra(PAIRED_NAMES));


        Bundle mapViewBundle = null;
        if (savedInstanceState != null) {
            mapViewBundle = savedInstanceState.getBundle("MapViewBundleKey");
        }
        mapView.onCreate(mapViewBundle);
        mapView.getMapAsync(this);
    }


    @Override
    public void onMapReady(HuaweiMap huaweiMap) {
        hMap = huaweiMap;
        setMapView();
    }

    private void setMapView() {
        hMap.setMapType(HuaweiMap.MAP_TYPE_NORMAL);
        setPoint((ArrayList<TogetherSelectBean>) togetherBean.getList());
    }


    @OnClick({R.id.together_share_back, R.id.together_share, R.id.together_share_local_iv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.together_share_back:
                finish();
                break;
            case R.id.together_share:
                showShareDialog();
                break;
            case R.id.together_share_local_iv:
                break;
        }
    }

    private void setPoint(ArrayList<TogetherSelectBean> selectBeans) {
        if (selectBeans != null && !selectBeans.isEmpty()) {
            List<LatLng> latLngs = new ArrayList<>();
            for (TogetherSelectBean togetherSelectBean : selectBeans) {
                if (!TextUtils.isEmpty(togetherSelectBean.getLocation())) {
                    String[] strings = togetherSelectBean.getLocation().split(",");
                    LatLng latLng = new LatLng(Double.parseDouble(strings[1]), Double.parseDouble(strings[0]));
                    latLngs.add(latLng);

                    MarkerOptions options = new MarkerOptions()
                            .position(latLng)
                            .icon(BitmapDescriptorFactory.fromResource(R.drawable.location_pink))
                            .draggable(false);
                    hMap.addMarker(options);
                }
            }
            // 创建LatLngBounds.Builder并包含所有点
            adjust(latLngs);
        }
    }

    private void adjust(List<LatLng> latLngs) {

        mapView.setPadding(0, 0, 0, SizeUtils.dp2px(235));

        LatLngBounds.Builder builder = new LatLngBounds.Builder();
        for (LatLng latLng : latLngs) {
            builder.include(latLng);
        }
        LatLngBounds bounds = builder.build();
        // 定义地图的边界填充（以像素为单位）
        int padding = 100; // 根据需要调整
        // 创建相机更新
        CameraUpdate cameraUpdate = CameraUpdateFactory.newLatLngBounds(bounds, padding);
        // 设置相机视角
        hMap.moveCamera(cameraUpdate);
    }

    public static Bitmap getMapAndViewScreenShot(Bitmap bitmap, ViewGroup viewContainer, MapView mapView, View... views) {
        int width = viewContainer.getWidth();
        int height = viewContainer.getHeight();
        final Bitmap screenBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(screenBitmap);
        canvas.drawBitmap(bitmap, mapView.getLeft(), mapView.getTop(), null);

        for (View view : views) {
            view.setDrawingCacheEnabled(true);
            canvas.drawBitmap(view.getDrawingCache(), view.getLeft(), view.getTop(), null);
        }
        return screenBitmap;
    }

    private void toShare(CommonShareType type) {
        hMap.snapshot(bitmap -> {
            Bitmap snapShareBitmap = getMapAndViewScreenShot(bitmap, mShareInfoCl, mapView, mBottomShareCl);
            String path = FileUtils.saveBitmapFromSDCard(snapShareBitmap,
                    "totwoo_cache_img_" + System.currentTimeMillis());
            switch (type) {
                case FRIENDS:
                    ShareUtilsSingleton.getInstance().shareImageToWechatMoment(path);
                    break;
                case WECHAT:
                    ShareUtilsSingleton.getInstance().shareImageToWechat(path);
                    break;
                case WEIBO:
                    ShareUtilsSingleton.getInstance().shareImageToWeibo(FootPrintShareActivity.this, path, "");
                    break;
                case QZONE:
                    ShareUtilsSingleton.getInstance().shareImageToQzone(path, "");
                    break;
                case QQ:
                    ShareUtilsSingleton.getInstance().shareImageToQQ(path);
                    break;
            }
        });
    }

    private boolean isShowing;

    @Override
    public void onResume() {
        super.onResume();
        //在activity执行onResume时执行mMapView.onResume()，重新绘制加载地图
        mapView.onResume();
        isShowing = true;
    }

    @Override
    public void onPause() {
        super.onPause();
        //在activity执行onPause时执行mMapView.onPause()，暂停地图的绘制
        mapView.onPause();
        isShowing = false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            mapView.onDestroy();
        } catch (Exception e) {
            e.printStackTrace();
        }
        InjectUtils.injectUnregisterListenerAll(this);
    }

    private CommonShareDialog shareDialog;

    private void showShareDialog() {
        if (shareDialog == null) {
            List<CommonShareType> types = new ArrayList<>();
            types.add(CommonShareType.FRIENDS);
            types.add(CommonShareType.WECHAT);
            types.add(CommonShareType.WEIBO);
            types.add(CommonShareType.QZONE);
            types.add(CommonShareType.QQ);
            shareDialog = new CommonShareDialog(FootPrintShareActivity.this, types, v -> {
                toShare((CommonShareType) v.getTag());
            });
//            shareDialog.setCustomTitle(CommonUtils.setNumberOrangeSpan(getResources().getString(R.string.share_text_head_info), 88, 17));
            shareDialog.setCancelVisibility(false);
        }
        shareDialog.show();
    }

    private SpannableString setStyle(String string, String inside) {
        int index = string.indexOf(inside);
        int endIndex = index + inside.length();
        SpannableString spannableString = new SpannableString(string);
//        spannableString.setSpan(new AbsoluteSizeSpan(17, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_main)), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        if (isShowing) {
            showNewUserGifDialog();
        }
    }

    private NewUserGiftDialog newUserGiftDialog;

    private void showNewUserGifDialog() {
        newUserGiftDialog = new NewUserGiftDialog(FootPrintShareActivity.this, v -> {
            if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_YESORNO_LUCKY_CLICK);
            } else {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_YESORNO_LUCKY_CLICK);
            }
            WebViewActivity.loadUrl(FootPrintShareActivity.this, HttpHelper.URL_GIFT, false);
            newUserGiftDialog.dismiss();
        }, v -> newUserGiftDialog.dismiss(), CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券", 88, 20), "立即抽奖");
        newUserGiftDialog.show();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

}
