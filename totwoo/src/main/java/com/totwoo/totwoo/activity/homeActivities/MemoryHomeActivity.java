package com.totwoo.totwoo.activity.homeActivities;

import android.os.Bundle;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.CustomMagicFragment;
import com.totwoo.totwoo.fragment.MeFragment;
import com.totwoo.totwoo.fragment.MemoryFragment;

import java.util.ArrayList;

public class MemoryHomeActivity extends HomeBaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Class<? extends BaseFragment>[] baseFragments = new Class[3];
        baseFragments[0] = MemoryFragment.class;
        baseFragments[1] = CustomMagicFragment.class;
        baseFragments[2] = MeFragment.class;

        ArrayList<HomepageBottomInfo> infos = new ArrayList<>();
        infos.add(new HomepageBottomInfo(R.drawable.new_home_sg_un, R.drawable.new_home_sg, R.string.memory));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_magic_un, R.drawable.new_home_magic, R.string.heart));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_me_un, R.drawable.new_home_me, R.string.user));
        super.setBottomInfo(infos);
        super.setFragmentsAndInitViewpager(baseFragments);
        super.setTotwooIndex(-1);
    }
}
