package com.totwoo.totwoo.activity.security;

import static com.totwoo.totwoo.activity.ContactsListActivity.REPORT_CONTACT_LIST;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.ContactsActivityForReport;
import com.totwoo.totwoo.bean.SecurityContactsBean;
import com.totwoo.totwoo.bean.SecurityContactsHttpBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class SecurityReportListActivity extends BaseActivity {
    @BindView(R.id.report_tv)
    TextView mReportTv;
    @BindView(R.id.report_rv)
    RecyclerView mReportRv;
    @BindView(R.id.report_add_cl)
    ConstraintLayout mReportAddCl;
    @BindView(R.id.report_iv)
    ImageView mReportIv;

    private SecurityReportAdapter securityReportAdapter;
//    private ArrayList<SecurityContactsBean> originalBeans;

    private ArrayList<SecurityContactsBean> originalBeans;
    private ArrayList<SecurityContactsBean> changedBeans;
    private DiffUtil.Callback diffUtilCallBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_report_list);
        ButterKnife.bind(this);

        if (Apputils.systemLanguageIsChinese(SecurityReportListActivity.this)) {
            mReportIv.setImageResource(R.drawable.report_banner);
        } else {
            mReportIv.setImageResource(R.drawable.report_banner_en);
        }
        originalBeans = new ArrayList<>();
        changedBeans = new ArrayList<>();
        mReportRv.setLayoutManager(new LinearLayoutManager(this));
        securityReportAdapter = new SecurityReportAdapter();
        mReportRv.setAdapter(securityReportAdapter);
        mReportTv.setText(setStyle());
        diffUtilCallBack = new DiffUtil.Callback() {
            @Override
            public int getOldListSize() {
                return originalBeans.size();
            }

            @Override
            public int getNewListSize() {
                return changedBeans.size();
            }

            @Override
            public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
                return TextUtils.equals(originalBeans.get(oldItemPosition).toString(), changedBeans.get(newItemPosition).toString()) && oldItemPosition == newItemPosition;
            }

            @Override
            public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
                return TextUtils.equals(originalBeans.get(oldItemPosition).toString(), changedBeans.get(newItemPosition).toString());
            }
        };
        getSecurityContacts();
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopTitle(R.string.safe_report_title);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
    }

    private SpannableString setStyle() {
        String string = getString(R.string.safe_report_hint);
        SpannableString spannableString = new SpannableString(string);
        String sub = "3";
        int index = string.indexOf(sub);
        int endIndex = index + sub.length();
        spannableString.setSpan(new AbsoluteSizeSpan(16, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff2e9995")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @OnClick(R.id.report_add_cl)
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.report_add_cl:
                if (!PermissionUtil.hasContactsPermission(SecurityReportListActivity.this)) {
                    return;
                }
                startContactsList();
                break;
        }
    }

    private void startContactsList() {
        Intent intent = new Intent(SecurityReportListActivity.this, ContactsActivityForReport.class);
        if (originalBeans != null && originalBeans.size() > 0) {
            intent.putExtra(REPORT_CONTACT_LIST, originalBeans);
        }
        startActivityForResult(intent, 100);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        getSecurityContacts();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int grantResult : grantResults) {
            if (grantResult != PackageManager.PERMISSION_DENIED) {
                startContactsList();
            }
        }
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }

    private void getSecurityContacts() {
        HttpHelper.safeService.getReportContacts(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<SecurityContactsHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(SecurityReportListActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
                        if (securityContactsHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            handlerSecurityBeans(securityContactsHttpBeanHttpBaseBean);
                        }
                    }
                });
    }

    private void handlerSecurityBeans(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
        changedBeans.clear();
        if (securityContactsHttpBeanHttpBaseBean.getData() != null && securityContactsHttpBeanHttpBaseBean.getData().getInfo() != null && securityContactsHttpBeanHttpBaseBean.getData().getInfo().size() > 0) {
            changedBeans.addAll(securityContactsHttpBeanHttpBaseBean.getData().getInfo());
        }
        updateItems();
        originalBeans.clear();
        originalBeans.addAll(changedBeans);

        if (originalBeans.size() == 3) {
            mReportAddCl.setVisibility(View.GONE);
        } else {
            mReportAddCl.setVisibility(View.VISIBLE);
        }
    }

    private void updateItems() {
//        securityReportAdapter.notifyDataSetChanged();

        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(diffUtilCallBack, true);
        diffResult.dispatchUpdatesTo(securityReportAdapter);

    }

    private void delContact(String phoneNumber) {
        HttpHelper.safeService.delReportContacts(phoneNumber)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<SecurityContactsHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(SecurityReportListActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
                        if (securityContactsHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            handlerSecurityBeans(securityContactsHttpBeanHttpBaseBean);
                        }
                    }
                });
    }

    private CommonMiddleDialog delDialog;

    private void showDelDialog(String phoneNumber) {
        delDialog = new CommonMiddleDialog(this);
        delDialog.setMessage(getString(R.string.safe_contacts_share_content));
        delDialog.setSure(R.string.common_confirm, v -> {
            delDialog.dismiss();
            delContact(phoneNumber);
        });
        delDialog.setCancel(R.string.cancel);
        delDialog.show();
    }

    public class SecurityReportAdapter extends RecyclerView.Adapter<SecurityReportAdapter.ViewHolder> {
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.security_list_report_item, parent, false);
            return new SecurityReportAdapter.ViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            if (TextUtils.equals(originalBeans.get(position).getName(), originalBeans.get(position).getTel())) {
                holder.mPhoneTv.setVisibility(View.GONE);
                holder.mNameTv.setVisibility(View.GONE);
                holder.mPhoneMiddleTv.setVisibility(View.VISIBLE);
            } else {
                holder.mPhoneTv.setVisibility(View.VISIBLE);
                holder.mNameTv.setVisibility(View.VISIBLE);
                holder.mPhoneMiddleTv.setVisibility(View.GONE);
            }
            holder.mNameTv.setText(originalBeans.get(position).getName());
            holder.mPhoneTv.setText(originalBeans.get(position).getTel());
            holder.mPhoneMiddleTv.setText(originalBeans.get(position).getTel());
            holder.mDeleteIv.setOnClickListener(v -> showDelDialog(originalBeans.get(position).getTel()));
        }

        @Override
        public int getItemCount() {
            return originalBeans == null ? 0 : originalBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.security_report_delete_iv)
            ImageView mDeleteIv;
            @BindView(R.id.security_report_phone_tv)
            TextView mPhoneTv;
            @BindView(R.id.security_report_name_tv)
            TextView mNameTv;
            @BindView(R.id.security_report_phone_middle_tv)
            TextView mPhoneMiddleTv;

            public ViewHolder(View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }
}
