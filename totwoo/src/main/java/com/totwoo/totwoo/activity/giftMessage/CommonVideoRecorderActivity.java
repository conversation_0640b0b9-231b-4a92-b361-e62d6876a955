package com.totwoo.totwoo.activity.giftMessage;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Message;
import android.provider.MediaStore;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.VideoSelectActivity;
import com.totwoo.totwoo.record.CameraXRecorder;
import com.totwoo.totwoo.record.RecorderConfig;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.WeakReferenceHandler;
import com.totwoo.totwoo.widget.CircularProgressBar;

import java.io.File;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class CommonVideoRecorderActivity extends BaseActivity implements SubscriberListener, CameraXRecorder.OnRecordStateChangeListener {
    private static final String CACHE_GIFT_VIDEO_COVER = FileUtils.getCacheDir() + "/totwoo_gift_video_cover.jpg";

    private static final int SELECT_LOCAL_VIDEO = 233;

    @BindView(R.id.top_bar_back_btn)
    ImageView backIcon;

    @BindView(R.id.gift_add_video_start_iv)
    ImageView mReVideoIv;

    @BindView(R.id.gift_add_video_start_tv)
    TextView mGuideTextTv;

    @BindView(R.id.video_discount_cpb)
    CircularProgressBar mCpb;

    @BindView(R.id.video_preview)
    PreviewView video_preview;

    @BindView(R.id.gift_add_video_open_iv)
    ImageView videoOpen;

    @BindView(R.id.gift_video_cancel_cl)
    ConstraintLayout mCancelCl;

    CameraXRecorder mRecorder;

    private boolean reVideoing;

    private long reSec;
    private Subscription reVideoStart;

    private RecorderConfig recorderConfig;

    private DisCountHandler handler;
    private long perTime = 100;
    private long allTime = 30000;
    private long endTime;
    private float actionDownY;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gift_add_video);

        recorderConfig = (RecorderConfig) getIntent().getParcelableExtra(RecorderConfig.EXTRA_RECORDER_CONFIG_TAG);
        if (recorderConfig == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        ButterKnife.bind(this);
        CommonUtils.setStateBar(this, true);
        InjectUtils.injectOnlyEvent(this);
        backIcon.setOnClickListener(v -> finish());
        init();

        PermissionUtil.hasAudioPermission(this);
    }

    @Override
    protected void initTopBar() {
    }

    private void init() {
        // 初始化录像机
        mRecorder = new CameraXRecorder(this);
        mRecorder.recorderAudio(recorderConfig.isWithAudio());
        mRecorder.setSuggestOutputSize(recorderConfig.getSuggestWidth(), recorderConfig.getSuggestHeight());
        mRecorder.setCameraPreviewView(video_preview);
        mRecorder.setStateChangeListener(this);
        mRecorder.setFileSizeLimit(recorderConfig.getMaxVideoFileLimit());
        mRecorder.init(this);

        videoOpen.setVisibility(recorderConfig.isSupportAlbum() ? View.VISIBLE : View.GONE);

        handler = new DisCountHandler(CommonVideoRecorderActivity.this);
        mCpb.setProgress(0);
        controlButton();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void controlButton() {
        mReVideoIv.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    actionDownY = event.getRawY();
                    mReVideoIv.setImageResource(R.drawable.gift_video_on);
                    mCpb.setVisibility(View.VISIBLE);
                    reSec = System.currentTimeMillis();
                    endTime = System.currentTimeMillis() + 30000;
                    mGuideTextTv.setText(R.string.gift_video_up_cancel);
                    mGuideTextTv.setCompoundDrawablesRelativeWithIntrinsicBounds(getResources().getDrawable(R.drawable.gift_move_up), null, null, null);
                    mGuideTextTv.setCompoundDrawablePadding(CommonUtils.dip2px(CommonVideoRecorderActivity.this, 4));
                    handler.sendEmptyMessageDelayed(0, perTime);
                    reVideoStart = Observable.timer(300, TimeUnit.MILLISECONDS, Schedulers.newThread())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(aLong -> {
                                if (!reVideoing) {
                                    startRecord();
                                }
                                reVideoing = true;
                            }, throwable -> {
                                LogUtils.e("throwable = " + throwable.toString());
                            });
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (event.getRawY() < actionDownY - 200) {
                        mCancelCl.setVisibility(View.VISIBLE);
                    } else {
                        mCancelCl.setVisibility(View.GONE);
                    }
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    if (!reVideoing) {
                        reVideoStart.unsubscribe();
                    }
                    mCpb.setVisibility(View.GONE);
                    mReVideoIv.setImageResource(R.drawable.gift_video_start);
                    reSec = System.currentTimeMillis() - reSec;
                    if (event.getRawY() < actionDownY - 200) {
                        cancelRecord();
                    } else if (reSec < 1300) {
                        cancelRecord();
                        ToastUtils.showShort(CommonVideoRecorderActivity.this, getString(R.string.record_time_tip));
                    } else {
                        ToastUtils.cancelCustomToast();
                        finishRecord();
                    }
                    mCancelCl.setVisibility(View.GONE);

                    mGuideTextTv.setText(R.string.medio_guide_text);
                    mGuideTextTv.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null);
                    break;
            }
            return true;
        });
    }

    /**
     * 开始录制
     */
    private void startRecord() {
        if (mRecorder.isRecording()) {
            Toast.makeText(this, R.string.make_card_reing_video, Toast.LENGTH_SHORT).show();
            return;
        }
        goneView();

        // initialize video camera
        // 录制视频
        mRecorder.startRecording(CommonArgs.CACHE_GIFT_VIDEO_PATH);
    }

    /**
     * 停止录制
     */
    private void finishRecord() {
        mRecorder.stopRecording();
    }

    private void cancelRecord() {
        mRecorder.stopRecording();
        reStartProgress();
    }

    private void reStartProgress() {
        reSec = 0;
        handler.removeCallbacksAndMessages(null);
        mCpb.setProgress(0);
        allTime = 30000;
        reVideoing = false;
        visibleView();
    }

    /**
     * 寄语编辑页删除
     * GiftInfoAddActivity
     */
    @EventInject(eventType = S.E.E_GIFT_DELETE_INFO, runThread = TaskType.UI)
    public void deleteInfo(EventData data) {
        finish();
    }

    /**
     * 寄语发送成功
     * ContactsSelectActivity
     */
    @EventInject(eventType = S.E.E_GIFT_SEND_SUCCEED, runThread = TaskType.UI)
    public void sendSucceed(EventData data) {
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
        if (recorderConfig != null) {
            recorderConfig.removeTarget();
        }
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @OnClick({R.id.gift_add_video_open_iv, R.id.gift_add_video_change_iv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.gift_add_video_open_iv:
                Intent intent;
                intent = new Intent(Intent.ACTION_PICK, null);
                intent.setDataAndType(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, "video/*");
                startActivityForResult(intent, SELECT_LOCAL_VIDEO);
//
//                startActivityForResult(new Intent(CommonVideoRecorderActivity.this, VideoSelectActivity.class)
//                                .putExtra(VideoSelectActivity.EXTRA_MAX_VIDEO_FILE_SIZE_LIMIT, recorderConfig.getMaxVideoFileLimit()),
//                        0);
                break;
            case R.id.gift_add_video_change_iv:
                if (mRecorder != null) {
                    mRecorder.switchCamera();
                }
                break;
        }
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    public void onStateChange(int state) {
        Log.w("onStateChange", "state = " + state);
        if (state == CameraXRecorder.RECORDER_STATE_ERROR) {
            Log.e("onStateChange", "Error: " + mRecorder.getErrorString());
        }
        if (state == CameraXRecorder.RECORDER_STATE_SUCCESS) {
            checkVideoSize(CommonArgs.CACHE_GIFT_VIDEO_PATH);
        }
    }

    public class DisCountHandler extends WeakReferenceHandler<CommonVideoRecorderActivity> {

        public DisCountHandler(CommonVideoRecorderActivity wishAddVideoActivity) {
            super(wishAddVideoActivity);
        }

        @Override
        public void handleLiveMessage(Message msg) {
            if (allTime > perTime) {
                allTime = endTime - System.currentTimeMillis();
                float percent = allTime / 300f;
                mCpb.setProgress((100 - percent));
                handler.sendEmptyMessageDelayed(0, perTime);
            } else {
                mCpb.setProgress(100);
                finishRecord();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == 0 && resultCode == VideoSelectActivity.VIDEO_SELECT_SUCCESS && data != null) {
            // 自绘选择器选择的视频
            checkVideoSize(data.getStringExtra(VideoSelectActivity.APP_SELECT_VIDEO_PATH));
        } else if (requestCode == SELECT_LOCAL_VIDEO && resultCode == RESULT_OK && data != null) {
            // 系统相册获取的视频
            CommonUtils.copyUriToPath(this, data.getData(), CommonArgs.CACHE_GIFT_VIDEO_PATH);
            checkVideoSize(CommonArgs.CACHE_GIFT_VIDEO_PATH);
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 检测视频是否合法
     *
     * @param path
     */
    private void checkVideoSize(String path) {
        File file = new File(CommonArgs.CACHE_GIFT_VIDEO_PATH);
//        if (!file.exists()) {
//            Toast.makeText(this, R.string.data_error, Toast.LENGTH_LONG).show();
//            return;
//        }
        if (file.length() > recorderConfig.getMaxVideoFileLimit()) {
            Toast.makeText(this, getString(R.string.error_video_size, recorderConfig.getMaxVideoFileLimit() / 1024 / 1024), Toast.LENGTH_LONG).show();
            return;
        }

        // 通过 Glide 获取视频封面
        RequestOptions options = new RequestOptions().placeholder(R.drawable.gift_placeholder).skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE);
        Glide.with(this).asDrawable().load(path).apply(options).addListener(new RequestListener<Drawable>() {
            @Override
            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                LogUtils.e("Load video thumbnail failed.");
                if (recorderConfig.getTarget() != null) {
                    recorderConfig.getTarget().goNext(path, null);
                }
                reStartProgress();
                return true;
            }

            @Override
            public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                FileUtils.saveBitmapToPath(((BitmapDrawable) resource).getBitmap(), CACHE_GIFT_VIDEO_COVER);
                LogUtils.d("Load video thumbnail success.");
                if (recorderConfig.getTarget() != null) {
                    recorderConfig.getTarget().goNext(path, CACHE_GIFT_VIDEO_COVER);
                }
                reStartProgress();
                return true;
            }
        }).submit();

    }

    private void goneView() {
        videoOpen.setVisibility(View.GONE);
    }

    private void visibleView() {
        videoOpen.setVisibility(View.VISIBLE);
    }
}
