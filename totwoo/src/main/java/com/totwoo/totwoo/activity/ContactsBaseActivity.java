package com.totwoo.totwoo.activity;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.blankj.utilcode.util.ClickUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.jakewharton.rxbinding.widget.RxTextView;
import com.jakewharton.rxbinding.widget.TextViewAfterTextChangeEvent;
import com.tencent.bugly.crashreport.CrashReport;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.LocalContactsBean;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SoftKeyBoardListener;
import com.totwoo.totwoo.utils.StringUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.BaseHeaderAdapter;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderEntity;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderItemDecoration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;

public abstract class ContactsBaseActivity extends BaseActivity implements LoaderManager.LoaderCallbacks<Cursor> {
    @BindView(R.id.contact_search_content_cl)
    ConstraintLayout mSearchContentCl;
    @BindView(R.id.contact_search_et)
    EditText mSearchEt;
    @BindView(R.id.contact_input_cl)
    ConstraintLayout mInputCl;
    @BindView(R.id.contact_input_et)
    EditText mInputEt;
    @BindView(R.id.contact_input_city_tv)
    TextView mCityTv;
    @BindView(R.id.top_bar_back_btn)
    ImageView mBackIv;
    @BindView(R.id.top_bar_right_tv)
    TextView mRightTv;
    @BindView(R.id.top_bar_title_view)
    TextView mTitleTv;
    @BindView(R.id.contact_main_rv)
    RecyclerView mMainContactList;
    @BindView(R.id.contact_select_rv)
    RecyclerView mSelectList;
    @BindView(R.id.contact_select_sending_tv)
    TextView mSendingTv;
    @BindView(R.id.card_store_lv)
    LottieAnimationView mCardStoreLv;
    @BindView(R.id.contact_search_hint_bg)
    View hintBg;
    @BindView(R.id.contacts_send_success_bg)
    View mSendSuccessBg;
    @BindView(R.id.contact_empty_text)
    TextView mEmptyTv;
    @BindView(R.id.contacts_add_cl)
    ConstraintLayout mAddCl;
    @BindView(R.id.contacts_add_head_view)
    View mAddHeadView;

    private String mCurFilter;
    private List<PinnedHeaderEntity<LocalContactsBean>> allContactBeans;
    private BaseHeaderAdapter<PinnedHeaderEntity<LocalContactsBean>> mAdapter;
    List<LocalContactsBean> selectBeans;
    private List<String> sortKeys;
    private List<Integer> sortKeysIndex;
    private String countryCode;
    private ContactSelectAdapter contactSelectAdapter;
    int selectSize = -1;
    String selectOutOfIndexMsg;


    @Override
    protected void setupDefaultEdgeToEdge() {
        //设置view 高度
        RelativeLayout topBar = getTopBar();
        if (topBar != null) {
            ViewGroup.LayoutParams layoutParams = topBar.getLayoutParams();
            if (layoutParams != null) {
                layoutParams.height = (int) this.getResources().getDimension(R.dimen.activity_actionbar_height);
                topBar.setLayoutParams(layoutParams);
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_contacts_select);
        ButterKnife.bind(this);



        ClickUtils.applySingleDebouncing(mRightTv, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        clickConfirm();
                    }
                }
        );

        mSearchEt.setFocusable(true);
        mSearchEt.setFocusableInTouchMode(true);
        mInputEt.setFocusable(true);
        mInputEt.setFocusableInTouchMode(true);

        allContactBeans = new ArrayList<>();
        sortKeys = new ArrayList<>();
        sortKeysIndex = new ArrayList<>();
        selectBeans = new ArrayList<>();

        countryCode = PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? "86" : "1");
        mCityTv.setText("+" + countryCode);

        mMainContactList.setLayoutManager(new LinearLayoutManager(ContactsBaseActivity.this));
        mMainContactList.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int i) {
                switch (mAdapter.getItemViewType(i)) {
                    case BaseHeaderAdapter.TYPE_DATA:
                        if (selectSize > 0 && selectBeans.size() >= selectSize) {
                            ToastUtils.showLong(ContactsBaseActivity.this, selectOutOfIndexMsg);
                            return;
                        }
                        PinnedHeaderEntity<LocalContactsBean> entity = mAdapter.getData().get(i);
                        addBean(entity.getData());
                        if (mSearchContentCl.getVisibility() == View.VISIBLE) {
                            mSearchContentCl.setVisibility(View.GONE);
                            hideKeyboard(mSearchEt);
                            mSearchEt.setText("");
                            mCurFilter = null;
                            getSupportLoaderManager().restartLoader(0, null,
                                    ContactsBaseActivity.this);
                        }
                        break;
                    case BaseHeaderAdapter.TYPE_HEADER:
                        entity = mAdapter.getData().get(i);
                        break;
                }
            }
        });
        mMainContactList.addItemDecoration(new PinnedHeaderItemDecoration.Builder(BaseHeaderAdapter.TYPE_HEADER).create());

        contactSelectAdapter = new ContactSelectAdapter();
        mSelectList.setLayoutManager(new LinearLayoutManager(ContactsBaseActivity.this, RecyclerView.HORIZONTAL, false));
        mSelectList.setAdapter(contactSelectAdapter);

        try {
            LoaderManager.getInstance(this).initLoader(0, null, this);
        } catch (Exception e) {
            e.printStackTrace();
        }

        RxTextView.afterTextChangeEvents(mSearchEt)
                .throttleWithTimeout(400, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
                .subscribe(new Action1<TextViewAfterTextChangeEvent>() {
                    @Override
                    public void call(TextViewAfterTextChangeEvent textViewAfterTextChangeEvent) {
                        if (mSearchContentCl.getVisibility() == View.GONE) {
                            return;
                        }
                        String newFilter = textViewAfterTextChangeEvent.editable().toString();
                        if (TextUtils.equals(newFilter, mCurFilter)) {
                            return;
                        }
                        mCurFilter = newFilter;
                        if (TextUtils.isEmpty(mCurFilter)) {
                            hintBg.setVisibility(View.VISIBLE);
                        } else {
                            hintBg.setVisibility(View.GONE);
                        }
                        getSupportLoaderManager().restartLoader(0, null,
                                ContactsBaseActivity.this);
                    }
                });

        SoftKeyBoardListener.setListener(ContactsBaseActivity.this, new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
            @Override
            public void keyBoardShow(int height) {
            }

            @Override
            public void keyBoardHide(int height) {
                goneInputCl();
            }
        });

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CONTACTS) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_CONTACTS}, 333);
        }
    }

    abstract void clickConfirm();

    private String lastKey;
    private int lastCount;

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
        setTopTitle(getString(R.string.select_contact));
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopRightString(R.string.set_password_finish);
    }

    private void initContacts(Cursor cursor) {
        //获取所有联系人
        if (cursor == null || cursor.getCount() == lastCount) {
            return;
        }
        lastCount = cursor.getCount();
        allContactBeans.clear();
        lastKey = null;
        while (cursor.moveToNext()) {
            try {
                //获取联系人sortKey
                String sortKey = StringUtils.getSortKey(cursor.getString(4));
                //分组
                if (!TextUtils.equals(lastKey, sortKey)) {
                    allContactBeans.add(new PinnedHeaderEntity<>(new LocalContactsBean(), BaseHeaderAdapter.TYPE_HEADER, sortKey));
                    lastKey = sortKey;
                    sortKeys.add(sortKey);
                    sortKeysIndex.add(allContactBeans.size() - 1);
                }
                allContactBeans.add(new PinnedHeaderEntity<>(new LocalContactsBean(cursor.getString(0), CommonUtils.getSimplePhone(cursor.getString(1)),
                        sortKey, cursor.getLong(2) > 0 ? cursor.getLong(3) : -1), BaseHeaderAdapter.TYPE_DATA, sortKey));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //展示
        setInfoToAdapter();
        //联系人右方字母列表
//        int sortKeyIndex = sortKeys.indexOf(indexName);
//        mMainContactList.scrollToPosition(sortKeysIndex.get(sortKeyIndex));
    }

    private void setInfoToAdapter() {
        if (mAdapter == null) {
            mAdapter = new BaseHeaderAdapter<PinnedHeaderEntity<LocalContactsBean>>(allContactBeans) {
                @Override
                protected void addItemTypes() {
                    addItemType(BaseHeaderAdapter.TYPE_HEADER, R.layout.contact_list_header_item);
                    addItemType(BaseHeaderAdapter.TYPE_DATA, R.layout.contact_list_data_item);
                }

                @Override
                protected void convert(BaseViewHolder helper, PinnedHeaderEntity<LocalContactsBean> item) {
                    switch (helper.getItemViewType()) {
                        case BaseHeaderAdapter.TYPE_HEADER:
                            helper.setText(R.id.contact_header_tv, item.getPinnedHeaderName());
                            break;
                        case BaseHeaderAdapter.TYPE_DATA:
                            helper.setText(R.id.contact_data_name, item.getData().getName());
                            helper.setText(R.id.contact_data_number, item.getData().getNumber());
                            break;
                    }
                }
            };
            mMainContactList.setAdapter(mAdapter);
        } else {
            if (allContactBeans.size() == 0) {
                mEmptyTv.setVisibility(View.VISIBLE);
            } else {
                mEmptyTv.setVisibility(View.GONE);
            }
            mAdapter.notifyDataSetChanged();
        }
    }

    @OnClick({R.id.contact_search_click_iv, R.id.contact_search_hint_bg, R.id.contact_search_cancel_tv, R.id.contacts_add_cl,
            R.id.contact_input_done_tv, R.id.contact_input_city_tv, R.id.contact_input_cl, R.id.contacts_send_success_bg, R.id.contact_select_help_cl})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.contact_search_click_iv:
                goneInputCl();
                mSearchContentCl.setVisibility(View.VISIBLE);
                mSearchEt.requestFocus();
                mSearchEt.setFocusableInTouchMode(true);
                showKeyboard(mSearchEt);
                break;
            case R.id.contact_search_hint_bg:
                goneSearchCl();
                break;
            case R.id.contact_search_cancel_tv:
                goneSearchCl();
                mSearchEt.setText("");
                mCurFilter = null;
                getSupportLoaderManager().restartLoader(0, null,
                        ContactsBaseActivity.this);
                break;
            case R.id.contacts_add_cl:
                goneSearchCl();
                mSearchEt.setText("");
                mCurFilter = null;
                getSupportLoaderManager().restartLoader(0, null,
                        ContactsBaseActivity.this);
                mInputCl.setVisibility(View.VISIBLE);
                mInputEt.requestFocus();
                showKeyboard(mInputEt);
                break;
            case R.id.contact_input_done_tv:
                if (selectSize > 0 && selectBeans.size() >= selectSize) {
                    ToastUtils.showLong(ContactsBaseActivity.this, selectOutOfIndexMsg);
                    return;
                }
                String inputNumber = mInputEt.getText().toString().trim();
                if (TextUtils.isEmpty(inputNumber)) {
                    ToastUtils.showShort(ContactsBaseActivity.this, R.string.error_invalid_phone);
                    return;
                }
                if (!TextUtils.isDigitsOnly(inputNumber)) {
                    ToastUtils.showLong(ContactsBaseActivity.this, R.string.error_incorrect_phone);
                    return;
                }
                String number = countryCode + mInputEt.getText().toString().trim();
                number = CommonUtils.getSimplePhone(number);

                goneInputCl();
                addBean(new LocalContactsBean(number, "+" + number, StringUtils.getSortKey(number), -1));
                mInputEt.setText("");
                break;
            case R.id.contact_input_city_tv:
                Intent intent = new Intent(ContactsBaseActivity.this, CountryCodeListActivity.class);
                startActivityForResult(intent, 0);

                // 切换动画
                overridePendingTransition(R.anim.activity_fade_in,
                        R.anim.activity_fade_out);
                break;
            case R.id.contact_select_help_cl:
                WebViewActivity.loadUrl(ContactsBaseActivity.this, HttpHelper.URL_HELP_TOTWOO, false);
                break;
            case R.id.contact_input_cl:
            case R.id.contacts_send_success_bg:
                //消费点击事件
                break;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == 333) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                LoaderManager.getInstance(this).restartLoader(0, null, this);
            } else {
                // 权限禁止, 提示开启
                final CustomDialog dialog = new CustomDialog(this);
                dialog.setMessage(R.string.no_contact);
                dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                        dialog.dismiss();
                    }
                });
                dialog.show();
            }
        }
    }

    private void goneInputCl() {
        if (mInputCl.getVisibility() == View.VISIBLE) {
            mInputCl.setVisibility(View.GONE);
            hideKeyboard(mInputEt);
        }
    }

    private void goneSearchCl() {
        if (mSearchContentCl.getVisibility() == View.VISIBLE) {
            mSearchContentCl.setVisibility(View.GONE);
            hideKeyboard(mSearchEt);
        }
    }

    private void hideKeyboard(EditText editText) {
        InputMethodManager img = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        img.hideSoftInputFromWindow(editText.getWindowToken(), 0);
    }

    private void showKeyboard(EditText editText) {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
    }

    @NonNull
    @Override
    public Loader<Cursor> onCreateLoader(int id, @Nullable Bundle args) {
        Uri baseUri;
        if (!TextUtils.isEmpty(mCurFilter)) {
            baseUri = Uri.withAppendedPath(ContactsContract.CommonDataKinds.Phone.CONTENT_FILTER_URI,
                    Uri.encode(mCurFilter));
        } else {
            baseUri = ContactsContract.CommonDataKinds.Phone.CONTENT_URI;
        }

        // Now create and return a CursorLoader that will take care of
        // creating a Cursor for the data being displayed.
        String select = "((" + ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " NOTNULL) AND ("
                + ContactsContract.CommonDataKinds.Phone.HAS_PHONE_NUMBER + "=1))";
        return new CursorLoader(ContactsBaseActivity.this, baseUri,
                new String[]{ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME, ContactsContract.CommonDataKinds.Phone.NUMBER,
                        ContactsContract.Contacts.Photo.PHOTO_ID, ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                        ContactsContract.CommonDataKinds.Phone.SORT_KEY_PRIMARY, ContactsContract.Contacts._ID}, select, null,
                ContactsContract.CommonDataKinds.Phone.SORT_KEY_PRIMARY + " COLLATE LOCALIZED ASC");
    }

    @Override
    public void onLoadFinished(@NonNull Loader<Cursor> loader, Cursor data) {
        try {
            initContacts(data);
        } catch (Exception e) {
            e.printStackTrace();
            // 权限禁止, 提示开启
            CrashReport.postCatchedException(e);
        }

//        if (data.getCount() == 0) {
//            filterNoResultTv.setVisibility(View.VISIBLE);
//            if (TextUtils.isEmpty(mCurFilter)) {
//                filterNoResultTv.setText(R.string.no_contact);
//            } else {
//                filterNoResultTv.setText(getString(R.string.search_no_result, mCurFilter));
//            }
//        } else {
//            filterNoResultTv.setVisibility(View.GONE);
//        }
    }

    @Override
    public void onLoaderReset(@NonNull Loader<Cursor> loader) {
//        initContacts(data);
    }

    private void addBean(LocalContactsBean bean) {
        bean.setNumber(CommonUtils.checkPhoneNumber(bean.getNumber()));
        if (TextUtils.isEmpty(bean.getNumber())) {
            ToastUtils.showLong(this, getString(R.string.phone_number_invalid));
            return;
        }


        // 根据号码去重
        for (LocalContactsBean localContactsBean : selectBeans) {
            if (TextUtils.equals(localContactsBean.getNumber(), bean.getNumber())) {
                selectBeans.remove(localContactsBean);
                ToastUtils.show(getBaseContext(), getString(R.string.cant_choose_repeated_contact), Toast.LENGTH_LONG);
                return;
            }
        }
        if (existentBeans != null && existentBeans.size() > 0) {
            for (LocalContactsBean localContactsBean : existentBeans) {
                if (TextUtils.equals(localContactsBean.getNumber(), bean.getNumber())) {
                    ToastUtils.showShort(ContactsBaseActivity.this, R.string.safe_contacts_same_error);
                    return;
                }
            }
        }

        selectBeans.add(bean);
        contactSelectAdapter.notifyDataSetChanged();
        mSelectList.setVisibility(View.VISIBLE);
        if (mAddCl != null && mAddCl.getVisibility() != View.VISIBLE) {
            mAddHeadView.setVisibility(View.VISIBLE);
        }
    }

    private List<LocalContactsBean> existentBeans;

    public void setExistentBeans(List<LocalContactsBean> beans) {
        existentBeans = new ArrayList<>();
        existentBeans.addAll(beans);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (mInputCl != null
                    && mInputCl.getVisibility() == View.VISIBLE) {
                goneInputCl();
                return true;
            } else if (mSearchContentCl != null &&
                    mSearchContentCl.getVisibility() == View.VISIBLE) {
                goneSearchCl();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (resultCode) {
            case 0:
                if (data != null) {
                    countryCode = data.getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                    mCityTv.setText("+" + countryCode);
                    mInputEt.requestFocus();
                    mHandler.postDelayed(() -> {
                        InputMethodManager img = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
                        img.showSoftInput(mInputEt, 0);
                    }, 150);
                }
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    private class ContactSelectAdapter extends RecyclerView.Adapter<ContactSelectAdapter.ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.contacts_select_item, parent, false);
            return new ContactSelectAdapter.ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            // 添加边界检查，防止IndexOutOfBoundsException
            if (selectBeans == null || position < 0 || position >= selectBeans.size()) {
                return;
            }
            holder.mNameTv.setText(selectBeans.get(position).getName());
            holder.mContentLl.setOnClickListener(v -> {
                if (selectBeans == null || selectBeans.isEmpty()) {
                    return;
                }
                // 添加边界检查，防止IndexOutOfBoundsException
                if (position >= 0 && position < selectBeans.size()) {
                    selectBeans.remove(position);
                    contactSelectAdapter.notifyDataSetChanged();
                    if (selectBeans.size() == 0) {
                        mSelectList.setVisibility(View.GONE);
                        mAddHeadView.setVisibility(View.GONE);
                    }
                }
            });
        }

        @Override
        public int getItemCount() {
            return selectBeans != null ? selectBeans.size() : 0;
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mDeleteIv;
            TextView mNameTv;
            LinearLayout mContentLl;

            public ViewHolder(View itemView) {
                super(itemView);
                mDeleteIv = (ImageView) itemView.findViewById(R.id.contact_select_delete_iv);
                mNameTv = (TextView) itemView.findViewById(R.id.contact_select_name_tv);
                mContentLl = (LinearLayout) itemView.findViewById(R.id.contact_select_ll);
            }
        }
    }
}
