package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.View;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.widget.QRcodeDialog;

import butterknife.ButterKnife;
import butterknife.OnClick;

public class FeedbackQRCodeActivity extends BaseActivity {
    private QRcodeDialog qRcodeDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_feedback_qrcode);
        ButterKnife.bind(this);

    }

    @Override
    protected void initTopBar() {
        setTopLeftIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.opinion_feedback);
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    @OnClick({R.id.help_qrcode_public_iv,R.id.help_qrcode_after_sales_iv})
    public void onClick(View view){
        switch (view.getId()){
            case R.id.help_qrcode_public_iv:
                qRcodeDialog = new QRcodeDialog(FeedbackQRCodeActivity.this,R.drawable.qrcode_public);
                qRcodeDialog.show();
                break;
            case R.id.help_qrcode_after_sales_iv:
                qRcodeDialog = new QRcodeDialog(FeedbackQRCodeActivity.this,R.drawable.qrcode_after_sales);
                qRcodeDialog.show();
                break;
        }
    }
}
