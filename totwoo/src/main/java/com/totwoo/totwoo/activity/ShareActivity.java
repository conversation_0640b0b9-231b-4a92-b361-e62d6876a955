package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.Step;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.data.TotwooLogic;
import com.totwoo.totwoo.utils.DbHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShareViewHelper;

import java.math.BigDecimal;
import java.util.Calendar;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * App的整体分享界面
 * 
 * <AUTHOR>
 * @date 2015年10月8日
 */
public class ShareActivity extends BaseActivity{
	/** 分享的布局文件 */
	@BindView(R.id.share_content_layout)
	RelativeLayout shareContentLayout;
	/** 首饰照片 */
	@BindView(R.id.share_head_layout_image)
	ImageView jewImage;
	/** 用户头像 */
	@BindView(R.id.share_head_icon_image)
	ImageView userHeadImg;
	/** 用户昵称 */
	@BindView(R.id.share_user_name_tv)
	TextView userNickNme;
	/** 当前日期 */
	@BindView(R.id.share_date_tv)
	TextView dateTv;

	/** 心有灵犀次数 */
	@BindView(R.id.share_the_heart_count_tv)
	TextView theHeartCount;
	/** 健步次数 */
	@BindView(R.id.share_step_tv)
	TextView stepCount;
	/** 星座运势 */
	@BindView(R.id.share_constellation_ratingBar)
	RatingBar conRatingBar;

	/** 卡路里消耗 值布局 */
	@BindView(R.id.share_calorie_value_layout)
	LinearLayout calorieLayout;

	/** 卡路里消耗 值 */
	@BindView(R.id.share_calorie_tv)
	TextView calorieTv;

	/** 心情寄语 */
	@BindView(R.id.share_mood_info_tv)
	TextView moodTv;
	/** 心情寄语 */
	@BindView(R.id.share_bottom_image)
	ImageView bottomImg;

    private ShareViewHelper shareHelper;

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.activity_share);
		ButterKnife.bind(this);

		initData();

        // 初始化分享组件
        shareHelper = new ShareViewHelper(this, findViewById(R.id.share_icon_layout));
        shareHelper.setShareContentLayout(shareContentLayout);
        shareHelper.setShareContent(getString(R.string.share_context));
        shareHelper.setShareTite(getString(R.string.share_title_info));
	}

	/**
	 * 初始化相应的数据
	 */
	private void initData() {
//		shareUtils = new ShareUtils(this);
		// 顶部大图
		String jewname = PreferencesUtils.getString(this,
				BleParams.PAIRED_JEWELRY_NAME_TAG, "");
		switch (jewname) {
		case BleParams.JEWELRY_BLE_NAME_MP:
            if(Apputils.systemLanguageIsChinese(this)){
                jewImage.setImageResource(R.drawable.share_head_image_01);
            }else{
                jewImage.setImageResource(R.drawable.share_head_image_01_en);
            }
			break;
		case BleParams.JEWELRY_BLE_NAME_MB:
            if(Apputils.systemLanguageIsChinese(this)){
                jewImage.setImageResource(R.drawable.share_head_image_02);
            }else{
                jewImage.setImageResource(R.drawable.share_head_image_02_en);
            }
			break;
		default:
            if(Apputils.systemLanguageIsChinese(this)){
                jewImage.setImageResource(R.drawable.share_head_image_01);
            }else{
                jewImage.setImageResource(R.drawable.share_head_image_01_en);
            }
		}

		// 日期
		Calendar cal = Calendar.getInstance();

		dateTv.setText(getResources().getStringArray(R.array.month_names_en)[cal
						.get(Calendar.MONTH)] + " " + cal.get(Calendar.DAY_OF_MONTH) + ", "
                + cal.get(Calendar.YEAR));

		// 个人信息
		if (!TextUtils.isEmpty(ToTwooApplication.owner.getHeaderUrl())) {
			BitmapHelper.display(this, userHeadImg,
					ToTwooApplication.owner.getHeaderUrl());
		}
		userNickNme.setText(ToTwooApplication.owner.getNickName());

		// 心有灵犀次数
		if (TextUtils.isEmpty(ToTwooApplication.owner.getPairedId())) {
			theHeartCount.setText("0");
		} else {
			theHeartCount.setText(PreferencesUtils.getInt(this,
					TotwooLogic.THE_HEART_COUNT_TODAY_TAG, 0) + "");
		}

		int steps = 0;
		// 健步
		try {
			Step step = DbHelper.getDbUtils().findById(Step.class,
					Apputils.getZeroCalendar(null).getTimeInMillis());
			if (step != null) {
				steps = step.getSteps();
			}
		} catch (DbException e) {
			e.printStackTrace();
		}

		stepCount.setText(steps + "");
		// 国际版的相应调整
		if (!Apputils.systemLanguageIsChinese(this)) {
			moodTv.setVisibility(View.INVISIBLE);
			conRatingBar.setVisibility(View.GONE);
			calorieLayout.setVisibility(View.VISIBLE);
			calorieTv.setText(new BigDecimal(getCaliore(steps)).setScale(0,
					BigDecimal.ROUND_HALF_UP) + "");
			bottomImg.setImageResource(R.drawable.share_bottom_img_en);

		} else {
			// 星座
			moodTv.setVisibility(View.VISIBLE);
			conRatingBar.setVisibility(View.VISIBLE);
//			conRatingBar.setRating(PreferencesUtils.getInt(this,
//                    HomeActivity.HOME_CONSTELLATION_RATE_TAG, 0));
//			moodTv.setText(PreferencesUtils.getString(this,
//                    HomeActivity.HOME_CONSTELLATION_SUMMERY_TAG, ""));

			calorieLayout.setVisibility(View.GONE);
		}
	}

	/**
	 * 根据步数, 获得对应的卡路里消耗, 单位(千卡)
	 * 
	 * @return
	 */
	private float getCaliore(int step) {
		return (70.5f * ToTwooApplication.owner.getHeight() / 185f * step / 100000)
				* 1.036f * ToTwooApplication.owner.getWeight();
	}

	@Override
	protected void initTopBar() {
		setTopBackIcon(R.drawable.back_icon_black);
	}
    

	@Override
	protected void onPause() {
		if (shareHelper != null) {
            shareHelper.showLoadingDialog(false);
		}
		super.onPause();
	}

	@Override
	protected void onStop() {
		// if (snapShareBitmap != null) {
		// snapShareBitmap.recycle();
		// snapShareBitmap = null;
		// }
		super.onStop();

	}

	@Override
	protected void onDestroy() {
//		if (snapShareBitmap != null) {
//			snapShareBitmap.recycle();
//			snapShareBitmap = null;
//		}
//		jewImage.destroyDrawingCache();
		super.onDestroy();
	}
}
