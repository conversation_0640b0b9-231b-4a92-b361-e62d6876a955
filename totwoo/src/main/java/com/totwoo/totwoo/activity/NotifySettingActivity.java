package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.CommonArgs.NOTIFY_CALL_COLOR_TAG;
import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hjq.shape.view.ShapeTextView;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.Sedentary;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomMiddleTextDialog;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

/**
 * 所有提醒设置的开关界面
 * <p>
 * Created by lixingmao on 2017/2/17.
 */

public class NotifySettingActivity extends BaseActivity {
    public static final String NOTIFY_TYPE_TAG = "notify_type_tag";

    public static final int TYPE_TOTTWOO = 0;
    public static final int TYPE_CALL = 1;
    public static final int TYPE_SEDENTARY = 2;
    public static final int TYPE_STEP = 3;
    public static final int TYPE_FORTUNE = 4;
    public static final int TYPE_PERIOD = 5;
    public static final int TYPE_WATER = 6;
    public static final int TYPE_WISH = 7;
    public static final int TYPE_GIFT = 8;

    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.call_switch_info_tv)
    TextView mCallSwitchInfoTv;
    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    @BindView(R.id.notify_switch_click_item)
    RelativeLayout mNotifySwitchClickItem;
    @BindView(R.id.notify_switch_click_fill_view)
    View mNotifySwitchfillView;
    @BindView(R.id.make_card_sample_subtitle)
    TextView mMakeCardSampleSubtitle;
    @BindView(R.id.notify_color_layoout)
    LinearLayout mNotifyColorLayoout;
    @BindView(R.id.long_vibration_tv)
    ShapeTextView mLongVibrationTv;
    @BindView(R.id.short_vibration_tv)
    ShapeTextView mShortVibrationTv;


    @BindView(R.id.short_vibration_iv)
    View mShortVibrationIv;

    @BindView(R.id.long_vibration_iv)
    View mLongVibrationIv;


    @BindView(R.id.notify_vibration_layout)
    ConstraintLayout mNotifyVibrationLayout;
    @BindView(R.id.notify_setting_content)
    LinearLayout mNotifySettingContent;

    @BindView(R.id.notify_bright_type_layout)
    LinearLayout mBrightTypeLayout;

    @BindView(R.id.notify_setting_color_library_rv)
    RecyclerView colorLibraryRecyclerView;

    @BindView(R.id.app_notify_fill_view)
    View bqfillView;

    @BindView(R.id.app_notify_tv)
    TextView bqTv;

    @BindView(R.id.app_notify_bq)
    LinearLayout bqView;

    private int notifyType;
    private JewelryNotifyModel nowSetModel;

    private Intent resultData;

    private int retryCount;
    private CustomColorLibraryAdapter colorLibraryAdapter;

    CustomMiddleTextDialog customMiddleTextDialog;
    private boolean jewelryGlitterEnabled;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notify_setting);
        ButterKnife.bind(this);

        resultData = getIntent();

        notifyType = resultData.getIntExtra(NOTIFY_TYPE_TAG, 0);

        setTopBackIcon(R.drawable.back_icon_black);
//        setTopbarBackground(R.color.layer_bg_white);
        setTopLeftOnclik(v -> notifyAndFinish());
        if (notifyType == TYPE_TOTTWOO && BleParams.isCodeJewelry()) {
            setTopRightIcon(R.drawable.help_icon);
            setTopRightOnClick(v -> customMiddleTextDialog.show());
            customMiddleTextDialog = new CustomMiddleTextDialog(this);
            customMiddleTextDialog.setTitleTv(getString(R.string.morse_code_title), true);
            customMiddleTextDialog.setInfoText(getString(R.string.morse_code_content));
            customMiddleTextDialog.setConfirmTv(getString(R.string.i_know), v -> customMiddleTextDialog.dismiss());
            mNotifyVibrationLayout.setVisibility(View.GONE);
        }

        initView(notifyType);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (notifyType == TYPE_FORTUNE) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_FORTUNE_NOTIFY_CHANGE, null);
        } else if (notifyType == TYPE_GIFT) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_GIFT_NOTIFY_CHANGE, null);
        } else if (notifyType == TYPE_STEP) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_STEP_NOTIFY_CHANGE, null);
        }
    }

    private void notifyAndFinish() {
        if (notifyType == TYPE_WISH) {
            HttpHelper.wishService.setWishNotify(nowSetModel.isNotifySwitch() ? 1 : 0, nowSetModel.getFlashColor(), nowSetModel.getVibrationHttpString())
                    .compose(HttpHelper.<HttpBaseBean<Object>>rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                            if (objectHttpBaseBean.getErrorCode() == 0) {
                                finish();
                            }
                        }
                    });
        } else {
            finish();
        }
    }

    @Override
    public void onBackPressed() {
        notifyAndFinish();
    }

    /**
     * 根据界面类型初始化界面
     *
     * @param notifyType
     */
    private void initView(int notifyType) {

        String title = "";
        switch (notifyType) {
            case TYPE_TOTTWOO:
                nowSetModel = NotifyUtil.getTotwooNotifyModel(this);
                title = getString(R.string.notify_remind_totwoo_title);
                if (BleParams.isCodeJewelry()) {
                    mMakeCardSampleSubtitle.setText(setStyle());
                    mMakeCardSampleSubtitle.setOnClickListener(v -> customMiddleTextDialog.show());
                } else {
                    mMakeCardSampleSubtitle.setText(R.string.notify_remind_totwoo_set_dialog_title);
                }
                mBrightTypeLayout.setVisibility(View.VISIBLE);
                findViewById(R.id.notify_vibration_layout).setVisibility(View.GONE);
                bqTv.setVisibility(View.VISIBLE);
                bqView.setVisibility(View.VISIBLE);
                break;
            case TYPE_CALL:
                mNotifySwitchfillView.setVisibility(View.GONE);
                mNotifySwitchClickItem.setVisibility(View.GONE);
                mNotifyVibrationLayout.setVisibility(View.GONE);

                // 电话提醒只关心颜色
                nowSetModel = new JewelryNotifyModel(true, getIntent().getStringExtra(NOTIFY_CALL_COLOR_TAG), 0);

                title = getString(R.string.jewelry_message_notification_mode);

                mMakeCardSampleSubtitle.setText(getString(R.string.call_remind_dialog_title));

                break;
            case TYPE_SEDENTARY:
                nowSetModel = NotifyUtil.getSedentaryNotifyModel(this);

                title = getString(R.string.sedentary_reminder);
                mMakeCardSampleSubtitle.setText(R.string.notify_remind_sedentary_set_dialog_title);

                break;
            case TYPE_STEP:
                nowSetModel = NotifyUtil.getStepNotifyModel(this);
                title = getString(R.string.notify_remind_step_title);
                mMakeCardSampleSubtitle.setText(R.string.notify_remind_step_set_dialog_title);
                if (BleParams.isSecurityJewlery()) {
                    findViewById(R.id.notify_color_layout_content).setVisibility(View.GONE);
                    findViewById(R.id.notify_vibration_layout).setVisibility(View.GONE);
                    mMakeCardSampleSubtitle.setText(R.string.step_notify_instructions);
                }
                break;
            case TYPE_FORTUNE:
                nowSetModel = NotifyUtil.getFortuneNotifyModel(this);
                title = getString(R.string.notify_remind_fortune_title);
                if (BleParams.isButtonBatteryJewelry()) {
                    mMakeCardSampleSubtitle.setText(R.string.notify_remind_fortune_set_dialog_title_no_vibrate);
                } else {
                    mMakeCardSampleSubtitle.setText(R.string.notify_remind_fortune_set_dialog_title);
                }
                if (BleParams.isButtonBatteryJewelry()) {
                    findViewById(R.id.notify_vibration_layout).setVisibility(View.GONE);
                }
                break;
            case TYPE_GIFT:
                nowSetModel = NotifyUtil.getGiftNotifyModel(this);
                title = getString(R.string.notify_remind_gift_title);
                if (BleParams.isButtonBatteryJewelry()) {
                    mMakeCardSampleSubtitle.setText(R.string.notify_remind_gift_set_dialog_title_no_vibrate);
                } else {
                    mMakeCardSampleSubtitle.setText(R.string.notify_remind_gift_set_dialog_title);
                }
                if (BleParams.isButtonBatteryJewelry()) {
                    findViewById(R.id.notify_vibration_layout).setVisibility(View.GONE);
                }
                break;
            case TYPE_PERIOD:
                nowSetModel = NotifyUtil.getPeriodNotifyModel(this);
                title = getString(R.string.period_notification);
                mMakeCardSampleSubtitle.setText(R.string.period_notify_instructions);

                break;
            case TYPE_WATER:
                nowSetModel = NotifyUtil.getWaterTimeNotifyModel(this);
                title = getString(R.string.water_time_reminder);
                mMakeCardSampleSubtitle.setText(R.string.water_time_notify_instructions);
                break;
            case TYPE_WISH:
                nowSetModel = NotifyUtil.getWishNotifyModel(this);
                title = getString(R.string.wish_notification);
                mMakeCardSampleSubtitle.setText(R.string.wish_notify_instructions);
                break;
        }

        setTopTitle(title);


        if (nowSetModel == null) {
            return;
        }

        mCallSwitchCb.setChecked(nowSetModel.isNotifySwitch());
        mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);

        if (!nowSetModel.isNotifySwitch()) {
            mNotifySettingContent.setVisibility(View.GONE);
        }

        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                setTextColorBtn(false);
                break;
        }

        jewelryGlitterEnabled = !(BleParams.isCtJewlery() || BleParams.isMWJewlery()) || PreferencesUtils.getBoolean(this, "jewelry_glitter_enabled", true);
        if (!jewelryGlitterEnabled) {
            colorLibraryRecyclerView.setAlpha(0.4f);
        } else {
            colorLibraryRecyclerView.setAlpha(1f);
        }

        int spanCount = BleParams.isCtJewlery() ? 7 : 6;
        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(NotifySettingActivity.this, spanCount));
        colorLibraryAdapter = new CustomColorLibraryAdapter(nowSetModel.getFlashColor(), spanCount, false,false);

        colorLibraryAdapter.setOnItemClickListener((BaseQuickAdapter adapter, View view, int position) -> {
            if (!jewelryGlitterEnabled) {
                ToastUtils.showLong(NotifySettingActivity.this, R.string.enable_notification_can_flash);
                return;
            }
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);
            if (colorLibraryBean != null) {
                nowSetModel.setFlashColor(colorLibraryBean.getColor());//颜色名字
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                saveNowModel(false);
            }
        });

        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
    }

    @OnClick({R.id.notify_switch_click_item,
            R.id.long_vibration_tv, R.id.short_vibration_tv,
            R.id.app_notify_header_bq1, R.id.app_notify_header_bq2,
            R.id.app_notify_header_bq3, R.id.app_notify_header_bq4})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.app_notify_header_bq1:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(NotifySettingActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                BluetoothManage.getInstance().receiveFace(6);
                break;
            case R.id.app_notify_header_bq2:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(NotifySettingActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                BluetoothManage.getInstance().receiveFace(2);
                break;
            case R.id.app_notify_header_bq3:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(NotifySettingActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                BluetoothManage.getInstance().receiveFace(3);
                break;
            case R.id.app_notify_header_bq4:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(NotifySettingActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                BluetoothManage.getInstance().receiveFace(4);
                break;
            case R.id.notify_switch_click_item:
                if (notifyType == TYPE_SEDENTARY || notifyType == TYPE_STEP) {
                    if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                        ToastUtils.showShort(NotifySettingActivity.this, R.string.error_jewelry_connect);
                        return;
                    }
                }
                mCallSwitchCb.setChecked(!mCallSwitchCb.isChecked());
                nowSetModel.setNotifySwitch(mCallSwitchCb.isChecked());
                saveNowModel(true);
                break;
            case R.id.long_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
                saveNowModel(false);
                setTextColorBtn(true);
                break;

            case R.id.short_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                saveNowModel(false);
                setTextColorBtn(false);
                break;
        }
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationIv.setVisibility(View.VISIBLE);
            mShortVibrationIv.setVisibility(View.GONE);

            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        } else {
            mLongVibrationIv.setVisibility(View.GONE);
            mShortVibrationIv.setVisibility(View.VISIBLE);

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        }
    }

    /**
     * 保存对应的数据, 根据不同的类型, 做相应的首饰反馈
     *
     * @param isSwitch 是否是开关操作
     */
    private void saveNowModel(boolean isSwitch) {
        if (!ToTwooApplication.isDebug && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.show(NotifySettingActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_LONG);
            return;
        }
        switch (notifyType) {
            case TYPE_TOTTWOO:
                if (!isSwitch) {
                    if (BleParams.isCodeJewelry()) {
                        BluetoothManage.getInstance().notifyMorseCode(nowSetModel.getVibrationSeconds(), NotifyUtil.getColorValue(nowSetModel.getFlashColor()));
                    } else {
                        BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                    }
                }
                NotifyUtil.setTotwooNotify(this, nowSetModel);
                break;

            case TYPE_CALL:
                // 界面 finish 的时候处理
                resultData.putExtra(NOTIFY_CALL_COLOR_TAG, nowSetModel.getFlashColor());
                setResult(RESULT_OK, resultData);

                if (!isSwitch) {
                    BluetoothManage.getInstance().notifyJewelry(LONG_VIBRATION_SEC, nowSetModel.getFlashColorValue());
                }

                break;

            case TYPE_SEDENTARY:
                if (isSwitch) {
                    final Sedentary sed = nowSetModel.isNotifySwitch() ? Sedentary.getSedentaryData(this) : new Sedentary(false, 0, 0, 0, "");

                    BluetoothManage.getInstance().setSedentaryReminder(sed);
                    NotifyUtil.setSedentaryNotify(NotifySettingActivity.this, nowSetModel);

                } else {
                    BluetoothManage.getInstance().setSedentaryNotify(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                    NotifyUtil.setSedentaryNotify(NotifySettingActivity.this, nowSetModel);
                }

                break;
            case TYPE_STEP:
                if (isSwitch) {
                    final int target = nowSetModel.isNotifySwitch() ? ToTwooApplication.owner.getWalkTarget() : 0;
                    BluetoothManage.getInstance().setStepTarget(target);
                    if (BleParams.isSecurityJewlery()) {
                        nowSetModel.setFlashColor("WHITE");
                    }
                    NotifyUtil.setStepNotify(NotifySettingActivity.this, nowSetModel);
                } else {
                    BluetoothManage.getInstance().setStepTargetNotify(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                    if (BleParams.isSecurityJewlery()) {
                        nowSetModel.setFlashColor("WHITE");
                    }
                    NotifyUtil.setStepNotify(NotifySettingActivity.this, nowSetModel);
                }
                break;

            case TYPE_FORTUNE:
                if (!isSwitch) {
                    BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                }
                NotifyUtil.setFortuneNotify(this, nowSetModel);
                break;
            case TYPE_GIFT:
                if (!isSwitch) {
                    BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                }
                NotifyUtil.setGiftNotify(this, nowSetModel);
                break;
            case TYPE_PERIOD:
                if (!isSwitch) {
                    BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                }
                NotifyUtil.setPeriodNotify(this, nowSetModel);
                break;
            case TYPE_WATER:
                if (!isSwitch) {
                    BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                }
                NotifyUtil.setWaterTimeNotify(this, nowSetModel);
                break;
            case TYPE_WISH:
                if (!isSwitch) {
                    BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                }
                NotifyUtil.setWishNotify(this, nowSetModel);
                break;
        }

        if (isSwitch) {
            mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
            //切换时候的动画
            Animation anim = AnimationUtils.loadAnimation(this, nowSetModel.isNotifySwitch() ? R.anim.layout_open : R.anim.layout_close);
            if (nowSetModel.isNotifySwitch()) {
                mNotifySettingContent.setVisibility(View.VISIBLE);
            } else {
                anim.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        mNotifySettingContent.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
            }
            mNotifySettingContent.startAnimation(anim);
        }
    }

    private SpannableString setStyle() {
        String string = getString(R.string.notify_remind_totwoo_set_morse_dialog_title);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = getString(R.string.notify_remind_totwoo_set_morse);
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(14, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#000000")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }
}
