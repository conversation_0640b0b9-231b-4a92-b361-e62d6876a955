package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.graphics.Point;
import android.os.Bundle;
import android.os.Handler;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.Sedentary;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.OnConfirmListener;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.BirthSettingView;
import com.totwoo.totwoo.widget.GenderSettingView;
import com.totwoo.totwoo.widget.HeithtSettingView;
import com.totwoo.totwoo.widget.WeightSettingView;
import com.totwoo.totwoo.widget.WheelView;

import java.util.Arrays;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 用户基本信息设置界面，主要对首次注册的用户设置性别、身高、体重等信息</br> 由可重用的几个 Fragment组成， 用户单个信息设置即用单独
 * Fragment即可</br></br> 顺序定义四个界面（性别， 身高，体重，生日）为0,1,2,3；以便顺序执行
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class UserInfoSettingActivity extends BaseActivity {
//    public static final String IN_FINISH_SETTING_TAG = "is_finish_setting_tag";
    /**
     * 标题 （性别， 身高， 体重， 生日）
     */
    @BindView(R.id.setting_title_tv)
    TextView mTitleView;

    /**
     * 下一步按钮
     */
    @BindView(R.id.setting_navigation_next_step_btn)
    TextView mNextView;

    /**
     * 上一步按钮
     */
    @BindView(R.id.setting_navigation_last_step_btn)
    TextView mLastView;

    /**
     * 连接首饰 按钮
     */
    @BindView(R.id.setting_navigation_connecting_jewelry_btn)
    TextView mConnectView;

    @BindView(R.id.setting_info_main_layout)
    FrameLayout mainLayout;

    @BindView(R.id.setting_info_)
    TextView setting_info_;

    /**
     * 当前Fragment 对应的序号
     */
    private int currViewIndex;

    /**
     * 当前主 View
     */
    private View currViw;

    /**
     * 当前View 小人左上角的点， 作为下一个界面动画的起点用
     */
    private Point point;

    /**
     * 没连接首饰需要设置的属性界面序号
     */
    private int[] unConnectionInfoSetting = new int[]{2, 5};

    private int[] unSettingBaseInfo = new int[]{};

    private WheelView wheelView;
    boolean checkState;
    public static final String IS_COUNTER_TARGET = "is_counter_target";
    public static final String IS_SHOULD_OPEN = "is_should_open";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_user_info_setting);
        ButterKnife.bind(this);
        checkState = getIntent().getBooleanExtra("checkstate", false);
        LogUtils.w("checkState :" + checkState);
        int height = getIntent().getIntExtra("height", 0);
        int weight = getIntent().getIntExtra("weight", 0);
        LogUtils.e("height:" + height);
        LogUtils.e("weight:" + weight);
        if (height < 0 && weight < 0) {
            unSettingBaseInfo = new int[]{3, 4};
        } else if (height < 0) {
            unSettingBaseInfo = new int[]{3};
        } else if (weight < 0) {
            unSettingBaseInfo = new int[]{4};
        }

        if (unSettingBaseInfo.length != 0) {
            currViewIndex = unSettingBaseInfo[0];
        }
        if (unSettingBaseInfo.length > 0) {
            mLastView.setVisibility(View.GONE);
        }
        initMainView(currViewIndex, point);
        setSpinState(false);
    }

    @Override
    protected void onResume() {
        LogUtils.i("onResume", "UserInfoSettingActivity");
        super.onResume();
    }

    /**
     * 根据需要展示对应的View
     *
     * @param index
     */
    private void initMainView(int index, Point left_top) {
        if (unSettingBaseInfo.length == 0 && !checkState) {
            index = unConnectionInfoSetting[index];
        }
        LogUtils.e("unSettingBaseInfo.length:" + unSettingBaseInfo.length);
        LogUtils.e("index:" + index);
        String title = null;
        switch (index) {
            case 0://健步目标设置
                wheelView = new WheelView(this);
                wheelView.setOverScrollMode(ScrollView.OVER_SCROLL_NEVER);
                wheelView.setLayoutParams(new FrameLayout.LayoutParams(Apputils.dp2px(this, 115f), wheelView.getItemHetght() * 05, Gravity.CENTER));
                wheelView.setItems(ConfigData.STEP_TARGETS, 5, " Steps");
//                wheelView.setOnWheelViewListener(new WheelView.OnWheelViewListener() {
//                    @Override
//                    public void onSelected(WheelView wheelView, int selectedIndex, String item) {
//
//                    }
//                });
                wheelView.setSeletion(7);
                currViw = wheelView;
                title = getString(R.string.user_info_setting_step_tag_title);
                setting_info_.setText(R.string.user_info_setting_step_tag_info);
                break;
            case 1:// 久坐提醒时长设置
                wheelView = new WheelView(this);
                wheelView.setOverScrollMode(ScrollView.OVER_SCROLL_NEVER);
                wheelView.setLayoutParams(new FrameLayout.LayoutParams(Apputils.dp2px(this, 115f), wheelView.getItemHetght() * 03, Gravity.CENTER));
                wheelView.setItems(Arrays.asList(ConfigData.SIT_WHENLONG_ARRAY), 3, " min.");
//                wheelView.setOnWheelViewListener(new WheelView.OnWheelViewListener() {
//                    @Override
//                    public void onSelected(WheelView wheelView, int selectedIndex, String item) {
//                    }
//                });
                wheelView.setSeletion((SedentaryReminderActivity.DEFAULT_SEDENTARY_TIME - Integer.parseInt(ConfigData.SIT_WHENLONG_ARRAY[0])) / 10);
                currViw = wheelView;
                title = getString(R.string.user_info_setting_sedentary_title);
                setting_info_.setText(R.string.user_info_setting_sedentary_info);
                break;
            case 2:
                currViw = new GenderSettingView(this, null, left_top);
                title = getString(R.string.user_info_setting_gender_title);
                setting_info_.setText(R.string.user_info_setting_gender_info);
                break;
            case 3:
                currViw = new HeithtSettingView(this, null, left_top);
                title = getString(R.string.user_info_setting_height_title);
                setting_info_.setText(R.string.user_info_setting_height_info);
                break;
            case 4:
                currViw = new WeightSettingView(this, null, left_top);
                title = getString(R.string.user_info_setting_weight_title);
                setting_info_.setText(R.string.user_info_setting_weight_info);
                break;
            case 5:
                currViw = new BirthSettingView(this);
                title = getString(R.string.user_info_setting_birth_title);
                setting_info_.setText(R.string.user_info_setting_birthday_info);
                break;
        }

        if (currViw != null) {
            mainLayout.removeAllViews();
            if (currViw instanceof WheelView) {
                mainLayout.addView(currViw);
            } else {
                mainLayout.addView(currViw, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            }

            if (currViw != null && currViw instanceof OnConfirmListener) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        ((OnConfirmListener) currViw).loadAnim();
                    }
                }, 8);
            }
        }
        if (title != null) {
            mTitleView.setText(title);
        }

        if (unSettingBaseInfo.length != 0) {
            updateNavigation1(index);
        } else {
            updateNavigation(index);
        }

        if (index == 5) {
            mNextView.setText(R.string.next_step);
        } else if (unSettingBaseInfo.length == 1 || (unSettingBaseInfo.length == 2 && index == 4)) {
            mNextView.setText(R.string.confirm);
        } else {
            mNextView.setText(R.string.next_step);
        }
    }

    private void updateNavigation1(int index) {
        if (unSettingBaseInfo.length == 2 && index == 4) {
            mLastView.setVisibility(View.VISIBLE);
            mNextView.setVisibility(View.VISIBLE);
            mConnectView.setVisibility(View.GONE);
        } else {
            mLastView.setVisibility(View.GONE);
            mNextView.setVisibility(View.VISIBLE);
            mConnectView.setVisibility(View.GONE);
        }
    }

    /**
     * 更新对应的导航按钮
     *
     * @param index
     */
    private void updateNavigation(int index) {
        switch (index) {
            case 0://健步目标
                mLastView.setVisibility(View.GONE);
                mNextView.setVisibility(View.VISIBLE);
                break;
            case 1://久坐提醒时长
                mLastView.setVisibility(View.VISIBLE);
                mNextView.setVisibility(View.VISIBLE);
                break;
            case 2://性别
                mNextView.setVisibility(View.GONE);
                if (!checkState) {
                    mLastView.setVisibility(View.GONE);
                }
                mConnectView.setVisibility(View.GONE);
                break;
            case 3:            // 身高体重设置，显示上一步， 下一步
            case 4:
                mLastView.setVisibility(View.VISIBLE);
                mNextView.setVisibility(View.VISIBLE);
                mConnectView.setVisibility(View.GONE);
                break;
            case 5:// 生日设置， 显示上一步及连接首饰按钮
                mLastView.setVisibility(View.VISIBLE);
                mNextView.setVisibility(View.VISIBLE);
                mConnectView.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 下一步点击
     */
    @OnClick(R.id.setting_navigation_next_step_btn)
    public void goNext(View v) {
        // 调用接口， 保存信息
        if (currViw != null && currViw instanceof OnConfirmListener) {
            ((OnConfirmListener) currViw).onSaved();
            point = ((OnConfirmListener) currViw).getCenterPoint();
        }
        //保存设置信息
        if (currViw instanceof WheelView) {
            int parseInt = Integer.parseInt(wheelView.getSeletedItem());
            switch (currViewIndex) {
                case 0:
                    PreferencesUtils.put(UserInfoSettingActivity.this, StepTargetSettingActivity.STEP_TARGET, parseInt);
                    ToTwooApplication.owner.setWalkTarget(parseInt);
                    SysLocalDataBean.sycSetTargetData();

                    BluetoothManage.getInstance().setStepTarget(parseInt);
                    break;
                case 1:
                    PreferencesUtils.put(UserInfoSettingActivity.this, "sitWhenlong", parseInt);
                    BluetoothManage.getInstance().setSedentaryReminder(new Sedentary(true, parseInt, DateUtil.getStringToDate("HH:mm", "09:00"),
                            DateUtil.getStringToDate("HH:mm", "18:00"), "12345"));
                    break;
            }
        }

        if ((unSettingBaseInfo.length == 2 && currViewIndex == 4) || unSettingBaseInfo.length == 1) {
            if (getIntent().getBooleanExtra(IS_COUNTER_TARGET, false)) {
                startActivity(new Intent(this, StepCounterActivity.class));
            } else {
                if (getIntent().getBooleanExtra(IS_SHOULD_OPEN, false)) {
                    if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
                        JewelryNotifyModel model = NotifyUtil.getStepNotifyModel(this);
                        model.setNotifySwitch(true);
                        NotifyUtil.setStepNotify(this, model);
                    } else {
                        ToastUtils.showShort(this, R.string.error_jewelry_connect);
                    }
                }
                startActivity(new Intent(this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_STEP));
            }
            overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
            finish();
            return;
        }

        //说明到了最后一个界面
        if (currViewIndex == 5 || (!checkState && currViewIndex == unConnectionInfoSetting.length - 1)) {

//            if(PreferencesUtils.getBoolean(UserInfoSettingActivity.this,CommonArgs.NEW_USER_BENFIT_SHARE,false) && Apputils.systemLanguageIsChinese(UserInfoSettingActivity.this)){
//                startActivity(new Intent(UserInfoSettingActivity.this,NewUserBenfitActivity.class));
//                ToTwooApplication.owner.setNew(false);
//            }else{
                HomeActivityControl.getInstance().openHomeActivity(this);
            overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);

                ToTwooApplication.owner.setNew(false);
//            }
            finish();
            return;
        }

        currViewIndex++;
        initMainView(currViewIndex, point);
    }

    /**
     * 上一步点击
     */
    @OnClick(R.id.setting_navigation_last_step_btn)
    public void goLast(View v) {
        if (currViw != null && currViw instanceof OnConfirmListener) {
            point = ((OnConfirmListener) currViw).getCenterPoint();
        }
        currViewIndex--;
        initMainView(currViewIndex, point);
    }

//    /**
//     * 连接首饰
//     */
//    @OnClick(R.id.setting_navigation_connecting_jewelry_btn)
//    public void goConnect(View v) {
//        // 调用接口， 保存信息
//        if (currViw != null && currViw instanceof OnConfirmListener) {
//            ((OnConfirmListener) currViw).onSaved();
//        }
//        startActivity(new Intent(this, JewelryConnectActivity.class));
//        overridePendingTransition(R.anim.activity_fade_in,
//                R.anim.activity_fade_out);
//        finish();
//    }

    /**
     * 返回键， 依次向前推进界面， 到第一个界面之后推出当前Activity
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (unSettingBaseInfo.length != 0) {
                if (unSettingBaseInfo.length == 2 && currViewIndex == 4) {
                    currViewIndex--;
                    initMainView(currViewIndex, point);
                    return true;
                } else {
                    finish();
                    return true;
                }
            }

            if (currViewIndex != 0) {
                currViewIndex--;
                initMainView(currViewIndex, point);
                return true;
            }
        }

        return super.onKeyDown(keyCode, event);
    }

}
