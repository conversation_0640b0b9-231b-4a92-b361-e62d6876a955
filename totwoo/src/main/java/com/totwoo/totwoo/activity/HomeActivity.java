//package com.totwoo.totwoo.activity;
//
//import android.app.Activity;
//import android.app.NotificationManager;
//import android.content.Context;
//import android.content.DialogInterface;
//import android.content.Intent;
//import android.graphics.Color;
//import android.net.Uri;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.HandlerThread;
//import android.os.Looper;
//import android.os.Message;
//import android.provider.MediaStore;
//import android.provider.Settings;
//import androidx.annotation.NonNull;
//import androidx.constraintlayout.widget.ConstraintLayout;
//import androidx.fragment.app.Fragment;
//import androidx.fragment.app.FragmentManager;
//import androidx.fragment.app.FragmentTransaction;
//import androidx.viewpager.widget.ViewPager;
//import android.text.TextUtils;
//import android.view.KeyEvent;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.RelativeLayout;
//import android.widget.TextView;
//import android.widget.Toast;
//
//import com.arialyy.annotations.Download;
//import com.arialyy.aria.core.Aria;
//import com.arialyy.aria.core.download.DownloadTask;
//import com.bumptech.glide.Glide;
//import com.etone.framework.annotation.EventInject;
//import com.etone.framework.annotation.InjectUtils;
//import com.etone.framework.event.EventData;
//import com.etone.framework.event.SubscriberListener;
//import com.etone.framework.event.TaskType;
//import com.etone.framework.utils.JSONUtils;
//import com.liulishuo.magicprogresswidget.MagicProgressBar;
//import com.totwoo.library.bitmap.BitmapHelper;
//import com.totwoo.library.net.HttpRequest;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.S;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.adapter.HomePageAdapter;
//import com.totwoo.totwoo.bean.AppUpdateBean;
//import com.totwoo.totwoo.bean.FaceBookSharePathEventData;
//import com.totwoo.totwoo.bean.GreetingCard;
//import com.totwoo.totwoo.bean.NotifyDataModel;
//import com.totwoo.totwoo.bean.NotifyMessage;
//import com.totwoo.totwoo.bean.Owner;
//import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.ble.BleParams;
//import com.totwoo.totwoo.ble.BleUtils;
//import com.totwoo.totwoo.ble.BluetoothManage;
//import com.totwoo.totwoo.ble.JewInfoSingleton;
//import com.totwoo.totwoo.controller.HttpValues;
//import com.totwoo.totwoo.data.GreetingCardLoader;
//import com.totwoo.totwoo.newConrtoller.MemoryController;
//import com.totwoo.totwoo.newConrtoller.MessageController;
//import com.totwoo.totwoo.tim.FileUtil;
//import com.totwoo.totwoo.utils.ARCameraShareUtil;
//import com.totwoo.totwoo.utils.CommonArgs;
//import com.totwoo.totwoo.utils.CommonUtils;
//import com.totwoo.totwoo.utils.ConfigData;
//import com.totwoo.totwoo.utils.CustomNotifyDbHelper;
//import com.totwoo.totwoo.utils.DesUtil;
//import com.totwoo.totwoo.utils.FileUtils;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.NetUtils;
//import com.totwoo.totwoo.utils.PermissionUtil;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//import com.totwoo.totwoo.utils.RequestCallBack;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.utils.WaterTimeDbHelper;
//import com.totwoo.totwoo.widget.AppDownloadDialog;
//import com.totwoo.totwoo.widget.CustomDialog;
//
//import org.greenrobot.eventbus.EventBus;
//import org.greenrobot.eventbus.Subscribe;
//import org.greenrobot.eventbus.ThreadMode;
//import org.json.JSONObject;
//
//import java.io.File;
//import java.lang.ref.WeakReference;
//import java.util.HashMap;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//import cn.jpush.android.api.JPushInterface;
//import rx.Observer;
//import rx.Subscriber;
//import rx.android.schedulers.AndroidSchedulers;
//import rx.schedulers.Schedulers;
//
///**
// * Created by xinyoulingxi on 2017/11/29.
// */
//public class HomeActivity extends BaseActivity implements SubscriberListener {
//
//    @BindView(R.id.home_content_viewpager)
//    ViewPager homeContentViewpager;
//
//    @BindView(R.id.message_layout)
//    public View messageView;
//
//    @BindView(R.id.message_iv)
//    public ImageView messageIv;
//
//    @BindView(R.id.message_close)
//    public ImageView messageClose;
//
//    @BindView(R.id.home_bottom_tab0_tv)
//    TextView home_bottom_tab0_tv;
//
//    @BindView(R.id.home_bottom_tab1_tv)
//    TextView home_bottom_tab1_tv;
//
//    @BindView(R.id.home_bottom_tab2_tv)
//    TextView home_bottom_tab2_tv;
//
//    @BindView(R.id.angel_hint_order_cl)
//    ConstraintLayout angel_hint_order_cl;
//
//    @BindView(R.id.angel_hint_order_iv)
//    ImageView angel_hint_order_iv;
//
//    @BindView(R.id.angel_hint_main_iv)
//    ImageView angel_hint_main_iv;
//
//    private HomePageAdapter homePageAdapter;
//
//    private int currPosition = 0;
//
//    public static boolean isAlive;
//
//    public BottomHolder bottomHolders[];
//
//    private int currentFromType = 1;
//    public static final String JPUSH_ALIAS_OK = "jpush_anlas_ok";
//    /**
//     * 用户性别发生变化发送的通知消息
//     */
//    public static final String ACTION_USER_GENDEN_CHANGE = "action_user_gendet_change";
//
//    /**
//     * 首页是否 OnResume 状态
//     */
//    private boolean mResumed;
//
//    private Uri uri;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_home_new2);
//        getWindow().setBackgroundDrawable(null);
//        ButterKnife.bind(this);
//        InjectUtils.injectOnlyEvent(this);
//
//        currPosition = -1;
//
//        isAlive = true;
//
//        ToTwooApplication.startService(this);
//
//        EventBus.getDefault().register(this);
//
//        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
//            if (!BleUtils.isBlEEnable(this)) {
//                showBluetoothDialog();
//            } else {
//                BluetoothManage.getInstance().initBleWrapper(HomeActivity.this);
//                BluetoothManage.getInstance().reconnect();
//            }
//        }
//
//        initThisActivity();
//        initJewelryThread();
//        putRegisterInfo();
//        angel_hint_order_cl.setOnClickListener(v -> {
//            angel_hint_order_cl.setVisibility(View.GONE);
//            isHintShow = false;
//        });
//        angel_hint_order_iv.setOnClickListener(v -> {
//            startActivity(new Intent(HomeActivity.this, CustomOrderActivity.class).putExtra("from_type", currentFromType));
//            angel_hint_order_cl.setVisibility(View.GONE);
//            isHintShow = false;
//        });
//        int width = CommonUtils.getScreenWidth() - 2 * CommonUtils.dip2px(HomeActivity.this,30);
//        int height = width/4*5;
//        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width,height);
//        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
//        messageIv.setLayoutParams(layoutParams);
//        CommonUtils.isNotificationServiceEnabled();
//
//        checkAppUpdate();
////        FileUtils.cleanCacheBitmap();
//        PermissionUtil.hasLocationPermission(this);
//        Aria.download(this).register();
//        
//    }
//
//    private void putRegisterInfo() {
//        int status = JewInfoSingleton.getInstance().getConnectState();
//        String phoneType = Build.MODEL;
//        String res = NetUtils.checkNetworkType(ToTwooApplication.baseContext);
//        String ver = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "V");
//        if (ver != null && ver.length() > 1)
//            ver = ver.substring(1);
//        else
//            ver = "";
//        String jewType = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//
//        HttpHelper.update.updateRegister(JPushInterface.getRegistrationID(HomeActivity.this), status, phoneType,
//                TextUtils.isEmpty(res) ? "" : res.toUpperCase(), ver, jewType, CommonUtils.getSystemVersion())
//                .subscribeOn(Schedulers.newThread())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(new Observer<HttpBaseBean>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean httpBaseBean) {
//
//                    }
//                });
//    }
//
//    public void switchViewPages() {
//        if (homeContentViewpager.getAdapter() == null)
//            return;
//
//        LogUtils.e("aab switchViewPages");
//        FragmentManager fm = getSupportFragmentManager();
//        FragmentTransaction ft = fm.beginTransaction();
//        int index = homeContentViewpager.getAdapter().getCount();
//        while (index > 0) {
//            LogUtils.e(HomePageAdapter.TAG + " switchViewPages");
//            ft.remove(homePageAdapter.fragmentsData[index - 1]);
//            index--;
//        }
////        ft.commitAllowingStateLoss();
//        ft.commitNowAllowingStateLoss();
////        ft.commit();
//    }
//
//    private CheckConnectHandler checkConnectHandler;
//
//    private void initJewelryThread() {
//        HandlerThread handlerThread = new HandlerThread("initJewelryThread");
//        handlerThread.start();
//        checkConnectHandler = new CheckConnectHandler(this, handlerThread.getLooper());
//        checkConnectHandler.sendEmptyMessage(0);
//    }
//
//    private class CheckConnectHandler extends Handler {
//        private final WeakReference<HomeActivity> mActivity;
//
//        private CheckConnectHandler(HomeActivity activity, Looper looper) {
//            super(looper);
//            this.mActivity = new WeakReference<HomeActivity>(activity);
//        }
//
//        @Override
//        public void handleMessage(Message msg) {
//            HomeActivity mainActivity = mActivity.get();
//            if (mainActivity == null) {
//                return;
//            }
//            updateConnectStatus();
//            //一分钟一次的传给服务器连接状态
//            checkConnectHandler.sendEmptyMessageDelayed(0, 1 * 60 * 1000);
//
//        }
//    }
//
//    private int count = 0;
//
//    private void initThisActivity() {
//        LogUtils.e(HomePageAdapter.TAG + " initThisActivity");
//        switchViewPages();
//        bottomHolders = BottomHolder.initHolders(this);
//        count++;
//        setSpinState(false);
//
//        LogUtils.e(HomePageAdapter.TAG + " new HomePageAdapter");
//        homePageAdapter = new HomePageAdapter(this, getSupportFragmentManager(), count);
//        homeContentViewpager.clearOnPageChangeListeners();
//        homeContentViewpager.setAdapter(homePageAdapter);
//        homeContentViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
//            @Override
//            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
//
//            }
//
//            @Override
//            public void onPageSelected(int position) {
//                selectTab(position, false);
//            }
//
//            @Override
//            public void onPageScrollStateChanged(int state) {
//
//            }
//        });
//        LogUtils.e(HomePageAdapter.TAG + " selectTab");
//        selectTab(0, true, true);
//
//        /**
//         * 延迟 1 秒之后检查贺卡情况
//         */
//        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
//            mHandler.postDelayed(() -> checkGreetingCard(true), 400);
//        }
//
//        // 检查固件跟app 的更新
//        mHandler.postDelayed(() -> BluetoothManage.getInstance().checkOTA(), 4200);
//
//        // 清除心有灵犀的通知提醒
//        clearTOTWOONotification();
//
//        int pushIndex = getIntent().getIntExtra(CommonArgs.HOME_PUSH_TAB_INDEX, 0);
//        if (pushIndex != 0) {
//            homeContentViewpager.setCurrentItem(pushIndex, false);
//        }
//        MessageController.getInstance().messageShow();
//    }
//
//    private boolean isFrist = true;
//
//    @Override
//    protected void onResume() {
//        super.onResume();
//        mResumed = true;
//        if (!isFrist) {
//            if (currPosition >= 0 && homePageAdapter.getItem(currPosition) != null) {
//                homePageAdapter.getItem(currPosition).onShow();
//            }
//        }
//        isFrist = false;
//
//        checkGreetingCard(true);
//
//        // 检查是否有新消息, 显示或者隐藏小红点
//        checkNewMessage();
//
//        // 更新顶部栏状态, 防止小内存手机 HomeActivity 被回收导致显示不正确
////        notifyJewerlyState(null);
//
//        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED && BleUtils.isBlEEnable(this)) {
//            if (BluetoothManage.getInstance().getBondedDevices()) {
//                showUnBondDialog();
//            }
//            // 为防止数据错误, 关闭 totwoo 消息屏蔽
//            BluetoothManage.getInstance().setBlockStatus(false);
//        }
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//
//        LogUtils.e("important");
//
//        if (currPosition >= 0 && homePageAdapter.getItem(currPosition) != null) {
//            homePageAdapter.getItem(currPosition).onHide();
//        }
//
//        mResumed = false;
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        isAlive = false;
//
//        if (checkConnectHandler != null) checkConnectHandler.removeCallbacksAndMessages(null);
//
//        InjectUtils.injectUnregisterListenerAll(this);
//
//        EventBus.getDefault().unregister(this);
//
//        if (homePageAdapter != null) {
//            homePageAdapter.recycle();
//            homePageAdapter = null;
//            homeContentViewpager = null;
//        }
//    }
//
//    private void selectTab(int i, boolean click) {
//        selectTab(i, click, false);
//    }
//
//    private void selectTab(int i, boolean click, boolean isInit) {
//        if (currPosition == i && !isInit) {
//            return;
//        }
//
//        if (currPosition >= 0 && homePageAdapter.getItem(currPosition) != null)
//            homePageAdapter.getItem(currPosition).onHide();
//
//        currPosition = i;
//
//        String jewName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//
//        resetTabView(jewName);
//
////        if (ToTwooApplication.owner.getGender() == 0) {
//        switch (i) {
//            case 0:
//                bottomHolders[0].text.setTextColor(getResources().getColor(R.color.text_color_black_important));
//                if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP))
//                    bottomHolders[0].icon.setImageResource(R.drawable.new_home_sg);
//                else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//                    bottomHolders[0].icon.setImageResource(R.drawable.new_home_tutu);
//                else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SC) || jewName.equals(BleParams.JEWELRY_BLE_NAME_WL))
//                    bottomHolders[0].icon.setImageResource(R.drawable.new_home_lucky);
//                else if(BleParams.isMWJewlery())
//                    bottomHolders[0].icon.setImageResource(R.drawable.new_home_reminder);
//                else
//                    bottomHolders[0].icon.setImageResource(R.drawable.new_home_xylx);
//                bottomHolders[0].point.setVisibility(View.GONE);
//                break;
//            case 1:
//                bottomHolders[1].text.setTextColor(getResources().getColor(R.color.text_color_black_important));
//                if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//                    bottomHolders[1].icon.setImageResource(R.drawable.new_home_xylx);
//                else if(BleParams.isMWJewlery())
//                    bottomHolders[1].icon.setImageResource(R.drawable.new_home_fun);
//                else
//                    bottomHolders[1].icon.setImageResource(R.drawable.new_home_magic);
//                bottomHolders[1].point.setVisibility(View.GONE);
//                break;
//            case 2:
//                bottomHolders[2].text.setTextColor(getResources().getColor(R.color.text_color_black_important));
//                bottomHolders[2].icon.setImageResource(R.drawable.new_home_me);
//                break;
//        }
////        }
////        else {
////            switch (i) {
////                case 0:
////                    bottomHolders[0].text.setTextColor(getResources().getColor(R.color.text_color_black_important));
////                    if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP))
////                        bottomHolders[0].icon.setImageResource(R.drawable.new_home_sg_girl);
////                    else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
////                        bottomHolders[0].icon.setImageResource(R.drawable.new_home_tutu_girl);
////                    else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SC))
////                        bottomHolders[0].icon.setImageResource(R.drawable.new_home_lucky_girl);
////                    else
////                        bottomHolders[0].icon.setImageResource(R.drawable.new_home_xylx_girl);
////                    bottomHolders[0].point.setVisibility(View.GONE);
////                    break;
////                case 1:
////                    bottomHolders[1].text.setTextColor(getResources().getColor(R.color.text_color_black_important));
////                    if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
////                        bottomHolders[1].icon.setImageResource(R.drawable.new_home_xylx_girl);
////                    else
////                        bottomHolders[1].icon.setImageResource(R.drawable.new_home_magic_girl);
////                    bottomHolders[1].point.setVisibility(View.GONE);
////                    break;
////                case 2:
////                    bottomHolders[2].text.setTextColor(getResources().getColor(R.color.text_color_black_important));
////                    bottomHolders[2].icon.setImageResource(R.drawable.new_home_me_girl);
////                    break;
////            }
////        }
//
//        if (click) {
//            homeContentViewpager.setCurrentItem(i, false);
//        }
//
//        if (homePageAdapter.getItem(currPosition) != null) {
//            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOMEACTIVITY_ONSHOW, null);
//            homePageAdapter.getItem(currPosition).onShow();
//        }
//
//        updateConnectStatus();
//    }
//
//    private void updateConnectStatus() {
//        String name = PreferencesUtils.getString(HomeActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED)
//            name = "";
//
//        MemoryController.getInstance().checkConnectState(name);
//    }
//
//    private void resetTabView(String jewName) {
//        for (int i = 0; i < bottomHolders.length; i++)
//            bottomHolders[i].text.setTextColor(getResources().getColor(R.color.text_color_black_nomal));
//
//        if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP))
//            bottomHolders[0].icon.setImageResource(R.drawable.new_home_sg_un);
//        else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//            bottomHolders[0].icon.setImageResource(R.drawable.new_home_tutu_un);
//        else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SC) || jewName.equals(BleParams.JEWELRY_BLE_NAME_WL))
//            bottomHolders[0].icon.setImageResource(R.drawable.new_home_lucky_un);
//        else if(BleParams.isMWJewlery())
//            bottomHolders[0].icon.setImageResource(R.drawable.new_home_reminder_un);
//        else
//            bottomHolders[0].icon.setImageResource(R.drawable.new_home_xylx_un);
//
//        if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//            bottomHolders[1].icon.setImageResource(R.drawable.new_home_xylx_un);
//        else if(BleParams.isMWJewlery())
//            bottomHolders[1].icon.setImageResource(R.drawable.new_home_fun_un);
//        else
//            bottomHolders[1].icon.setImageResource(R.drawable.new_home_magic_un);
//
//        bottomHolders[2].icon.setImageResource(R.drawable.new_home_me_un);
//
//    }
//
//    /**
//     * 接收登录页传来的需要 show 的 GreetingCard
//     */
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void receiveGreetingCard(GreetingCard card) {
//        if (mResumed && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
//            showGreetingCard(card);
//        } else {
//            GreetingCardLoader.saveUnShowCard(this, card);
//        }
//    }
//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void recieveLogout(Owner owner) {
//        if (!CommonUtils.isLogin()) {
//            WaterTimeDbHelper.getInstance().deleteAllTable();
//            CustomNotifyDbHelper.getInstance().deleteAllBean();
//            CommonUtils.setLogin(false);
//            // 处理界面跳转工作
//            sendCloseActivitiesBR(true);
//        }
//    }
//
//    public static boolean hasUnread = false;
//
//    @EventInject(eventType = S.E.E_RECEIVED_IM_MESSAGE, runThread = TaskType.UI)
//    public void onReceivedIMMEssage(EventData data) {
//        hasUnread = true;
//        String jewName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//        if (currPosition != 0 && !jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//            bottomHolders[0].point.setVisibility(View.VISIBLE);
//        else if (currPosition != 1 && jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//            bottomHolders[1].point.setVisibility(View.VISIBLE);
//    }
//
//    @EventInject(eventType = S.E.E_FINISH_HOMEACTIVITY, runThread = TaskType.UI)
//    public void onFinishThisActivity(EventData data) {
//        /*HomeNewActivity.isAlive = false;
//        Intent intent = getIntent();
//        overridePendingTransition(0, 0);
//        intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
//        finish();
//
//        overridePendingTransition(0, 0);
//        startActivity(intent);*/
//        initThisActivity();
//    }
//
//    /**
//     * 接收虚拟人facebook分享传来的图片地址，调出分享
//     *
//     * @param data
//     */
//    @EventInject(eventType = S.E.E_VIS_FACEBOOK_IMAGE, runThread = TaskType.UI)
//    public void onFaceBookImageShare(EventData data) {
//        FaceBookSharePathEventData pathEventData = (FaceBookSharePathEventData) data;
//        String path = pathEventData.getPath();
//        ARCameraShareUtil.getInstance().imageShareToFaceBook(path, HomeActivity.this);
//    }
//
//    /**
//     * 接收虚拟人facebook分享传来的视频地址，调出分享
//     *
//     * @param data
//     */
//    @EventInject(eventType = S.E.E_VIS_FACEBOOK_VEDIO, runThread = TaskType.UI)
//    public void onFaceBookVedioShare(EventData data) {
//        FaceBookSharePathEventData pathEventData = (FaceBookSharePathEventData) data;
//        String path = pathEventData.getPath();
//        ARCameraShareUtil.getInstance().imageShareToFaceBook(path, HomeActivity.this);
//    }
//
//    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CHANGE, runThread = TaskType.UI)
//    public void notifyJewerlyState(EventData data) {
//        updateTopLayer();
//    }
//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onReceiveState(final TotwooMessage message) {
//        if (TextUtils.equals(message.getTotwooState(), TotwooMessage.TOTWOO_SEND_SUCCESS)) {
//            return;
//        }
//        String jewName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//        if (currPosition != 0 && !jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP) && !jewName.equals(BleParams.JEWELRY_BLE_NAME_SL)) {
//            bottomHolders[0].point.setVisibility(View.VISIBLE);
//            bottomHolders[1].point.setVisibility(View.GONE);
//        } else if (currPosition != 1 && !jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP) && jewName.equals(BleParams.JEWELRY_BLE_NAME_SL)) {
//            bottomHolders[0].point.setVisibility(View.GONE);
//            bottomHolders[1].point.setVisibility(View.VISIBLE);
//        }
//        LogUtils.e("messageType:" + message.getTotwooState());
//    }
//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onBgChange(String action) {
////        if (HomeActivity.ACTION_USER_GENDEN_CHANGE.equals(action)) {
////            //没有保存currentType。目前只有我的页面可以跳转，之后有变化再改
////            if (ToTwooApplication.owner.getGender() == 0) {
////                bottomHolders[2].icon.setImageResource(R.drawable.new_home_me);
////            } else {
////                bottomHolders[2].icon.setImageResource(R.drawable.new_home_me_girl);
////            }
////        }
//    }
//
//    @EventInject(eventType = S.E.E_MESSAGE_SHOW_SUCCESSED, runThread = TaskType.UI)
//    public void onMessageShowSuccess(EventData data) {
//        HttpValues hv = (HttpValues) data;
//        String tmp = JSONUtils.getString(hv.content, "data", "");
//        final String message_id = JSONUtils.getString(tmp, "message_id", "");
//        String img_url = JSONUtils.getString(tmp, "img_url", "");
//        final String jump_url = JSONUtils.getString(tmp, "jump_url", "");
//        final int is_share = JSONUtils.getInt(tmp, "is_share", 0);
//        final String title = JSONUtils.getString(tmp, "title", "");
//        final String content = JSONUtils.getString(tmp, "content", "");
//        messageView.setVisibility(View.VISIBLE);
//
//        BitmapHelper.displayWithoutPlaceHolder(HomeActivity.this, messageIv, img_url);
//        if (!jump_url.equals("")) {
//            messageIv.setOnClickListener(v -> {
//                MessageController.getInstance().messageClick(message_id);
////                    Intent intent = new Intent(HomeActivity.this, WebActivity.class);
////                    intent.putExtra(WebActivity.WEB_URL_TAG, jump_url);
////                    HomeActivity.this.startActivity(intent);
//                String url = jump_url + DesUtil.fullSign();
//                if (is_share == 1) {
//                    WebViewActivity.loadUrl(HomeActivity.this, url, false, true, title, content);
//                } else {
//                    WebViewActivity.loadUrl(HomeActivity.this, url, false);
//                }
//
//            });
//        }
//
//        messageClose.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                MessageController.getInstance().messageClick(message_id);
//                messageView.setVisibility(View.GONE);
//            }
//        });
//    }
//
//    @OnClick({R.id.home_bottom_tab0, R.id.home_bottom_tab1, R.id.home_bottom_tab2})
//    public void onClick(View view) {
//        switch (view.getId()) {
//            case R.id.home_bottom_tab0:
//                selectTab(0, true);
//                checkNewMessage();
//                break;
//            case R.id.home_bottom_tab1:
//                selectTab(1, true);
//                checkNewMessage();
//                break;
//            case R.id.home_bottom_tab2:
//                selectTab(2, true);
//                break;
//        }
//    }
//
//    /**
//     * 检查是否有新消息, 显示或者隐藏左侧小红点
//     */
//    private void checkNewMessage() {
//        String jewName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//        if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP))
//            return;
//        LogUtils.e("aab checkNewMessage");
//        HttpRequest.post(HttpHelper.URL_GET_MESSAGE_CHECK_NEW,
//                HttpHelper.getBaseParams(true), new RequestCallBack<String>() {
//                    @Override
//                    public void onLogicSuccess(String s) {
//                        super.onLogicSuccess(s);
//                        JSONObject data = HttpHelper.parserStringResponse(s);
//
//                        NotifyMessage mes = new NotifyMessage();
//                        int count = data.optInt("unread_message_count");
//                        LogUtils.e("unread_message_count:" + count);
//                        if (data != null && data.optInt("unread_message_count") != 0) {
//                            bottomHolders[2].point.setVisibility(View.VISIBLE);
//                            mes.setIsNew(true);
//                        } else {
//                            bottomHolders[2].point.setVisibility(View.GONE);
//                            mes.setIsNew(false);
//                        }
//                        EventBus.getDefault().postSticky(mes);
//                    }
//                });
//    }
//
//    @EventInject(eventType = S.E.E_TOKEN_FAILED, runThread = TaskType.UI)
//    public void onTokenFailed(EventData data) {
//        LogUtils.e("aab showTokenFailedDialog");
//        showTokenFailedDialog();
//    }
//
//    @EventInject(eventType = S.E.E_CAMERA_PERMISSION, runThread = TaskType.UI)
//    public void onCheckCamera(EventData data) {
//        if (!PermissionUtil.hasCameraPermission(this)) {
//            return;
//        }
//        if (!PermissionUtil.hasStoragePermission(this)) {
//            return;
//        }
//        com.etone.framework.event.EventBus.onPostReceived(S.E.E_CAMERA_PERMISSION_HAS, null);
//    }
//
//    private boolean isLogOut = false;
//
//    private CustomDialog failedDialog;
//
//    public void showTokenFailedDialog() {
//        if (failedDialog != null && failedDialog.isShowing()) {
//            return;
//        }
//        failedDialog = new CustomDialog(this);
//        failedDialog.setMessage(R.string.token_invalid);
//        failedDialog.setPositiveButton(R.string.i_know, v -> {
//            isLogOut = true;
//            CommonUtils.clearUserData();
//        });
//        failedDialog.setCancelable(false);
//        failedDialog.setOnDismissListener(dialog -> {
//            isLogOut = true;
//            CommonUtils.clearUserData();
//        });
//        failedDialog.show();
//    }
//
//    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_APART, runThread = TaskType.UI)
//    public void updateJewerlyState(EventData data) {
//        if (!isLogOut)
//            return;
//
//        failedDialog.dismiss();
//        sendCloseActivitiesBR(false);
//        startActivity(new Intent(this, LoginAndPasswordActivity.class));
//    }
//
//    public void showUnBondDialog() {
//        final CustomDialog dialog = new CustomDialog(this);
//        dialog.setMessage(R.string.error_bonded);
//        dialog.setPositiveButton(R.string.system_setting_dialog_setting, v -> {
//            Intent intent = new Intent(Settings.ACTION_BLUETOOTH_SETTINGS);
//            startActivity(intent);
//            dialog.dismiss();
//        });
//        dialog.show();
//    }
//
//    private void updateTopLayer() {
//        for (int i = 0; i < 3; i++) {
//            Fragment page = homePageAdapter.getItem(i);
//
//            if (page != null && page instanceof JewelryStateChangeListener) {
//                ((JewelryStateChangeListener) page).onChange();
//                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOMEACTIVITY_ONSHOW, null);
//            }
//        }
//    }
//
//    public interface JewelryStateChangeListener {
//        void onChange();
//    }
//
//    /**
//     * 清楚当前的所有的通知
//     */
//    private void clearTOTWOONotification() {
//        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
//        manager.cancel(NotifyDataModel.NOTI_TOTWOO_ID);
//
//        PreferencesUtils.put(this, NotifyDataModel.NOTI_TOTWOO_COUNT_TAG, 0);
//    }
//
//    @Override
//    protected void onNewIntent(Intent intent) {
//        super.onNewIntent(intent);
//
//        int message = intent.getIntExtra("message", 0);
//        String jewName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//
//        if (message == 1) {
//            if (!jewName.equals(BleParams.JEWELRY_BLE_NAME_SL)) {
//                selectTab(0, true);
//            } else {
//                selectTab(1, true);
//            }
//        } else if (message == 2) {
//            if (!jewName.equals(BleParams.JEWELRY_BLE_NAME_SL)) {
//                selectTab(2, true);
//            } else {
//                selectTab(0, true);
//                com.etone.framework.event.EventBus.onPostReceived(S.E.E_SCROLL_TO_SEDENTARY, null);
//            }
//
//        } else if (message == 3) {
//            if (!jewName.equals(BleParams.JEWELRY_BLE_NAME_SL)) {
//                selectTab(1, true);
//            } else {
//                selectTab(0, true);
//            }
//        }
//
//        TotwooMessage msg = (TotwooMessage) intent.getSerializableExtra("msgInfo");
//        if (msg != null) {
//            com.etone.framework.event.EventBus.onPostReceived(S.E.E_PUSH_RECEIVED_TOTWOO_MESSAGE, msg);
//        }
//    }
//
//    /**
//     * 检查当前是否有未接收贺卡, 加载对应资源, 成功之后展示
//     */
//    private void checkGreetingCard(boolean needLoad) {
//        LogUtils.e("aab checkGreetingCard");
//        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED) {
//            return;
//        }
//
//        GreetingCard card = GreetingCardLoader.getUnShowCard(this);
//        if (card != null) {
//            LogUtils.e("aab checkGreetingCard card != null");
//            if (mResumed) {
//                showGreetingCard(card);
//            }
//        } else if (needLoad) {
//            LogUtils.e("aab checkGreetingCard needLoad");
//            GreetingCardLoader.getGreetingCardData(false);
//        }
//    }
//
//    /**
//     * 开始展示贺卡, 目前逻辑跳转贺卡接收页面,
//     *
//     * @param card
//     */
//    public void showGreetingCard(GreetingCard card) {
//        Intent intent = new Intent(this, GreetingCardShowActivity.class);
//        intent.putExtra(GreetingCardShowActivity.IS_NEW_CARD, true);
//        intent.putExtra("isFromPre", true);
//        intent.putExtra(GreetingCardShowActivity.EXAT_GREETING_CARD, card);
//        startActivity(intent);
//    }
//
//    /**
//     * 展示请求蓝牙开启的对话框
//     */
//    public void showBluetoothDialog() {
//        final CustomDialog dialog = new CustomDialog(this);
//        dialog.setMessage(R.string.request_open_bluetooth);
//        dialog.setPositiveButton(R.string.allow, new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                BleUtils.enableBlueTooth(HomeActivity.this);
//                dialog.dismiss();
//            }
//        });
//        dialog.setNegativeButton(R.string.cancel, new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                dialog.dismiss();
//            }
//        });
//
//        dialog.show();
//    }
//
//    @Override
//    public void onEventException(String eventType, EventData data, Throwable e) {
//
//    }
//
//    public static class BottomHolder {
//        public ImageView icon;
//        public TextView text;
//        public RelativeLayout layout;
//        public View point;
//
//        public BottomHolder(Activity activity, int iconId, int textId, int layoutId, int pointId) {
//            icon = (ImageView) activity.findViewById(iconId);
//            text = (TextView) activity.findViewById(textId);
//            layout = (RelativeLayout) activity.findViewById(layoutId);
//            point = activity.findViewById(pointId);
//        }
//
//        public static BottomHolder[] initHolders(Activity a) {
//            BottomHolder[] holders = new BottomHolder[3];
//
//            BottomHolder holder = new BottomHolder(a, R.id.home_bottom_tab0_iv, R.id.home_bottom_tab0_tv, R.id.home_bottom_tab0, R.id.home_tab0_red_point);
//            holders[0] = holder;
//            holder = new BottomHolder(a, R.id.home_bottom_tab1_iv, R.id.home_bottom_tab1_tv, R.id.home_bottom_tab1, R.id.home_tab1_red_point);
//            holders[1] = holder;
//            holder = new BottomHolder(a, R.id.home_bottom_tab2_iv, R.id.home_bottom_tab2_tv, R.id.home_bottom_tab2, R.id.home_tab2_red_point);
//            holders[2] = holder;
//
//            String jewName = PreferencesUtils.getString(a, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
//
//            if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP))
//                holders[0].text.setText(R.string.memory);
//            else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//                holders[0].text.setText(R.string.tutu);
//            else if(BleParams.isMWJewlery())
//                holders[0].text.setText(R.string.reminder);
//            else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SC) || jewName.equals(BleParams.JEWELRY_BLE_NAME_WL))
//                holders[0].text.setText(R.string.lucky);
//            else
//                holders[0].text.setText(R.string.notify);
//
//            if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL))
//                holders[1].text.setText(R.string.notify);
//            else if(BleParams.isMWJewlery())
//                holders[1].text.setText(R.string.fun);
//            else
//                holders[1].text.setText(R.string.heart);
//
//            holders[2].text.setText(R.string.user);
//
//            return holders;
//        }
//    }
//
//    @EventInject(eventType = S.E.E_LOVE_PAIR_ALBUM, runThread = TaskType.UI)
//    public void onAlbumClick(EventData data) {
//        Intent intent = new Intent(Intent.ACTION_PICK, null);
//        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
//        startActivityForResult(intent, CommonArgs.SELECT_PHOTO);
//    }
//
//    @EventInject(eventType = S.E.E_LOVE_PAIR_CAMERA, runThread = TaskType.UI)
//    public void onCameraClick(EventData data) {
//        if (PermissionUtil.hasCameraPermission(HomeActivity.this)) {
//            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
////            Uri imageUri = Uri.fromFile(new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE));
//            // 指定照片保存路径（SD卡），USER_HEAD_PORTRAIT为一个临时文件，每次拍照后这个图片都会被替换
//            uri = CommonUtils.getUriForFile(HomeActivity.this, new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE_TEMP));
//            intent.putExtra(MediaStore.EXTRA_OUTPUT, uri);
//            startActivityForResult(intent, CommonArgs.SHOOTING_PHOTO);
//        }
////        try {
////            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
//////            Uri imageUri = Uri.fromFile(new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE));
////            // 指定照片保存路径（SD卡），USER_HEAD_PORTRAIT为一个临时文件，每次拍照后这个图片都会被替换
////            uri = CommonUtils.getUriForFile(HomeActivity.this,new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE_TEMP));
////            intent.putExtra(MediaStore.EXTRA_OUTPUT, uri);
////            startActivityForResult(intent, CommonArgs.SHOOTING_PHOTO);
////        } catch (Exception e) {
////            e.printStackTrace();
////            final CustomDialog dialog = new CustomDialog(HomeActivity.this);
////            dialog.setMessage(R.string.open_camera_error1);
////            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
////                @Override
////                public void onClick(View v) {
////                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + ToTwooApplication.baseContext.getPackageName())));
////                    dialog.dismiss();
////                }
////            });
////            dialog.show();
////        }
//    }
//
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//
//        LogUtils.e("aab requestCode = " + requestCode + " resultCode = " + resultCode);
//
//        switch (requestCode) {
//            case CommonArgs.SHOOTING_PHOTO:// 相机拍摄
//                if (resultCode == -1) {
//                    CommonUtils.startPhotoZoom(uri, this, CommonArgs.CROP_PHOTO, CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE, 288, 512);
//                }
//                break;
//            case CommonArgs.SELECT_PHOTO:// 相册选取
//                Uri uri = null;
//                if (data != null) {
//                    uri = data.getData();
//                }
//
//                if (uri != null) {
//                    CommonUtils.startPhotoZoom(uri, this, CommonArgs.CROP_PHOTO, CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE, 288, 512);
//                }
//                break;
//            case CommonArgs.CROP_PHOTO://
//
//                if (resultCode == -1) {
//                    LogUtils.e("aab E_LOVE_PAIR_BACKGROUND");
//                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOVE_PAIR_BACKGROUND, null);
//                }
//                break;
//        }
//    }
//
//    private boolean isBack = false;
//
//    private void setOrderHintOn() {
//        angel_hint_order_cl.setVisibility(View.VISIBLE);
//        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
//            angel_hint_main_iv.setImageResource(R.drawable.angel_hint_cn);
//        } else {
//            angel_hint_main_iv.setImageResource(R.drawable.angel_hint_en);
//        }
//
//        isHintShow = true;
//    }
//
//    @EventInject(eventType = S.E.E_ANGEL_HINT, runThread = TaskType.UI)
//    public void onAngelHintReceiver(EventData data) {
//        currentFromType = 1;
//        setOrderHintOn();
//    }
//
//    @EventInject(eventType = S.E.E_MAGIC_HINT, runThread = TaskType.UI)
//    public void onMagicHintReceiver(EventData data) {
//        currentFromType = 2;
//        setOrderHintOn();
//    }
//
//    private boolean isHintShow = false;
//
//    @Override
//    public void onBackPressed() {
//        if (isHintShow) {
//            isHintShow = false;
//            angel_hint_order_cl.setVisibility(View.GONE);
//            return;
//        }
//        if (isBack) {
//            super.onBackPressed();
//        } else {
//            isBack = true;
//            ToastUtils.showShort(HomeActivity.this, getResources().getString(R.string.press_again_exit));
//            mHandler.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    isBack = false;
//                }
//            }, 2000);
//        }
//    }
//    private boolean isForeUpdate = false;
//    private void checkAppUpdate(){
//        if(isForceUpdate()){
//            isForeUpdate = true;
//            showUpdateVersionDialog(PreferencesUtils.getString(HomeActivity.this, CommonArgs.PREF_FORCE_UPDATE_INFO, ""),
//                    PreferencesUtils.getString(HomeActivity.this, CommonArgs.PREF_FORCE_UPDATE_URL, ""),
//                    PreferencesUtils.getString(HomeActivity.this, CommonArgs.PREF_FORCE_UPDATE_SERVICE_VERSION, ""),
//                    PreferencesUtils.getLong(HomeActivity.this, CommonArgs.PREF_FORCE_UPDATE_LENGTH, 0));
//            return;
//        }
//        HttpHelper.commonServiceV2.checkAppUpdate(Apputils.getVersionName(HomeActivity.this), ConfigData.region, PreferencesUtils.getString(HomeActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))
//                .compose(HttpHelper.rxSchedulerHelper())
//                .subscribe(new Subscriber<HttpBaseBean<AppUpdateBean>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<AppUpdateBean> appUpdateBeanHttpBaseBean) {
//                        if (appUpdateBeanHttpBaseBean.getErrorCode() == 0) {
//                            if (appUpdateBeanHttpBaseBean.getData().isNeed_update()) {
//                                String updateInfo;
//                                if (Apputils.systemLanguageIsChinese(HomeActivity.this)) {
//                                    updateInfo = appUpdateBeanHttpBaseBean.getData().getTxt();
//                                } else {
//                                    updateInfo = appUpdateBeanHttpBaseBean.getData().getEn_txt();
//                                }
//                                isForeUpdate = appUpdateBeanHttpBaseBean.getData().getIs_force_update() == 1;
//                                String url = appUpdateBeanHttpBaseBean.getData().getUrl();
//                                String version = appUpdateBeanHttpBaseBean.getData().getLastest_version();
//                                long length = appUpdateBeanHttpBaseBean.getData().getPackage_size();
//
//                                if (!TextUtils.equals(PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.PREF_LAST_VERSION, ""), version)) {
//                                    PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.PREF_LAST_VERSION_COUNT);
//                                }
//                                PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.PREF_LAST_VERSION, version);
//
//                                // 存储强制更新版本数据
//                                if (isForeUpdate) {
//                                    PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_VERSION, Apputils.getVersionName(ToTwooApplication.baseContext));
//                                    PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_INFO, updateInfo);
//                                    PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_URL, url);
//                                    PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_SERVICE_VERSION, version);
//                                    PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_LENGTH, updateInfo);
//                                } else {
//                                    PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_VERSION);
//                                    PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_INFO);
//                                }
//                                if(PreferencesUtils.getInt(ToTwooApplication.baseContext, CommonArgs.PREF_LAST_VERSION_COUNT, 0) < 5){
//                                    showUpdateVersionDialog(updateInfo, url, version, length);
//                                }
//                            } else {
//                                FileUtils.cleanCacheApk();
//                            }
//                        } else {
//                            ToastUtils.show(HomeActivity.this, R.string.error_net, Toast.LENGTH_SHORT);
//                        }
//                    }
//                });
//    }
//
//    private AppDownloadDialog appDownloadDialog;
//    private void showUpdateVersionDialog(String updateInfo, String url, String version, long length) {
//        appDownloadDialog = new AppDownloadDialog(HomeActivity.this, updateInfo, v -> {
//            downloadAria(url, version, length);
//            appDownloadDialog.dismiss();
//        }, v -> {
//            if (isForceUpdate()) {
//                HomeActivity.this.sendBroadcast(new Intent(BaseActivity.KILLACTIVITYS));
//            } else {
//                int count = PreferencesUtils.getInt(ToTwooApplication.baseContext, CommonArgs.PREF_LAST_VERSION_COUNT, 0);
//                count++;
//                PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.PREF_LAST_VERSION_COUNT, count);
//                appDownloadDialog.dismiss();
//            }
//        });
//        if (isForeUpdate) {
//            appDownloadDialog.setCanceledOnTouchOutside(false);
//            appDownloadDialog.setOnKeyListener((dialog, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
//        }
//        appDownloadDialog.show();
//    }
//
//    private void downloadAria(String url, String version, long targetLength) {
//        String target = FileUtils.getDownloadDir() + File.separator + "apk"+ File.separator + "totwoo" + version + ".apk";
//        File file = new File(target);
//        //因为断点续传的原因，file的长度没法作为是不是完整包的依据。就用了一个参数CommonArgs.APK_DOWNLOAD_SUCCESS判断。但是MessageActivity的会影响。所以判断有dialog的时候才去修改这个值
//        if (file.exists() && file.length() == targetLength && PreferencesUtils.getBoolean(HomeActivity.this, CommonArgs.APK_DOWNLOAD_SUCCESS,false)) {
//            CommonUtils.installApk(file, HomeActivity.this);
//        } else {
//            Aria.download(this)
//                    .load(url)     //读取下载地址
//                    .setFilePath(file.getPath()) //设置文件保存的完整路径
//                    .start();   //启动下载
//            PreferencesUtils.remove(HomeActivity.this, CommonArgs.APK_DOWNLOAD_SUCCESS);
//            showDownloadingProgressDialog();
//        }
//    }
//
//    private MagicProgressBar app_downloading_mpb;
//    private TextView app_downloading_progress_tv;
//
//    private CustomDialog downloadProgressDialog;
//
//    private void showDownloadingProgressDialog() {
//        downloadProgressDialog = new CustomDialog(HomeActivity.this);
//        View rootView = LayoutInflater.from(HomeActivity.this).inflate(R.layout.dialog_app_downloading, null);
//        downloadProgressDialog.setRootView(rootView);
//        app_downloading_mpb = rootView.findViewById(R.id.app_downloading_mpb);
//        app_downloading_mpb.setPercent(0f);
//        app_downloading_progress_tv = rootView.findViewById(R.id.app_downloading_progress_tv);
//        app_downloading_progress_tv.setText(HomeActivity.this.getString(R.string.downloading_progress) + " " + 0 + " %");
//        ImageView app_downloading_iv = rootView.findViewById(R.id.app_downloading_iv);
//        Glide.with(HomeActivity.this).asGif().load(R.drawable.download_progress).into(app_downloading_iv);
//
//        if (isForeUpdate) {
//            downloadProgressDialog.setCanceledOnTouchOutside(false);
//            downloadProgressDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
//                @Override
//                public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
//                    return keyCode == KeyEvent.KEYCODE_BACK;
//
//                }
//            });
//        }
//        downloadProgressDialog.show();
//    }
//
//    private int lastProgress = 0;
//
//    @Download.onTaskRunning
//    protected void running(DownloadTask task) {
//        int progress = task.getPercent();    //任务进度百分比
//        if (lastProgress != progress) {
//            lastProgress = progress;
//            if (downloadProgressDialog != null && downloadProgressDialog.isShowing()) {
//                app_downloading_progress_tv.setText(HomeActivity.this.getString(R.string.downloading_progress) + " " + progress + " %");
//                float percent = (float) progress / 100;
//                app_downloading_mpb.setPercent(percent);
//            }
//            if (progress % 2 == 0) {
//                CommonUtils.downloadingNotification(progress);
//            }
//        }
//        LogUtils.e("aab progress = " + progress);
//    }
//
//    @Download.onTaskComplete
//    void taskComplete(DownloadTask task) {
//        //在这里处理任务完成的状态
//        File downloadFile = new File(task.getDownloadPath());
//        Intent installIntent = new Intent(Intent.ACTION_VIEW);
//        installIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            Uri uriForFile = CommonUtils.getUriForFile(HomeActivity.this, downloadFile);
//            installIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
//            installIntent.setDataAndType(uriForFile, "application/vnd.android.package-archive");
//        } else {
//            installIntent.setDataAndType(Uri.fromFile(downloadFile), "application/vnd.android.package-archive");
//        }
//
//        CommonUtils.downloadSuccessNotification(installIntent);
//        ToastUtils.show(HomeActivity.this, R.string.download_success, Toast.LENGTH_SHORT);
//        CommonUtils.installApk(downloadFile, HomeActivity.this.getApplicationContext());
//        if (downloadProgressDialog != null && downloadProgressDialog.isShowing()) {
//            app_downloading_progress_tv.setText(HomeActivity.this.getString(R.string.download_success));
//            downloadProgressDialog.dismiss();
//            PreferencesUtils.put(HomeActivity.this, CommonArgs.APK_DOWNLOAD_SUCCESS,true);
//        }
//    }
//
//    @Download.onTaskStart
//    void taskStart(DownloadTask task) {
//        CommonUtils.downloadingNotification(0);
//    }
//
//    /**
//     * 检查当前版本是否低于最低要求, 最低版本当前版本做对比
//     */
//    private boolean isForceUpdate() {
//        String ver = PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.PREF_FORCE_UPDATE_VERSION, "");
//        if (ver.length() == 0) {
//            return false;
//        }
//        //zy：只判断了版本是否更新，有逻辑漏洞。但是不影响，后面会判断的。
//        if(TextUtils.equals(Apputils.getVersionName(ToTwooApplication.baseContext),ver)){
//            return true;
//        }else{
//            PreferencesUtils.remove(ToTwooApplication.baseContext,CommonArgs.PREF_FORCE_UPDATE_VERSION);
//            return false;
//        }
//    }
//}
