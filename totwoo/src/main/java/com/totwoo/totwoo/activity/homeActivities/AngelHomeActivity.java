package com.totwoo.totwoo.activity.homeActivities;

import android.os.Bundle;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.CustomAngelFragment;
import com.totwoo.totwoo.fragment.LoveFragment;
import com.totwoo.totwoo.fragment.MeFragment;

import java.util.ArrayList;

public class AngelHomeActivity extends HomeBaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Class<? extends BaseFragment>[] baseFragments = new Class[3];
        baseFragments[0] = CustomAngelFragment.class;
        baseFragments[1] = LoveFragment.class;
        baseFragments[2] = MeFragment.class;

        ArrayList<HomepageBottomInfo> infos = new ArrayList<>();
        infos.add(new HomepageBottomInfo(R.drawable.new_home_tutu_un, R.drawable.new_home_tutu, R.string.tutu));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_xylx_un, R.drawable.new_home_xylx, R.string.notify));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_me_un, R.drawable.new_home_me, R.string.user));
        super.setBottomInfo(infos);
        super.setFragmentsAndInitViewpager(baseFragments);
        super.setCurrentFromType(1);
        super.setTotwooIndex(1);
    }
}
