package com.totwoo.totwoo.activity;

import android.animation.Animator;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.etone.framework.event.EventBus;
import com.google.gson.Gson;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.giftMessage.GiftDataActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftInfoAddActivity;
import com.totwoo.totwoo.activity.giftMessage.GiftMessageListActivity;
import com.totwoo.totwoo.bean.GiftMessageCard;
import com.totwoo.totwoo.bean.GiftMessageReceiverInfo;
import com.totwoo.totwoo.bean.LocalContactsBean;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.bean.holderBean.SendGreetingCardResponse;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import java.io.File;
import java.util.ArrayList;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class ContactsActivityForGift extends ContactsBaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mCardStoreLv.setImageAssetsFolder("lottie_card_store/");
        mCardStoreLv.setAnimation("card_store.json");
    }

    @Override
    void clickConfirm() {
        if (selectBeans == null || selectBeans.isEmpty()) {
            mSendingTv.setVisibility(View.VISIBLE);
            mSendingTv.setText(R.string.send_card_no_receive_err);
            mHandler.postDelayed(() -> mSendingTv.setVisibility(View.GONE), 3000);
            return;
        }

        sendGreetingCard();
    }

    private int type;

    /**
     * 发送贺卡操作
     */
    private void sendGreetingCard() {
        type = getIntent().getIntExtra(GiftDataActivity.TYPE, 1);
        switch (type) {
            case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                upload(ContactsActivityForGift.this, CommonArgs.CACHE_GIFT_IMAGE, null, 1);
                break;
            case CommonArgs.COMMON_SEND_TYPE_SOUND:
                upload(ContactsActivityForGift.this, CommonArgs.CACHE_GIFT_AUDIO_PATH, null, 2);
                break;
            case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                uploadCover(ContactsActivityForGift.this, getIntent().getStringExtra(CommonArgs.COVER_PATH));
                break;
        }
    }

    public void uploadCover(final Context context, final String filePath) {
        HttpHelper.card.getQiNiuToken(1, "greetingcard")
                .subscribeOn(Schedulers.io())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        showFailDialog();
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0) {
                            File file = new File(filePath);
                            if (!file.exists()) {
                                ToastUtils.showShort(context, R.string.error_net);
                            }

                            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
//                                            ToastUtils.showLong(context, R.string.upload_filed);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            upload(ContactsActivityForGift.this, getIntent().getStringExtra(CommonArgs.VIDEO_PATH), url, 3);
                                        }
                                    });
                        }
                    }
                });
    }

    public void upload(final Context context, final String filePath, final String coverPath, int qiniuType) {
        mSendingTv.setVisibility(View.VISIBLE);
        mSendingTv.setText(R.string.sending);
        mSendSuccessBg.setVisibility(View.VISIBLE);
        HttpHelper.card.getQiNiuToken(qiniuType, "greetingcard")
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        showFailDialog();
                        mSendingTv.setVisibility(View.GONE);
                        mSendSuccessBg.setVisibility(View.GONE);
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0) {
                            File file = new File(filePath);
                            if (!file.exists()) {
                                ToastUtils.showShort(context, R.string.error_net);
                            }

                            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
                                            showFailDialog();
                                            mSendingTv.setVisibility(View.GONE);
                                            mSendSuccessBg.setVisibility(View.GONE);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            switch (type) {
                                                case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                                                    saveInfoServer(url, null, null, null);
                                                    break;
                                                case CommonArgs.COMMON_SEND_TYPE_SOUND:
                                                    saveInfoServer(null, url, null, null);
                                                    break;
                                                case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                                                    saveInfoServer(null, null, url, coverPath);
                                                    break;
                                            }
                                        }
                                    });
                        }
                    }
                });
    }

    /**
     * 根据GreetingCard 数据加载数据类型
     *
     * @return
     */
    public static int loadGreetCardType(GiftMessageCard cardData) {
        int type = 0;
        if (cardData == null) {
            return type;
        }

        if (!TextUtils.isEmpty(cardData.getText())) {
            type += 2 >> 1;
        }

        if (!TextUtils.isEmpty(cardData.getImageUrl())) {
            type += 2;
        }

        if (!TextUtils.isEmpty(cardData.getAudioUrl())) {
            type += 2 << 1;
        }

        if (!TextUtils.isEmpty(cardData.getVedioUrl())) {
            type += 2 << 2;
        }
        return type;
    }

    private void saveInfoServer(String imgUrl, String audioUrl, String vedioUrl, String coverUrl) {
        String name = getIntent().getStringExtra(GiftInfoAddActivity.GIFT_SENDER_NAME);
        String text = getIntent().getStringExtra(GiftInfoAddActivity.GIFT_SENDER_INFO);
        Gson gson = new Gson();
        ArrayList<GiftMessageReceiverInfo> infos = new ArrayList<>();
        for (LocalContactsBean localContactsBean : selectBeans) {
            infos.add(new GiftMessageReceiverInfo(localContactsBean.getName(), localContactsBean.getNumber()));
        }
        GiftMessageCard giftMessageCard = new GiftMessageCard(imgUrl, vedioUrl, coverUrl, audioUrl, text);
        HttpHelper.card.sendCard(ToTwooApplication.owner.getTotwooId(), name, loadGreetCardType(giftMessageCard), gson.toJson(giftMessageCard), gson.toJson(infos))
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SendGreetingCardResponse>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        showFailDialog();
                        mSendingTv.setVisibility(View.GONE);
                        mSendSuccessBg.setVisibility(View.GONE);
                    }

                    @Override
                    public void onNext(HttpBaseBean<SendGreetingCardResponse> sendGreetingCardResponseHttpBaseBean) {
                        mSendingTv.setVisibility(View.GONE);
                        mSendSuccessBg.setBackgroundColor(getResources().getColor(R.color.text_color_black_hint));
                        mCardStoreLv.setVisibility(View.VISIBLE);
                        mCardStoreLv.addAnimatorListener(new Animator.AnimatorListener() {
                            @Override
                            public void onAnimationStart(Animator animation) {

                            }

                            @Override
                            public void onAnimationEnd(Animator animation) {
                                Intent intent = new Intent(ContactsActivityForGift.this, GiftMessageListActivity.class);
                                intent.putExtra(CommonArgs.FROM_TYPE, GiftMessageListActivity.SEND_SUCCESS);
                                startActivity(intent);
                                EventBus.onPostReceived(S.E.E_GIFT_SEND_SUCCEED, null);
                                ContactsActivityForGift.this.finish();
                            }

                            @Override
                            public void onAnimationCancel(Animator animation) {

                            }

                            @Override
                            public void onAnimationRepeat(Animator animation) {

                            }
                        });
                        mCardStoreLv.playAnimation();
                    }
                });
    }

    private CommonMiddleDialog commonMiddleDialog;

    private void showFailDialog() {
//        if (failDialog == null) {
//            failDialog = new CustomDialog(this);
////            failDialog.setTitle(getString(R.string.warm_tips));
//            failDialog.setMessage(getString(R.string.send_card_failed));
//            failDialog.setPositiveButton(v -> failDialog.dismiss());
//        }
//        failDialog.show();

        try {
            if (commonMiddleDialog == null) {
                commonMiddleDialog = new CommonMiddleDialog(this);
                commonMiddleDialog.setMessage(getString(R.string.send_card_failed));
                commonMiddleDialog.setSure(v -> commonMiddleDialog.dismiss());
            }
            commonMiddleDialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
