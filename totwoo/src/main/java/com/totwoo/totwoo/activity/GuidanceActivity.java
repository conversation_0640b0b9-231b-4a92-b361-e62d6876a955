package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.GestureDetector;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ViewSwitcher;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager.widget.ViewPager.OnPageChangeListener;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.widget.CountView;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * ， 展示新版本特色功能的引导
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class GuidanceActivity extends BaseActivity {
    /**
     * 引导页版本号， 用来判断当前设备是否显示过当前引导页面
     */
    public static final int GUIDANCE_VERSION = 3;

    public static final String SHOW_GUIDANCE_VERSION_TAG = "show_guidance_version_tag";

    /**
     * 主体ViewPager
     */
    @BindView(R.id.guidance_viewpager)
    ViewPager mViewPager;

    /**
     * 标识图片页码的小圆点
     */
    @BindView(R.id.guidance_count_view)
    CountView countView;

    @BindView(R.id.guidance_phone_page_sv)
    HorizontalScrollView phonePageSv;

    @BindView(R.id.guidance_phone_page_layout)
    LinearLayout mGuidancePhonePageLayout;

    @BindView(R.id.guidance_start_btn)
    TextView startBtn;

    @BindView(R.id.guidance_title_viewswitcher)
    ViewSwitcher mGuidanceTitleViewswitcher;

    @BindView (R.id.activity_guidance_iv01)
    ImageView mIv1;

    @BindView (R.id.activity_guidance_iv02)
    ImageView mIv2;

    private int[] scenes;
    //引导页图片
//    private int[] bgs = {R.drawable.guidance_bg_03, R.drawable.guidance_bg_01, R.drawable.guidance_bg_02};
    private int[] titlesubs = {R.string.guidance_title_sub_01, R.string.guidance_title_sub_02, R.string.guidance_title_sub_03};
    private int[] titles = {R.string.guidance_title_01, R.string.guidance_title_02, R.string.guidance_title_03};

    /**
     * 手势判别器
     */
    private GestureDetector gd;
    private int perScenceWidth;
    private int perScenceHeight;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);

        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_guidance);

        ButterKnife.bind(this);

        if (Apputils.systemLanguageIsChinese(this))
        {
//            //scenes = new int[]{R.drawable.guidance_scene_01, R.drawable.guidance_scene_02, R.drawable.guidance_scene_03};
//            mIv1.setImageResource(R.drawable.guidance_scene_01);
//            mIv2.setImageResource(R.drawable.guidance_scene_02);
        }
        else
        {
//            //scenes = new int[]{R.drawable.guidance_scene_01_en, R.drawable.guidance_scene_02_en, R.drawable.guidance_scene_03_en};
//            mIv1.setImageResource(R.drawable.guidance_scene_01_en);
//            mIv2.setImageResource(R.drawable.guidance_scene_02_en);
        }

        mViewPager.setAdapter(new ViewPagerAdapter());
        countView.setCount(2);

        mViewPager.addOnPageChangeListener(new OnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                countView.setSelected(position);
                if (position == 2 - 1) {
                    startBtn.setVisibility(View.VISIBLE);
                    countView.setVisibility(View.GONE);
                } else {
                    startBtn.setVisibility(View.GONE);
                    countView.setVisibility(View.VISIBLE);
                }


                View nextView = mGuidanceTitleViewswitcher.getNextView();
                ((TextView)nextView.findViewById(R.id.guidance_title_01)).setText(titlesubs[position]);
                ((TextView)nextView.findViewById(R.id.guidance_title_02)).setText(titles[position]);
                mGuidanceTitleViewswitcher.showNext();
            }

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                if (perScenceWidth == 0)
                {
                    perScenceWidth = phonePageSv.getMeasuredWidth();
                    perScenceHeight = phonePageSv.getMeasuredHeight();
                    ViewGroup.LayoutParams ll = mIv1.getLayoutParams();
                    ll.width = perScenceWidth;
                    ll.height = perScenceHeight;
                    mIv1.setLayoutParams(ll);
                    mIv2.setLayoutParams(ll);
                }
                phonePageSv.scrollTo((int) (perScenceWidth * (position + positionOffset)), 0);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        startBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goNext();
            }
        });

        setSpinState(false);
    }


    /**
     * 三张引导页对应ViewPager的 Adapter， 对应item 仅仅是一张图片
     *
     * <AUTHOR>
     * @date 2015-2015年7月7日
     */
    private class ViewPagerAdapter extends PagerAdapter {
        @Override
        public int getCount() {
            return 2;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            ImageView view = new ImageView(GuidanceActivity.this);
            view.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
//            view.setImageResource(bgs[position]);
            view.setTag(position);
            container.addView(view);
            return view;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            for (int i = 0; i < container.getChildCount(); i++) {
                View v = container.getChildAt(i);
                if (v != null && v.getTag() != null
                        && Integer.parseInt(v.getTag().toString()) == position) {
                    container.removeView(v);
                    break;
                }
            }
        }

        @Override
        public boolean isViewFromObject(View arg0, Object arg1) {
            return arg0 == arg1;
        }
    }

    /**
     * 关闭当前页， 前去下一步<br>
     * <p>
     * 1, 判断用户是否已经登录, 未登录用户跳转注册登录页面。</br> 2, 已登录用户，判断是否完成了基本用户信息的设置，
     * 未设置完成条转入信息设置页； </br> 2,已完成基本信息设置的直接跳入主界面，没有显示过，跳转引导界面；</br>
     */
    private void goNext() {
        PreferencesUtils.put(this, SHOW_GUIDANCE_VERSION_TAG, GUIDANCE_VERSION);

        if (!CommonUtils.isLogin()) {
            HomeActivityControl.getInstance().openLoginActivity(this);
        } else if (!ToTwooApplication
                .isInfoSetFinish(ToTwooApplication.owner)) {
//            startActivity(new Intent(this, UserInfoSettingActivity.class));
            startActivity(new Intent(this, InitInfoActivity.class).putExtra(InitInfoActivity.INIT_INFO,true));
        } else {
            startActivity(new Intent(this, HomeActivityControl.getInstance().getTagertClass()));
        }
        overridePendingTransition(android.R.anim.fade_in,
                android.R.anim.fade_out);

        finish();
    }
}
