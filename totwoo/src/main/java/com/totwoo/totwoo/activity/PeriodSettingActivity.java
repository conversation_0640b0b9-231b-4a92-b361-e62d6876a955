package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.hjq.shape.view.ShapeTextView;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.PeriodBean;
import com.totwoo.totwoo.bean.PeriodNotifyStatus;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.PeriodSettingView;
import com.totwoo.totwoo.widget.WheelView;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;
import com.umeng.analytics.MobclickAgent;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/2/7.
 */

public class PeriodSettingActivity extends BaseActivity {

    @BindView(R.id.period_last_period_value_tv)
    TextView mLastPeriodTv;
    @BindView(R.id.period_week_value_tv)
    TextView mWeekTv;
    @BindView(R.id.period_month_value_tv)
    TextView mMonthTv;
    @BindView(R.id.period_notify_time_value_tv)
    TextView mNotifyTimeTv;

    @BindView(R.id.period_setting_content)
    LinearLayout mPeriodSettingContent;

    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    @BindView(R.id.period_show_cb)
    CheckBox mPeriodShowCb;
    //    @BindView(R.id.unit_conversion_tv)
//    TextView mUnitConversionTv;
    @BindView(R.id.long_vibration_tv)
    ShapeTextView mLongVibrationTv;
    @BindView(R.id.short_vibration_tv)
    ShapeTextView mShortVibrationTv;

    @BindView(R.id.short_vibration_iv)
    View mShortVibrationIv;

    @BindView(R.id.long_vibration_iv)
    View mLongVibrationIv;

    @BindView(R.id.call_switch_info_tv)
    TextView mCallSwitchInfoTv;

    @BindView(R.id.period_setting_color_library_rv)
    RecyclerView colorLibraryRecyclerView;

    @BindView(R.id.period_show_item)
    ConstraintLayout mShowItem;
    @BindView(R.id.period_show_item_view)
    View mShowItemView;
    @BindView(R.id.notify_vibration_layout)
    View mVibrationLayout;
    @BindView(R.id.make_card_sample_subtitle)
    TextView mModeSubtitle;

    private JewelryNotifyModel nowSetModel;
    private boolean isChangePeriod = false;
    private CustomColorLibraryAdapter colorLibraryAdapter;
    final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

    private CustomBottomDialog dialog;
    private static final String PERIOD_SHOW_STATUS = "period_show_status";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_period_setting);
        ButterKnife.bind(this);
        initView();
        BluetoothManage.getInstance().connectedStatus();
        setSpinState(false);
        isChangePeriod = getIntent().getBooleanExtra("isChangePeriod", true);
//        if(BleParams.isButtonBatteryJewelry()){
//            mModeSubtitle.setText(R.string.period_notify_instructions_no_vibrate);
//        }
    }

    /**
     * 根据界面类型初始化界面
     */
    private void initView() {

//        mUnitConversionTv.setText("Reminder Mode");

        String periodDay = owner.getPeriodDay();

        if (!TextUtils.isEmpty(periodDay)) {
            long periodTime = 0;
            try {
                periodTime = format.parse(periodDay).getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if (periodTime > 0) {
                mLastPeriodTv.setText(periodDay);
            } else {
                mLastPeriodTv.setText(getString(R.string.period_setting_select_please));
            }
        } else
            mLastPeriodTv.setText(getString(R.string.period_setting_select_please));
        int week = PreferencesUtils.getInt(PeriodSettingActivity.this, PeriodBean.PERIOD_WEEK, 5);
        if (week != 0)
            mWeekTv.setText(week + getString(R.string.period_day));
        else
            mWeekTv.setText(getString(R.string.period_setting_select_please));

        int month = PreferencesUtils.getInt(PeriodSettingActivity.this, PeriodBean.PERIOD_MONTH, 28);
        if (month != 0)
            mMonthTv.setText(month + getString(R.string.period_day));
        else
            mMonthTv.setText(getString(R.string.period_setting_select_please));

        long notify_time = PreferencesUtils.getLong(PeriodSettingActivity.this, PeriodBean.PERIOD_NOTIFY_TIME, 0);

        LogUtils.i("aab notify_time = " + notify_time);
        LogUtils.i("aab getDateAllFormatToString = " + DateUtil.getDateAllFormatToString(notify_time));
        if (notify_time != 0)
            mNotifyTimeTv.setText(setStyle(" " + DateUtil.getDateToString("HH", notify_time) + ":" + DateUtil.getDateToString("mm", notify_time)));
        else
            mNotifyTimeTv.setText(setStyle(" " + "08:00"));

        nowSetModel = NotifyUtil.getPeriodNotifyModel(this);

        if (nowSetModel == null) {
            return;
        }

        mCallSwitchInfoTv.setText(getString(R.string.period_setting_hint));

        mCallSwitchCb.setChecked(nowSetModel.isNotifySwitch());
        mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
        mPeriodShowCb.setChecked(PreferencesUtils.getBoolean(this, PERIOD_SHOW_STATUS, true));

        if (!nowSetModel.isNotifySwitch()) {
            mPeriodSettingContent.setVisibility(View.GONE);
        }

        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                setTextColorBtn(false);
                break;
        }

        if (!nowSetModel.containsColor(nowSetModel.getFlashColor())) {
            nowSetModel.setFlashColor("ORANGE");
        }

        int spanCount =  BleParams.isCtJewlery() ? 7 : 6;
        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(PeriodSettingActivity.this, spanCount));
        colorLibraryAdapter = new CustomColorLibraryAdapter(nowSetModel.getFlashColor(), spanCount,false,false);

        colorLibraryAdapter.setOnItemClickListener((adapter, view, position) -> {
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);

            if (colorLibraryBean != null) {
                nowSetModel.setFlashColor(colorLibraryBean.getColor());//颜色名字
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                saveNowModel(false);
            }
        });

        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
        HttpHelper.periodSave.getPeriod()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<PeriodBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodBean> periodBeanHttpBaseBean) {
                        if (periodBeanHttpBaseBean.getErrorCode() == 0) {
                            PeriodBean bean = periodBeanHttpBaseBean.getData();
                            String periodDay = bean.getLast_menst();
                            owner.setPeriodDay(periodDay);
                            long periodTime = 0;
                            try {
                                periodTime = format.parse(periodDay).getTime();
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            if (!TextUtils.isEmpty(periodDay) && periodTime > 0)
                                mLastPeriodTv.setText(periodDay);
                            else
                                mLastPeriodTv.setText(getString(R.string.period_setting_select_please));

                        }
                    }
                });
        getPeriodShow();

        if(BleParams.isNoLoveJewelry()){
            mShowItem.setVisibility(View.GONE);
            mShowItemView.setVisibility(View.GONE);
        }else{
            mShowItem.setVisibility(View.VISIBLE);
            mShowItemView.setVisibility(View.VISIBLE);
        }
        if(BleParams.isButtonBatteryJewelry()){
            mVibrationLayout.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.long_vibration_tv, R.id.short_vibration_tv, R.id.period_last_period_layout,
            R.id.period_week_layout, R.id.period_month_layout, R.id.period_notify_time_layout,
            R.id.notify_switch_click_item, R.id.period_show_item})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.period_last_period_layout:
//                showPeriodSettingDialog(0);
                showBirthDialog();
                break;
            case R.id.period_week_layout:
                showPeriodSettingDialog(1);
                break;
            case R.id.period_month_layout:
                showPeriodSettingDialog(2);
                break;
            case R.id.period_notify_time_layout:
                showPeriodSettingDialog(3);
                break;
            case R.id.notify_switch_click_item:
                mCallSwitchCb.setChecked(!mCallSwitchCb.isChecked());
                nowSetModel.setNotifySwitch(mCallSwitchCb.isChecked());
                isChangePeriod = true;
                saveNowModel(true);
                break;
            case R.id.long_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
                mShortVibrationTv.setBackground(null);
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(true);
                break;
            case R.id.short_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                mLongVibrationTv.setBackground(null);
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(false);
                break;
            case R.id.period_show_item:
                mPeriodShowCb.setChecked(!mPeriodShowCb.isChecked());
                if(!mPeriodShowCb.isChecked()){
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEN_SETPAGE_SELECTDIS);
                }
                updatePeriodShow(mPeriodShowCb.isChecked());
                break;
        }
    }

    private void getPeriodShow() {
        HttpHelper.periodSave.getFriendMenstShow(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<PeriodNotifyStatus>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodNotifyStatus> periodNotifyStatusHttpBaseBean) {
                        if (periodNotifyStatusHttpBaseBean.getErrorCode() == 0) {
                            boolean notifyStatus = periodNotifyStatusHttpBaseBean.getData().getIs_show_menst() == 1;
                            PreferencesUtils.put(PeriodSettingActivity.this, PERIOD_SHOW_STATUS, notifyStatus);
                            mPeriodShowCb.setChecked(notifyStatus);
                        }
                    }
                });
    }

    private void updatePeriodShow(boolean isShow) {
        HttpHelper.periodSave.setFriendMenstShow(isShow ? 1 : 0)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<PeriodNotifyStatus>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(PeriodSettingActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodNotifyStatus> periodNotifyStatusHttpBaseBean) {
                        if (periodNotifyStatusHttpBaseBean.getErrorCode() == 0) {
                            PreferencesUtils.put(PeriodSettingActivity.this, PERIOD_SHOW_STATUS, isShow);
                        }
                    }
                });
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> {
            saveInfo();
//                finish();
        });
        setTopTitle(R.string.period_notification);
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationIv.setVisibility(View.VISIBLE);
            mShortVibrationIv.setVisibility(View.GONE);

            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        } else {
            mLongVibrationIv.setVisibility(View.GONE);
            mShortVibrationIv.setVisibility(View.VISIBLE);

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        }
    }


    /**
     * 保存对应的数据, 根据不同的类型, 做相应的首饰反馈
     *
     * @param isSwitch 是否是开关操作
     */
    private void saveNowModel(boolean isSwitch) {

        if (!isSwitch) {
            BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
        }
        NotifyUtil.setPeriodNotify(this, nowSetModel);
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_PERIOD_STATUS, null);
        if (isSwitch) {
            mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
            //切换时候的动画
            Animation anim = AnimationUtils.loadAnimation(this, nowSetModel.isNotifySwitch() ? R.anim.layout_open : R.anim.layout_close);
            if (nowSetModel.isNotifySwitch()) {
                mPeriodSettingContent.setVisibility(View.VISIBLE);
            } else {
                anim.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        mPeriodSettingContent.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
            }
            mPeriodSettingContent.startAnimation(anim);
        }
    }

    //设置经期的Dialog
    private void showPeriodSettingDialog(int type) {
        dialog = new CustomBottomDialog(this);
        switch (type) {
            case 0:
                dialog.setTitle(R.string.birthday);
                final PeriodSettingView periodSettingView = new PeriodSettingView(this);
                dialog.setMainView(periodSettingView);
                dialog.setSaveClick(v -> {
                    String date = periodSettingView.getN_year()
                            + "-" + periodSettingView.getN_month() + "-"
                            + periodSettingView.getN_day();
                    owner.setPeriodDay(date);
                    mLastPeriodTv.setText(date);

                    dialog.dismiss();
                });
                break;
            case 1:
                dialog.setTitle(getString(R.string.period_setting_week));
//                dialog.setInfo(getString(R.string.period_setting_select_week_info));
                LinearLayout weekLayout = new LinearLayout(this);
                weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
                weekLayout.setOrientation(LinearLayout.HORIZONTAL);

                weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                        Apputils.dp2px(this, 20), 0);
                final WheelView weekWheelView = new WheelView(this);
                weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
                weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
                weekWheelView.setItems(Arrays.asList(ConfigData.PERIOD_WEEK_ARRAY), 3,
                        " " + getString(R.string.period_day));
                // 获取当前设置的时间去设置wheelview
                final int weekMin = PreferencesUtils.getInt(this, PeriodBean.PERIOD_WEEK, 5);
                weekWheelView.setSeletion(weekMin - Integer.parseInt(ConfigData.PERIOD_WEEK_ARRAY[0]));
                weekLayout.addView(weekWheelView);
                dialog.setMainView(weekLayout);
                dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
                    isChangePeriod = true;
                    mWeekTv.setText(weekWheelView.getSeletedItem() + getString(R.string.period_day));
                    int parseInt = Integer.parseInt(weekWheelView.getSeletedItem());
                    PreferencesUtils.put(PeriodSettingActivity.this, PeriodBean.PERIOD_WEEK, parseInt);

                    dialog.dismiss();
                });
                break;
            case 2:
                dialog.setTitle(getString(R.string.period_setting_month));
//                dialog.setInfo(getString(R.string.period_setting_select_month_info));
                LinearLayout monthLayout = new LinearLayout(this);
                monthLayout.setLayoutParams(new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
                monthLayout.setOrientation(LinearLayout.HORIZONTAL);

                monthLayout.setPadding(Apputils.dp2px(this, 20), 0,
                        Apputils.dp2px(this, 20), 0);
                final WheelView wheelView = new WheelView(this);
                wheelView.setLayoutParams(new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
                wheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
                wheelView.setItems(Arrays.asList(ConfigData.PERIOD_MONTH_ARRAY), 3,
                        " " + getString(R.string.period_day));
                // 获取当前设置的时间去设置wheelview
                final int monthMin = PreferencesUtils.getInt(this, PeriodBean.PERIOD_MONTH, 28);
                wheelView.setSeletion(monthMin - Integer.parseInt(ConfigData.PERIOD_MONTH_ARRAY[0]));
                monthLayout.addView(wheelView);
                dialog.setMainView(monthLayout);
                dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
                    isChangePeriod = true;
                    mMonthTv.setText(wheelView.getSeletedItem() + getString(R.string.period_day));
                    int parseInt = Integer.parseInt(wheelView.getSeletedItem());
                    PreferencesUtils.put(PeriodSettingActivity.this, PeriodBean.PERIOD_MONTH, parseInt);

                    dialog.dismiss();
                });
                break;
            case 3:
                dialog.setTitle(getString(R.string.period_setting_select_remind_title));
//                dialog.setInfo(getString(R.string.period_setting_select_remind_info));
                String[] mins = {"00", "30"};
                RelativeLayout layout = (RelativeLayout) View.inflate(this,
                        R.layout.sedentary_reminder_start_time_dialog, null);
                final WheelView hh_wl_time = (WheelView) layout.findViewById(R.id.hh_wl);
                final WheelView mm_wl_time = (WheelView) layout.findViewById(R.id.mm_wl);
                final TextView tv_pre = (TextView) layout.findViewById(R.id.tv_pre);
                hh_wl_time.setItems(Arrays
                                .asList(ConfigData.SEDENTARY_REMINDER_START_TIME_HH_ARRAY), 3,
                        null);
                mm_wl_time.setItems(Arrays
                                .asList(mins), 3,
                        null);
                tv_pre.setText(getString(R.string.period_setting_one_day_before));
                long time = PreferencesUtils.getLong(this, PeriodBean.PERIOD_NOTIFY_TIME, DateUtil.getStringToDate("HH:mm", "08:00"));
                hh_wl_time.setSeletion(Integer.parseInt(DateUtil.getDateToString("HH", time)));
                if (Integer.parseInt(DateUtil.getDateToString("mm", time)) == 0) {
                    mm_wl_time.setSeletion(0);
                } else {
                    mm_wl_time.setSeletion(1);
                }

                dialog.setMainView(layout);
                dialog.setSaveClick(v -> {
                    isChangePeriod = true;
                    String text = hh_wl_time.getSeletedItem() + ":" + mm_wl_time.getSeletedItem();
                    long selectUtc = DateUtil.getStringToDate("HH:mm", text);
                    PreferencesUtils.put(PeriodSettingActivity.this, PeriodBean.PERIOD_NOTIFY_TIME, selectUtc);
                    mNotifyTimeTv.setText(setStyle(" " + text));
                    dialog.dismiss();
                });
                break;
        }
        dialog.show();
    }

    private void saveInfo() {
        //如果没有修改经期时间数据，就直接返回。不然就保存数据
        if (!isChangePeriod) {
            finish();
        } else {
            if (!nowSetModel.isNotifySwitch()) {
                HttpHelper.periodSave.savePeriodOff((nowSetModel.isNotifySwitch() ? 1 : 0))
                        .subscribeOn(Schedulers.newThread())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Observer<HttpBaseBean<PeriodBean>>() {
                            @Override
                            public void onCompleted() {
                                finish();
                            }

                            @Override
                            public void onError(Throwable e) {
                                ToastUtils.showShort(PeriodSettingActivity.this, R.string.error_net);
                                finish();
                            }

                            @Override
                            public void onNext(HttpBaseBean<PeriodBean> periodBeanHttpBaseBean) {
                                if (periodBeanHttpBaseBean.getErrorCode() == 0) {

                                }
                            }
                        });
            } else {
                String periodDay = owner.getPeriodDay();
                int week = PreferencesUtils.getInt(PeriodSettingActivity.this, PeriodBean.PERIOD_WEEK, 5);
                int month = PreferencesUtils.getInt(PeriodSettingActivity.this, PeriodBean.PERIOD_MONTH, 28);

                if (TextUtils.isEmpty(periodDay)) {
                    notifyEmpty();
                    return;
                }
                if (week == 0) {
                    notifyEmpty();
                    return;
                }
                if (month == 0) {
                    notifyEmpty();
                    return;
                }

                String notifyTime = "08:00";
                long notify_time = PreferencesUtils.getLong(PeriodSettingActivity.this, PeriodBean.PERIOD_NOTIFY_TIME, 0);
                if (notify_time != 0)
                    notifyTime = DateUtil.getDateToString("HH", notify_time) + ":" + DateUtil.getDateToString("mm", notify_time);
                else
                    notifyTime = "08:00";

                HttpHelper.periodSave.savePeriod((nowSetModel.isNotifySwitch() ? 1 : 0), periodDay, week, month, notifyTime, nowSetModel.getFlashColor(), nowSetModel.getVibrationHttpString())
                        .subscribeOn(Schedulers.newThread())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Observer<HttpBaseBean<PeriodBean>>() {
                            @Override
                            public void onCompleted() {
                                finish();
                            }

                            @Override
                            public void onError(Throwable e) {
                                ToastUtils.showShort(PeriodSettingActivity.this, R.string.error_net);
                                finish();
                            }

                            @Override
                            public void onNext(HttpBaseBean<PeriodBean> periodBeanHttpBaseBean) {
                                if (periodBeanHttpBaseBean.getErrorCode() == 0) {

                                }
                            }
                        });
            }

        }
    }

    private void notifyEmpty() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(PeriodSettingActivity.this);
        commonMiddleDialog.setMessage(R.string.period_setting_empty_error);
        commonMiddleDialog.setSure(v -> {
            nowSetModel.setNotifySwitch(false);
            NotifyUtil.setPeriodNotify(PeriodSettingActivity.this, nowSetModel);
            finish();
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    private void showBirthDialog() {
        String periodDay = owner.getPeriodDay();
        if (TextUtils.isEmpty(periodDay))
            periodDay = format.format(System.currentTimeMillis());

        long periodTime;
        try {
            periodTime = format.parse(periodDay).getTime();
        } catch (ParseException e) {
            periodTime = System.currentTimeMillis();
        }

        long tenYears = 1L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack((timePickerView, millseconds) -> {
                    isChangePeriod = true;
                    String tempBirthday = format.format(millseconds);
                    owner.setPeriodDay(tempBirthday);
                    mLastPeriodTv.setText(tempBirthday);

                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.period_setting_last_period_dialog))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - tenYears)
                .setMaxMillseconds(System.currentTimeMillis())
                .setCurrentMillseconds(periodTime)
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "year_month_day");
    }

    @Override
    public void onBackPressed() {
        saveInfo();
    }

    private SpannableString setStyle(String time) {
        String string = getString(R.string.period_setting_one_day_before_pre) + time;
        SpannableString spannableString = new SpannableString(string);
        int index = 0;
        int endIndex;
        if (Apputils.systemLanguageIsChinese(PeriodSettingActivity.this)) {
            endIndex = 4;
        } else {
            endIndex = 12;
        }
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff000000")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }
}
