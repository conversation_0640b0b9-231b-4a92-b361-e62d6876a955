//package com.totwoo.totwoo.activity.security;
//
//import android.content.Intent;
//import android.content.pm.PackageManager;
//import android.net.Uri;
//import android.os.Bundle;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//import androidx.constraintlayout.widget.ConstraintLayout;
//import androidx.recyclerview.widget.LinearLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.etone.framework.annotation.EventInject;
//import com.etone.framework.annotation.InjectUtils;
//import com.etone.framework.event.EventData;
//import com.etone.framework.event.SubscriberListener;
//import com.etone.framework.event.TaskType;
//import com.facebook.FacebookCallback;
//import com.facebook.share.Sharer;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.S;
//import com.totwoo.totwoo.activity.BaseActivity;
//import com.totwoo.totwoo.activity.ContactsActivityForEmergency;
//import com.totwoo.totwoo.bean.SecurityContactsBean;
//import com.totwoo.totwoo.bean.SecurityContactsHttpBean;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.utils.CommonArgs;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.PermissionUtil;
//import com.totwoo.totwoo.utils.ShareUtilsSingleton;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.widget.CommonMiddleDialog;
//import com.totwoo.totwoo.widget.CommonShareDialog;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//import rx.Subscriber;
//
//import static com.totwoo.totwoo.activity.ContactsListActivity.SECURITY_CONTACT_LIST;
//
//public class SecurityListActivity extends BaseActivity implements SubscriberListener {
//
//    @BindView(R.id.security_list_rv)
//    RecyclerView mRecyclerView;
//    @BindView(R.id.security_list_add_cl)
//    ConstraintLayout mAddCl;
//    @BindView(R.id.security_list_bottom_tv)
//    TextView mNextTv;
//
//    //    private CustomDialog shareDialog;
//    private CommonMiddleDialog delDialog;
//
//    private ArrayList<SecurityContactsBean> contactsBeans;
//    private SecurityContactsAdapter securityContactsAdapter;
//    private FacebookCallback<Sharer.Result> facebookCallback;
//    public static int INIT_STATUS = 0;
//    public static int ADD_STATUS = 1;
//    public static int MANAGER_STATUS = 2;
//    private int current_status;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_security_list);
//        ButterKnife.bind(this);
//        InjectUtils.injectOnlyEvent(this);
//        contactsBeans = new ArrayList<>();
//        mRecyclerView.setLayoutManager(new LinearLayoutManager(SecurityListActivity.this));
//        securityContactsAdapter = new SecurityContactsAdapter();
//        mRecyclerView.setAdapter(securityContactsAdapter);
//        getSecurityContacts();
//        current_status = getIntent().getIntExtra(CommonArgs.FROM_TYPE, 2);
//        if (current_status == INIT_STATUS || current_status == ADD_STATUS) {
//            mNextTv.setVisibility(View.VISIBLE);
//        }
//    }
//
//    @Override
//    protected void initTopBar() {
//        super.initTopBar();
//        if (current_status == MANAGER_STATUS) {
//            setTopBackIcon(R.drawable.back_icon_black);
//            setTopLeftOnclik(v -> finish());
//        }
//        setTopTitle(R.string.safe_contacts_title);
//    }
//
//    @OnClick({R.id.security_list_add_cl, R.id.security_list_bottom_tv})
//    protected void onClick(View view) {
//        switch (view.getId()) {
//            case R.id.security_list_add_cl:
//                if (!PermissionUtil.hasContactsPermission(SecurityListActivity.this)) {
//                    return;
//                }
//                startContactsList();
//                break;
//            case R.id.security_list_bottom_tv:
//                startActivity(new Intent(SecurityListActivity.this, EmergencyDocActivity.class).putExtra(CommonArgs.FROM_TYPE, current_status));
//                break;
//        }
//    }
//
//    private void startContactsList() {
////        Intent intent = new Intent(SecurityListActivity.this, ContactsListActivity.class);
////        intent.putExtra(IS_SECURITY_CONTACT, true);
////        if (contactsBeans != null && contactsBeans.size() > 0) {
////            intent.putExtra(SECURITY_CONTACT_LIST, contactsBeans);
////        }
////        startActivityForResult(intent, 100);
//
//        Intent intent = new Intent(SecurityListActivity.this, ContactsActivityForEmergency.class);
//        if (contactsBeans != null && contactsBeans.size() > 0) {
//            intent.putExtra(SECURITY_CONTACT_LIST, contactsBeans);
//        }
//        startActivityForResult(intent, 100);
//    }
//
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        getSecurityContacts();
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        for (int grantResult : grantResults) {
//            if (grantResult != PackageManager.PERMISSION_DENIED) {
//                startContactsList();
//            }
//        }
//        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
//    }
//
//    private void getSecurityContacts() {
//        HttpHelper.safeService.getContacts(2001)
//                .compose(HttpHelper.rxSchedulerHelper())
//                .subscribe(new Subscriber<HttpBaseBean<SecurityContactsHttpBean>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        ToastUtils.showShort(SecurityListActivity.this, getString(R.string.error_net));
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
//                        if (securityContactsHttpBeanHttpBaseBean.getErrorCode() == 0) {
//                            handlerSecurityBeans(securityContactsHttpBeanHttpBaseBean);
//                        }
//                    }
//                });
//    }
//
//    private void handlerSecurityBeans(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
//        contactsBeans.clear();
//        if (securityContactsHttpBeanHttpBaseBean.getData() != null && securityContactsHttpBeanHttpBaseBean.getData().getInfo() != null && securityContactsHttpBeanHttpBaseBean.getData().getInfo().size() > 0) {
//            contactsBeans.addAll(securityContactsHttpBeanHttpBaseBean.getData().getInfo());
//        }
//        securityContactsAdapter.notifyDataSetChanged();
//        if (contactsBeans.size() == 3) {
//            mAddCl.setVisibility(View.GONE);
//        } else {
//            mAddCl.setVisibility(View.VISIBLE);
//        }
//    }
//
//    private void delSecurityContact(String phoneNumber) {
//        HttpHelper.safeService.delContacts(phoneNumber)
//                .compose(HttpHelper.rxSchedulerHelper())
//                .subscribe(new Subscriber<HttpBaseBean<SecurityContactsHttpBean>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        ToastUtils.showShort(SecurityListActivity.this, getString(R.string.error_net));
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
//                        if (securityContactsHttpBeanHttpBaseBean.getErrorCode() == 0) {
//                            handlerSecurityBeans(securityContactsHttpBeanHttpBaseBean);
//                        }
//                    }
//                });
//    }
//
//    public class SecurityContactsAdapter extends RecyclerView.Adapter<SecurityContactsAdapter.ViewHolder> {
//
//        @NonNull
//        @Override
//        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
//            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.security_contact_bean, parent, false);
//            return new ViewHolder(itemView);
//        }
//
//        @Override
//        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
//            holder.mNameTv.setText(contactsBeans.get(position).getName());
//            holder.mContentCl.setOnLongClickListener(v -> {
//                showDelDialog(contactsBeans.get(position).getTel());
//                return true;
//            });
//            holder.mShareTv.setOnClickListener(v -> showShareDialog(contactsBeans.get(position).getTel()));
//
//        }
//
//        @Override
//        public int getItemCount() {
//            return contactsBeans == null ? 0 : contactsBeans.size();
//        }
//
//        public class ViewHolder extends RecyclerView.ViewHolder {
//            @BindView(R.id.security_contact_name_tv)
//            TextView mNameTv;
//            @BindView(R.id.security_contact_share_tv)
//            TextView mShareTv;
//            @BindView(R.id.security_contact_content_cl)
//            ConstraintLayout mContentCl;
//
//            public ViewHolder(View itemView) {
//                super(itemView);
//                ButterKnife.bind(this, itemView);
//            }
//        }
//    }
//
//    private void showDelDialog(String phoneNumber) {
//        delDialog = new CommonMiddleDialog(this);
//        delDialog.setMessage(getString(R.string.safe_contacts_share_content));
//        delDialog.setSure(R.string.common_confirm, v -> {
//            delDialog.dismiss();
//            delSecurityContact(phoneNumber);
//        });
//        delDialog.setCancel(R.string.cancel);
//        delDialog.show();
//    }
//
//    private CommonShareDialog commonShareDialog;
//
//    private void showShareDialog(String phoneNumber) {
//        String shareInfo = getString(R.string.safe_contacts_share_text);
//        if (commonShareDialog == null) {
////            if (Apputils.systemLanguageIsChinese(SecurityListActivity.this)) {
//            List<CommonShareType> types = new ArrayList<>();
//            types.add(CommonShareType.WECHAT);
//            types.add(CommonShareType.MESSAGE);
//            commonShareDialog = new CommonShareDialog(SecurityListActivity.this, types, v -> {
//                switch ((CommonShareType) v.getTag()) {
//                    case WECHAT:
//                        ShareUtilsSingleton.getInstance().shareTextToWechat(shareInfo);
//                        commonShareDialog.dismiss();
//                        break;
//                    case MESSAGE:
//                        shareGuardByMessage(phoneNumber);
//                        commonShareDialog.dismiss();
//                        break;
//                }
//
//            });
//            commonShareDialog.setCustomTitle(getResources().getString(R.string.safe_contacts_share_title));
////            }
////            else {
////                facebookCallback = new FacebookCallback<Sharer.Result>() {
////                    @Override
////                    public void onSuccess(Sharer.Result result) {
////                        ToastUtils.showShort(SecurityListActivity.this, getResources().getString(R.string.share_complete));
////                    }
////
////                    @Override
////                    public void onCancel() {
////                        ToastUtils.showShort(SecurityListActivity.this, getResources().getString(R.string.share_cancel));
////                    }
////
////                    @Override
////                    public void onError(FacebookException error) {
////                        ToastUtils.showShort(SecurityListActivity.this, getResources().getString(R.string.share_error));
////                    }
////                };
////                List<CommonShareType> types = new ArrayList<>();
////                types.add(CommonShareType.FACEBOOK);
////                types.add(CommonShareType.MESSAGE);
////                commonShareDialog = new CommonShareDialog(SecurityListActivity.this, types, v -> {
////                    switch ((CommonShareType)v.getTag()){
////                        case FACEBOOK:
////                            ShareUtilsSingleton.getInstance().shareTextToFacebook(shareInfo, SecurityListActivity.this, facebookCallback);
////                            shareDialog.dismiss();
////                            break;
////                        case MESSAGE:
////                            shareGuardByMessage(phoneNumber);
////                            shareDialog.dismiss();
////                            break;
////                    }
////
////                });
////                commonShareDialog.setCustomTitle(getResources().getString(R.string.safe_contacts_share_title));
////            }
//        }
//        commonShareDialog.show();
//    }
//
////    private void showShareDialog(String phoneNumber) {
////        shareDialog = new CustomDialog(this);
////        String shareInfo = getString(R.string.safe_contacts_share_text);
////        View view = getLayoutInflater().inflate(R.layout.safe_share_layout, null);
//////        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
////        view.findViewById(R.id.common_share_wechat_iv).setOnClickListener(v -> {
////            ShareUtilsSingleton.getInstance().shareTextToWechat(shareInfo);
////            shareDialog.dismiss();
////        });
////        view.findViewById(R.id.common_share_message_iv).setOnClickListener(v -> {
////            shareGuardByMessage(phoneNumber);
////            shareDialog.dismiss();
////        });
//////        }
//////        else {
//////            facebookCallback = new FacebookCallback<Sharer.Result>() {
//////                @Override
//////                public void onSuccess(Sharer.Result result) {
//////                    ToastUtils.showShort(SecurityListActivity.this, getResources().getString(R.string.share_complete));
//////                }
//////
//////                @Override
//////                public void onCancel() {
//////                    ToastUtils.showShort(SecurityListActivity.this, getResources().getString(R.string.share_cancel));
//////                }
//////
//////                @Override
//////                public void onError(FacebookException error) {
//////                    ToastUtils.showShort(SecurityListActivity.this, getResources().getString(R.string.share_error));
//////                }
//////            };
//////            view.findViewById(R.id.common_share_facebook_iv).setOnClickListener(v -> {
//////                ShareUtilsSingleton.getInstance().shareTextToFacebook(shareInfo, SecurityListActivity.this, facebookCallback);
//////                shareDialog.dismiss();
//////            });
//////            view.findViewById(R.id.common_share_message_iv).setOnClickListener(v -> {
//////                shareGuardByMessage(phoneNumber);
//////                shareDialog.dismiss();
//////            });
//////            view.findViewById(R.id.common_share_facebook_iv).setVisibility(View.VISIBLE);
//////            view.findViewById(R.id.common_share_wechat_iv).setVisibility(View.GONE);
//////        }
////        shareDialog.setMainLayoutView(view);
////        shareDialog.setPositiveButton(R.string.cancel, v -> shareDialog.dismiss());
////        shareDialog.show();
////    }
//
//    protected void shareGuardByMessage(String phoneNumber) {
//        Uri smsToUri = Uri.parse("smsto:+" + phoneNumber);
//
//        Intent intent = new Intent(Intent.ACTION_SENDTO, smsToUri);
//
//        intent.putExtra("sms_body", getString(R.string.safe_contacts_share_text));
//        startActivity(intent);
//    }
//
//    @EventInject(eventType = S.E.E_IMEI_UPDATE_SUCCEED, runThread = TaskType.UI)
//    public void imeiUpdateSucceed(EventData data) {
//        finish();
//    }
//
//    @Override
//    public void onEventException(String eventType, EventData data, Throwable e) {
//
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        InjectUtils.injectUnregisterListenerAll(this);
//    }
//}
