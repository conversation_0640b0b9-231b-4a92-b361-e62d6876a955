package com.totwoo.totwoo.activity.nfc;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.event.EventBus;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.nfc.MediaBean;
import com.totwoo.totwoo.databinding.ActivityMeidaDetailBinding;
import com.totwoo.totwoo.utils.AppObserver;
import com.totwoo.totwoo.utils.BlackAlertDialogUtil;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Func1;

/**
 * 图文详情页面
 */
public class NfcMediaDetailActivity extends BaseActivity {
    private static final int MEDIA_INFO_MAX_LENGTH = 100;

    private ActivityMeidaDetailBinding binding;

    private String mediaId;
    private String videoPath;
    private String coverPath;
    private String mediaInfo;

    public static void go(Context context, String mediaId, String imagePath, String info) {
        go(context, mediaId, null, imagePath, info);
    }

    /**
     * 预览
     *
     * @param context
     * @param videoPath 视频路径
     * @param coverPath 图片路径, 如果有视频的话, 则作为封面使用
     * @param info      用于底部展示的文字
     */
    public static void go(Context context, String mediaId, String videoPath, String coverPath, String info) {
        context.startActivity(new Intent(context, NfcMediaDetailActivity.class)
                .putExtra("mediaId", mediaId)
                .putExtra(CommonArgs.VIDEO_PATH, videoPath)
                .putExtra(CommonArgs.COVER_PATH, coverPath)
                .putExtra(CommonArgs.MEDIA_INFO, info));
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMeidaDetailBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        CommonUtils.setStateBarTransparentForBlackUI(this);

        initView();
    }

    private void initView() {
        mediaId = getIntent().getStringExtra("mediaId");
        videoPath = getIntent().getStringExtra(CommonArgs.VIDEO_PATH);
        coverPath = getIntent().getStringExtra(CommonArgs.COVER_PATH);
        mediaInfo = getIntent().getStringExtra(CommonArgs.MEDIA_INFO);

        binding.nfcMediaDetailBack.setOnClickListener(v -> checkBack());


        binding.nfcMediaDetailTitleTv.setText(TextUtils.isEmpty(videoPath) ?
                R.string.secret_media_pic_edit_title : R.string.secret_media_video_edit_title);

        // 图片显示
        Glide.with(this).load(coverPath != null ? coverPath : videoPath)
                .apply(new RequestOptions().diskCacheStrategy(DiskCacheStrategy.NONE).skipMemoryCache(true))
                .into(binding.nfcMediaDetailImage);

//        binding.nfcMediaDetailImage.setOnClickListener(v -> saveAndBack());
        binding.nfcMediaDetailDeleteIcon.setOnClickListener(v -> delete());

        // 视频与否
        binding.nfcMediaDetailVideoPlayIcon.setVisibility(videoPath == null ? View.GONE : View.VISIBLE);

        // 文字
        binding.nfcMediaDetailInfoTv.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                int max = Apputils.systemLanguageIsChinese(NfcMediaDetailActivity.this)
                        ? MEDIA_INFO_MAX_LENGTH : MEDIA_INFO_MAX_LENGTH * 2;
                binding.nfcMediaDetailInfoCountTv.setText(String.format(Locale.getDefault(), "%d/%d", s.length(), max));

                if (s.length() > max) {
                    ToastUtils.showLong(NfcMediaDetailActivity.this, R.string.wish_out_of_limit);
                    binding.nfcMediaDetailInfoTv.setText(s.subSequence(0, max));
                    binding.nfcMediaDetailInfoTv.setSelection(binding.nfcMediaDetailInfoTv.length() - 1);
                }
            }
        });
        binding.nfcMediaDetailInfoTv.setText(mediaInfo);

        // 预览
        binding.nfcMediaDetailPreview.setOnClickListener(v -> saveAndBack());
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            checkBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void saveAndBack() {
        boolean isVideo = !TextUtils.isEmpty(videoPath);
        AppObserver<JsonElement> saveObserver = new AppObserver<JsonElement>() {
            @Override
            public void onCompleted() {
                super.onCompleted();
                NfcLoading.dismiss();
            }

            @Override
            public void onSuccess(JsonElement baseBean) {
                // 上传成功的逻辑
                ToastUtils.showLong(NfcMediaDetailActivity.this, R.string.saved_success);
                EventBus.onPostReceived(S.E.E_GIFT_SEND_SUCCEED, null);

                setResult(RESULT_OK, new Intent().putExtra(NfcMediaListActivity.EXTRA_NFC_SECRET_MEDIA_DATA, new Gson().fromJson(baseBean, MediaBean.class)));
                finish();
            }
        };

        NfcLoading.show(this);
        if (isVideo) {
            Observable.zip(HttpHelper.uploadFile(3, "media_nfc", videoPath),
                            HttpHelper.uploadFile(1, "media_nfc", coverPath),
                            Arrays::asList)
                    .flatMap((Func1<List<String>, Observable<HttpBaseBean<JsonElement>>>) strings -> HttpHelper.nfcService.mediaSave(2, "", strings.get(0), strings.get(1), binding.nfcMediaDetailInfoTv.getText().toString()))
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(saveObserver);
        } else {
            HttpHelper.uploadFile(1, "media_nfc", coverPath)
                    .flatMap((Func1<String, Observable<HttpBaseBean<JsonElement>>>) s -> HttpHelper.nfcService.mediaSave(1, s, "", "", binding.nfcMediaDetailInfoTv.getText().toString()))
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(saveObserver);
        }
    }


    private void checkBack() {
        BlackAlertDialogUtil.showCommonDialog(this, R.string.nfc_back_without_save, this::finish);
    }

    private void delete() {
        BlackAlertDialogUtil.showCommonDialog(this, R.string.custom_notify_list_delete_hint, this::finish);
    }
}
