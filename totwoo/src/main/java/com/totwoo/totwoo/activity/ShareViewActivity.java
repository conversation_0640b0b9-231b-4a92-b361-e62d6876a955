//package com.totwoo.totwoo.activity;
//
//import android.content.Context;
//import android.content.Intent;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.Message;
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.EditText;
//import android.widget.ImageView;
//
//import com.mob.tools.utils.UIHandler;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.utils.ShareUtils;
//import com.totwoo.totwoo.utils.StringUtils;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.widget.CustomProgressBarDialog;
//
//import java.util.HashMap;
//
//import cn.sharesdk.facebook.Facebook;
//import cn.sharesdk.framework.Platform;
//import cn.sharesdk.framework.PlatformActionListener;
//import cn.sharesdk.framework.ShareSDK;
//
//
//public class ShareViewActivity extends BaseActivity {
//    private static final String PREF_SHARE_TYPE = "pref_share_type";
//    private static final String PREF_SHARE = "pref_share";
//
//    private final static int SHARE_SUCCESS = 1;
//    private final static int SHARE_CANCEL = 2;
//    private final static int SHARE_ERROR = 3;
//
//    private EditText infoEt;
//    private ImageView imageIv;
//    private ImageView deleteIv;
//    private int shareType;
//    private ShareUtils share;
//
//    private CustomProgressBarDialog dialog;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_share_view);
//
//        infoEt = (EditText) findViewById(R.id.share_text_et);
//        imageIv = (ImageView) findViewById(R.id.share_image_iv);
//        deleteIv = (ImageView) findViewById(R.id.share_delete_iv);
//
//        deleteIv.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                imageIv.setVisibility(View.GONE);
//                share.setShareImgPath(null);
//                deleteIv.setVisibility(View.GONE);
//            }
//        });
//
//        infoEt.requestFocus();
//        imageIv.setImageResource(R.drawable.home_bright_holder_bg);
//
//        shareType = getIntent().getIntExtra(PREF_SHARE_TYPE, 0);
//        share = getIntent().getParcelableExtra(PREF_SHARE);
//
//        infoEt.setText(share.getShareTitle());
//
//        dialog = new CustomProgressBarDialog(this);
//        dialog.setMessage(R.string.loadding);
//
//        imageIv.setImageBitmap(android.graphics.BitmapFactory.decodeFile(share.getShareImgPath()));
//    }
//
//    @Override
//    protected void initTopBar() {
//        setTopBackIcon(R.drawable.back_icon_black);
//        setTopRightString(R.string.share);
//        setTopTitle(R.string.share);
//        setTopRightOnClick(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                doShare();
//            }
//        });
//    }
//
//    private void doShare() {
//        Facebook.ShareParams sp = new Facebook.ShareParams();
//        String text = infoEt.getText().toString().trim();
//
//        if (TextUtils.isEmpty(text)){
//            text = share.getShareTitle();
//        }
//
//        sp.setText(text);
//        switch (shareType) {
//            case ShareUtils.SHARE_IMAGE_TEXT:
//                sp.setImagePath(share.getShareImgPath());
//                break;
//            case ShareUtils.SHARE_WEBPAGE:
//                sp.setText(text + "  " + share.getShareUrl());
//                sp.setImagePath(share.getShareImgPath());
//                break;
//        }
//
//        // showShare(false, Facebook.NAME, false);
//        Platform facebook = ShareSDK.getPlatform(Facebook.NAME);
//        // 设置分享事件回调
//        facebook.setPlatformActionListener(mListener);
//
//        // 执行图文分享
//        facebook.share(sp);
//        dialog.show();
//    }
//
//    public static void showEditShare(Context content, ShareUtils share, int shareType) {
//        Intent intent  = new Intent(content, ShareViewActivity.class);
//        intent.putExtra(PREF_SHARE , share);
//        intent.putExtra(PREF_SHARE_TYPE, shareType);
//        content.startActivity(intent);
//    }
//
//
//    public PlatformActionListener mListener = new PlatformActionListener() {
//
//        @Override
//        public void onCancel(Platform platf, int arg1) {
//            LogUtils.i("share", "onCancel");
//            if (arg1 == Platform.ACTION_SHARE) {
//                UIHandler.sendEmptyMessage(SHARE_CANCEL, mHandler);
//            }
//        }
//
//        @Override
//        public void onComplete(Platform platf, int arg1,
//                               HashMap<String, Object> arg2) {
//            LogUtils.i("share", "onComplete");
//
//            if (arg1 == Platform.ACTION_SHARE) {
//                UIHandler.sendEmptyMessage(SHARE_SUCCESS, mHandler);
//                LogUtils.i("share", "响应分享事件");
//
//            }
//            if (arg1 == Platform.SHARE_TEXT) {
//                LogUtils.i("share", "响应分享文本事件");
//                UIHandler.sendEmptyMessage(SHARE_SUCCESS, mHandler);
//                LogUtils.i("", "..");
//            }
//            // UIHandler.sendEmptyMessage(SHARE_SUCCESS, mHandler);
//        }
//
//        @Override
//        public void onError(Platform platf, int arg1, Throwable arg2) {
//            LogUtils.i("share", "onError:" + arg2.toString());
//            if (arg1 == Platform.ACTION_SHARE) {
//                UIHandler.sendEmptyMessage(SHARE_ERROR, mHandler);
//            }
//        }
//    };
//
//
//    private Handler.Callback mHandler = new Handler.Callback() {
//        @Override
//        public boolean handleMessage(Message msg) {
//            switch (msg.what) {
//                case SHARE_ERROR: {
//                    ToastUtils.showLong(ShareViewActivity.this, R.string.share_error);
//                    dialog.dismiss();
//                }
//                break;
//                case SHARE_SUCCESS: {
//                    ToastUtils.showLong(ToTwooApplication.baseContext, R.string.share_complete);
//                    dialog.dismiss();
//                    finish();
//                }
//                break;
//                case SHARE_CANCEL: {
//                    ToastUtils.showLong(ShareViewActivity.this, R.string.share_cancel);
//                    dialog.dismiss();
//                    break;
//                }
//            }
//            return true;
//        }
//    };
//
//}
