package com.totwoo.totwoo.activity;

import static android.webkit.WebView.enableSlowWholeDocumentDraw;

import android.annotation.SuppressLint;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Picture;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.ArrayAdapter;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.ShareUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.config.FullscreenHolder;
import com.totwoo.totwoo.widget.config.IWebPageView;
import com.totwoo.totwoo.widget.config.MyWebChromeClient;
import com.totwoo.totwoo.widget.config.MyWebViewClient;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 网页可以处理:
 * 点击相应控件:
 * - 拨打电话、发送短信、发送邮件
 * - 上传图片(版本兼容)
 * - 全屏播放网络视频
 * - 进度条显示
 * - 返回网页上一层、显示网页标题
 * JS交互部分：
 * - 前端代码嵌入js(缺乏灵活性)
 * - 网页自带js跳转
 */
public class WebViewActivity extends BaseActivity implements IWebPageView, View.OnClickListener {

    @BindView(R.id.web_menu_layout)
    RelativeLayout menuLayout;

    @BindView(R.id.web_menu_list)
    ListView menuListView;

    // 进度条
    @BindView(R.id.pb_progress)
    ProgressBar mProgressBar;
    @BindView(R.id.web_container)
    FrameLayout webContainer;
    // 全屏时视频加载view
    @BindView(R.id.video_fullView)
    FrameLayout videoFullView;
    // 加载视频相关
    private MyWebChromeClient mWebChromeClient;
    // 是否是全屏视频链接
    private boolean mIsMovie;
    // 网页链接
    private String mUrl;

    public static final String KEY_TITLE = "KEY_TITLE";


    /**
     * 当前网页标题
     */
    private String mWebTitle;
    private ShareUtils shareUtils;
    private boolean isShare;
    private String shareTitle;
    private String shareContent;
    private String is_full_show;

    private WebView webView;

    /**
     * 分享对话框
     */
    private CustomDialog shareDialog;

    @Override
    protected void initTopBar() {
        if (TextUtils.equals("1", is_full_show)) {
            setTopLeftIcon(R.drawable.back_icon_black3);
//            setTopTitle(getIntent().getStringExtra(KEY_TITLE));

            setTopbarBackground(R.color.transparent);
            if (isShare) {
                setTopRightIcon(R.drawable.share_ico_3);
                setTopRightOnClick(v -> {
                    // showMenu(v);
                    showShareDialog();
                });
            }
        } else {
            setTopLeftIcon(R.drawable.back_icon_black);
            setTopTitle(getIntent().getStringExtra(KEY_TITLE));

            if (isShare) {
                setTopRightIcon(R.drawable.share_ico_2);
                setTopRightOnClick(v -> {
                    // showMenu(v);
                    showShareDialog();
                });
            }

            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) webContainer.getLayoutParams();
            // 非全屏模式：设置 layout_below
            layoutParams.addRule(RelativeLayout.BELOW, R.id.totwoo_topbar_layout);
// 应用更新后的布局参数
            webContainer.setLayoutParams(layoutParams);
        }
        //加粗
        getTopTitleView().getPaint().setFakeBoldText(true);
        setTopLeftOnclik(v -> webViewBack());
        setTopCancelIcon(R.drawable.close_icon);
        super.initTopBar();
    }


    private void showShareDialog() {
//        final CustomDialog customShareDialog = new CustomDialog(WebViewActivity.this);
//        customShareDialog.setTitle(getResources().getString(R.string.share));
//        // FileUtils.saveBitmapFromSDCard(
//        // snapShareBitmap(), "totwoo_cache_img.jpg");
//        View view = getLayoutInflater().inflate(R.layout.common_share_layout_new, null);
//        view.findViewById(R.id.common_share_friend_iv).setOnClickListener(v -> {
//            ShareUtilsSingleton.getInstance().shareUrlToWechatMoment(shareTitle, shareContent, getImagePath(), mUrl);
//            customShareDialog.dismiss();
//        });
//        view.findViewById(R.id.common_share_wechat_iv).setOnClickListener(v -> {
//            ShareUtilsSingleton.getInstance().shareUrlToWechat(shareTitle, shareContent, getImagePath(), mUrl);
//            customShareDialog.dismiss();
//        });
//        view.findViewById(R.id.common_share_weibo_iv).setOnClickListener(v -> {
//            ShareUtilsSingleton.getInstance().shareUrlToWeibo(WebViewActivity.this, shareTitle, getImagePath(), mUrl);
//            customShareDialog.dismiss();
//        });
//        view.findViewById(R.id.common_share_qq_iv).setOnClickListener(v -> {
//            ShareUtilsSingleton.getInstance().shareUrlToQQ(shareTitle, shareContent, getImagePath(), mUrl);
//            customShareDialog.dismiss();
//        });
//        view.findViewById(R.id.common_share_qzone_iv).setOnClickListener(v -> {
//            ShareUtilsSingleton.getInstance().shareUrlToQzone(shareTitle, shareContent, getImagePath(), mUrl);
//            customShareDialog.dismiss();
//        });
//        view.findViewById(R.id.common_share_facebook_iv).setOnClickListener(v -> {
//            ShareUtilsSingleton.getInstance().shareUrlToFacebook(shareTitle, mUrl, WebViewActivity.this, null);
//            customShareDialog.dismiss();
//        });
//        view.findViewById(R.id.common_share_twitter_iv).setOnClickListener(v -> {
//            ShareUtilsSingleton.getInstance().shareUrlToTwitter(shareTitle, getImagePath(), mUrl);
//            customShareDialog.dismiss();
//        });
//        if (!Apputils.systemLanguageIsChinese(WebViewActivity.this)) {
//            view.findViewById(R.id.common_share_facebook_iv).setVisibility(View.VISIBLE);
//            view.findViewById(R.id.common_share_twitter_iv).setVisibility(View.VISIBLE);
//            view.findViewById(R.id.common_share_qq_iv).setVisibility(View.GONE);
//            view.findViewById(R.id.common_share_qzone_iv).setVisibility(View.GONE);
//            view.findViewById(R.id.common_share_weibo_iv).setVisibility(View.GONE);
//        }
//        customShareDialog.setMainLayoutView(view);
//        customShareDialog.setPositiveButton(R.string.cancel, v -> customShareDialog.dismiss());
//        customShareDialog.show();
        //调用系统intent分享
        Intent intent = new Intent(Intent.ACTION_SEND);
        intent.setType("text/plain");
        intent.putExtra(Intent.EXTRA_SUBJECT, shareTitle);
        intent.putExtra(Intent.EXTRA_TEXT, shareContent);
        startActivity(Intent.createChooser(intent, "分享到"));

    }

    private String getImagePath() {
        return FileUtils.saveBitmapFromSDCard(
                BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.icon_share_temp),
                "totwoo_cache_img_share_logo");
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getIntentData();

        // 部分手机在应用第一次创建WebView时会抛出java.lang.IllegalStateException: AwContents must be
        // created if we are not posting!异常, 导致应用崩溃。
        // Caused by: java.lang.IllegalStateException: AwContents must be created if we
        // are not posting!
        // 1）此问题出现在底层 ；
        // 2）非应用层所能控制；
        // 3）只出现在部分手机上；
        // 4）是调用先后顺序的问题导致的；
        // 解决方案:
        // 在实际创建WebView之前，额外再创建一个不需要使用的WebView对象，让其先崩溃一次，以保护住实际需要使用时不崩溃。
        try {

            try {
                webView = new WebView(this);
            } catch (Exception e) {
                webView = new WebView(this);
            }

            enableSlowWholeDocumentDraw();

            setContentView(mIsMovie ? R.layout.activity_web_view_movie : R.layout.activity_web_view);
            ButterKnife.bind(this);
        } catch (Exception e) {
            e.printStackTrace();
            // WebView 加载异常, 直接跳转外部浏览器
            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(mUrl)));
            finish();
            return;
        }

        webContainer.addView(webView);

        initWebView();
        webView.loadUrl(mUrl);
    }

    private void getIntentData() {
        if (getIntent() != null) {
            mIsMovie = getIntent().getBooleanExtra("mIsMovie", false);
            mUrl = getIntent().getStringExtra("mUrl");
            LogUtils.e("aab url = " + mUrl);
            isShare = getIntent().getBooleanExtra("isShare", false);
            shareTitle = getIntent().getStringExtra("shareTitle");
            shareContent = getIntent().getStringExtra("shareContent");
            is_full_show = getIntent().getStringExtra("is_full_show");
        }
    }

    @SuppressLint({"SetJavaScriptEnabled", "AddJavascriptInterface"})
    private void initWebView() {
        mProgressBar.setVisibility(View.VISIBLE);
        WebSettings ws = webView.getSettings();
        // 设置此属性，可任意比例缩放。
        ws.setUseWideViewPort(true);
        // 告诉WebView启用JavaScript执行。默认的是false。
        ws.setJavaScriptEnabled(true);
        // 使用localStorage则必须打开
        ws.setDomStorageEnabled(true);
        // webview从5.0开始默认不允许混合模式,https中不能加载http资源,需要设置开启。
        ws.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        mWebChromeClient = new MyWebChromeClient(this);
        webView.setWebChromeClient(mWebChromeClient);

        webView.setWebViewClient(new MyWebViewClient(this));
        mWebChromeClient.setOnReceivedTitle(() -> {
            if (webView.canGoBack()) {
                setTopCancelIconVisibility(View.VISIBLE);
            } else {
                setTopCancelIconVisibility(View.GONE);
            }
        });
        webView.setDownloadListener((url, userAgent, contentDisposition, mimetype, contentLength) -> {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(intent);
        });
    }

    @Override
    public void hindProgressBar() {
        mProgressBar.setVisibility(View.GONE);
    }

    @Override
    public void showWebView() {
        webView.setVisibility(View.VISIBLE);
    }

    @Override
    public void hindWebView() {
        webView.setVisibility(View.INVISIBLE);
    }

    @Override
    public void fullViewAddView(View view) {
        FrameLayout decor = (FrameLayout) getWindow().getDecorView();
        videoFullView = new FullscreenHolder(WebViewActivity.this);
        videoFullView.addView(view);
        decor.addView(videoFullView);
    }

    @Override
    public void showVideoFullView() {
        videoFullView.setVisibility(View.VISIBLE);
    }

    @Override
    public void hindVideoFullView() {
        videoFullView.setVisibility(View.GONE);
    }

    @Override
    public void startProgress(int newProgress) {
        mProgressBar.setVisibility(View.VISIBLE);
        mProgressBar.setProgress(newProgress);
        if (newProgress == 100) {
            mProgressBar.setVisibility(View.GONE);
        }
    }

    /**
     * android与js交互：
     * 前端嵌入js代码：不能加重复的节点，不然会覆盖
     */
    @Override
    public void addImageClickListener() {
        // 这段js函数的功能就是，遍历所有的img节点，并添加onclick函数，函数的功能是在图片点击的时候调用本地java接口并传递url过去
        // 如要点击一张图片在弹出的页面查看所有的图片集合,则获取的值应该是个图片数组
        webView.loadUrl("javascript:(function(){" +
                "var objs = document.getElementsByTagName(\"img\");" +
                "for(var i=0;i<objs.length;i++)" +
                "{" +
                // "objs[i].onclick=function(){alert(this.getAttribute(\"has_link\"));}" +
                "objs[i].onclick=function(){window.injectedObject.imageClick(this.getAttribute(\"src\"),this.getAttribute(\"has_link\"));}"
                +
                "}" +
                "})()");

        // 遍历所有的<li>节点,将节点里的属性传递过去(属性自定义,用于页面跳转)
        webView.loadUrl("javascript:(function(){" +
                "var objs =document.getElementsByTagName(\"li\");" +
                "for(var i=0;i<objs.length;i++)" +
                "{" +
                "objs[i].onclick=function(){" +
                "window.injectedObject.textClick(this.getAttribute(\"type\"),this.getAttribute(\"item_pk\"));}" +
                "}" +
                "})()");

        /** 传应用内的数据给html，方便html处理 */
        // 无参数调用
        webView.loadUrl("javascript:javacalljs()");
        // 传递参数调用
        webView.loadUrl("javascript:javacalljswithargs('" + "android传入到网页里的数据，有参" + "')");

    }

    public FrameLayout getVideoFullView() {
        return videoFullView;
    }

    /**
     * 全屏时按返加键执行退出全屏方法
     */
    public void hideCustomView() {
        mWebChromeClient.onHideCustomView();
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
    }

    /**
     * 上传图片之后的回调
     */
    @SuppressLint("MissingSuperCall")
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent intent) {
        if (requestCode == MyWebChromeClient.FILECHOOSER_RESULTCODE) {
            mWebChromeClient.mUploadMessage(intent, resultCode);
        } else if (requestCode == MyWebChromeClient.FILECHOOSER_RESULTCODE_FOR_ANDROID_5) {
            mWebChromeClient.mUploadMessageForAndroid5(intent, resultCode);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 全屏播放退出全屏
            // if (mWebChromeClient.inCustomView()) {
            // hideCustomView();
            // return true;
            //
            // //返回网页上一页
            // }
            // else

            webViewBack();
        }
        return false;
    }

    @Override
    protected void onPause() {
        super.onPause();
        webView.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        webView.onResume();
        // 支付宝网页版在打开文章详情之后,无法点击按钮下一步
        webView.resumeTimers();
        if (mUrl != null && (mUrl.contains("kefu_v1.html") || mUrl.contains("report.html"))) {
            webView.loadUrl(mUrl);
        }
        // 设置为横屏
        if (getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (videoFullView != null) {
            videoFullView.removeAllViews();
        }
        if (webView != null) {
            ViewGroup parent = (ViewGroup) webView.getParent();
            if (parent != null) {
                parent.removeView(webView);
            }
            webView.removeAllViews();
            webView.stopLoading();
            webView.setWebChromeClient(null);
            webView.setWebViewClient(null);
            webView.destroy();
            webView = null;
        }
    }

    /**
     * 打开网页:
     *
     * @param mContext 上下文
     * @param mUrl     要加载的网页url
     * @param mIsMovie 是否是视频链接(视频链接布局不一致)
     */
    public static void loadUrl(Context mContext, String mUrl, boolean mIsMovie) {
        loadUrl(mContext, mUrl, mIsMovie, false, null, null);
    }

    /**
     * 打开网页:
     *
     * @param mContext 上下文
     * @param mUrl     要加载的网页url
     */
    public static void loadUrl(Context mContext, String mUrl) {
        loadUrl(mContext, mUrl, false, false, null, null);
    }

    /**
     * 打开网页:
     *
     * @param mContext 上下文
     * @param mUrl     要加载的网页url
     * @param mIsMovie 是否是视频链接(视频链接布局不一致)
     */
    public static void loadUrl(Context mContext, String mUrl, boolean mIsMovie, boolean isShare, String title,
                               String content) {
        LogUtils.e("aab url = " + mUrl);
        Intent intent = new Intent(mContext, WebViewActivity.class);
        intent.putExtra("mUrl", mUrl);
        intent.putExtra("mIsMovie", mIsMovie);
        intent.putExtra("isShare", isShare);
        intent.putExtra("shareTitle", title);
        intent.putExtra(KEY_TITLE, title);
        intent.putExtra("shareContent", content);
        mContext.startActivity(intent);
    }


    /**
     * 打开网页:
     *
     * @param mContext 上下文
     * @param mUrl     要加载的网页url
     * @param mIsMovie 是否是视频链接(视频链接布局不一致)
     */
    public static void loadUrl(Context mContext, String mUrl, boolean mIsMovie, boolean isShare, String title,
                               String content, String is_full_show) {
        LogUtils.e("aab url = " + mUrl);
        Intent intent = new Intent(mContext, WebViewActivity.class);
        intent.putExtra("mUrl", mUrl);
        intent.putExtra("mIsMovie", mIsMovie);
        intent.putExtra("isShare", isShare);
        intent.putExtra("shareTitle", title);
        intent.putExtra(KEY_TITLE, title);
        intent.putExtra("shareContent", content);
        intent.putExtra("is_full_show", is_full_show);
        mContext.startActivity(intent);
    }

    /**
     * 展示菜单
     */
    protected void showMenu(View v) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            menuLayout.setPadding(0, Apputils.getStatusHeight(this), 0, 0);
        }

        menuLayout.setVisibility(View.VISIBLE);
        menuLayout.setOnClickListener(v1 -> v1.setVisibility(View.GONE));

        menuListView.setAdapter(new ArrayAdapter<String>(this,
                R.layout.web_menu_item, getResources().getStringArray(
                R.array.web_menu_items)));

        menuListView.setOnItemClickListener((parent, view, position, id) -> {
            switch (position) {
                case 0:
                    ClipboardManager cmb = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                    cmb.setText(webView.getUrl());
                    ToastUtils.showLong(WebViewActivity.this,
                            R.string.link_has_copyed);
                    break;
                case 1:
                    showShareDialog(mUrl);
                    break;
                case 2:
                    if (webView != null) {
                        webView.reload();
                    }
                    break;
            }
            menuLayout.setVisibility(View.GONE);
        });

        // PopupMenu popup = new PopupMenu(this, v);
        // popup.getMenuInflater().inflate(R.menu.menu_web_activity,
        // popup.getMenu());
        //
        // popup.setOnMenuItemClickListener(new
        // PopupMenu.OnMenuItemClickListener() {
        // public boolean onMenuItemClick(MenuItem item) {
        // switch (item.getItemId()) {
        // case R.id.menu_action_copy_link:
        // ClipboardManager cmb = (ClipboardManager)
        // getSystemService(Context.CLIPBOARD_SERVICE);
        // cmb.setText(mWebView.getUrl());
        // ToastUtils.showLong(WebActivity.this,
        // R.string.link_has_copyed);
        // break;
        // case R.id.menu_action_share:
        // showShareDialog(StringUtils.isEmpty(mWebTitle) ? "" : mUrl);
        // break;
        // case R.id.menu_action_refresh:
        // if (mWebView != null) {
        // mWebView.reload();
        // }
        // break;
        // }
        // return true;
        // }
        // });
        // popup.show();
    }

    /**
     * 展示分享的对话框
     *
     * @param url
     */
    private void showShareDialog(String url) {

        shareUtils = new ShareUtils(WebViewActivity.this);
        shareUtils.setShareUrl(url);
        shareUtils.setShareImgPath(FileUtils.saveBitmapFromSDCard(
                snapShareBitmap(), "totwoo_cache_img"));
        shareUtils.setShareTitle(mWebTitle);
        shareDialog = new CustomDialog(this);
        shareDialog.setTitle(R.string.share);
        View view = LayoutInflater.from(this).inflate(
                R.layout.share_black_layout, null);

        View facebook = view.findViewById(R.id.share_facebook);
        View twitter = view.findViewById(R.id.share_twitter);
        View wechatComment = view.findViewById(R.id.share_wechar_comment);
        View wechat = view.findViewById(R.id.share_wechar);
        View weibo = view.findViewById(R.id.share_weibo);
        View qq = view.findViewById(R.id.share_qq);
        View qzone = view.findViewById(R.id.share_qzone);

        if (!Apputils.systemLanguageIsChinese(this)) {
            facebook.setVisibility(View.VISIBLE);
            twitter.setVisibility(View.VISIBLE);
            wechatComment.setVisibility(View.VISIBLE);

            facebook.setOnClickListener(this);
            twitter.setOnClickListener(this);
            wechatComment.setOnClickListener(this);
        } else {
            wechatComment.setVisibility(View.VISIBLE);
            wechat.setVisibility(View.VISIBLE);
            weibo.setVisibility(View.VISIBLE);
            qq.setVisibility(View.VISIBLE);
            qzone.setVisibility(View.VISIBLE);

            wechatComment.setOnClickListener(this);
            wechat.setOnClickListener(this);
            weibo.setOnClickListener(this);
            qq.setOnClickListener(this);
            qzone.setOnClickListener(this);
        }

        shareDialog.setMainLayoutView(view);
        shareDialog.setNegativeButton(R.string.cancel);
        shareDialog.show();
    }

    /**
     * 获取当前屏幕截图，包含状态栏
     *
     * @return
     */
    public Bitmap snapShareBitmap() {
        Picture snapShot = webView.capturePicture();
        Bitmap bmp = Bitmap.createBitmap(snapShot.getWidth(), snapShot.getHeight(),
                Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas(bmp);
        snapShot.draw(canvas);
        return bmp;

        // return bmp;
    }

    @Override
    public void onClick(View v) {
        if (shareUtils == null) {
            return;
        }
        switch (v.getId()) {
            case R.id.share_wechar_comment:
                shareUtils.shareToWeCharComment(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_wechar:
                shareUtils.shareToWeChar(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_weibo:
                shareUtils.shareToSinaWeibo(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_qq:
                shareUtils.shareToQQ(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_qzone:
                shareUtils.shareToQzone(ShareUtils.SHARE_WEBPAGE);
                break;
            case R.id.share_facebook:
                shareUtils.shareToFackBook(ShareUtils.SHARE_WEBPAGE, this);
                break;
            case R.id.share_twitter:
                shareUtils.shareToTwitter(ShareUtils.SHARE_WEBPAGE);
                break;
        }

        if (shareDialog != null && shareDialog.isShowing()) {
            shareDialog.dismiss();
        }
    }

    private void webViewBack() {
        if (videoFullView.getVisibility() == View.VISIBLE) {
            hideCustomView();
            return;
        }
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            finish();
        }
    }
}
