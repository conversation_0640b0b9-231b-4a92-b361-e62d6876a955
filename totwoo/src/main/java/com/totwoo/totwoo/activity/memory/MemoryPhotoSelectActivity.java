package com.totwoo.totwoo.activity.memory;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.provider.Settings;
import android.view.View;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.component.http.HttpParams;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.ImageBean;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.bean.MemoryPhotoSelectBean;
import com.totwoo.totwoo.bean.holderBean.ImageFolderBean;
import com.totwoo.totwoo.newConrtoller.LoadSystemImagesController;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/3.
 */

public class MemoryPhotoSelectActivity extends BaseActivity implements SubscriberListener, View.OnClickListener, AdapterView.OnItemClickListener
{
    public static final String IMAGE_PATH = FileUtils.getImageDir() + File.separator;

    @BindView(R.id.memory_photo_select_gv)
    public GridView gv;

    @BindView(R.id.memory_photo_select_ok)
    public TextView ok;

    private MemoryPhotoSelectAdapter adapter;
    private String content;
    private ArrayList<ImageBean> photoLists;
    private ArrayList<ImageFolderBean> folderLists;
    private LinkedHashSet<String> selectedPhotoPath;
    private ImageFolderBean lastFolder;

    private HashSet<String> tmpSelectedPhotoPath;
    private boolean isAdd = false;

    private String photoPath;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_photo_select);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        initData();

        LoadSystemImagesController.getInstance().getAllImages(this);

        initListener();
    }

    private void initData()
    {
        Intent intent = this.getIntent();
        content = intent.getStringExtra(S.M.M_CONTENT);
        photoLists = (ArrayList<ImageBean>) intent.getSerializableExtra(S.M.M_IMAGES);
//        folderLists = (ArrayList<ImageFolderBean>) intent.getSerializableExtra(S.M.M_FOLDERS);
        selectedPhotoPath = (LinkedHashSet<String>) intent.getSerializableExtra(S.M.M_SELECTED);
        lastFolder = (ImageFolderBean) intent.getSerializableExtra(S.M.M_FOLDER);
        isAdd = intent.getBooleanExtra(S.M.M_ADD, false);

        if (selectedPhotoPath == null)
            selectedPhotoPath = new LinkedHashSet<>();

        tmpSelectedPhotoPath = (HashSet<String>) selectedPhotoPath.clone();
    }

    private void initListener()
    {
        this.ok.setOnClickListener(this);
    }

    @EventInject(eventType = S.E.E_LOAD_SYSTEM_IMAGE_FINISH, runThread = TaskType.UI)
    public void onLoadSystemImagesFinished(EventData data)
    {
        HttpParams hp = (HttpParams) data;
        if (photoLists == null || photoLists.size() == 0)
            photoLists = (ArrayList<ImageBean>) hp.getUserDefine("allImageList");


        folderLists = (ArrayList<ImageFolderBean>) hp.getUserDefine("folderList");

        initGridView(photoLists);
    }

    private void initGridView(ArrayList<ImageBean> photoLists)
    {
        ArrayList<MemoryPhotoSelectBean> list = new ArrayList<>();
        list.add(new MemoryPhotoSelectBean("", "", false));
        for (int i=0; i<photoLists.size(); i++)
        {
            ImageBean ib = photoLists.get(i);
            list.add(new MemoryPhotoSelectBean(ib.imagePath, ib.bigImagePath, isSelected(selectedPhotoPath, ib.bigImagePath)));
        }

        adapter = new MemoryPhotoSelectAdapter(this, list, selectedPhotoPath);
        gv.setAdapter(adapter);
        gv.setOnItemClickListener(this);
    }

    private boolean isSelected(HashSet<String> res, String str)
    {
        if (res == null || res.size() == 0)
            return false;

        Iterator<String> iterator = res.iterator();
        while (iterator.hasNext())
            if (iterator.next().equals(str))
                return true;

        return false;
    }

    @Override
    protected void initTopBar()
    {
        setTopbarBackground(R.color.layer_bg_white);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.memory_photo_select_title);
        setTopRightString(R.string.memory_photo_Album);
        setTopRightOnClick(this);
        setTopLeftOnclik(this);
    }

    @Override
    public void onClick(View v)
    {
        switch (v.getId())
        {
            case R.id.memory_photo_select_ok:
                onOkClicked(false);
                break;
            case R.id.top_bar_right_tv:
                onPhotoListClicked();
                break;
            case R.id.top_bar_back_btn:
                onBackClicked();
                break;
        }
    }

    private void onBackClicked()
    {
        if (isAdd)
        {
            Intent i = new Intent(this, MemoryPhotoAddActivity.class);
            i.putExtra(S.M.M_CONTENT, content);
            i.putExtra(S.M.M_SELECTED, tmpSelectedPhotoPath);
            startActivity(i);
        }

        this.finish();
    }

    private void onOkClicked(boolean isTakePhoto)
    {
        selectedPhotoPath = adapter.photoPath;
        if (selectedPhotoPath == null || selectedPhotoPath.size() == 0)
        {
            ToastUtils.showShort(this, R.string.memory_photo_notselect);
            return;
        }

        Intent i = new Intent(this, MemoryPhotoAddActivity.class);
        i.putExtra(S.M.M_CONTENT, content);
        i.putExtra(S.M.M_SELECTED, isTakePhoto ? tmpSelectedPhotoPath : selectedPhotoPath);
        startActivity(i);
        this.finish();
    }

    @Override
    public void onBackPressed()
    {
        onBackClicked();
        super.onBackPressed();
    }

    private void onPhotoListClicked()
    {
        selectedPhotoPath = adapter.photoPath;
        Intent i = new Intent (this, MemoryPhotoFolderActivity.class);
        i.putExtra(S.M.M_CONTENT, content);
        i.putExtra(S.M.M_FOLDERS, folderLists);
        i.putExtra(S.M.M_SELECTED, selectedPhotoPath);
        i.putExtra(S.M.M_FOLDER, lastFolder);
        startActivity(i);
        this.finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {

    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id)
    {
        if (position == 0)
        {
            if(!CommonUtils.isCameraCanUse()){
                final CustomDialog dialog = new CustomDialog(MemoryPhotoSelectActivity.this);
                dialog.setMessage(R.string.open_camera_error1);
                dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener()
                {
                    @Override
                    public void onClick(View v)
                    {
                        MemoryPhotoSelectActivity.this.startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + MemoryPhotoSelectActivity.this.getPackageName())));
                        dialog.dismiss();
                    }
                });
                dialog.show();
                return;

            }
            callSystemPhoto();
        }
        else
        {
            Intent intent = new Intent(this, MemoryPhotoShowActivity.class);
            MemoryBean mb = new MemoryBean();
            mb.img_url = new String[]{adapter.resource.get(position).path,};

            intent.putExtra(S.M.M_SINGLE, true);
            intent.putExtra(S.M.M_IMAGES, mb);
            startActivity(intent);
        }
    }

    private void callSystemPhoto()
    {
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        photoPath = IMAGE_PATH + System.currentTimeMillis();
//        Uri imageUri = Uri.fromFile(new File(photoPath));
        // 指定照片保存路径（SD卡），USER_HEAD_PORTRAIT为一个临时文件，每次拍照后这个图片都会被替换
        intent.putExtra(MediaStore.EXTRA_OUTPUT, CommonUtils.getUriForFile(this,new File(photoPath)));
        startActivityForResult(intent, 1);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);

        switch (requestCode)
        {
            case 1:// 相机拍摄
                if (resultCode == -1)
                {
                    tmpSelectedPhotoPath.add(photoPath);
                    adapter.photoPath.add(photoPath);
                    adapter.notifyDataSetChanged();
                    onOkClicked(true);
                }
                break;
        }
    }
}
