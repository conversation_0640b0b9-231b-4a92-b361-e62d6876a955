package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.event.EventBus;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.adapter.ColorLibraryAdapter;
import com.totwoo.totwoo.adapter.WeekSelectAdapter;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.WheelView;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;
import com.totwoo.totwoo.widget.pickerview.listener.OnDateSetListener;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/3/6.
 */

public class CustomNotifyEditActivity extends BaseActivity {
    @BindView(R.id.edit_custom_long_vibration_tv)
    TextView mLongVibrationTv;
    @BindView(R.id.edit_custom_short_vibration_tv)
    TextView mShortVibrationTv;
    @BindView(R.id.edit_custom_content)
    LinearLayout mPeriodSettingContent;
    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    @BindView(R.id.edit_custom_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.edit_custom_time_layout)
    FrameLayout mTimeFL;
    @BindView(R.id.edit_custom_time_layout_view)
    View mTimeView;
    @BindView(R.id.edit_custom_birthday_layout)
    FrameLayout mBirthdayFL;
    @BindView(R.id.edit_custom_birthday_layout_view)
    View mBirthdayView;
    @BindView(R.id.edit_custom_repeat_layout)
    FrameLayout mRepeatFL;
    @BindView(R.id.edit_custom_repeat_layout_view)
    View mRepeatView;
    @BindView(R.id.edit_custom_notify_value_tv)
    TextView mNotifyTimeTv;
    @BindView(R.id.edit_custom_repeat_value_tv)
    TextView mRepeatTv;
    @BindView(R.id.edit_custom_time_value_tv)
    TextView mTimeValueTv;
    @BindView(R.id.edit_custom_birthday_value_tv)
    TextView mBirthdayValueTv;
    @BindView(R.id.edit_custom_birthday_key_tv)
    TextView mBirthdayKeyTv;

    CustomItemBean bean;
    JewelryNotifyModel nowSetModel;
    ColorLibraryAdapter colorLibraryAdapter;

    SimpleDateFormat formatToDay = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat formatToMin = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    private String tempTime;
    private String tempBirthday;

    private String currentRepeatType;
    private int currentNotifyTimeIndex;
    private int currentType;
    private CustomBottomDialog dialog;
    private long currentTime;
    private List<String> defaultNotifyTimeStrs;
    private List<String> defaultBirthNotifyTimeStrs;
    private List<String> defaultAnnNotifyTimeStrs;
    private final static double DEFAULT_NOTIFY_TIME_DOUBLES[] = {0, 0.25, 0.5, 1, 6, 24, 72};
    private final static double DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[] = {24, 48, 72, 168};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_custom_notify);
        ButterKnife.bind(this);
        bean = (CustomItemBean) getIntent().getSerializableExtra("bean");

        defaultNotifyTimeStrs = new ArrayList<>();
        int size = DEFAULT_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            defaultNotifyTimeStrs.add(getHourStringByIndex(i));
        }
        defaultBirthNotifyTimeStrs = new ArrayList<>();
        int birthSize = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < birthSize; i++) {
            defaultBirthNotifyTimeStrs.add(getBirthHourStringByIndex(i));
        }
        defaultAnnNotifyTimeStrs = new ArrayList<>();
        for (int i = 0; i < birthSize; i++) {
            defaultAnnNotifyTimeStrs.add(getAnnHourStringByIndex(i));
        }
        if (bean != null) {
            initView();
        } else {
            int define_id = getIntent().getIntExtra("define_id", 0);
            if (define_id != 0) {
                HttpHelper.customService.getCustom(define_id)
                        .subscribeOn(Schedulers.newThread())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Observer<HttpBaseBean<CustomItemBean>>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {

                            }

                            @Override
                            public void onNext(HttpBaseBean<CustomItemBean> customItemBeanHttpBaseBean) {
                                bean = customItemBeanHttpBaseBean.getData();
                                initView();
                            }
                        });
            }
        }
    }

    private void initView() {
        nowSetModel = new JewelryNotifyModel();
        nowSetModel.setFlashColor(bean.getNotify_mode());
        if (bean.getIs_open() == 1)
            nowSetModel.setNotifySwitch(true);
        else
            nowSetModel.setNotifySwitch(false);

        if (TextUtils.equals(bean.getShock_type(), "short"))
            nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
        else
            nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);

        currentType = bean.getDefine_type();
        if (currentType == CustomItemBean.SCHEDULE || currentType == CustomItemBean.OTHER) {
            currentNotifyTimeIndex = getHourIndexByDouble(Double.valueOf(bean.getRemind_time()));
            mNotifyTimeTv.setText(getHourStringByIndex(currentNotifyTimeIndex));
        } else {
            currentNotifyTimeIndex = getBirthHourIndexByDouble(Double.valueOf(bean.getRemind_time()));
            if(currentType == CustomItemBean.BIRTHDAY){
                mNotifyTimeTv.setText(getBirthHourStringByIndex(currentNotifyTimeIndex));
            }else{
                mNotifyTimeTv.setText(getAnnHourStringByIndex(currentNotifyTimeIndex));
            }
        }
        currentRepeatType = bean.getRepeat_notify();
        mCallSwitchCb.setChecked(nowSetModel.isNotifySwitch());
        mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
        if (!nowSetModel.isNotifySwitch())
            mPeriodSettingContent.setVisibility(View.GONE);

        if (currentType == CustomItemBean.SCHEDULE || currentType == CustomItemBean.OTHER) {
            tempTime = bean.getDefine_time();
            mTimeValueTv.setText(tempTime);
            try {
                currentTime = formatToMin.parse(bean.getDefine_time()).getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            mBirthdayFL.setVisibility(View.GONE);
            mBirthdayView.setVisibility(View.GONE);
            mTimeFL.setVisibility(View.VISIBLE);
            mTimeView.setVisibility(View.VISIBLE);
            mRepeatFL.setVisibility(View.VISIBLE);
            mRepeatView.setVisibility(View.VISIBLE);
        } else {
            tempBirthday = bean.getDefine_time();
            mBirthdayValueTv.setText(tempBirthday);
            if (currentType == CustomItemBean.BIRTHDAY)
                mBirthdayKeyTv.setText(getString(R.string.custom_notify_type_birthday));
            else
                mBirthdayKeyTv.setText(getString(R.string.custom_notify_type_anniversary));
            try {
                currentTime = formatToDay.parse(bean.getDefine_time()).getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            mBirthdayFL.setVisibility(View.VISIBLE);
            mBirthdayView.setVisibility(View.VISIBLE);
            mTimeFL.setVisibility(View.GONE);
            mTimeView.setVisibility(View.GONE);
            mRepeatFL.setVisibility(View.GONE);
            mRepeatView.setVisibility(View.GONE);
        }
        mRepeatTv.setText(setStringToView(currentRepeatType));
        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                setTextColorBtn(false);
                break;
        }
        colorLibraryRecyclerView.setLayoutManager(new LinearLayoutManager(CustomNotifyEditActivity.this, LinearLayoutManager.HORIZONTAL, false));
        colorLibraryAdapter = new ColorLibraryAdapter(nowSetModel.getFlashColor(), CustomNotifyEditActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                nowSetModel.setFlashColor((String) v.getTag());
                colorLibraryAdapter.setSelectColor((String) v.getTag());
                saveNowModel(false);
            }
        });
        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
        colorLibraryRecyclerView.scrollToPosition(colorLibraryAdapter.getIndex(nowSetModel.getFlashColor()));

        setTopTitle(bean.getTitle());
    }

    @Override
    public void onBackPressed() {
        saveInfo();
//        super.onBackPressed();
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> saveInfo());

        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopRightIcon(R.drawable.delete);
        setTopRightOnClick(v -> {
            final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(CustomNotifyEditActivity.this);
            commonMiddleDialog.setMessage(R.string.custom_notify_list_delete_hint);
            commonMiddleDialog.setSure(v1 -> {
                delete();
                commonMiddleDialog.dismiss();
            });
            commonMiddleDialog.setCancel(R.string.give_up);
            commonMiddleDialog.show();
        });
        setSpinState(false);
    }

    @OnClick({R.id.edit_custom_long_vibration_tv, R.id.edit_custom_short_vibration_tv,
            R.id.edit_custom_time_layout, R.id.edit_custom_birthday_layout, R.id.edit_custom_notify_layout,
            R.id.edit_custom_repeat_layout, R.id.edit_custom_save_tv, R.id.notify_switch_click_item})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.edit_custom_long_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
                mShortVibrationTv.setBackground(null);
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(true);
                break;
            case R.id.edit_custom_short_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                mLongVibrationTv.setBackground(null);
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(false);
                break;
            case R.id.notify_switch_click_item:
                mCallSwitchCb.setChecked(!mCallSwitchCb.isChecked());
                nowSetModel.setNotifySwitch(mCallSwitchCb.isChecked());
                saveNowModel(true);
                break;
            case R.id.edit_custom_notify_layout:
                if (currentType == CustomItemBean.SCHEDULE || currentType == CustomItemBean.OTHER)
                    showNotifyDialog();
                else if(currentType == CustomItemBean.BIRTHDAY)
                    showBirthNotifyDialog();
                else
                    showAnnNotifyDialog();
                break;
            case R.id.edit_custom_repeat_layout:
                showRepeatDialog();
                break;
            case R.id.edit_custom_time_layout:
                showTimeDialog();
                break;
            case R.id.edit_custom_birthday_layout:
                if (currentType == CustomItemBean.BIRTHDAY) {
                    showBirthDialog();
                } else if (currentType == CustomItemBean.ANNIVERSARY) {
                    showAnnDialog();
                }
                break;
            case R.id.edit_custom_save_tv:
                saveInfo();
                break;
        }
    }

    private void saveInfo() {
        if(nowSetModel == null){
            finish();
        }
        final String defineTime;
        if (currentType == CustomItemBean.SCHEDULE || currentType == CustomItemBean.OTHER)
            defineTime = tempTime;
        else
            defineTime = tempBirthday;

        final String notifyTime;
        if (currentType == CustomItemBean.SCHEDULE || currentType == CustomItemBean.OTHER)
            notifyTime = DEFAULT_NOTIFY_TIME_DOUBLES[currentNotifyTimeIndex] + "";
        else
            notifyTime = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[currentNotifyTimeIndex] + "";

        final int open;
        if (nowSetModel.isNotifySwitch())
            open = 1;
        else
            open = 0;

        //talk_id和target_uid是爱的提醒需要的参数。这里传空就行了
        HttpHelper.customService.updateCustom(open, currentType, bean.getDefine_id(), defineTime,
                notifyTime, currentRepeatType, nowSetModel.getVibrationHttpString(), nowSetModel.getFlashColor(),
                "","")
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<CustomItemBean>>() {
                    @Override
                    public void onCompleted() {
                        finish();
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(CustomNotifyEditActivity.this, R.string.error_net);
                        finish();
                    }

                    @Override
                    public void onNext(HttpBaseBean<CustomItemBean> customItemBeanHttpBaseBean) {
                        if (customItemBeanHttpBaseBean.getErrorCode() == 0) {
                            bean.setIs_open(open);
                            bean.setShock_type(nowSetModel.getVibrationHttpString());
                            bean.setDefine_time(defineTime);
                            bean.setNotify_mode(nowSetModel.getFlashColor());
                            bean.setRepeat_notify(currentRepeatType);
                            bean.setRemind_time(notifyTime);
                            EventBus.onPostReceived(S.E.E_CUSTOM_UPDATE_SUCCESSED, bean);
                        }
                    }
                });

    }

    private void showBirthDialog() {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        long hunrundYears = 100L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        tempBirthday = format.format(millseconds);
                        currentTime = millseconds;
                        StringBuffer buffer = new StringBuffer(tempBirthday);
                        String year = buffer.substring(0, 4);
                        String month = buffer.substring(5, 7);
                        String day = buffer.substring(8, 10);
                        mBirthdayValueTv.setText(year + "-" + month + "-" + day);

                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.custom_notify_remind_birthday))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - hunrundYears)
                .setMaxMillseconds(System.currentTimeMillis())
                .setCurrentMillseconds(currentTime)
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "year_month_day");
    }

    private void showAnnDialog() {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        long tenYears = 10L * 365 * 1000 * 60 * 60 * 24L;
        long hunrundYears = 100L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        tempBirthday = format.format(millseconds);
                        currentTime = millseconds;
                        StringBuffer buffer = new StringBuffer(tempBirthday);
                        String year = buffer.substring(0, 4);
                        String month = buffer.substring(5, 7);
                        String day = buffer.substring(8, 10);
                        mBirthdayValueTv.setText(year + "-" + month + "-" + day);

                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.custom_notify_remind_anniversary))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - hunrundYears)
                .setMaxMillseconds(System.currentTimeMillis() + tenYears)
                .setCurrentMillseconds(currentTime)
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "year_month_day");
    }

    private void showTimeDialog() {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        long tenYears = 10L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        long pickerCurrentMillseconds = timePickerView.getCurrentMillSeconds();
                        LogUtils.e("pickerCurrentMillseconds = " + pickerCurrentMillseconds);
                        LogUtils.e("millseconds = " + millseconds);
                        LogUtils.e("format.format(pickerCurrentMillseconds) = " + format.format(pickerCurrentMillseconds));
                        tempTime = format.format(millseconds);
                        currentTime = millseconds;
                        StringBuffer buffer = new StringBuffer(tempTime);
                        String year = buffer.substring(0, 4);
                        String month = buffer.substring(5, 7);
                        String day = buffer.substring(8, 10);
                        String hourAndMin = buffer.substring(11);
                        mTimeValueTv.setText(year + "-" + month + "-" + day + "  " + hourAndMin);

                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.custom_notify_remind_time_dialog_title))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis())
                .setMaxMillseconds(System.currentTimeMillis() + tenYears)
                .setCurrentMillseconds(currentTime)
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.ALL)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "all");
    }

    private void showNotifyDialog() {
        dialog = new CustomBottomDialog(CustomNotifyEditActivity.this);
        dialog.setTitle(getString(R.string.custom_notify_remind_time));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(currentNotifyTimeIndex);
        weekLayout.addView(weekWheelView);
        dialog.setMainView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String str = weekWheelView.getSeletedItem();
            mNotifyTimeTv.setText(str);
            currentNotifyTimeIndex = defaultNotifyTimeStrs.indexOf(str);
            dialog.dismiss();
        });
        dialog.show();
    }

    private void showBirthNotifyDialog() {
        dialog = new CustomBottomDialog(CustomNotifyEditActivity.this);
        dialog.setTitle(getString(R.string.custom_notify_remind_time));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultBirthNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(currentNotifyTimeIndex);
        weekLayout.addView(weekWheelView);
        dialog.setMainView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String str = weekWheelView.getSeletedItem();
            mNotifyTimeTv.setText(str);
            currentNotifyTimeIndex = defaultBirthNotifyTimeStrs.indexOf(str);
            dialog.dismiss();
        });
        dialog.show();
    }
    private void showAnnNotifyDialog() {
        dialog = new CustomBottomDialog(CustomNotifyEditActivity.this);
        dialog.setTitle(getString(R.string.custom_notify_remind_time));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultAnnNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(currentNotifyTimeIndex);
        weekLayout.addView(weekWheelView);
        dialog.setMainView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String str = weekWheelView.getSeletedItem();
            mNotifyTimeTv.setText(str);
            currentNotifyTimeIndex = defaultAnnNotifyTimeStrs.indexOf(str);
            dialog.dismiss();
        });
        dialog.show();
    }

    private void showRepeatDialog() {
        dialog = new CustomBottomDialog(CustomNotifyEditActivity.this);
//        dialog.setTitle(getString(R.string.custom_notify_remind_mode));
//        dialog.setInfo(getString(R.string.custom_notify_remind_mode_info));
        RecyclerView recyclerView = new RecyclerView(this);
        recyclerView.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                Apputils.dp2px(this, 200)));
        recyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        final WeekSelectAdapter adapter = new WeekSelectAdapter(currentRepeatType, this);
        recyclerView.setAdapter(adapter);

        dialog.setMainView(recyclerView);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
            currentRepeatType = adapter.getSelectWeek();
            mRepeatTv.setText(setStringToView(currentRepeatType));
            dialog.dismiss();
        });
        dialog.show();
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationTv.setTextColor(0xffffffff);
            mShortVibrationTv.setTextColor(0xde000000);
        } else {
            mShortVibrationTv.setTextColor(0xffffffff);
            mLongVibrationTv.setTextColor(0xde000000);
        }
    }

    private void saveNowModel(boolean isSwitch) {
        if (!isSwitch)
            BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());

        if (isSwitch) {
            mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
            //切换时候的动画
            Animation anim = AnimationUtils.loadAnimation(this, nowSetModel.isNotifySwitch() ? R.anim.layout_open : R.anim.layout_close);
            if (nowSetModel.isNotifySwitch()) {
                mPeriodSettingContent.setVisibility(View.VISIBLE);
            } else {
                anim.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        mPeriodSettingContent.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
            }
            mPeriodSettingContent.startAnimation(anim);
        }
    }

    private String getHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_event_now);
        switch (index) {
            case 0:
                string = getString(R.string.custom_notify_event_now);
                break;
            case 1:
                string = getString(R.string.custom_notify_event_15m);
                break;
            case 2:
                string = getString(R.string.custom_notify_event_30m);
                break;
            case 3:
                string = getString(R.string.custom_notify_event_1h);
                break;
            case 4:
                string = getString(R.string.custom_notify_event_6h);
                break;
            case 5:
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 6:
                string = getString(R.string.custom_notify_remind_3d);
                break;
        }
        return string;
    }

    private String getBirthHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_remind_1d);
        switch (index) {
//            case 0:
//                string = getString(R.string.custom_notify_remind_birthday_day);
//                break;
            case 0:
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 1:
                string = getString(R.string.custom_notify_remind_2d);
                break;
            case 2:
                string = getString(R.string.custom_notify_remind_3d);
                break;
            case 3:
                string = getString(R.string.custom_notify_remind_7d);
                break;
        }
        return string;
    }

    private String getAnnHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_remind_1d);
        switch (index) {
//            case 0:
//                string = getString(R.string.custom_notify_remind_ann_day);
//                break;
            case 0:
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 1:
                string = getString(R.string.custom_notify_remind_2d);
                break;
            case 2:
                string = getString(R.string.custom_notify_remind_3d);
                break;
            case 3:
                string = getString(R.string.custom_notify_remind_7d);
                break;
        }
        return string;
    }

    private int getHourIndexByDouble(double d) {
        int index = 0;
        int size = DEFAULT_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            if (d == DEFAULT_NOTIFY_TIME_DOUBLES[i]) {
                index = i;
            }
        }
        return index;
    }

    private int getBirthHourIndexByDouble(double d) {
        int index = 0;
        int size = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            if (d == DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[i]) {
                index = i;
            }
        }
        return index;
    }

    private String setStringToView(String str) {
        if (TextUtils.isEmpty(str) || TextUtils.equals(str, "0")) {
            return getString(R.string.custom_notify_one_time);
        } else if (TextUtils.equals(str, "1,2,3,4,5,6,7")) {
            return getString(R.string.custom_notify_every_day);
        } else {
            StringBuffer buffer = new StringBuffer();
            char[] chars = str.toCharArray();
            int charLength = chars.length;
            for (int i = 0; i < charLength; i++) {
                if (TextUtils.equals(",", String.valueOf(chars[i]))) {
                    buffer.append("、");
                } else {
                    char tempChar = chars[i];
                    int tempInt = Integer.parseInt(String.valueOf(tempChar));
                    buffer.append(getResources().getStringArray(R.array.week_name)[tempInt - 1]);
                }
            }
            return buffer.toString();
        }
    }

    private void delete() {
        HttpHelper.customService.deleteCustom(bean.getDefine_id(),"")
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(CustomNotifyEditActivity.this, R.string.custom_notify_list_delete_fail);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            deleteSuccess();
                        }
                    }
                });

    }

    private void deleteSuccess() {
        EventBus.onPostReceived(S.E.E_CUSTOM_DELETE_SUCCESSED, bean);
        finish();
    }
}
