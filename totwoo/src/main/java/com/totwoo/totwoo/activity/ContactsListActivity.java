package com.totwoo.totwoo.activity;

import android.animation.Animator;
import android.app.Activity;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.event.EventBus;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.jakewharton.rxbinding.widget.RxTextView;
import com.jakewharton.rxbinding.widget.TextViewAfterTextChangeEvent;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.CallRemindContact;
import com.totwoo.totwoo.bean.GreetingCard;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.bean.SecurityContactsBean;
import com.totwoo.totwoo.bean.SosContactsBean;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.bean.holderBean.SendGreetingCardRequest;
import com.totwoo.totwoo.bean.holderBean.SendGreetingCardResponse;
import com.totwoo.totwoo.data.GreetingCardLoader;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.CustomProgressBarDialog;
import com.totwoo.totwoo.widget.KeyboardLayout;
import com.umeng.analytics.MobclickAgent;

import net.sourceforge.pinyin4j.PinyinHelper;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Observable;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.functions.FuncN;
import rx.schedulers.Schedulers;

/**
 * 联系人列表页, 选择联系人发送贺卡
 * <p/>
 * 因为界面与{@link com.totwoo.totwoo.activity.TheHeartChooseActivity}
 * 一致性较强, 业务逻辑相差很多, 因此公用界面 xml 文件
 */
public class ContactsListActivity extends BaseActivity implements LoaderManager.LoaderCallbacks<Cursor> {

    public static final int SEND_SUCCESS = 1;

    public static int SEND_ERROR = 0;

    public static final String IS_SECURITY_CONTACT = "isSecurityContact";
    public static final String SECURITY_CONTACT_LIST = "SecurityContactList";
    public static final String REPORT_CONTACT_LIST = "reportContactList";

    /**
     * 用户列表对应的ListView
     */
    @BindView(R.id.the_heart_choose_listview)
    ListView mListView;

    /**
     * 没有搜索词是的 阴影层
     */
    @BindView(R.id.the_heart_choose_no_filter_layer)
    View noFilterLayer;

    /**
     * 搜索无果的文案
     */
    @BindView(R.id.the_heart_choose_filter_no_result_tv)
    TextView filterNoResultTv;

    @BindView(R.id.contacts_choose_scroll_view)
    HorizontalScrollView chooseScrollView;

    /**
     * 已选选择的联系人列表
     */
    @BindView(R.id.contacts_choose_layout)
    LinearLayout chooseLayout;

    @BindView(R.id.manually_add_contacts_line)
    View line;

    @BindView(R.id.manually_add_contacts_cl)
    ConstraintLayout manuallyAddCl;
    @BindView(R.id.manually_add_contacts_tv)
    TextView manuallyAddTv;
    @BindView(R.id.manually_add_contacts_question_cl)
    ConstraintLayout manuallyAddIv;

    @BindView(R.id.add_contact_layout)
    RelativeLayout addContactLayout;

    @BindView(R.id.add_contact_country_code)
    TextView addContactCodeTv;

    @BindView(R.id.add_contact_et)
    EditText addContactEt;

    @BindView(R.id.add_contact_ok)
    TextView addContactOkTv;

    @BindView(R.id.conent_layout)
    KeyboardLayout contentLayout;

    @BindView(R.id.card_store_lv)
    LottieAnimationView mCardStoreLv;

    @BindView(R.id.memory_save_bg)
    View saveBg;

    /**
     * 键盘高度. 默认为关闭,因此为 0
     */
    private int imeHeight;

    /**
     * ListView 对应 Adapter
     */
    private ContactsAdapter mAdapter;

    /**
     * 加载进度框
     */
    private CustomProgressBarDialog progressBar;

    private CustomDialog resultDialog;

    /**
     * 已选择的联系人列表
     */
    private ArrayList<Contact> chooseContactList;

    private EditText searchView;

    private ImageView searchIcon;

    private String mCurFilter;

    private GreetingCard mCard;
    //是否是选择重要联系人
    private boolean isImportantContact;

    private boolean isSecurityContact;
    private ArrayList<SecurityContactsBean> contactsBeans;

    //重要联系人还能选几个
    private int allowAddCount;

    private ArrayList<CallRemindContact> mCallRemindContacts;

    private String country_code_value = "86";
    private int securityContactSelectedSize;

    private void initChooseList() {
        int choosedCount = allowAddCount;
        Intent intent = getIntent();
        for (int i = 0; i < choosedCount; i++) {
            SosContactsBean.EmergencyPhoneDataBean bean = (SosContactsBean.EmergencyPhoneDataBean) intent.getSerializableExtra("Contact" + i);
            if (bean == null)
                continue;

            Contact c = new Contact();
            c.phone = bean.getEmergencyPhone();
            c.name = bean.getEmergencyName();
            chooseContactList.add(c);
        }

        if (isSecurityContact && contactsBeans.size() > 0) {
            securityContactSelectedSize = contactsBeans.size();
//            for (SecurityContactsBean securityContactsBean : contactsBeans) {
//                Contact c = new Contact();
//                c.phone = securityContactsBean.getTel();
//                c.name = securityContactsBean.getName();
//                chooseContactList.add(c);
//            }
        }

        if (chooseContactList.size() != 0)
            updateChooseView(false);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_contact_list);
        ButterKnife.bind(this);

//        mCard = (GreetingCard) getIntent().getSerializableExtra(GreetingCardShowActivity.EXAT_GREETING_CARD);
        //是否是选择重要联系人
        isImportantContact = getIntent().getBooleanExtra("isImportantContact", false);
        allowAddCount = getIntent().getIntExtra("allowAddCount", 3);

        isSecurityContact = getIntent().getBooleanExtra(IS_SECURITY_CONTACT, false);
        contactsBeans = new ArrayList<>();
        if (getIntent().getParcelableArrayListExtra(SECURITY_CONTACT_LIST) != null) {
            contactsBeans.addAll(getIntent().getParcelableArrayListExtra(SECURITY_CONTACT_LIST));
        }


        line.setVisibility(View.VISIBLE);
        manuallyAddCl.setVisibility(View.VISIBLE);

        mAdapter = new ContactsAdapter(this, null);
        mListView.setAdapter(mAdapter);

        chooseContactList = new ArrayList<>();
        initChooseList();


        if (!Apputils.systemLanguageIsChinese(this)) {
            country_code_value = Apputils.getSystemLanguageCountryCode(this);
            addContactCodeTv.setText("+" + country_code_value);
        }

        // 添加条目点击事件
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view,
                                    int position, long id) {
                //不能超过可添加重要联系人联系人
                if (isImportantContact && chooseContactList.size() >= allowAddCount) {
                    ToastUtils.show(getBaseContext(), getString(R.string.important_contact_exceed), Toast.LENGTH_LONG);
                    return;
                }
                if (isSecurityContact && chooseContactList.size() + securityContactSelectedSize >= 3) {
                    ToastUtils.show(getBaseContext(), getString(R.string.safe_contacts_add_over_count), Toast.LENGTH_LONG);
                    return;
                }
                // 选择联系人
                chooseContact(position);

                if (searchView != null && searchView.getVisibility() == View.VISIBLE) {
                    mCurFilter = null;
                    getSupportLoaderManager().restartLoader(0, null,
                            ContactsListActivity.this);

                    closeSearchView();
                }
            }
        });


        // 设置当列表快速滑动时， 不加载图片
        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                switch (scrollState) {
                    case AbsListView.OnScrollListener.SCROLL_STATE_IDLE:
                        if (!mAdapter.isLoadImage) {
                            mAdapter.isLoadImage = true;
                            // 每次停止加载之后, 刷新当前界面元素, 加载对应的图片
                            if (mAdapter != null) {
                                mAdapter.notifyDataSetChanged();
                            }
                        }
                        break;
                    case AbsListView.OnScrollListener.SCROLL_STATE_TOUCH_SCROLL:
                        mAdapter.isLoadImage = true;
                        break;
                    case AbsListView.OnScrollListener.SCROLL_STATE_FLING:
                        mAdapter.isLoadImage = false;
                        break;
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem,
                                 int visibleItemCount, int totalItemCount) {
            }
        });
        if (isImportantContact) {
            manuallyAddCl.setVisibility(View.GONE);
            line.setVisibility(View.GONE);
        } else {
            if (isSecurityContact) {
                manuallyAddIv.setVisibility(View.GONE);
            }
            manuallyAddTv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (isSecurityContact && chooseContactList.size() + securityContactSelectedSize >= 3) {
                        ToastUtils.show(getBaseContext(), getString(R.string.important_contact_exceed), Toast.LENGTH_LONG);
                        return;
                    }
                    showMaunAddView();
                }
            });
            if (!Apputils.systemLanguageIsChinese(ContactsListActivity.this)) {
                manuallyAddIv.setVisibility(View.GONE);
            }
            manuallyAddIv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.SECRET_SELECTRECEIVER_HELP);
                    WebViewActivity.loadUrl(ContactsListActivity.this, HttpHelper.URL_HELP_TOTWOO, false);
                }
            });
        }

        // 通过监听键盘状态. 动态改变组件
        contentLayout.setKeyboardListener(new KeyboardLayout.KeyboardLayoutListener() {
            @Override
            public void onKeyboardStateChanged(boolean isActive, int keyboardHeight) {
                if (isActive) {
                    imeHeight = keyboardHeight;
                    updateListViewHeight();
                } else {
                    imeHeight = 0;
                    hideManuAddView();
                }
            }
        });

        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                updateListViewHeight();
            }
        }, 150);


        try {
            getSupportLoaderManager().initLoader(0, null, this);
        } catch (Exception e) {
            e.printStackTrace();
        }

        mCardStoreLv.setImageAssetsFolder("lottie_card_store/");
        mCardStoreLv.setAnimation("card_store.json");
    }

    /**
     * 展示手动添加联系人的 Veiw
     */
    private void showMaunAddView() {
        addContactLayout.setVisibility(View.VISIBLE);
        addContactEt.requestFocus();

        InputMethodManager img = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        img.showSoftInput(addContactEt, 0);

        addContactCodeTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ContactsListActivity.this, CountryCodeListActivity.class);
                startActivityForResult(intent, 0);

                // 切换动画
                overridePendingTransition(R.anim.activity_fade_in,
                        R.anim.activity_fade_out);
            }
        });

        addContactOkTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String text = addContactEt.getText().toString().trim();
                if (TextUtils.isEmpty(text)) {
                    ToastUtils.showLong(ContactsListActivity.this, R.string.please_input_phone);
                    return;
                }

                if (!android.text.TextUtils.isDigitsOnly(text)) {
                    ToastUtils.showLong(ContactsListActivity.this, R.string.error_incorrect_phone);
                    return;
                }

                updateChooseView(false);

                Contact bean = new Contact();
                bean.phone = country_code_value + text;

//                bean.phone = CommonUtils.checkPhoneNumber(text);
                bean.name = "+" + bean.phone;

                // 根据号码去重
                for (Contact con : chooseContactList) {
                    if (con.phone.equals(bean.phone)) {
                        chooseContactList.remove(con);
                        break;
                    }
                }
                chooseContactList.add(bean);

                updateChooseView(false);
                addContactEt.setText("");

                InputMethodManager img = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
                img.hideSoftInputFromWindow(addContactEt.getWindowToken(), 0);
            }
        });
    }

    private void hideManuAddView() {
        addContactLayout.setVisibility(View.GONE);
        updateListViewHeight();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (resultCode) {
            case 0:
                if (data != null) {
                    country_code_value = data.getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                    addContactCodeTv.setText("+" + country_code_value);
                    showMaunAddView();

                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            InputMethodManager img = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
                            img.showSoftInput(addContactEt, 0);
                        }
                    }, 150);
                }
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 选择联系人, 对于号码相同的联系人, 直接去重
     * 更新视图
     *
     * @param position
     */
    private void chooseContact(int position) {
        // 增加联系人的操作

        Contact bean = getContact((Cursor) mAdapter.getItem(position));
        // 检查号码是否有效, 并格式化为需要的号码格式
//        String totwooPhone = filterPhone(bean.phone);
//        if (totwooPhone != null) {
//            bean.phone = totwooPhone;
//        } else {
//            ToastUtils.showLong(this, getString(R.string.phone_number_invalid));
//            return;
//        }
        bean.phone = CommonUtils.checkPhoneNumber(bean.phone);
        if (TextUtils.isEmpty(CommonUtils.checkPhoneNumber(bean.phone))) {
            ToastUtils.showLong(this, getString(R.string.phone_number_invalid));
        }

        // 根据号码去重
        for (Contact con : chooseContactList) {
            if (con.phone.equals(bean.phone)) {
                chooseContactList.remove(con);
                break;
            }
        }
        chooseContactList.add(bean);

        updateChooseView(false);
    }

    int lastScrollX = 0;

    /**
     * 更新已选择的联系人列表
     *
     * @param isDelete 是否是删除, 控制列表的滚动
     */
    private void updateChooseView(boolean isDelete) {
        if (chooseContactList == null || chooseContactList.size() == 0) {
            chooseScrollView.setVisibility(View.GONE);
            updateListViewHeight();
        } else {
            chooseScrollView.setVisibility(View.VISIBLE);

            lastScrollX = chooseScrollView.getScrollX();
            chooseLayout.removeAllViews();

            for (Contact con : chooseContactList) {
                chooseLayout.addView(getChooseView(con));
            }

            updateListViewHeight();

            // 如果是删除列表, 保持原来的位置, 如果是添加, 滚动到最后
            if (isDelete) {
                chooseScrollView.smoothScrollTo(lastScrollX, 0);
            } else {
                // 立即滚动有时会无效, 做延时处理
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        chooseScrollView.smoothScrollTo(100000, 0);
                    }
                }, 200);
            }
        }
    }

    /**
     * 构建单个选择联系人的 View
     *
     * @param con
     * @return
     */
    private View getChooseView(final Contact con) {
        TextView v = new TextView(this);
        v.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        int offset = Apputils.dp2px(this, 16);
        v.setPadding(0, offset, offset, offset);
        v.setText(TextUtils.isEmpty(con.name) ? con.phone : con.name);
        v.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.delete_gray_icon, 0);
        v.setCompoundDrawablePadding(Apputils.dp2px(this, 4));

        v.setTextColor(getResources().getColor(R.color.text_color_black_important));
        v.setTextSize(16);
        v.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                chooseContactList.remove(con);
                updateChooseView(true);
            }
        });
        return v;
    }

    @Override
    protected void initTopBar() {
        // 防止再次initTopBar 造成的界面混乱
        if (searchView != null && searchView.getVisibility() == View.VISIBLE) {
            return;
        }
        setTopBackIcon(R.drawable.back_icon_black);
        if (isImportantContact) {
            setTopTitle(getString(R.string.call_remind_select_contact_title));
        } else if (isSecurityContact) {
            setTopTitle(getString(R.string.safe_contacts_add_title));
        } else {
            setTopTitle(R.string.select_contact);

        }
        setTopRightIcon(R.drawable.top_right_icon_ok);


        setTopRightOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isSecurityContact) {
//                    && securityContactSelectedSize == 0
                    if (chooseContactList.size() == 0) {
                        ToastUtils.showShort(ContactsListActivity.this, R.string.safe_emergency_add_empty_toast);
                        return;
                    }
                    if (securityContactSelectedSize != 0) {
                        for (SecurityContactsBean securityContactsBean : contactsBeans) {
                            Contact c = new Contact();
                            c.phone = securityContactsBean.getTel();
                            c.name = securityContactsBean.getName();
                            chooseContactList.add(c);
                        }
                    }
                    //目前直接把选中的联系人传给服务器
                    List<SecurityContactsBean> beans = new ArrayList<>();
                    for (Contact contact : chooseContactList) {
                        beans.add(new SecurityContactsBean(contact.name, contact.phone));
                    }

                    Gson gson = new Gson();
                    String jsonString = gson.toJson(beans);
                    saveSecurityContacts(jsonString);
                } else if (isImportantContact) {
                    mCallRemindContacts = new ArrayList<>();
                    Observable.from(chooseContactList)
                            .subscribe(new Subscriber<Contact>() {
                                @Override
                                public void onCompleted() {
                                    if (mCallRemindContacts.size() == 0) {
                                        ToastUtils.show(getBaseContext(), getString(R.string.call_remind_add_contact_0), Toast.LENGTH_LONG);
                                        return;
                                    }
                                    String dataJson = PreferencesUtils.getString(getBaseContext(), CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_DATA_KEY, "");
                                    ArrayList<CallRemindContact> contacts = new Gson().fromJson(dataJson, new TypeToken<List<CallRemindContact>>() {
                                    }.getType());
                                    if (contacts != null) {
                                        for (CallRemindContact mContact : contacts) {
                                            for (CallRemindContact contact : mCallRemindContacts) {
                                                if (contact.getPhoneNumber().equals(mContact.getPhoneNumber())) {
                                                    ToastUtils.show(getBaseContext(), getString(R.string.cant_choose_repeated_contact), Toast.LENGTH_LONG);
                                                    return;
                                                }
                                            }
                                        }
                                    }
                                    getIntent().putParcelableArrayListExtra("contact", mCallRemindContacts);
                                    setResult(0, getIntent());
                                    finish();
                                }

                                @Override
                                public void onError(Throwable e) {

                                }

                                @Override
                                public void onNext(Contact contact) {
                                    CallRemindContact callRemindContact = new CallRemindContact(contact.phone, contact.id, contact.name, "RED", 0);
                                    mCallRemindContacts.add(callRemindContact);
                                }
                            });
                } else {
                    getTopRightIcon().setClickable(false);
                    sendGreetingCard();
                }
            }
        });
        searchIcon = getTopRight2Icon();
        searchIcon.setImageResource(R.drawable.search_icon);
        searchIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 初始化搜索框
                initSearchView();
            }
        });
    }

    private void saveSecurityContacts(String jsonString) {
        LogUtils.e("aab jsonString = " + jsonString);
        HttpHelper.safeService.saveContacts(jsonString)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(ContactsListActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        finish();
                    }
                });
    }


    private void showSuccessAnim(final Activity activity) {
        saveBg.setVisibility(View.VISIBLE);
        mCardStoreLv.setVisibility(View.VISIBLE);
        mCardStoreLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                setResult(SEND_SUCCESS);
//                Intent intent = new Intent(ContactsListActivity.this, GreetingCardListActivity.class);
//                intent.putExtra("test1", "okok");
//                startActivity(intent);
                activity.finish();
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mCardStoreLv.playAnimation();
    }

    /**
     * 发送贺卡操作
     */
    private void sendGreetingCard() {
        if (chooseContactList == null || chooseContactList.size() == 0) {
            ToastUtils.showLong(this, R.string.send_card_no_receive_err);
            getTopRightIcon().setClickable(true);
            return;
        }

        showProgressBar(true);

        final List<Observable<HttpBaseBean<GetQiNiuToken>>> getTokenObervables = new ArrayList<>();
        final ArrayList<Observable<QiNiuResponse>> qiNiuUploadObervables = new ArrayList<>();
        final Owner owner = ToTwooApplication.owner;

        final List<String> keys = new ArrayList<>();

        final Map<String, String> paths = new HashMap<>();

        final GreetingCard.GreetingCardData data = mCard.getGreetingCardData();

        if (!TextUtils.isEmpty(data.getImageUrl())) {
            keys.add("image");
            getTokenObervables.add(HttpHelper.card.getQiNiuToken(1, "greetingcard"));
        }

        if (!TextUtils.isEmpty(data.getAudioUrl())) {
            keys.add("audio");
            getTokenObervables.add(HttpHelper.card.getQiNiuToken(2, "greetingcard"));
        }
        if (!TextUtils.isEmpty(data.getAudioPreviewImageUrl())) {
            keys.add("audioImage");
            getTokenObervables.add(HttpHelper.card.getQiNiuToken(1, "greetingcard"));
        }

        if (!TextUtils.isEmpty(data.getVedioUrl())) {
            keys.add("vedio");
            getTokenObervables.add(HttpHelper.card.getQiNiuToken(3, "greetingcard"));
            keys.add("vedioImage");
            getTokenObervables.add(HttpHelper.card.getQiNiuToken(1, "greetingcard"));
        }

        if (!TextUtils.isEmpty(data.getText())) {
            keys.add("text");
            getTokenObervables.add(HttpHelper.card.getQiNiuToken(1, "greetingcard"));
        }


        //获取所有token
        Observable.zip(getTokenObervables, new FuncN<Map<String, GetQiNiuToken>>() {
                    @Override
                    public Map<String, GetQiNiuToken> call(Object... args) {
                        Map<String, GetQiNiuToken> qiniuTokens = new HashMap<>();
                        for (int i = 0; i < args.length; i++) {
                            qiniuTokens.put(keys.get(i), ((HttpBaseBean<GetQiNiuToken>) args[i]).getData());
                        }
                        return qiniuTokens;
                    }
                }).subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Map<String, GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {

                        Observable.zip(qiNiuUploadObervables, new FuncN<SendGreetingCardRequest.GreetingCardDataBean>() {
                                    @Override
                                    public SendGreetingCardRequest.GreetingCardDataBean call(Object... args) {
                                        SendGreetingCardRequest.GreetingCardDataBean filePaths = new SendGreetingCardRequest.GreetingCardDataBean();
                                        for (int i = 0; i < args.length; i++) {

                                            paths.put(keys.get(i), ((QiNiuResponse) args[i]).getKey());
                                        }

                                        if (!TextUtils.isEmpty(data.getImageUrl())) {
                                            filePaths.setImageUrl(paths.get("image"));
                                        }

                                        if (!TextUtils.isEmpty(data.getAudioUrl())) {
                                            filePaths.setAudioUrl(paths.get("audio"));
                                        }
                                        if (!TextUtils.isEmpty(data.getAudioPreviewImageUrl())) {
                                            filePaths.setAudioPreviewImageUrl(paths.get("audioImage"));
                                        }

                                        if (!TextUtils.isEmpty(data.getVedioUrl())) {
                                            filePaths.setVedioUrl(paths.get("vedio"));
                                            filePaths.setVedioPreviewImageUrl(paths.get("vedioImage"));
                                        }

                                        if (!TextUtils.isEmpty(data.getText())) {
                                            filePaths.setText(data.getText());
                                            filePaths.setTextPreviewImageUrl(paths.get("text"));
                                        }
                                        return filePaths;
                                    }
                                }).subscribeOn(Schedulers.newThread())
                                .observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<SendGreetingCardRequest.GreetingCardDataBean>() {
                                    @Override
                                    public void onCompleted() {
                                    }

                                    @Override
                                    public void onError(Throwable e) {
                                        getTopRightIcon().setClickable(true);
                                        e.printStackTrace();
//                              ToastUtils.showShort(ContactsListActivity.this, "发送失败");
                                        showResultDialog(false);
                                        showProgressBar(false);
                                    }

                                    @Override
                                    public void onNext(SendGreetingCardRequest.GreetingCardDataBean greetingCardDataBean) {
                                        SendGreetingCardRequest request = new SendGreetingCardRequest();
                                        request.setSenderId(owner.getTotwooId() + "");
                                        request.setSenderName(mCard.getSenderName());

                                        List<SendGreetingCardRequest.ReceiversBean> receivers = new ArrayList<SendGreetingCardRequest.ReceiversBean>();
                                        for (Contact contact : chooseContactList) {
                                            receivers.add(new SendGreetingCardRequest.ReceiversBean(contact.phone, contact.name));
                                        }

                                        request.setReceivers(receivers);
                                        request.setGreetingCardData(greetingCardDataBean);
                                        Gson gson = new Gson();
                                        int type = GreetingCardLoader.loadGreetCardType(gson.fromJson(gson.toJson(request.getGreetingCardData()), GreetingCard.GreetingCardData.class));
                                        LogUtils.i(type + "");
                                        request.setGreetingCardType(type);
                                        LogUtils.e("JSON = " + gson.toJson(request.getReceivers()));
                                        HttpHelper.card.sendCard(request.getSenderId(),
                                                        request.getSenderName(), request.getGreetingCardType(),
                                                        gson.toJson(request.getGreetingCardData()),
                                                        gson.toJson(request.getReceivers()))
                                                .subscribeOn(Schedulers.newThread())
                                                .observeOn(AndroidSchedulers.mainThread())
                                                .subscribe(new Observer<HttpBaseBean<SendGreetingCardResponse>>() {
                                                    @Override
                                                    public void onCompleted() {
//                                                ToastUtils.showShort(ContactsListActivity.this, "发送成功");
                                                        showResultDialog(true);
                                                        showProgressBar(false);
//                                                getTopRightIcon().setClickable(true);
//                                                setResult(SEND_SUCCESS);
//                                                finish();
                                                    }

                                                    @Override
                                                    public void onError(Throwable e) {
//                                                ToastUtils.showShort(ContactsListActivity.this, "发送失败");
                                                        getTopRightIcon().setClickable(true);
                                                        showResultDialog(false);
                                                        showProgressBar(false);
                                                    }

                                                    @Override
                                                    public void onNext(HttpBaseBean<SendGreetingCardResponse> getQiNiuTokenHttpBaseBean) {
                                                        LogUtils.i("");
                                                    }

                                                });
                                    }
                                });

                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
//                        ToastUtils.showShort(ContactsListActivity.this, "发送失败");
                        getTopRightIcon().setClickable(true);
                        showResultDialog(false);
                        showProgressBar(false);
                    }

                    @Override
                    public void onNext(Map<String, GetQiNiuToken> getQiNiuTokens) {
                        if (!TextUtils.isEmpty(data.getImageUrl())) {

                            GetQiNiuToken token = getQiNiuTokens.get("image");
                            addUploadFile(token, mCard.getGreetingCardData().getImageUrl(), qiNiuUploadObervables);
                        } else if (!TextUtils.isEmpty(data.getAudioUrl())) {
                            GetQiNiuToken token = getQiNiuTokens.get("audio");
                            addUploadFile(token, mCard.getGreetingCardData().getAudioUrl(), qiNiuUploadObervables);

                            if (!TextUtils.isEmpty(data.getAudioPreviewImageUrl())) {
                                token = getQiNiuTokens.get("audioImage");
                                addUploadFile(token, mCard.getGreetingCardData().getAudioPreviewImageUrl(), qiNiuUploadObervables);
                            }
                        } else if (!TextUtils.isEmpty(data.getVedioUrl())) {
                            GetQiNiuToken token = getQiNiuTokens.get("vedio");
                            addUploadFile(token, mCard.getGreetingCardData().getVedioUrl(), qiNiuUploadObervables);

                            if (!TextUtils.isEmpty(data.getVedioPreviewImageUrl())) {
                                token = getQiNiuTokens.get("vedioImage");
                                addUploadFile(token, mCard.getGreetingCardData().getVedioPreviewImageUrl(), qiNiuUploadObervables);
                            }
                        }
//                        else if (!StringUtils.isEmpty(data.getText())) {
//                            GetQiNiuToken token = getQiNiuTokens.get("text");
//                            if (!StringUtils.isEmpty(data.getTextPreviewImageUrl())) {
//                                addUploadFile(token, mCard.getGreetingCardData().getTextPreviewImageUrl(), qiNiuUploadObervables);
//                            }
//                        }
                    }
                });
    }

    private void addUploadFile(GetQiNiuToken token, String path, ArrayList<Observable<QiNiuResponse>> qiNiuPaths) {
        File file = new File(path);
        RequestBody requestFile =
                RequestBody.create(MediaType.parse("multipart/form-data"), file);

        MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

        qiNiuPaths.add(HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, token.getFilePath()), RequestBody.create(null, token.getUpToken())));
    }

    /**
     * 初始化搜索框, 相应的事件
     */
    protected void initSearchView() {
        searchView = getTopSearchView();
        getTopTitleView().setVisibility(View.GONE);
        searchIcon.setVisibility(View.GONE);
        noFilterLayer.setVisibility(View.VISIBLE);

        // 设置监听搜索
        RxTextView.afterTextChangeEvents(searchView)
                .throttleWithTimeout(400, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
                .subscribe(new Action1<TextViewAfterTextChangeEvent>() {
                    @Override
                    public void call(TextViewAfterTextChangeEvent textViewAfterTextChangeEvent) {
                        String newText = textViewAfterTextChangeEvent.editable().toString();
                        String newFilter = !TextUtils.isEmpty(newText) ? newText : null;

                        if (searchView != null && searchView.getVisibility() == View.GONE) {
                            return;
                        }

                        // 根据当前搜索框状态, 改变右侧关闭按钮状态
                        // 设置右侧关闭按钮监听
                        if (!TextUtils.isEmpty(newText)) {
                            noFilterLayer.setVisibility(View.GONE);
                            searchIcon.setImageResource(R.drawable.close_icon);
                            searchIcon.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    searchView.setText("");
                                    searchIcon.setVisibility(View.GONE);
                                }
                            });
                        } else {
                            noFilterLayer.setVisibility(View.VISIBLE);
                            searchIcon.setVisibility(View.GONE);
                        }

                        // 判断当前搜索字段是否发生了实质性变化, 如果有, 重新加载
                        if (mCurFilter == null && newFilter == null) {
                            return;
                        }
                        if (mCurFilter != null && mCurFilter.equals(newFilter)) {
                            return;
                        }
                        mCurFilter = newFilter;
                        getSupportLoaderManager().restartLoader(0, null,
                                ContactsListActivity.this);
                    }
                });

        InputMethodManager inputManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        inputManager.toggleSoftInputFromWindow(searchView.getWindowToken(), 0,
                0);

        // 关闭搜索框
        noFilterLayer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                closeSearchView();
            }
        });
    }

    /**
     * 关闭搜索框
     */
    protected void closeSearchView() {
        searchView.setText("");
        searchView.setVisibility(View.GONE);
        getTopTitleView();
        searchIcon.setVisibility(View.VISIBLE);
        searchIcon.setImageResource(R.drawable.search_icon);
        searchIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initSearchView();
            }
        });
        noFilterLayer.setVisibility(View.GONE);

        // 关闭输入法
        InputMethodManager inputManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        inputManager.hideSoftInputFromWindow(searchView.getWindowToken(), 0);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (searchView != null
                    && searchView.getVisibility() == View.VISIBLE) {
                closeSearchView();
                return true;
            } else if (addContactLayout != null &&
                    addContactLayout.getVisibility() == View.VISIBLE) {
                hideManuAddView();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 取出手机号码中的无效字符, 返回简单的号码
     *
     * @param phone
     * @return
     */
    private String getSimplePhone(String phone) {
        if (phone == null) {
            return null;
        }
        // 过滤掉号码 中的空格和分割线
        char[] pp = new char[phone.length()];
        int j = 0;
        for (int i = 0; i < phone.length(); i++) {
            char c = phone.charAt(i);
            if (c != '-' && c != ' ') {
                pp[j++] = c;
            }
        }
        return new String(pp).trim();
    }

    /**
     * 获取sort key的首个字符，如果是英文字母就直接返回，否则返回#。
     *
     * @param sortKeyString 数据库中读取出的sort key
     * @return 英文字母或者#
     */
    public static String getSortKey(String sortKeyString) {
        String key = sortKeyString.substring(0, 1).toUpperCase(
                Locale.getDefault());
        if (key.matches("^[A-Z]$")) {
            return key;
        } else {
//            String[] keys = PinyinHelper
//                    .toHanyuPinyinStringArray(key.charAt(0));
//            if (keys != null) {
//                key = keys[0].substring(0, 1).toUpperCase(Locale.getDefault());
//                if (key.matches("^[A-Z]$")) {
//                    return key;
//                }
//            }
            try {
                key = PinyinHelper.toHanyuPinyinStringArray(key.charAt(0))[0].substring(0, 1);
                if (key.matches("^[A-Z]$")) {
                    return key;
                }
            } catch (Exception e) {
                e.printStackTrace();
                return "#";
            }

        }
        return "#";
    }

    /**
     * 指定的号码, 是否为 所需号码, 如果是, 返回指定格式, 如果否, 返回null
     *
     * @param phone
     * @return
     */
    private String filterPhone(String phone) {
        if (phone == null) {
            return null;
        }

        if (phone.startsWith("00")) {
            phone = phone.replaceFirst("00", "+");
        }
        if (!phone.startsWith("+")) {
            phone = "+" + phone;
        }

        PhoneNumberUtil numberUtil = PhoneNumberUtil.getInstance();

        String country_code = PreferencesUtils
                .getString(this, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(this) ? "86" : "39");

        try {
            phone = phone.replace(
                    "+",
                    "+"
                            + country_code);
            Phonenumber.PhoneNumber parse = numberUtil.parse(phone, country_code);
            if (numberUtil.isValidNumber(parse)) {
//                LogUtils.i("phone",phone.replace("+", ""));
                return phone.replace("+", "");
            } else {
                phone = phone.replace(
                        "+" + country_code,
                        "+");
                parse = numberUtil.parse(phone, country_code);
                if (numberUtil.isValidNumber(parse)) {
//                    LogUtils.i("phone",phone.replace("+", "")+"+");
                    return phone.replace("+", "");
                } else {
                    return null;
                }
            }
        } catch (NumberParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Loader<Cursor> onCreateLoader(int id, Bundle args) {
        // This is called when a new Loader needs to be created. This
        // sample only has one Loader, so we don't care about the ID.
        // First, pick the base URI to use depending on whether we are
        // currently filtering.

        Uri baseUri;
        if (mCurFilter != null) {
            baseUri = Uri.withAppendedPath(ContactsContract.CommonDataKinds.Phone.CONTENT_FILTER_URI,
                    Uri.encode(mCurFilter));
        } else {
            baseUri = ContactsContract.CommonDataKinds.Phone.CONTENT_URI;
        }

        // Now create and return a CursorLoader that will take care of
        // creating a Cursor for the data being displayed.
        String select = "((" + ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " NOTNULL) AND ("
                + ContactsContract.CommonDataKinds.Phone.HAS_PHONE_NUMBER + "=1))";
        return new CursorLoader(ContactsListActivity.this, baseUri,
                new String[]{ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME, ContactsContract.CommonDataKinds.Phone.NUMBER,
                        ContactsContract.Contacts.Photo.PHOTO_ID, ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                        ContactsContract.CommonDataKinds.Phone.SORT_KEY_PRIMARY, ContactsContract.Contacts._ID}, select, null,
                ContactsContract.CommonDataKinds.Phone.SORT_KEY_PRIMARY + " COLLATE LOCALIZED ASC");

    }

    @Override
    public void onLoadFinished(Loader<Cursor> loader, Cursor data) {
        // Swap the new cursor in. (The framework will take care of closing
        // the
        // old cursor once we return.)

        try {
            mAdapter.swapCursor(data);
        } catch (Exception e) {
            e.printStackTrace();
            // 权限禁止, 提示开启
            final CustomDialog dialog = new CustomDialog(this);
            dialog.setMessage(R.string.no_contact);
            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    dialog.dismiss();
                }
            });
            dialog.show();
        }

        findViewById(R.id.the_heart_choose_loadding_imge).setVisibility(
                View.GONE);

        if (data == null) {
            final CustomDialog dialog = new CustomDialog(this);
            dialog.setMessage(R.string.no_contact);
            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    dialog.dismiss();
                }
            });
            dialog.show();
        } else if (data.getCount() == 0) {
            filterNoResultTv.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(mCurFilter)) {
                filterNoResultTv.setText(R.string.no_contact);
            } else {
                filterNoResultTv.setText(getString(R.string.search_no_result, mCurFilter));
            }
        } else {
            filterNoResultTv.setVisibility(View.GONE);
        }
    }

    @Override
    public void onLoaderReset(Loader<Cursor> loader) {
        // This is called when the last Cursor provided to onLoadFinished()
        // above is about to be closed. We need to make sure we are no
        // longer using it.
        mAdapter.swapCursor(null);
    }

    /**
     * 展示进度框
     *
     * @param show true 为展示, false 为隐藏
     */
    private void showProgressBar(boolean show) {
        if (show) {
            if (progressBar == null) {
                progressBar = new CustomProgressBarDialog(this);
                progressBar.setMessage(R.string.sending);
            }
            progressBar.show();
        } else {

            if (progressBar != null && progressBar.isShowing()) {
                progressBar.dismiss();
            }
        }
    }

    private void updateListViewHeight() {
        LogUtils.i("update Contact list height, imeHeiht: " + imeHeight);

        if (mListView == null) {
            return;
        }

        int height = Apputils.getScreenHeight(this) - Apputils.getStatusHeight(this);

        height -= getResources().getDimensionPixelOffset(R.dimen.activity_actionbar_height);

        height -= manuallyAddCl.getMeasuredHeight();

        if (chooseScrollView.getVisibility() == View.VISIBLE) {
//            int chooseHeight = CommonUtils.dip2px(ContactsListActivity.this, 84);
//            height -= chooseHeight;
            height -= chooseLayout.getMeasuredHeight();
        }

        height -= imeHeight;

        LogUtils.i("update Contact list height, height: " + height);
        mListView.getLayoutParams().height = height - 20;
        mAdapter.notifyDataSetChanged();
    }

    /**
     * 通讯录列表适配器
     *
     * <AUTHOR>
     * @date 2015-2015年7月29日
     */
    class ContactsAdapter extends CursorAdapter {
        /**
         * 是否加载 图片， 用户快速滑动时， 停止加载图片
         */
        public boolean isLoadImage;
        private Context mContext;

        public ContactsAdapter(Context context, Cursor c) {
            super(context, c);
            mContext = context;
        }

        @Override
        public View newView(Context context, Cursor cursor, ViewGroup parent) {

            ViewHolder holder = new ViewHolder();

            View convertView = LayoutInflater.from(mContext).inflate(
                    R.layout.contacts_list_item, parent, false);

            holder.dividerLayout = (LinearLayout) convertView
                    .findViewById(R.id.contacts_item_divider_layout);
            holder.divider = (TextView) convertView
                    .findViewById(R.id.contacts_item_divider);
            holder.icon = (ImageView) convertView
                    .findViewById(R.id.contacts_item_icon);
            holder.name = (TextView) convertView
                    .findViewById(R.id.contacts_item_name);
            holder.info = (TextView) convertView
                    .findViewById(R.id.contacts_item_state_info);
            holder.actionBtn = (TextView) convertView
                    .findViewById(R.id.contacts_item_btn);

            convertView.setTag(holder);

            return convertView;
        }

        @Override
        public Cursor runQueryOnBackgroundThread(CharSequence constraint) {
            LogUtils.i("runQueryOnBackgroundThread() === " + constraint);

            return super.runQueryOnBackgroundThread(constraint);
        }

        @Override
        public void bindView(View view, Context context, Cursor cursor) {
            ViewHolder holder = (ViewHolder) view.getTag();

            final Contact bean = getContact(cursor);
            if (bean == null) {
                return;
            }

            // 头像, 优先显示网络
            if (bean.id > 0 && isLoadImage) {
                holder.icon.setImageBitmap(getPhotoById(bean.id));
            } else {
                holder.icon.setImageResource(R.drawable.default_head_yellow);
            }

            if (bean.name != null) {
                holder.name.setText(bean.name);
            } else {
                holder.name.setText("");
            }

            // 如果当前的排序字段与前一个不同, 则显示排序字段
            if (needShowSortKey(cursor, bean)) {
                holder.dividerLayout.setVisibility(View.VISIBLE);
                holder.divider.setText(bean.sortKey);
            } else {
                holder.dividerLayout.setVisibility(View.GONE);
            }

            holder.info.setText(bean.phone);
//          holder.info.setTextColor(mContext.getResources().getColor(
//                        R.color.text_color_black_nomal));
        }
    }

    /**
     * 是否显示排序字段
     *
     * @param cursor
     * @return
     */
    public boolean needShowSortKey(Cursor cursor, Contact contact) {
        boolean need = true;

        if (cursor.moveToPrevious()) {
            need = !getContact(cursor).sortKey.equals(contact.sortKey);
            cursor.moveToNext();
        }

        return need;
    }

    /**
     * 通过制定的 Cursor 及当前游标位置获取 Contact
     *
     * @param phoneCursor
     * @return
     */
    public Contact getContact(Cursor phoneCursor) {
        if (phoneCursor == null) {
            return null;
        }
        Contact bean = new Contact();

        bean.name = phoneCursor.getString(0);
        bean.phone = getSimplePhone(phoneCursor.getString(1));

        // 得到联系人头像ID
        Long photoid = phoneCursor.getLong(2);

        // photoid 大于0 表示联系人有头像, 设置 Contact id 便于取头像
        if (photoid > 0) {
            // 得到联系人ID
            bean.id = phoneCursor.getLong(3);
        } else {
            bean.id = -1;
        }

        bean.sortKey = getSortKey(phoneCursor.getString(4));

        return bean;
    }

    /**
     * 展示结果提示框
     */
    private void showResultDialog(boolean isSuccess) {
        if (resultDialog == null) {
            resultDialog = new CustomDialog(this);
            resultDialog.setCanceledOnTouchOutside(false);
//            resultDialog.setNegativeButton(R.string.confirm);
        }

        if (isSuccess) {
//            resultDialog.setTitle(R.string.send_success_);
            /*resultDialog.setMessage(R.string.send_card_success);

            resultDialog.setPositiveButton(R.string.confirm, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    resultDialog.dismiss();
                    setResult(SEND_SUCCESS);
                    startActivity(new Intent(ContactsListActivity.this, GreetingCardListActivity.class));
                    finish();
                }
            });*/
            showSuccessAnim(this);
            EventBus.onPostReceived(S.E.E_SECRET_SEND_SUCCESSED, null);
            return;

        } else {
//            resultDialog.setTitle(R.string.send_failed_);
            resultDialog.setMessage(R.string.send_card_failed);
            resultDialog.setPositiveButton(R.string.confirm, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    resultDialog.dismiss();
                }
            });
        }
        resultDialog.show();
    }


    private Bitmap getPhotoById(long contactid) {

        Uri uri = ContentUris.withAppendedId(ContactsContract.Contacts.CONTENT_URI, contactid);
        InputStream input = ContactsContract.Contacts
                .openContactPhotoInputStream(getContentResolver(), uri);
        return BitmapFactory.decodeStream(input);
    }


    class ViewHolder {
        public LinearLayout dividerLayout;
        public TextView divider;
        public ImageView icon;
        private TextView name;
        private TextView info;
        private TextView actionBtn;
    }

    class Contact {
        public String name;
        public String phone;
        public String sortKey;
        public long id;
    }
}