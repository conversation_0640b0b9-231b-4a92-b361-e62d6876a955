package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.View;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.totwoo.totwoo.R;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

public class ImageShowActivity extends BaseActivity {
    @BindView(R.id.image_show_vp)
    ViewPager viewPager;

    MyPageAdapter pagerAdapter;
    private ArrayList<View> views;

    public static final String RESOURCE_IDS = "resource_ids";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_image_show);
        ButterKnife.bind(this);

        int[] ids = getIntent().getIntArrayExtra(RESOURCE_IDS);

        views = new ArrayList<>();

//        for (int i = 0; i < ids.length; i++) {
//            View view = getLayoutInflater().inflate();
//            views.add(view);
//        }

        pagerAdapter = new MyPageAdapter(views);
        viewPager.setAdapter(pagerAdapter);
    }
    private class MyPageAdapter extends PagerAdapter {
        private ArrayList<View> views;
        public MyPageAdapter(ArrayList<View> views){
            this.views = views;
        }

        public int getCount() {
            return views.size();
        }

        public Object instantiateItem(View arg0, final int arg1) {
            ((ViewPager) arg0).addView(views.get(arg1));

            return views.get(arg1);
        }

        public void destroyItem(View arg0, int arg1, Object arg2) {
            ((ViewPager) arg0).removeView((View) arg2);
        }

        public boolean isViewFromObject(View arg0, Object arg1) {
            return arg0 == arg1;
        }

    }
}
