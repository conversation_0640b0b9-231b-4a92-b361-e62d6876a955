package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.DONT_SHOW_TIPS_TAG;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SnackBarUtil;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observable;
import rx.Subscription;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

/**
 * 璀璨模式模块
 *
 * <AUTHOR>
 * @date 2015-2015年7月10日
 */
public class BrightModeActivity extends BaseActivity implements SubscriberListener {

    @BindView(R.id.bright_mode_lv)
    ListView mBrightModeLv;

    @BindView(R.id.bright_banner_lv)
    LottieAnimationView mBannerLv;
    private BrightModeAdapter mAdapter;

    @BindView(R.id.bottom_switch_layout)
    ViewGroup switchLayout;

    @BindView(R.id.flash_switch_title_tv)
    TextView switchTv;

    @BindView(R.id.flash_switch_cb)
    TextView switchCb;

    /**
     * 表示当前灯光模式的状态, 0 表示从未开启, 1~9 表示9中模式正在开启中, -1 ~ -9 表示当前关闭, 上次开启的为某个模式
     */
    private int nowColorIndex = 0;

    Subscription closeFlashSb;

    private int reCloseFlashCount;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bright_mode);
        ButterKnife.bind(this);

        InjectUtils.injectOnlyEvent(this);

        nowColorIndex = PreferencesUtils.getInt(this, COLOR_VALUE, -1);

        CommonUtils.setStateBar(this, true);

        initData();

        PreferencesUtils.put(this, "enter_flash", true);

        mBannerLv.setImageAssetsFolder("flash_set_image/");
        mBannerLv.setAnimation("flash_set.json");

        switchCb.setSelected(CommonUtils.jewelryFlashOpen(this));
        switchTv.setText(CommonUtils.jewelryFlashOpen(this) ? R.string.turn_on_flash : R.string.turn_off_flash);
        switchLayout.setOnClickListener(v ->
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_FLASH_CHANGE, null));
    }

    @Override
    protected void initTopBar() {
        setTopbarBackground(R.color.transparent);
        setTopBackIcon(R.drawable.back_icon_write);
        getTopTitleView().setTextColor(getResources().getColor(R.color.text_color_white));
        setTopTitle(getString(R.string.homoe_bright_mode));
        super.initTopBar();
    }

    @Override
    protected void onResume() {
        super.onResume();

        BluetoothManage.getInstance().connectedStatus();
    }

    private void initData() {
        mAdapter = new BrightModeAdapter();
        mBrightModeLv.setAdapter(mAdapter);

        mBrightModeLv.setOnItemClickListener((parent, view, position, id) -> {

            if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                Toast.makeText(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT).show();
                return;
            }

            boolean isOn = CommonUtils.jewelryFlashOpen(this);
            if (!isOn) {
                ToastUtils.showLong(this, R.string.flash_change_with_off);
                return;
            }

            // 开启某个灯光模式
            nowColorIndex = position + 1;
            mAdapter.notifyDataSetChanged();

            final BrightModeListDataBean brightModeListDataBean = BrightModeListDataBean.dataBeans.get(position);
//                mBannerImageView.setImageResource(brightModeListDataBean.getBannerResId());
            //根据选择颜色开启灯光
            BluetoothManage.getInstance().changeBirghtMode(NotifyUtil.getColorValue(brightModeListDataBean.getFlashColorValue()));
            PreferencesUtils.put(BrightModeActivity.this, COLOR_VALUE, nowColorIndex);
            //如果首页是关闭那么只闪3秒
            if (!isOn) {
                //发生到TotwooBrightHolder
                PreferencesUtils.put(BrightModeActivity.this, COLOR_VALUE, -nowColorIndex);
                if (closeFlashSb != null) {
                    closeFlashSb.unsubscribe();
                }
                reCloseFlashCount = 0;

                closeFlashSb = Observable.timer(7500, TimeUnit.MILLISECONDS, Schedulers.newThread())
                        .observeOn(Schedulers.newThread())
                        .subscribe(new Action1<Long>() {
                            @Override
                            public void call(Long aLong) {
                                if (PreferencesUtils.getInt(BrightModeActivity.this, COLOR_VALUE, -1) <= 0) {
                                    closeFlash();
                                }
                            }
                        }, new Action1<Throwable>() {
                            @Override
                            public void call(Throwable throwable) {
                                // RxJava错误处理回调
                                LogUtils.e("BrightModeActivity", "Timer error: " + throwable.getMessage());
                            }
                        });
            }

//                // 0代表多彩
//                if (brightModeListDataBean.getBannerResId() == 0) {
//                    mBannerBgView.setImageResource(R.drawable.bright_mode_white_banner);
//                    mBannerImageView.setImageResource(BrightModeListDataBean.dataBeans.get(1).getBannerResId());
//                    //为了保持时间同步 重新开启动画
//                    Animation animation = AnimationUtils.loadAnimation(BrightModeActivity.this, R.anim.bright_mode_banner_anim);
//                    animation.setAnimationListener(new Animation.AnimationListener() {
//
//                        @Override
//                        public void onAnimationStart(Animation animation) {
//                            animRepeatCount = 0;
//                            index = 0;
//                        }
//
//                        @Override
//                        public void onAnimationEnd(Animation animation) {
//                        }
//
//                        @Override
//                        public void onAnimationRepeat(Animation animation) {
//                            animRepeatCount++;
//                            //对2取余等于0 说明完成了一次闪烁 要换颜色了
//                            if (animRepeatCount % 2 == 0) {
//                                mBannerBgView.setImageResource(BrightModeListDataBean.dataBeans.get((index % 8) + 1).getBannerResId());
//                                index++;
//                                mBannerImageView.setImageResource(BrightModeListDataBean.dataBeans.get((index % 8) + 1).getBannerResId());
//                            }
//                        }
//                    });
//                    mBannerImageView.startAnimation(animation);
//
//                } else {
//                    startBannerGradient();
//                }
        });
    }

    public void closeFlash() {
        BluetoothManage.getInstance().changeBirghtMode(-1);
    }


//    private void startBannerGradient() {
//        mBannerBgView.setImageResource(R.drawable.bright_mode_title_banner_bg);
//        Animation animation = AnimationUtils.loadAnimation(this, R.anim.bright_mode_banner_anim);
//        mBannerImageView.setVisibility(View.VISIBLE);
//        mBannerImageView.startAnimation(animation);
//    }

    @EventInject(eventType = S.E.E_FLASH_CHANGED, runThread = TaskType.UI)
    public void onFlashReceiver(EventData data) {
        boolean open = CommonUtils.jewelryFlashOpen(this);

        switchCb.setSelected(open);
        switchTv.setText(open ? R.string.turn_on_flash : R.string.turn_off_flash);

        if (open && !PreferencesUtils.getBoolean(this, DONT_SHOW_TIPS_TAG, false)) {
            SnackBarUtil.showLong(mBrightModeLv, R.string.bright_open_during, R.string.no_longer_tips_notify_guide,
                    v -> PreferencesUtils.put(this, DONT_SHOW_TIPS_TAG, true));
        }
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {
    }

    public class BrightModeAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            return BrightModeListDataBean.dataBeans.size();
        }

        @Override
        public Object getItem(int position) {
            return BrightModeListDataBean.dataBeans.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            RelativeLayout rl;
            ViewHolder holder;
            if (convertView == null) {
                rl = (RelativeLayout) View.inflate(BrightModeActivity.this, R.layout.bright_mode_color_item, null);
                holder = new ViewHolder(rl);
                rl.setTag(holder);
            } else {
                rl = (RelativeLayout) convertView;
                holder = (ViewHolder) rl.getTag();
            }
            BrightModeListDataBean bean = BrightModeListDataBean.dataBeans.get(position);
            holder.mBrightModeColorInfo.setText(bean.getColorInfoResId());
            holder.mBrightModeColorName.setText(bean.getColorNameResId());
            holder.mFlashItemIconIv.setImageResource(bean.getColorDrawableResId());
            holder.mBirghtBg.setBackgroundResource(bean.backgroundId);

            holder.mBrightModeSelectIv.setVisibility(position + 1 == Math.abs(nowColorIndex) ? View.VISIBLE : View.GONE);
            return rl;
        }

        public class ViewHolder {
            @BindView(R.id.flash_item_icon_iv)
            ImageView mFlashItemIconIv;
            @BindView(R.id.bright_mode_color_name)
            TextView mBrightModeColorName;
            @BindView(R.id.bright_mode_color_info)
            TextView mBrightModeColorInfo;
            @BindView(R.id.bright_mode_select_iv)
            ImageView mBrightModeSelectIv;
            @BindView(R.id.flash_item_bg)
            View mBirghtBg;

            ViewHolder(View view) {
                ButterKnife.bind(this, view);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing()) {
            if (closeFlashSb != null && !closeFlashSb.isUnsubscribed()) {
                closeFlashSb.unsubscribe();
            }
            if (PreferencesUtils.getInt(BrightModeActivity.this, COLOR_VALUE, -1) <= 0) {
                closeFlash();
            }
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    public static class BrightModeListDataBean {

        public static List<BrightModeListDataBean> dataBeans;

        static {
            dataBeans = new ArrayList<>();
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_colorful, R.string.bright_mode_colorful, R.string.app_name, 0, "COLORFUL", R.drawable.bright_00_color));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_pink, R.string.bright_mode_pink, R.string.app_name, R.drawable.bright_mode_pink_banner, "PINK", R.drawable.bright_01_pink));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_red, R.string.bright_mode_red, R.string.app_name, R.drawable.bright_mode_red_banner, "RED", R.drawable.bright_02_red));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_orange, R.string.bright_mode_orange, R.string.app_name, R.drawable.bright_mode_orange_banner, "ORANGE", R.drawable.bright_03_orange));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_yellow, R.string.bright_mode_yellow, R.string.app_name, R.drawable.bright_mode_yellow_banner, "YELLOW", R.drawable.bright_04_yellow));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_green, R.string.bright_mode_green, R.string.app_name, R.drawable.bright_mode_green_banner, "GREEN", R.drawable.bright_05_green));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_cyan, R.string.bright_mode_cyan, R.string.app_name, R.drawable.bright_mode_cyan_banner, "CYAN", R.drawable.bright_06_cyan));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_blue, R.string.bright_mode_blue, R.string.app_name, R.drawable.bright_mode_blue_banner, "BLUE", R.drawable.bright_07_blue));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_purple, R.string.bright_mode_purple, R.string.app_name, R.drawable.bright_mode_purple_banner, "PURPLE", R.drawable.bright_08_purple));
            dataBeans.add(new BrightModeListDataBean(R.drawable.notify_ray_white, R.string.bright_mode_white, R.string.app_name, R.drawable.bright_mode_white_banner, "WHITE", R.drawable.bright_09_white));
        }

        private int colorDrawableResId;

        private int colorNameResId;

        private int colorInfoResId;

        private int bannerResId;

        public int backgroundId;

        public String getFlashColorValue() {
            return flashColorValue;
        }

        public void setFlashColorValue(String flashColorValue) {
            this.flashColorValue = flashColorValue;
        }

        private String flashColorValue;

        public BrightModeListDataBean(int colorDrawableResId, int colorNameResId, int colorInfoResId, int bannerResId, String flashColorValue, int backgroundId) {
            this.colorDrawableResId = colorDrawableResId;
            this.colorNameResId = colorNameResId;
            this.colorInfoResId = colorInfoResId;
            this.bannerResId = bannerResId;
            this.flashColorValue = flashColorValue;
            this.backgroundId = backgroundId;
        }

        public int getBannerResId() {
            return bannerResId;
        }

        public void setBannerResId(int bannerResId) {
            this.bannerResId = bannerResId;
        }

        public int getColorDrawableResId() {
            return colorDrawableResId;
        }

        public void setColorDrawableResId(int colorDrawableResId) {
            this.colorDrawableResId = colorDrawableResId;
        }

        public int getColorNameResId() {
            return colorNameResId;
        }

        public void setColorNameResId(int colorNameResId) {
            this.colorNameResId = colorNameResId;
        }

        public int getColorInfoResId() {
            return colorInfoResId;
        }

        public void setColorInfoResId(int colorInfoResId) {
            this.colorInfoResId = colorInfoResId;
        }
    }

//		isBrightModeOn = PreferencesUtils.getBoolean(this, ISBRIGHTMODEON_KEY,
//				false);
//
////		if (!Apputils.systemLanguageIsChinese(this)) {
////			findViewById(R.id.bright_title_line).getLayoutParams().width = Apputils
////					.dp2px(this, 164);
////		}
//
//		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
//			findViewById(R.id.bright_head_layout).setPadding(
//					0,
//					getResources().getDimensionPixelSize(
//							R.dimen.bright_title_margin_top)
//							+ Apputils.getStatusHeight(this), 0, 0);
//		}
//
//		// 设置按钮初始状态, 模式关闭情况下, 显示可操作的开
//		switchBtn.setChecked(!isBrightModeOn);
//		switchBtn.setText(getBtnText(!isBrightModeOn));
//
//		switchBtn.setOnClickListener(new OnClickListener() {
//			@Override
//			public void onClick(View v) {
//				boolean isOK;
//				if (isBrightModeOn) {
//					// 关闭操作
//					isOK = JewController.changeBirghtMode(
//							BrightModeActivity.this, false);
//				} else {
//					// 打开璀璨模式
//					isOK = JewController.changeBirghtMode(
//							BrightModeActivity.this, true);
//				}
//				// 如果当前无配对设备, 直接提示, 不再做任何逻辑
//				if (!isOK) {
//					ToastUtils.showLong(BrightModeActivity.this,
//							R.string.error_jewelry_connect);
//					switchBtn.setChecked(!isBrightModeOn);
//					return;
//				}
//				//发送到HomeBrightHolder
//				EventBus.getDefault().post(new BrightSwitch(!isBrightModeOn));
//
//				switchBtn.setChecked(!isBrightModeOn);
//				switchBtn.setClickable(false);
//				mRunable = new Runnable() {
//					@Override
//					public void run() {
//						switchBtn.setClickable(true);
//						isBrightModeOn = !isBrightModeOn;
//						PreferencesUtils.put(BrightModeActivity.this,
//								ISBRIGHTMODEON_KEY, isBrightModeOn);
//						switchBtn.setText(getBtnText(!isBrightModeOn));
//						switchBtn.setChecked(!isBrightModeOn);
//					}
//				};
//				mHandler.postDelayed(mRunable, 200);
//			}
//		});
//		registerReceiver(mFailedReceiver, new IntentFilter(
//				BleWrapper.ACTION_BLE_CONTROL_FAILED));
//	}
//
//	@Override
//	protected void onDestroy() {
//		super.onDestroy();
//		if (bright_head_layout != null)
//			bright_head_layout.destroyDrawingCache();
//		unregisterReceiver(mFailedReceiver);
//	}
//
//	/**
//	 * 获取当前需要显示的文字
//	 *
//	 * @param checked
//	 * @return
//	 */
//	private int getBtnText(boolean checked) {
//		if (checked) {
//			return R.string.on;
//		} else {
//			return R.string.off;
//		}
//	}
//
//	@Override
//	protected void initTopBar() {
//		setTopBackIcon(R.drawable.back_icon_write);
//	}
//
//	private BroadcastReceiver mFailedReceiver = new BroadcastReceiver() {
//		@Override
//		public void onReceive(Context context, Intent intent) {
//			if (intent.getAction().equals(BleWrapper.ACTION_BLE_CONTROL_FAILED)) {
//				if (mRunable != null) {
//					mHandler.removeCallbacks(mRunable);
//					switchBtn.setClickable(true);
//					mRunable = null;
//
//					ToastUtils.showLong(BrightModeActivity.this,
//							R.string.error_jewelry_connect);
//				}
//			}
//		}
//	};
}
