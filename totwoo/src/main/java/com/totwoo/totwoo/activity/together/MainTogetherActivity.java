package com.totwoo.totwoo.activity.together;

import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.activity.LoveSpacePinkActivity.FOOT_PRINT_DATA;
import static com.totwoo.totwoo.activity.LoveSpacePinkActivity.PAIRED_HEAD_URL;
import static com.totwoo.totwoo.activity.LoveSpacePinkActivity.PAIRED_NAMES;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.google.gson.Gson;
import com.huawei.hms.maps.CameraUpdate;
import com.huawei.hms.maps.CameraUpdateFactory;
import com.huawei.hms.maps.HuaweiMap;
import com.huawei.hms.maps.MapView;
import com.huawei.hms.maps.OnMapReadyCallback;
import com.huawei.hms.maps.model.BitmapDescriptorFactory;
import com.huawei.hms.maps.model.LatLng;
import com.huawei.hms.maps.model.LatLngBounds;
import com.huawei.hms.maps.model.Marker;
import com.huawei.hms.maps.model.MarkerOptions;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.FootPrintHttpBean;
import com.totwoo.totwoo.bean.TogetherBean;
import com.totwoo.totwoo.bean.TogetherSelectBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.utils.location.MyMapLocationClient;
import com.totwoo.totwoo.utils.location.MyMapLocationClientOption;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.ShareFootDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;

public class MainTogetherActivity extends BaseActivity implements OnMapReadyCallback {
    private static final String TAG = "MainTogetherActivity";
    @BindView(R.id.together_map)
    MapView mapView;
    @BindView(R.id.together_city_add)
    ImageView mAddView;

    @BindView(R.id.topBar)
    View topBar;
    @BindView(R.id.together_me)
    ImageView selfIv;
    @BindView(R.id.together_other)
    ImageView otherIv;
    @BindView(R.id.together_nation_count)
    TextView mNationCount;
    @BindView(R.id.together_province_count)
    TextView mProvinceCount;
    @BindView(R.id.together_city_count)
    TextView mCityCount;
    @BindView(R.id.together_beyond_tv)
    TextView mBeyondTv;
    private HuaweiMap hMap;
    public final static String TOGETHER_INFO = "together_info";
    private String info_str;
    private Gson gson;
    private ArrayList<TogetherSelectBean> selectBeans;
    private ACache aCache;

    private boolean mapRendered = false;
    private static final String MAPVIEW_BUNDLE_KEY = "MapViewBundleKey";
    private TogetherBean togetherBean;
    private String pairedHeadUrl;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main_footprint);
        ButterKnife.bind(this);
        gson = new Gson();
        aCache = ACache.get(MainTogetherActivity.this);

        findViewById(R.id.together_back_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        Bundle mapViewBundle = null;
        if (savedInstanceState != null) {
            mapViewBundle = savedInstanceState.getBundle("MapViewBundleKey");
        }
        mapView.onCreate(mapViewBundle);
        mapView.getMapAsync(this);


        getSelectedCity();

        pairedHeadUrl = getIntent().getStringExtra(PAIRED_HEAD_URL);
        int partner_gender = TextUtils.isEmpty(aCache.getAsString(CommonArgs.PARTNER_GENDER)) ? (1 - owner.getGender()) : Integer.valueOf(aCache.getAsString(CommonArgs.PARTNER_GENDER));
        BitmapHelper.setHead(ToTwooApplication.baseContext, selfIv, ToTwooApplication.owner.getHeaderUrl(), owner.getGender());
        BitmapHelper.setHead(ToTwooApplication.baseContext, otherIv, pairedHeadUrl, partner_gender);


        if (Apputils.systemLanguageIsChinese(this)) {
            PermissionUtil.hasLocationPermission(this);
            if (PermissionUtil.isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
                getMyLocation();
            }
        }

        EdgeToEdgeUtils.setupTopInsets(topBar);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (PermissionUtil.isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
            getMyLocation();
        }
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }


    @SuppressLint("StringFormatInvalid")
    private void setInfo(TogetherBean togetherBean) {
        mNationCount.setText(String.valueOf(togetherBean.getCountry_total()));
        mProvinceCount.setText(String.valueOf(togetherBean.getProvince_total()));
        mCityCount.setText(String.valueOf(togetherBean.getCity_total()));
        mBeyondTv.setText(setStyle(getString(R.string.foot_print_beyond, togetherBean.getPercentage()), togetherBean.getPercentage()));
        selectBeans = (ArrayList<TogetherSelectBean>) togetherBean.getList();
        setPoint(togetherBean.getCountry_total());
    }

    @OnClick({ R.id.together_city_add, R.id.together_share_tv, R.id.together_local_iv, R.id.together_me, R.id.together_other,
            R.id.together_nation_tv, R.id.together_nation_count, R.id.together_province_tv, R.id.together_province_count, R.id.together_city_tv,
            R.id.together_city_count, R.id.together_info_cv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.together_city_add:
                startActivityForResult(new Intent(MainTogetherActivity.this, FootPrintSelectActivity.class)
                        .putExtra(TOGETHER_INFO, info_str), 1);
                break;
            case R.id.together_share_tv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PRINT_SHARE);
//                startActivity(new Intent(MainTogetherActivity.this, FootPrintShareActivity.class)
//                        .putExtra(PAIRED_HEAD_URL, getIntent().getStringExtra(PAIRED_HEAD_URL))
//                        .putExtra(PAIRED_NAMES, getIntent().getStringExtra(PAIRED_NAMES))
//                        .putExtra(TOGETHER_INFO, info_str));

                showShareDialog();
                break;
            case R.id.together_local_iv:
//                if (myLocationLatLng != null) {
//                    //可以手动设置回自己的坐标位置
//                    aMap.setMapStatus(MapStatusUpdateFactory.buildUpdateByCenter(myLocationLatLng));
//                }
                break;
            case R.id.together_me:
            case R.id.together_other:
            case R.id.together_nation_tv:
            case R.id.together_nation_count:
            case R.id.together_province_tv:
            case R.id.together_province_count:
            case R.id.together_city_tv:
            case R.id.together_city_count:
                showCitiesDialog();
                break;
            case R.id.together_info_cv:
                break;
        }
    }


    private void getSelectedCity() {
        HttpHelper.footPrintService.getSelectedCity(ToTwooApplication.otherPhone, ToTwooApplication.owner.getPairedId())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<TogetherBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<TogetherBean> togetherBeanHttpBaseBean) {
                        if (togetherBeanHttpBaseBean.getErrorCode() == 0) {
                            togetherBean = togetherBeanHttpBaseBean.getData();
                            info_str = gson.toJson(togetherBeanHttpBaseBean.getData());
                            aCache.put(FOOT_PRINT_DATA, info_str);
                            setInfo(togetherBeanHttpBaseBean.getData());
                        }
                    }
                });
    }

    private ArrayList<Marker> markers = new ArrayList<>();

    private void setPoint(int countryCont) {
        if (!markers.isEmpty()) {
            for (Marker marker : markers) {
                marker.remove();
            }
            markers.clear();
        }

        if (selectBeans != null && !selectBeans.isEmpty()) {
            List<LatLng> latLngs = new ArrayList<>();
            for (TogetherSelectBean togetherSelectBean : selectBeans) {
                if (!TextUtils.isEmpty(togetherSelectBean.getLocation())) {
                    String[] strings = togetherSelectBean.getLocation().split(",");
                    LatLng latLng = new LatLng(Double.parseDouble(strings[1]), Double.parseDouble(strings[0]));
                    latLngs.add(latLng);

                    MarkerOptions options = new MarkerOptions()
                            .position(latLng)
                            .icon(BitmapDescriptorFactory.fromResource(R.drawable.location_pink))
                            .draggable(false);

                    Marker marker = hMap.addMarker(options);
                    markers.add(marker);
                }
            }

            // 创建LatLngBounds.Builder并包含所有点
            adjust(latLngs);
        }
    }

    private void adjust(List<LatLng> latLngs) {
        if (latLngs == null || latLngs.isEmpty()) {
            return;
        }

        mapView.setPadding(0, 0, 0, SizeUtils.dp2px(225));

        LatLngBounds.Builder builder = new LatLngBounds.Builder();
        for (LatLng latLng : latLngs) {
            builder.include(latLng);
        }
        LatLngBounds bounds = builder.build();
        // 定义地图的边界填充（以像素为单位）
        int padding = 100; // 根据需要调整
        // 创建相机更新
        CameraUpdate cameraUpdate = CameraUpdateFactory.newLatLngBounds(bounds, padding);
        // 设置相机视角
        hMap.moveCamera(cameraUpdate);
    }

    @Override
    public void onResume() {
        super.onResume();
        //在activity执行onResume时执行mMapView.onResume()，重新绘制加载地图
        mapView.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        //在activity执行onPause时执行mMapView.onPause()，暂停地图的绘制
        mapView.onPause();
    }

    @Override
    protected void onStop() {
        super.onStop();
        mapView.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mapView.onDestroy();
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);

        Bundle mapViewBundle = outState.getBundle(MAPVIEW_BUNDLE_KEY);
        if (mapViewBundle == null) {
            mapViewBundle = new Bundle();
            outState.putBundle(MAPVIEW_BUNDLE_KEY, mapViewBundle);
        }

        mapView.onSaveInstanceState(mapViewBundle);
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        mapView.onLowMemory();
    }

    private SpannableString setStyle(String string, String inside) {
        int index = string.indexOf(inside);
        int endIndex = index + inside.length();
        SpannableString spannableString = new SpannableString(string);
//        spannableString.setSpan(new AbsoluteSizeSpan(17, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_main)), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1 && resultCode == 1) {
            getSelectedCity();
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
        mapView.onStart();
    }

    private void showCitiesDialog() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PRINT_CITIES_CHECK);
        if (selectBeans == null || selectBeans.size() == 0) {
            return;
        }
        CustomBottomDialog citiesDialog = new CustomBottomDialog(this);
        citiesDialog.setTitle("你们一起去过的地方");
        LinearLayout marriageLayout = (LinearLayout) View.inflate(this, R.layout.dialog_inside_cities, null);
        RecyclerView mCitiesRv = marriageLayout.findViewById(R.id.show_cities_rv);
        TogetherCityAdapter togetherCityAdapter = new TogetherCityAdapter(selectBeans);
        mCitiesRv.setLayoutManager(new GridLayoutManager(MainTogetherActivity.this, 3));
        mCitiesRv.setAdapter(togetherCityAdapter);
        citiesDialog.setMainView(marriageLayout);
        citiesDialog.setTopBgRes(R.drawable.dialog_citites_top_bg);
        citiesDialog.setCancelRes(R.drawable.black_cancel_72);
        citiesDialog.show();
    }


    private void showShareDialog() {
        ShareFootDialog shareFootDialog  = new ShareFootDialog(this, togetherBean,getIntent().getStringExtra(PAIRED_NAMES),pairedHeadUrl, () -> {

        });
        shareFootDialog.show();

    }

    @Override
    public void onMapReady(HuaweiMap huaweiMap) {
        hMap = huaweiMap;
        setMapView();
    }

    private void setMapView() {
        hMap.setMapType(HuaweiMap.MAP_TYPE_NORMAL);


        info_str = aCache.getAsString(FOOT_PRINT_DATA);
        togetherBean = gson.fromJson(info_str, TogetherBean.class);
        if (togetherBean != null) {
            setInfo(togetherBean);
        }
    }


    //声明mlocationClient对象
    public MyMapLocationClient mLocationClient;
    //声明mLocationOption对象
    public MyMapLocationClientOption mLocationOption = null;

    private void getMyLocation() {
        if (mLocationClient == null) {
            mLocationClient = MyMapLocationClient.newLocationClient(this);
            //初始化定位参数
            mLocationOption = new MyMapLocationClientOption();
            //设置定位监听
            mLocationClient.setLocationListener(aMapLocation -> {
                if (aMapLocation.getErrorCode() == 0 && !TextUtils.isEmpty(aMapLocation.getCity())) {
                    setCity(aMapLocation);
                }
            });
            //设置定位模式为高精度模式，Battery_Saving为低功耗模式，Device_Sensors是仅设备模式
            mLocationOption.setLocationMode(MyMapLocationClientOption.MyMapLocationMode.PRIORITY_HIGH_ACCURACY);
            mLocationOption.setOnceLocation(true);
            //设置定位间隔,单位毫秒,默认为2000ms
//            mLocationOption.setInterval(30000);
            //设置定位参数
            mLocationClient.setLocationOption(mLocationOption);
        }
        mLocationClient.startLocation();
    }


    private void setCity(MyMapLocationClient.LocationResult locationResult) {
        HttpHelper.footPrintService.checkCity(ToTwooApplication.otherPhone, ToTwooApplication.owner.getPairedId(), locationResult.getCity())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<FootPrintHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<FootPrintHttpBean> footPrintHttpBeanHttpBaseBean) {
                        if (footPrintHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            showAddCityDialog(locationResult);
                        }
                    }
                });
    }

    private CommonMiddleDialog cityMiddleDialog;

    @SuppressLint("StringFormatInvalid")
    private void showAddCityDialog(MyMapLocationClient.LocationResult locationResult) {
        String name = locationResult.getCity();
        cityMiddleDialog = new CommonMiddleDialog(this);
        cityMiddleDialog.setMessage(setStringStyle(getString(R.string.foot_print_add, name), name));
        cityMiddleDialog.setSure(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PRINT_ADD_LOCATION_CITY);
            addCity(locationResult);
        });
        cityMiddleDialog.setCancel(R.string.cancel, dialog -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PRINT_CANCEL_LOCATION_CITY);
            cityMiddleDialog.dismiss();
        });
        cityMiddleDialog.show();
    }

    private void addCity(MyMapLocationClient.LocationResult locationResult) {
        HttpHelper.footPrintService.addCity(ToTwooApplication.otherPhone, ToTwooApplication.owner.getPairedId(), locationResult.getCity())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            cityMiddleDialog.dismiss();
                            //添加成功

                            getSelectedCity();
//                            MarkerOptions options = new MarkerOptions()
//                                    .position(new LatLng(locationResult.latitude, locationResult.longitude))
//                                    .icon(BitmapDescriptorFactory.fromResource(R.drawable.location_pink))
//                                    .draggable(false);
//
//                            Marker marker = hMap.addMarker(options);
//                            markers.add(marker);
                        }
                    }
                });
    }

    private SpannableString setStringStyle(String string, String insideStr) {
        SpannableString spannableString = new SpannableString(string);
        int startIndex = string.indexOf(insideStr);
        int endIndex = startIndex + insideStr.length();
        setSpan(spannableString, startIndex, endIndex);
        return spannableString;
    }


    private SpannableString setSpan(SpannableString spannableString, int startIndex, int endIndex) {
        spannableString.setSpan(new AbsoluteSizeSpan(13, true), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_color_black)), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    public class TogetherCityAdapter extends RecyclerView.Adapter<TogetherCityAdapter.ViewHolder> {
        private ArrayList<TogetherSelectBean> mCityBeans;

        public TogetherCityAdapter(List<TogetherSelectBean> togetherSelectBeans) {
            mCityBeans = (ArrayList<TogetherSelectBean>) togetherSelectBeans;
        }

        @NonNull
        @Override
        public TogetherCityAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new TogetherCityAdapter.ViewHolder(LayoutInflater.from(MainTogetherActivity.this).inflate(R.layout.together_city_item, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull TogetherCityAdapter.ViewHolder holder, int position) {
            holder.togetherCityTv.setText(mCityBeans.get(position).getName());
            if (!TextUtils.isEmpty(mCityBeans.get(position).getName()) && mCityBeans.get(position).getName().length() > 5) {
                holder.togetherCityTv.setTextSize(12);
            } else {
                holder.togetherCityTv.setTextSize(14);
            }
            holder.togetherCityTv.setTextColor(getResources().getColor(R.color.white));
            holder.togetherCityTv.setBackground(getResources().getDrawable(R.drawable.shape_trans_pink_18));
        }

        @Override
        public int getItemCount() {
            return mCityBeans == null ? 0 : mCityBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.together_city)
            TextView togetherCityTv;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }
}
