package com.totwoo.totwoo.activity;

import android.Manifest;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.LinearLayout;
import android.widget.LinearLayout.LayoutParams;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.adapter.CitySelectAdapter;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.location.MyMapLocationClient;
import com.totwoo.totwoo.utils.location.MyMapLocationClientOption;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SelectCityActivity extends BaseActivity {

    public static final int COUNTRY = 90;

    public static final int PROVINCE = 91;

    public static final int CITY = 92;

    public static final int POSITION = 93;

    /**
     * 城市选择国家
     */
    public Map<String, Map<String, String>> CITY_SELECT_COUNTRY = new HashMap<String, Map<String, String>>();
    /**
     * 城市选择省份
     */
    public Map<String, Map<String, String>> CITY_SELECT_PROVINCE = new HashMap<String, Map<String, String>>();
    /**
     * 城市选择城市
     */
    public Map<String, Map<String, String>> CITY_SELECT_CITY = new HashMap<>();

    /**
     * 选择国家layout
     */
    @BindView(R.id.select_country_ll)
    LinearLayout select_country_ll;

    /**
     * 定位layout
     */
    @BindView(R.id.gps_country_ll)
    LinearLayout gps_country_ll;

    /**
     * 根据gps定位出来的位置TV
     */
    @BindView(R.id.gps_country_tv)
    TextView gps_country_tv;

    /**
     * 当前显示的列表地区
     */
    @BindView(R.id.country_tv)
    TextView country_tv;

    /**
     * 国家列表
     */
    @BindView(R.id.country_lv)
    ListView country_lv;

    /**
     * 选择省份layout
     */
    @BindView(R.id.select_province_ll)
    LinearLayout select_province_ll;

    /**
     * 位于国家
     */
    @BindView(R.id.province_tv)
    TextView province_tv;

    /**
     * 省份列表
     */
    @BindView(R.id.province_lv)
    ListView province_lv;

    /**
     * 选择城市layout
     */
    @BindView(R.id.select_city_ll)
    LinearLayout select_city_ll;

    /**
     * 位于省份
     */
    @BindView(R.id.city_tv)
    TextView city_tv;

    /**
     * 城市
     */
    @BindView(R.id.city_lv)
    ListView city_lv;

    /**
     * select_country_lv的头view
     */
    @BindView(R.id.select_country_lv_head)
    LinearLayout select_country_lv_head;

    /**
     * select_province_lv的头view
     */
    @BindView(R.id.select_province_haed)
    RelativeLayout select_province_haed;

    /**
     * select_city_lv的头view
     */
    @BindView(R.id.select_city_haed)
    RelativeLayout select_city_haed;

    private CitySelectAdapter countryAdapter;

    private CitySelectAdapter provinceAdapter;

    private CitySelectAdapter cityAdapter;

    // 选择的国家
    private String country;
    // 选择的省份
    private String province;
    // 选择的城市
    private String city;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_select_city);
        ButterKnife.bind(this);
        // requestPosition();
        initCityData();
        LogUtils.e("aab SelectCityActivity.onCreate()");
        setListener();
        PermissionUtil.hasLocationPermission(this);

        if (PermissionUtil.isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
            try {
                getMyLocation();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (PermissionUtil.isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
            try {
                getMyLocation();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }

    //声明mlocationClient对象
    public MyMapLocationClient mLocationClient;
    //声明mLocationOption对象
    public MyMapLocationClientOption mLocationOption = null;

    private void getMyLocation() throws Exception {
        if (mLocationClient == null) {
            mLocationClient = MyMapLocationClient.newLocationClient(this);
            //初始化定位参数
            mLocationOption = new MyMapLocationClientOption();
            //设置定位监听
            mLocationClient.setLocationListener(aMapLocation -> {
                if (aMapLocation.getErrorCode() == 0) {
                    StringBuilder pos = new StringBuilder();
                    if (TextUtils.equals(aMapLocation.getProvince(), aMapLocation.getCity())) {
                        pos.append(aMapLocation.getCountry())
                                .append(" ")
                                .append(aMapLocation.getProvince());
                    } else {
                        pos.append(aMapLocation.getCountry())
                                .append(" ")
                                .append(aMapLocation.getProvince())
                                .append(" ").append(aMapLocation.getCity());
                    }
                    runOnUiThread(() -> gps_country_tv.setText(pos.toString()));
                }
            });
            //设置定位模式为高精度模式，Battery_Saving为低功耗模式，Device_Sensors是仅设备模式
            mLocationOption.setLocationMode(MyMapLocationClientOption.MyMapLocationMode.PRIORITY_HIGH_ACCURACY);
            mLocationOption.setOnceLocation(true);
            //设置定位参数
            mLocationClient.setLocationOption(mLocationOption);
        }
        mLocationClient.startLocation();
    }

    private void setListener() {
        // 国家list监听
        country_lv.setOnItemClickListener((parent, view, position, id) -> {
            if (position == 0) {
                return;
            }
            country = countryAdapter.getItem(position).toString();
            getIntent().putExtra("country", country);
            province_tv.setText(country);
            if (provinceAdapter == null) {
                provinceAdapter = new CitySelectAdapter(
                        SelectCityActivity.this);
                // 添加haedview
                select_province_ll.removeView(select_province_haed);
                province_lv
                        .addHeaderView(select_province_haed, null, false);
                select_province_haed
                        .setLayoutParams(new AbsListView.LayoutParams(
                                LayoutParams.MATCH_PARENT,
                                LayoutParams.WRAP_CONTENT));
                select_province_haed.setPadding(
                        Apputils.dp2px(SelectCityActivity.this, 40),
                        Apputils.dp2px(SelectCityActivity.this, 20), 0,
                        Apputils.dp2px(SelectCityActivity.this, 25));
                select_province_haed.setClickable(false);
                province_lv.setAdapter(provinceAdapter);
            }
            // 根据国家去获取省份列表
            Map<String, String> mapData = CITY_SELECT_PROVINCE
                    .get(country);
            if (mapData != null) {
                provinceAdapter.setMapData(mapData, false);
                select_country_ll.setVisibility(View.GONE);
                select_province_ll.setVisibility(View.VISIBLE);
            } else {
                setResult(COUNTRY, getIntent());
                finish();
            }

        });
        // 省份list监听
        province_lv.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view,
                                    int position, long id) {
                if (position == 0) {
                    return;
                }
                province = provinceAdapter.getItem(position).toString();
                getIntent().putExtra("province", province);
                city_tv.setText(province);
                // 根据国家去获取省份列表
                Map<String, String> mapData = CITY_SELECT_CITY
                        .get(province);
                if (mapData != null) {
                    if (cityAdapter == null) {
                        cityAdapter = new CitySelectAdapter(
                                SelectCityActivity.this);
                        select_city_ll.removeView(select_city_haed);
                        // 添加haedview
                        city_lv.addHeaderView(select_city_haed, null, false);
                        select_city_haed
                                .setLayoutParams(new AbsListView.LayoutParams(
                                        LayoutParams.MATCH_PARENT,
                                        LayoutParams.WRAP_CONTENT));
                        select_city_haed.setPadding(
                                Apputils.dp2px(SelectCityActivity.this, 40),
                                Apputils.dp2px(SelectCityActivity.this, 20), 0,
                                Apputils.dp2px(SelectCityActivity.this, 25));
                        select_city_haed.setClickable(false);
                        city_lv.setAdapter(cityAdapter);
                    }
                    cityAdapter.setMapData(mapData, false);
                    select_province_ll.setVisibility(view.GONE);
                    select_city_ll.setVisibility(view.VISIBLE);
                } else {
                    setResult(PROVINCE, getIntent());
                    finish();
                }
            }
        });
        // 城市list监听
        city_lv.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view,
                                    int position, long id) {
                if (position == 0) {
                    return;
                }
                city = cityAdapter.getItem(position).toString();
                getIntent().putExtra("city", city);
                setResult(CITY, getIntent());
                finish();
            }
        });

        gps_country_ll.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {

                String cityString = gps_country_tv.getText().toString();
                if (getString(R.string.positioning).equals(cityString)
                        || getString(R.string.position_failure).equals(
                        cityString)) {
                    return;
                }
                getIntent().putExtra("position", cityString);
                setResult(POSITION, getIntent());
                finish();
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (select_city_ll.getVisibility() == View.VISIBLE) {
                select_city_ll.setVisibility(View.GONE);
                select_province_ll.setVisibility(View.VISIBLE);
                return true;
            }
            if (select_province_ll.getVisibility() == View.VISIBLE) {
                select_province_ll.setVisibility(View.GONE);
                select_country_ll.setVisibility(View.VISIBLE);
                return true;
            }
            finish();
        }
        return true;
    }

    private void showData() {
        countryAdapter = new CitySelectAdapter(
                SelectCityActivity.this);
        select_country_ll.removeView(select_country_lv_head);

        country_lv.addHeaderView(select_country_lv_head, null,
                false);
        select_country_lv_head
                .setLayoutParams(new AbsListView.LayoutParams(
                        LayoutParams.MATCH_PARENT,
                        LayoutParams.WRAP_CONTENT));
        country_lv.setAdapter(countryAdapter);
        countryAdapter.setMapData(CITY_SELECT_COUNTRY
                .get(getString(R.string.zh_cn_)), true);
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.current_city);
        setTopLeftOnclik(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (select_city_ll.getVisibility() == View.VISIBLE) {
                    select_city_ll.setVisibility(View.GONE);
                    select_province_ll.setVisibility(View.VISIBLE);
                    return;
                }
                if (select_province_ll.getVisibility() == View.VISIBLE) {
                    select_province_ll.setVisibility(View.GONE);
                    select_country_ll.setVisibility(View.VISIBLE);
                    return;
                }
                finish();
            }

        });
        super.initTopBar();
    }


    /**
     * 加载城市数据
     */
    public void initCityData() {
        new Thread() {
            public void run() {
                try {
                    InputStream open = getAssets()
                            .open("city");
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    int i = -1;
                    while ((i = open.read()) != -1) {
                        baos.write(i);
                    }
                    String cityJson = baos.toString();
                    if (null == cityJson) {
                        return;
                    }
                    JSONObject jsonObject = new JSONObject(cityJson);
                    JSONArray jsonArray;
                    // 根据系统语言环境加载城市
                    if (!Apputils.systemLanguageIsChinese(SelectCityActivity.this)) {
                        jsonArray = jsonObject.optJSONArray(getString(R.string.en));
                    } else {
                        jsonArray = jsonObject.optJSONArray(getString(R.string.zh_cn_));
                    }

                    Map<String, String> country = new HashMap<String, String>();
                    Map<String, String> province;
                    Map<String, String> city;
                    // 便利json放到对应的map里
                    for (int j = 0; j < jsonArray.length(); j++) {
                        JSONArray item = jsonArray.getJSONArray(j);
                        switch (item.length()) {
                            case 2:
                                country.put(item.getString(0), item.getString(1));
                                break;
                            case 3:
                                String key = country.get(item.get(0));
                                province = CITY_SELECT_PROVINCE.get(key);
                                if (null == province) {
                                    province = new HashMap<String, String>();
                                }
                                province.put(item.getString(1), item.getString(2));
                                CITY_SELECT_PROVINCE.put(key, province);
                                break;
                            case 4:
                                // 获取省份的key（key是国家名称）
                                String provinceKey = country.get(item.get(0));
                                // 省份map
                                Map<String, String> map = CITY_SELECT_PROVINCE
                                        .get(provinceKey);
                                // 获取省份名称 作为城市的key
                                String cityKey = map.get(item.getString(1));
                                city = CITY_SELECT_CITY.get(cityKey);
                                if (null == city) {
                                    city = new HashMap<String, String>();
                                }
                                city.put(item.getString(2), item.getString(3));
                                CITY_SELECT_CITY.put(cityKey, city);
                                break;
                        }

                    }
                    CITY_SELECT_COUNTRY.put(getString(R.string.zh_cn_),
                            country);

                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            showData();
                        }
                    });

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }.start();

    }

}
