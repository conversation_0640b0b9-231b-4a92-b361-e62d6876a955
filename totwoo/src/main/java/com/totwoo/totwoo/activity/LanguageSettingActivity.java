package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.os.LocaleListCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.databinding.ActivityLanguageSettingBinding;
import com.totwoo.totwoo.databinding.LaunageSwitchItemLayoutBinding;
import com.totwoo.totwoo.receiver.JpushReceiver;
import com.totwoo.totwoo.utils.BindingViewHolder;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;

import org.xmlpull.v1.XmlPullParser;

import java.util.ArrayList;
import java.util.Objects;

import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class LanguageSettingActivity extends BaseActivity {
    private static final String TAG = "LanguageSettingActivity";
    public ArrayList<SimpleLocale> supportLanguages = new ArrayList<>();

    public static final String EXTRA_LANGUAGE_CHANGE = "LANGUAGE_CHANGE";

    private ActivityLanguageSettingBinding binding;
    /**
     * 当前选择的语言序号
     */
    private int selectPosition;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityLanguageSettingBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());

        loadSupportLanguages();

        LocaleListCompat locales = AppCompatDelegate.getApplicationLocales();

        if (!locales.isEmpty() && locales.get(0) != null) {
            String language = Objects.requireNonNull(locales.get(0)).getLanguage();
            for (int i = 0; i < supportLanguages.size(); i++) {
                if (supportLanguages.get(i).getLanguage().equals(language)) {
                    selectPosition = i;
                    break;
                }
            }
            LogUtils.i(TAG, "Current language: " + language + ", selectPosition: " + selectPosition);
        }

        binding.languageSettingListView.setLayoutManager(new LinearLayoutManager(this));
        binding.languageSettingListView.setAdapter(new RecyclerView.Adapter<BindingViewHolder<LaunageSwitchItemLayoutBinding>>() {
            @NonNull
            @Override
            public BindingViewHolder<LaunageSwitchItemLayoutBinding> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                return new BindingViewHolder<>(LaunageSwitchItemLayoutBinding.inflate(getLayoutInflater()));
            }

            @Override
            public void onBindViewHolder(@NonNull BindingViewHolder<LaunageSwitchItemLayoutBinding> holder, int position) {
                holder.binding.languageItemTitle.setText(supportLanguages.get(position).getName());
                holder.binding.languageItemSelectIcon.setVisibility(position == selectPosition ? View.VISIBLE : View.GONE);
                holder.itemView.setOnClickListener(v -> {
                    notifyItemChanged(selectPosition);
                    selectPosition = holder.getAbsoluteAdapterPosition();
                    notifyItemChanged(selectPosition);
                });
            }

            @Override
            public int getItemCount() {
                return supportLanguages.size();
            }
        });
        binding.languageSettingChangeButton.setOnClickListener(v -> changeLanguage(selectPosition));
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopTitle(R.string.multi_language);
        setTopBackIcon(R.drawable.back_icon_black);
    }

    private void changeLanguage(int position) {
        String localList;
        if (position == 0) {
            localList = "";
        }else if ("en".equals(supportLanguages.get(position).language)) {
            localList = "en";
        }else {
            localList = supportLanguages.get(position).getLanguage() + ",en";
        }
        setResult(RESULT_OK, new Intent().putExtra(EXTRA_LANGUAGE_CHANGE, localList));
//        // 上传语言类型
        String res = NetUtils.checkNetworkType(ToTwooApplication.baseContext);
        String ver = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "V");
        String name = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");

        if (ver != null && ver.length() > 1)
            ver = ver.substring(1);
        else
            ver = "";

        String countryCode = PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? "86" : "1");
        HttpHelper.update.updateRegister( PreferencesUtils.getString(this, JpushReceiver.REGISTER_ID, ""), JewInfoSingleton.STATE_CONNECTED, Build.MODEL,
                        TextUtils.isEmpty(res) ? "" : res.toUpperCase(), ver, name, countryCode, CommonUtils.getSystemVersion())
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean httpBaseBean) {

                    }
                });
        finish();
    }

    private void loadSupportLanguages() {
        // 添加默认自动值
        supportLanguages = new ArrayList<>();
        supportLanguages.add(new SimpleLocale("auto", getString(R.string.language_auto)));

        try {
            XmlPullParser xpp = getResources().getXml(R.xml.locales_config);
            while (xpp.getEventType() != XmlPullParser.END_DOCUMENT) {
                if (xpp.getEventType() == XmlPullParser.START_TAG) {
                    if (xpp.getName().equals("locale")) {
                        supportLanguages.add(new SimpleLocale(xpp.getAttributeValue(0), xpp.getAttributeValue(1)));
                    }
                }
                xpp.next();
            }
        } catch (Exception e) {
            LogUtils.e("Load language error. ", e);
        }
    }

    private static class SimpleLocale {
        private String language;
        private String name;

        public SimpleLocale(String language, String name) {
            this.language = language;
            this.name = name;
        }

        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
