package com.totwoo.totwoo.activity.memory;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.scaleVideoView.ScalableType;
import com.totwoo.totwoo.widget.scaleVideoView.ScalableVideoView;

import java.text.SimpleDateFormat;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/8.
 */

public class MemoryVedioShowActivity extends BaseActivity implements SubscriberListener, View.OnClickListener
{
    @BindView(R.id.make_card_audio_play_btn)
    ImageView mAudioVideoPlayBtn;

    @BindView(R.id.make_card_video_view)
    ScalableVideoView mScalableVideoView;

    @BindView(R.id.make_card_top_conver_layer)
    ImageView mMakeCardTopCoverIv;

    @BindView(R.id.memory_photo_show_tv)
    public TextView tv;

    private MemoryBean mb;
    private CustomDialog dialog;

    private Dialog dialog1;

    private boolean isShowTitle = true;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_vedio_show1);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        initData();
        initTopBar2();
        initCover();
        setContent();
        mHandler.postDelayed(new Runnable()
        {
            @Override
            public void run()
            {
                new Thread(new Runnable() {
                    @Override
                    public void run()
                    {
                        initVedioView();
                    }
                }).start();
            }
        }, 200);
    }

    private void initCover()
    {
        mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
        BitmapHelper.display(this, mMakeCardTopCoverIv, mb.cover_url);
    }

    private void initVedioView()
    {
        try
        {
            mScalableVideoView.setDataSource(mb.vedio_url);
            mScalableVideoView.setScalableType(ScalableType.CENTER_CROP);
            mScalableVideoView.prepare();
            mScalableVideoView.setOnClickListener(this);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        mAudioVideoPlayBtn.setOnClickListener(v -> {
            mScalableVideoView.start();
            mScalableVideoView.setVisibility(View.VISIBLE);
            mMakeCardTopCoverIv.setVisibility(View.GONE);
            mAudioVideoPlayBtn.setVisibility(View.GONE);
            showOrHideTitle();
            mScalableVideoView.setOnCompletionListener(mp -> {
                mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                isShowTitle = false;
                showOrHideTitle();
            });
        });
    }

    private void setContent()
    {
        tv.setVisibility(isShowTitle ? View.VISIBLE : View.GONE);
        tv.setText(mb.content);
    }

    private void initData()
    {
        Intent i = this.getIntent();
        mb = (MemoryBean) i.getSerializableExtra(S.M.M_IMAGES);
    }

    private void initTopBar2()
    {
        setTopBackIcon(R.drawable.back_icon_white);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_white_important));
        setTopRightIcon(R.drawable.memory_photo_show_delete);
        setTopRightOnClick(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                getPhotoDialog();
            }
        });
    }

    private void showOrHideTitle()
    {
        isShowTitle = !isShowTitle;
        getTopBar().setVisibility(isShowTitle ? View.VISIBLE : View.GONE);
        tv.setVisibility(isShowTitle ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        if (dialog1 != null)
            dialog1.dismiss();
    }

    public void getPhotoDialog()
    {
        dialog = new CustomDialog(this);
        dialog.setTitle("");
        dialog.setMessage(R.string.memory_delete);
        dialog.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                MemoryController.getInstance().delete(mb);
            }
        });
        dialog.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void onDeleteMemorySuccessed(EventData data)
    {
        ToastUtils.showShort(this, R.string.delete_success);
        if (dialog1 != null)
            dialog1.dismiss();
        this.finish();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_FAILED, runThread = TaskType.UI)
    public void onDeleteMemoryFailed(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        String msg = hv.errorMesg;
        ToastUtils.showShort(this, msg);
        if (dialog1 != null)
            dialog1.dismiss();
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
    }

    @Override
    protected void onPause()
    {
        super.onPause();

        try
        {
            if (mScalableVideoView != null && mScalableVideoView.isPlaying())
            {
                mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                mScalableVideoView.pause();
            }
        }
        catch (IllegalStateException e)
        {
        }
        catch (Exception e)
        {
        }
    }

    @Override
    public void onClick(View v)
    {
        if (mScalableVideoView.isPlaying())
        {
            mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
            mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
            mScalableVideoView.pause();
            isShowTitle = false;
            showOrHideTitle();
        }
    }
}
