package com.totwoo.totwoo.activity;

import android.os.Bundle;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.HttpHelper;

import rx.Observer;

public class UploadWatchInfoActivity extends BaseActivity {
    public static final int PRE_GET_STATUS = 0;
    public static final int GET_SELF_STATUS = 1;
    public static final int UPLOAD_STATUS = 2;
    public static final int GET_PAIRED_STATUS = 3;
    public static final int PAIRED_SUCCESS_STATUS = 4;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_upload_watch_info);
    }

    private void getBindMacAddress(){
        BluetoothManage.getInstance().getMacAddress();
    }

    private void uploadMacAddress(String address){
        HttpHelper.commonService.setMacAddress(address)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if(stringHttpBaseBean.getErrorCode() == 0){
                            updateUI(UPLOAD_STATUS,true);
                            getPairedMacAddress();
                        }else{
                            updateUI(UPLOAD_STATUS,false);
                        }
                    }
                });
    }

    private void getPairedMacAddress(){
        HttpHelper.commonService.getMacAddress(ToTwooApplication.otherPhone)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if(stringHttpBaseBean.getErrorCode() == 0){
                            updateUI(GET_PAIRED_STATUS,true);
                            getPairedMacAddress();
                        }else{
                            updateUI(GET_PAIRED_STATUS,false);
                        }
                    }
                });
    }

    private void syncMacAddressToWatch(String address){
        BluetoothManage.getInstance().setMacAddress(address);
    }

    private void uploadSyncSuccessd(){
        //TODO 告诉服务器同步成功了
    }

    private boolean pairedSyncSuccess;

    private void getPairedSyncStatus(){
        if(pairedSyncSuccess){
            //TODO update UploadInfo字段。
            //TODO remove error字段
        }else{
            //TODO save error字段
        }
    }

    private void updateUI(int status,boolean success) {
        switch (status){
            case GET_SELF_STATUS:
                break;
            case UPLOAD_STATUS:
                break;
            case GET_PAIRED_STATUS:
                break;
            case PAIRED_SUCCESS_STATUS:
                break;

        }
    }
}
