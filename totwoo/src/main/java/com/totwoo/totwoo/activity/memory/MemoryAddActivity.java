package com.totwoo.totwoo.activity.memory;

import android.content.Intent;
import android.hardware.Camera;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.ImageView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.widget.CustomDialog;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import sz.itguy.wxlikevideo.camera.CameraHelper;

/**
 * Created by xinyoulingxi on 2017/12/5.
 */

public class MemoryAddActivity extends BaseActivity {
    @BindView(R.id.activity_memory_list_empty_photo)
    View photo;

    @BindView(R.id.activity_memory_list_empty_voice)
    View voice;

    @BindView(R.id.activity_memory_list_empty_vedio)
    View vedio;

    @BindView(R.id.activity_memory_list_empty_diary)
    View diary;

    @BindView(R.id.activity_memory_list_empty_iv)
    ImageView emptyIv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_add);
        ButterKnife.bind(this);
    }

    @Override
    protected void initTopBar() {
        setTopbarBackground(R.color.layer_bg_white);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(getString(R.string.memory_list_title1));
    }

    @OnClick({R.id.activity_memory_list_empty_photo, R.id.activity_memory_list_empty_voice, R.id.activity_memory_list_empty_vedio, R.id.activity_memory_list_empty_diary})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.activity_memory_list_empty_photo:
                onPhotoClicked();
                break;
            case R.id.activity_memory_list_empty_voice:
                onVoiceClicked();
                break;
            case R.id.activity_memory_list_empty_vedio:
                onVedioClicked();
                break;
            case R.id.activity_memory_list_empty_diary:
                onSayClicked();
                break;
            default:
                break;
        }
    }

    private void onSayClicked() {
        startActivity(new Intent(this, MemorySayActivity.class));
        onCloseClicked();
    }

    private void onPhotoClicked() {
        startActivity(new Intent(this, MemoryPhotoSelectActivity.class));
        onCloseClicked();
    }

    private void onVoiceClicked() {
        if (!NetUtils.checkPermission(this, "android.permission.RECORD_AUDIO")) {
            final CustomDialog dialog = new CustomDialog(this);
            dialog.setMessage(R.string.open_audio_error);
            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    dialog.dismiss();
                }
            });
            dialog.show();
            return;
        }
        startActivity(new Intent(this, MemoryAudioActivity.class));
        onCloseClicked();
    }

    private void onVedioClicked() {
        int cameraId = CameraHelper.getDefaultCameraID();
        Camera mCamera = CameraHelper.getCameraInstance(cameraId);
        if (mCamera == null) {
            final CustomDialog dialog = new CustomDialog(this);
            dialog.setMessage(R.string.open_camera_error1);
            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                    dialog.dismiss();
                }
            });
            dialog.show();
            return;
        } else {
            mCamera.release();
        }

        startActivity(new Intent(this, MemoryVedioActivity.class));
        onCloseClicked();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (Apputils.systemLanguageIsChinese(this))
            emptyIv.setImageResource(R.drawable.memory_list_paper_ch);
        else
            emptyIv.setImageResource(R.drawable.memory_list_paper_en);
    }

    private void onCloseClicked() {
        this.finish();
    }
}
