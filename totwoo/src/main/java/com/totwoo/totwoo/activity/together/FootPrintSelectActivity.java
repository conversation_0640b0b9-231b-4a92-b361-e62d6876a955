package com.totwoo.totwoo.activity.together;

import static com.totwoo.totwoo.activity.together.MainTogetherActivity.TOGETHER_INFO;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.TogetherBean;
import com.totwoo.totwoo.bean.TogetherHttpBean;
import com.totwoo.totwoo.bean.TogetherProvinceBean;
import com.totwoo.totwoo.bean.TogetherSelectBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;

public class FootPrintSelectActivity extends BaseActivity {
    @BindView(R.id.foot_print_selected_rv)
    RecyclerView mSelectedRv;
    @BindView(R.id.foot_print_province_rv)
    RecyclerView mProvinceRv;
    @BindView(R.id.foot_print_city_rv)
    RecyclerView mCityRv;
    @BindView(R.id.foot_print_China_tv)
    TextView mChinaTv;
    @BindView(R.id.foot_print_China_line)
    View mChinaLine;
    @BindView(R.id.foot_print_other_tv)
    TextView mOtherTv;
    @BindView(R.id.foot_print_other_line)
    View mOtherLine;

    @BindView(R.id.topBar)
    View topBar;

    private boolean isChina = true;
    private ArrayList<TogetherSelectBean> selectedBeans;
    private ArrayList<TogetherSelectBean> originalBeans;
    private TogetherSelectedAdapter togetherSelectedAdapter;

    private TogetherCityAdapter togetherCityAdapter;
    private ArrayList<TogetherSelectBean> cityBeans;
    private ArrayList<TogetherSelectBean> countryBeans;

    private TogetherProvinceAdapter togetherProvinceAdapter;
    private ArrayList<TogetherProvinceBean> provinceBeans;
    private ArrayList<TogetherProvinceBean> continentBeans;

    private ACache aCache;
    private Gson gson;
    private static final String PROVINCE_DATA = "province_data";
    private static final String HOT_CITY_DATA = "hot_city_data";
    private static final String CONTINENT_DATA = "continent_data";
    private static final String HOT_COUNTRY_DATA = "hot_country_data";
    private static final String CITY_PRE = "city_pre";
    private static final int CACHE_TIME = 7 * 24 * 3600;
//    private static final int CACHE_TIME = 60;

    private int provinceIndex = 0;
    private int continentIndex = 0;

    public static final int SAVE_SELECT_CITY_SUCCESS = 1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_foot_print);
        ButterKnife.bind(this);
        aCache = ACache.get(FootPrintSelectActivity.this);
        gson = new Gson();

        String info = getIntent().getStringExtra(TOGETHER_INFO);
        Gson gson = new Gson();
        TogetherBean togetherBean = gson.fromJson(info, TogetherBean.class);
        selectedBeans = new ArrayList<>();
        originalBeans = new ArrayList<>();
        if (togetherBean != null) {
            selectedBeans = (ArrayList<TogetherSelectBean>) togetherBean.getList();
            originalBeans.addAll(selectedBeans);
        }
        togetherSelectedAdapter = new TogetherSelectedAdapter();
        mSelectedRv.setAdapter(togetherSelectedAdapter);
        mSelectedRv.setLayoutManager(new LinearLayoutManager(FootPrintSelectActivity.this, LinearLayoutManager.HORIZONTAL, false));
        if (selectedBeans != null && !selectedBeans.isEmpty()) {
            mSelectedRv.setVisibility(View.VISIBLE);
            mSelectedRv.scrollToPosition(togetherSelectedAdapter.getItemCount() - 1);
        }

        provinceBeans = new ArrayList<>();
        continentBeans = new ArrayList<>();
        togetherProvinceAdapter = new TogetherProvinceAdapter(provinceBeans);
        mProvinceRv.setLayoutManager(new LinearLayoutManager(FootPrintSelectActivity.this));
        mProvinceRv.setAdapter(togetherProvinceAdapter);
        getProvince();

        cityBeans = new ArrayList<>();
        countryBeans = new ArrayList<>();
        togetherCityAdapter = new TogetherCityAdapter(cityBeans);
        mCityRv.setLayoutManager(new GridLayoutManager(FootPrintSelectActivity.this, 2));
        mCityRv.setAdapter(togetherCityAdapter);
        getHotCity();

        EdgeToEdgeUtils.setupTopInsets(topBar);
    }

    @OnClick({R.id.foot_print_back_iv, R.id.foot_print_save_tv, R.id.foot_print_China, R.id.foot_print_other})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.foot_print_back_iv:
                back();
                break;
            case R.id.foot_print_save_tv:
                save();
                break;
            case R.id.foot_print_China:
                selectChina(true);
                break;
            case R.id.foot_print_other:
                selectChina(false);
                break;
        }
    }

    private void selectChina(boolean selectChina) {
        if (isChina != selectChina) {
            isChina = selectChina;
            if (selectChina) {
                mChinaTv.setTextColor(getResources().getColor(R.color.color_main));
                mChinaTv.setTextSize(17);
                mOtherTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
                mOtherTv.setTextSize(15);
                mChinaLine.setVisibility(View.VISIBLE);
                mOtherLine.setVisibility(View.GONE);
                getProvince();
                if (provinceIndex == 0) {
                    getHotCity();
                } else {
                    getCity(provinceBeans.get(provinceIndex).getId(), true);
                }
            } else {
                mOtherTv.setTextColor(getResources().getColor(R.color.color_main));
                mOtherTv.setTextSize(17);
                mChinaTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
                mChinaTv.setTextSize(15);
                mChinaLine.setVisibility(View.GONE);
                mOtherLine.setVisibility(View.VISIBLE);
                getContinent();
                if (continentIndex == 0) {
                    getCountry();
                } else {
                    getCity(continentBeans.get(continentIndex).getId(), true);
                }
            }
        }
    }

    private void getProvince() {
        if (provinceBeans != null && provinceBeans.size() > 0) {
            togetherProvinceAdapter.setProvinceBeans(provinceBeans);
            mProvinceRv.scrollToPosition(provinceIndex);
            return;
        }
        if (!TextUtils.isEmpty(aCache.getAsString(PROVINCE_DATA))) {
            String json = aCache.getAsString(PROVINCE_DATA);
            provinceBeans = gson.fromJson(json, new TypeToken<List<TogetherProvinceBean>>() {
            }.getType());
            togetherProvinceAdapter.setProvinceBeans(provinceBeans);
            return;
        }
        HttpHelper.footPrintService.getProvince()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherProvinceBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherProvinceBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            provinceBeans = (ArrayList<TogetherProvinceBean>) listHttpBaseBean.getData();
                            provinceBeans.add(0, new TogetherProvinceBean(0, "热门城市", 0, 0, 0, true));
                            togetherProvinceAdapter.setProvinceBeans(provinceBeans);
                            aCache.put(PROVINCE_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }

    private void getHotCity() {
        if (!TextUtils.isEmpty(aCache.getAsString(HOT_CITY_DATA))) {
            String json = aCache.getAsString(HOT_CITY_DATA);
            cityBeans = gson.fromJson(json, new TypeToken<List<TogetherSelectBean>>() {
            }.getType());
            togetherCityAdapter.setCityBeans(cityBeans);
            return;
        }
        HttpHelper.footPrintService.getHotCity()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherSelectBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherSelectBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            cityBeans = (ArrayList<TogetherSelectBean>) listHttpBaseBean.getData();
                            togetherCityAdapter.setCityBeans(cityBeans);
                            aCache.put(HOT_CITY_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }

    private void getCity(int id, boolean fromChina) {
        String cacheString = CITY_PRE + id;
        if (!TextUtils.isEmpty(aCache.getAsString(cacheString))) {
            String json = aCache.getAsString(cacheString);
            LogUtils.e("aab json = " + json);
            if (fromChina) {
                cityBeans = gson.fromJson(json, new TypeToken<List<TogetherSelectBean>>() {
                }.getType());
                togetherCityAdapter.setCityBeans(cityBeans);
            } else {
                countryBeans = gson.fromJson(json, new TypeToken<List<TogetherSelectBean>>() {
                }.getType());
                togetherCityAdapter.setCityBeans(countryBeans);
            }
            return;
        }
        HttpHelper.footPrintService.getCity(id)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherSelectBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherSelectBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            if (fromChina) {
                                cityBeans = (ArrayList<TogetherSelectBean>) listHttpBaseBean.getData();
                                togetherCityAdapter.setCityBeans(cityBeans);
                                aCache.put(cacheString, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                            } else {
                                countryBeans = (ArrayList<TogetherSelectBean>) listHttpBaseBean.getData();
                                togetherCityAdapter.setCityBeans(countryBeans);
                                aCache.put(cacheString, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                            }
                        }
                    }
                });
    }

    private void getContinent() {
        if (continentBeans != null && continentBeans.size() > 0) {
            togetherProvinceAdapter.setProvinceBeans(continentBeans);
            mProvinceRv.scrollToPosition(continentIndex);
            return;
        }
        if (!TextUtils.isEmpty(aCache.getAsString(CONTINENT_DATA))) {
            String json = aCache.getAsString(CONTINENT_DATA);
            continentBeans = gson.fromJson(json, new TypeToken<List<TogetherProvinceBean>>() {
            }.getType());
            togetherProvinceAdapter.setProvinceBeans(continentBeans);
            return;
        }
        HttpHelper.footPrintService.getContinent()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherProvinceBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherProvinceBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            continentBeans = (ArrayList<TogetherProvinceBean>) listHttpBaseBean.getData();
                            togetherProvinceAdapter.setProvinceBeans(continentBeans);
                            continentBeans.add(0, new TogetherProvinceBean(0, "热门国家", 0, 0, 0, true));
                            aCache.put(CONTINENT_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }

    private void getCountry() {
        if (!TextUtils.isEmpty(aCache.getAsString(HOT_COUNTRY_DATA))) {
            String json = aCache.getAsString(HOT_COUNTRY_DATA);
            countryBeans = gson.fromJson(json, new TypeToken<List<TogetherSelectBean>>() {
            }.getType());
            togetherCityAdapter.setCityBeans(countryBeans);
            return;
        }
        HttpHelper.footPrintService.getHot_country()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<TogetherSelectBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<TogetherSelectBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            countryBeans = (ArrayList<TogetherSelectBean>) listHttpBaseBean.getData();
                            togetherCityAdapter.setCityBeans(countryBeans);
                            aCache.put(HOT_COUNTRY_DATA, gson.toJson(listHttpBaseBean.getData()), CACHE_TIME);
                        }
                    }
                });
    }

    public class TogetherSelectedAdapter extends RecyclerView.Adapter<TogetherSelectedAdapter.ViewHolder> {
        @NonNull
        @Override
        public TogetherSelectedAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new ViewHolder(LayoutInflater.from(FootPrintSelectActivity.this).inflate(R.layout.together_selected_item, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull TogetherSelectedAdapter.ViewHolder holder, int position) {
            holder.mCity.setText(selectedBeans.get(position).getName());
            holder.mDelete.setOnClickListener(v -> {
                removeBean(selectedBeans.get(position));
                if (isChina) {
                    togetherCityAdapter.setCityBeans(cityBeans);
                    togetherProvinceAdapter.setProvinceBeans(provinceBeans);
                } else {
                    togetherCityAdapter.setCityBeans(countryBeans);
                    togetherProvinceAdapter.setProvinceBeans(continentBeans);
                }
                togetherSelectedAdapter.notifyDataSetChanged();
            });
        }

        @Override
        public int getItemCount() {
            return selectedBeans == null ? 0 : selectedBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.together_selected_city)
            TextView mCity;
            @BindView(R.id.together_selected_delete)
            ImageView mDelete;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    public class TogetherProvinceAdapter extends RecyclerView.Adapter<TogetherProvinceAdapter.ViewHolder> {
        private ArrayList<TogetherProvinceBean> mProvinceBeans;

        public TogetherProvinceAdapter(List<TogetherProvinceBean> provinceBeans) {
            this.mProvinceBeans = (ArrayList<TogetherProvinceBean>) provinceBeans;
        }

        public void setProvinceBeans(ArrayList<TogetherProvinceBean> provinceBeans) {
            mProvinceBeans = refreshProvince(provinceBeans);
            notifyDataSetChanged();
        }

        @NonNull
        @Override
        public TogetherProvinceAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new TogetherProvinceAdapter.ViewHolder(LayoutInflater.from(FootPrintSelectActivity.this).inflate(R.layout.together_province_item, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull TogetherProvinceAdapter.ViewHolder holder, int position) {
            holder.togetherProvinceName.setText(mProvinceBeans.get(position).getName());
            if (mProvinceBeans.get(position).isSelect()) {
                holder.togetherProvinceName.setTextColor(getResources().getColor(R.color.color_main));
                holder.togetherProvinceName.getPaint().setFakeBoldText(true);
            } else {
                holder.togetherProvinceName.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
                holder.togetherProvinceName.getPaint().setFakeBoldText(false);
            }

            holder.togetherProvinceCount.setText(String.valueOf(mProvinceBeans.get(position).getSelectCount()));
            if (mProvinceBeans.get(position).getSelectCount() > 0) {
                holder.togetherProvinceCount.setVisibility(View.VISIBLE);
            } else {
                holder.togetherProvinceCount.setVisibility(View.GONE);
            }

            if (position == mProvinceBeans.size() - 1) {
                holder.togetherProvinceLine.setVisibility(View.GONE);
            } else {
                holder.togetherProvinceLine.setVisibility(View.VISIBLE);
            }
            holder.mProvinceContent.setOnClickListener(v -> {
                if (isChina) {
                    TogetherProvinceBean provinceBean = mProvinceBeans.get(provinceIndex);
                    provinceBean.setSelect(false);
                    mProvinceBeans.set(provinceIndex, provinceBean);

                    provinceIndex = position;

                    TogetherProvinceBean selectBean = mProvinceBeans.get(provinceIndex);
                    selectBean.setSelect(true);
                    mProvinceBeans.set(provinceIndex, selectBean);
                    if (provinceIndex > 0) {
                        getCity(selectBean.getId(), true);
                    } else {
                        getHotCity();
                    }
                } else {
                    TogetherProvinceBean provinceBean = mProvinceBeans.get(continentIndex);
                    provinceBean.setSelect(false);
                    mProvinceBeans.set(continentIndex, provinceBean);

                    continentIndex = position;

                    TogetherProvinceBean selectBean = mProvinceBeans.get(continentIndex);
                    selectBean.setSelect(true);
                    mProvinceBeans.set(continentIndex, selectBean);
                    if (continentIndex > 0) {
                        getCity(selectBean.getId(), false);
                    } else {
                        getCountry();
                    }
                }
                togetherProvinceAdapter.notifyDataSetChanged();
            });
        }

        @Override
        public int getItemCount() {
            return mProvinceBeans == null ? 0 : mProvinceBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.together_province_name)
            TextView togetherProvinceName;
            @BindView(R.id.together_province_line)
            View togetherProvinceLine;
            @BindView(R.id.together_province_count)
            TextView togetherProvinceCount;
            @BindView(R.id.together_province_content)
            ConstraintLayout mProvinceContent;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    public class TogetherCityAdapter extends RecyclerView.Adapter<TogetherCityAdapter.ViewHolder> {
        private ArrayList<TogetherSelectBean> mCityBeans;

        public TogetherCityAdapter(List<TogetherSelectBean> togetherSelectBeans) {
            mCityBeans = (ArrayList<TogetherSelectBean>) togetherSelectBeans;
        }

        public void setCityBeans(ArrayList<TogetherSelectBean> togetherSelectBeans) {
            mCityBeans = refreshSelected(togetherSelectBeans);
            notifyDataSetChanged();
        }

        @NonNull
        @Override
        public TogetherCityAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new TogetherCityAdapter.ViewHolder(LayoutInflater.from(FootPrintSelectActivity.this).inflate(R.layout.together_city_item, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull TogetherCityAdapter.ViewHolder holder, int position) {
            holder.togetherCityTv.setText(mCityBeans.get(position).getName());
            if (!TextUtils.isEmpty(mCityBeans.get(position).getName()) && mCityBeans.get(position).getName().length() > 5) {
                holder.togetherCityTv.setTextSize(12);
            } else {
                holder.togetherCityTv.setTextSize(14);
            }
            if (mCityBeans.get(position).isSelect()) {
                holder.togetherCityTv.setTextColor(getResources().getColor(R.color.color_main));
                holder.togetherCityTv.setBackground(getResources().getDrawable(R.drawable.shape_together_city_select));
            } else {
                holder.togetherCityTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
                holder.togetherCityTv.setBackground(getResources().getDrawable(R.drawable.shape_together_city_unselected));
            }
            holder.togetherCityTv.setOnClickListener(v -> {
                boolean contains = contains(mCityBeans.get(position).getId());
                if (contains) {
                    removeBean(mCityBeans.get(position));
                    TogetherSelectBean selectBean = mCityBeans.get(position);
                    selectBean.setSelect(false);
                    mCityBeans.set(position, selectBean);
                    togetherSelectedAdapter.notifyDataSetChanged();
                } else {
                    TogetherSelectBean selectBean = mCityBeans.get(position);
                    selectBean.setSelect(true);
                    mCityBeans.set(position, selectBean);
                    selectedBeans.add(selectBean);
                    if (mSelectedRv.getVisibility() == View.GONE) {
                        mSelectedRv.setVisibility(View.VISIBLE);
                    }
                    togetherSelectedAdapter.notifyDataSetChanged();
                    mSelectedRv.smoothScrollToPosition(togetherSelectedAdapter.getItemCount() - 1);
                }
                //刷新各个列表的显示
                togetherCityAdapter.notifyDataSetChanged();
                //刷新省份或者大洲包含的数据
                if (isChina) {
                    togetherProvinceAdapter.setProvinceBeans(provinceBeans);
                } else {
                    togetherProvinceAdapter.setProvinceBeans(continentBeans);
                }
            });
        }

        @Override
        public int getItemCount() {
            return mCityBeans == null ? 0 : mCityBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.together_city)
            TextView togetherCityTv;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    private boolean contains(int id) {
        if (selectedBeans == null || selectedBeans.size() == 0) {
            return false;
        }

        for (TogetherSelectBean togetherSelectBean : selectedBeans) {
            if (togetherSelectBean.getId() == id) {
                return true;
            }
        }
        return false;
    }

    private ArrayList<TogetherSelectBean> refreshSelected(ArrayList<TogetherSelectBean> targetBeans) {
        if (selectedBeans != null && selectedBeans.size() > 0) {
            for (int i = 0; i < targetBeans.size(); i++) {
                TogetherSelectBean selectBean = targetBeans.get(i);
                for (int j = 0; j < selectedBeans.size(); j++) {
                    if (selectedBeans.get(j).getId() == targetBeans.get(i).getId()) {
                        selectBean.setSelect(true);
                        targetBeans.set(i, selectBean);
                        break;
                    } else {
                        selectBean.setSelect(false);
                        targetBeans.set(i, selectBean);
                    }
                }
            }
        } else {
            for (int i = 0; i < targetBeans.size(); i++) {
                if (targetBeans.get(i).isSelect()) {
                    TogetherSelectBean selectBean = targetBeans.get(i);
                    selectBean.setSelect(false);
                    targetBeans.set(i, selectBean);
                }
            }
        }
        return targetBeans;
    }

    private ArrayList<TogetherProvinceBean> refreshProvince(ArrayList<TogetherProvinceBean> targetBeans) {
        //省份选择数量清零
        for (int i = 0; i < targetBeans.size(); i++) {
            if (targetBeans.get(i).getSelectCount() > 0) {
                TogetherProvinceBean togetherProvinceBean = targetBeans.get(i);
                togetherProvinceBean.setSelectCount(0);
                targetBeans.set(i, togetherProvinceBean);
            }
        }

        if (selectedBeans != null && selectedBeans.size() > 0) {
            int count = 0;
            for (int i = 0; i < targetBeans.size(); i++) {
                TogetherProvinceBean togetherProvinceBean = targetBeans.get(i);
                for (int j = 0; j < selectedBeans.size(); j++) {
                    if (togetherProvinceBean.getId() == selectedBeans.get(j).getPid()) {
                        togetherProvinceBean.setSelectCount(togetherProvinceBean.getSelectCount() + 1);
                        count++;
                    }
                }
                targetBeans.set(i, togetherProvinceBean);
                if (count == (selectedBeans.size())) {
                    break;
                }
            }
        }
        return targetBeans;
    }

    private void removeBean(TogetherSelectBean targetBean) {
        for (int i = 0; i < selectedBeans.size(); i++) {
            if (selectedBeans.get(i).getId() == targetBean.getId()) {
                selectedBeans.remove(i);
                break;
            }
        }
        if (selectedBeans.isEmpty()) {
            mSelectedRv.setVisibility(View.GONE);
        }
    }

    @Override
    public void onBackPressed() {
        back();
    }

    private void back() {
        if (isChange()) {
            showNotSaveDialog();
        } else {
            finish();
        }
    }

    private boolean isChange() {
        if (selectedBeans.size() == originalBeans.size()) {
            for (TogetherSelectBean togetherSelectBean : selectedBeans) {
                if (!contains(originalBeans, togetherSelectBean)) {
                    return true;
                }
            }
            return false;
        } else {
            return true;
        }
    }

    private boolean contains(ArrayList<TogetherSelectBean> originalBeans, TogetherSelectBean targetBean) {
        int number = 0;
        for (TogetherSelectBean togetherSelectBean : originalBeans) {
            if (togetherSelectBean.getId() == targetBean.getId()) {
                number++;
            }
        }
        return number > 0;
    }

    private void showNotSaveDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(FootPrintSelectActivity.this);
        commonMiddleDialog.setMessage("你已选择的地方还没有生成足迹，确认要返回吗？");
        commonMiddleDialog.setSure(v -> finish());
        commonMiddleDialog.setCancel("取消");
        commonMiddleDialog.show();
    }

    private void save() {
        String cities = "";
        if (selectedBeans != null && selectedBeans.size() > 0) {
            ArrayList<TogetherHttpBean> beans = new ArrayList<>();
            for (TogetherSelectBean togetherSelectBean : selectedBeans) {
                beans.add(new TogetherHttpBean(togetherSelectBean.getId(), togetherSelectBean.getName(), togetherSelectBean.getPid(), togetherSelectBean.getType()));
            }
            cities = gson.toJson(beans);
        }
        HttpHelper.footPrintService.saveCity(ToTwooApplication.otherPhone, ToTwooApplication.owner.getPairedId(), cities)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        setResult(SAVE_SELECT_CITY_SUCCESS);
                        finish();
                    }
                });
    }
}
