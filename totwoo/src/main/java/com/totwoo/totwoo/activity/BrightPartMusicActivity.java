package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.DONT_SHOW_TIPS_TAG;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_PART_VALUE;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.adapter.BrightMusicAdapter;
import com.totwoo.totwoo.adapter.BrightMusicSelectAdapter;
import com.totwoo.totwoo.bean.BrightMusicBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.service.BrightMusicPlayService;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SnackBarUtil;
import com.totwoo.totwoo.widget.IMHintDialog;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class BrightPartMusicActivity extends BaseActivity implements BrightMusicAdapter.BrightLightSelected, BrightMusicSelectAdapter.BrightLightSelected, SubscriberListener {
    @BindView(R.id.bright_music_rv)
    RecyclerView mRecyclerView;
    //    @BindView(R.id.bright_mode_top_banner_iv)
//    ImageView mBannerIv;
//    @BindView(R.id.bright_mode_top_bg_iv)
//    ImageView mBgIv;
    @BindView(R.id.bright_banner_lv)
    LottieAnimationView mBannerLv;
    @BindView(R.id.bright_music_select_cl)
    ConstraintLayout mMusicSelectCl;
    @BindView(R.id.bright_music_cv)
    CardView mMusicCv;
    @BindView(R.id.bright_music_select_rv)
    RecyclerView mRecyclerMusicSelectView;
    @BindView(R.id.bright_music_info_tv)
    TextView mInfoTv;

    @BindView(R.id.flash_switch_title_tv)
    TextView switchTv;

    @BindView(R.id.flash_switch_cb)
    TextView switchCb;


    private BrightMusicAdapter brightMusicAdapter;
    private BrightMusicSelectAdapter brightMusicSelectAdapter;
    private boolean brightMode;
    private String IS_TOUCH_BRIGHT_HINT_SHOW = "IS_TOUCH_BRIGHT_HINT_SHOW";
    private String IS_CLICK_BRIGHT_HINT_SHOW = "IS_CLICK_BRIGHT_HINT_SHOW";
    private static final String REMIND_BRIGHT_SHOW = "remind_bright_show";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bright_music);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        brightMode = PreferencesUtils.getInt(this, COLOR_VALUE, -1) > 0;

        brightMusicAdapter = new BrightMusicAdapter(BrightPartMusicActivity.this);
        brightMusicAdapter.setOnLightClick(this);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(BrightPartMusicActivity.this, RecyclerView.VERTICAL, false));
        mRecyclerView.setAdapter(brightMusicAdapter);

        CommonUtils.setStateBar(this, true);

        int nowMusicIndex = PreferencesUtils.getInt(this, MUSIC_PART_VALUE, 0);
        nowMusicIndex = Math.abs(nowMusicIndex);
        ArrayList<BrightMusicBean> brightMusicBeans = new ArrayList<>();
        brightMusicBeans.add(new BrightMusicBean(R.string.bright_music_default, 0 == nowMusicIndex));
        brightMusicBeans.add(new BrightMusicBean(R.string.bright_music_birthday, 1 == nowMusicIndex));
        brightMusicSelectAdapter = new BrightMusicSelectAdapter(BrightPartMusicActivity.this, brightMusicBeans);
        brightMusicSelectAdapter.setBrightMusicClick(this);
        mRecyclerMusicSelectView.setLayoutManager(new LinearLayoutManager(BrightPartMusicActivity.this, RecyclerView.VERTICAL, false));
        mRecyclerMusicSelectView.setAdapter(brightMusicSelectAdapter);

        startService(new Intent(this, BrightMusicPlayService.class));

//        mInfoTv.setText(R.string.bright_click_top_info);
        imHintDialog = new IMHintDialog(BrightPartMusicActivity.this, v -> imHintDialog.dismiss(), getResources().getString(R.string.flash_hint_title),
                getResources().getString(R.string.flash_hint_paragraph1), null);
        if (!PreferencesUtils.getBoolean(BrightPartMusicActivity.this, IS_CLICK_BRIGHT_HINT_SHOW, false)) {
            PreferencesUtils.put(BrightPartMusicActivity.this, IS_CLICK_BRIGHT_HINT_SHOW, true);
            imHintDialog.show();
        }
        mBannerLv.setImageAssetsFolder("flash_set_image/");
        mBannerLv.setAnimation("flash_set.json");

        switchCb.setSelected(CommonUtils.jewelryFlashOpen(this));
        switchTv.setText(CommonUtils.jewelryFlashOpen(this) ? R.string.turn_on_flash : R.string.turn_off_flash);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }


    @EventInject(eventType = S.E.E_FLASH_CHANGED, runThread = TaskType.UI)
    public void onFlashReceiver(EventData data) {
        boolean open = CommonUtils.jewelryFlashOpen(this);

        switchCb.setSelected(open);
        switchTv.setText(open ? R.string.turn_on_flash : R.string.turn_off_flash);

        brightMusicAdapter.setIsPlaying(open);

        if (open && !PreferencesUtils.getBoolean(this, DONT_SHOW_TIPS_TAG, false)) {
            SnackBarUtil.showLong(mRecyclerView, R.string.bright_open_during, R.string.no_longer_tips_notify_guide,
                    v -> PreferencesUtils.put(this, DONT_SHOW_TIPS_TAG, true));
        }
    }

    @OnClick({R.id.bright_music_select_cl, R.id.bright_music_cv, R.id.bottom_switch_layout})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.bright_music_select_cl:
                goneMusicSelectView();
                break;
            case R.id.bottom_switch_layout:
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_FLASH_CHANGE, null);
                break;
            case R.id.bright_music_cv:
                break;
        }
    }

    private IMHintDialog imHintDialog;

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_write);
        setTopRightIcon(R.drawable.bright_music_info);
        setTopRightOnClick(v -> imHintDialog.show());
        getTopTitleView().setTextColor(getResources().getColor(R.color.text_color_white));
        setTopTitle(getString(R.string.homoe_bright_mode));
        setTopbarBackground(R.color.transparent);
        super.initTopBar();
    }

    @Override
    protected void onResume() {
        super.onResume();
        BluetoothManage.getInstance().connectedStatus();
    }

    private String getFlashColorValue(int position) {
        String str = null;
        switch (position) {
            case 0:
                str = "COLORFUL";
                break;
            case 1:
                str = "PINK";
                break;
            case 2:
                str = "RED";
                break;
            case 3:
                str = "ORANGE";
                break;
            case 4:
                str = "YELLOW";
                break;
            case 5:
                str = "GREEN";
                break;
            case 6:
                str = "CYAN";
                break;
            case 7:
                str = "BLUE";
                break;
            case 8:
                str = "PURPLE";
                break;
            case 9:
                str = "WHITE";
                break;
        }
        return str;
    }

    @Override
    public void onItemClick(int position) {
        boolean isOn = CommonUtils.jewelryFlashOpen(this);

        //给手机下闪光指令
        if (!isOn) {
            PreferencesUtils.put(BrightPartMusicActivity.this, COLOR_VALUE, -position - 1);
        } else {
            PreferencesUtils.put(BrightPartMusicActivity.this, COLOR_VALUE, position + 1);
        }
        playMusicAndBright();
        //更换顶部banner图片
        //弹出音乐选择view
        if (position != 0) {
            showMusicSelectView();
        } else {
            PreferencesUtils.put(BrightPartMusicActivity.this, MUSIC_PART_VALUE, 0);
            brightMusicSelectAdapter.setSelect(0);
        }
    }

    private void showMusicSelectView() {
        musicSelectViewVisable = true;
        mMusicSelectCl.setVisibility(View.VISIBLE);
        brightMusicSelectAdapter.notifyDataSetChanged();
        mMusicCv.setVisibility(View.VISIBLE);
        int width = CommonUtils.dip2px(BrightPartMusicActivity.this, 280);
        TranslateAnimation translateAnimation = new TranslateAnimation(width, 0, 0, 0);
        translateAnimation.setFillAfter(true);
        translateAnimation.setDuration(500);
        translateAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mMusicCv.startAnimation(translateAnimation);
    }

    private void goneMusicSelectView() {
        musicSelectViewVisable = false;
        int width = CommonUtils.dip2px(BrightPartMusicActivity.this, 280);
        TranslateAnimation translateAnimation = new TranslateAnimation(0, width, 0, 0);
        translateAnimation.setFillAfter(true);
        translateAnimation.setDuration(400);
        translateAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mMusicSelectCl.setVisibility(View.GONE);
                mMusicCv.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mMusicCv.startAnimation(translateAnimation);
    }

    private boolean musicSelectViewVisable = false;

    @Override
    public void onBackPressed() {
        if (musicSelectViewVisable) {
            goneMusicSelectView();
        } else {
            finish();
        }
    }

    @Override
    public void onMusicItemClick(int position) {
        PreferencesUtils.put(BrightPartMusicActivity.this, MUSIC_PART_VALUE, position);
        playMusicAndBright();
    }

    @EventInject(eventType = S.E.E_MUSIC_PLAY_END, runThread = TaskType.UI)
    public void musicPlayEnd(EventData data) {
        brightMusicAdapter.setIsPlaying(false);
    }

    @EventInject(eventType = S.E.E_MUSIC_PLAY_DESTROY, runThread = TaskType.UI)
    public void serviceDestory(EventData data) {
        startService(new Intent(this, BrightMusicPlayService.class));
    }

    private void playMusicAndBright() {
        int musicIndex = PreferencesUtils.getInt(BrightPartMusicActivity.this, MUSIC_PART_VALUE, 0);
        int brightIndex = PreferencesUtils.getInt(BrightPartMusicActivity.this, COLOR_VALUE, -1);
        brightIndex = Math.abs(brightIndex) - 1;

        if (brightIndex > 0) {
            brightMusicAdapter.setIsPlaying(musicIndex > 0);
            if (musicIndex == 1) {
                musicIndex = 7;
            }
            if (brightMode) {
                BluetoothManage.getInstance().changeMusicBrightMode(musicIndex, NotifyUtil.getColorValue(getFlashColorValue(brightIndex)));
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_PLAY_PART, null);
            } else {
                BluetoothManage.getInstance().changeMusicBrightModePreview(musicIndex, NotifyUtil.getColorValue(getFlashColorValue(brightIndex)));
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_START_PART, null);
            }
        } else {
            if (brightMode) {
                BluetoothManage.getInstance().changeMusicBrightMode(0, NotifyUtil.getColorValue(getFlashColorValue(brightIndex)));
            } else {
                BluetoothManage.getInstance().changeMusicBrightModePreview(0, NotifyUtil.getColorValue(getFlashColorValue(brightIndex)));
            }
            brightMusicAdapter.setIsPlaying(false);
        }
        if (brightIndex == 0 || musicIndex == 0) {
            if (!brightMode) {
                setTimer(brightIndex, 7500);
            }
        }

    }

    Timer timer = new Timer();
    boolean isTiming = false;

    private void setTimer(int brightIndex, long time) {
//        Observable.timer(time, TimeUnit.MILLISECONDS, Schedulers.newThread())
//                .observeOn(Schedulers.newThread())
//                .subscribe(new Action1<Long>() {
//                    @Override
//                    public void call(Long aLong) {
//                        //现在播放的有音乐return
//                        if(PreferencesUtils.getInt(BrightMusicActivity.this, MUSIC_PART_VALUE,0) > 0){
//                            return;
//                        }
//                        int nowColorIndex = -brightIndex - 1;
//                        PreferencesUtils.put(BrightMusicActivity.this, COLOR_VALUE, nowColorIndex);
//                        BluetoothManage.getInstance().changeBirghtMode(-1);
//                    }
//                });
        if (isTiming) {
            timer.cancel();
            timer = new Timer();
        }

        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                //现在播放的有音乐return
                if (PreferencesUtils.getInt(BrightPartMusicActivity.this, MUSIC_PART_VALUE, 0) > 0 || PreferencesUtils.getInt(BrightPartMusicActivity.this, COLOR_VALUE, -1) > 0) {
                    return;
                }
                isTiming = false;
                int nowColorIndex = -brightIndex - 1;
                PreferencesUtils.put(BrightPartMusicActivity.this, COLOR_VALUE, nowColorIndex);
                BluetoothManage.getInstance().changeBirghtMode(-1);
            }
        };
        timer.schedule(timerTask, time);
        isTiming = true;

    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing()) {
            if (PreferencesUtils.getInt(BrightPartMusicActivity.this, COLOR_VALUE, -1) > 0) {
                return;
            }
            timer.cancel();
            BluetoothManage.getInstance().changeBirghtMode(-1);
            if (PreferencesUtils.getInt(BrightPartMusicActivity.this, MUSIC_PART_VALUE, 0) > 0) {
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
            }
        }
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
