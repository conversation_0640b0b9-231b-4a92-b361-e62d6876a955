package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.etone.framework.event.EventBus;
import com.hjq.shape.view.ShapeTextView;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.WheelView;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/3/6.
 */

public class LoveNotifyEditActivity extends BaseActivity {
    @BindView(R.id.edit_custom_long_vibration_tv)
    ShapeTextView mLongVibrationTv;
    @BindView(R.id.edit_custom_short_vibration_tv)
    ShapeTextView mShortVibrationTv;

    @BindView(R.id.short_vibration_iv)
    View mShortVibrationIv;

    @BindView(R.id.long_vibration_iv)
    View mLongVibrationIv;

    @BindView(R.id.edit_custom_content)
    LinearLayout mPeriodSettingContent;
    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    @BindView(R.id.edit_custom_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.edit_custom_time_layout)
    FrameLayout mTimeFL;
    @BindView(R.id.edit_custom_time_layout_view)
    View mTimeView;
    @BindView(R.id.edit_custom_birthday_layout)
    FrameLayout mBirthdayFL;
    @BindView(R.id.edit_custom_birthday_layout_view)
    View mBirthdayView;
    @BindView(R.id.edit_custom_repeat_layout)
    FrameLayout mRepeatFL;
    @BindView(R.id.edit_custom_repeat_layout_view)
    View mRepeatView;
    @BindView(R.id.edit_custom_notify_value_tv)
    TextView mNotifyTimeTv;
    @BindView(R.id.edit_custom_birthday_value_tv)
    TextView mBirthdayValueTv;
    @BindView(R.id.edit_custom_birthday_key_tv)
    TextView mBirthdayKeyTv;
    @BindView(R.id.add_love_vibration_ll)
    ViewGroup mVibrationLl;
    @BindView(R.id.add_love_notify_mode_title)
    TextView mModeTitle;

    CustomItemBean bean;
    JewelryNotifyModel nowSetModel;
    CustomColorLibraryAdapter colorLibraryAdapter;

    SimpleDateFormat formatToDay = new SimpleDateFormat("yyyy-MM-dd");

    private String tempBirthday;

    private String currentRepeatType;
    private int currentNotifyTimeIndex;
    private int currentType;
    private CustomBottomDialog dialog;
    private long currentTime;
    private List<String> defaultAnnNotifyTimeStrs;
    private final static double DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[] = {24, 48, 72, 168};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_custom_notify);
        ButterKnife.bind(this);
        bean = (CustomItemBean) getIntent().getSerializableExtra("bean");

        int birthSize = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;
        defaultAnnNotifyTimeStrs = new ArrayList<>();
        for (int i = 0; i < birthSize; i++) {
            defaultAnnNotifyTimeStrs.add(getAnnHourStringByIndex(i));
        }
        if (bean != null) {
            initView();
        } else {
            int define_id = getIntent().getIntExtra("define_id", 0);
            if (define_id != 0) {
                HttpHelper.customService.getCustom(define_id)
                        .subscribeOn(Schedulers.newThread())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Observer<HttpBaseBean<CustomItemBean>>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {

                            }

                            @Override
                            public void onNext(HttpBaseBean<CustomItemBean> customItemBeanHttpBaseBean) {
                                bean = customItemBeanHttpBaseBean.getData();
                                initView();
                            }
                        });
            }
        }
        if (BleParams.isButtonBatteryJewelry()) {
            mVibrationLl.setVisibility(View.GONE);
            mModeTitle.setText(R.string.love_notify_instruction_no_vibrate);
        }
    }

    private void initView() {
        nowSetModel = new JewelryNotifyModel();
        nowSetModel.setFlashColor(bean.getNotify_mode());
        if (bean.getIs_open() == 1)
            nowSetModel.setNotifySwitch(true);
        else
            nowSetModel.setNotifySwitch(false);

        if (TextUtils.equals(bean.getShock_type(), "short"))
            nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
        else
            nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);

        currentType = bean.getDefine_type();
        currentNotifyTimeIndex = getBirthHourIndexByDouble(Double.valueOf(bean.getRemind_time()));
        mNotifyTimeTv.setText(getAnnHourStringByIndex(currentNotifyTimeIndex));
        currentRepeatType = bean.getRepeat_notify();
        mCallSwitchCb.setChecked(nowSetModel.isNotifySwitch());
        mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
        if (!nowSetModel.isNotifySwitch())
            mPeriodSettingContent.setVisibility(View.GONE);

        tempBirthday = bean.getDefine_time();
        mBirthdayValueTv.setText(tempBirthday);
        mBirthdayKeyTv.setText(getString(R.string.love_space_notify_date));
        try {
            currentTime = formatToDay.parse(bean.getDefine_time()).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        mBirthdayFL.setVisibility(View.VISIBLE);
        mBirthdayView.setVisibility(View.VISIBLE);
        mTimeFL.setVisibility(View.GONE);
        mTimeView.setVisibility(View.GONE);
        mRepeatFL.setVisibility(View.GONE);
        mRepeatView.setVisibility(View.GONE);

        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                setTextColorBtn(false);
                break;
        }
        int spanCount = BleParams.isCtJewlery() ? 7 : 6;

        boolean jewelryGlitterEnabled = !(BleParams.isCtJewlery() || BleParams.isMWJewlery()) || PreferencesUtils.getBoolean(this, "jewelry_glitter_enabled", true);
        if (!jewelryGlitterEnabled) {
            colorLibraryRecyclerView.setAlpha(0.4f);
        } else {
            colorLibraryRecyclerView.setAlpha(1f);
        }

        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(this, spanCount));
        colorLibraryAdapter = new CustomColorLibraryAdapter(nowSetModel.getFlashColor(), spanCount,
                false,true);

        colorLibraryAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (!jewelryGlitterEnabled) {
                ToastUtils.showLong(this, R.string.enable_notification_can_flash);
                return;
            }
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);
            if (colorLibraryBean != null) {
                nowSetModel.setFlashColor(colorLibraryBean.getColor());
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                saveNowModel(false);
            }
        });
        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);

        setTopTitle(bean.getTitle());
    }

    @Override
    public void onBackPressed() {
        saveInfo();
//        super.onBackPressed();
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveInfo();
            }
        });

        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopRightIcon(R.drawable.delete);
        setTopRightOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final CustomDialog customDialog = new CustomDialog(LoveNotifyEditActivity.this);
                customDialog.setMessage(R.string.custom_notify_list_delete_hint);
                customDialog.setTitle(R.string.tips);
                customDialog.setPositiveButton(v12 -> {
                    delete();
                    customDialog.dismiss();
                });
                customDialog.setNegativeButton(R.string.cancel, v1 -> {
                    customDialog.dismiss();
                });
                customDialog.show();
            }
        });
        setSpinState(false);
    }

    @OnClick({R.id.edit_custom_long_vibration_tv, R.id.edit_custom_short_vibration_tv,
            R.id.edit_custom_birthday_layout, R.id.edit_custom_notify_layout,
            R.id.edit_custom_save_tv, R.id.notify_switch_click_item})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.edit_custom_long_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);

                saveNowModel(false);
                setTextColorBtn(true);
                break;
            case R.id.edit_custom_short_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                saveNowModel(false);
                setTextColorBtn(false);
                break;
            case R.id.notify_switch_click_item:
                mCallSwitchCb.setChecked(!mCallSwitchCb.isChecked());
                nowSetModel.setNotifySwitch(mCallSwitchCb.isChecked());
                saveNowModel(true);
                break;
            case R.id.edit_custom_notify_layout:
                showAnnNotifyDialog();
                break;
            case R.id.edit_custom_birthday_layout:
                showAnnDialog();
                break;
            case R.id.edit_custom_save_tv:
                saveInfo();
                break;
        }
    }

    private void saveInfo() {
        if (nowSetModel == null) {
            finish();
            return;
        }
        final String defineTime = tempBirthday;

        final String notifyTime = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[currentNotifyTimeIndex] + "";

        final int open;
        if (nowSetModel.isNotifySwitch())
            open = 1;
        else
            open = 0;

        HttpHelper.customService.updateCustom(open, currentType, bean.getDefine_id(), defineTime,
                        notifyTime, currentRepeatType, nowSetModel.getVibrationHttpString(), nowSetModel.getFlashColor(),
                        ToTwooApplication.owner.getPairedId(), ToTwooApplication.otherPhone)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<CustomItemBean>>() {
                    @Override
                    public void onCompleted() {
                        finish();
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoveNotifyEditActivity.this, R.string.error_net);
                        finish();
                    }

                    @Override
                    public void onNext(HttpBaseBean<CustomItemBean> customItemBeanHttpBaseBean) {
                        //TODO
                        if (customItemBeanHttpBaseBean.getErrorCode() == 0) {
                            bean.setIs_open(open);
                            bean.setShock_type(nowSetModel.getVibrationHttpString());
                            bean.setDefine_time(defineTime);
                            bean.setNotify_mode(nowSetModel.getFlashColor());
                            bean.setRepeat_notify(currentRepeatType);
                            bean.setRemind_time(notifyTime);
                            EventBus.onPostReceived(S.E.E_CUSTOM_UPDATE_SUCCESSED, bean);
                        }
                    }
                });

    }

    private void showAnnDialog() {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        long thirtyYears = 30L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack((timePickerView, millseconds) -> {
                    tempBirthday = format.format(millseconds);
                    currentTime = millseconds;
                    StringBuffer buffer = new StringBuffer(tempBirthday);
                    String year = buffer.substring(0, 4);
                    String month = buffer.substring(5, 7);
                    String day = buffer.substring(8, 10);
                    mBirthdayValueTv.setText(year + "-" + month + "-" + day);

                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.love_space_notify_select_date))
                .setInfoText(getString(R.string.love_notify_remind_time))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis())
                .setMaxMillseconds(System.currentTimeMillis() + thirtyYears)
                .setCurrentMillseconds(currentTime)
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "year_month_day");
    }

    private void showAnnNotifyDialog() {
        dialog = new CustomBottomDialog(LoveNotifyEditActivity.this);
        dialog.setTitle(getString(R.string.period_setting_select_remind_title));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultAnnNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(currentNotifyTimeIndex);
        weekLayout.addView(weekWheelView);
        dialog.setMainView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String str = weekWheelView.getSeletedItem();
            mNotifyTimeTv.setText(str);
            currentNotifyTimeIndex = defaultAnnNotifyTimeStrs.indexOf(str);
            dialog.dismiss();
        });
        dialog.show();
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationIv.setVisibility(View.VISIBLE);
            mShortVibrationIv.setVisibility(View.GONE);

            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        } else {
            mLongVibrationIv.setVisibility(View.GONE);
            mShortVibrationIv.setVisibility(View.VISIBLE);

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        }
    }

    private void saveNowModel(boolean isSwitch) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            Toast.makeText(ToTwooApplication.baseContext, R.string.error_jewelry_connect, Toast.LENGTH_SHORT).show();
            return;
        }
        if (!isSwitch)
            BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());

        if (isSwitch) {
            mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
            //切换时候的动画
            Animation anim = AnimationUtils.loadAnimation(this, nowSetModel.isNotifySwitch() ? R.anim.layout_open : R.anim.layout_close);
            anim.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {
                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    if (nowSetModel.isNotifySwitch()) {
                        mPeriodSettingContent.setVisibility(View.VISIBLE);
                    } else {
                        mPeriodSettingContent.setVisibility(View.INVISIBLE);
                    }
                }

                @Override
                public void onAnimationRepeat(Animation animation) {
                }
            });
            mPeriodSettingContent.startAnimation(anim);
        }
    }

    private String getAnnHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_remind_1d);
        switch (index) {
            case 0:
//                string = getString(R.string.love_space_notify_remind_day);
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 1:
                string = getString(R.string.custom_notify_remind_2d);
                break;
            case 2:
                string = getString(R.string.custom_notify_remind_3d);
                break;
            case 3:
                string = getString(R.string.custom_notify_remind_7d);
                break;
            case 4:
                break;
        }
        return string;
    }

    private void setSelectColor(ImageView imageView, String colorName) {
        int resId = NotifyUtil.getColorImageResId(colorName);
        imageView.setImageResource(resId);
        if (resId == R.drawable.custom_color_normal) {
            imageView.setColorFilter(Color.parseColor(NotifyUtil.getDisplayColorByColorName(colorName)));
        } else {
            imageView.setColorFilter(null);
        }
    }

    private int getBirthHourIndexByDouble(double d) {
        int index = 0;
        int size = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            if (d == DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[i]) {
                index = i;
            }
        }
        return index;
    }

    private void delete() {
        HttpHelper.customService.deleteCustom(bean.getDefine_id(), ToTwooApplication.otherPhone)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoveNotifyEditActivity.this, R.string.custom_notify_list_delete_fail);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            deleteSuccess();
                        }
                    }
                });

    }

    private void deleteSuccess() {
        //TODO
        EventBus.onPostReceived(S.E.E_CUSTOM_DELETE_SUCCESSED, bean);
        finish();
    }
}
