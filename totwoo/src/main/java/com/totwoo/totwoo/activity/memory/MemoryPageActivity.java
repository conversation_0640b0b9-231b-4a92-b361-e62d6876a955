package com.totwoo.totwoo.activity.memory;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.ScrollingMovementMethod;
import android.text.style.TextAppearanceSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PopupMenuUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.totwoo.totwoo.widget.PageWidget;
import com.totwoo.totwoo.widget.SceneAnimation;
import com.totwoo.totwoo.widget.scaleVideoView.ScalableType;
import com.totwoo.totwoo.widget.scaleVideoView.ScalableVideoView;
import com.umeng.analytics.MobclickAgent;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 时光记忆入口页面
 */

public class MemoryPageActivity extends BaseActivity implements SubscriberListener, ViewPager.OnPageChangeListener {
    private static final String FIRST_ADD_CLICKED = "FIRST_ADD_CLICKED";
    public static final String IS_OPENED = "isOpened";
    private ArrayList<MemoryBean> memoryList = new ArrayList<>();
    private MemoryPageAdapter memoryPageAdapter;
    private int nextPage = 0;
    private int totalCount = 0;

    @BindView(R.id.shared_layout)
    public View sharedView;

    @BindView(R.id.activity_memory_page_layout1)
    public View parentView;

    @BindView(R.id.page_vp)
    public ViewPager vp;

    @BindView(R.id.btn_iv)
    public ImageView iv;

    @BindView(R.id.btn_layout)
    public View btnLayout;

    @BindView(R.id.memory_list_empty)
    public LinearLayout emptyLayout;

    @BindView(R.id.empty_photo_tv)
    public View emptyPhotoTv;

    @BindView(R.id.activity_memory_list_empty_diary)
    public LinearLayout emptyDiary;

    @BindView(R.id.activity_memory_list_empty_photo)
    public LinearLayout emptyPhoto;

    @BindView(R.id.activity_memory_list_empty_voice)
    public LinearLayout emptyVoice;

    @BindView(R.id.activity_memory_list_empty_vedio)
    public LinearLayout emptyVedio;

    @BindView(R.id.activity_memory_list_empty_iv)
    public ImageView emptyIv;

    @BindView(R.id.first_add_layout)
    public View firstAddView;

    @BindView(R.id.first_add_iv)
    public ImageView firstAddIv;


    private ShakeMonitor mShakeMonitor;

    @BindView(R.id.activity_memory_page_layout)
    public View layout;

    @BindView(R.id.activity_memory_list_enter_layout)
    public View enterLayout;

    @BindView(R.id.memory_enter_close)
    public TextView close;

    @BindView(R.id.memory_enter_logo)
    public ImageView logo;

    @BindView(R.id.memory_enter_tap)
    public ImageView tap;

    @BindView(R.id.memory_enter_diary)
    public ImageView diary;

    @BindView(R.id.page_view)
    public PageWidget pageWidget;

    private FacebookCallback<Sharer.Result> facebookCallback;

    private NewUserGiftDialog newUserGiftDialog;

    private void initViews() {
        if (ToTwooApplication.isDebug) {
            enterLayout.setVisibility(View.GONE);
        }
        if (Apputils.systemLanguageIsChinese(this)) {
            logo.setImageResource(R.drawable.memory_enter_logo_ch);
            tap.setImageResource(R.drawable.memory_enter_tap_ch);
            diary.setImageResource(R.drawable.memory_enter_diary_ch);
            firstAddIv.setImageResource(R.drawable.first_grid_ch);
        } else {
            logo.setImageResource(R.drawable.memory_enter_logo_en);
            tap.setImageResource(R.drawable.memory_enter_tap_en);
            diary.setImageResource(R.drawable.memory_enter_diary_en);
            firstAddIv.setImageResource(R.drawable.first_grid_en);
        }
    }

    private void initAnims() {
        final Animation a1 = AnimationUtils.loadAnimation(this, R.anim.alaph_to20);
        final Animation a2 = AnimationUtils.loadAnimation(this, R.anim.alaph_to100);
        a1.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                tap.startAnimation(a2);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        a2.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                tap.startAnimation(a1);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        tap.startAnimation(a1);
    }

    private void initShakeListener() {
        mShakeMonitor = new ShakeMonitor(this);
        mShakeMonitor.isEnablePhoneShake(false);

        // 设置摇首饰的监听
        mShakeMonitor.setOnEventListener(type -> {
            if (!NetUtils.isConnected(MemoryPageActivity.this)) {
                ToastUtils.showShort(MemoryPageActivity.this, R.string.error_net);
                return;
            }

            AlphaAnimation alphaAnimation = new AlphaAnimation(1.0f, 0);
            alphaAnimation.setFillAfter(false);
            alphaAnimation.setDuration(1500);
            alphaAnimation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    enterLayout.setVisibility(View.GONE);
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
            enterLayout.startAnimation(alphaAnimation);
        });
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mShakeMonitor != null)
            mShakeMonitor.stop();
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (mShakeMonitor != null) {
            mShakeMonitor.start();
        }

    }

    boolean wahaha = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_page);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        CommonUtils.setStateBar(this, false);

        Intent i = getIntent();
        initViews();
        if (i.getBooleanExtra(IS_OPENED, false)) {
            enterLayout.setVisibility(View.GONE);
        } else {
            initShakeListener();
            initAnims();
        }

        wahaha = i.getBooleanExtra("wahaha", false);
        if (wahaha) {
            showAddView();
        }

        BluetoothManage.getInstance().connectedStatus();

        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MemoryPageActivity.this.finish();
            }
        });

        MemoryController.getInstance().getList(nextPage);

        initShare();
    }

    private String getMemoryPath() {
        File f = new File(CommonArgs.MEMORY_SHARE_IMAGE);
        if (!f.exists()) {
            try {
                BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.memory_share_icon).compress(Bitmap.CompressFormat.JPEG, 100, new FileOutputStream(new File(CommonArgs.MEMORY_SHARE_IMAGE)));
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }
//         = FileUtils.saveBitmapFromSDCard(BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.memory_share_icon),
//                "totwoo_cache_img_" + System.currentTimeMillis());
        return f.getPath();
    }

    private void initShare() {


        facebookCallback = new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                ToastUtils.showShort(MemoryPageActivity.this, getResources().getString(R.string.share_complete));
            }

            @Override
            public void onCancel() {
                ToastUtils.showShort(MemoryPageActivity.this, getResources().getString(R.string.share_cancel));
            }

            @Override
            public void onError(FacebookException error) {
                ToastUtils.showShort(MemoryPageActivity.this, getResources().getString(R.string.share_error));
            }
        };

        if (Apputils.systemLanguageIsChinese(MemoryPageActivity.this)) {
            findViewById(R.id.common_share_title_tv).setVisibility(View.VISIBLE);
            ((TextView) findViewById(R.id.common_share_title_tv)).setText(CommonUtils.setNumberGoldenSpan(getResources().getString(R.string.share_text_head_info), 88, 16));
        } else {
            findViewById(R.id.common_share_title_tv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_facebook_iv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_twitter_iv).setVisibility(View.VISIBLE);
            findViewById(R.id.common_share_qq_iv).setVisibility(View.GONE);
            findViewById(R.id.common_share_qzone_iv).setVisibility(View.GONE);
            findViewById(R.id.common_share_weibo_iv).setVisibility(View.GONE);
        }

        newUserGiftDialog = new NewUserGiftDialog(MemoryPageActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_SHARE_LUCKY_CLICK);
                WebViewActivity.loadUrl(MemoryPageActivity.this, HttpHelper.URL_GIFT, false);
                newUserGiftDialog.dismiss();
            }
        }, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                newUserGiftDialog.dismiss();
            }
        }, CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券", 88, 20), "立即抽奖");

        findViewById(R.id.common_share_friend_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareUrlToWechatMoment(getString(R.string.share_memory_title), getString(R.string.share_memory_content), getMemoryPath(), getShareUrl());
            }
        });
        findViewById(R.id.common_share_wechat_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareUrlToWechat(getString(R.string.share_memory_title), getString(R.string.share_memory_content), getMemoryPath(), getShareUrl());
            }
        });

        findViewById(R.id.common_share_qq_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareUrlToQQ(getString(R.string.share_memory_title), getString(R.string.share_memory_content), getMemoryPath(), getShareUrl());
            }
        });
        findViewById(R.id.common_share_qzone_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareUrlToQzone(getString(R.string.share_memory_title), getString(R.string.share_memory_content), getMemoryPath(), getShareUrl());
            }
        });

        findViewById(R.id.common_share_weibo_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareUrlToWeibo(MemoryPageActivity.this, getString(R.string.share_memory_title), getMemoryPath(), getShareUrl());
            }
        });
        findViewById(R.id.common_share_facebook_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareUrlToFacebook(getString(R.string.share_memory_title), getShareUrl(), MemoryPageActivity.this, facebookCallback);
            }
        });
        findViewById(R.id.common_share_twitter_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareUrlToTwitter(getString(R.string.share_memory_title), getMemoryPath(), getShareUrl());
            }
        });
    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        newUserGiftDialog.show();
    }

    @EventInject(eventType = S.E.E_MEMORY_GET_LIST_SUCCESSED, runThread = TaskType.UI)
    public void onGetMemoryListSuccessed(EventData data) {
        HttpValues hv = (HttpValues) data;
        ArrayList<MemoryBean> list = (ArrayList<MemoryBean>) hv.getUserDefine("MemoryBeanList");
        totalCount = (int) hv.getUserDefine("count");
        LogUtils.e("list.size:" + list.size());
        if ((list == null || list.size() == 0) && nextPage == 0) {
            boolean res = PreferencesUtils.getBoolean(this, FIRST_ADD_CLICKED, false);
            if (res == false) {
                emptyPhotoTv.setVisibility(View.INVISIBLE);
                firstAddView.setVisibility(View.VISIBLE);
            }
            showAddView();
            return;
        } else {
            if (wahaha)
                wahaha = false;
            else {
                getTopRightIcon().setVisibility(View.VISIBLE);
                getTopRight2Icon().setVisibility(View.VISIBLE);
            }
        }

        if (list == null || list.size() == 0) {
            ToastUtils.showShort(this, R.string.memory_list_no);
            return;
        }

        for (int i = 0; i < list.size(); i++)
            memoryList.add(list.get(i));
        if (memoryPageAdapter == null) {
            memoryPageAdapter = new MemoryPageAdapter(this);
            vp.setAdapter(memoryPageAdapter);
            vp.addOnPageChangeListener(this);
        }
        memoryPageAdapter.notifyDataSetChanged();
        nextPage++;
    }

    private void showAddView() {
        emptyLayout.setVisibility(View.VISIBLE);
        getTopRightIcon().setVisibility(View.GONE);
        getTopRight2Icon().setVisibility(View.GONE);
        layout.setVisibility(View.GONE);
        //enterLayout.setVisibility(View.GONE);
        if (Apputils.systemLanguageIsChinese(this))
            emptyIv.setImageResource(R.drawable.memory_list_paper_ch);
        else
            emptyIv.setImageResource(R.drawable.memory_list_paper_en);
    }

    @EventInject(eventType = S.E.E_MEMORY_LIST_SWITCH, runThread = TaskType.UI)
    public void onMemorySwitch(EventData data) {
        HttpValues hv = (HttpValues) data;
        ArrayList<MemoryBean> list = (ArrayList<MemoryBean>) hv.getUserDefine("list");
        int count = (int) hv.getUserDefine("count");
        int page = (int) hv.getUserDefine("page");
        int pos = (int) hv.getUserDefine("pos");

        totalCount = count;
        nextPage = page;
        memoryList.clear();
        for (int i = 0; i < list.size(); i++)
            memoryList.add(list.get(i));

        memoryPageAdapter.notifyDataSetChanged();
        vp.setCurrentItem(pos);
    }

    @EventInject(eventType = S.E.E_MEMORY_GET_LIST_FAILED, runThread = TaskType.UI)
    public void onGetMemoryListFailed(EventData data) {
        ToastUtils.showShort(this, R.string.error_net);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    private int currPosition = 0;

    @Override
    public void onPageSelected(int position) {
        currPosition = position;
        try {
            if (mScalableVideoView != null) {
                if (mScalableVideoView.isPlaying()) {
                    mScalableVideoView.pause();
                    mScalableVideoView = null;
                }
            }
        } catch (Exception e) {
            //e.printStackTrace();
        }

        try {
            if (mPlayer != null) {
                if (mPlayer.isPlaying()) {
                    mPlayer.pause();
                    sa.stop();
                    mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (memoryList.size() == totalCount)
            return;
        if (position == memoryList.size() - 1)
            MemoryController.getInstance().getList(nextPage);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    protected void initTopBar() {
        setTopbarBackground(R.color.layer_bg_white);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopTitle(getString(R.string.memory_list_title1));
        setTopRightGone();
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MemoryPageActivity.this.finish();
            }
        });

        ImageView rightIcon = getTopRightIcon();
        ImageView right2Icon = getTopRight2Icon();

//        if (Apputils.systemLanguageIsChinese(MemoryPageActivity.this)) {
//            right2Icon.setImageResource(R.drawable.icon_share_gift);
//        } else {
//            right2Icon.setImageResource(R.drawable.share_ico_2);
//        }
        rightIcon.setImageResource(R.drawable.memory_page_more);

//        right2Icon.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (memoryList.size() == 0)
//                    return;
//                if (!PermissionUtil.hasStoragePermission(MemoryPageActivity.this)) {
//                    return;
//                }
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_SHARE_CLICK);
//                sharedView.setVisibility(View.VISIBLE);
//            }
//        });

        rightIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LogUtils.e("onRightIconClicked");
                if (memoryList.size() == 0)
                    return;
                getPhotoDialog(memoryList.get(currPosition));
            }
        });

        if (wahaha) {
            rightIcon.setVisibility(View.GONE);
            right2Icon.setVisibility(View.GONE);
        }
    }

    private boolean isPopupWindowShowing = false;

    @OnClick({R.id.btn_layout, R.id.activity_memory_list_empty_diary, R.id.activity_memory_list_empty_photo,
            R.id.activity_memory_list_empty_vedio, R.id.activity_memory_list_empty_voice, R.id.shared_layout, R.id.first_add_layout})
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.first_add_layout:
                PreferencesUtils.put(this, FIRST_ADD_CLICKED, true);
                firstAddView.setVisibility(View.GONE);
                emptyPhotoTv.setVisibility(View.VISIBLE);
                break;
            case R.id.shared_layout:
                sharedView.setVisibility(View.GONE);
                break;
            case R.id.btn_layout:
                if (PermissionUtil.hasStoragePermission(MemoryPageActivity.this)) {
                    PopupMenuUtil.getInstance()._show(this, this, iv);
                    isPopupWindowShowing = true;
                }
                break;
            case R.id.activity_memory_list_empty_diary:
                onSayClicked();
                break;
            case R.id.activity_memory_list_empty_photo:
                onPhotoClicked();
                break;
            case R.id.activity_memory_list_empty_voice:
                onVoiceClicked();
                break;
            case R.id.activity_memory_list_empty_vedio:
                onVedioClicked();
                break;
        }
    }

    private String getShareUrl() {
        StringBuffer shareUrl = new StringBuffer("http://m.totwoo.cn/share/memory.php?uid=");
        shareUrl.append(ToTwooApplication.owner.getTotwooId());
        MemoryBean mb = memoryList.get(vp.getCurrentItem());
        switch (mb.memory_type) {
            case 1://纯文字不传type
                shareUrl.append("&type=txt");
                break;
            case 2:
                shareUrl.append("&type=img");
                break;
            case 3:
                shareUrl.append("&type=audio");
                break;
            case 4:
                shareUrl.append("&type=video");
                break;
        }
        shareUrl.append("&tid=").append(mb.memory_id);
        shareUrl.append("&state=").append(Apputils.systemLanguageIsChinese(this) ? "cn" : "en");
        return shareUrl.toString();
    }

    private void onSayClicked() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_INSER_TEXT_CLICK);
        startActivity(new Intent(this, MemorySayActivity.class));
        this.finish();
    }

    private void onPhotoClicked() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_INSER_PHOTO_CLICK);
        if (PermissionUtil.hasCameraPermission(MemoryPageActivity.this)) {
            startActivity(new Intent(this, MemoryPhotoSelectActivity.class));
            this.finish();
        }
    }

    private void onVoiceClicked() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_INSER_VOICE_CLICK);
        if (PermissionUtil.hasAudioPermission(MemoryPageActivity.this)) {
            startActivity(new Intent(this, MemoryAudioActivity.class));
            this.finish();
        }
//        if (!CommonUtils.HasRecordSoundPermission(MemoryPageActivity.this))
//        {
//            final CustomDialog dialog = new CustomDialog(this);
//            dialog.setMessage(R.string.open_audio_error);
//            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener()
//            {
//                @Override
//                public void onClick(View v)
//                {
//                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
//                    dialog.dismiss();
//                }
//            });
//            dialog.show();
//            return;
//        }
    }

    private void onVedioClicked() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MEMORY_INSER_VIDEO_CLICK);
//        if (!CommonUtils.isCameraCanUse())
//        {
//            final CustomDialog dialog = new CustomDialog(this);
//            dialog.setMessage(R.string.open_camera_error1);
//            dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener()
//            {
//                @Override
//                public void onClick(View v)
//                {
//                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
//                    dialog.dismiss();
//                }
//            });
//            dialog.show();
//            return;
//        }
        if (!PermissionUtil.hasCameraPermission(MemoryPageActivity.this)) {
            return;
        }
        startActivity(new Intent(this, MemoryVedioActivity.class));
        this.finish();
    }

    @Override
    public void onBackPressed() {
        if (isPopupWindowShowing) {
            PopupMenuUtil.getInstance()._rlClickAction();
            isPopupWindowShowing = false;
        } else if (sharedView.getVisibility() == View.VISIBLE) {
            sharedView.setVisibility(View.GONE);
        } else
            super.onBackPressed();
    }

    private ScalableVideoView mScalableVideoView;
    private MediaPlayer mPlayer;
    private SceneAnimation sa;
    private ImageView mAudioVideoPlayBtn;

    private class MemoryPageAdapter extends PagerAdapter {
        private LayoutInflater layoutInflater;

        public MemoryPageAdapter(Context context) {
            layoutInflater = LayoutInflater.from(context);
        }

        @Override
        public int getCount() {
            return memoryList.size();
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            if (memoryList.size() - 1 >= position) {
                MemoryBean mb = memoryList.get(position);
                switch (mb.memory_type) {
                    case 1:
                        return getDiaryView(mb, container);
                    case 2:
                        switch (mb.img_url.length) {
                            case 1:
                                return getOnePhotoView(mb, container);
                            case 2:
                                return getTwoPhotoView(mb, container);
                            case 3:
                                return getThreePhotoView(mb, container);
                            case 4:
                                return getFourPhotoView(mb, container);
                            default:
                                return getMorePhotoView(mb, container);
                        }
                    case 3:
                        return getAudioView(mb, container);
                    case 4:
                        return getVedioView(mb, container);
                    default:
                        break;
                }
            }

            return null;
        }

        private View getDiaryView(MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_diary, null);
            TextView tv = (TextView) view.findViewById(R.id.memory_say_tx);
            tv.setMovementMethod(ScrollingMovementMethod.getInstance());
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            SpannableString styledText = new SpannableString(mb.content);
            try {
                styledText.setSpan(new TextAppearanceSpan(MemoryPageActivity.this, R.style.memory_say_text_style_big), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                styledText.setSpan(new TextAppearanceSpan(MemoryPageActivity.this, R.style.memory_say_text_style_small), 1, mb.content.length() - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            } catch (Exception e) {
                e.printStackTrace();
            }
            tv.setText(styledText, TextView.BufferType.SPANNABLE);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);

            container.addView(view);
            return view;
        }

        private View getOnePhotoView(final MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_photo_one, null);
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(MemoryPageActivity.this, MemoryPhotoShowActivity.class);
                    intent.putExtra(S.M.M_SINGLE, false);
                    intent.putExtra(S.M.M_IMAGES, mb);
                    MemoryPageActivity.this.startActivity(intent);
                }
            });
            ImageView photo1 = (ImageView) view.findViewById(R.id.memory_page_photo_one);
            TextView content = (TextView) view.findViewById(R.id.memory_page_photo_bottom_content);
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            LogUtils.e("aab mb.img_url[0] = " + mb.img_url[0]);
            int degree = BitmapHelper.readPictureDegree(mb.img_url[0]);
            LogUtils.e("aab degree = " + degree);
            BitmapHelper.display(MemoryPageActivity.this, photo1, mb.img_url[0], R.drawable.memory_bg);
            content.setText(mb.content);
            content.setMaxHeight(CommonUtils.dip2px(MemoryPageActivity.this, 86));
            content.setMovementMethod(ScrollingMovementMethod.getInstance());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);

            container.addView(view);
            return view;
        }

        private View getTwoPhotoView(final MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_photo_two, null);
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(MemoryPageActivity.this, MemoryPhotoShowActivity.class);
                    intent.putExtra(S.M.M_SINGLE, false);
                    intent.putExtra(S.M.M_IMAGES, mb);
                    MemoryPageActivity.this.startActivity(intent);
                }
            });
            ImageView photo1 = (ImageView) view.findViewById(R.id.memory_page_photo_one);
            ImageView photo2 = (ImageView) view.findViewById(R.id.memory_page_photo_two);
            TextView content = (TextView) view.findViewById(R.id.memory_page_photo_bottom_content);
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            BitmapHelper.display(MemoryPageActivity.this, photo1, mb.img_url[0], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo2, mb.img_url[1], R.drawable.memory_bg, 560);
            content.setText(mb.content);
            content.setMaxHeight(CommonUtils.dip2px(MemoryPageActivity.this, 86));
            content.setMovementMethod(ScrollingMovementMethod.getInstance());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);

            container.addView(view);
            return view;
        }

        private View getThreePhotoView(final MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_photo_three, null);
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(MemoryPageActivity.this, MemoryPhotoShowActivity.class);
                    intent.putExtra(S.M.M_SINGLE, false);
                    intent.putExtra(S.M.M_IMAGES, mb);
                    MemoryPageActivity.this.startActivity(intent);
                }
            });
            ImageView photo1 = (ImageView) view.findViewById(R.id.memory_page_photo_one);
            ImageView photo2 = (ImageView) view.findViewById(R.id.memory_page_photo_two);
            ImageView photo3 = (ImageView) view.findViewById(R.id.memory_page_photo_three);
            TextView content = (TextView) view.findViewById(R.id.memory_page_photo_bottom_content);
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            BitmapHelper.display(MemoryPageActivity.this, photo1, mb.img_url[0], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo2, mb.img_url[1], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo3, mb.img_url[2], R.drawable.memory_bg, 560);
            content.setText(mb.content);
            content.setMaxHeight(CommonUtils.dip2px(MemoryPageActivity.this, 86));
            content.setMovementMethod(ScrollingMovementMethod.getInstance());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);

            container.addView(view);
            return view;
        }

        private View getFourPhotoView(final MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_photo_four, null);
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(MemoryPageActivity.this, MemoryPhotoShowActivity.class);
                    intent.putExtra(S.M.M_SINGLE, false);
                    intent.putExtra(S.M.M_IMAGES, mb);
                    MemoryPageActivity.this.startActivity(intent);
                }
            });

            ImageView photo1 = (ImageView) view.findViewById(R.id.memory_page_photo_one);
            ImageView photo2 = (ImageView) view.findViewById(R.id.memory_page_photo_two);
            ImageView photo3 = (ImageView) view.findViewById(R.id.memory_page_photo_three);
            ImageView photo4 = (ImageView) view.findViewById(R.id.memory_page_photo_four);
            TextView content = (TextView) view.findViewById(R.id.memory_page_photo_bottom_content);
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            BitmapHelper.display(MemoryPageActivity.this, photo1, mb.img_url[0], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo2, mb.img_url[1], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo3, mb.img_url[2], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo4, mb.img_url[3], R.drawable.memory_bg, 560);
            content.setText(mb.content);
            content.setMaxHeight(CommonUtils.dip2px(MemoryPageActivity.this, 86));
            content.setMovementMethod(ScrollingMovementMethod.getInstance());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);

            container.addView(view);
            return view;
        }

        private View getMorePhotoView(final MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_photo_four, null);
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(MemoryPageActivity.this, MemoryPhotoShowActivity.class);
                    intent.putExtra(S.M.M_SINGLE, false);
                    intent.putExtra(S.M.M_IMAGES, mb);
                    MemoryPageActivity.this.startActivity(intent);
                }
            });

            TextView num = (TextView) view.findViewById(R.id.memory_page_photo_four_num);
            ImageView photo1 = (ImageView) view.findViewById(R.id.memory_page_photo_one);
            ImageView photo2 = (ImageView) view.findViewById(R.id.memory_page_photo_two);
            ImageView photo3 = (ImageView) view.findViewById(R.id.memory_page_photo_three);
            ImageView photo4 = (ImageView) view.findViewById(R.id.memory_page_photo_four);
            TextView content = (TextView) view.findViewById(R.id.memory_page_photo_bottom_content);
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            BitmapHelper.display(MemoryPageActivity.this, photo1, mb.img_url[0], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo2, mb.img_url[1], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo3, mb.img_url[2], R.drawable.memory_bg, 560);
            BitmapHelper.display(MemoryPageActivity.this, photo4, mb.img_url[3], R.drawable.memory_bg, 560);
            num.setVisibility(View.VISIBLE);
            num.setText(mb.img_url.length + "p");
            content.setText(mb.content);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);

            container.addView(view);
            return view;
        }

        private View getAudioView(final MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_audio, null);
            final ImageView audioGif = (ImageView) view.findViewById(R.id.audio_gif);
            final ImageView mAudioVideoPlayBtn = (ImageView) view.findViewById(R.id.make_card_audio_play_btn);
            final FrameLayout layout = (FrameLayout) view.findViewById(R.id.audio_layout);

            TextView content = (TextView) view.findViewById(R.id.memory_page_photo_bottom_content);
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            content.setText(mb.content);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);
            container.addView(view);

            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        final SceneAnimation sa = new SceneAnimation(audioGif, MemoryAudioActivity.gifs, 10, false);
                        final MediaPlayer mPlayer = new MediaPlayer();
                        MemoryPageActivity.this.mPlayer = mPlayer;
                        MemoryPageActivity.this.sa = sa;
                        MemoryPageActivity.this.mAudioVideoPlayBtn = mAudioVideoPlayBtn;
                        mPlayer.setDataSource(MemoryPageActivity.this, Uri.parse(mb.audio_url));
                        mPlayer.prepare();
                        layout.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (mPlayer.isPlaying()) {
                                    mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                                    mPlayer.pause();
                                    sa.stop();
                                }
                            }
                        });

                        mAudioVideoPlayBtn.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                mPlayer.start();
                                mAudioVideoPlayBtn.setVisibility(View.GONE);
                                sa.start();
                                mPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                                    @Override
                                    public void onCompletion(MediaPlayer mp) {
                                        mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                                        sa.stop();
                                    }
                                });
                            }
                        });
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }, 1000);

            return view;
        }

        private View getVedioView(final MemoryBean mb, ViewGroup container) {
            View view = layoutInflater.inflate(R.layout.memory_page_vedio, null);
            final ScalableVideoView mScalableVideoView = (ScalableVideoView) view.findViewById(R.id.make_card_video_view);
            MemoryPageActivity.this.mScalableVideoView = mScalableVideoView;
            final ImageView mMakeCardTopCoverIv = (ImageView) view.findViewById(R.id.make_card_top_conver_layer);
            final ImageView mAudioVideoPlayBtn = (ImageView) view.findViewById(R.id.make_card_audio_play_btn);

            TextView content = (TextView) view.findViewById(R.id.memory_page_photo_bottom_content);
            TextView year = (TextView) view.findViewById(R.id.memory_page_photo_bottom_yearmonth);
            TextView day = (TextView) view.findViewById(R.id.memory_page_photo_bottom_day);

            content.setText(mb.content);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
            String res = sdf.format(new Date(mb.create_time));
            year.setText(res);
            sdf = new SimpleDateFormat("dd");
            res = sdf.format(new Date(mb.create_time));
            day.setText(res);

            container.addView(view);

            mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
            BitmapHelper.display(MemoryPageActivity.this, mMakeCardTopCoverIv, mb.cover_url);

            try {
                mScalableVideoView.setDataSource(mb.vedio_url);
                mScalableVideoView.setScalableType(ScalableType.CENTER_CROP);
                mScalableVideoView.prepare();
                mScalableVideoView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mScalableVideoView.isPlaying()) {
                            mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                            mMakeCardTopCoverIv.setVisibility(View.VISIBLE);
                            mScalableVideoView.pause();
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }

            mAudioVideoPlayBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mScalableVideoView.start();
                    mScalableVideoView.setVisibility(View.VISIBLE);
                    mMakeCardTopCoverIv.setVisibility(View.GONE);
                    mAudioVideoPlayBtn.setVisibility(View.GONE);
                    mScalableVideoView.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                        @Override
                        public void onCompletion(MediaPlayer mp) {
                            mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                        }
                    });
                }
            });
            return view;
        }
    }

    CustomDialog dialog;

    public void getPhotoDialog() {
        dialog = new CustomDialog(this);
        dialog.setTitle("");
        dialog.setMessage(R.string.memory_delete);
        dialog.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        dialog.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }


    public void getPhotoDialog(final MemoryBean mb) {
        dialog = new CustomDialog(this);
        LinearLayout modify_head_dialog_ll = new LinearLayout(this);
        modify_head_dialog_ll.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        modify_head_dialog_ll.setOrientation(LinearLayout.VERTICAL);
        TextView order_tv = new TextView(this);
        TextView delete_tv = new TextView(this);
        modify_head_dialog_ll.addView(order_tv);
        modify_head_dialog_ll.addView(delete_tv);

        dialog.setMainLayoutView(modify_head_dialog_ll);
        order_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        delete_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        order_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20), Apputils.dp2px(this, 15));
        delete_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20), Apputils.dp2px(this, 15));
        order_tv.setBackgroundResource(R.drawable.item_bg);
        delete_tv.setBackgroundResource(R.drawable.item_bg);
        Drawable drawable1 = getResources().getDrawable(R.drawable.memory_page_flip);
        Drawable drawable2 = getResources().getDrawable(R.drawable.delete);
        order_tv.setCompoundDrawablesWithIntrinsicBounds(drawable1, null, null, null);
        order_tv.setCompoundDrawablePadding(Apputils.dp2px(this, 9));
        delete_tv.setCompoundDrawablesWithIntrinsicBounds(drawable2, null, null, null);
        delete_tv.setCompoundDrawablePadding(Apputils.dp2px(this, 9));
        order_tv.setText(getString(R.string.switch_list_mode));
        delete_tv.setText(getString(R.string.delete));
        order_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        delete_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        order_tv.setTextSize(16);
        delete_tv.setTextSize(16);
        dialog.setNegativeButtonText(R.string.cancel);
        order_tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MemoryPageActivity.this, MemoryListActivity.class);
                intent.putExtra(IS_OPENED, true);
                MemoryPageActivity.this.startActivity(intent);

                dialog.dismiss();
            }
        });

        delete_tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //MemoryController.getInstance().delete(mb);
                getPhotoDialog1(mb);
                dialog.dismiss();
            }
        });

        dialog.show();
    }

    CustomDialog dialog1;

    public void getPhotoDialog1(final MemoryBean mb) {
        dialog1 = new CustomDialog(this);
        dialog1.setTitle("");
        dialog1.setMessage(R.string.memory_delete);
        dialog1.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MemoryController.getInstance().delete(mb);
            }
        });
        dialog1.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog1.dismiss();
            }
        });
        dialog1.show();
    }


    @SuppressLint("WrongConstant")
    @EventInject(eventType = S.E.E_MEMORY_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void onDeleteMemorySuccessed(EventData data) {
        ToastUtils.showShort(this, R.string.memory_delete_success);
        if (dialog != null)
            dialog.dismiss();
        if (dialog1 != null)
            dialog1.dismiss();
        //initListTitle();
        memoryList.remove(currPosition);
        memoryPageAdapter.notifyDataSetChanged();
        LogUtils.e("memoryList.size:" + memoryList.size());
        if (memoryList.size() == 0) {
            LogUtils.e("emptyLayout.visiblity:" + emptyLayout.getVisibility());
            nextPage = 0;
            layout.setVisibility(View.GONE);
            emptyLayout.setVisibility(View.VISIBLE);
            getTopRightIcon().setVisibility(View.GONE);
            getTopRight2Icon().setVisibility(View.GONE);
            LogUtils.e("emptyLayout.visiblity:" + emptyLayout.getVisibility());
        } else {
            getTopRightIcon().setVisibility(View.VISIBLE);
            getTopRight2Icon().setVisibility(View.VISIBLE);
        }
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_FAILED, runThread = TaskType.UI)
    public void onDeleteMemoryFailed(EventData data) {
        HttpValues hv = (HttpValues) data;
        String msg = hv.errorMesg;
        ToastUtils.showShort(this, msg);
        if (dialog != null)
            dialog.dismiss();
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_SUCCESSED, runThread = TaskType.UI)
    public void onSaveSuccessed(EventData data) {
        this.finish();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
