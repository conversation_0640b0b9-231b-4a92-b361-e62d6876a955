package com.totwoo.totwoo.activity;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.Qian;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.fragment.QianDetailPage;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.Calendar;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * App的整体分享界面
 *
 * <AUTHOR>
 * @date 2015年10月8日
 */
public class QianShareActivity extends BaseActivity implements SubscriberListener {
    /**
     * 分享的布局文件
     */
    @BindView(R.id.share_content_layout)
    LinearLayout shareContentLayout;
    /**
     * 用户头像
     */
    @BindView(R.id.share_head_icon_image)
    ImageView userHeadImg;
    /**
     * 用户昵称
     */
    @BindView(R.id.share_user_name_tv)
    TextView userNickNme;
    /**
     * 当前日期
     */
    @BindView(R.id.share_date_tv)
    TextView dateTv;

    @BindView(R.id.qian_detail_page_layout)
    QianDetailPage qianDetailPage;

    @BindView(R.id.share_title_info_tv)
    TextView mShareTitleInfo;

    private int qianType;

    private FacebookCallback<Sharer.Result> facebookCallback;

    private NewUserGiftDialog newUserGiftDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState){
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qian_share);
        ButterKnife.bind(this);

        InjectUtils.injectOnlyEvent(this);

        qianType = getIntent().getIntExtra(QianDetailActivity.QIAN_TYPE_TAG, 0);

        initData();

        facebookCallback = new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                ToastUtils.showShort(QianShareActivity.this,getResources().getString(R.string.share_complete));
            }

            @Override
            public void onCancel() {
                ToastUtils.showShort(QianShareActivity.this,getResources().getString(R.string.share_cancel));
            }

            @Override
            public void onError(FacebookException error) {
                ToastUtils.showShort(QianShareActivity.this,getResources().getString(R.string.share_error));
            }
        };
        if(Apputils.systemLanguageIsChinese(QianShareActivity.this)){
            findViewById(R.id.common_share_title_tv).setVisibility(View.VISIBLE);
            ((TextView)findViewById(R.id.common_share_title_tv)).setText(CommonUtils.setNumberGoldenSpan(getResources().getString(R.string.share_text_head_info),88,16));
        }
        newUserGiftDialog = new NewUserGiftDialog(QianShareActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(getIntent().getIntExtra(CommonArgs.FROM_TYPE,1) == 1){
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_YESORNO_LUCKY_CLICK);
                }else{
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_YESORNO_LUCKY_CLICK);
                }
                WebViewActivity.loadUrl(QianShareActivity.this, HttpHelper.URL_GIFT, false);
                newUserGiftDialog.dismiss();
            }
        }, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                newUserGiftDialog.dismiss();
            }
        },CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券",88,20),"立即抽奖");
        findViewById(R.id.common_share_friend_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath());
            }
        });
        findViewById(R.id.common_share_wechat_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWechat(getPath());
            }
        });

        findViewById(R.id.common_share_qq_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToQQ(getPath());
            }
        });
        findViewById(R.id.common_share_qzone_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToQzone(getPath(),getString(R.string.share_qian_title_info));
            }
        });

        findViewById(R.id.common_share_weibo_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToWeibo(QianShareActivity.this,getPath(),getString(R.string.share_qian_title_info));
            }
        });
        findViewById(R.id.common_share_facebook_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToFacebook(getPath(),QianShareActivity.this,facebookCallback);
            }
        });
        findViewById(R.id.common_share_twitter_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareUtilsSingleton.getInstance().shareImageToTwitter(getPath(),getString(R.string.share_qian_title_info));
            }
        });

        if(BleParams.isSecurityJewlery()){
            mShareTitleInfo.setText(R.string.safe_share_qian_title_info);
        }else{
            mShareTitleInfo.setText(R.string.share_qian_title_info);
        }
    }

    private String getPath(){
        Bitmap snapShareBitmap = ShareUtilsSingleton.getBitmapByView(shareContentLayout);
        return FileUtils.saveBitmapFromSDCard(snapShareBitmap,
                "totwoo_cache_img_" + System.currentTimeMillis());
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
    }

    /**
     * 初始化相应的数据
     */
    private void initData() {
        // 日期
        Calendar cal = Calendar.getInstance();

        dateTv.setText(getResources().getStringArray(R.array.month_names_en)[cal
                .get(Calendar.MONTH)] + " " + cal.get(Calendar.DAY_OF_MONTH) + ", "
                + cal.get(Calendar.YEAR));

        // 个人信息
        if (!TextUtils.isEmpty(ToTwooApplication.owner.getHeaderUrl())) {
            BitmapHelper.display(this, userHeadImg,
                    ToTwooApplication.owner.getHeaderUrl());
        }
        userNickNme.setText(ToTwooApplication.owner.getNickName());

        qianDetailPage.setQian((Qian) ACache.get(QianShareActivity.this).getAsObject(CommonArgs.CACHE_QIAN + qianType));
    }

    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        newUserGiftDialog.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
