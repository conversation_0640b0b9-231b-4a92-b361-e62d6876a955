package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.Step;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.StepController;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.StepUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.StepDataTable;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class StepSettingActivity extends BaseActivity implements SubscriberListener {
    private static final int STEPTARGETSETTING = 1;
    @BindView(R.id.step_set_desc)
    TextView mStepDesc;

    JewelryNotifyModel nowSetModel;

    @BindView(R.id.long_vibration_tv)
    TextView mLongVibrationTv;
    @BindView(R.id.short_vibration_tv)
    TextView mShortVibrationTv;
    @BindView(R.id.sedentary_reminder_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    @BindView(R.id.call_switch_title_tv)
    TextView call_switch_title_tv;
    @BindView(R.id.call_switch_cb)
    CheckBox call_switch_cb;
    @BindView(R.id.step_setting_content)
    LinearLayout step_setting_content;
    @BindView(R.id.step_set_title)
    TextView mSetStepTitle;
    @BindView(R.id.step_walk_distance_tv)
    TextView walkDisTv;
    @BindView(R.id.step_walk_step_tv)
    TextView stepTv;
    @BindView(R.id.step_walk_calorie_tv)
    TextView calorieTv;
    @BindView(R.id.step_calorie_info_tv)
    TextView calorieInfoTv;
    /**
     * 自定义的步数展示表格视图
     */
    @BindView(R.id.step_data_table)
    StepDataTable tableView;

    /**
     * 最近一周的步数数据
     */
    private List<Integer> stepWeekData;

    /**
     * 需要同步数据的时间戳
     */
    private List<Long> needSynDate;

    /**
     * 最近一周步数的最大值
     */
    private int maxStep;

    /**
     * 今天健步数值
     */
    private int todayStep;

    /**
     * 目标健步数值
     */
    private int stepTarget;

    private CustomColorLibraryAdapter colorLibraryAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_step_setting);
        ButterKnife.bind(this);
        EventBus.getDefault().register(this);
        InjectUtils.injectOnlyEvent(this);

        nowSetModel = NotifyUtil.getStepNotifyModel(this);
        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));

                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));

                setTextColorBtn(false);
                break;
        }
        if (nowSetModel.isNotifySwitch()) {
            call_switch_title_tv
                    .setText(R.string.notify_on);
        } else {
            call_switch_title_tv
                    .setText(R.string.notify_off);
        }

        call_switch_cb.setChecked(nowSetModel.isNotifySwitch());
        if (!nowSetModel.isNotifySwitch() || BleParams.isSecurityJewlery()) {
            step_setting_content.setVisibility(View.GONE);
        }

        int spanCount = BleParams.isCtJewlery() ? 7 : 6;
        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(this, spanCount));
        colorLibraryAdapter = new CustomColorLibraryAdapter(nowSetModel.getFlashColor(), spanCount, false,false);

        colorLibraryAdapter.setOnItemClickListener((adapter, view, position) -> {
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);

            if (colorLibraryBean != null) {
                nowSetModel.setFlashColor(colorLibraryBean.getColor());//颜色名字
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                saveNowModel(false);
            }
        });

        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);

        tableView.setOnSelectChangeListener(this::showDateTitle);
        stepTarget = ToTwooApplication.owner.getWalkTarget();
        StepController.getInstance().getStepList();
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);


//        if (Apputils.systemLanguageIsChinese(StepSettingActivity.this)) {
//            setTopRightIcon(R.drawable.icon_share_gift);
//        } else {
//            setTopRightIcon(R.drawable.share_ico_2);
//        }
//        setTopRightOnClick(v -> {
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LIVE_STEP_SHARE);
//            if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_STEP_CLICK);
//            } else {
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_STEP_CLICK);
//            }
//            if (!PermissionUtil.hasStoragePermission(StepSettingActivity.this)) {
//                return;
//            }
//            Intent intent = new Intent(StepSettingActivity.this, ShareStepActivity.class).putExtra(CommonArgs.FROM_TYPE, getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1));
//
//            int step = 0;
//            if (stepWeekData == null || stepWeekData.size() == 0)
//                step = 0;
//            else
//                step = stepWeekData.get(stepWeekData.size() - 1);
//
//            intent.putExtra("step", step);
//            startActivity(intent);
//
//        });
    }

    /**
     * 注册监听健步数据变化广播
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceive(Step step) {
        if (stepWeekData == null)
            stepWeekData = new ArrayList<>();
        if (stepWeekData.size() == 0)
            stepWeekData.add(0);
        stepWeekData.set(stepWeekData.size() - 1, step.getSteps());
        todayStep = step.getSteps();
//        setStepSituation(todayStep);
        setTodayStepSituation();
        tableView.setStepData(stepWeekData);
        int selectDay = tableView.getSelectDay();
        if (selectDay == 6) {
            showDateTitle(selectDay);
        }
    }

    @OnClick({R.id.notify_switch_click_item, R.id.step_calorie_info_tv, R.id.long_vibration_tv, R.id.short_vibration_tv,
            R.id.step_target_modify_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.notify_switch_click_item:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.showShort(StepSettingActivity.this, R.string.error_jewelry_connect);
                    return;
                }
                boolean checked = !call_switch_cb.isChecked();
                call_switch_cb.setChecked(checked);
                if (checked) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext,TrackEvent.MAGIC_STEP_TURN_ON);
                }
                call_switch_title_tv.setText(checked ? R.string.notify_on : R.string.notify_off);
                //切换时候的动画
                Animation anim = AnimationUtils.loadAnimation(StepSettingActivity.this, checked ? R.anim.layout_open : R.anim.layout_close);
                if (checked && !BleParams.isSecurityJewlery()) {
                    step_setting_content.setVisibility(View.VISIBLE);
                } else {
                    anim.setAnimationListener(new Animation.AnimationListener() {
                        @Override
                        public void onAnimationStart(Animation animation) {
                        }

                        @Override
                        public void onAnimationEnd(Animation animation) {
                            step_setting_content.setVisibility(View.GONE);
                        }

                        @Override
                        public void onAnimationRepeat(Animation animation) {

                        }
                    });
                }
                step_setting_content.startAnimation(anim);
                saveNowModel(true);
                break;
            case R.id.step_calorie_info_tv:
                startActivity(new Intent(StepSettingActivity.this, CalorieActivity.class));
                break;
            case R.id.long_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(true);
                break;
            case R.id.short_vibration_tv:
                nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_vibration_unselect_bg));
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                saveNowModel(false);
                setTextColorBtn(false);
                break;
            case R.id.step_target_modify_tv:
                if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_STEP_TRACK_CLICK);
                } else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_STEP_TRACK_CLICK);
                }
                startActivityForResult(new Intent(StepSettingActivity.this,
                        StepTargetSettingActivity.class), STEPTARGETSETTING);
                break;
        }
    }

//    private void setStepSituation(int step) {
//        int stepTarget = PreferencesUtils.getInt(this, StepTargetSettingActivity.STEP_TARGET, 8000);
//
//        if (step >= stepTarget) {// 完成目标
//            mStepDesc.setText(R.string.share_step_great_info);
//        } else if (step > 0) {// 完成一部分
//            mStepDesc.setText(getString(R.string.share_step_fighting_info, (stepTarget
//                    - step)));
//        } else {// 还没开始
//            mStepDesc.setText(R.string.share_step_no_info);
//        }
//    }

    private void saveNowModel(boolean isSwitch) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showShort(StepSettingActivity.this, R.string.error_jewelry_connect);
            return;
        }
        if (isSwitch) {
            nowSetModel.setNotifySwitch(call_switch_cb.isChecked());
            final int target = nowSetModel.isNotifySwitch() ? ToTwooApplication.owner.getWalkTarget() : 0;
            BluetoothManage.getInstance().setStepTarget(target);
            if (BleParams.isSecurityJewlery()) {
                nowSetModel.setFlashColor("WHITE");
            }
            NotifyUtil.setStepNotify(StepSettingActivity.this, nowSetModel);
        } else {
            BluetoothManage.getInstance().setStepTargetNotify(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
            if (BleParams.isSecurityJewlery()) {
                nowSetModel.setFlashColor("WHITE");
            }
            NotifyUtil.setStepNotify(StepSettingActivity.this, nowSetModel);
//            BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
        }
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationTv.setTextColor(0xffffffff);
            mShortVibrationTv.setTextColor(0xde000000);
        } else {
            mShortVibrationTv.setTextColor(0xffffffff);
            mLongVibrationTv.setTextColor(0xde000000);
        }
    }

    @EventInject(eventType = S.E.E_STEP_GET_LIST_SUCCESSED, runThread = TaskType.Async)
    public void onGetStepListSuccessed(EventData data) {
        HttpValues hv = (HttpValues) data;
        Step step = null;
        ArrayList<Step> list = (ArrayList<Step>) hv.getUserDefine("stepList");
        if (stepWeekData == null)
            stepWeekData = new ArrayList<Integer>();
        else
            stepWeekData.clear();

        for (int i = 0; i < list.size(); i++) {
            step = list.get(i);
            stepWeekData.add(step.getSteps());
            if (step.getSteps() > maxStep)
                maxStep = step.getSteps();
        }

        if (stepWeekData.size() == 0)
            for (int i = 0; i < 6; i++)
                stepWeekData.add(0);

        stepWeekData.add(0);    //最后加一条今天的数据为0

        // 通知界面, 更新数据
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                // 设置数据表格相关数据, 最大值, 目标值, 具体数据
                if (maxStep > tableView.maxStep) {
                    tableView.setMaxStep(maxStep);
                }
                tableView.setTargetStep(ToTwooApplication.owner.getWalkTarget());
                tableView.setStepData(stepWeekData);
                todayStep = stepWeekData.get(stepWeekData.size() - 1);

                tableView.startInitAm();
                setTodayStepSituation();
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case STEPTARGETSETTING:
                stepTarget = ToTwooApplication.owner.getWalkTarget();
                tableView.setTargetStep(stepTarget);
                setTodayStepSituation();
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @EventInject(eventType = S.E.E_STEP_GET_LIST_FAILED, runThread = TaskType.UI)
    public void onGetStepListFailed(EventData data) {
        ToastUtils.show(this, R.string.error_net, Toast.LENGTH_SHORT);
    }

    /**
     * 根据选择的情况, 显示对应的数据
     *
     * @param day_of_week
     */
    protected void showDateTitle(int day_of_week) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -(6 - day_of_week));
        if (6 == day_of_week) {
            mSetStepTitle.setText(R.string.today);
        } else {
            mSetStepTitle.setText((cal.get(Calendar.MONTH) + 1) + "/"
                    + cal.get(Calendar.DAY_OF_MONTH));
        }
        if (stepWeekData != null && stepWeekData.size() >= 7 && day_of_week < 7) {
            int step = stepWeekData.get(day_of_week);

            SpannableString stepss = new SpannableString(step
                    + getString(R.string.step));
//            stepss.setSpan(new AbsoluteSizeSpan(16, true), 0, stepss.length()
//                            - getString(R.string.step).length(),
//                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            stepTv.setText(setStyle(stepss, stepss.length()
                    - getString(R.string.step).length()));
            // 公里数保留一位小数
            DecimalFormat format = new DecimalFormat("#.#");
            SpannableString walk = new SpannableString(
                    format.format(StepUtils.getWalkDistance(step, true))
                            + getString(R.string.km));
//            walk.setSpan(new AbsoluteSizeSpan(16, true), 0, walk.length()
//                            - getString(R.string.km).length(),
//                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

            walkDisTv.setText(setStyle(walk, walk.length()
                    - getString(R.string.km).length()));

            // 四舍五入取整
            SpannableString calorie = new SpannableString(StepUtils.getCaliore(step, true)
                    + getString(R.string.kcal));
//            calorie.setSpan(new AbsoluteSizeSpan(16, true), 0, calorie.length()
//                            - getString(R.string.kcal).length(),
//                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            calorieTv.setText(setStyle(calorie, calorie.length()
                    - getString(R.string.kcal).length()));
        }
    }

    private SpannableString setStyle(SpannableString spannableString, int endIndex) {
        spannableString.setSpan(new AbsoluteSizeSpan(16, true), 0, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), 0, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#000000")), 0, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @SuppressLint({"StringFormatInvalid", "StringFormatMatches"})
    private void setTodayStepSituation() {
        if (todayStep >= stepTarget) {// 完成目标
            mStepDesc.setText(String
                    .format(getString(R.string.step_great_info), todayStep
                            - stepTarget));
        } else if (todayStep > 0) {// 完成一部分
            if (BleParams.isSecurityJewlery()) {
                mStepDesc.setText(String.format(
                        getString(R.string.safe_step_fighting_info), stepTarget - todayStep));
            } else {
                mStepDesc.setText(String.format(
                        getString(R.string.step_fighting_info), stepTarget - todayStep));
            }
        } else {// 还没开始
            if (BleParams.isSecurityJewlery()) {
                mStepDesc.setText(R.string.safe_step_oh_no_info);
            } else {
                mStepDesc.setText(R.string.step_oh_no_info);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_STEP_NOTIFY_CHANGE, null);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
