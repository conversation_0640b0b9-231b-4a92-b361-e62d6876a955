package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.activity.nfc.NfcBoundActivity;
import com.totwoo.totwoo.ble.BleUtils;
import com.totwoo.totwoo.databinding.ActivityJewelrySelectBinding;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomDialog;
import com.umeng.analytics.MobclickAgent;

public class JewelrySelectActivity extends BaseActivity {
    private ActivityJewelrySelectBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityJewelrySelectBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());

        initView();
    }

    private void initView() {
        CommonUtils.setStateBar(JewelrySelectActivity.this, false);

        binding.selectJewelryBack.setOnClickListener(v -> finishInfoOrStart());
        binding.selectJewelryGoBle.setOnClickListener(v -> goConnect(true));
        binding.selectJewelryGoNfc.setOnClickListener(v -> goConnect(false));

//        // 设置类别标题
//        jewelryTypes = getResources().getStringArray(R.array.jewelry_type);
//        CommonNavigator commonNavigator = new CommonNavigator(this);
//        commonNavigator.setAdjustMode(true);
//        commonNavigator.setAdapter(new CommonNavigatorAdapter() {
//
//            @Override
//            public int getCount() {
//                return jewelryTypes.length;
//            }
//
//            @Override
//            public IPagerTitleView getTitleView(Context context, final int index) {
//                SimplePagerTitleView simplePagerTitleView = new ScaleTransitionPagerTitleView(context);
//                simplePagerTitleView.setNormalColor(getResources().getColor(R.color.text_color_black_33));
//                simplePagerTitleView.setSelectedColor(getResources().getColor(R.color.color_main));
//                simplePagerTitleView.setText(jewelryTypes[index]);
//                simplePagerTitleView.setTextSize(16);
//                simplePagerTitleView.setOnClickListener(v -> binding.jewelryTypeViewPager.setCurrentItem(index));
//                return simplePagerTitleView;
//            }
//
//            @Override
//            public IPagerIndicator getIndicator(Context context) {
//                LinePagerIndicator linePagerIndicator = new LinePagerIndicator(context);
//                linePagerIndicator.setMode(LinePagerIndicator.MODE_EXACTLY);
//                linePagerIndicator.setLineWidth(CommonUtils.dip2px(JewelrySelectActivity.this, 20));
//                linePagerIndicator.setColors(getResources().getColor(R.color.safe_green_color));
//                return linePagerIndicator;
//            }
//        });
//        binding.jewelryTypeIndicator.setNavigator(commonNavigator);
//
//
//        // 设置切换的页面
//        RecyclerView.Adapter pageAdapter = new RecyclerView.Adapter<BindingViewHolder<JewelrySelectPageItemBinding>>() {
//            @NonNull
//            @Override
//            public BindingViewHolder<JewelrySelectPageItemBinding> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
//                return new BindingViewHolder<>(JewelrySelectPageItemBinding.inflate(LayoutInflater.from(JewelrySelectActivity.this), parent, false));
//            }
//
//            @Override
//            public void onBindViewHolder(@NonNull BindingViewHolder<JewelrySelectPageItemBinding> holder, int position) {
//                if (position == 0) {
//                    holder.binding.selectJewelryTypeImage.setImageResource(R.drawable.jewelry_select_type_ble);
//                    holder.binding.selectJewelryInfoText.setText(R.string.jewelry_select_hint);
////                    holder.binding.selectJewelryGoBtn.setText(R.string.bluetooth_connection);
//                    holder.binding.selectJewelryGoBtn.setOnClickListener(v -> goConnect(true));
//                } else {
//                    holder.binding.selectJewelryTypeImage.setImageResource(R.drawable.jewelry_select_type_nfc);
//                    holder.binding.selectJewelryInfoText.setText(R.string.jewelry_select_hint_nfc);
////                    holder.binding.selectJewelryGoBtn.setText(jewelryTypes[1]);
//                    holder.binding.selectJewelryGoBtn.setOnClickListener(v -> goConnect(false));
//                }
//            }
//
//            @Override
//            public int getItemCount() {
//                return jewelryTypes.length;
//            }
//        };
//
//        binding.jewelryTypeViewPager.setAdapter(pageAdapter);
//        binding.jewelryTypeViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
//            @Override
//            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
//                binding.jewelryTypeIndicator.onPageScrolled(position, positionOffset, positionOffsetPixels);
//            }
//
//            @Override
//            public void onPageSelected(int position) {
//                binding.jewelryTypeIndicator.onPageSelected(position);
//            }
//
//            @Override
//            public void onPageScrollStateChanged(int state) {
//                binding.jewelryTypeIndicator.onPageScrollStateChanged(state);
//            }
//        });
    }


    private void goConnect(boolean isBle) {
        if (!NetUtils.isConnected(this)) {
            ToastUtils.showLong(this, R.string.error_net);
            return;
        }
        if (!isBle) {
            checkNFCAndGoConnect();
        } else {
            if (!PermissionUtil.hasBluetoothPermission(this)) {
                return;
            }

            if (!BleUtils.isBlEEnable(this)) {
                showBluetoothDialog();
            } else {
                Intent intent = new Intent(this, JewelryConnectActivity.class);
                if (TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN)) {
                    intent.addFlags(Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
                    intent.putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN);
                }
//                intent.putExtra(JewelryConnectActivity.JEWELRY_TYPE, item.getType());
                startActivity(intent);
            }
        }
    }


    /**
     * 检查当前手机 NFC 支持情况, 以及是否已经打开
     */
    private void checkNFCAndGoConnect() {
//        NfcManager nfcManager = (NfcManager) getSystemService(NFC_SERVICE);
//        if (nfcManager.getDefaultAdapter() == null){
//            // nfc 不支持: 是否进入绑定页面??
//        } else {
        Intent intent = new Intent(this, NfcBoundActivity.class);
        if (TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
            intent.putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN);
        }
//        intent.putExtra(JewelryConnectActivity.JEWELRY_TYPE, item.getType());
        startActivity(intent);
//        }
    }

//    private ArrayList<ArrayList<JewelrySelectItemBean>> initBeans() {
//        ArrayList<ArrayList<JewelrySelectItemBean>> allBeans = new ArrayList<>();
//        ArrayList<JewelrySelectItemBean> pendantBeans = new ArrayList<>();
//
//        // 1，首饰列表处原来因为历史原因“恋恋”（英文ALWAYS）入口图里放了很多不同的首饰，出于营销需要还是需要对号入座，把产品图都放出来。这些产品对应的蓝牙名字全部为TWO75, 主要包括（日月恋人1，守护恋人、蓝色恋人、钥匙和锁、海誓山盟、灵犀恋人、笑脸）。入口图尽快是不同产品的图，但在连接后，产品固件默认对应的图会是一张统一的默认的图，这张图UI会重新做一张出来。
//        // 2，要加入即将上线的新首饰：新日月恋人和糖，这两款产品是两个入口，对应的蓝牙名称为TWO80（新日月恋人）和TWO81（糖），以及情书2,对应的蓝牙名称为TWO63，TWO63对应的是原来情书的页面。
//        // 3，分类中的NFC改名字为戒指。因为以后有可能会有既有NFC又有蓝牙功能的首饰上线。所以，在列表中只按首饰分类列出。
//        // 详情见蓝湖切图。原来的中英文名字也都改成为新的UI切图上的文案名字。注意1和2用的是罗马数字。
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_sm1), BleParams.JEWELRY_BLE_NAME_EMBMN, R.drawable.jewelry_icon_sm1));  // 日月恋人I.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_ms), BleParams.JEWELRY_BLE_NAME_EMBMN, R.drawable.jewelry_icon_ms));  // 海誓山盟.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_protector), BleParams.JEWELRY_BLE_NAME_EMBMN, R.drawable.jewelry_icon_protector));  // 守护恋人.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_young), BleParams.JEWELRY_BLE_NAME_EMBMN, R.drawable.jewelry_icon_young));  // 蓝色恋人.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_solmate), BleParams.JEWELRY_BLE_NAME_EMBMN, R.drawable.jewelry_icon_soulmate));  // 灵犀恋人.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_kl), BleParams.JEWELRY_BLE_NAME_EMBMN, R.drawable.jewelry_icon_kl));  // 钥匙和锁.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_smile), BleParams.JEWELRY_BLE_NAME_EMBMN, R.drawable.jewelry_icon_smile));  // 笑脸.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_ll1), BleParams.JEWELRY_BLE_NAME_LLM, R.drawable.jewelry_icon_ll1));  // 情书I.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_ll2), BleParams.JEWELRY_BLE_NAME_KF, R.drawable.jewelry_icon_ll2));  // 情书II.
////        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_sm2), BleParams.JEWELRY_BLE_NAME_SM2, R.drawable.jewelry_icon_sm2));  // 日月恋人II.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_81), BleParams.JEWELRY_BLE_NAME_CANDY, R.drawable.jewelry_icon_81));  // 山盟海誓（振动款）.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_lmc), BleParams.JEWELRY_BLE_NAME_GMB, R.drawable.jewelry_icon_lmc));  // 密语.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_wb1), BleParams.JEWELRY_BLE_NAME_WBCB, R.drawable.jewelry_icon_wb1));  // 勇敢手链.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_wb2), BleParams.JEWELRY_BLE_NAME_DB, R.drawable.jewelry_icon_wb2));  // 勇敢手镯.
//        pendantBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_wb), BleParams.JEWELRY_BLE_NAME_MB, R.drawable.jewelry_icon_wb));  // 绽放.
//
//        allBeans.add(pendantBeans);
//
//        ArrayList<JewelrySelectItemBean> bangleBeans = new ArrayList<>();
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_bloom_p), BleParams.JEWELRY_BLE_NAME_SL, R.drawable.product_jewelry_name_sl));
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_secret), BleParams.JEWELRY_BLE_NAME_GMP, R.drawable.product_jewelry_name_gmp));
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_lollipop), BleParams.JEWELRY_BLE_NAME_LPP, R.drawable.product_jewelry_name_lpp));
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_wonderland), BleParams.JEWELRY_BLE_NAME_WL, R.drawable.product_jewelry_name_wl));
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_lucky_clover), BleParams.JEWELRY_BLE_NAME_SC, R.drawable.product_jewelry_name_lc));
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_time_memory), BleParams.JEWELRY_BLE_NAME_MEMORYP, R.drawable.product_jewelry_name_mm));
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_moonshine), BleParams.JEWELRY_BLE_NAME_MWP, R.drawable.product_jewelry_name_mwp));
//        bangleBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_bold_p), BleParams.JEWELRY_BLE_NAME_DP, R.drawable.product_jewelry_name_bdp));
//        allBeans.add(bangleBeans);
//
//        ArrayList<JewelrySelectItemBean> nfcBeans = new ArrayList<>();
//        nfcBeans.add(new JewelrySelectItemBean(getString(R.string.secret), BleParams.JEWELRY_NFC_NAME_MEET_PREF, BleParams.JEWELRY_HARDWARE_TYPE_NFC, R.drawable.product_jewelry_name_nfc_secret));
//        allBeans.add(nfcBeans);
//
//        ArrayList<JewelrySelectItemBean> SafetyBeans = new ArrayList<>();
//        SafetyBeans.add(new JewelrySelectItemBean(getString(R.string.jewelry_type_apple), BleParams.JEWELRY_BLE_NAME_SA, R.drawable.product_jewelry_name_apple));
//        allBeans.add(SafetyBeans);
//        return allBeans;
//    }

    private void finishInfoOrStart() {
//        if (TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN)) {
            HomeActivityControl.getInstance().openHomeActivity(JewelrySelectActivity.this);
            finish();
//        } else {
//            showNotSaveDialog();
//        }
    }

    private void showNotSaveDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(JewelrySelectActivity.this);
        commonMiddleDialog.setMessage(R.string.jewelry_connect_tips);
        commonMiddleDialog.setSure(v -> finish());
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    @Override
    public void onBackPressed() {
        if (!TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN)) {
            super.onBackPressed();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }

    /**
     * 展示请求蓝牙开启的对话框
     */
    public void showBluetoothDialog() {
        final CustomDialog dialog = new CustomDialog(this);
        dialog.setMessage(R.string.request_open_bluetooth);
        dialog.setPositiveButton(R.string.allow, v -> {
            BleUtils.enableBlueTooth(JewelrySelectActivity.this);
            dialog.dismiss();
        });
        dialog.setNegativeButton(R.string.cancel, v -> dialog.dismiss());

        dialog.show();
    }

    private boolean hasLocationPermission() {
        return PermissionUtil.hasLocationPermission(JewelrySelectActivity.this);
    }

    private CustomDialog locationCustomDialog;

    private void showLocationDenyDialog() {
        if (locationCustomDialog == null) {
            locationCustomDialog = new CustomDialog(JewelrySelectActivity.this);
            locationCustomDialog.setTitle(R.string.tips);
            locationCustomDialog.setMessage(R.string.gps_connect_request_hint);
            locationCustomDialog.setPositiveButton(R.string.open_hint, v -> {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CONNECT_LOCATION_SWITCH_SET);
                startActivity(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS));
            });
        }
        locationCustomDialog.show();
    }
}
