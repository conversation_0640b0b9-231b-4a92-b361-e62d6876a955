package com.totwoo.totwoo.activity.security;

import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import java.io.IOException;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class FakeCallActivity extends BaseActivity {

    @BindView(R.id.fake_call_dangerous_title)
    TextView mDangerousTitle;
    @BindView(R.id.fake_call_embarrass_title)
    TextView mEmbarrassTitle;
    @BindView(R.id.fake_call_main_iv)
    ImageView mMainIv;
    @BindView(R.id.fake_call_tv)
    TextView mMainTv;
    @BindView(R.id.fake_call_ring_cl)
    ConstraintLayout mRingViewCl;
    @BindView(R.id.fake_call_ring_time)
    TextView mTimeTv;
    @BindView(R.id.fake_call_ring_cancel_cl)
    ConstraintLayout mRingCancelCl;
    @BindView(R.id.fake_call_ring_answer_cl)
    ConstraintLayout mRingAnswerCl;
    @BindView(R.id.fake_call_answer_cancel_cl)
    ConstraintLayout mAnswerCancelCl;
    @BindView(R.id.fake_call_ring_icon_ll)
    LinearLayout mRingIconLl;
    @BindView(R.id.fake_call_answer_icon_ll1)
    LinearLayout mAnswerIconLl1;
    @BindView(R.id.fake_call_answer_icon_ll2)
    LinearLayout mAnswerIconLl2;
    @BindView(R.id.fake_call_ring_name)
    TextView mRingName;

    private boolean isDangerous = true;

    private MediaPlayer mediaPlayer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_fake_call);
        ButterKnife.bind(this);
        mediaPlayer = new MediaPlayer();
        mediaPlayer.setOnCompletionListener(mp -> mediaPlayer.start());
        mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
        setTopTitle(R.string.safe_fake_call_title);
    }

    @OnClick({R.id.fake_call_dangerous_title, R.id.fake_call_embarrass_title, R.id.fake_call_info_cl, R.id.fake_call_phone_iv,
            R.id.fake_call_ring_cancel_tv, R.id.fake_call_answer_cancel_cl, R.id.fake_call_ring_answer_cl})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.fake_call_dangerous_title:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.SAFE_FIND_CAMOUFLAGEINCOMINGCALL_TX);
                if (!isDangerous) {
                    isDangerous = true;
                    typeSwitch();
                }
                break;
            case R.id.fake_call_embarrass_title:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.SAFE_FIND_CAMOUFLAGEINCOMINGCALL_TS);
                if (isDangerous) {
                    isDangerous = false;
                    typeSwitch();
                }
                break;
            case R.id.fake_call_ring_answer_cl:
                stopMusic();
                if (isDangerous) {
                    playMusic(Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.fake_call_dangerous));
                } else {
                    playMusic(Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.fake_call_embarrass));
                }
                showAnswerView();
                break;
            case R.id.fake_call_ring_cancel_tv:
            case R.id.fake_call_answer_cancel_cl:
                stopMusic();
                goneRingingView();
                break;
            case R.id.fake_call_info_cl:
                break;
            case R.id.fake_call_phone_iv:
                playMusic(Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.fake_call_ring));
                showRingingView();
                break;
        }
    }

    private void typeSwitch() {
        if (isDangerous) {
            mDangerousTitle.setTextColor(getResources().getColor(R.color.black));
            mDangerousTitle.setBackground(getResources().getDrawable(R.drawable.shape_white_12_top));
            mEmbarrassTitle.setTextColor(getResources().getColor(R.color.text_color_gray_99));
            mEmbarrassTitle.setBackground(null);
            mMainIv.setImageResource(R.drawable.fake_call_dangerous);
            mMainTv.setText(R.string.safe_fake_call_emergency_info);
            mRingName.setText("亲爱哒");
        } else {
            mDangerousTitle.setTextColor(getResources().getColor(R.color.text_color_gray_99));
            mDangerousTitle.setBackground(null);
            mEmbarrassTitle.setTextColor(getResources().getColor(R.color.black));
            mEmbarrassTitle.setBackground(getResources().getDrawable(R.drawable.shape_white_12_top));
            mMainIv.setImageResource(R.drawable.fake_call_embarrass);
            mMainTv.setText(R.string.safe_fake_call_embarrass_info);
            mRingName.setText("表妹");
        }
    }

    private void showRingingView() {
        mRingViewCl.setVisibility(View.VISIBLE);
        mTimeTv.setVisibility(View.GONE);
        mRingCancelCl.setVisibility(View.VISIBLE);
        mRingAnswerCl.setVisibility(View.VISIBLE);
        mAnswerCancelCl.setVisibility(View.GONE);
        mRingIconLl.setVisibility(View.VISIBLE);
        mAnswerIconLl1.setVisibility(View.GONE);
        mAnswerIconLl2.setVisibility(View.GONE);
    }

    private void showAnswerView() {
        mRingViewCl.setVisibility(View.VISIBLE);
        mTimeTv.setVisibility(View.VISIBLE);
        mRingCancelCl.setVisibility(View.GONE);
        mRingAnswerCl.setVisibility(View.GONE);
        mAnswerCancelCl.setVisibility(View.VISIBLE);
        mRingIconLl.setVisibility(View.GONE);
        mAnswerIconLl1.setVisibility(View.VISIBLE);
        mAnswerIconLl2.setVisibility(View.VISIBLE);
    }

    private void goneRingingView() {
        mRingViewCl.setVisibility(View.GONE);
    }

    private void playMusic(Uri uri) {
        try {
            mediaPlayer.setDataSource(this, uri);
            mediaPlayer.prepareAsync();
            mediaPlayer.setOnPreparedListener(mp -> mediaPlayer.start());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void stopMusic() {
        if (mediaPlayer.isPlaying()) {
            mediaPlayer.pause();
            mediaPlayer.reset();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mediaPlayer.release();
    }

    @Override
    public void onBackPressed() {
        if(mRingViewCl.getVisibility() == View.VISIBLE){
            stopMusic();
            goneRingingView();
        }else{
            super.onBackPressed();
        }
    }
}

