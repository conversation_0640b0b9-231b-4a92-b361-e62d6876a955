package com.totwoo.totwoo.activity;

import android.annotation.SuppressLint;
import android.app.LoaderManager.LoaderCallbacks;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.Context;
import android.content.CursorLoader;
import android.content.Intent;
import android.content.Loader;
import android.database.Cursor;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.os.Message;
import android.provider.ContactsContract;
import android.provider.ContactsContract.CommonDataKinds.Phone;
import android.provider.ContactsContract.Contacts.Photo;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.AbsListView;
import android.widget.AbsListView.OnScrollListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.BaseAdapter;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.jakewharton.rxbinding.widget.RxTextView;
import com.jakewharton.rxbinding.widget.TextViewAfterTextChangeEvent;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ContactsBean;
import com.totwoo.totwoo.bean.HttpContactsBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.data.CoupleLogic.CoupleCallback;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomProgressBarDialog;
import com.umeng.analytics.MobclickAgent;

import net.sourceforge.pinyin4j.PinyinHelper;

import org.json.JSONArray;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;

/**
 * 配对选择界面
 *
 * <AUTHOR>
 * @date 2015-2015年7月16日
 */
public class TheHeartChooseActivity extends BaseActivity implements
        LoaderCallbacks<Cursor> {
    /**
     * 短信请求对方注册, 短信界面要求结果的请求码
     */
    private static int REQUEST_OTHER_CODE = 20;

    /**
     * 读取的联系人列表
     */
    private ArrayList<ContactsBean> contactsData;
    /**
     * 请求的totwoo用户列表
     */
    private ArrayList<ContactsBean> totwooListData;

    /**
     * 用户列表对应的ListView
     */
    @BindView(R.id.the_heart_choose_listview)
    ListView mListView;

    /**
     * 没有搜索词是的 阴影层
     */
    @BindView(R.id.the_heart_choose_no_filter_layer)
    View noFilterLayer;

    /**
     * 搜索无果的文案
     */
    @BindView(R.id.the_heart_choose_filter_no_result_tv)
    TextView filterNoResultTv;

    /**
     * ListView 对应 Adapter
     */
    private ContactsAdapter mAdapter;

    /**
     * 配对操作管理类
     */
    private CoupleLogic mCoupleLogic;

    /**
     * 暂时缓存请求状态的联系人对应position, 当有新的请求成功时, 自动将此条联系人状态改为正常, 并更新为最新请求成功的联系人
     */
    private int temRequestIndex = -1;

    /**
     * 加载进度框
     */
    private CustomProgressBarDialog progressBar;
    private EditText searchView;
    private String mCurFilter;
    private String country_code;

    /**
     * 获取sort key的首个字符，如果是英文字母就直接返回，否则返回#。
     *
     * @param sortKeyString 数据库中读取出的sort key
     * @return 英文字母或者#
     */
    public String getSortKey(String sortKeyString) {
        if (TextUtils.isEmpty(sortKeyString)) {
            return "#";
        }

        String key = sortKeyString.substring(0, 1).toUpperCase(
                Locale.getDefault());
        if (key.matches("^[A-Z]$")) {
            return key;
        } else {
//            String[] keys = PinyinHelper
//                    .toHanyuPinyinStringArray(key.charAt(0));
//            if (keys != null) {
//                key = keys[0].substring(0, 1).toUpperCase(Locale.getDefault());
//                if (key.matches("^[A-Z]$")) {
//                    return key;
//                }
//            }
            try {
                key = PinyinHelper.toHanyuPinyinStringArray(key.charAt(0))[0].substring(0, 1);
                if (key.matches("^[A-Z]$")) {
                    return key;
                }
            } catch (Exception e) {
                e.printStackTrace();
                return "#";
            }
        }
        return "#";
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_the_heart_choose);
        ButterKnife.bind(this);

        mCoupleLogic = new CoupleLogic(this);

        mAdapter = new ContactsAdapter(this);
        mListView.setAdapter(mAdapter);

        // 添加条目点击事件
        mListView.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view,
                                    int position, long id) {
                sendCoupleRequest(contactsData.get(position), position);
            }
        });

        // 设置当列表快速滑动时， 不加载图片
        mListView.setOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                switch (scrollState) {
                    case OnScrollListener.SCROLL_STATE_IDLE:
                        if (!mAdapter.isLoadImage) {
                            mAdapter.isLoadImage = true;
                            // 每次停止加载之后, 刷新当前界面元素, 加载对应的图片
                            if (mAdapter != null) {
                                mAdapter.notifyDataSetChanged();
                            }
                        }

                        break;
                    case OnScrollListener.SCROLL_STATE_TOUCH_SCROLL:
                        mAdapter.isLoadImage = true;
                        break;
                    case OnScrollListener.SCROLL_STATE_FLING:
                        mAdapter.isLoadImage = false;
                        break;
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem,
                                 int visibleItemCount, int totalItemCount) {
            }
        });
        getLoaderManager().initLoader(0, null, this);
        Glide.with(this).load(R.drawable.loading).into((ImageView) findViewById(R.id.the_heart_choose_loadding_imge));
    }

    /**
     * 请求服务器更新用户状态, 刷新totwoo 用户信息
     */
    protected void getCopleFriendData() {
        JSONArray arr = new JSONArray();
        for (ContactsBean bean : contactsData) {
            if (bean != null && !TextUtils.isEmpty(bean.getSpecific_id())) {
                arr.put(bean.getPhoneNumber());
            }
        }
        HttpHelper.commonServiceV2.friendsList(arr.toString())
                .compose(HttpHelper.<HttpBaseBean<List<HttpContactsBean>>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<List<HttpContactsBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<HttpContactsBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            ArrayList<HttpContactsBean> tempBeans = (ArrayList<HttpContactsBean>) listHttpBaseBean.getData();
                            LogUtils.e("aab tempBeans.size = " + tempBeans.size());
                            if (tempBeans.size() > 0) {
                                if (totwooListData == null) {
                                    totwooListData = new ArrayList<>();
                                }
                                for (HttpContactsBean httpContactsBean : tempBeans) {
                                    ContactsBean bean = new ContactsBean();
                                    bean.setSpecific_id(httpContactsBean.getTotwoo_id());
                                    bean.setPhoneNumber(httpContactsBean.getMobilephone());
                                    bean.setName(httpContactsBean.getNick_name());
                                    bean.setHeadUrl(httpContactsBean.getHead_portrait());
                                    bean.setTalkId(httpContactsBean.getTalkId());
                                    bean.setCoupleShip(httpContactsBean.getCoupleShip());
                                    bean.setTotwoo_id(httpContactsBean.getTotwoo_id());
                                    totwooListData.add(bean);
                                }
                                updateContactsList();
                            }
                        }
                    }
                });

//        RequestParams params = HttpHelper.getBaseParams(true);
//        JSONArray arr = new JSONArray();
//        for (ContactsBean bean : contactsData) {
//            if (bean != null && !TextUtils.isEmpty(bean.getSpecific_id())) {
//                arr.put(bean.getPhoneNumber());
//            }
//        }
//        params.addFormDataPart("contacts", arr.toString());
//
//        HttpRequest.post(HttpHelper.URL_FRIENDS,
//                params, new RequestCallBack<String>() {
//                    @Override
//                    public void onLogicSuccess(String s) {
//                        super.onLogicSuccess(s);
//
//                        if (s == null) {
//                            return;
//                        }
//
//                        JSONArray array = null;
//                        try {
//                            array = new JSONArray(s);
//                        } catch (JSONException e) {
//                            e.printStackTrace();
//                        }
//
//                        if (array == null) {
//                            return;
//                        }
//
//                        for (int i = 0; i < array.length(); i++) {
//                            JSONObject obj = array.optJSONObject(i);
//                            if (obj != null) {
//                                ContactsBean bean = new ContactsBean();
//                                bean.setSpecific_id(obj
//                                        .optString("totwoo_id"));
//                                bean.setPhoneNumber(obj.optString("mobilephone"));
//                                bean.setName(obj.optString("nick_name"));
//                                bean.setHeadUrl(obj.optString("head_portrait"));
//                                bean.setTalkId(obj.optString("talkId"));
//
//                                int state = obj.optInt("coupleShip");
//
//                                switch (state){
//                                    case 1:
//                                        bean.setCouple_state(CoupleLogic.COUPLE_STATE_REQUEST);
//                                        break;
//                                    case 2:
//                                        bean.setCouple_state(CoupleLogic.COUPLE_STATE_REPLY);
//                                        break;
//                                    case 3:
//                                        bean.setCouple_state(CoupleLogic.COUPLE_STATE_PAIRED);
//                                        break;
//                                    case 4:
//                                        bean.setCouple_state(CoupleLogic.COUPLE_STATE_APART);
//                                        break;
//                                }
//
//                                if (totwooListData == null) {
//                                    totwooListData = new ArrayList<>();
//                                }
//                                totwooListData.add(bean);
//                            }
//                            // 更新联系人列表
//                            updateContactsList();
//                        }
//                    }
//                });

    }

    /**
     * 获取totwoo用户数据之后, 统一更新列表<br>
     * 逐一对比原始列表数据, 发现有
     */
    protected void updateContactsList() {
        LogUtils.e("aab contactsData.size = " + contactsData.size());
        LogUtils.e("aab totwooListData.size = " + totwooListData.size());
        if (contactsData == null || totwooListData == null) {
            return;
        }

        for (int i = 0; i < contactsData.size(); i++) {
            ContactsBean origBean = contactsData.get(i);
            for (ContactsBean totwooBean : totwooListData) {
                if (totwooBean != null
                        && origBean != null
                        && origBean.getPhoneNumber().equals(
                        totwooBean.getPhoneNumber())) {
                    origBean.setName(totwooBean.getName());
                    origBean.setHeadUrl(totwooBean.getHeadUrl());
                    origBean.setCoupleShip(totwooBean.getCoupleShip());
                    origBean.setTalkId(totwooBean.getTalkId());
                    origBean.setTotwoo_id(totwooBean.getTotwoo_id());
                    if (totwooBean.getCoupleShip() == CoupleLogic.COUPLE_STATE_REQUEST) {
                        temRequestIndex = i;
                    }
                    origBean.setType(ContactsBean.CONTACTS_TYPE_TOTWOO);

                    if (mAdapter != null) {
                        mAdapter.notifyDataSetChanged();
                    }
                    continue;
                }
            }
        }
    }

    @Override
    protected void initTopBar() {
        // 防止再次initTopBar 造成的界面混乱
        if (searchView != null && searchView.getVisibility() == View.VISIBLE) {
            return;
        }

        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.select_match);
        setTopRightIcon(R.drawable.search_icon);
        setTopRightOnClick(new OnClickListener() {
            @Override
            public void onClick(View v) {
                initSearchView();
            }
        });
    }

    /**
     * 初始化搜索框, 相应的事件
     */
    protected void initSearchView() {
        searchView = getTopSearchView();
        getTopTitleView().setVisibility(View.GONE);
        getTopRightIcon().setVisibility(View.GONE);
        noFilterLayer.setVisibility(View.VISIBLE);

        // 设置监听搜索
        RxTextView.afterTextChangeEvents(searchView)
                .throttleWithTimeout(400, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
                .subscribe(new Action1<TextViewAfterTextChangeEvent>() {
                    @Override
                    public void call(TextViewAfterTextChangeEvent textViewAfterTextChangeEvent) {
                        String newText = textViewAfterTextChangeEvent.editable().toString();
                        String newFilter = !TextUtils.isEmpty(newText) ? newText : null;

                        if (searchView != null && searchView.getVisibility() == View.GONE) {
                            return;
                        }

                        // 根据当前搜索框状态, 改变右侧关闭按钮状态
                        // 设置右侧关闭按钮监听
                        if (!TextUtils.isEmpty(newText)) {
                            noFilterLayer.setVisibility(View.GONE);
                            setTopRightIcon(R.drawable.close_icon);
                            setTopRightOnClick(new OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    searchView.setText("");
                                    getTopRightIcon().setVisibility(View.GONE);
                                }
                            });
                        } else {
                            noFilterLayer.setVisibility(View.VISIBLE);
                            getTopRightIcon().setVisibility(View.GONE);
                        }

                        // 判断当前搜索字段是否发生了实质性变化, 如果有, 重新加载
                        if (mCurFilter == null && newFilter == null) {
                            return;
                        }
                        if (mCurFilter != null && mCurFilter.equals(newFilter)) {
                            return;
                        }
                        mCurFilter = newFilter;
                        getLoaderManager().restartLoader(0, null,
                                TheHeartChooseActivity.this);
                        showProgressBar(true);
                    }
                }, new Action1<Throwable>() {
                    @Override
                    public void call(Throwable throwable) {
                        // RxJava错误处理回调
                        LogUtils.e("TheHeartChooseActivity", "Search text change error: " + throwable.getMessage());
                    }
                });

        InputMethodManager inputManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        inputManager.toggleSoftInputFromWindow(searchView.getWindowToken(), 0,
                0);

        // 关闭搜索框
        noFilterLayer.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                closeSearchView();
            }
        });
    }

    /**
     * 关闭搜索框
     */
    protected void closeSearchView() {
        searchView.setText("");
        searchView.setVisibility(View.GONE);
        getTopTitleView();
        setTopRightIcon(R.drawable.search_icon);
        noFilterLayer.setVisibility(View.GONE);
        setTopRightOnClick(new OnClickListener() {
            @Override
            public void onClick(View v) {
                initSearchView();
            }
        });

        // 关闭输入法
        InputMethodManager inputManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        inputManager.hideSoftInputFromWindow(searchView.getWindowToken(), 0);
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (searchView != null
                    && searchView.getVisibility() == View.VISIBLE) {
                closeSearchView();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 取出手机号码中的无效字符, 返回简单的号码
     *
     * @param phone
     * @return
     */
    private String getSimplePhone(String phone) {
        if (phone == null) {
            return null;
        }
        // 过滤掉号码 中的空格和分割线
        char[] pp = new char[phone.length()];
        int j = 0;
        for (int i = 0; i < phone.length(); i++) {
            char c = phone.charAt(i);
            if (c != '-' && c != ' ') {
                pp[j++] = c;
            }
        }
        return new String(pp).trim();
    }

    /**
     * 指定的号码, 是否为 所需号码, 如果是, 返回指定格式, 如果否, 返回null
     *
     * @param phone
     * @return
     */
//    private String filterPhone(String phone) {
//        if (phone == null) {
//            return null;
//        }
//
//        if (phone.startsWith("00")) {
//            phone = phone.replaceFirst("00", "+");
//        }
//        if (!phone.startsWith("+")) {
//            phone = "+" + phone;
//        }
//        LogUtils.e("phone phone = " + phone);
//
//        PhoneNumberUtil numberUtil = PhoneNumberUtil.getInstance();
//        if (country_code == null) {
//            country_code = PreferencesUtils
//                    .getString(this, CommonArgs.COUNTRY_CODE_KEY, Apputils.systemLanguageIsChinese(this) ? "86" : "39");
//        }
//
//        try {
//            phone = phone.replace(
//                    "+",
//                    "+"
//                            + country_code);
//            PhoneNumber parse = numberUtil.parse(phone, country_code);
//            if (numberUtil.isValidNumber(parse)) {
//                return phone.replace("+", "");
//            } else {
//                phone = phone.replace(
//                        "+" + country_code,
//                        "+");
//                parse = numberUtil.parse(phone, country_code);
//                if (numberUtil.isValidNumber(parse)) {
//                    return phone.replace("+", "");
//                } else {
//                    return null;
//                }
//            }
//        } catch (NumberParseException e) {
//            return null;
//        }
//
//    }

    /**
     * 获取SIM卡中联系人数据
     */
    @SuppressLint("Range")
    public void getSIMContacts() {
        // TelephonyManager mTelephonyManager = (TelephonyManager)
        // getSystemService(Context.TELEPHONY_SERVICE);
        ContentResolver cr = getContentResolver();
        final String SIM_URI_ADN = "content://icc/adn";// SIM卡

        Uri uri = Uri.parse(SIM_URI_ADN);
        Cursor cursor = cr.query(uri, null, null, null, null);
        while (cursor.moveToFirst()) {
            ContactsBean bean = new ContactsBean();

            bean.setName(cursor.getString(cursor.getColumnIndex("name")));

            bean.setSpecific_id(cursor.getString(cursor
                    .getColumnIndex("number")));

            // 通过消息将 bean 发送给主线程， 避免在线程中操作列表数据
            Message msg = Message.obtain(mHandler, 0);
            msg.obj = bean;
            msg.sendToTarget();
        }
        cursor.close();
    }

    /**
     * 对于非totwoo 用户, 通过第三方的平台邀请对方注册<br>
     * 目前对于国内版利用短信请求
     */
    protected void requestOtherReisgered(String id) {
        Uri smsToUri = Uri.parse("smsto:+" + id);

        Intent intent = new Intent(Intent.ACTION_SENDTO, smsToUri);

        intent.putExtra("sms_body",
                getString(R.string.request_other_registered_text));

        startActivityForResult(intent, REQUEST_OTHER_CODE);
    }

    /**
     * 短信请求注册返回, 处理返回结果
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

///       各个系统 API 不同, 返回可能有误, 暂时去掉对应的文案提示.
//        if (requestCode == REQUEST_OTHER_CODE) {
//            if (resultCode == RESULT_OK) {
//                ToastUtils.showLong(TheHeartChooseActivity.this,
//                        R.string.send_success);
//            } else {
//                ToastUtils.showLong(TheHeartChooseActivity.this,
//                        R.string.send_failed);
//            }
//        }
    }

    /**
     * 发送配对请求数据, 发送配对请求, 仅针对已断开配对, 或无任何配对状态的人有效
     *
     * @param bean
     */
    private void sendCoupleRequest(final ContactsBean bean, final int position) {
        if (bean != null && (bean.getCoupleShip() == CoupleLogic.COUPLE_STATE_NULL || bean
                .getCoupleShip() == CoupleLogic.COUPLE_STATE_APART)) {
            mCoupleLogic.sendRequest(bean, new CoupleCallback() {
                @Override
                public void onResult(boolean success) {
                    if (success) {
                        // 弹窗提示发送成功
//                        final CustomDialog dialog = new CustomDialog(
//                                TheHeartChooseActivity.this);
//                        dialog.setTitle(R.string.send_success);
//                        if (bean.getType() != ContactsBean.CONTACTS_TYPE_TOTWOO) {
//                            dialog.setMessage(R.string.send_request_success_info_not_totwoo);
//                            if(Apputils.systemLanguageIsChinese(TheHeartChooseActivity.this)){
//                                dialog.setTopRight("有疑问？", new OnClickListener() {
//                                    @Override
//                                    public void onClick(View v) {
//                                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PAIR_SENDSUSSESS_HELP);
//                                        WebViewActivity.loadUrl(TheHeartChooseActivity.this, HttpHelper.URL_HELP_TOTWOO, false);
//                                    }
//                                });
//                            }
//                        } else {
//                            bean.setType(ContactsBean.CONTACTS_TYPE_TOTWOO);
//                            dialog.setMessage(R.string.send_request_success_info);
//                        }
//                        dialog.setPositiveButton(new OnClickListener() {
//                            @Override
//                            public void onClick(View v) {
//                                dialog.dismiss();
//                                // 对于非totwoo 用户, 通过第三方的平台邀请对方注册
//                                if (bean.getType() != ContactsBean.CONTACTS_TYPE_TOTWOO) {
//                                    requestOtherReisgered(bean.getSpecific_id());
//                                }
//                            }
//                        });
//                        dialog.show();

                        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(TheHeartChooseActivity.this);
                        if (bean.getType() != ContactsBean.CONTACTS_TYPE_TOTWOO) {
                            commonMiddleDialog.setTitle(R.string.send_success);
                            commonMiddleDialog.setInfo(R.string.send_request_success_info_not_totwoo);
                            if (Apputils.systemLanguageIsChinese(TheHeartChooseActivity.this)) {
                                commonMiddleDialog.setExtraInfo("手机号有问题？", v -> {
                                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.PAIR_SENDSUSSESS_HELP);
                                    WebViewActivity.loadUrl(TheHeartChooseActivity.this, HttpHelper.URL_HELP_TOTWOO, false);
                                });
                            }
                            commonMiddleDialog.setSure(R.string.invite_register, v -> {
                                commonMiddleDialog.dismiss();
                                // 对于非totwoo 用户, 通过第三方的平台邀请对方注册
                                requestOtherReisgered(bean.getSpecific_id());
                            });
                        } else {
                            bean.setType(ContactsBean.CONTACTS_TYPE_TOTWOO);
                            commonMiddleDialog.setInfo(R.string.send_success);
                            commonMiddleDialog.setSure(R.string.i_know, v -> {
                                commonMiddleDialog.dismiss();
                            });
                        }

                        commonMiddleDialog.show();

                        bean.setCoupleShip(CoupleLogic.COUPLE_STATE_REQUEST);
                        // 取消之前请求状态的用户状态, 更新最新缓存
                        temRequestIndex = position;

                        // 刷新列表
                        if (mAdapter != null) {
                            mAdapter.notifyDataSetChanged();
                        }
                    }
                }
            });
        }
    }

    @Override
    public Loader<Cursor> onCreateLoader(int id, Bundle args) {
        // This is called when a new Loader needs to be created. This
        // sample only has one Loader, so we don't care about the ID.
        // First, pick the base URI to use depending on whether we are
        // currently filtering.

        Uri baseUri;
        if (mCurFilter != null) {
            baseUri = Uri.withAppendedPath(Phone.CONTENT_FILTER_URI,
                    Uri.encode(mCurFilter));
        } else {
            baseUri = Phone.CONTENT_URI;
        }

        // Now create and return a CursorLoader that will take care of
        // creating a Cursor for the data being displayed.
        String select = "((" + Phone.DISPLAY_NAME + " NOTNULL) AND ("
                + Phone.HAS_PHONE_NUMBER + "=1))";
        return new CursorLoader(TheHeartChooseActivity.this, baseUri,
                new String[]{Phone.DISPLAY_NAME, Phone.NUMBER,
                        Photo.PHOTO_ID, Phone.CONTACT_ID,
                        Phone.SORT_KEY_PRIMARY}, select, null,
                Phone.SORT_KEY_PRIMARY + " COLLATE LOCALIZED ASC");

    }

    @Override
    public void onLoadFinished(Loader<Cursor> loader, Cursor data) {
        // Swap the new cursor in. (The framework will take care of closing
        // the
        // old cursor once we return.)
        try {
            updateContactData(data);
        } catch (Exception e) {
            e.printStackTrace();
        }

        findViewById(R.id.the_heart_choose_loadding_imge).setVisibility(
                View.GONE);

    }

    @Override
    public void onLoaderReset(Loader<Cursor> loader) {
        // This is called when the last Cursor provided to onLoadFinished()
        // above is about to be closed. We need to make sure we are no
        // longer using it.
        // mAdapter.swapCursor(null);
    }

    /**
     * 将取到的Cursor 进行过滤, 不合标准的将被直接过滤掉
     *
     * @return
     */
    private void updateContactData(Cursor phoneCursor) {
        if (contactsData == null) {
            contactsData = new ArrayList<ContactsBean>();
        } else {
            contactsData.clear();
        }

        if (phoneCursor != null && phoneCursor.getCount() != 0) {
            while (phoneCursor.moveToNext()) {
                // 得到手机号码
                // 当手机号码为空的或者为空字段 跳过当前循环
                String phoneNumber = getSimplePhone(phoneCursor.getString(1));

                String phone = CommonUtils.checkPhoneNumber(phoneNumber);

                if (phone == null || phone.equals(ToTwooApplication.owner.getTotwooId())) {
                    continue;
                }

                ContactsBean bean = new ContactsBean();
                bean.setSpecific_id(phoneNumber); // 暂存原始的手机号用户显示
                bean.setPhoneNumber(phone);
                bean.setType(ContactsBean.CONTACTS_TYPE_CONTACT);

                // 得到联系人名称
                bean.setContactName(phoneCursor.getString(0));

                bean.setSorted_key(getSortKey(phoneCursor.getString(4)));

                // 得到联系人ID
                Long contactid = phoneCursor.getLong(3);

                // 得到联系人头像ID
                Long photoid = phoneCursor.getLong(2);

                // photoid 大于0 表示联系人有头像 如果没有给此人设置头像则给他一个默认的
                if (photoid > 0) {
                    Uri uri = ContentUris.withAppendedId(
                            ContactsContract.Contacts.CONTENT_URI, contactid);
                    InputStream input = ContactsContract.Contacts
                            .openContactPhotoInputStream(getContentResolver(),
                                    uri);
                    bean.setHeadIcon(BitmapFactory.decodeStream(input));
                }

                // 如果当前的 totwoolist已经有数据, 证明此次为搜索的筛选加载, 直接对比其中数据,
                // 取出totwoo用户列表
                if (totwooListData != null) {
                    for (ContactsBean con : totwooListData) {
                        if (con.getSpecific_id().equals(bean.getPhoneNumber())) {
                            bean.setName(con.getName());
                            bean.setHeadUrl(con.getHeadUrl());
                            bean.setCoupleShip(con.getCoupleShip());
                            bean.setTalkId(con.getTalkId());
                            if (con.getCoupleShip() == CoupleLogic.COUPLE_STATE_REQUEST) {
                                temRequestIndex = phoneCursor.getPosition();
                            }
                            bean.setType(ContactsBean.CONTACTS_TYPE_TOTWOO);
                        }
                    }
                }

                contactsData.add(bean);
            }
            phoneCursor.close();

            // 如果是搜索结果为空的话, 显示提示文案
            if (!TextUtils.isEmpty(mCurFilter) && contactsData.size() == 0) {
                filterNoResultTv.setVisibility(View.VISIBLE);
                filterNoResultTv.setText(getString(R.string.search_no_result,
                        mCurFilter));
            } else {
                filterNoResultTv.setVisibility(View.GONE);
            }

            // 首次加载完成时, 需要加载totwoo用户数据
            if (totwooListData == null) {
                getCopleFriendData();
            }
        } else {
//            if (phoneCursor == null) {
//                // 没有读取联系人权限
//                final CustomDialog dialog = new CustomDialog(this);
//                dialog.setMessage(R.string.no_contact);
//                dialog.setPositiveButton(R.string.immediately_receive, v -> {
//                    startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
//                    dialog.dismiss();
//                });
//                dialog.show();
//            } else {
            filterNoResultTv.setVisibility(View.VISIBLE);
            filterNoResultTv.setText(TextUtils.isEmpty(mCurFilter) ? getString(R.string.no_contacts_data) : getString(R.string.search_no_result,
                    mCurFilter));
//            }
        }


        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
            showProgressBar(false);
        }
    }

    /**
     * 展示进度框
     *
     * @param show true 为展示, false 为隐藏
     */
    private void showProgressBar(boolean show) {
        if (show) {
            if (progressBar == null) {
                progressBar = new CustomProgressBarDialog(this);
            }
            progressBar.show();
        } else {

            if (progressBar != null && progressBar.isShowing()) {
                progressBar.dismiss();
            }
        }
    }

    /**
     * 通讯录列表适配器
     *
     * <AUTHOR>
     * @date 2015-2015年7月29日
     */
    private class ContactsAdapter extends BaseAdapter {
        /**
         * 是否加载 图片， 用户快速滑动时， 停止加载图片
         */
        public boolean isLoadImage;
        private Context mContext;

        public ContactsAdapter(Context context) {
            mContext = context;
            isLoadImage = true;
            if (contactsData == null) {
                contactsData = new ArrayList<ContactsBean>();
            }
        }

        @Override
        public int getCount() {
            return contactsData.size();
        }

        @Override
        public Object getItem(int position) {
            return contactsData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(final int position, View convertView,
                            ViewGroup parent) {
            ViewHolder holder;

            if (convertView == null) {
                convertView = LayoutInflater.from(mContext).inflate(
                        R.layout.contacts_list_item, null);

                holder = new ViewHolder();
                holder.dividerLayout = (LinearLayout) convertView
                        .findViewById(R.id.contacts_item_divider_layout);
                holder.divider = (TextView) convertView
                        .findViewById(R.id.contacts_item_divider);
                holder.icon = (ImageView) convertView
                        .findViewById(R.id.contacts_item_icon);
                holder.name = (TextView) convertView
                        .findViewById(R.id.contacts_item_name);
                holder.info = (TextView) convertView
                        .findViewById(R.id.contacts_item_state_info);
                holder.actionBtn = (TextView) convertView
                        .findViewById(R.id.contacts_item_btn);

                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }

            final ContactsBean bean = contactsData.get(position);
            if (bean == null) {
                return convertView;
            }

            // 头像, 优先显示网络
            if (!TextUtils.isEmpty(bean.getHeadUrl())) {
                BitmapHelper.display(TheHeartChooseActivity.this, holder.icon,
                        bean.getHeadUrl());
            } else if (bean.getHeadIcon() != null) {
                holder.icon.setImageBitmap(bean.getHeadIcon());
            } else {
                holder.icon.setImageResource(R.drawable.default_head_yellow);
            }

            if (!TextUtils.isEmpty(bean.getContactName())) {
                holder.name.setText(bean.getContactName());
            } else {
                holder.name.setText("");
            }

            // 如果当前的排序字段与前一个不同, 则显示排序字段
            if (position == 0
                    || !bean.getSorted_key().equals(
                    contactsData.get(position - 1).getSorted_key())) {
                holder.dividerLayout.setVisibility(View.VISIBLE);
                holder.divider.setText(bean.getSorted_key());
            } else {
                holder.dividerLayout.setVisibility(View.GONE);
            }

            // 按照用户类别显示说明
            if (TextUtils.isEmpty(bean.getTotwoo_id())) {
                holder.info.setText(bean.getSpecific_id());
                holder.info.setTextColor(mContext.getResources().getColor(
                        R.color.text_color_black_nomal));
            } else if (bean.getType() == ContactsBean.CONTACTS_TYPE_TOTWOO) {
                holder.info.setTextColor(mContext.getResources().getColor(
                        R.color.text_color_golden));

                if (TextUtils.isEmpty(bean.getName())) {
                    holder.info.setText(getString(R.string.totwoo_user));
                } else {
                    holder.info.setText(getString(R.string.totwoo_user) + ": "
                            + bean.getName());
                }
            }

            // 根据状态不同, 处理相关逻辑
            holder.actionBtn.setTag(bean);
            switch (bean.getCoupleShip()) {
                case CoupleLogic.COUPLE_STATE_APART:
                    holder.actionBtn.setVisibility(View.VISIBLE);
                    holder.actionBtn.setText(R.string.request_again);

                    // 曾经配对状态, 点击提交申请
                    holder.actionBtn.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            ContactsBean bean = (ContactsBean) v.getTag();
                            sendCoupleRequest(bean, position);
                        }
                    });

                    break;
                case CoupleLogic.COUPLE_STATE_PAIRED:
                    holder.actionBtn.setVisibility(View.VISIBLE);
                    holder.actionBtn.setText(R.string.apart_paired);

                    // 已配对状态, 点击解除配对, 成功之后跳转介绍页
                    holder.actionBtn.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            final ContactsBean bean = (ContactsBean) v.getTag();
                            mCoupleLogic.apartCouple(ToTwooApplication.owner.getPairedId(), new CoupleCallback() {
                                @Override
                                public void onResult(boolean success) {
                                    if (success) {
                                        if (bean != null) {
                                            // 更新列表
                                            bean.setCoupleShip(CoupleLogic.COUPLE_STATE_APART);
                                            if (mAdapter != null) {
                                                mAdapter.notifyDataSetChanged();
                                            }
                                        }
                                    }
                                }
                            });
                        }
                    });

                    break;
                // 待回复状态, 点击回复同意(目前不能拒绝)
                case CoupleLogic.COUPLE_STATE_REPLY:
                    holder.actionBtn.setVisibility(View.VISIBLE);
                    holder.actionBtn.setText(R.string.agree);
                    holder.actionBtn.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            final ContactsBean bean = (ContactsBean) v.getTag();
                            if (bean != null) {
                                mCoupleLogic.replyRequest(bean.getTalkId(), null);
                            }
                        }
                    });

                    break;
                // 请求状态, 点击取消请求
                // case ContactsBean.COUPLE_STATE_REQUEST:
                // holder.actionBtn.setVisibility(View.VISIBLE);
                // holder.actionBtn.setText(R.string.cancel_request);
                // holder.actionBtn.setOnClickListener(new OnClickListener() {
                // @Override
                // public void onClick(View v) {
                // final ContactsBean bean = (ContactsBean) v.getTag();
                // if (bean != null) {
                // mCoupleLogic.cancelRequest(bean,
                // new CoupleCallback() {
                // @Override
                // public void onResult(boolean success) {
                // // 取消成功, 修改状态, 更新条目
                // bean.setCouple_state(ContactsBean.COUPLE_STATE_NULL);
                //
                // if (mAdapter != null) {
                // mAdapter.notifyDataSetChanged();
                // }
                // }
                // });
                // }
                // }
                // });

                // break;
                default:
                    holder.actionBtn.setVisibility(View.GONE);
                    break;
            }

            // 因为 Request 状态的唯一性, 使用标记位置的方法识别Request状态
            if (position == temRequestIndex) {
                holder.actionBtn.setVisibility(View.VISIBLE);
                holder.actionBtn.setText(R.string.cancel_request);
                holder.actionBtn.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        final ContactsBean bean = (ContactsBean) v.getTag();
                        if (bean != null) {
                            mCoupleLogic.cancelRequest(bean,
                                    new CoupleCallback() {
                                        @Override
                                        public void onResult(boolean success) {
                                            // 取消成功, 修改状态, 更新条目
                                            bean.setCoupleShip(CoupleLogic.COUPLE_STATE_NULL);

                                            temRequestIndex = -1;
                                            if (mAdapter != null) {
                                                mAdapter.notifyDataSetChanged();
                                            }
                                        }
                                    });
                        }
                    }
                });
            }
            return convertView;
        }
    }

    class ViewHolder {
        public LinearLayout dividerLayout;
        public TextView divider;
        public ImageView icon;
        private TextView name;
        private TextView info;
        private TextView actionBtn;
    }
}
