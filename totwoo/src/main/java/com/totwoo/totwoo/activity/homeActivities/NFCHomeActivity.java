package com.totwoo.totwoo.activity.homeActivities;

import android.os.Bundle;
import android.view.KeyEvent;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.fragment.MeFragment;
import com.totwoo.totwoo.fragment.NfcFragment;

import java.util.ArrayList;

/**
 * NFC 首饰首页; 目前仅有一款 NFC 首饰: 秘密之戒; 不确定后续发布的 NFC 首饰是否共同这一个页面, 目前按照统一 NFC 主页设计;
 * 后续根据需求变更进行拆分或者重命名
 */
public class NFCHomeActivity extends HomeBaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initUI();
    }

    private void initUI() {
        setBottomLlColor(R.color.b_bottom_color);
        ArrayList<HomepageBottomInfo> infos = new ArrayList<>();
        infos.add(new HomepageBottomInfo(R.drawable.icon_nfc_home_unselected, R.drawable.icon_nfc_home, R.string.keep_secret));
        infos.add(new HomepageBottomInfo(R.drawable.icon_nfc_user_unselected, R.drawable.icon_nfc_user, R.string.user));
        setBottomInfo(infos);

        setBottomShowIconOnly(true);

        setFragmentsAndInitViewpager(new Class[]{
                NfcFragment.class,
                MeFragment.class
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!getSupportFragmentManager().getFragments().isEmpty() && getSupportFragmentManager().getFragments().get(0) != null) {
                return ((NfcFragment)getSupportFragmentManager().getFragments().get(0)).onBackPress();
            }
        }
        return super.onKeyDown(keyCode, event);
    }
}
