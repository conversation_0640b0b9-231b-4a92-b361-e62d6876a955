package com.totwoo.totwoo.activity;

import android.animation.Animator;
import android.annotation.SuppressLint;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewAnimationUtils;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;

import com.airbnb.lottie.LottieAnimationView;
import com.tencent.qcloud.tuicore.util.PermissionRequester;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.widget.ExpandableTextView;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 摇签决定是否的界面
 */
public class YesNoActivity extends BaseActivity implements View.OnClickListener, BluetoothManage.ConnectSuccessListener {

    @BindView(R.id.yes_main_scrollview)
    ScrollView mainScrollView;

    @BindView(R.id.yes_shake_info_tv)
    TextView shakeinfoTv;

    @BindView(R.id.yes_explain_info_tv)
    TextView explainTv;

    @BindView(R.id.yes_explain_up_iv)
    ImageView openIv;

    @BindView(R.id.yes_explain_extv)
    ExpandableTextView expTv;

//    @BindView(R.id.yes_progress_view)
//    YesNoProgressView mProgressView;

    @BindView(R.id.animation_view)
    LottieAnimationView lottieAnimationView;

    @BindView(R.id.yes_state_iv)
    ImageView statePic;
    @BindView(R.id.yes_state_info)
    TextView stateTv;
//    @BindView(R.id.yes_descrite_again_tv)
//    TextView againTv;

    /**
     * 相应首饰摇晃的监听器
     */
    private ShakeMonitor mSakeListener;

    @SuppressLint("WrongViewCast")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_yes_no);
        ButterKnife.bind(this);

        // 英文版界面微调
        if (!Apputils.systemLanguageIsChinese(this)) {
            shakeinfoTv.setVisibility(View.GONE);
            explainTv.setVisibility(View.GONE);
            findViewById(R.id.yes_shake_line).getLayoutParams().width = Apputils.dp2px(this, 160);
        }

        stateTv.setText(BleParams.isTouchJewelry() ? R.string.yes_no_start_info_touch : R.string.yes_no_start_info);


        String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        String yesTip = JewelryOnlineDataManager.getInstance().getConnectedJewInfo().getYes_tip();
        if (!TextUtils.isEmpty(yesTip)) {
            expTv.setText(yesTip);
        } else if (BleParams.isSM2(jewName)) {
            // 8x 系列新款首饰样式调整, 追加新的文言, 后续可能调整
            expTv.setText(R.string.yes_no_explain_info_touch_sm);
        } else if (BleParams.isTouchJewelry(jewName)) {
//            stateTv.setText(R.string.yes_no_start_info_touch_no_vibrate);
            expTv.setText(R.string.yes_no_explain_info_touch);
        } else {
//            stateTv.setText(R.string.yes_no_start_info);
            expTv.setText(R.string.yes_no_explain_info);
        }

        mSakeListener = new ShakeMonitor(this);

        initViewListener();
    }

    /**
     * 初始化所有的监听事件, View 的点击监听, 首饰的摇晃监听, 动画的开始结束监听
     */
    private void initViewListener() {
        expTv.setOnClickListener(this);
        openIv.setOnClickListener(this);
//        againTv.setOnClickListener(this);

        // 可展开的 TextView 展开监听, 实现展开时自动滚动界面
        expTv.setOnExpandListener(new ExpandableTextView.OnExpandListener() {
            @Override
            public void onExpand(ExpandableTextView parent) {
                // 文字关闭后把屏幕跟文字底部对齐
                int[] position = new int[2];
                expTv.getLocationOnScreen(position);
                final int value = position[1] + mainScrollView.getScrollY()
                        + expTv.getHeight()
                        - Apputils.getScreenHeight(YesNoActivity.this)
                        + 50;
                new Thread() {
                    public void run() {
                        for (int i = mainScrollView.getScrollY(); i < value; i += 10) {
                            SystemClock.sleep(5);
                            Message message = Message.obtain();
                            message.what = 0;
                            message.arg1 = i;
                            mHandler.sendMessage(message);
                        }
                    }
                }.start();
            }
        });

        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case 0:
                        mainScrollView.setScrollY(msg.arg1);
                        break;
                }
            }
        };
        lottieAnimationView.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                showView(statePic);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        // 设置摇首饰的监听
        mSakeListener.setOnEventListener((type) -> hideView(statePic));

        mHandler.postDelayed(() -> expTv.expand(false), 100);

    }

    @Override
    protected void onResume() {
        super.onResume();
        mSakeListener.start();
        BluetoothManage.getInstance().connectedStatus();
        BluetoothManage.getInstance().setConnectSuccessListener(this);
        BluetoothManage.getInstance().stayIn(true);

    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mSakeListener != null) {
            mSakeListener.stop();
        }
        BluetoothManage.getInstance().setConnectSuccessListener(null);
        BluetoothManage.getInstance().stayIn(false);
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        if (PermissionRequester.systemLanguageIsChinese(this)) {
            setTopTitle(R.string.yesno_title);
        } else {
            setTopTitle("");
        }
    }

    /**
     * 获取做决定的结果, 根据一定算法确定当次结果
     *
     * @return
     */
    private boolean getResult() {
        return (Math.random() > 0.5f);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.yes_explain_up_iv:
            case R.id.yes_explain_extv:
                if (!expTv.isExpanded()) {
                    openIv.setImageResource(R.drawable.cloes_minus_sign);
                } else {
                    openIv.setImageResource(R.drawable.open_plus);
                }
                expTv.toggle();
                break;
//            case R.id.yes_descrite_again_tv:
//                // 重新测试
//                againTv.setVisibility(View.GONE);
//                stateTv.setVisibility(View.VISIBLE);
//                stateTv.setText(R.string.yes_no_start_info);
//
//                statePic.setImageResource(R.drawable.yes_start_icon);

//                break;
        }
    }

    /**
     * 圆环收起的动画效果, 目前仅支持5.0以上平台
     *
     * @param view
     */
    private void hideView(View view) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            stateTv.setVisibility(View.VISIBLE);

            stateTv.setText(BleParams.isTouchJewelry() ? R.string.yes_no_loading_info_touch : R.string.yes_no_loading_info);
//            againTv.setVisibility(View.GONE);

            statePic.setVisibility(View.INVISIBLE);
            lottieAnimationView.setVisibility(View.VISIBLE);
            lottieAnimationView.playAnimation();
        } else {
            if (!view.isAttachedToWindow()) {
                return;
            }
            Animator hide = ViewAnimationUtils.createCircularReveal(
                    view,
                    view.getWidth() / 2, view.getHeight() / 2,
                    view.getWidth(), Apputils.dp2px(this, 30));

            hide.setDuration(1200);
            hide.setInterpolator(new AccelerateInterpolator(1f));
            hide.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    stateTv.setVisibility(View.VISIBLE);
                    stateTv.setText(BleParams.isTouchJewelry() ? R.string.yes_no_loading_info_touch : R.string.yes_no_loading_info);
//                    againTv.setVisibility(View.GONE);

                    statePic.setVisibility(View.INVISIBLE);
                    lottieAnimationView.setVisibility(View.VISIBLE);
                    lottieAnimationView.playAnimation();
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                }

                @Override
                public void onAnimationRepeat(Animator animation) {
                }
            });
            hide.start();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        lottieAnimationView.cancelAnimation();
    }

    /**
     * 圆环展开的动画效果, 目前仅支持5.0以上平台
     *
     * @param view
     */
    private void showView(View view) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            // 结果展示
//            againTv.setVisibility(View.VISIBLE);
            if (BleParams.isTouchJewelry()) {
                stateTv.setText(R.string.decision_again_touch);
            } else {
                stateTv.setText(R.string.decision_again);
            }

            lottieAnimationView.setVisibility(View.GONE);
            statePic.setVisibility(View.VISIBLE);
            statePic.setImageResource(getResult() ? R.drawable.yes_icon : R.drawable.no_icon);
        } else {
            if (!view.isAttachedToWindow()) {
                return;
            }
            Animator show = ViewAnimationUtils.createCircularReveal(
                    view,
                    view.getWidth() / 2, view.getHeight() / 2,
                    Apputils.dp2px(this, 30), view.getWidth());

            show.setDuration(600);
            show.setInterpolator(new DecelerateInterpolator(1f));
            show.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    // 结果展示
//                    againTv.setVisibility(View.VISIBLE);
                    if (BleParams.isTouchJewelry()) {
                        stateTv.setText(R.string.decision_again_touch);
                    } else {
                        stateTv.setText(R.string.decision_again);
                    }

                    lottieAnimationView.setVisibility(View.GONE);
                    statePic.setVisibility(View.VISIBLE);
                    statePic.setImageResource(getResult() ? R.drawable.yes_icon : R.drawable.no_icon);
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                }

                @Override
                public void onAnimationRepeat(Animator animation) {
                }
            });

            show.start();
        }


    }

    @Override
    public void onConnectSuccessd() {
        mHandler.postDelayed(() -> BluetoothManage.getInstance().stayIn(true), 2000);
    }
}
