package com.totwoo.totwoo.activity.nfc;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;

import com.blankj.utilcode.util.NetworkUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.data.nfc.SecretInfoManager;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;

/**
 * NFC 用户信息密码设置页面
 */
public class NfcSecretPasswdActivity extends BaseActivity {
    private SecretInfoManager secretInfoManager;
    private CheckBox pwdSwitch;
    private ViewGroup editLayout;
    private EditText pwdEt;
    private TextView saveTv;
    private TextView pwdInfoTv;
    private TextView switchTitleTv;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_nfc_passwd);

        secretInfoManager = SecretInfoManager.getInstance();

        initUI();

        secretInfoManager.fetchPwdOrDomain((code) -> {
            if (code == 0) {
                refreshUI(secretInfoManager.isPwdOpen());
            } else {
                ToastUtils.showLong(this, HttpHelper.getUnknownErrorMessage(this, String.valueOf(code)));
            }
        });
    }

    private void initUI() {
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        pwdSwitch = findViewById(R.id.nfc_pwd_switch);
        editLayout = findViewById(R.id.nfc_pwd_edit_layout);
        pwdEt = findViewById(R.id.nfc_pwd_edit_pwd_et);
        saveTv = findViewById(R.id.nfc_pwd_save_tv);
        pwdInfoTv = findViewById(R.id.nfc_pwd_info_tv);
        switchTitleTv = findViewById(R.id.nfc_pwd_switch_title);

        findViewById(R.id.nfc_sort_back).setOnClickListener(v -> finish());

        pwdSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            refreshUI(isChecked);
            secretInfoManager.updatePwd(isChecked, secretInfoManager.getPwd(), null);
        });

        saveTv.setOnClickListener(v -> {
            saveOrModifyPwd();
        });

        refreshUI(secretInfoManager.isPwdOpen());
    }

    /**
     * 根据当前数据状态, 刷新 UI
     *
     * @param isChecked
     */
    private void refreshUI(boolean isChecked) {
        pwdSwitch.setChecked(isChecked);
        if (isChecked) {
            saveTv.setVisibility(View.VISIBLE);
            switchTitleTv.setText(R.string.open_password);

            if (TextUtils.isEmpty(secretInfoManager.getPwd())) {
                editLayout.setVisibility(View.VISIBLE);
                pwdInfoTv.setVisibility(View.GONE);
                saveTv.setText(R.string.save);
            } else {
                editLayout.setVisibility(View.GONE);
                pwdInfoTv.setVisibility(View.VISIBLE);
                pwdInfoTv.setText(getString(R.string.you_secret_pwd_is, secretInfoManager.getPwd()));
                saveTv.setText(R.string.modify_password);
            }
        } else {
            switchTitleTv.setText(R.string.close_password);
            editLayout.setVisibility(View.GONE);
            saveTv.setVisibility(View.GONE);
            pwdInfoTv.setVisibility(View.GONE);
        }
    }

    private void saveOrModifyPwd() {
        if (editLayout.getVisibility() == View.GONE) {
            editLayout.setVisibility(View.VISIBLE);
            pwdInfoTv.setVisibility(View.GONE);
            saveTv.setText(R.string.save);
        } else {
            if (!NetworkUtils.isConnected()) {
                ToastUtils.showLong(this, R.string.error_net);
                return;
            }
            // 检查输入
            if (pwdEt.getText().length() < 6) {
                ToastUtils.showLong(this, R.string.verification_nfc_password);
                return;
            }
            NfcLoading.show(this);
            secretInfoManager.updatePwd(true, pwdEt.getText().toString(), code -> {
                NfcLoading.dismiss();
                if (code == 0) {
                    refreshUI(true);
                } else {
                    ToastUtils.showLong(this, HttpHelper.getUnknownErrorMessage(this, String.valueOf(code)));
                }
            });
        }
    }
}
