package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.ColorLibraryAdapter;
import com.totwoo.totwoo.adapter.WeekSelectAdapter;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.CustomNotifyDbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.WheelView;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;
import com.totwoo.totwoo.widget.pickerview.listener.OnDateSetListener;
import com.umeng.analytics.MobclickAgent;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/2/28.
 */

public class AddCustomNotifyActivity extends BaseActivity {

    //默认选择的是日程
    private int currentType = CustomItemBean.SCHEDULE;
    @BindView(R.id.add_custom_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    @BindView(R.id.add_custom_time_layout)
    FrameLayout mTimeFL;
    @BindView(R.id.add_custom_time_layout_view)
    View mTimeView;
    @BindView(R.id.add_custom_birthday_layout)
    FrameLayout mBirthdayFL;
    @BindView(R.id.add_custom_birthday_layout_view)
    View mBirthdayView;
    @BindView(R.id.add_custom_repeat_layout)
    FrameLayout mRepeatFL;
    @BindView(R.id.add_custom_repeat_layout_view)
    View mRepeatView;
    @BindView(R.id.add_custom_long_vibration_tv)
    TextView mLongVibrationTv;
    @BindView(R.id.add_custom_short_vibration_tv)
    TextView mShortVibrationTv;
    @BindView(R.id.add_custom_title_edittext)
    EditText mTitleEdittext;
    @BindView(R.id.add_custom_notify_value_tv)
    TextView mNotifyTimeTv;
    @BindView(R.id.add_custom_repeat_value_tv)
    TextView mRepeatTv;
    @BindView(R.id.add_custom_time_value_tv)
    TextView mTimeValueTv;
    @BindView(R.id.add_custom_birthday_value_tv)
    TextView mBirthdayValueTv;
    @BindView(R.id.add_custom_birthday_key_tv)
    TextView mBirthdayKeyTv;
    @BindView(R.id.schedule_iv)
    ImageView mScheduleIv;
    @BindView(R.id.schedule_select_iv)
    ImageView mScheduleSelectIv;
    @BindView(R.id.birthday_iv)
    ImageView mBirthdayIv;
    @BindView(R.id.birthday_select_iv)
    ImageView mBirthdaySelectIv;
    @BindView(R.id.anniversary_iv)
    ImageView mAnniversaryIv;
    @BindView(R.id.anniversary_select_iv)
    ImageView mAnniversarySelectIv;
    @BindView(R.id.other_iv)
    ImageView mOtherIv;
    @BindView(R.id.other_select_iv)
    ImageView mOtherSelectIv;
    @BindView(R.id.add_custom_type_tv)
    TextView mTypeTv;


    //    JewelryNotifyModel nowSetModel;
    private List<CustomItemBean> beans = new ArrayList<>();
//    private CustomItemBean currentBean;

//    private int currentNotifyTimeIndex = 3;
//    private int currentBirthNotifyTimeIndex = 1;
//    private String currentRepeatType = "0";
//    private String tempTime;
//    private String tempBirthday;

    private CustomItemBean currentBean;

    SimpleDateFormat allFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    SimpleDateFormat ymdFormat = new SimpleDateFormat("yyyy-MM-dd");

    private ColorLibraryAdapter colorLibraryAdapter;
    private CustomBottomDialog dialog;
    private boolean isChange = false;
    private List<String> defaultNotifyTimeStrs;
    private List<String> defaultBirthNotifyTimeStrs;
    private List<String> defaultAnnNotifyTimeStrs;
    private final static double DEFAULT_NOTIFY_TIME_DOUBLES[] = {0, 0.25, 0.5, 1, 6, 24, 72};
    private final static double DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[] = {24, 48, 72, 168};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        setContentView(R.layout.activity_add_custom_notify);
        ButterKnife.bind(this);
        String tempTime = allFormat.format(System.currentTimeMillis());
        String tempBirth = ymdFormat.format(System.currentTimeMillis());

        CustomItemBean bean1 = new CustomItemBean();
        bean1.setRemind_time(DEFAULT_NOTIFY_TIME_DOUBLES[3] + "");
        bean1.setRepeat_notify("0");
        bean1.setDefine_time(tempTime);
        bean1.setShock_type(JewelryNotifyModel.LONG);
        bean1.setNotify_mode("RED");
        beans.add(bean1);

        CustomItemBean bean2 = new CustomItemBean();
        bean2.setRemind_time(DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[1] + "");
        bean2.setRepeat_notify("0");
        bean2.setDefine_time(tempBirth);
        bean2.setShock_type(JewelryNotifyModel.LONG);
        bean2.setNotify_mode("RED");
        beans.add(bean2);

        CustomItemBean bean3 = new CustomItemBean();
        bean3.setRemind_time(DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[1] + "");
        bean3.setRepeat_notify("0");
        bean3.setDefine_time(tempBirth);
        bean3.setShock_type(JewelryNotifyModel.LONG);
        bean3.setNotify_mode("RED");
        beans.add(bean3);

        CustomItemBean bean4 = new CustomItemBean();
        bean4.setRemind_time(DEFAULT_NOTIFY_TIME_DOUBLES[3] + "");
        bean4.setRepeat_notify("0");
        bean4.setDefine_time(tempTime);
        bean4.setShock_type(JewelryNotifyModel.LONG);
        bean4.setNotify_mode("RED");
        beans.add(bean4);

        currentBean = beans.get(currentType - 1);
        initView();
        scheduleShow();
        defaultNotifyTimeStrs = new ArrayList<>();
        int size = DEFAULT_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            defaultNotifyTimeStrs.add(getHourStringByIndex(i));
        }
        defaultBirthNotifyTimeStrs = new ArrayList<>();
        int birthSize = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < birthSize; i++) {
            defaultBirthNotifyTimeStrs.add(getBirthHourStringByIndex(i));
        }
        defaultAnnNotifyTimeStrs = new ArrayList<>();
        for (int i = 0; i < birthSize; i++) {
            defaultAnnNotifyTimeStrs.add(getAnnHourStringByIndex(i));
        }
    }

    @Override
    public void onBackPressed() {
        quit();
    }

    private boolean isChangeThings() {
        if (isChange) {
            return true;
        }
        for (CustomItemBean customItemBean : beans) {
            if (!TextUtils.isEmpty(customItemBean.getTitle())) {
                return true;
            }
        }
        if (!TextUtils.isEmpty(mTitleEdittext.getText().toString().trim())) {
            return true;
        }
        return false;
    }

    private void quit() {
        if (isChangeThings()) {
            final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(AddCustomNotifyActivity.this);
            commonMiddleDialog.setMessage(R.string.add_custom_notify_quit_hint);
            commonMiddleDialog.setSure(v -> {
                finish();
                commonMiddleDialog.dismiss();
            });
            commonMiddleDialog.setCancel(R.string.give_up);
            commonMiddleDialog.show();
        } else {
            finish();
        }
    }

    private void chooseType(int type) {
        if (currentType == type) {
            return;
        }

        currentBean.setTitle(mTitleEdittext.getText().toString().trim());
        beans.set(currentType - 1, currentBean);

        switch (type) {
            case CustomItemBean.SCHEDULE:
                defaultSetting();
                mScheduleIv.setImageResource(R.drawable.custom_notify_schedule);
                mScheduleSelectIv.setVisibility(View.VISIBLE);
                mTypeTv.setText(R.string.custom_notify_type_event);
                scheduleShow();
                break;
            case CustomItemBean.BIRTHDAY:
                defaultSetting();
                mBirthdayIv.setImageResource(R.drawable.custom_notify_birthday);
                mBirthdaySelectIv.setVisibility(View.VISIBLE);
                mTypeTv.setText(R.string.custom_notify_type_birthday);
                birthdayShow();
                break;
            case CustomItemBean.ANNIVERSARY:
                defaultSetting();
                mAnniversaryIv.setImageResource(R.drawable.custom_notify_anniversary);
                mAnniversarySelectIv.setVisibility(View.VISIBLE);
                mTypeTv.setText(R.string.custom_notify_type_anniversary);
                birthdayShow();
                break;
            case CustomItemBean.OTHER:
                defaultSetting();
                mOtherIv.setImageResource(R.drawable.custom_notify_other);
                mOtherSelectIv.setVisibility(View.VISIBLE);
                mTypeTv.setText(R.string.custom_notify_type_others);
                scheduleShow();
                break;
        }

        currentType = type;
        currentBean = beans.get(currentType - 1);
        mTitleEdittext.setText(currentBean.getTitle());
        if (currentType == CustomItemBean.SCHEDULE || currentType == CustomItemBean.OTHER) {
            mNotifyTimeTv.setText(getHourStringByIndex(getHourIndexByDouble(Double.valueOf(currentBean.getRemind_time()))));
            StringBuffer buffer = new StringBuffer(currentBean.getDefine_time());
            String year = buffer.substring(0, 4);
            String month = buffer.substring(5, 7);
            String day = buffer.substring(8, 10);
            String hourAndMin = buffer.substring(11);
            mTimeValueTv.setText(year + "-" + month + "-" + day + "  " + hourAndMin);
        } else {
            if (currentType == CustomItemBean.BIRTHDAY) {
                mNotifyTimeTv.setText(getBirthHourStringByIndex(getBirthHourIndexByDouble(Double.valueOf(currentBean.getRemind_time()))));
            } else {
                mNotifyTimeTv.setText(getAnnHourStringByIndex(getBirthHourIndexByDouble(Double.valueOf(currentBean.getRemind_time()))));
            }
            StringBuffer buffer = new StringBuffer(currentBean.getDefine_time());
            String year = buffer.substring(0, 4);
            String month = buffer.substring(5, 7);
            String day = buffer.substring(8, 10);
            mBirthdayValueTv.setText(year + "-" + month + "-" + day);
        }

        mRepeatTv.setText(setStringToView(currentBean.getRepeat_notify()));
        colorLibraryAdapter.setSelectColor(currentBean.getNotify_mode());
        switch (currentBean.getShock_type()) {
            case JewelryNotifyModel.LONG:
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                setTextColorBtn(true);
                break;
            case JewelryNotifyModel.SHORT:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                setTextColorBtn(false);
                break;
        }
    }

    private void scheduleShow() {
        mBirthdayFL.setVisibility(View.GONE);
        mBirthdayView.setVisibility(View.GONE);
        mTimeFL.setVisibility(View.VISIBLE);
        mTimeView.setVisibility(View.VISIBLE);
        mRepeatFL.setVisibility(View.VISIBLE);
        mRepeatView.setVisibility(View.VISIBLE);
    }

    private void birthdayShow() {
        mBirthdayFL.setVisibility(View.VISIBLE);
        mBirthdayView.setVisibility(View.VISIBLE);
        mTimeFL.setVisibility(View.GONE);
        mTimeView.setVisibility(View.GONE);
        mRepeatFL.setVisibility(View.GONE);
        mRepeatView.setVisibility(View.GONE);
    }

    private void defaultSetting() {
//        mScheduleIv.setImageResource(R.drawable.custom_notify_schedule_un);
//        mBirthdayIv.setImageResource(R.drawable.custom_notify_birthday_un);
//        mAnniversaryIv.setImageResource(R.drawable.custom_notify_anniversary_un);
//        mOtherIv.setImageResource(R.drawable.custom_notify_other_un);
        mScheduleSelectIv.setVisibility(View.GONE);
        mBirthdaySelectIv.setVisibility(View.GONE);
        mAnniversarySelectIv.setVisibility(View.GONE);
        mOtherSelectIv.setVisibility(View.GONE);
    }

    private void initView() {
        mTitleEdittext.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null && s.length() > 0) {
                    int len = s.length();
                    if (Apputils.systemLanguageIsChinese(getApplicationContext()) && len > 10) {
                        s = s.subSequence(0, 10);
                        mTitleEdittext.setText(s);
                        ToastUtils.showShort(AddCustomNotifyActivity.this, "最多只能10个汉字");
                        mTitleEdittext.setSelection(10);
                    } else if (!Apputils.systemLanguageIsChinese(getApplicationContext()) && len > 20) {
                        s = s.subSequence(0, 20);
                        mTitleEdittext.setText(s);
                        ToastUtils.showShort(AddCustomNotifyActivity.this, "20 characters");
                        mTitleEdittext.setSelection(20);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        switch (currentBean.getShock_type()) {
            case JewelryNotifyModel.LONG:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                setTextColorBtn(true);
                break;
            case JewelryNotifyModel.SHORT:
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));

                setTextColorBtn(false);
                break;
        }

        StringBuffer buffer = new StringBuffer(currentBean.getDefine_time());
        String year = buffer.substring(0, 4);
        String month = buffer.substring(5, 7);
        String day = buffer.substring(8, 10);
        String hourAndMin = buffer.substring(11);
        mTimeValueTv.setText(year + "-" + month + "-" + day + "  " + hourAndMin);
        mNotifyTimeTv.setText(getHourStringByIndex(getHourIndexByDouble(Double.valueOf(currentBean.getRemind_time()))));
        mRepeatTv.setText(setStringToView(currentBean.getRepeat_notify()));

        colorLibraryRecyclerView.setLayoutManager(new LinearLayoutManager(AddCustomNotifyActivity.this, LinearLayoutManager.HORIZONTAL, false));
        colorLibraryAdapter = new ColorLibraryAdapter(currentBean.getNotify_mode(), AddCustomNotifyActivity.this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                currentBean.setNotify_mode((String) v.getTag());
                isChange = true;
                colorLibraryAdapter.setSelectColor((String) v.getTag());
                saveNowModel();
            }
        });
        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
        colorLibraryRecyclerView.scrollToPosition(colorLibraryAdapter.getIndex(currentBean.getNotify_mode()));

    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_white);
        setTopLeftOnclik(v -> quit());
        setTopTitle(getString(R.string.custom_notify_add_title));
        setTopTitleColor(getResources().getColor(R.color.white));

        setTopbarBackground(R.color.notification_red);
    }


    @OnClick({R.id.add_custom_schedule, R.id.add_custom_birthday, R.id.add_custom_anniversary,
            R.id.add_custom_other, R.id.add_custom_long_vibration_tv, R.id.add_custom_short_vibration_tv,
            R.id.add_custom_notify_layout, R.id.add_custom_repeat_layout, R.id.add_custom_time_layout,
            R.id.add_custom_birthday_layout, R.id.add_custom_save_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.add_custom_schedule:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_CUSTOM_ADD_EVENT);
                chooseType(CustomItemBean.SCHEDULE);
                break;
            case R.id.add_custom_birthday:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_CUSTOM_ADD_BIRTHDAY);
                mBirthdayKeyTv.setText(getString(R.string.custom_notify_type_birthday));
                chooseType(CustomItemBean.BIRTHDAY);
                break;
            case R.id.add_custom_anniversary:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_CUSTOM_ADD_ANNIVERSARY);
                mBirthdayKeyTv.setText(getString(R.string.custom_notify_type_anniversary));
                chooseType(CustomItemBean.ANNIVERSARY);
                break;
            case R.id.add_custom_other:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_CUSTOM_ADD_OTHER);
                chooseType(CustomItemBean.OTHER);
                break;
            case R.id.add_custom_long_vibration_tv:
                isChange = true;
                currentBean.setShock_type(JewelryNotifyModel.LONG);
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mShortVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                saveNowModel();
                setTextColorBtn(true);
                break;

            case R.id.add_custom_short_vibration_tv:
                isChange = true;
                currentBean.setShock_type(JewelryNotifyModel.SHORT);
                mLongVibrationTv.setBackground(null);
                view.setBackground(getResources().getDrawable(R.drawable.shape_solid_black_8));
                mLongVibrationTv.setBackground(getResources().getDrawable(R.drawable.change_music_vibration_unselect_bg));
                saveNowModel();
                setTextColorBtn(false);
                break;
            case R.id.add_custom_notify_layout:
                if (currentType == CustomItemBean.SCHEDULE || currentType == CustomItemBean.OTHER)
                    showNotifyDialog();
                else if (currentType == CustomItemBean.BIRTHDAY)
                    showBirthNotifyDialog();
                else
                    showAnnNotifyDialog();

                break;
            case R.id.add_custom_repeat_layout:
                showRepeatDialog();
                break;
            case R.id.add_custom_time_layout:
                try {
                    showTimeDialog();
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                break;
            case R.id.add_custom_birthday_layout:
                if (currentType == CustomItemBean.BIRTHDAY) {
                    try {
                        showBirthDialog();
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                } else if (currentType == CustomItemBean.ANNIVERSARY) {
                    try {
                        showAnnDialog();
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
                break;
            case R.id.add_custom_save_tv:
                saveInfo();
                break;
        }
    }

    private void saveInfo() {
        String title = mTitleEdittext.getText().toString().trim();
        if (TextUtils.isEmpty(title)) {
            ToastUtils.showShort(AddCustomNotifyActivity.this, getString(R.string.custom_notify_input_title));
            return;
        }

        //talk_id和target_uid是爱的提醒需要的参数。这里传空就行了
        HttpHelper.customService.saveCustom(1, currentType, title, currentBean.getDefine_time(),
                        currentBean.getRemind_time(), currentBean.getRepeat_notify(),
                        currentBean.getShock_type(), currentBean.getNotify_mode(), "", "")
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<CustomItemBean>>() {
                    @Override
                    public void onCompleted() {
                        if (!TextUtils.equals(getIntent().getStringExtra("from"), "list")) {
                            startActivity(new Intent(AddCustomNotifyActivity.this, CustomNotifyListActivity.class));
                        }
                        finish();
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(AddCustomNotifyActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<CustomItemBean> customItemBeanHttpBaseBean) {
                        if (customItemBeanHttpBaseBean.getErrorCode() == 0) {
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ADD_SUCCESSED, null);
                            CustomItemBean customItemBean = customItemBeanHttpBaseBean.getData();
                            customItemBean.setDefine_time(currentBean.getDefine_time());
                            CustomNotifyDbHelper.getInstance().addBean(customItemBean);
                        }
                    }
                });

    }

    private void showBirthDialog() throws ParseException {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        long hunrundYears = 100L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        String tempBirthday = format.format(millseconds);
                        isChange = true;
                        currentBean.setDefine_time(tempBirthday);
                        StringBuffer buffer = new StringBuffer(tempBirthday);
                        String year = buffer.substring(0, 4);
                        String month = buffer.substring(5, 7);
                        String day = buffer.substring(8, 10);
                        mBirthdayValueTv.setText(year + "-" + month + "-" + day);

                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
//                .setTitleStringId(getString(R.string.custom_notify_remind_birthday))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - hunrundYears)
                .setMaxMillseconds(System.currentTimeMillis())
                .setCurrentMillseconds(format.parse(currentBean.getDefine_time()).getTime())
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "year_month_day");
    }

    private void showAnnDialog() throws ParseException {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        long tenYears = 10L * 365 * 1000 * 60 * 60 * 24L;
        long hunrundYears = 100L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        String tempBirthday = format.format(millseconds);
                        isChange = true;
                        currentBean.setDefine_time(tempBirthday);
                        StringBuffer buffer = new StringBuffer(tempBirthday);
                        String year = buffer.substring(0, 4);
                        String month = buffer.substring(5, 7);
                        String day = buffer.substring(8, 10);
                        mBirthdayValueTv.setText(year + "-" + month + "-" + day);

                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
//                .setTitleStringId(getString(R.string.custom_notify_remind_anniversary))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - hunrundYears)
                .setMaxMillseconds(System.currentTimeMillis() + tenYears)
                .setCurrentMillseconds(format.parse(currentBean.getDefine_time()).getTime())
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "year_month_day");
    }

    private void showTimeDialog() throws ParseException {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        long tenYears = 10L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        String tempTime = format.format(millseconds);
                        isChange = true;
                        currentBean.setDefine_time(tempTime);
                        StringBuffer buffer = new StringBuffer(tempTime);
                        String year = buffer.substring(0, 4);
                        String month = buffer.substring(5, 7);
                        String day = buffer.substring(8, 10);
                        String hourAndMin = buffer.substring(11);
                        mTimeValueTv.setText(year + "-" + month + "-" + day + "  " + hourAndMin);

                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
//                .setTitleStringId(getString(R.string.custom_notify_remind_time_dialog_title))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis())
                .setMaxMillseconds(System.currentTimeMillis() + tenYears)
                .setCurrentMillseconds(format.parse(currentBean.getDefine_time()).getTime())
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.ALL)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "all");
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationTv.setTextColor(0xffffffff);
            mShortVibrationTv.setTextColor(0xde000000);
        } else {
            mShortVibrationTv.setTextColor(0xffffffff);
            mLongVibrationTv.setTextColor(0xde000000);
        }
    }

    private void saveNowModel() {
        JewelryNotifyModel jewelryNotifyModel = new JewelryNotifyModel();
        jewelryNotifyModel.setFlashColor(currentBean.getNotify_mode());
        if (TextUtils.equals(currentBean.getShock_type(), JewelryNotifyModel.LONG))
            jewelryNotifyModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
        else
            jewelryNotifyModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);

        BluetoothManage.getInstance().notifyJewelry(jewelryNotifyModel.getVibrationSeconds(), jewelryNotifyModel.getFlashColorValue());
    }

    private void showNotifyDialog() {
        dialog = new CustomBottomDialog(AddCustomNotifyActivity.this);
//        dialog.setTitle(getString(R.string.custom_notify_remind_time));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(getHourIndexByDouble(Double.valueOf(currentBean.getRemind_time())));
        weekLayout.addView(weekWheelView);
        dialog.setMainView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String str = weekWheelView.getSeletedItem();
            isChange = true;
            mNotifyTimeTv.setText(str);
            currentBean.setRemind_time(DEFAULT_NOTIFY_TIME_DOUBLES[defaultNotifyTimeStrs.indexOf(str)] + "");
            dialog.dismiss();
        });
        dialog.show();
    }

    private void showBirthNotifyDialog() {
        dialog = new CustomBottomDialog(AddCustomNotifyActivity.this);
//        dialog.setTitle(getString(R.string.custom_notify_remind_time));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultBirthNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(getBirthHourIndexByDouble(Double.valueOf(currentBean.getRemind_time())));
        weekLayout.addView(weekWheelView);
        dialog.setMainView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String str = weekWheelView.getSeletedItem();
            isChange = true;
            mNotifyTimeTv.setText(str);
            currentBean.setRemind_time(DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[defaultBirthNotifyTimeStrs.indexOf(str)] + "");
            dialog.dismiss();
        });
        dialog.show();
    }

    private void showAnnNotifyDialog() {
        dialog = new CustomBottomDialog(AddCustomNotifyActivity.this);
//        dialog.setTitle(getString(R.string.custom_notify_remind_time));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultAnnNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(getBirthHourIndexByDouble(Double.valueOf(currentBean.getRemind_time())));
        weekLayout.addView(weekWheelView);
        dialog.setMainView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
            String str = weekWheelView.getSeletedItem();
            isChange = true;
            mNotifyTimeTv.setText(str);
            currentBean.setRemind_time(DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[defaultAnnNotifyTimeStrs.indexOf(str)] + "");
            dialog.dismiss();
        });
        dialog.show();
    }

    private void showRepeatDialog() {
        dialog = new CustomBottomDialog(AddCustomNotifyActivity.this);
//        dialog.setTitle(getString(R.string.custom_notify_remind_mode));
//        dialog.setInfo(getString(R.string.custom_notify_remind_mode_info));
        RecyclerView recyclerView = new RecyclerView(this);
        recyclerView.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                Apputils.dp2px(this, 200)));
        recyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        final WeekSelectAdapter adapter = new WeekSelectAdapter(currentBean.getRepeat_notify(), this);
        recyclerView.setAdapter(adapter);

        dialog.setMainView(recyclerView);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setSaveClick(v -> {
            currentBean.setRepeat_notify(adapter.getSelectWeek());
            isChange = true;
            mRepeatTv.setText(setStringToView(adapter.getSelectWeek()));
            dialog.dismiss();
        });
        dialog.show();
    }

    private String getHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_event_now);
        switch (index) {
            case 0:
                string = getString(R.string.custom_notify_event_now);
                break;
            case 1:
                string = getString(R.string.custom_notify_event_15m);
                break;
            case 2:
                string = getString(R.string.custom_notify_event_30m);
                break;
            case 3:
                string = getString(R.string.custom_notify_event_1h);
                break;
            case 4:
                string = getString(R.string.custom_notify_event_6h);
                break;
            case 5:
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 6:
                string = getString(R.string.custom_notify_remind_3d);
                break;
        }
        return string;
    }

    private String getBirthHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_remind_1d);
        switch (index) {
            case 0:
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 1:
                string = getString(R.string.custom_notify_remind_2d);
                break;
            case 2:
                string = getString(R.string.custom_notify_remind_3d);
                break;
            case 3:
                string = getString(R.string.custom_notify_remind_7d);
                break;
            case 4:
                break;
        }
        return string;
    }

    private String getAnnHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_remind_1d);
        switch (index) {
//            case 0:
//                string = getString(R.string.custom_notify_remind_ann_day);
//                break;
            case 0:
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 1:
                string = getString(R.string.custom_notify_remind_2d);
                break;
            case 2:
                string = getString(R.string.custom_notify_remind_3d);
                break;
            case 3:
                string = getString(R.string.custom_notify_remind_7d);
                break;
        }
        return string;
    }

    private String setStringToView(String str) {
        if (TextUtils.isEmpty(str) || TextUtils.equals(str, "0")) {
            return getString(R.string.custom_notify_one_time);
        } else if (TextUtils.equals(str, "1,2,3,4,5,6,7")) {
            return getString(R.string.custom_notify_every_day);
        } else {
            StringBuffer buffer = new StringBuffer();
            char[] chars = str.toCharArray();
            int charLength = chars.length;
            for (int i = 0; i < charLength; i++) {
                if (TextUtils.equals(",", String.valueOf(chars[i]))) {
                    buffer.append("、");
                } else {
                    char tempChar = chars[i];
                    int tempInt = Integer.parseInt(String.valueOf(tempChar));
                    buffer.append(getResources().getStringArray(R.array.week_name)[tempInt - 1]);
                }
            }
            return buffer.toString();
        }
    }

    private int getHourIndexByDouble(double d) {
        int index = 0;
        int size = DEFAULT_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            if (d == DEFAULT_NOTIFY_TIME_DOUBLES[i]) {
                index = i;
            }
        }
        return index;
    }

    private int getBirthHourIndexByDouble(double d) {
        int index = 0;
        int size = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            if (d == DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[i]) {
                index = i;
            }
        }
        return index;
    }

    @Override
    protected void onResume() {
        super.onResume();
        BluetoothManage.getInstance().connectedStatus();
    }

}
