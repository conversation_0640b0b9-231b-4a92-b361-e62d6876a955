package com.totwoo.totwoo.activity.memory;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;

import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.holderBean.ImageFolderBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashSet;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/4.
 */

public class MemoryPhotoFolderActivity extends BaseActivity implements SubscriberListener, AdapterView.OnItemClickListener, View.OnClickListener
{
    @BindView(R.id.memory_photo_folder_lv)
    public ListView lv;

    private String content;
    private ArrayList<ImageFolderBean> folderList;
    private LinkedHashSet<String> selectedPhotoPath;
    private ImageFolderBean lastFolder;
    private boolean isAdd;

    private MemoryPhotoFolderAdapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_photo_folder);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        initData();
        initListView();
    }

    private void initData()
    {
        Intent intent = getIntent();
        content = intent.getStringExtra(S.M.M_CONTENT);
        folderList = (ArrayList<ImageFolderBean>) intent.getSerializableExtra(S.M.M_FOLDERS);
        selectedPhotoPath = (LinkedHashSet<String>) intent.getSerializableExtra(S.M.M_SELECTED);
        lastFolder = (ImageFolderBean) intent.getSerializableExtra(S.M.M_FOLDER);
        isAdd = intent.getBooleanExtra(S.M.M_ADD, false);
        if (lastFolder == null)
            lastFolder = folderList.get(0);
    }

    private void initListView()
    {
        adapter = new MemoryPhotoFolderAdapter(this, folderList);
        lv.setAdapter(adapter);
        lv.setOnItemClickListener(this);
    }

    @Override
    protected void initTopBar()
    {
        setTopbarBackground(R.color.layer_bg_white);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.memory_photo_Album);
        setTopLeftOnclik(this);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id)
    {
        ImageFolderBean ifb = adapter.resource.get(position);
        Intent intent = new Intent(this, MemoryPhotoSelectActivity.class);
        intent.putExtra(S.M.M_CONTENT, content);
//        intent.putExtra(S.M.M_FOLDERS, (Serializable)folderList);
        intent.putExtra(S.M.M_SELECTED, selectedPhotoPath);
        intent.putExtra(S.M.M_IMAGES, (Serializable)ifb.imgList);
        intent.putExtra(S.M.M_FOLDER, ifb);
        intent.putExtra(S.M.M_ADD, isAdd);
        startActivity(intent);
        this.finish();
    }

    @Override
    public void onClick(View v)
    {
        Intent intent = new Intent(this, MemoryPhotoSelectActivity.class);
        intent.putExtra(S.M.M_CONTENT, content);
//        intent.putExtra(S.M.M_FOLDERS, folderList);
        intent.putExtra(S.M.M_SELECTED, selectedPhotoPath);
        intent.putExtra(S.M.M_IMAGES, lastFolder.imgList);
        intent.putExtra(S.M.M_FOLDER, lastFolder);
        intent.putExtra(S.M.M_ADD, isAdd);
        startActivity(intent);
        this.finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {

    }

    @Override
    public void onBackPressed()
    {
        onClick(null);
        super.onBackPressed();
    }
}
