package com.totwoo.totwoo.activity.memory;

import android.app.Dialog;
import android.content.Intent;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.SceneAnimation;

import java.text.SimpleDateFormat;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/8.
 */

public class MemoryAudioShowActivity extends BaseActivity implements SubscriberListener, View.OnClickListener
{
    @BindView(R.id.make_card_audio_play_btn)
    ImageView mAudioVideoPlayBtn;

    @BindView(R.id.memory_photo_show_tv)
    public TextView tv;

    @BindView (R.id.audio_layout)
    public FrameLayout layout;

    @BindView (R.id.audio_gif)
    public ImageView audioGif;

    private MemoryBean mb;
    private CustomDialog dialog;

    private MediaPlayer mPlayer;

    private Dialog dialog1;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_audio_show1);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        initData();
        initTopBar2();
        setContent();
        mHandler.postDelayed(() -> initVedioView(), 1000);
    }

    SceneAnimation sa;
    private void initVedioView()
    {
        try
        {
            sa = new SceneAnimation(audioGif, MemoryAudioActivity.gifs, 10, false);
            mPlayer = new MediaPlayer();
            mPlayer.setDataSource(this, Uri.parse(mb.audio_url));
            mPlayer.prepare();
            layout.setOnClickListener(this);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        mAudioVideoPlayBtn.setOnClickListener(v -> {
            mPlayer.start();
            mAudioVideoPlayBtn.setVisibility(View.GONE);
            sa.start();
            mPlayer.setOnCompletionListener(mp -> {
                mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                sa.stop();
            });
        });
    }

    private void setContent()
    {
        tv.setVisibility(View.VISIBLE);
        tv.setText(mb.content);
    }

    private void initData()
    {
        Intent i = this.getIntent();
        mb = (MemoryBean) i.getSerializableExtra(S.M.M_IMAGES);
    }

    private void initTopBar2()
    {
        setTopBackIcon(R.drawable.back_icon_white);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = sdf.format(new Date(mb.create_time));
        setTopTitle(title);
        setTopTitleColor(getResources().getColor(R.color.text_color_white_important));
        setTopRightIcon(R.drawable.memory_photo_show_delete);
        setTopRightOnClick(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                getPhotoDialog();
            }
        });
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        if (dialog1 != null)
            dialog1.dismiss();
    }

    public void getPhotoDialog()
    {
        dialog = new CustomDialog(this);
        dialog.setTitle("");
        dialog.setMessage(R.string.memory_delete);
        dialog.setPositiveButton(R.string.memory_delete_ok, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                MemoryController.getInstance().delete(mb);
            }
        });
        dialog.setNegativeButton(R.string.memory_delete_no, new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void onDeleteMemorySuccessed(EventData data)
    {
        ToastUtils.showShort(this, R.string.memory_delete_success);
        if (dialog1 != null)
            dialog1.dismiss();
        this.finish();
    }

    @EventInject(eventType = S.E.E_MEMORY_DELETE_FAILED, runThread = TaskType.UI)
    public void onDeleteMemoryFailed(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        String msg = hv.errorMesg;
        ToastUtils.showShort(this, msg);
        if (dialog1 != null)
            dialog1.dismiss();
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
        try {
            if (mPlayer != null) {
                mPlayer.release();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onPause()
    {
        super.onPause();

        try
        {
            if (mPlayer != null && mPlayer.isPlaying())
            {
                mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
                mPlayer.pause();
                sa.stop();
            }
        }
        catch (IllegalStateException e)
        {
        }
        catch (Exception e)
        {
        }
    }

    @Override
    public void onClick(View v)
    {
        if (mPlayer.isPlaying())
        {
            mAudioVideoPlayBtn.setVisibility(View.VISIBLE);
            mPlayer.pause();
            sa.stop();
        }
    }
}