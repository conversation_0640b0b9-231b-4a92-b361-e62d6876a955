package com.totwoo.totwoo.activity.wish;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;

import com.bumptech.glide.Glide;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.PlayerView;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.databinding.ActivityMediaPrewBinding;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;

/**
 * 多媒体预览: 支持图片, 视频(封面图)
 */
public class MediaPreviewActivity extends BaseActivity {
    private static final String TAG = "MediaPreviewActivity";

    private PreviewConfig config;
    private ExoPlayer exoPlayer;
    private PlayerView playerView;
    private ActivityMediaPrewBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMediaPrewBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        CommonUtils.setStateBar(this, true);

        config = (PreviewConfig) getIntent().getSerializableExtra(PreviewConfig.EXTRA_PREVIEW_CONFIG_TAG);

        if (config == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        try {
            initData();
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showLong(this, R.string.data_error);
        }
    }

    private void initData() {
        // 显示封面图片
        String imageUrl = config.getCoverPath() == null ? config.getVideoPath() : config.getCoverPath();
        if (!TextUtils.isEmpty(imageUrl)) {
            Glide.with(this)
                    .load(HttpHelper.getRealImageUrl(imageUrl))
                    .into(binding.mediaCoverView);
        }

        // 显示信息文本
        if (!TextUtils.isEmpty(config.getInfo())) {
            binding.mediaInfoTv.setVisibility(View.VISIBLE);
            binding.mediaInfoTv.setText(config.getInfo());
        }

        // 如果有视频，初始化播放器
        if (!TextUtils.isEmpty(config.getVideoPath())) {
            initExoPlayer();
        } else {
            binding.mediaProgressbar.setVisibility(View.GONE);
        }
    }

    private void initExoPlayer() {
        // 创建简单的 ExoPlayer 实例
        exoPlayer = new ExoPlayer.Builder(getApplicationContext()).build();

        // 设置播放器视图
        playerView = binding.mediaVideoView;
        playerView.setPlayer(exoPlayer);

        // 创建媒体项并播放
        String videoUrl = HttpHelper.getRealImageUrl(config.getVideoPath());
        MediaItem mediaItem = MediaItem.fromUri(videoUrl);
        exoPlayer.setMediaItem(mediaItem);

        // 设置循环播放
        if (config.isLoopPlay()) {
            exoPlayer.setRepeatMode(Player.REPEAT_MODE_ONE);
        }

        // 简单的播放状态监听
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                if (playbackState == Player.STATE_READY) {
                    // 视频准备就绪，隐藏封面和进度条
                    binding.mediaProgressbar.setVisibility(View.GONE);
                    binding.mediaCoverView.setVisibility(View.GONE);
                    playerView.setVisibility(View.VISIBLE);
                } else if (playbackState == Player.STATE_BUFFERING) {
                    binding.mediaProgressbar.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onPlayerError(PlaybackException error) {
                binding.mediaProgressbar.setVisibility(View.GONE);
                ToastUtils.showLong(getApplicationContext(), R.string.video_play_error);
            }
        });

        // 准备并开始播放
        exoPlayer.prepare();
        exoPlayer.setPlayWhenReady(true);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (exoPlayer != null) {
            exoPlayer.play();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (exoPlayer != null) {
            exoPlayer.pause();
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 释放播放器资源
        if (exoPlayer != null) {
            exoPlayer.release();
            exoPlayer = null;
        }
        if (playerView != null) {
            playerView.setPlayer(null);
            playerView = null;
        }
        // 清理 binding 引用
        binding = null;
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopLeftOnclik(v -> finish());
        setTopbarBackground(R.color.black);

        if (config.getTitleId() != 0) {
            setTopTitle(config.getTitleId());
            setTopTitleColor(0xffffffff);
        }

        if (config.getMenuListener() != null) {
            if (config.getMenuIcon() != 0) {
                setTopRightIcon(config.getMenuIcon());
            } else {
                setTopRightString(config.getMenuTextId());
            }
            setTopRightOnClick(v -> config.getMenuListener().onClick(this));
        }
    }
}
