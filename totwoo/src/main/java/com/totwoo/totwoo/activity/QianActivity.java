package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.gson.Gson;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ConstellationComment;
import com.totwoo.totwoo.bean.JewelryOnlineDataBean;
import com.totwoo.totwoo.bean.Qian;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.fragment.QianDetailPage;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import org.json.JSONObject;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Created by lixingmao on 2017/2/24.
 */

public class QianActivity extends BaseActivity implements BluetoothManage.ConnectSuccessListener {

    @BindView(R.id.qian_type_love)
    TextView mQianTypeLove;
    @BindView(R.id.qian_type_wealth)
    TextView mQianTypeWealth;
    @BindView(R.id.qian_date_career)
    TextView mQianDateCareer;
    @BindView(R.id.qian_content_viewpager)
    ViewPager mQianContentViewpager;
    @BindView(R.id.qian_date_cursor)
    ImageView mQianDateCursor;

    /**
     * 手机摇一摇监听器
     */
    private ShakeMonitor shakeMonitor;

    private QianPageAdapter mAdapter;

    /**
     * 铃声播放池
     */
    private SoundPool soundPool;

    private int qianType;

    /**
     * 星点评数据
     */
    private ConstellationComment comment;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qian);
        ButterKnife.bind(this);

        soundPool = new SoundPool(10, AudioManager.STREAM_RING, 5);

        mAdapter = new QianPageAdapter();
        mQianContentViewpager.setAdapter(mAdapter);

        initCursonanimer();

        initData();
        qianType = getIntent().getIntExtra(CommonArgs.SELECT_INDEX, 0);

        // 更新顶部栏的状态
        updateDateBar(qianType);
        mQianContentViewpager.setCurrentItem(qianType);
        setShareStatus();
        float offset = (Apputils.getScreenWidth(QianActivity.this) * qianType) / 3f;
        mQianDateCursor.setTranslationX(offset);
    }


    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.qian_zh);
//        setTopRightIcon(R.drawable.icon_share_gift);
//        setTopRightOnClick(v -> {
//            if (!PermissionUtil.hasStoragePermission(QianActivity.this)) {
//                return;
//            }
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ASKFORRSIGNATURE_SHARE);
//            if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_YESORNO_CLICK);
//            } else {
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_YESORNO_CLICK);
//            }
//            startActivity(new Intent(QianActivity.this, QianShareActivity.class).putExtra(QianDetailActivity.QIAN_TYPE_TAG, qianType).putExtra(CommonArgs.FROM_TYPE, getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1)));
//        });
        setShareStatus();
    }

    private void setShareStatus() {
        if (canGetQian(qianType)) {
            getTopRightIcon().setVisibility(View.GONE);
        } else {
            getTopRightIcon().setVisibility(View.VISIBLE);
        }
    }


    /**
     * 初始化Cursor 移动动画效果
     */
    private void initCursonanimer() {
        // 重新定位下动画图标
        if (mQianDateCursor.getLayoutParams() != null) {
            mQianDateCursor.getLayoutParams().width = Apputils
                    .getScreenWidth(QianActivity.this) / 3;
        }

        mQianContentViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {

                qianType = position;

                // 更新顶部栏的状态
                updateDateBar(position);
                setShareStatus();
            }

            @Override
            public void onPageScrolled(int position, float positionOffset,
                                       int positionOffsetPixels) {
                float offset = (Apputils.getScreenWidth(QianActivity.this) * position + positionOffsetPixels) / 3f;
                mQianDateCursor.setTranslationX(offset);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

    }

    /**
     * 更新顶部日期栏的状态
     *
     * @param position
     */

    protected void updateDateBar(int position) {
        mQianTypeLove.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));
        mQianTypeWealth.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));
        mQianDateCareer.setTextColor(getResources().getColor(R.color.constellation_tab_text_color));
        mQianTypeLove.getPaint().setFakeBoldText(false);
        mQianTypeWealth.getPaint().setFakeBoldText(false);
        mQianDateCareer.getPaint().setFakeBoldText(false);

        switch (position) {
            case 0:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ASKFORRSIGNATURE_MARRIAGE);
                mQianTypeLove.setTextColor(getResources().getColor(R.color.text_color_black));
                mQianTypeLove.getPaint().setFakeBoldText(true);
                break;
            case 1:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ASKFORRSIGNATURE_WEALTH);
                mQianTypeWealth.setTextColor(getResources().getColor(R.color.text_color_black));
                mQianTypeWealth.getPaint().setFakeBoldText(true);
                break;
            case 2:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ASKFORRSIGNATURE_CAUSE);
                mQianDateCareer.setTextColor(getResources().getColor(R.color.text_color_black));
                mQianDateCareer.getPaint().setFakeBoldText(true);
                break;
        }
    }


    private void setBold(TextView textView) {
        mQianTypeLove.getPaint().setFakeBoldText(false);
        mQianTypeWealth.getPaint().setFakeBoldText(false);
        mQianDateCareer.getPaint().setFakeBoldText(false);
    }

    private void initData() {
        RequestParams params = HttpHelper.getBaseParams(true);
        //星点评
        HttpRequest.get(
                HttpHelper.URL_CONSTELLATION_COMMENT, params,
                new RequestCallBack<String>() {
                    @Override
                    public void onStart() {
                        JSONObject data = HttpHelper
                                .getDataCache(HttpHelper.URL_CONSTELLATION_COMMENT);
                        if (data != null) {
                            Gson gson = new Gson();
                            comment = gson.fromJson(data.toString(),
                                    ConstellationComment.class);
                        }
                        super.onStart();
                    }

                    @Override
                    public void onLogicSuccess(String s) {
                        System.out.println("qian:" + s);
                        JSONObject data = HttpHelper
                                .parserStringResponse(s);
                        if (data != null) {
                            Gson gson = new Gson();
                            comment = gson.fromJson(data.toString(),
                                    ConstellationComment.class);
                            // 保存缓存数据
                            HttpHelper.saveDataCache(
                                    HttpHelper.URL_CONSTELLATION_COMMENT,
                                    s);

                            mAdapter.notifyDataSetChanged();
                        }
                    }
                });
    }


    @Override
    protected void onResume() {
        super.onResume();
        checkAndSetQianState();
        BluetoothManage.getInstance().stayIn(true);
        BluetoothManage.getInstance().setConnectSuccessListener(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        BluetoothManage.getInstance().stayIn(false);
        BluetoothManage.getInstance().setConnectSuccessListener(null);
        if (shakeMonitor != null) {
            shakeMonitor.stop();
        }
    }

    private void shakeOrTouch(int type) {
        if (canGetQian(qianType)) {
            playSound();
            startActivityForResult(new Intent(QianActivity.this, QianDetailActivity.class).putExtra(QianDetailActivity.QIAN_TYPE_TAG, qianType), 2);
        } else {
            ToastUtils.showLong(QianActivity.this, R.string.has_qian_0);
        }
    }

    /**
     * 检查并设置抽签的状态
     */
    private void checkAndSetQianState() {
        BluetoothManage.getInstance().connectedStatus();

        if (shakeMonitor == null) {
            shakeMonitor = new ShakeMonitor(this);
            shakeMonitor.setOnEventListener(this::shakeOrTouch);
        }
        shakeMonitor.start();
    }

    /**
     * 当前求签类型是否还可以抽签今天. 每天, 每种可抽签一次每天
     *
     * @return
     */
    private boolean canGetQian(int type) {
        return PreferencesUtils.getLong(this, QianDetailActivity.LAST_QIAN_TAG_PREFIX + type, 0)
                != Apputils.getZeroCalendar(null).getTimeInMillis();
    }

    private Qian getLastQian(int type) {
        return (Qian) ACache.get(QianActivity.this).getAsObject(CommonArgs.CACHE_QIAN + type);
    }

    /**
     * 播放声音
     */
    private void playSound() {
        soundPool.setOnLoadCompleteListener((soundPool, sampleId, status) -> soundPool.play(sampleId, 1, 1, 0, 0, 1));
        soundPool.load(this, R.raw.qian_sound, 1);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (soundPool != null) {
            soundPool.release();
        }
    }

    long tt = 0;
    int count = 0;

    @OnClick({R.id.qian_type_love, R.id.qian_type_wealth, R.id.qian_date_career})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.qian_type_love:
                mQianContentViewpager.setCurrentItem(0, false);
                break;
            case R.id.qian_type_wealth:
                mQianContentViewpager.setCurrentItem(1, false);
                break;
            case R.id.qian_date_career:
                mQianContentViewpager.setCurrentItem(2, false);

                // 连续 5 次点击事业, 清除限制, 测试专用
//                if ((System.currentTimeMillis() - tt) < 800) {
//                    if (count++ == 5) {
//                        PreferencesUtils.put(QianActivity.this, QianDetailActivity.LAST_QIAN_TAG_PREFIX + 0, 0L);
//                        PreferencesUtils.put(QianActivity.this, QianDetailActivity.LAST_QIAN_TAG_PREFIX + 1, 0L);
//                        PreferencesUtils.put(QianActivity.this, QianDetailActivity.LAST_QIAN_TAG_PREFIX + 2, 0L);
//                        mAdapter.notifyDataSetChanged();
//                    }
//                } else {
//                    count = 0;
//                }
                tt = System.currentTimeMillis();

                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == 2) {
            mAdapter.notifyDataSetChanged();
        }
        setShareStatus();
    }

    @Override
    public void onConnectSuccessd() {
        LogUtils.e("aab onConnectSuccessd");
        mHandler.postDelayed(() -> BluetoothManage.getInstance().stayIn(true), 2000);

    }

    class QianPageAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return 3;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View rootView;

            if (canGetQian(position)) {
                rootView = View.inflate(QianActivity.this, R.layout.constellation_qian_page_layout, null);
                showQianInfo(rootView, position);
            } else {
                if (getLastQian(position) == null) {
                    PreferencesUtils.remove(QianActivity.this, QianDetailActivity.LAST_QIAN_TAG_PREFIX + position);
                    rootView = View.inflate(QianActivity.this, R.layout.constellation_qian_page_layout, null);
                    showQianInfo(rootView, position);
                } else {
                    rootView = new QianDetailPage(QianActivity.this, getLastQian(position), false);
                }
            }

            rootView.setTag(position);
            container.addView(rootView);
            return rootView;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            for (int i = 0; i < container.getChildCount(); i++) {
                Object tag = container.getChildAt(i).getTag();
                if (tag != null && tag instanceof Integer && ((Integer) tag == position)) {
                    container.removeViewAt(i);
                }
            }
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }
    }

    /**
     * 展示抽签界面, 大师的数据信息
     *
     * @param view
     */
    private void showQianInfo(View view, int position) {
        // 根据抽签次数, 显示说明文案
        TextView infoTv = (TextView) view.findViewById(R.id.qian_info_tv);
        String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");

        JewelryOnlineDataBean jewInfo = JewelryOnlineDataManager.getInstance().getConnectedJewInfo();

        switch (position) {
            case 0:
                if (!TextUtils.isEmpty(jewInfo.getQian_marriage_tip())) {
                    infoTv.setText(jewInfo.getQian_marriage_tip());
                } else if (BleParams.isMWJewlery(jewName) || BleParams.isCtJewlery()) {
                    infoTv.setText(R.string.has_touch_qian_1);
                } else if (BleParams.isSM2(jewName) && !TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_80)) {
                    infoTv.setText(R.string.has_touch_no_vibrate_qian_1_sm);
                } else if (BleParams.isTouchJewelry(jewName)) {
                    infoTv.setText(R.string.has_touch_no_vibrate_qian_1);
                } else {
                    infoTv.setText(R.string.has_qian_1);
                }
                break;
            case 1:
                if (!TextUtils.isEmpty(jewInfo.getQian_wealth_tip())) {
                    infoTv.setText(jewInfo.getQian_wealth_tip());
                } else if (BleParams.isMWJewlery(jewName) || BleParams.isCtJewlery()) {
                    infoTv.setText(R.string.has_touch_qian_2);
                } else if (BleParams.isSM2(jewName) && !TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_80)) {
                    infoTv.setText(R.string.has_touch_no_vibrate_qian_2_sm);
                } else if (BleParams.isTouchJewelry()) {
                    infoTv.setText(R.string.has_touch_no_vibrate_qian_2);
                } else {
                    infoTv.setText(R.string.has_qian_2);
                }
                break;
            case 2:
                if (!TextUtils.isEmpty(jewInfo.getQian_cause_tip())) {
                    infoTv.setText(jewInfo.getQian_cause_tip());
                } else if (BleParams.isMWJewlery()|| BleParams.isCtJewlery()) {
                    infoTv.setText(R.string.has_touch_qian_3);
                } else if (BleParams.isSM2(jewName) && !TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_80)) {
                    infoTv.setText(R.string.has_touch_no_vibrate_qian_3_sm);
                } else if (BleParams.isTouchJewelry()) {
                    infoTv.setText(R.string.has_touch_no_vibrate_qian_3);
                } else {
                    infoTv.setText(R.string.has_qian_3);
                }
                break;
        }

        if (comment == null) {
            return;
        }

        BitmapHelper.display(this, (ImageView) view.findViewById(R.id.qian_pic_iv), comment.getLingqian_img());
        ((TextView) view.findViewById(R.id.qian_desc_tv)).setText(comment.getLingqian_intro());
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
