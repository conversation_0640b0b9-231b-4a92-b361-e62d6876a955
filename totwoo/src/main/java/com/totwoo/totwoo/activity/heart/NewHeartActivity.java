//package com.totwoo.totwoo.activity.heart;
//
//import android.animation.ObjectAnimator;
//import android.app.Activity;
//import android.content.Context;
//import android.content.Intent;
//import android.graphics.Bitmap;
//import android.graphics.BitmapFactory;
//import android.graphics.Color;
//import android.graphics.Matrix;
//import android.graphics.Point;
//import android.graphics.Rect;
//import android.graphics.Typeface;
//import android.net.Uri;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.HandlerThread;
//import android.os.Looper;
//import android.os.Message;
//import android.provider.MediaStore;
//import android.provider.Settings;
//import android.support.v7.widget.LinearLayoutManager;
//import android.text.SpannableString;
//import android.text.Spanned;
//import android.text.TextUtils;
//import android.text.style.AbsoluteSizeSpan;
//import android.text.style.ForegroundColorSpan;
//import android.util.Log;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.ListAdapter;
//import android.widget.ListView;
//import android.widget.ScrollView;
//import android.widget.TextView;
//import android.widget.Toast;
//
//import com.bumptech.glide.Glide;
//import com.etone.framework.annotation.EventInject;
//import com.etone.framework.annotation.InjectUtils;
//import com.etone.framework.annotation.LayoutInject;
//import com.etone.framework.annotation.ViewInject;
//import com.etone.framework.annotation.event.OnClick;
//import com.etone.framework.base.InstanceHolder;
//import com.etone.framework.component.bitmap.BitmapUtils;
//import com.etone.framework.event.EventBus;
//import com.etone.framework.event.EventData;
//import com.etone.framework.event.SubscriberListener;
//import com.etone.framework.event.TaskType;
//import com.totwoo.library.bitmap.BitmapHelper;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.S;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.activity.BaseActivity;
//import com.totwoo.totwoo.activity.HistoryActivity;
//import com.totwoo.totwoo.activity.HomeActivity;
//import com.totwoo.totwoo.activity.MeSettingActivity;
//import com.totwoo.totwoo.activity.TheHeartActivity;
//import com.totwoo.totwoo.activity.TheHeartManageActivity;
//import com.totwoo.totwoo.bean.Jewelry;
//import com.totwoo.totwoo.bean.NotifyDataModel;
//import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
//import com.totwoo.totwoo.ble.JewController;
//import com.totwoo.totwoo.controller.HttpValues;
//import com.totwoo.totwoo.data.CoupleLogic;
//import com.totwoo.totwoo.data.TotwooLogic;
//import com.totwoo.totwoo.newConrtoller.MemoryController;
//import com.totwoo.totwoo.newConrtoller.UpdatePictureController;
//import com.totwoo.totwoo.utils.ACache;
//import com.totwoo.totwoo.utils.CommonArgs;
//import com.totwoo.totwoo.utils.CommonUtils;
//import com.totwoo.totwoo.utils.FileUtils;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.utils.ViewUtil;
//import com.totwoo.totwoo.widget.CustomDialog;
//import com.totwoo.totwoo.widget.CustomTypefaceSpan;
//import com.totwoo.totwoo.widget.NewScrollView;
//import com.totwoo.totwoo.widget.PullZoomRecyclerView;
//import com.totwoo.totwoo.widget.PullZoomScrollView;
//import com.totwoo.totwoo.widget.SendBQDialogController;
//
//import org.greenrobot.eventbus.Subscribe;
//import org.greenrobot.eventbus.ThreadMode;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.lang.ref.WeakReference;
//import java.util.ArrayList;
//import java.util.concurrent.atomic.AtomicBoolean;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//
///**
// * Created by xinyoulingxi on 2017/5/26.
// */
//@LayoutInject(R.layout.new_heart_activity)
//public class NewHeartActivity extends BaseActivity implements SubscriberListener, View.OnClickListener
//{
//    @ViewInject (R.id.new_heart_sv)
//    public NewScrollView scrollView;
//
//    @ViewInject (R.id.new_heart_lv)
//    public ListView listView;
//
//    @ViewInject (R.id.new_heart_content)
//    public View contentView;
//
//    @ViewInject (R.id.new_heart_send)
//    public View sendView;
//
//    public boolean canSee1 = false;
//    public AtomicBoolean isLoading;
//
//    public int topBarHeight;
//    public NewTotwooListAdapter adapter;
//    public NewHeartHeadView headView;
//
//    public static final String TOTWOO_IMAGE_JPG = "totwoo_image_custombg";
//    private final int SELECT_PHOTO = 0;
//    private final int SHOOTING_PHOTO = 1;
//    public final static int CROP_PHOTO = 2;
//    public static final String USER_HEAD_PORTRAIT = FileUtils.getImageDir() + File.separator + TOTWOO_IMAGE_JPG;
//    public CustomDialog dialog;
//    public Bitmap topBackgroundImg;
//
//    public NewHeartController controller = NewHeartController.getInstance();
//    public TotwooLogic totwooLogic;
//    public SendBQDialogController sendBQDialogController;
//
//    @ViewInject (R.id.new_heart_load_more)
//    public TextView loadMore;
//
//    private int defaultHeartCountPosition = 0;
//
//    private CustomTypefaceSpan tfspan;
//    private Typeface typefaceGithic;
//    private TextView new_heart_send_count_tv;
//    private TextView new_heart_receiver_count_tv;
//    private int sendCount;
//    private int receiverCount;
//
//    @Override
//    public void onCreate(Bundle savedInstanceState)
//    {
//        super.onCreate(savedInstanceState);
//        InstanceHolder.putInstance(this);
//        org.greenrobot.eventbus.EventBus.getDefault().register(this);
//        InjectUtils.injectActivity(this);
//
//        initHeadView();
//        initZoomViews();
//        initTopTitleBar();
//        initScrollView();
//        initLogicComponent();
//        isLoading = new AtomicBoolean(true);
//        sendView.setOnClickListener(this);
//        //NewHeartController.getInstance().getHeartHomeData();
//        NewHeartController.getInstance().getHeartHomeDataV3();
//        loadMore.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                Intent i = new Intent(NewHeartActivity.this, HistoryActivity.class);
//                NewHeartActivity.this.startActivity(i);
//            }
//        });
//        new_heart_send_count_tv = findViewById(R.id.new_heart_send_count_tv);
//        sendCount = getIntent().getIntExtra(CommonArgs.SEND,0);
//        new_heart_send_count_tv.setText(setTextSpan(R.string.totwoo_today_send_totwoo_num,sendCount));
//        new_heart_receiver_count_tv = findViewById(R.id.new_heart_receiver_count_tv);
//        receiverCount = getIntent().getIntExtra(CommonArgs.RECEIVER,0);
//        new_heart_receiver_count_tv.setText(setTextSpan(R.string.totwoo_today_received_totwoo_num,receiverCount));
//    }
//
//    private void initScrollView()
//    {
//        scrollView.setScrollViewListener(new NewScrollView.ScrollViewListener() {
//            @Override
//            public void onScrollViewChanged(NewScrollView sc, int x, int y, int oldx, int oldy)
//            {
//                View view = sc.getChildAt(sc.getChildCount()-1);
//                int d = (int)view.getY() + view.getHeight();
//
//                int scrollY = sc.getScrollY();
//                d -= (sc.getHeight()+scrollY);
//                if(d == 0)
//                {
//                    /*if (currPage > 0 && isLoading.compareAndSet(false, true))
//                        controller.getHeartPageDataV2(currPage, false, headView.hhb.lastMsgId);*/
//                }
////                int height = headView.headTopIv.getHeight() - getTopBarBackgroundObject().getMeasuredHeight() - topBarHeight;
////                View v = getTopBarBackgroundObject();
////                if (scrollY > height)
////                {
////                    setTopTitleColor(0xff000000);
////                    setTopBackIcon(R.drawable.back_icon_black);
////                    setTopRightIcon(R.drawable.the_heart_manage_icon);
////                    v.setAlpha(1);
////                }
////                else
////                {
////                    setTopTitleColor(0xffffffff);
////                    setTopBackIcon(R.drawable.back_icon_white);
////                    setTopRightIcon(R.drawable.the_heart_manage_icon_white);
////                    float alaph = 1 - (((float)height) - ((float)scrollY)) / ((float)height);
////                    v.setAlpha(alaph);
////                }
//
////                if (scrollY == 0)
////                    
////                else
////                    
//
//                boolean can = canSee(NewHeartActivity.this, headView.heartCountTv, listView);
//                if (can != canSee1)
//                {
//                    setAnim1(can);
//                    canSee1 = can;
//                }
//
//                if (defaultHeartCountPosition == 0)
//                {
//                    int[] position = new int[2];
//                    headView.line3.getLocationInWindow(position);
//                    //LogUtils.e("heartCountTv.position:" + position[0] + ", " + position[1]);
//                    defaultHeartCountPosition = position[1];
//                }
//            }
//        });
//    }
//
//    private void setAnim1(boolean canSee)
//    {
//        ObjectAnimator anim;
//        if (canSee)
//        {
//            sendView.setVisibility(View.VISIBLE);
//            anim = ObjectAnimator.ofFloat(sendView, "alpha", 0f, 1f);
//            sendView.setClickable(true);
//        }
//        else
//        {
//            anim = ObjectAnimator.ofFloat(sendView, "alpha", 1f, 0f);
//            //sendView.setVisibility(View.GONE);
//            sendView.setClickable(false);
//        }
//
//        anim.setDuration(500);
//        anim.start();
//    }
//
//    public static boolean canSee(Activity activity, View view1, View view2)
//    {
//        Point p = new Point();
//        activity.getWindowManager().getDefaultDisplay().getSize(p);
//        int screenWidth = p.x;
//        int screenHeight = p.y;
//        Rect rect = new Rect(0, 0, screenWidth, screenHeight);
//        int[] location = new int[2];
//        view1.getLocationInWindow(location);
//        if (view1.getLocalVisibleRect(rect) || view2.getLocalVisibleRect(rect))
//            return true;
//        else
//            return false;
//    }
//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onReceiveState(final TotwooMessage message) {
//        switch (message.getTotwooState()) {
//            case TheHeartActivity.ACTION_TOTWOO_DATA_CHANGEED://收到totwoo
//                receiverCount++;
//                new_heart_receiver_count_tv.setText(setTextSpan(R.string.totwoo_today_received_totwoo_num,receiverCount));
//                break;
//            case TotwooMessage.TOTWOO_SEND_SUCCESS://totwoo发送成功
//                sendCount++;
//                new_heart_send_count_tv.setText(setTextSpan(R.string.totwoo_today_send_totwoo_num,sendCount));
//                break;
//        }
//    }
//
//    private void setAnim(boolean canSee)
//    {
//        ObjectAnimator anim;
//        View v = getTopBarBackgroundObject();
//        if (canSee)
//        {
//            anim = ObjectAnimator.ofFloat(v, "alpha", 1f, 0f);
//            
//            setTopTitleColor(0xffffffff);
//            setTopBackIcon(R.drawable.back_icon_black);
//            setTopRightIcon(R.drawable.the_heart_manage_icon);
//        }
//        else
//        {
//            v.setVisibility(View.VISIBLE);
//            anim = ObjectAnimator.ofFloat(v, "alpha", 0f, 1f);
//            
//            setTopTitleColor(0xff000000);
//            setTopBackIcon(R.drawable.back_icon_black);
//            setTopRightIcon(R.drawable.the_heart_manage_icon);
//        }
//
//        anim.setDuration(500);
//        anim.start();
//    }
//
//    private void initLogicComponent()
//    {
//        totwooLogic = new TotwooLogic(this, new TotwooSendCallBack());
//        sendBQDialogController = new SendBQDialogController(this, totwooLogic);
//        adapter = new NewTotwooListAdapter(this, new ArrayList<HeartListBean>());
//        listView.setAdapter(adapter);
//        setListViewHeightBasedOnChild(listView);
//        adapter.notifyDataSetChanged();
//        LogUtils.e("adapter.count:" + adapter.getCount());
//        if (adapter.getCount() >= 10)
//            loadMore.setVisibility(View.VISIBLE);
//        else
//            loadMore.setVisibility(View.GONE);
//    }
//
//    private void initZoomViews()
//    {
//        //scrollView.setZooViews(headView.headTop1, headView.headTop, headView.headTopIv, headView.headTopMask);
//    }
//
//    private void initHeadView()
//    {
//        headView = new NewHeartHeadView(this, contentView);
//        headView.headTop.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                getPhotoDialog();
//                dialog.show();
//            }
//        });
//    }
//
//    private void initTopTitleBar()
//    {
////        View v = getTopBarBackgroundObject();
////        v.setVisibility(View.VISIBLE);
////        v.setAlpha(0);
//
////        
////        setTopTitleColor(0xffffffff);
////        setTopBackIcon(R.drawable.back_icon_white);
////        setTopRightIcon(R.drawable.the_heart_manage_icon_white);
//
//        setTopTitleColor(0xff000000);
//        setTopBackIcon(R.drawable.back_icon_black);
//        setTopRightIcon(R.drawable.the_heart_manage_icon);
//
////        Rect rectangle = new Rect();
////        getWindow().getDecorView().getWindowVisibleDisplayFrame(rectangle);
//        setTopTitle(R.string.the_heart);
//        setTopRightOnClick(new View.OnClickListener()
//        {
//            @Override
//            public void onClick(View v)
//            {
//                startActivity(new Intent(NewHeartActivity.this, TheHeartManageActivity.class));
//            }
//        });
////        topBarHeight = rectangle.top;
//    }
//
//    @EventInject(eventType = S.E.E_HEART_GET_HOME_DATA_SUCCESSED, runThread = TaskType.UI)
//    public void onGetHomeDataSuccessed(EventData data)
//    {
//        updateInfos(data, true);
//        if(getIntent().getBooleanExtra(CommonArgs.SHOULDSCROLL,false)){
//            mHandler.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    scrollView.smoothScrollBy(0,CommonUtils.dip2px(NewHeartActivity.this,600));
//                }
//            },500);
//        }
//        isLoading.set(false);
//    }
//
//    @EventInject (eventType = S.E.E_HEART_GET_HOME_DATA_FAILED, runThread = TaskType.UI)
//    public void onGetHomeDataFailed (EventData data)
//    {
//        isLoading.set(false);
//    }
//
//    @EventInject (eventType = S.E.E_HEART_GET_PAGE_DATA_SUCCESSED, runThread = TaskType.UI)
//    public void onGetPageDataSuccessed (EventData data)
//    {
//        HttpValues hv = (HttpValues) data;
//        if((boolean)hv.getUserDefine("isHistory"))
//            return;
//        updateInfos(data, false);
//        isLoading.set(false);
//    }
//
//    @EventInject (eventType = S.E.E_HEART_GET_PAGE_DATA_FAILED, runThread = TaskType.UI)
//    public void onGetPageDataFailed (EventData data)
//    {
//        isLoading.set(false);
//    }
//
//    //true:set, false:add
//    private void updateInfos(EventData data, boolean setOrAdd)
//    {
//        HttpValues hv = (HttpValues) data;
//        NewHeartHomeBean hhb = (NewHeartHomeBean) hv.getUserDefine("hhb");
//        ArrayList<HeartListBean> totwooList = (ArrayList<HeartListBean>) hv.getUserDefine("totwooList");
//        boolean isRefresh = (boolean) hv.getUserDefine("isRefresh");
//        int listCount = (int) hv.getUserDefine("listCount");
//        if (setOrAdd)
//        {
//            headView.setData(hhb);
//            adapter.setTotwooListData(totwooList, hhb.userinfos[1].nick_name, hhb.userinfos[1].head_portrait);
//        }
//        else
//        {
//            headView.setHeartCount(hhb);
//            if (isRefresh)
//            {
//                adapter.setTotwooListData(totwooList, hhb.userinfos[1].nick_name, hhb.userinfos[1].head_portrait);
//            }
//            else
//                //adapter.addTotwooListData(totwooList, hhb.userinfos[1].nick_name, hhb.userinfos[1].head_portrait);
//                adapter.setTotwooListData(totwooList, hhb.userinfos[1].nick_name, hhb.userinfos[1].head_portrait);
//        }
//
//        LogUtils.e("adapter.count:" + listCount);
//        if (listCount >= 10)
//            loadMore.setVisibility(View.VISIBLE);
//        else
//            loadMore.setVisibility(View.GONE);
//
////        headView.setHeartCount(hhb);
////        adapter.setTotwooListData(totwooList, hhb.userinfos[1].nick_name, hhb.userinfos[1].head_portrait);
//
//        headView.hhb.lastMsgId = hhb.lastMsgId;
//        adapter.notifyDataSetChanged();
//        setListViewHeightBasedOnChild(listView);
//        /*if (listCount < 20)
//            currPage = -1;
//        else
//            currPage++;*/
//
//        //只有刷新当时候，才会回滚到聊天顶部
//        if (isRefresh)
//        {
//            scrollView.post(new Runnable()
//            {
//                @Override
//                public void run()
//                {
//                    int top = headView.heartCountTv.getTop();
//                    LogUtils.e("top:" + top);
//                    scrollView.smoothScrollTo(0, defaultHeartCountPosition);
//                }
//            });
//        }
//    }
//
//    @EventInject (eventType = S.E.E_RECEIVED_TOTWOO_MESSAGE, runThread = TaskType.Async)
//    public void onReceivedTotwooMessage(EventData data)
//    {
//        //当收到消息之后
//        refreshNewestData();
//    }
//
//    @Override
//    public void onClick(View v) {
//        sendBQDialogController.showBQDialog(false);
//    }
//
//    private class TotwooSendCallBack implements TotwooLogic.TotwooSendCallBack
//    {
//        @Override
//        public void onSuccess()
//        {
//            // 发送成功的逻辑, 统一按照消息处理
////            refreshNewestData();
//        }
//
//        @Override
//        public void onFailed(String error_msg)
//        {
//            //ToastUtils.show(NewHeartActivity.this, R.string.send_failed_, Toast.LENGTH_SHORT);
//            ToastUtils.showShort(NewHeartActivity.this, error_msg);
//        }
//    }
//
//    private void refreshNewestData()
//    {
//        if (adapter != null)
//        {
//            /*currPage = 0;
//            if (isLoading.compareAndSet(false, true))
//                */
//            controller.getHeartPageDataV2(0, true, headView.hhb.lastMsgId, false);
//        }
//    }
//
//    @Override
//    public void onEventException(String eventType, EventData data, Throwable e)
//    {
//        e.printStackTrace();
//    }
//
//    @Override
//    public void onDestroy()
//    {
//        org.greenrobot.eventbus.EventBus.getDefault().unregister(this);
//        EventBus.unregisterListenerAll(this);
//        InstanceHolder.deleteInstance(this.getClass());
//        InjectUtils.injectUnregisterListenerAll(this);
//        super.onDestroy();
//    }
//
//    public static int setListViewHeightBasedOnChild(ListView listView)
//    {
//        if (listView == null)
//            return 0;
//
//        ListAdapter adapter = listView.getAdapter();
//        if (adapter == null)
//            return 0;
//
//        int totalHeight = 0;
//        for (int i=0; i<adapter.getCount(); i++)
//        {
//            View listItem = adapter.getView(i, null, listView);
//            listItem.measure(0, 0);
//            totalHeight += listItem.getMeasuredHeight();
//        }
//
//        ViewGroup.LayoutParams params = listView.getLayoutParams();
//        params.height = totalHeight + (listView.getDividerHeight() * (adapter.getCount() - 1));
//        listView.setLayoutParams(params);
//        return totalHeight;
//    }
//
//    public void getPhotoDialog()
//    {
//        dialog = new CustomDialog(this);
//        dialog.setTitle(R.string.modify_background);
//        LinearLayout modify_head_dialog_ll = new LinearLayout(this);
//        modify_head_dialog_ll
//                .setLayoutParams(new LinearLayout.LayoutParams(
//                        LinearLayout.LayoutParams.MATCH_PARENT,
//                        LinearLayout.LayoutParams.WRAP_CONTENT));
//        modify_head_dialog_ll.setOrientation(LinearLayout.VERTICAL);
//        TextView album_tv = new TextView(this);
//        TextView camera_tv = new TextView(this);
//        TextView default_tv = new TextView(this);
//        modify_head_dialog_ll.addView(album_tv);
//        modify_head_dialog_ll.addView(camera_tv);
//        modify_head_dialog_ll.addView(default_tv);
//
//        dialog.setMainLayoutView(modify_head_dialog_ll);
//        album_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
//        camera_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
//        default_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
//        album_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20),Apputils.dp2px(this, 15));
//        camera_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20), Apputils.dp2px(this, 15));
//        default_tv.setPadding(Apputils.dp2px(this, 20), Apputils.dp2px(this, 15), Apputils.dp2px(this, 20),Apputils.dp2px(this, 15));
//        album_tv.setBackgroundResource(R.drawable.item_bg);
//        camera_tv.setBackgroundResource(R.drawable.item_bg);
//        default_tv.setBackgroundResource(R.drawable.item_bg);
//        album_tv.setText(R.string.album_select_usericon);
//        camera_tv.setText(R.string.use_camera);
//        default_tv.setText(R.string.restore_default);
//        album_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
//        camera_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
//        default_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
//        album_tv.setTextSize(16);
//        camera_tv.setTextSize(16);
//        default_tv.setTextSize(16);
//        dialog.setNegativeButtonText(R.string.cancel);
//        // 相册tv监听点击开启选择图片app
//        album_tv.setOnClickListener(new View.OnClickListener() {
//
//            @Override
//            public void onClick(View v) {
//
//                // Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
//                // intent.addCategory(Intent.CATEGORY_OPENABLE);
//                // intent.setType("image/*");
//                Intent intent = new Intent(Intent.ACTION_PICK, null);
//                intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
//                startActivityForResult(intent, SELECT_PHOTO);
//            }
//        });
//        // 相机tv监听点击开启拍照app
//        camera_tv.setOnClickListener(new View.OnClickListener() {
//
//            @Override
//            public void onClick(View v)
//            {
//                try
//                {
//                    Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
//                    Uri imageUri = Uri.fromFile(new File(USER_HEAD_PORTRAIT));
//                    // 指定照片保存路径（SD卡），USER_HEAD_PORTRAIT为一个临时文件，每次拍照后这个图片都会被替换
//                    intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
//                    startActivityForResult(intent, SHOOTING_PHOTO);
//                }
//                catch (Exception e)
//                {
//                    e.printStackTrace();
//                    final CustomDialog dialog = new CustomDialog(NewHeartActivity.this);
//                    dialog.setMessage(R.string.open_camera_error1);
//                    dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
//                        @Override
//                        public void onClick(View v) {
//                            startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
//                            dialog.dismiss();
//                        }
//                    });
//                    dialog.show();
//                }
//            }
//        });
//        default_tv.setOnClickListener(new View.OnClickListener()
//        {
//            @Override
//            public void onClick(View v)
//            {
//                if (headView != null && headView.hhb != null && headView.hhb.background != null && !headView.hhb.background.equals("totwoohome3.jpg"))
//                {
//                    UpdatePictureController.getInstance().updateBackgroundPic("", 1);
//                    headView.headTopIv.setImageResource(R.drawable.the_heart_banner);
//                    //BitmapHelper.display(NewHeartActivity.this, headView.headTopIv, R.drawable.the_heart_banner);
//                }
//                try
//                {
//                    Glide.with(NewHeartActivity.this).load(R.drawable.the_heart_banner);
//                }
//                catch (Exception e)
//                {
//                    e.printStackTrace();
//                }
//                LogUtils.e("testaaaaaaaaaaaaaaaaaa");
//                dialog.dismiss();
//            }
//        });
//    }
//
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//
//        switch (requestCode) {
//            case SHOOTING_PHOTO:// 相机拍摄
//                if (resultCode == -1){
//
//                    CommonUtils.startPhotoZoom(CommonUtils.getUriForFile(this,new File(USER_HEAD_PORTRAIT)),
//                            this, CROP_PHOTO, USER_HEAD_PORTRAIT, 36, 25);
//                }
//                break;
//            case SELECT_PHOTO:// 相册选取
//                Uri uri = null;
//                if (data != null) {
//                    uri = data.getData();
//                }
//
//                if (uri != null) {
//                    CommonUtils.startPhotoZoom(uri, this, CROP_PHOTO, USER_HEAD_PORTRAIT, 36, 25);
//                }
//                break;
//            case CROP_PHOTO://
//
//                if (resultCode == -1){
//                    if (dialog != null) {
//                        dialog.dismiss();
//                    }
//
//                    handlePhoto(USER_HEAD_PORTRAIT);
//                }
//                break;
//        }
//    }
//
//    // 处理图片
//    private void handlePhoto(String path)
//    {
////        if (topBackgroundImg != null) {// 如果不释放的话，不断取图片，将会内存不够
////            topBackgroundImg.recycle();
////        }
//
//        if (!new File(path).exists())
//        {
//            headView.headTopIv.setImageResource(R.drawable.the_heart_banner);
//            return;
//        }
//
//        //bu.display(topBackground, path);
//        // 为了防止Oom先加载个缩略图
//        BitmapFactory.Options options = new BitmapFactory.Options();
//        options.inJustDecodeBounds = false;
//        options.inSampleSize = 3;
//
//        topBackgroundImg = BitmapFactory.decodeFile(path, options);
//
//        //如果图片尺寸小，就放大
//        if (topBackgroundImg.getWidth() < Apputils.getScreenWidth(this))
//        {
//            Bitmap newBitmap = resizeImage(this, topBackgroundImg);
//            if (topBackgroundImg != null) {// 如果不释放的话，不断取图片，将会内存不够
//                topBackgroundImg.recycle();
//            }
//            if (newBitmap == null)
//            {
//                return;
//            }
//            topBackgroundImg = newBitmap;
//        }
//        mHandler.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                headView.headTopIv.setImageBitmap(topBackgroundImg);
//            }
//        },500);
//        File f = new File(USER_HEAD_PORTRAIT);
//        try
//        {
//            if (f.exists())
//                f.delete();
//            f.createNewFile();
//            topBackgroundImg.compress(Bitmap.CompressFormat.JPEG, 100, new FileOutputStream(new File(USER_HEAD_PORTRAIT)));
//        }
//        catch (Exception e)
//        {
//            e.printStackTrace();
//            f.deleteOnExit();
//        }
//
//        UpdatePictureController.getInstance().uploadPictures(this, USER_HEAD_PORTRAIT);
//    }
//
//    /*将图片放大到屏幕宽度尺寸到比例*/
//    public static Bitmap resizeImage(Context context, Bitmap bitmap)
//    {
//        Bitmap resizedBitmap = null;
//        try
//        {
//            Bitmap BitmapOrg = bitmap;
//            int width = BitmapOrg.getWidth();
//            int height = BitmapOrg.getHeight();
//            int newWidth = Apputils.getScreenWidth(context);
//
//            float scale = ((float) newWidth) / width;
//
//            Matrix matrix = new Matrix();
//            matrix.postScale(scale, scale);
//            // if you want to rotate the Bitmap
//            // matrix.postRotate(45);
//            resizedBitmap = Bitmap.createBitmap(BitmapOrg, 0, 0, width, height, matrix, true);
//        }
//        catch (Exception e)
//        {
//            e.printStackTrace();
//        }
//
//        return resizedBitmap;
//    }
//
//    @EventInject(eventType = S.E.E_BACKGROUND_UPDATED_SUCCESSED, runThread = TaskType.UI)
//    public void onUpdateBackgroundSuccessed(EventData data)
//    {
//        HttpValues hv = (HttpValues) data;
//        String backgroundUrl = (String) hv.getUserDefine("updateUrl");
//
//        //只有当当前图片URL和上传成功后当URL不一致当时候，才进行图片当加载
//        /*if (!headView.hhb.background.equals(backgroundUrl))
//        {
//            BitmapHelper.display(this, headView.headTopIv, backgroundUrl);
//        }*/
//
//        headView.hhb.background = backgroundUrl;
//    }
//
//    @EventInject(eventType = S.E.E_BACKGROUND_UPDATED_FAILED, runThread = TaskType.UI)
//    public void onUpdateBackgroundFailed(EventData data)
//    {
//
//    }
//
//    @EventInject (eventType = S.E.E_HISTORY_DELETE, runThread = TaskType.UI)
//    public void onHistoryDeleted(EventData data)
//    {
//        refreshNewestData();
//        loadMore.setVisibility(View.GONE);
//    }
//
//    private CharSequence setTextSpan(int resourceId, int number){
//        if (typefaceGithic == null) {
//            typefaceGithic = ResourcesCompat.getFont(NewHeartActivity.this, R.font.gothicb);
//        }
//
//        if (tfspan == null) {
//            tfspan = new CustomTypefaceSpan(typefaceGithic);
//        }
//
//        SpannableString spannableString = new SpannableString(NewHeartActivity.this.getString(resourceId, number + ""));
//        int index = spannableString.toString().indexOf(number+"");
//        spannableString.setSpan(new AbsoluteSizeSpan(20, true), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(tfspan, index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#c7ad92")), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//
//        return spannableString;
//    }
//}
