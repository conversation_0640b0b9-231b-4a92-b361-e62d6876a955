package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.hjq.shape.view.ShapeTextView;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.adapter.ColorLibraryAdapter;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.MusicBrightBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.service.BrightMusicPlayService;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomMiddleTextDialog;
import com.totwoo.totwoo.widget.CustomTextview;
import com.totwoo.totwoo.widget.FullyLinearLayoutManager;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class NotifyTotwooActivity extends BaseActivity {
    public static final String TOTWOO_MUSIC_TYPE = "TOTWOO_MUSIC_TYPE";

    @BindView(R.id.notify_setting_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    @BindView(R.id.make_card_sample_subtitle)
    TextView mMakeCardSampleSubtitle;
    @BindView(R.id.call_switch_title_tv)
    TextView mCallSwitchTitleTv;
    @BindView(R.id.call_switch_cb)
    CheckBox mCallSwitchCb;
    @BindView(R.id.notify_bright_type_layout)
    LinearLayout mBrightTypeLayout;
    @BindView(R.id.notify_vibration_layout)
    ViewGroup mNotifyVibrationLayout;
    @BindView(R.id.app_notify_tv)
    TextView bqTv;
    @BindView(R.id.app_notify_bq)
    LinearLayout bqView;

    @BindView(R.id.app_notify_header_icon_bq1)
    ImageView bqIcon1;
    @BindView(R.id.app_notify_header_icon_bq2)
    ImageView bqIcon2;
    @BindView(R.id.app_notify_header_icon_bq3)
    ImageView bqIcon3;
    @BindView(R.id.app_notify_header_icon_bq4)
    ImageView bqIcon4;

    @BindView(R.id.notify_setting_content)
    LinearLayout mNotifySettingContent;
    @BindView(R.id.long_vibration_tv)
    ShapeTextView mLongVibrationTv;
    @BindView(R.id.short_vibration_tv)
    ShapeTextView mShortVibrationTv;

    @BindView(R.id.short_vibration_iv)
    View mShortVibrationIv;

    @BindView(R.id.long_vibration_iv)
    View mLongVibrationIv;


    @BindView(R.id.long_bright_vibration_tv)
    CustomTextview mBrightLongVibrationTv;
    @BindView(R.id.short_bright_vibration_tv)
    CustomTextview mBrightShortVibrationTv;
    @BindView(R.id.notify_bright_vibration_layout)
    LinearLayout mBrightVibrationLayout;
    @BindView(R.id.notify_bright_music_breath_tv)
    TextView mBrightBreathTv;
    @BindView(R.id.notify_bright_music_breath_iv)
    ImageView mBrightBreathIv;
    @BindView(R.id.notify_bright_music_rv)
    RecyclerView mBrightMusicRv;
    @BindView(R.id.notify_totwoo_message_type)
    LinearLayout mChangeLayout;
    @BindView(R.id.notify_miss_you_tv)
    TextView mMissYouTv;
    @BindView(R.id.notify_love_you_tv)
    TextView mLoveYouTv;

    @BindView(R.id.notify_bright_music_breath_layout)
    ConstraintLayout mBreathLayout;
    @BindView(R.id.notify_bright_music_breath_line)
    View mBreathLine;

    private ColorLibraryAdapter colorLibraryAdapter;
    private BrightMusicAdapter brightMusicAdapter;

    private CustomMiddleTextDialog customMiddleTextDialog;
    private JewelryNotifyModel nowSetModel;

    private ArrayList<MusicBrightBean> musicBrightBeans;
    private boolean isMissYou = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notify_setting);
        ButterKnife.bind(this);

        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.notify_remind_totwoo_title);
        startService(new Intent(this, BrightMusicPlayService.class));

        mChangeLayout.setVisibility(View.VISIBLE);
        mBrightTypeLayout.setVisibility(View.GONE);
        bqTv.setVisibility(View.VISIBLE);
        bqView.setVisibility(View.VISIBLE);

        // TWO82 需要你、伤心、对不起三种表情的交替光，把每种表情右侧对应的颜色图片更换一下即可，其他不用做调整
        if (BleParams.needRemovePYWOColor()) {
            bqIcon1.setImageResource(R.drawable.expression_sad_pywo);
            bqIcon2.setImageResource(R.drawable.expression_need_you_pywo);
            bqIcon4.setImageResource(R.drawable.expression_sorry_pywo);
        }

        mBreathLayout.setVisibility(View.VISIBLE);
        mBreathLine.setVisibility(View.VISIBLE);

        if (BleParams.isCodeJewelry()) {
            setTopRightIcon(R.drawable.help_icon);
            setTopRightOnClick(v -> customMiddleTextDialog.show());
            customMiddleTextDialog = new CustomMiddleTextDialog(this);
            customMiddleTextDialog.setTitleTv(getString(R.string.morse_code_title), true);
            customMiddleTextDialog.setInfoText(getString(R.string.morse_code_content));
            customMiddleTextDialog.setConfirmTv(getString(R.string.i_know), v -> customMiddleTextDialog.dismiss());
            mMakeCardSampleSubtitle.setText(setStyle());
            mMakeCardSampleSubtitle.setOnClickListener(v -> customMiddleTextDialog.show());
            mNotifyVibrationLayout.setVisibility(View.GONE);
        } else {
            if (BleParams.isButtonBatteryJewelry()) {
                mMakeCardSampleSubtitle.setText(R.string.notify_remind_totwoo_set_dialog_title_no_vibration);
            } else {
                mMakeCardSampleSubtitle.setText(R.string.notify_remind_totwoo_set_dialog_title);
            }
            mNotifyVibrationLayout.setVisibility(View.VISIBLE);
        }

        if (BleParams.isButtonBatteryJewelry()) {
            mNotifyVibrationLayout.setVisibility(View.GONE);
        }

        musicBrightBeans = new ArrayList<>();
        musicBrightBeans.add(new MusicBrightBean(getString(R.string.music_mode), 8, false));
        mBrightMusicRv.setLayoutManager(new FullyLinearLayoutManager(NotifyTotwooActivity.this));
        brightMusicAdapter = new BrightMusicAdapter();
        mBrightMusicRv.setAdapter(brightMusicAdapter);

        selectMusicType(PreferencesUtils.getInt(NotifyTotwooActivity.this, TOTWOO_MUSIC_TYPE, 1));

        nowSetModel = NotifyUtil.getTotwooNotifyModel(this);

        if (nowSetModel == null) {
            return;
        }

        mCallSwitchCb.setChecked(nowSetModel.isNotifySwitch());
        mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on_setting : R.string.notify_off);

        if (!nowSetModel.isNotifySwitch()) {
            mNotifySettingContent.setVisibility(View.GONE);
        }

        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                setTextColorBtn(true);
                break;
            case SHORT_VIBRATION_SEC:
                setTextColorBtn(false);
                break;
        }

        colorLibraryRecyclerView.setLayoutManager(new LinearLayoutManager(NotifyTotwooActivity.this, LinearLayoutManager.HORIZONTAL, false));
        colorLibraryAdapter = new ColorLibraryAdapter(nowSetModel.getFlashColor(), NotifyTotwooActivity.this, v -> {
            if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                return;
            }
            if (isMissYou) {
                nowSetModel.setFlashColor((String) v.getTag());
            } else {
                setLoveYouVibrationFlashColor((String) v.getTag());
            }
            colorLibraryAdapter.setSelectColor((String) v.getTag());
            saveNowModel(false);
        });
        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
//        colorLibraryRecyclerView.scrollToPosition(colorLibraryAdapter.getIndex(nowSetModel.getFlashColor()));
        changeMessageType();
    }

    @OnClick({R.id.long_bright_vibration_tv, R.id.short_bright_vibration_tv, R.id.notify_switch_click_item,
            R.id.notify_bright_music_breath_layout, R.id.app_notify_header_bq1, R.id.app_notify_header_bq2,
            R.id.app_notify_header_bq4, R.id.long_vibration_tv, R.id.short_vibration_tv, R.id.notify_miss_you_tv,
            R.id.notify_love_you_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.long_bright_vibration_tv:
            case R.id.long_vibration_tv:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                if (isMissYou) {
                    nowSetModel.setVibrationSeconds(LONG_VIBRATION_SEC);
                } else {
                    setLoveYouVibrationSeconds(LONG_VIBRATION_SEC);
                }
                saveNowModel(false);
                setTextColorBtn(true);
                break;
            case R.id.short_bright_vibration_tv:
            case R.id.short_vibration_tv:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                if (isMissYou) {
                    nowSetModel.setVibrationSeconds(SHORT_VIBRATION_SEC);
                } else {
                    setLoveYouVibrationSeconds(SHORT_VIBRATION_SEC);
                }
                saveNowModel(false);
                setTextColorBtn(false);
                break;
            case R.id.notify_switch_click_item:
                mCallSwitchCb.setChecked(!mCallSwitchCb.isChecked());
                nowSetModel.setNotifySwitch(mCallSwitchCb.isChecked());
                saveNowModel(true);
                break;
            case R.id.notify_bright_music_breath_layout:
                selectMusicType(0);
                PreferencesUtils.put(NotifyTotwooActivity.this, TOTWOO_MUSIC_TYPE, 0);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
                BluetoothManage.getInstance().changeBirghtMode(-1, false);
                saveNowModel(false);
                break;
            case R.id.app_notify_header_bq1:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                BluetoothManage.getInstance().receiveFace(6);
                break;
            case R.id.app_notify_header_bq2:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                BluetoothManage.getInstance().receiveFace(2);
                break;
//            case R.id.app_notify_header_bq3:
//                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
//                    ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
//                    return;
//                }
//                BluetoothManage.getInstance().receiveFace(3);
//                break;
            case R.id.app_notify_header_bq4:
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
                    return;
                }
                BluetoothManage.getInstance().receiveFace(4);
                break;
            case R.id.notify_miss_you_tv:
                if (!isMissYou) {
                    isMissYou = true;
                    changeMessageType();
                }
                break;
            case R.id.notify_love_you_tv:
                if (isMissYou) {
                    isMissYou = false;
                    changeMessageType();
                }
                break;
        }
    }

    private void changeMessageType() {
        if (isMissYou) {
            //设置文字颜色
            mMissYouTv.setTextColor(getResources().getColor(R.color.color_main));
            mMissYouTv.setBackground(null);
            mMissYouTv.setTextSize(17);
            mMissYouTv.setTypeface(null, Typeface.BOLD);

            mLoveYouTv.setTextColor(getResources().getColor(R.color.text_color_black_nomal));
            mLoveYouTv.setBackgroundColor(ContextCompat.getColor(this,R.color.layer_bg_white));
            mLoveYouTv.setTextSize(15);
            mLoveYouTv.setTypeface(null, Typeface.NORMAL);

            if (BleParams.isCodeJewelry()) {
                mMakeCardSampleSubtitle.setText(setStyle());
                mMakeCardSampleSubtitle.setOnClickListener(v -> customMiddleTextDialog.show());
                mNotifyVibrationLayout.setVisibility(View.GONE);
            } else {
                if (BleParams.isButtonBatteryJewelry()) {
                    mMakeCardSampleSubtitle.setText(R.string.notify_remind_totwoo_set_dialog_title_no_vibration);
                } else {
                    mMakeCardSampleSubtitle.setText(R.string.notify_remind_totwoo_set_dialog_title);
                }
                mNotifyVibrationLayout.setVisibility(View.VISIBLE);
                switch (nowSetModel.getVibrationSeconds()) {
                    case LONG_VIBRATION_SEC:
                        setTextColorBtn(true);
                        break;
                    case SHORT_VIBRATION_SEC:
                        setTextColorBtn(false);
                        break;
                }
            }
            if (BleParams.isButtonBatteryJewelry()) {
                mNotifyVibrationLayout.setVisibility(View.GONE);
            }
            mBrightTypeLayout.setVisibility(View.GONE);

            colorLibraryAdapter.setSelectColor(nowSetModel.getFlashColor());
            colorLibraryRecyclerView.scrollToPosition(colorLibraryAdapter.getIndex(nowSetModel.getFlashColor()));
        } else {
            //设置文字颜色
            mLoveYouTv.setTextColor(getResources().getColor(R.color.color_main));
            mLoveYouTv.setBackground(null);
            mLoveYouTv.setTextSize(17);
            mLoveYouTv.setTypeface(null, Typeface.BOLD);

            mMissYouTv.setTextColor(getResources().getColor(R.color.text_color_black_nomal));
            mMissYouTv.setBackgroundColor(ContextCompat.getColor(this,R.color.layer_bg_white));
            mMissYouTv.setTextSize(15);
            mMissYouTv.setTypeface(null, Typeface.NORMAL);

//            if (BleParams.isNoneMusicJewelry()) {
            mNotifyVibrationLayout.setVisibility(View.VISIBLE);
            mBrightTypeLayout.setVisibility(View.GONE);

//            } else {
//                mNotifyVibrationLayout.setVisibility(View.GONE);
//                mBrightTypeLayout.setVisibility(View.VISIBLE);
//            }

            if (BleParams.isButtonBatteryJewelry()) {
                mNotifyVibrationLayout.setVisibility(View.GONE);
                mBrightTypeLayout.setVisibility(View.GONE);
            }

            if (BleParams.isButtonBatteryJewelry()) {
                mMakeCardSampleSubtitle.setText(R.string.app_notify_love_instructions_no_vibration);
            } else {
                mMakeCardSampleSubtitle.setText(R.string.app_notify_love_instructions);
            }

            //设置已选择的震动颜色和震动模式
            String loveColor = PreferencesUtils.getString(NotifyTotwooActivity.this, NotifyUtil.LOVE_YOU_FLASH_KEY, BleParams.getDefaultTotwooColor(false));
            colorLibraryAdapter.setSelectColor(loveColor);
            colorLibraryRecyclerView.scrollToPosition(colorLibraryAdapter.getIndex(loveColor));
            switch (getLoveYouVibrationSeconds()) {
                case LONG_VIBRATION_SEC:
                    setTextColorBtn(true);
                    break;
                case SHORT_VIBRATION_SEC:
                    setTextColorBtn(false);
                    break;
            }
        }
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationIv.setVisibility(View.VISIBLE);
            mShortVibrationIv.setVisibility(View.GONE);

            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mBrightShortVibrationTv.setTextColor(getResources().getColor(R.color.text_color_black_33));
            mBrightShortVibrationTv.setSelected(false);
            mBrightLongVibrationTv.setTextColor(getResources().getColor(R.color.white));
            mBrightLongVibrationTv.setSelected(true);
        } else {
            mLongVibrationIv.setVisibility(View.GONE);
            mShortVibrationIv.setVisibility(View.VISIBLE);

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mBrightShortVibrationTv.setTextColor(getResources().getColor(R.color.white));
            mBrightShortVibrationTv.setSelected(true);
            mBrightLongVibrationTv.setTextColor(getResources().getColor(R.color.text_color_black_33));
            mBrightLongVibrationTv.setSelected(false);
        }
    }

    private void saveNowModel(boolean isSwitch) {
//        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
//            ToastUtils.show(NotifyTotwooActivity.this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT);
//            return;
//        }
        if (!isSwitch) {
            if (isMissYou) {
                if (BleParams.isCodeJewelry()) {
                    BluetoothManage.getInstance().notifyMorseCode(nowSetModel.getVibrationSeconds(), NotifyUtil.getColorValue(nowSetModel.getFlashColor()));
                } else {
                    BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
                }
            } else {
//                if(BleParams.isNoneMusicJewelry() || PreferencesUtils.getInt(NotifyTotwooActivity.this, TOTWOO_MUSIC_TYPE, 1) == 0){
                BluetoothManage.getInstance().notifyJewelry(getLoveYouVibrationSeconds(), getLoveYouNotifyVibrationFlashColor());
//                }
//                else{
//                    BluetoothManage.getInstance().notifyBrightMusicOnce(8, getLoveYouVibrationFlashColor());
//                    MusicIndexBean musicIndexBean = new MusicIndexBean(8);
//                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_ONCE, musicIndexBean);
//                }
            }
        } else {
            mCallSwitchTitleTv.setText(nowSetModel.isNotifySwitch() ? R.string.notify_on : R.string.notify_off);
            //切换时候的动画
            Animation anim = AnimationUtils.loadAnimation(this, nowSetModel.isNotifySwitch() ? R.anim.layout_open : R.anim.layout_close);
            if (nowSetModel.isNotifySwitch()) {
                mNotifySettingContent.setVisibility(View.VISIBLE);
            } else {
                anim.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        mNotifySettingContent.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
            }
            mNotifySettingContent.startAnimation(anim);
        }
        NotifyUtil.setTotwooNotify(this, nowSetModel);
    }

    private SpannableString setStyle() {
        String string = getString(R.string.notify_remind_totwoo_set_morse_dialog_title);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = getString(R.string.notify_remind_totwoo_set_morse);
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(14, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#000000")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private void selectMusicType(int index) {
        switch (index) {
            case 0:
                mBrightBreathTv.setTextColor(getResources().getColor(R.color.color_main));
                mBrightBreathTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                mBrightBreathIv.setVisibility(View.VISIBLE);

                musicBrightBeans.get(0).setSelect(false);
                brightMusicAdapter.notifyDataSetChanged();

                mBrightVibrationLayout.setVisibility(View.VISIBLE);
                break;
            case 1:
                mBrightBreathTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
                mBrightBreathTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                mBrightBreathIv.setVisibility(View.GONE);

                musicBrightBeans.get(0).setSelect(true);
                brightMusicAdapter.notifyDataSetChanged();

                mBrightVibrationLayout.setVisibility(View.GONE);
                break;
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing() && PreferencesUtils.getInt(NotifyTotwooActivity.this, TOTWOO_MUSIC_TYPE, 1) > 0) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
            BluetoothManage.getInstance().changeBirghtMode(-1, false);
        }
    }

    private int getLoveYouVibrationSeconds() {
        return PreferencesUtils.getInt(NotifyTotwooActivity.this, NotifyUtil.LOVE_YOU_VIBRATION_SEC_KEY, LONG_VIBRATION_SEC);
    }

    private void setLoveYouVibrationSeconds(int seconds) {
        PreferencesUtils.put(NotifyTotwooActivity.this, NotifyUtil.LOVE_YOU_VIBRATION_SEC_KEY, seconds);
    }

    private int getLoveYouVibrationFlashColor() {
        return NotifyUtil.getColorValue(PreferencesUtils.getString(NotifyTotwooActivity.this, NotifyUtil.LOVE_YOU_FLASH_KEY, "PINK"));
    }

    private int getLoveYouNotifyVibrationFlashColor() {
        return NotifyUtil.getColorValue(PreferencesUtils.getString(NotifyTotwooActivity.this, NotifyUtil.LOVE_YOU_FLASH_KEY, "PINK"));
    }

    private void setLoveYouVibrationFlashColor(String flashColor) {
        PreferencesUtils.put(NotifyTotwooActivity.this, NotifyUtil.LOVE_YOU_FLASH_KEY, flashColor);
    }

    protected class BrightMusicAdapter extends RecyclerView.Adapter<BrightMusicAdapter.ViewHolder> {
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new BrightMusicAdapter.ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.music_bright_item, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.mLine.setVisibility(View.GONE);
            holder.mNameTv.setText(musicBrightBeans.get(position).getName());
            if (musicBrightBeans.get(position).isSelect()) {
                holder.mNameTv.setTextColor(getResources().getColor(R.color.color_main));
                holder.mNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                holder.mSelectIv.setVisibility(View.VISIBLE);
            } else {
                holder.mNameTv.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
                holder.mNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                holder.mSelectIv.setVisibility(View.INVISIBLE);
            }
            holder.mLayout.setOnClickListener(v -> {
                selectMusicType(position + 1);
                PreferencesUtils.put(NotifyTotwooActivity.this, TOTWOO_MUSIC_TYPE, position + 1);
                saveNowModel(false);
            });
        }

        @Override
        public int getItemCount() {
            return musicBrightBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.notify_bright_music_layout)
            ConstraintLayout mLayout;
            @BindView(R.id.notify_bright_music_tv)
            TextView mNameTv;
            @BindView(R.id.notify_bright_music_iv)
            ImageView mSelectIv;
            @BindView(R.id.notify_bright_music_view)
            View mLine;

            public ViewHolder(View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }
}
