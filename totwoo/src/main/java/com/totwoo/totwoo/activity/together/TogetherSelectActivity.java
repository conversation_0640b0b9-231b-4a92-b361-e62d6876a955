package com.totwoo.totwoo.activity.together;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.OvershootInterpolator;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.adapter.TogetherSelectAdapter;
import com.totwoo.totwoo.bean.TogetherSelectBean;
import com.totwoo.totwoo.databinding.TogetherSelectedItemBinding;

import net.lucode.hackware.magicindicator.FragmentContainerHelper;
import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.buildins.UIUtil;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.ColorTransitionPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.SimplePagerTitleView;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

public class TogetherSelectActivity extends BaseActivity {
    @BindView(R.id.together_select_viewPager)
    ViewPager2 mViewPager;
    @BindView(R.id.together_selected_rv)
    RecyclerView mSelectedRv;
    @BindView(R.id.together_select_indicator)
    MagicIndicator magicIndicator;

    private String[] types = {"国内", "国外"};
    private ArrayList<TogetherSelectBean> selectedBeans;
    private TogetherSelectedAdapter togetherSelectedAdapter;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_together_select);
        ButterKnife.bind(this);

        CommonNavigator commonNavigator = new CommonNavigator(this);
        commonNavigator.setAdapter(new CommonNavigatorAdapter() {

            @Override
            public int getCount() {
                return types.length;
            }

            @Override
            public IPagerTitleView getTitleView(Context context, final int index) {
                SimplePagerTitleView simplePagerTitleView = new ColorTransitionPagerTitleView(context);
                simplePagerTitleView.setNormalColor(getResources().getColor(R.color.text_color_gray_99));
                simplePagerTitleView.setSelectedColor(getResources().getColor(R.color.color_main));
                simplePagerTitleView.setText(types[index]);
                simplePagerTitleView.setOnClickListener(v -> mViewPager.setCurrentItem(index));
                return simplePagerTitleView;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                LinePagerIndicator linePagerIndicator = new LinePagerIndicator(context);
                linePagerIndicator.setMode(LinePagerIndicator.MODE_WRAP_CONTENT);
                linePagerIndicator.setColors(getResources().getColor(R.color.color_main));
                return linePagerIndicator;
            }
        });
        magicIndicator.setNavigator(commonNavigator);
        LinearLayout titleContainer = commonNavigator.getTitleContainer(); // must after setNavigator
        titleContainer.setShowDividers(LinearLayout.SHOW_DIVIDER_MIDDLE);
        titleContainer.setDividerDrawable(new ColorDrawable() {
            @Override
            public int getIntrinsicWidth() {
                return UIUtil.dip2px(TogetherSelectActivity.this, 15);
            }
        });

        final FragmentContainerHelper fragmentContainerHelper = new FragmentContainerHelper(magicIndicator);
        fragmentContainerHelper.setInterpolator(new OvershootInterpolator(2.0f));
        fragmentContainerHelper.setDuration(300);
        mViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                magicIndicator.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                magicIndicator.onPageSelected(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                magicIndicator.onPageScrollStateChanged(state);
            }
        });

        selectedBeans = new ArrayList<>();
        TogetherSelectAdapter togetherSelectAdapter = new TogetherSelectAdapter(TogetherSelectActivity.this, selectedBeans);
        togetherSelectAdapter.setOnCityClickListener((togetherSelectBean, isSelect) -> {
            if (isSelect) {
                selectedBeans.add(togetherSelectBean);
            } else {
                selectedBeans.remove(togetherSelectBean);
            }
            togetherSelectedAdapter.notifyDataSetChanged();
        });
        mViewPager.setAdapter(togetherSelectAdapter);
        mViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                magicIndicator.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                magicIndicator.onPageSelected(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                magicIndicator.onPageScrollStateChanged(state);
            }
        });

        togetherSelectedAdapter = new TogetherSelectedAdapter();
        mSelectedRv.setAdapter(togetherSelectedAdapter);
        mSelectedRv.setLayoutManager(new LinearLayoutManager(TogetherSelectActivity.this, LinearLayoutManager.HORIZONTAL, false));
    }

    public class TogetherSelectedAdapter extends RecyclerView.Adapter<TogetherSelectedAdapter.ViewHolder> {
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            TogetherSelectedItemBinding togetherSelectedItemBinding = TogetherSelectedItemBinding.inflate(LayoutInflater.from(TogetherSelectActivity.this), parent, false);
            return new ViewHolder(togetherSelectedItemBinding.getRoot(), togetherSelectedItemBinding);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.togetherSelectedItemBinding.togetherSelectedCity.setText(selectedBeans.get(position).getName());
            holder.togetherSelectedItemBinding.togetherSelectedDelete.setOnClickListener(v -> {
                selectedBeans.remove(position);
                togetherSelectedAdapter.notifyDataSetChanged();
            });
        }

        @Override
        public int getItemCount() {
            return selectedBeans == null ? 0 : selectedBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            private TogetherSelectedItemBinding togetherSelectedItemBinding;

            public ViewHolder(@NonNull View itemView, TogetherSelectedItemBinding togetherSelectedItemBinding) {
                super(itemView);
                this.togetherSelectedItemBinding = togetherSelectedItemBinding;
            }
        }
    }
}
