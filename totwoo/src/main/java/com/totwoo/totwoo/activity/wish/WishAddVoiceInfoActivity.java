package com.totwoo.totwoo.activity.wish;

import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.airbnb.lottie.LottieAnimationView;
import com.czt.mp3recorder.MP3Recorder;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.WeakReferenceHandler;
import com.totwoo.totwoo.widget.MagicProgressCircle;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

public class WishAddVoiceInfoActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.voice_discount_mpc)
    MagicProgressCircle mMPC;
    @BindView(R.id.wish_voice_discount_time_tv)
    TextView mDiscountTimeTv;
    @BindView(R.id.wish_add_voice_animation_view)
    LottieAnimationView mlottieView;
    @BindView(R.id.wish_add_voice_meteor_view)
    LottieAnimationView mMeteorlottieView;
    @BindView(R.id.wish_add_voice_dandelion_view)
    LottieAnimationView mDandelionView;
    @BindView(R.id.cancel_action_rect_ll)
    LinearLayout mCancelActionRectLl;
    @BindView(R.id.wish_voice_start_iv)
    ImageView mWishStartIv;
    @BindView(R.id.wish_voice_start_tv)
    TextView mGuideTextTv;

    private DisCountHandler handler;
    private long perTime = 100;
    private long allTime = 60000;
    private long endTime;

    int[] cancelPostion = new int[2];

    int cancelTop;

    int cancelBottom;

    private MP3Recorder mRecorder = new MP3Recorder(new File(CommonArgs.CACHE_WISH_AUDIO_PATH));
    private boolean reVideoing;

    private long reSec;
    Subscription reVideoStart;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_add_voice);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        mMPC.setPercent(1f);
        handler = new DisCountHandler(WishAddVoiceInfoActivity.this);

        mWishStartIv.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        //不显示拿不到区域
                        mGuideTextTv.setText(R.string.memory_audio_up_cancel);
                        mRecorder.setRecordSec(0);
                        if (mCancelActionRectLl.getVisibility() == View.GONE) {
                            mCancelActionRectLl.setAlpha(0);
                            mCancelActionRectLl.setVisibility(View.VISIBLE);
                        }
                        reSec = System.currentTimeMillis();
                        endTime = System.currentTimeMillis() + 60000;
                        handler.sendEmptyMessageDelayed(0, perTime);
                        mlottieView.setAnimation("sound.json");
                        mlottieView.playAnimation();
                        mDandelionView.setVisibility(View.VISIBLE);
                        reVideoStart = Observable.timer(perTime, TimeUnit.MILLISECONDS, Schedulers.newThread())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Action1<Long>() {
                                    @Override
                                    public void call(Long aLong) {
                                        if (!reVideoing) {
                                            startRecord();
                                        }
                                        reVideoing = true;
                                    }
                                });
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (cancelTop == 0 || cancelBottom == 0) {
                            mCancelActionRectLl.getLocationOnScreen(cancelPostion);
                            cancelTop = cancelPostion[1];
                            cancelBottom = cancelTop + mCancelActionRectLl.getHeight();
                        }
                        //在区域内则取消
                        if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom) {
                            if (mCancelActionRectLl.getAlpha() == 0) {
                                mCancelActionRectLl.setAlpha(1);
                            }
                        } else {
                            if (mCancelActionRectLl.getAlpha() == 1) {
                                mCancelActionRectLl.setAlpha(0);
                            }
                        }
                        break;
                    case MotionEvent.ACTION_CANCEL:
                    case MotionEvent.ACTION_UP:
                        if (!reVideoing) {
                            reVideoStart.unsubscribe();
                        }
                        reSec = System.currentTimeMillis() - reSec;
                        if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom) {
                            recordFinish();
                        } else if (reSec < 1300) {
                            recordFinish();
                            LogUtils.e("aab showToast");
                            Toast.makeText(WishAddVoiceInfoActivity.this,getString(R.string.memory_audio_time_short),Toast.LENGTH_SHORT).show();
//                            ToastUtils.showLong(WishAddVoiceInfoActivity.this, getString(R.string.memory_audio_time_short));
                        } else {
                            LogUtils.e("aab release");
                            recordSuccess();
                        }
                        mCancelActionRectLl.setVisibility(View.GONE);
                        mGuideTextTv.setText(R.string.memory_audio_start);
                        break;
                }
                return true;
            }
        });
    }

    private void recordFinish() {
        mDandelionView.setVisibility(View.GONE);
        mlottieView.setAnimation("voice_default.json");
        mlottieView.playAnimation();
        handler.removeCallbacksAndMessages(null);
        mMPC.setPercent(1f);
        mDiscountTimeTv.setText(60 + "s");
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

//    @OnClick({R.id.wish_voice_start_iv})
//    public void onClick(View view){
//        switch (view.getId()){
//            case R.id.wish_voice_start_iv:
//                endTime = System.currentTimeMillis() + 60000;
//                handler.sendEmptyMessageDelayed(0, perTime);
//                mlottieView.setAnimation("sound.json");
//                mlottieView.playAnimation();
//                break;
//        }
//    }

    public class DisCountHandler extends WeakReferenceHandler<WishAddVoiceInfoActivity> {

        public DisCountHandler(WishAddVoiceInfoActivity wishAddVoiceInfoActivity) {
            super(wishAddVoiceInfoActivity);
        }

        @Override
        public void handleLiveMessage(Message msg) {
            if (allTime > perTime) {
//                allTime -= perTime;
                allTime = endTime - System.currentTimeMillis();
                float percent = allTime / 60000f;
                mMPC.setPercent(percent);
                int discountTime = (int) (allTime / 1000);
                mDiscountTimeTv.setText(discountTime + "s");
                handler.sendEmptyMessageDelayed(0, perTime);
            } else {
                mMPC.setPercent(0f);
                mDiscountTimeTv.setText("0s");
                LogUtils.e("aab timeUp");
                recordSuccess();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mRecorder.release();
        handler.removeCallbacksAndMessages(null);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    /**
     * 开始录制
     */
    private void startRecord() {
        if (mRecorder.isRecording()) {
            Toast.makeText(this, R.string.make_card_reing_video, Toast.LENGTH_SHORT).show();
            return;
        }
        // 录制视频
        try {
            mRecorder.start();
        } catch (IOException e) {
            Toast.makeText(this, R.string.make_card_reing_video_failure, Toast.LENGTH_SHORT).show();
        }
    }


    /**
     * 停止录制
     */
    private void stopRecord() {
        mRecorder.stop();
    }

    @Override
    protected void onResume() {
        super.onResume();
        isSuccessed = false;
        allTime = 60000;
        mlottieView.playAnimation();
        mMeteorlottieView.playAnimation();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mlottieView.pauseAnimation();
        mMeteorlottieView.pauseAnimation();
    }

    private boolean isSuccessed = false;

    private void recordSuccess() {
        if (isSuccessed) {
            return;
        }
        isSuccessed = true;
        if (reVideoing) {
            stopRecord();
            reVideoing = false;
        }
        startActivity(new Intent(WishAddVoiceInfoActivity.this, WishAddInfoActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_SOUND));
        recordFinish();
    }

    /**
     * 心愿发布成功，干掉这个页面
     * WishAddInfoActivity
     */
    @EventInject(eventType = S.E.E_WISH_POST_SUCCESSED, runThread = TaskType.UI)
    public void postSuccess(EventData data) {
        finish();
    }
    /**
     * 心愿发布成功，干掉这个页面
     * WishAddInfoActivity
     */
    @EventInject(eventType = S.E.E_WISH_POST_BACK, runThread = TaskType.UI)
    public void postBack(EventData data) {
        finish();
    }
}
