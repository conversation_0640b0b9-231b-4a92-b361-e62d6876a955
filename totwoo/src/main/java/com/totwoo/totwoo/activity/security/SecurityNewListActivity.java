package com.totwoo.totwoo.activity.security;

import static com.totwoo.totwoo.activity.ContactsListActivity.SECURITY_CONTACT_LIST;

import android.app.Service;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.google.gson.Gson;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.ContactsActivityForEmergency;
import com.totwoo.totwoo.bean.SecurityContactsBean;
import com.totwoo.totwoo.bean.SecurityContactsHttpBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CommonShareDialog;
import com.totwoo.totwoo.widget.CommonShareType;
import com.totwoo.totwoo.widget.FullyLinearLayoutManager;
import com.totwoo.totwoo.widget.OnRecyclerItemClickListener;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class SecurityNewListActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.security_edit_ll)
    LinearLayout mEditLl;
    @BindView(R.id.security_edit_rv)
    RecyclerView mEditRecyclerView;
    @BindView(R.id.security_contact_rv)
    RecyclerView mRecyclerView;
    @BindView(R.id.security_add_rl)
    RelativeLayout mAddRl;
    @BindView(R.id.security_list_bottom_tv)
    TextView mBottomTv;
    @BindView(R.id.security_list_add_cl)
    ConstraintLayout mAddButtonCl;

    private ArrayList<SecurityContactsBean> contactsBeans;
    private ArrayList<SecurityContactsBean> editBeans;
    private SecurityContactsAdapter securityContactsAdapter;
    private SecurityEditAdapter securityEditAdapter;

    private boolean isOrder = false;
    private TextView mTopRightTv;
    private ImageView mIvBack;

    public static int INIT_STATUS = 0;
    public static int ADD_STATUS = 1;
    public static int MANAGER_STATUS = 2;
    private int current_status;
    private boolean isChanged = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_security_list_new);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        contactsBeans = new ArrayList<>();
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        securityContactsAdapter = new SecurityContactsAdapter();
        mRecyclerView.setAdapter(securityContactsAdapter);

        editBeans = new ArrayList<>();
        securityEditAdapter = new SecurityEditAdapter();
        mEditRecyclerView.setLayoutManager(new FullyLinearLayoutManager(this));
//        mEditRecyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL_LIST));
        mEditRecyclerView.setAdapter(securityEditAdapter);

        ItemTouchHelper mItemTouchHelper = new ItemTouchHelper(new ItemTouchHelper.Callback() {

            /**
             * 是否处理滑动事件 以及拖拽和滑动的方向 如果是列表类型的RecyclerView的只存在UP和DOWN，如果是网格类RecyclerView则还应该多有LEFT和RIGHT
             * @param recyclerView
             * @param viewHolder
             * @return
             */
            @Override
            public int getMovementFlags(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
                if (recyclerView.getLayoutManager() instanceof GridLayoutManager) {
                    final int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN |
                            ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
                    final int swipeFlags = 0;
                    return makeMovementFlags(dragFlags, swipeFlags);
                } else {
                    final int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
                    final int swipeFlags = 0;
//                    final int swipeFlags = ItemTouchHelper.START | ItemTouchHelper.END;
                    return makeMovementFlags(dragFlags, swipeFlags);
                }
            }

            @Override
            public boolean onMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, RecyclerView.ViewHolder target) {
                //得到当拖拽的viewHolder的Position
                int fromPosition = viewHolder.getAdapterPosition();
                //拿到当前拖拽到的item的viewHolder
                int toPosition = target.getAdapterPosition();
                if (fromPosition < toPosition) {
                    for (int i = fromPosition; i < toPosition; i++) {
                        Collections.swap(editBeans, i, i + 1);
                    }
                } else {
                    for (int i = fromPosition; i > toPosition; i--) {
                        Collections.swap(editBeans, i, i - 1);
                    }
                }
                isChanged = true;
                securityEditAdapter.notifyItemMoved(fromPosition, toPosition);
                return true;
            }

            @Override
            public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {

            }

            /**
             * 重写拖拽可用
             * @return
             */
            @Override
            public boolean isLongPressDragEnabled() {
                return false;
            }

            /**
             * 长按选中Item的时候开始调用
             *
             * @param viewHolder
             * @param actionState
             */
            @Override
            public void onSelectedChanged(RecyclerView.ViewHolder viewHolder, int actionState) {
                if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
                    viewHolder.itemView.setBackgroundColor(Color.LTGRAY);
                }
                super.onSelectedChanged(viewHolder, actionState);
            }

            /**
             * 手指松开的时候还原
             * @param recyclerView
             * @param viewHolder
             */
            @Override
            public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
                super.clearView(recyclerView, viewHolder);
                viewHolder.itemView.setBackgroundColor(0);
            }
        });

        mItemTouchHelper.attachToRecyclerView(mEditRecyclerView);

        mEditRecyclerView.addOnItemTouchListener(new OnRecyclerItemClickListener(mEditRecyclerView) {
            @Override
            public void onItemClick(RecyclerView.ViewHolder vh) {

            }

            @Override
            public void onItemLongClick(RecyclerView.ViewHolder vh) {
                //判断被拖拽的是否是前两个，如果不是则执行拖拽
//                if (vh.getLayoutPosition() != 0 && vh.getLayoutPosition() != 1) {
                mItemTouchHelper.startDrag(vh);

                //获取系统震动服务
                Vibrator vib = (Vibrator) getSystemService(Service.VIBRATOR_SERVICE);//震动70毫秒
                vib.vibrate(70);
//                }
            }
        });

        getSecurityContacts();
        current_status = getIntent().getIntExtra(CommonArgs.FROM_TYPE, 2);
        if (current_status == INIT_STATUS || current_status == ADD_STATUS) {
            mBottomTv.setVisibility(View.VISIBLE);
        }
    }

    private boolean isInit = true;
    @Override
    protected void initTopBar() {
        super.initTopBar();
        if(isInit){
            isInit = false;
        }else{
            return;
        }
        setTopTitle(R.string.safe_contacts_title);
        mIvBack = getTopLeftImage();
        mTopRightTv = getTopRightTv();
        if (current_status == MANAGER_STATUS) {
            mIvBack.setVisibility(View.VISIBLE);
            mIvBack.setImageResource(R.drawable.back_icon_black);
            mIvBack.setOnClickListener(v -> finish());
            mTopRightTv.setText(R.string.safe_emergency_contact_sort);
            mTopRightTv.setTextSize(14);
            mTopRightTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
            mTopRightTv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (isOrder) {
                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.EMERGENCYADDRESS_CLICK_SORT);
                        isOrder = false;
                        mTopRightTv.setText(R.string.safe_emergency_contact_sort);
                        mTopRightTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
                        mIvBack.setVisibility(View.VISIBLE);
                        setEditStatus();
                    } else {
                        isOrder = true;
                        mTopRightTv.setText(R.string.safe_emergency_contact_save);
                        mTopRightTv.setTextColor(getResources().getColor(R.color.color_main));
                        mIvBack.setVisibility(View.GONE);
                        setEditStatus();

                    }
                }
            });
            mTopRightTv.setVisibility(View.GONE);
        } else {
            mIvBack.setVisibility(View.GONE);
            mTopRightTv.setVisibility(View.GONE);
        }
    }

    private void setEditStatus() {
        if (isOrder) {
            mAddRl.setVisibility(View.GONE);
            mEditLl.setVisibility(View.VISIBLE);
            editBeans.clear();
            editBeans.addAll(contactsBeans);
            securityEditAdapter.notifyDataSetChanged();
        } else {
            mAddRl.setVisibility(View.VISIBLE);
            mEditLl.setVisibility(View.GONE);
            saveContacts();
        }
    }

    @OnClick({R.id.security_list_add_cl, R.id.security_list_bottom_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.security_list_add_cl:
                if (!PermissionUtil.hasContactsPermission(SecurityNewListActivity.this)) {
                    return;
                }
                startContactsList();
                break;
            case R.id.security_list_bottom_tv:
                startActivity(new Intent(SecurityNewListActivity.this, EmergencyDocActivity.class).putExtra(CommonArgs.FROM_TYPE, current_status));
                break;
        }
    }

    private void startContactsList() {
        Intent intent = new Intent(SecurityNewListActivity.this, ContactsActivityForEmergency.class);
        if (contactsBeans != null && contactsBeans.size() > 0) {
            intent.putExtra(SECURITY_CONTACT_LIST, contactsBeans);
        }
        startActivityForResult(intent, 100);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        getSecurityContacts();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int grantResult : grantResults) {
            if (grantResult != PackageManager.PERMISSION_DENIED) {
                startContactsList();
            }
        }
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }

    private void getSecurityContacts() {
        HttpHelper.safeService.getContacts(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<SecurityContactsHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(SecurityNewListActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
                        if (securityContactsHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            handlerSecurityBeans(securityContactsHttpBeanHttpBaseBean);
                        }
                    }
                });
    }

    private void handlerSecurityBeans(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
        contactsBeans.clear();
        if (securityContactsHttpBeanHttpBaseBean.getData() != null && securityContactsHttpBeanHttpBaseBean.getData().getInfo() != null && securityContactsHttpBeanHttpBaseBean.getData().getInfo().size() > 0) {
            contactsBeans.addAll(securityContactsHttpBeanHttpBaseBean.getData().getInfo());
        }
        securityContactsAdapter.notifyDataSetChanged();

        if (contactsBeans.size() == 3) {
            mAddButtonCl.setVisibility(View.GONE);
            if (current_status == MANAGER_STATUS) {
                mTopRightTv.setVisibility(View.VISIBLE);
            }
        } else if (contactsBeans.size() <= 1) {
            mAddButtonCl.setVisibility(View.VISIBLE);
            if (current_status == MANAGER_STATUS) {
                mTopRightTv.setVisibility(View.GONE);
            }
        } else {
            mAddButtonCl.setVisibility(View.VISIBLE);
            if (current_status == MANAGER_STATUS) {
                mTopRightTv.setVisibility(View.VISIBLE);
            }
        }
    }

    private void delSecurityContact(String phoneNumber) {
        HttpHelper.safeService.delContacts(phoneNumber)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<SecurityContactsHttpBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(SecurityNewListActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<SecurityContactsHttpBean> securityContactsHttpBeanHttpBaseBean) {
                        if (securityContactsHttpBeanHttpBaseBean.getErrorCode() == 0) {
                            handlerSecurityBeans(securityContactsHttpBeanHttpBaseBean);
                        }
                    }
                });
    }

    private CommonMiddleDialog delDialog;

    private void showDelDialog(String phoneNumber) {
        delDialog = new CommonMiddleDialog(this);
        delDialog.setMessage(getString(R.string.safe_contacts_share_content));
        delDialog.setSure(R.string.common_confirm, v -> {
            delDialog.dismiss();
            delSecurityContact(phoneNumber);
        });
        delDialog.setCancel(R.string.cancel);
        delDialog.show();
    }

    private CommonShareDialog commonShareDialog;

    private void showShareDialog(String phoneNumber) {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.EMERGENCY_CLICK_NATICY);
        String shareInfo = getString(R.string.safe_contacts_share_text);
        List<CommonShareType> types = new ArrayList<>();
        types.add(CommonShareType.WECHAT);
        types.add(CommonShareType.MESSAGE);
        commonShareDialog = new CommonShareDialog(SecurityNewListActivity.this, types, v -> {
            switch ((CommonShareType) v.getTag()) {
                case WECHAT:
                    ShareUtilsSingleton.getInstance().shareTextToWechat(shareInfo);
                    commonShareDialog.dismiss();
                    break;
                case MESSAGE:
                    shareGuardByMessage(phoneNumber);
                    commonShareDialog.dismiss();
                    break;
            }

        });
        commonShareDialog.setCustomTitle(getResources().getString(R.string.safe_contacts_share_title));
        commonShareDialog.show();
    }

    protected void shareGuardByMessage(String phoneNumber) {
        Uri smsToUri = Uri.parse("smsto:+" + phoneNumber);

        Intent intent = new Intent(Intent.ACTION_SENDTO, smsToUri);

        intent.putExtra("sms_body", getString(R.string.safe_contacts_share_text));
        startActivity(intent);
    }

    @EventInject(eventType = S.E.E_IMEI_UPDATE_SUCCEED, runThread = TaskType.UI)
    public void imeiUpdateSucceed(EventData data) {
        finish();
    }

    private void saveContacts() {
        isChanged = false;
        contactsBeans.clear();
        contactsBeans.addAll(editBeans);
        securityContactsAdapter.notifyDataSetChanged();

        Gson gson = new Gson();
        String jsonString = gson.toJson(editBeans);
        saveSecurityContacts(jsonString);
    }

    private void saveSecurityContacts(String jsonString) {
        LogUtils.e("aab jsonString = " + jsonString);
        HttpHelper.safeService.saveContacts(jsonString)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(SecurityNewListActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {

                    }
                });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    public class SecurityContactsAdapter extends RecyclerView.Adapter<SecurityContactsAdapter.ViewHolder> {

        @NonNull
        @Override
        public SecurityContactsAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.security_list_item, parent, false);
            return new ViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(@NonNull SecurityContactsAdapter.ViewHolder holder, int position) {
            if (position == 0) {
                holder.mIndexTv.setText(R.string.safe_emergency_contact_1);
            } else if (position == 1) {
                holder.mIndexTv.setText(R.string.safe_emergency_contact_2);
            } else if (position == 2) {
                holder.mIndexTv.setText(R.string.safe_emergency_contact_3);
            }
            if (TextUtils.equals(contactsBeans.get(position).getName(), contactsBeans.get(position).getTel())) {
                holder.mPhoneTv.setVisibility(View.GONE);
                holder.mNameTv.setVisibility(View.GONE);
                holder.mPhoneMiddleTv.setVisibility(View.VISIBLE);
            } else {
                holder.mPhoneTv.setVisibility(View.VISIBLE);
                holder.mNameTv.setVisibility(View.VISIBLE);
                holder.mPhoneMiddleTv.setVisibility(View.GONE);
            }
            holder.mNameTv.setText(contactsBeans.get(position).getName());
            holder.mPhoneTv.setText(contactsBeans.get(position).getTel());
            holder.mPhoneMiddleTv.setText(contactsBeans.get(position).getTel());
            holder.mDeleteIv.setOnClickListener(v -> showDelDialog(contactsBeans.get(position).getTel()));
            holder.mNotifyTv.setOnClickListener(v -> showShareDialog(contactsBeans.get(position).getTel()));

        }

        @Override
        public int getItemCount() {
            return contactsBeans == null ? 0 : contactsBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.security_item_index_tv)
            TextView mIndexTv;
            @BindView(R.id.security_item_delete_iv)
            ImageView mDeleteIv;
            @BindView(R.id.security_phone_tv)
            TextView mPhoneTv;
            @BindView(R.id.security_name_tv)
            TextView mNameTv;
            @BindView(R.id.security_phone_middle_tv)
            TextView mPhoneMiddleTv;
            @BindView(R.id.security_notify_tv)
            TextView mNotifyTv;

            public ViewHolder(View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    public class SecurityEditAdapter extends RecyclerView.Adapter<SecurityEditAdapter.ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.security_list_edit_item, parent, false);
            return new SecurityEditAdapter.ViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            if (TextUtils.equals(editBeans.get(position).getName(), editBeans.get(position).getTel())) {
                holder.mPhoneTv.setVisibility(View.GONE);
                holder.mNameTv.setVisibility(View.GONE);
                holder.mPhoneMiddleTv.setVisibility(View.VISIBLE);
            } else {
                holder.mPhoneTv.setVisibility(View.VISIBLE);
                holder.mNameTv.setVisibility(View.VISIBLE);
                holder.mPhoneMiddleTv.setVisibility(View.GONE);
            }
            holder.mNameTv.setText(editBeans.get(position).getName());
            holder.mPhoneTv.setText(editBeans.get(position).getTel());
            holder.mPhoneMiddleTv.setText(editBeans.get(position).getTel());
        }

        @Override
        public int getItemCount() {
            return editBeans == null ? 0 : editBeans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.security_edit_phone_tv)
            TextView mPhoneTv;
            @BindView(R.id.security_edit_name_tv)
            TextView mNameTv;
            @BindView(R.id.security_edit_phone_middle_tv)
            TextView mPhoneMiddleTv;

            public ViewHolder(View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }

    @Override
    public void onBackPressed() {
        notifyAndFinish();
    }

    private void notifyAndFinish() {
        if (!isOrder) {
            finish();
        } else if (isChanged) {
            showNotSaveDialog();
        } else {
            isOrder = false;
            mTopRightTv.setText(R.string.safe_emergency_contact_sort);
            mTopRightTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
            mIvBack.setVisibility(View.VISIBLE);
            mAddRl.setVisibility(View.VISIBLE);
            mEditLl.setVisibility(View.GONE);
        }
    }

    private void showNotSaveDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(SecurityNewListActivity.this);
        commonMiddleDialog.setMessage(R.string.custom_order_tips);
        commonMiddleDialog.setSure(v -> {
            commonMiddleDialog.dismiss();
            isOrder = false;
            isChanged = false;
            mTopRightTv.setText(R.string.safe_emergency_contact_sort);
            mTopRightTv.setTextColor(getResources().getColor(R.color.text_color_gray_99));
            mIvBack.setVisibility(View.VISIBLE);
            mAddRl.setVisibility(View.VISIBLE);
            mEditLl.setVisibility(View.GONE);
        });
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }
}
