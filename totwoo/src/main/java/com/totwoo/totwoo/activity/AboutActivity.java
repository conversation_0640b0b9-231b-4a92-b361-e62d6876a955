package com.totwoo.totwoo.activity;

import android.content.ClipboardManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.AppUpdateBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.ApkDownloader;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.AppDownloadDialog;
import com.totwoo.totwoo.widget.CustomDialog;

import java.io.File;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Subscriber;

public class AboutActivity extends BaseActivity implements OnClickListener {
    // 版本tv
    @BindView(R.id.version_tv)
    TextView version_tv;

    @BindView(R.id.member_activity_rl)
    RelativeLayout member_activity_rl;

    @BindView(R.id.totwoo_website_rl)
    RelativeLayout totwoo_website_rl;

    // 版本更新
    @BindView(R.id.version_update_rl)
    RelativeLayout version_update_rl;

    // 官方微信
    @BindView(R.id.official_wechat_rl)
    RelativeLayout official_wechat_rl;

    // 官方微博
    @BindView(R.id.offical_weibo_rl)
    RelativeLayout offical_weibo_rl;
    // 首饰维修保养
    @BindView(R.id.jewelry_repair_maintenance_rl)
    RelativeLayout jewelry_repair_maintenance_rl;
    // 帮助中心
    @BindView(R.id.help_rl)
    RelativeLayout help_rl;

    // 客服电话
    @BindView(R.id.customer_service_phone_rl)
    RelativeLayout customer_service_phone_rl;

    private AppDownloadDialog appDownloadDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about);
        ButterKnife.bind(this);

        initData();
        setOnclick();

        version_tv.setText("Version " + Apputils.getVersionName(this, true));

    }

    private void initData() {
        if (!Apputils.systemLanguageIsChinese(this)) {
            official_wechat_rl.setVisibility(View.GONE);
            member_activity_rl.setVisibility(View.GONE);
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.about);
    }

    private void setOnclick() {
        version_update_rl.setOnClickListener(this);
        member_activity_rl.setOnClickListener(this);
        official_wechat_rl.setOnClickListener(this);
        offical_weibo_rl.setOnClickListener(this);
        jewelry_repair_maintenance_rl.setOnClickListener(this);
        help_rl.setOnClickListener(this);
        customer_service_phone_rl.setOnClickListener(this);
        totwoo_website_rl.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.totwoo_website_rl:
                String url = HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_HELP);
                if (!Apputils.systemLanguageIsChinese(this)) {
                    url = "http://www.totwooglobal.com/";
                }
                WebActivity.showWeb(this, url, true);
                break;
            case R.id.member_activity_rl:
                WebActivity.showWeb(this, HttpHelper.HOSTURL + "survey");
                break;
            case R.id.version_update_rl:// 版本更新
                HttpHelper.commonService.checkAppUpdate(Apputils.getVersionName(this))
                        .compose(HttpHelper.rxSchedulerHelper())
                        .subscribe(new Subscriber<HttpBaseBean<AppUpdateBean>>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {

                            }

                            @Override
                            public void onNext(HttpBaseBean<AppUpdateBean> appUpdateBeanHttpBaseBean) {
                                if (appUpdateBeanHttpBaseBean.getErrorCode() == 0) {
                                    AppUpdateBean data = appUpdateBeanHttpBaseBean.getData();
                                    if (data.isNeed_update()) {
                                        String updateInfo;
                                        if (Apputils.systemLanguageIsChinese(AboutActivity.this)) {
                                            updateInfo = data.getTxt();
                                        } else {
                                            updateInfo = data.getEn_txt();
                                        }
                                        showUpdateVersionDialog(updateInfo, data.getUrl(), data.getLastest_version(), data.getPackage_size(), data.getIs_force_update() == 1);
                                    } else {
                                        ToastUtils.showLong(AboutActivity.this,
                                                R.string.no_need_update);
                                    }
                                } else {
                                    ToastUtils.show(AboutActivity.this, R.string.error_net, Toast.LENGTH_SHORT);
                                }
                            }
                        });
                break;
            case R.id.official_wechat_rl:// 官方微信
                ClipboardManager cmb = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                cmb.setText(getString(R.string.official_wewhat_number));
                showAttentionDialog();
                break;
            case R.id.offical_weibo_rl:// 官方微博
                WebActivity.showWeb(this, getString(R.string.official_weibo_http));
                break;
            case R.id.jewelry_repair_maintenance_rl:// 首饰维修与保养
                Intent intent = new Intent();
                Bundle bundle = new Bundle();
                intent.putExtra(" ",bundle);
                startActivity(intent);
                WebViewActivity.loadUrl(this, HttpHelper.URL_HELP_JEWERY, false);

                break;
            case R.id.help_rl:// 帮助中心
                LogUtils.e("aab HttpHelper.HOSTURL_WEB_HELP = " + HttpHelper.HOSTURL_WEB_HELP);
                WebViewActivity.loadUrl(this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_HELP), false);
//                WebActivity.showWeb(this, HttpHelper.HOSTURL_WEB_HELP, true);
                break;
            case R.id.customer_service_phone_rl:// 客服电话
                String action;
                if (Apputils.systemLanguageIsChinese(this)) {
                    action = Intent.ACTION_DIAL;
                } else {
                    action = Intent.ACTION_SENDTO;
                }
                try {
                    startActivity(new Intent(action, Uri.parse(getString(R.string.customer_service_phone_number))));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
        }
    }

    /**
     * 关注微信dialog
     */
    private void showAttentionDialog() {

        final CustomDialog dialog = new CustomDialog(this);

        dialog.setMessage(R.string.attention_wechat);

        dialog.setNegativeButton(R.string.cancel);

        dialog.setPositiveButton(R.string.attention, new OnClickListener() {

            @Override
            public void onClick(View v) {

                if (Apputils.isAppInstalled(AboutActivity.this,
                        "com.tencent.mm")) {
                    Intent intent = new Intent();

                    ComponentName cmp = new ComponentName("com.tencent.mm",
                            "com.tencent.mm.ui.LauncherUI");
                    intent.setAction(Intent.ACTION_MAIN);
                    intent.addCategory(Intent.CATEGORY_LAUNCHER);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setComponent(cmp);

                    startActivityForResult(intent, 0);
                    dialog.dismiss();
                } else {
                    dialog.setNegativeButtonVisibility(View.GONE);
                    dialog.setPositiveButton(R.string.i_know,
                            new OnClickListener() {

                                @Override
                                public void onClick(View v) {
                                    dialog.dismiss();
                                }
                            });
                }
            }
        });
        dialog.show();
    }

    private void showUpdateVersionDialog(String updateInfo, String url, String version, long length, boolean forceUpdate) {
//        appDownloadDialog = new AppDownloadDialog(AboutActivity.this, updateInfo, v -> {
//            checkAndDownloadApk(url, version, length, forceUpdate);
//            appDownloadDialog.dismiss();
//        }, v -> {
//            appDownloadDialog.dismiss();
//        });
//        appDownloadDialog.show();
    }

    private void checkAndDownloadApk(String url, String version, long targetLength, boolean forceUpdate) {
        File file = new File(FileUtils.getDownloadDir() + File.separator + "apk" + File.separator + "totwoo" + version + ".apk");

        //因为断点续传的原因，file的长度没法作为是不是完整包的依据。就用了一个参数CommonArgs.APK_DOWNLOAD_SUCCESS判断。但是MessageActivity的会影响。所以判断有dialog的时候才去修改这个值
        LogUtils.d("apk file.length() = " + file.length());
        if (file.exists() && file.length() == targetLength) {
            CommonUtils.installApk(file, this);
        } else {
            new ApkDownloader(true, true, true, forceUpdate)
                    .downloads(this, url, file);
        }
    }
}
