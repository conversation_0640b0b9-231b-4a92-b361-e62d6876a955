package com.totwoo.totwoo.activity;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.Qian;
import com.totwoo.totwoo.fragment.QianDetailPage;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.flipview.FlipView;

/**
 * 抽签详情页界面
 * <p/>
 * Created by lixingmao on 16/2/26.
 */
public class QianDetailActivity extends BaseActivity {
    public static final String LAST_QIAN_TAG_PREFIX = "last_qian_";
    public static final String QIAN_TYPE_TAG = "qian_type_tag";
//    public static final String QIAN_COUNT_TAG_PREFIX = "qian_count_";

    // 默认等待时间 5 秒
    private final int DEFAULT_MIN_WAIRING_TIME = 4600;
    /**
     * 正在抽签的过渡动画
     */
    private TextView flash;
    private QianDetailPage page;
    private FlipView flipView;

    private int qianType;

    /**
     * 开始请求数据时间
     */
    private long t0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qian_detail);

        page = new QianDetailPage(this, null, true);

        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT);

        params.addRule(RelativeLayout.BELOW, R.id.totwoo_topbar_layout);

        qianType = getIntent().getIntExtra(QIAN_TYPE_TAG, 0);

        flipView = insertFilpView(page);

        // 自动翻页, 屏蔽手动翻页
        flipView.setBlockTouch(true);
        ((RelativeLayout) findViewById(R.id.qian_detail_content_layout)).addView(flipView, params);
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopbarBackground(R.color.layer_bg_white);
    }

    /**
     * 将目标 View 嵌入 FilpView 仪实现过渡动画
     *
     * @param rootView
     * @return
     */
    private FlipView insertFilpView(final View rootView) {
        flash = new TextView(this);
        flash.setLayoutParams(new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        flash.setGravity(Gravity.CENTER);
        flash.setText(R.string.get_qian_ing);
        flash.setTextSize(18);
        flash.setTextColor(getResources().getColor(R.color.text_color_black_important));
        flash.setBackgroundColor(getResources().getColor(R.color.layer_bg_white_little_transparent));

        FlipView flipView = new FlipView(this);
        flipView.setAdapter(new BaseAdapter() {
            @Override
            public int getCount() {
                return 2;
            }

            @Override
            public Object getItem(int position) {
                return position;
            }

            @Override
            public long getItemId(int position) {
                return position;
            }

            @Override
            public View getView(int position, View convertView, ViewGroup parent) {
                return position == 0 ? flash : rootView;
            }
        });

        getQian();

        return flipView;
    }

    /**
     * 开始抽签逻辑,
     */
    private void getQian() {
        t0 = System.currentTimeMillis();

        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("qianType", qianType + 1);

        // 请求数据
        HttpRequest.get(
                HttpHelper.URL_GET_QIAN,
                params,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
                        Qian qian = Qian.getQianfromJson(
                                HttpHelper.parserStringResponse(s));

                        if (qian != null) {
                            // 设置数据, 刷新 detail 界面
                            page.setQian(qian);

                            showPage(qian);

                            // 抽签计数 +1
                            addQianCount();

                            // 缓存数据
//                            HttpHelper.saveDataCache(HttpHelper.URL_GET_QIAN + qianType, qian);
                            ACache.get(QianDetailActivity.this).put(CommonArgs.CACHE_QIAN + qianType,qian);
                        } else {
                            ToastUtils.showLong(QianDetailActivity.this, R.string.error_net);
                        }
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        finish();
                        ToastUtils.showLong(QianDetailActivity.this, R.string.error_net);
                    }
                });
    }

    /**
     * 抽签计数 +1
     */
    private void addQianCount() {

        PreferencesUtils.put(this,
                LAST_QIAN_TAG_PREFIX + qianType, Apputils.getZeroCalendar(null).getTimeInMillis());

//        if (PreferencesUtils.getLong(this, QianDetailActivity.LAST_QIAN_TAG_PREFIX + qianType, 0)
//                != Apputils.getZeroCalendar(null).getTimeInMillis()) {
//            PreferencesUtils.put(this,
//                    LAST_QIAN_TAG_PREFIX + qianType, Apputils.getZeroCalendar(null).getTimeInMillis());
//            PreferencesUtils.put(this, QIAN_COUNT_TAG_PREFIX + qianType, 1);
//        } else {
//            PreferencesUtils.put(this, QIAN_COUNT_TAG_PREFIX + qianType,
//                    PreferencesUtils.getInt(this, QianDetailActivity.QIAN_COUNT_TAG_PREFIX + qianType, 0) + 1);
//        }
    }

    /**
     * 数据请求成功, 跳转第二页
     *
     * @param qian
     */
    private void showPage(Qian qian) {
        long t1 = DEFAULT_MIN_WAIRING_TIME - (System.currentTimeMillis() - t0);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (flipView != null) {
                    ValueAnimator an = ValueAnimator.ofFloat(1, 0.2f).setDuration(600);

                    an.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                        @Override
                        public void onAnimationUpdate(ValueAnimator animation) {
                            float al = (Float) animation.getAnimatedValue();
                            flash.setAlpha(al);
                        }
                    });

                    an.addListener(new Animator.AnimatorListener() {
                        @Override
                        public void onAnimationStart(Animator animation) {
                        }

                        @Override
                        public void onAnimationEnd(Animator animation) {
//                            flash.setText("");
                            flipView.smoothFlipTo(1, 1000);

//                            setTopRightIcon(R.drawable.icon_share_gift);
//                            setTopRightOnClick(new View.OnClickListener() {
//                                @Override
//                                public void onClick(View v) {
//                                    startActivity(new Intent(QianDetailActivity.this, QianShareActivity.class).putExtra(QIAN_TYPE_TAG, qianType));
//                                }
//                            });
                        }

                        @Override
                        public void onAnimationCancel(Animator animation) {
                        }

                        @Override
                        public void onAnimationRepeat(Animator animation) {

                        }
                    });

                    an.start();
                }
            }
        }, t1 < 0 ? 0 : t1);
    }
}
