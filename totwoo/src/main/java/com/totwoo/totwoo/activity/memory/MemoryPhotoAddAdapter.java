package com.totwoo.totwoo.activity.memory;

import android.app.Activity;
import android.view.View;
import android.widget.ImageView;

import com.etone.framework.annotation.AdapterView;
import com.etone.framework.annotation.ViewInject;
import com.etone.framework.base.BaseArrayListAdapter;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;

import java.io.File;
import java.util.ArrayList;

/**
 * Created by xinyoulingxi on 2017/8/3.
 */

@AdapterView(R.layout.activity_memory_photo_add_item)
public class MemoryPhotoAddAdapter extends BaseArrayListAdapter<String>
{
    protected static final String MEMORY_PHOTO_ADD = "memoryPhotoAdd";

    private Activity context;
    public MemoryPhotoAddAdapter(Activity context, ArrayList<String> resource)
    {
        super(context, resource, true);
        this.context = context;
    }

    @Override
    public void initHolderData(BaseHolder _holder, int position, final String item)
    {
        Holder holder = (Holder) _holder;
        holder.delete.setVisibility(View.GONE);
        if (item.equals(MEMORY_PHOTO_ADD))
        {
            holder.bg.setImageResource(R.drawable.memory_photo_add);
        }
        else
        {
            BitmapHelper.display(context, holder.bg, new File(item), R.drawable.memory_set_edit); //我的头像
            holder.delete.setVisibility(View.VISIBLE);
            holder.delete.setOnClickListener(new View.OnClickListener()
            {
                @Override
                public void onClick(View v)
                {
                    resource.remove(item);
                    if (!checkSelectNewPhoto())
                        MemoryPhotoAddAdapter.this.notifyDataSetChanged();
                }
            });
        }
    }

    public boolean checkSelectNewPhoto()
    {
        int count = resource.size();
        if (count < 8 && !resource.contains(MEMORY_PHOTO_ADD))
        {
            resource.add(MEMORY_PHOTO_ADD);
            this.notifyDataSetChanged();
            return true;
        }

        return false;
    }

    public static class Holder implements BaseHolder
    {
        @ViewInject(R.id.memory_photo_add_item_img)
        public ImageView bg;

        @ViewInject (R.id.memory_photo_add_item_delete)
        public ImageView delete;
    }
}
