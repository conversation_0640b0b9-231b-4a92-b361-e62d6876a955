package com.totwoo.totwoo.activity;

import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Subscriber;

/**
 * 意见反馈
 */
public class OpinionFeedbackActivity extends BaseActivity{
    /**
     * 反馈信息
     */
    @BindView(R.id.opinion_et)
    EditText opinion_et;

    /**
     * 联系方式
     */
    @BindView(R.id.contact_way_et)
    EditText contact_way_et;

    @BindView(R.id.feedback_main_count_text)
    TextView feedback_main_count_text;

    private String opinion_message;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_opinion_feedback);
        ButterKnife.bind(this);
//        setTopRightIcon(R.drawable.opinion_submit);

        opinion_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
//                int[] length = StringUtils.length(s.toString());
//                if (length[0] > 1000) {
//                    opinion_et.setText(s.subSequence(0, 1000 - length[1] + (length[0] - 1000 - 1)));
//                    opinion_et.setSelection(opinion_et.getText().length());
//                }
                int length = s.toString().length();
                if(length <= 0){
                    feedback_main_count_text.setText("0/500");
                }else if(length <= 500){
                    feedback_main_count_text.setText(length + "/500");
                }else{
                    opinion_et.setText(s.subSequence(0, 500));
                    opinion_et.setSelection(500);
                }
            }
        });

    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.opinion_feedback);
        setTopRightString(R.string.submit);
        setTopRightOnClick(new OnClickListener() {
            @Override
            public void onClick(View v) {
                opinion_message = opinion_et.getText().toString().trim();
                if (!opinion_message.isEmpty()) {
                    submitOpinion();
                } else {
                    Toast.makeText(OpinionFeedbackActivity.this, R.string.please_input_feedback, Toast.LENGTH_SHORT).show();
                }
            }
        });

    }

    private void submitOpinion() {
        HttpHelper.commonServiceV2.feedback(opinion_message,contact_way_et.getText()
                .toString().trim(),Build.VERSION.RELEASE,NetUtils.checkNetworkType(this),
                Build.MANUFACTURER + Build.MODEL,
                PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))
                .compose(HttpHelper.<HttpBaseBean<String>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        ToastUtils.showShort(OpinionFeedbackActivity.this,
                                R.string.thanks_for_your_feedback_);
                        finish();
                    }
                });

//        RequestParams params = HttpHelper.getBaseParams(true);
//        params.addFormDataPart("message", opinion_message);
//        if (!contact_way_et.getText().toString().trim().isEmpty()) {
//            params.addFormDataPart("contact", contact_way_et.getText()
//                    .toString().trim());
//        }
//
//        params.addFormDataPart("version", Apputils.getVersionName(this));
//        params.addFormDataPart("platform", "android");
//        params.addFormDataPart("os_version", Build.VERSION.RELEASE);
//        params.addFormDataPart("network", NetUtils.checkNetworkType(this));
//        params.addFormDataPart("product", "totwoo_b_xxx");
//        params.addFormDataPart("phone", Build.MANUFACTURER + Build.MODEL);
//
//        HttpRequest.post(
//                HttpHelper.URL_FEEDBACK, params, new RequestCallBack<String>(this) {
//
//                    @Override
//                    public void onLogicSuccess(String s) {
//                        super.onLogicSuccess(s);
//                        ToastUtils.showShort(OpinionFeedbackActivity.this,
//                                R.string.thanks_for_your_feedback_);
//                        finish();
//                    }
//                });
//    }

        // 上传日志文件
//        File logFile = BLELOG.getTodatLogFile();
//        if (logFile == null) {
//            return;
//        }
//
//        UpdatePictureController.getInstance().uploadBleLog(this, logFile.getAbsolutePath());

        /*params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("file", logFile);
        params.addFormDataPart("totwoo_id", ToTwooApplication.owner.getTotwooId());

        HttpRequest.post("http://api.totwoo.com/v1/UpLoad/logfile", params, new RequestCallBack<String>(){
            @Override
            public void onLogicSuccess(String s) {
                super.onLogicSuccess(s);
            }
        });*/
    }
}
