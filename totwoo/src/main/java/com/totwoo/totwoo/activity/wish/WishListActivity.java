package com.totwoo.totwoo.activity.wish;

import android.animation.Animator;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.WishInfoBean;
import com.totwoo.totwoo.bean.WishInfoHttp;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.BaseHeaderAdapter;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderEntity;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderItemDecoration;

import org.json.JSONArray;
import org.json.JSONException;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class WishListActivity extends BaseActivity implements SubscriberListener {
    private static final String WISH_COVER_HAS_OPEN = "wish_cover_has_open";
    @BindView(R.id.wish_list_rv)
    RecyclerView mWishList;
    @BindView(R.id.card_front_cover)
    RelativeLayout cardFontCover;
    @BindView(R.id.wish_list_all_content)
    RelativeLayout mWishListAllContent;
    @BindView(R.id.wish_list_cover_lv)
    LottieAnimationView mWishListCoverLv;

    private List<PinnedHeaderEntity<WishInfoBean>> allWishBeans;
    private BaseHeaderAdapter<PinnedHeaderEntity<WishInfoBean>> mAdapter;
    private int cardHeight;
    private int cardWidth;
    private int currentPosition;
    private int totalCount;
    private int currentPage = 0;
    private String lastYear = "0";
    private ShakeMonitor mShakeMonitor;
    private boolean hasOpened = false;

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        outState.putBoolean(WISH_COVER_HAS_OPEN,hasOpened);
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            hasOpened = savedInstanceState.getBoolean(WISH_COVER_HAS_OPEN);
        }
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_list);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        mWishList.setLayoutManager(new LinearLayoutManager(WishListActivity.this));
        mWishList.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int i) {
                switch (mAdapter.getItemViewType(i)) {
                    case BaseHeaderAdapter.TYPE_DATA:
                        currentPosition = i;
                        PinnedHeaderEntity<WishInfoBean> entity = mAdapter.getData().get(i);
                        startActivity(new Intent(WishListActivity.this, WishDetailInfoActivity.class).putExtra(WishDetailInfoActivity.WISH_ID, entity.getData().getWish_id()));
                        break;
                    case BaseHeaderAdapter.TYPE_HEADER:
                        entity = mAdapter.getData().get(i);
                        break;
                }
            }

            @Override
            public void onItemLongClick(BaseQuickAdapter adapter, View view, int i) {
                switch (mAdapter.getItemViewType(i)) {
                    case BaseHeaderAdapter.TYPE_DATA:
                        PinnedHeaderEntity<WishInfoBean> entity = mAdapter.getData().get(i);
                        currentPosition = i;
                        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(WishListActivity.this);
                        commonMiddleDialog.setMessage(R.string.wish_detail_delete);
                        commonMiddleDialog.setSure(v -> {
                            delete();
                            commonMiddleDialog.dismiss();
                        });
                        commonMiddleDialog.setCancel(R.string.give_up);
                        commonMiddleDialog.show();
                        break;
                    case BaseHeaderAdapter.TYPE_HEADER:
                        entity = mAdapter.getData().get(i);
                        break;
                }
            }

        });
        mWishList.addItemDecoration(new PinnedHeaderItemDecoration.Builder(BaseHeaderAdapter.TYPE_HEADER).create());
        getInfo();
        cardWidth = CommonUtils.getScreenWidth() - CommonUtils.dip2px(this, 82);
        cardHeight = cardWidth * 5 / 8;
        initJewelryShake();
        mWishListCoverLv.setImageAssetsFolder("lottie_wish_list/");
        mWishListCoverLv.setAnimation("wish_list_open.json");
        if (hasOpened || ToTwooApplication.isDebug) {
            mWishListAllContent.setVisibility(View.VISIBLE);
            cardFontCover.setVisibility(View.GONE);
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(getString(R.string.wish_list_title));
    }

    private void getInfo() {
        HttpHelper.wishService.getWishList(currentPage, 10)
                .compose(HttpHelper.<HttpBaseBean<List<WishInfoHttp>>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<List<WishInfoHttp>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(WishListActivity.this,getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<List<WishInfoHttp>> wishListInfoHttpBaseBean) {
                        if (wishListInfoHttpBaseBean.getErrorCode() == 0 && wishListInfoHttpBaseBean.getData() != null) {
                            setRecyclerInfo(wishListInfoHttpBaseBean.getData());
                        }
                    }
                });
    }

    private void setRecyclerInfo(List<WishInfoHttp> years) {
        if (allWishBeans == null) {
            allWishBeans = new ArrayList<>();
        }
        for (WishInfoHttp wishInfoHttp : years) {
            totalCount = wishInfoHttp.getCount();
            if (wishInfoHttp.getInfo() != null && wishInfoHttp.getInfo().size() > 0) {
                if (!TextUtils.equals(wishInfoHttp.getYear(), lastYear)) {
                    allWishBeans.add(new PinnedHeaderEntity<>(new WishInfoBean(), BaseHeaderAdapter.TYPE_HEADER, wishInfoHttp.getYear()));
                    lastYear = wishInfoHttp.getYear();
                }
                for (WishInfoBean itemBean : wishInfoHttp.getInfo()) {
                    allWishBeans.add(new PinnedHeaderEntity<>(itemBean, BaseHeaderAdapter.TYPE_DATA, wishInfoHttp.getYear()));
                }
            }
        }
        setInfoToAdapter();
    }

    private void setInfoToAdapter() {
        if (mAdapter == null) {
            mAdapter = new BaseHeaderAdapter<PinnedHeaderEntity<WishInfoBean>>(allWishBeans) {

                @Override
                protected void addItemTypes() {
                    addItemType(BaseHeaderAdapter.TYPE_HEADER, R.layout.wish_list_header_item);
                    addItemType(BaseHeaderAdapter.TYPE_DATA, R.layout.wish_list_item);
                }

                @Override
                protected void convert(BaseViewHolder holder, final PinnedHeaderEntity<WishInfoBean> item) {
                    switch (holder.getItemViewType()) {
                        case BaseHeaderAdapter.TYPE_HEADER:
                            holder.setText(R.id.love_notify_header_tv, item.getPinnedHeaderName());
                            break;
                        case BaseHeaderAdapter.TYPE_DATA:
                            int position = holder.getLayoutPosition();
                            currentPosition = position;
                            long timeMillions = Long.valueOf(item.getData().getCreate_time()) * 1000;
                            holder.setText(R.id.wish_item_date_day, DateUtil.getStringDayByMillions(timeMillions));
                            int index = Integer.valueOf(DateUtil.getStringMonthByMillions(timeMillions)) - 1;
                            if (Apputils.systemLanguageIsChinese(WishListActivity.this)) {
                                holder.setText(R.id.wish_item_date_month, getResources().getStringArray(R.array.month_names)[index]);
                            } else {
                                holder.setText(R.id.wish_item_date_month, getResources().getStringArray(R.array.month_names_en)[index]);
                            }

                            if (isDateNeedShow(position)) {
                                holder.setVisible(R.id.wish_item_date_day, true);
                                holder.setVisible(R.id.wish_item_date_month, true);
                            } else {
                                holder.setVisible(R.id.wish_item_date_day, false);
                                holder.setVisible(R.id.wish_item_date_month, false);
                            }
//                            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(cardWidth, cardHeight);
//                            layoutParams.setMargins(CommonUtils.dip2px(WishListActivity.this, 68), CommonUtils.dip2px(WishListActivity.this, 10), CommonUtils.dip2px(WishListActivity.this, 14), 0);
//                            holder.getView(R.id.wish_item_content_cv).setLayoutParams(layoutParams);
                            holder.setText(R.id.wish_detail_like_count_tv, item.getData().getLike_count() + "");
                            if (item.getData().getIs_reach() == 0) {
                                holder.setVisible(R.id.wish_item_done_ll, false);
                            } else {
                                holder.setVisible(R.id.wish_item_done_ll, true);
                            }
                            TextView mInfoTv = holder.getView(R.id.wish_item_info_tv);
                            ImageView mContentIv = holder.getView(R.id.wish_item_content_iv);
                            ImageView mTopCornerIv = holder.getView(R.id.wish_item_top_corner_iv);
                            ImageView mControlIv = holder.getView(R.id.wish_item_control_iv);
                            switch (item.getData().getWish_type()) {
                                case CommonArgs.COMMON_SEND_TYPE_TEXT:
                                    mContentIv.setImageResource(R.drawable.wish_list_text_bg);
                                    mInfoTv.setText(item.getData().getContent());
                                    mInfoTv.setVisibility(View.VISIBLE);
                                    mControlIv.setVisibility(View.GONE);
                                    mTopCornerIv.setVisibility(View.VISIBLE);
                                    break;
                                case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                                    JSONArray jsonArray;
                                    String img_url = "";
                                    try {
                                        jsonArray = new JSONArray(item.getData().getImg_url());
                                        img_url = (String) jsonArray.get(0);
                                    } catch (JSONException e) {
                                        e.printStackTrace();
                                    }

                                    Glide.with(WishListActivity.this).load(img_url).into(mContentIv);
                                    mInfoTv.setVisibility(View.GONE);
                                    mControlIv.setVisibility(View.GONE);
                                    mTopCornerIv.setVisibility(View.GONE);
                                    break;
                                case CommonArgs.COMMON_SEND_TYPE_SOUND:
                                    mContentIv.setImageResource(R.drawable.wish_add_info_voice);
                                    mInfoTv.setVisibility(View.GONE);
                                    mControlIv.setVisibility(View.VISIBLE);
                                    mControlIv.setImageResource(R.drawable.wish_add_info_voice_play);
                                    mTopCornerIv.setVisibility(View.GONE);
                                    break;
                                case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                                    Glide.with(WishListActivity.this).load(item.getData().getCover_url()).into(mContentIv);
                                    mInfoTv.setVisibility(View.GONE);
                                    mControlIv.setVisibility(View.VISIBLE);
                                    mControlIv.setImageResource(R.drawable.wish_add_info_video_play);
                                    mTopCornerIv.setVisibility(View.GONE);
                                    break;
                            }

                            break;

                    }
                }
            };
            mWishList.setAdapter(mAdapter);
            mAdapter.setLoadMoreView(new MLoadMoreView());
            mAdapter.setOnLoadMoreListener(() -> {
                currentPage++;
                getInfo();
            }, mWishList);
        } else {
            mAdapter.notifyDataSetChanged();
        }
        mAdapter.loadMoreComplete();
        if (totalCount / 10 <= currentPage) {
            mAdapter.loadMoreEnd(true);
        }
    }

    private void delete() {
        HttpHelper.wishService.deleteWish(allWishBeans.get(currentPosition).getData().getWish_id())
                .compose(HttpHelper.<HttpBaseBean<Object>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        allWishBeans.remove(currentPosition);
                        mAdapter.notifyDataSetChanged();
                        if (allWishBeans.size() <= 1) {
                            EventBus.onPostReceived(S.E.E_WISH_DELETE_ALL, null);
                            finish();
                        }
                    }
                });
    }

    @OnClick({R.id.wish_show_close_cl})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.wish_show_close_cl:
                finish();
                break;
        }
    }

    private boolean isDateNeedShow(int position) {
        if (allWishBeans.get(position - 1).getItemType() == BaseHeaderAdapter.TYPE_HEADER) {
            return true;
        } else {
            return !DateUtil.isSameDay(Long.valueOf(allWishBeans.get(position).getData().getCreate_time()) * 1000, Long.valueOf(allWishBeans.get(position - 1).getData().getCreate_time()) * 1000);
        }
    }

    /**
     * 心愿编辑页删除成功
     * WishDetailInfoActivity
     */
    @EventInject(eventType = S.E.E_WISH_DELETE_SUCCESSED, runThread = TaskType.UI)
    public void deletePosition(EventData data) {
        allWishBeans.remove(currentPosition);
        if (allWishBeans.size() <= 1) {
            EventBus.onPostReceived(S.E.E_WISH_DELETE_ALL, null);
            finish();
        }
        mAdapter.notifyDataSetChanged();
    }

    /**
     * 心愿编辑页完成心愿
     * WishDetailInfoActivity
     */
    @EventInject(eventType = S.E.E_WISH_REACH_SUCCESSED, runThread = TaskType.UI)
    public void reachPosition(EventData data) {
        WishInfoBean infoBean = allWishBeans.get(currentPosition).getData();
        infoBean.setIs_reach(1);
        allWishBeans.set(currentPosition, new PinnedHeaderEntity<>(infoBean, BaseHeaderAdapter.TYPE_DATA, allWishBeans.get(currentPosition).getPinnedHeaderName()));
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    private void initJewelryShake() {
        mShakeMonitor = new ShakeMonitor(this);
        mShakeMonitor.isEnablePhoneShake(false);
        BluetoothManage.getInstance().connectedStatus();

        // 设置摇首饰的监听
        mShakeMonitor.setOnEventListener((type) -> {
            //弹出信纸
            changeGif();
            if (mShakeMonitor != null) {
                mShakeMonitor.stop();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mShakeMonitor != null)
            mShakeMonitor.start();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mShakeMonitor != null)
            mShakeMonitor.stop();
    }

    private void changeGif() {
        mWishListCoverLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                afterGif();
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mWishListCoverLv.playAnimation();
    }

    private void afterGif(){
        int height = CommonUtils.getScreenHeight();
        mWishListAllContent.setVisibility(View.VISIBLE);
        TranslateAnimation translateAnimation = new TranslateAnimation(0, 0, -height, 0);
        translateAnimation.setFillAfter(true);
        translateAnimation.setDuration(300);
        translateAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                hasOpened = true;
                cardFontCover.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mWishListAllContent.startAnimation(translateAnimation);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
    }
}
