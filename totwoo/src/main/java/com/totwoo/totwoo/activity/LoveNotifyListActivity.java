package com.totwoo.totwoo.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.LoveNotifyInfo;
import com.totwoo.totwoo.bean.LoveSpaceInfo;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.BaseHeaderAdapter;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderEntity;
import com.totwoo.totwoo.widget.StickRecyclerAdapter.PinnedHeaderItemDecoration;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class LoveNotifyListActivity extends BaseActivity {
    @BindView(R.id.love_notify_list_pair_info_tv)
    TextView mPairInfoTv;
    @BindView(R.id.love_notify_date_info_tv)
    TextView mDateInfoTv;
    @BindView(R.id.love_notify_list_rv)
    RecyclerView mRecyclerView;
    @BindView(R.id.love_notify_list_empty_iv)
    ImageView mEmptyIv;
    @BindView(R.id.love_notify_list_empty_tv)
    TextView mEmptyTv;

    private List<PinnedHeaderEntity<CustomItemBean>> data;
    private BaseHeaderAdapter<PinnedHeaderEntity<CustomItemBean>> mAdapter;
    private int currentPosition;
    private String gender;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_love_notify_list_rl);
        ButterKnife.bind(this);
        getMainInfo();
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        mRecyclerView.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int i) {
                switch (mAdapter.getItemViewType(i)) {
                    case BaseHeaderAdapter.TYPE_DATA:
                        PinnedHeaderEntity<CustomItemBean> entity = mAdapter.getData().get(i);
                        startActivity(new Intent(LoveNotifyListActivity.this, LoveNotifyEditActivity.class).putExtra("bean", entity.getData()));
                        break;
                    case BaseHeaderAdapter.TYPE_HEADER:
                        entity = mAdapter.getData().get(i);
                        break;
                }
            }

            @Override
            public void onItemLongClick(BaseQuickAdapter adapter, View view, int i) {
                switch (mAdapter.getItemViewType(i)) {
                    case BaseHeaderAdapter.TYPE_DATA:
                        PinnedHeaderEntity<CustomItemBean> entity = mAdapter.getData().get(i);
                        currentPosition = i;
                        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(LoveNotifyListActivity.this);
                        commonMiddleDialog.setMessage(R.string.custom_notify_list_delete_hint);
                        commonMiddleDialog.setSure(v -> {
                            delete();
                            commonMiddleDialog.dismiss();
                        });
                        commonMiddleDialog.setCancel(R.string.cancel);
                        commonMiddleDialog.show();
                        break;
                    case BaseHeaderAdapter.TYPE_HEADER:
                        entity = mAdapter.getData().get(i);
                        break;
                }
            }

        });
        mRecyclerView.addItemDecoration(new PinnedHeaderItemDecoration.Builder(BaseHeaderAdapter.TYPE_HEADER).create());
    }

    @Override
    protected void onResume() {
        super.onResume();
        getInfo();
    }

    private void getMainInfo() {
        HttpHelper.commonService.getSpaceInfo(ToTwooApplication.owner.getPairedId())
                .compose(HttpHelper.<HttpBaseBean<LoveSpaceInfo>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<LoveSpaceInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoveNotifyListActivity.this, R.string.error_net);
                    }

                    @SuppressLint("SetTextI18n")
                    @Override
                    public void onNext(HttpBaseBean<LoveSpaceInfo> loveSpaceInfoHttpBaseBean) {
                        if (loveSpaceInfoHttpBaseBean.getErrorCode() == 0) {
                            mDateInfoTv.setText(loveSpaceInfoHttpBaseBean.getData().getTogether_data());
                            mPairInfoTv.setText(CommonUtils.getFormatDate(loveSpaceInfoHttpBaseBean.getData().getFirst_couple(), Apputils.systemLanguageIsChinese(LoveNotifyListActivity.this)));
                            gender = loveSpaceInfoHttpBaseBean.getData().getUserinfo().getTarget().getSex();
                        }
                    }
                });
    }

    private void getInfo() {
        HttpHelper.customService.getLoveList(ToTwooApplication.owner.getPairedId(), ToTwooApplication.otherPhone)
                .compose(HttpHelper.<HttpBaseBean<List<LoveNotifyInfo>>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<List<LoveNotifyInfo>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoveNotifyListActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<List<LoveNotifyInfo>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            setRecyclerInfo(listHttpBaseBean.getData());
                        }
                    }
                });
    }

    @Override
    protected void initTopBar() {
        setTopLeftIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.love_space_list_title);
        setTopbarBackground(R.color.background_bg);
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
        setTopLeftOnclik(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        setTopRightIcon(R.drawable.custom_list_add);
        setTopRightOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.REMINDLIST_PUSH_ADDLOVEREMIND);
                startActivity(new Intent(LoveNotifyListActivity.this, AddLoveNotifyActivity.class));
            }
        });
    }

    private void setRecyclerInfo(List<LoveNotifyInfo> years) {
        if (years == null || years.size() == 0) {
            mEmptyTv.setVisibility(View.VISIBLE);
            mEmptyIv.setVisibility(View.VISIBLE);
            mRecyclerView.setVisibility(View.GONE);
            return;
        }
        mRecyclerView.setVisibility(View.VISIBLE);
        mEmptyTv.setVisibility(View.GONE);
        mEmptyIv.setVisibility(View.GONE);
        if (data != null) {
            data.clear();
        } else {
            data = new ArrayList<>();
        }
        for (LoveNotifyInfo loveNotifyInfo : years) {
            if (loveNotifyInfo.getInfo() != null && loveNotifyInfo.getInfo().size() > 0) {
                data.add(new PinnedHeaderEntity<>(new CustomItemBean(), BaseHeaderAdapter.TYPE_HEADER, loveNotifyInfo.getYear()));
                for (CustomItemBean itemBean : loveNotifyInfo.getInfo()) {
                    data.add(new PinnedHeaderEntity<>(itemBean, BaseHeaderAdapter.TYPE_DATA, loveNotifyInfo.getYear()));
                }
            }
        }
        setInfoToAdapter();
    }

    private void delete() {
        HttpHelper.customService.deleteCustom(data.get(currentPosition).getData().getDefine_id(), ToTwooApplication.otherPhone)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoveNotifyListActivity.this, R.string.custom_notify_list_delete_fail);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            deleteSuccess();
                        }
                    }
                });

    }

    private void deleteSuccess() {
//        CustomNotifyDbHelper.getInstance().deleteBean(data.get(currentPosition).getData().getDefine_id());
        getInfo();
    }

    private void setInfoToAdapter() {
        if (mAdapter == null) {
            mAdapter = new BaseHeaderAdapter<PinnedHeaderEntity<CustomItemBean>>(data) {

                @Override
                protected void addItemTypes() {
                    addItemType(BaseHeaderAdapter.TYPE_HEADER, R.layout.love_list_header_item);
                    addItemType(BaseHeaderAdapter.TYPE_DATA, R.layout.love_notify_item);
                }

                @Override
                protected void convert(BaseViewHolder holder, final PinnedHeaderEntity<CustomItemBean> item) {
                    switch (holder.getItemViewType()) {
                        case BaseHeaderAdapter.TYPE_HEADER:
                            holder.setText(R.id.love_notify_header_tv, item.getPinnedHeaderName());
                            break;
                        case BaseHeaderAdapter.TYPE_DATA:

                            int position = holder.getLayoutPosition();

                            String[] birth = item.getData().getDefine_time().split("-");
                            holder.setText(R.id.love_list_item_date, birth[1] + "/" + birth[2]);
                            holder.setText(R.id.love_list_item_title, item.getData().getTitle());

//                            if () {
                            setDateLayout(TextUtils.equals(item.getData().getCouple(), "my"), holder);
//                            } else {
//                                setDateLayout(TextUtils.equals(gender, "1"), holder);
//                            }

                            if (BleParams.isButtonBatteryJewelry()) {
                                holder.setVisible(R.id.love_list_item_virbation, false);
                            } else {
                                holder.setVisible(R.id.love_list_item_virbation, true);

                                if (TextUtils.equals(item.getData().getShock_type(), "short")) {
                                    holder.setImageResource(R.id.love_list_item_virbation, R.drawable.remind_short_fill);
                                } else {
                                    holder.setImageResource(R.id.love_list_item_virbation, R.drawable.remind_long);
                                }
                            }

                            setSelectColor(holder.getView(R.id.love_list_item_light),item.getData().getNotify_mode());
                            if (item.getData().getIs_open() == 1) {
                                holder.setVisible(R.id.love_list_item_light, true);
                                holder.setVisible(R.id.love_list_item_virbation, true);
                                holder.setVisible(R.id.love_list_item_close, false);
                            } else {
                                holder.setVisible(R.id.love_list_item_light, false);
                                holder.setVisible(R.id.love_list_item_virbation, false);
                                holder.setVisible(R.id.love_list_item_close, true);
                                holder.setText(R.id.love_list_item_close, R.string.notify_off);
                            }

                            break;

                    }
                }

                private void setSelectColor(ImageView imageView, String colorName) {
                    int resId = NotifyUtil.getColorImageResId(colorName);
                    imageView.setImageResource(resId);
                    if (resId == R.drawable.custom_color_normal) {
                        imageView.setColorFilter(Color.parseColor(NotifyUtil.getDisplayColorByColorName(colorName)));
                    }else {
                        imageView.setColorFilter(null);
                    }
                }

                private void setDateLayout(boolean isFemale, BaseViewHolder holder) {
                    if (isFemale) {
                        holder.setBackgroundRes(R.id.love_notify_date_ll, R.drawable.shape_love_notify_corner_pink);
                        holder.setImageResource(R.id.love_list_item_iv, R.drawable.love_notify_date_pink);
                    } else {
                        holder.setBackgroundRes(R.id.love_notify_date_ll, R.drawable.shape_love_notify_corner);
                        holder.setImageResource(R.id.love_list_item_iv, R.drawable.love_notify_date_green);
                    }
                }
            };
            mRecyclerView.setAdapter(mAdapter);
        } else {
            mAdapter.notifyDataSetChanged();
        }
    }
}
