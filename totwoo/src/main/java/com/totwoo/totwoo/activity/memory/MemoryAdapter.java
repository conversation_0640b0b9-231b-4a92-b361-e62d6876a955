package com.totwoo.totwoo.activity.memory;

import android.content.Context;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.AdapterView;
import com.etone.framework.annotation.ViewInject;
import com.etone.framework.base.BaseArrayListAdapter;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.utils.DateUtil;

import java.util.ArrayList;

/**
 * Created by xinyoulingxi on 2017/8/2.
 */

@AdapterView(R.layout.activity_memory_list_item)
public class MemoryAdapter extends BaseArrayListAdapter<MemoryBean>
{
    private Context context;
    private final SpannableStringBuilder sb = new SpannableStringBuilder("your text here");

    public MemoryAdapter(Context context, ArrayList<MemoryBean> resource)
    {
        super(context, resource, false);
        this.context = context;
    }

    @Override
    public void initHolderData(BaseHolder _holder, int position, MemoryBean item)
    {
        Holder holder = (Holder) _holder;
        holder.day.setText(DateUtil.getDateToString("yyyy/MM/dd", item.create_time));
        holder.bg.setVisibility(View.GONE);
        holder.vedioStart.setVisibility(View.GONE);
        holder.text.setVisibility(View.GONE);
        holder.textLine.setVisibility(View.GONE);
        //BitmapHelper.display(context, holder.bg, item.cover_url, item.memory_type == MemoryBean.TYPE_VED ? R.drawable.memory_set_list1 : R.drawable.memory_set_list);
        BitmapHelper.display(context, holder.bg, item.cover_url, R.drawable.memory_set_list);
        switch (item.memory_type)
        {
            case MemoryBean.TYPE_SAY:
                holder.text.setVisibility(View.VISIBLE);
                holder.textLine.setVisibility(View.VISIBLE);
                String res = item.content;
                if (res.length() > 50)
                    res = res.substring(0, 49) + "...";
                SpannableString styledText = new SpannableString(res);
                try
                {
                    styledText.setSpan(new TextAppearanceSpan(context, R.style.memory_text_style_big), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                    styledText.setSpan(new TextAppearanceSpan(context, R.style.memory_text_style_small), 1, res.length()-1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
                holder.text.setText(styledText, TextView.BufferType.SPANNABLE);
                break;
            case MemoryBean.TYPE_IMG:
                holder.bg.setVisibility(View.VISIBLE);
                break;
            case MemoryBean.TYPE_VED:
                holder.bg.setVisibility(View.VISIBLE);
                holder.vedioStart.setVisibility(View.VISIBLE);
                holder.vedioStart.setImageResource(R.drawable.vedio_list_play);
                break;
            case MemoryBean.TYPE_AUD:
                holder.bg.setVisibility(View.VISIBLE);
                holder.vedioStart.setVisibility(View.VISIBLE);
                holder.vedioStart.setImageResource(R.drawable.voice_list_play);
                break;
            default:
                break;
        }
    }

    public static class Holder implements BaseHolder
    {
        @ViewInject(R.id.memory_item_day)
        public TextView day;
        @ViewInject (R.id.memory_item_bg)
        public ImageView bg;
        @ViewInject (R.id.memory_item_vedio_start)
        public ImageView vedioStart;
        @ViewInject (R.id.memory_item_text)
        public TextView text;
        @ViewInject (R.id.memory_item_text_line)
        public ImageView textLine;
    }
}
