package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.text.TextPaint;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.SleepDayData;
import com.totwoo.totwoo.bean.SleepStateBean;
import com.totwoo.totwoo.bean.SleepUpdateBean;
import com.totwoo.totwoo.bean.SleepWeekBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SleepCalculateUtil;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.IMHintDialog;
import com.totwoo.totwoo.widget.SleepDayView;
import com.totwoo.totwoo.widget.SleepDayViewGroup;
import com.totwoo.totwoo.widget.SleepWeekView;
import com.umeng.analytics.MobclickAgent;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;

public class SleepStatisticActivity extends BaseActivity implements SleepDayView.OnTouchItemListener {
    @BindView(R.id.sleep_day_tv)
    TextView mDayTitle;
    @BindView(R.id.sleep_week_tv)
    TextView mWeekTitle;
    @BindView(R.id.sleep_month_tv)
    TextView mMonthTitle;
    //    @BindView(R.id.sleep_day_vp2)
//    ViewPager2 mSleepDayVp;
    @BindView(R.id.sleep_day_view)
    LinearLayout mSleepDayView;
    @BindView(R.id.sleep_left_iv)
    ImageView mLeftClickIv;
    @BindView(R.id.sleep_right_iv)
    ImageView mRightClickIv;
    @BindView(R.id.sleep_date_tv)
    TextView mDateTv;
    @BindView(R.id.sleep_week_sv)
    ScrollView mWeekSv;
    @BindView(R.id.sleep_week_avg_hour_tv)
    TextView mWeekAvgHourTv;
    @BindView(R.id.sleep_week_avg_hour_text)
    TextView mWeekAvgHourText;
    @BindView(R.id.sleep_week_avg_min_tv)
    TextView mWeekAvgMinTv;
    @BindView(R.id.sleep_week_avg_min_text)
    TextView mWeekAvgMinText;
    @BindView(R.id.sleep_week_deep_percent)
    TextView mWeekDeepPercent;
    @BindView(R.id.sleep_week_deep_hour_tv)
    TextView mWeekDeepHourTv;
    @BindView(R.id.sleep_week_deep_hour_text)
    TextView mWeekDeepHourText;
    @BindView(R.id.sleep_week_deep_min_tv)
    TextView mWeekDeepMinTv;
    @BindView(R.id.sleep_week_deep_min_text)
    TextView mWeekDeepMinText;
    @BindView(R.id.sleep_week_light_percent)
    TextView mWeekLightPercent;
    @BindView(R.id.sleep_week_light_hour_tv)
    TextView mWeekLightHourTv;
    @BindView(R.id.sleep_week_light_hour_text)
    TextView mWeekLightHourText;
    @BindView(R.id.sleep_week_light_min_tv)
    TextView mWeekLightMinTv;
    @BindView(R.id.sleep_week_light_min_text)
    TextView mWeekLightMinText;
    @BindView(R.id.sleep_week_0_tv)
    TextView mWeekTv0;
    @BindView(R.id.sleep_week_1_tv)
    TextView mWeekTv1;
    @BindView(R.id.sleep_week_2_tv)
    TextView mWeekTv2;
    @BindView(R.id.sleep_week_3_tv)
    TextView mWeekTv3;

    @BindView(R.id.sleep_month_sv)
    ScrollView mMonthSv;
    @BindView(R.id.sleep_month_avg_hour_tv)
    TextView mMonthAvgHourTv;
    @BindView(R.id.sleep_month_avg_hour_text)
    TextView mMonthAvgHourText;
    @BindView(R.id.sleep_month_avg_min_tv)
    TextView mMonthAvgMinTv;
    @BindView(R.id.sleep_month_avg_min_text)
    TextView mMonthAvgMinText;
    @BindView(R.id.sleep_month_deep_percent)
    TextView mMonthDeepPercent;
    @BindView(R.id.sleep_month_deep_hour_tv)
    TextView mMonthDeepHourTv;
    @BindView(R.id.sleep_month_deep_hour_text)
    TextView mMonthDeepHourText;
    @BindView(R.id.sleep_month_deep_min_tv)
    TextView mMonthDeepMinTv;
    @BindView(R.id.sleep_month_deep_min_text)
    TextView mMonthDeepMinText;
    @BindView(R.id.sleep_month_light_percent)
    TextView mMonthLightPercent;
    @BindView(R.id.sleep_month_light_hour_tv)
    TextView mMonthLightHourTv;
    @BindView(R.id.sleep_month_light_hour_text)
    TextView mMonthLightHourText;
    @BindView(R.id.sleep_month_light_min_tv)
    TextView mMonthLightMinTv;
    @BindView(R.id.sleep_month_light_min_text)
    TextView mMonthLightMinText;
    @BindView(R.id.sleep_month_0_tv)
    TextView mMonthTv0;
    @BindView(R.id.sleep_month_1_tv)
    TextView mMonthTv1;
    @BindView(R.id.sleep_month_2_tv)
    TextView mMonthTv2;
    @BindView(R.id.sleep_month_3_tv)
    TextView mMonthTv3;

    @BindView(R.id.sleep_month_state_info)
    LinearLayout mMonthStateInfoLl;
    @BindView(R.id.sleep_month_none_tv)
    TextView mMonthNoneTv;
    @BindView(R.id.sleep_month_light_info_cl)
    ConstraintLayout mMonthLightInfo;
    @BindView(R.id.sleep_month_deep_info_cl)
    ConstraintLayout mMonthDeepInfo;
    @BindView(R.id.sleep_month_avg_info_ll)
    LinearLayout mMonthAvgInfo;

    @BindView(R.id.sleep_week_state_info)
    LinearLayout mWeekStateInfoLl;
    @BindView(R.id.sleep_week_none_tv)
    TextView mWeekNoneTv;
    @BindView(R.id.sleep_week_light_info_cl)
    ConstraintLayout mWeekLightInfo;
    @BindView(R.id.sleep_week_deep_info_cl)
    ConstraintLayout mWeekDeepInfo;
    @BindView(R.id.sleep_week_avg_info_ll)
    LinearLayout mWeekAvgInfo;

//    private ArrayList<SleepDayData> daysInfo;
//    private DayViewpagerAdapter dayViewpagerAdapter;

    private ArrayList<SleepWeekBean> weekBeans;
    private int maxWeekSleep;

    private ArrayList<SleepWeekBean> monthBeans;
    private int maxMonthSleep;

    private SleepDayData currentSleepDayData;

    private static final int TYPE_DAY = 0;
    private static final int TYPE_WEEK = 1;
    private static final int TYPE_MONTH = 2;
    private int currentSelect = TYPE_DAY;
    //    private int currentVpPosition = 0;
    private int currentDayPosition = 0;
    private int currentWeekPosition = 0;
    private int currentMonthPosition = 0;
    private String tempDay;
    private String tempWeek;
    private long weekTime = 604800000;
    private long dayTime = 86400000;

    private SleepWeekView sleepWeekView;
    private SleepWeekView sleepWeekView1;
    private SleepWeekView sleepWeekView2;
    private SleepWeekView sleepWeekView3;

    private SleepWeekView sleepMonthView;
    private SleepWeekView sleepMonthView1;
    private SleepWeekView sleepMonthView2;
    private SleepWeekView sleepMonthView3;

    @BindView(R.id.sleep_day_timeline_ll)
    LinearLayout mTimeLineLL;

    @BindView(R.id.sleep_day_state_info_cl)
    ConstraintLayout mStateInfoCl;

    @BindView(R.id.sleep_day_pre_tv)
    TextView mDayPreTv;

    @BindView(R.id.sleep_day_hour_tv)
    TextView mDayHourTv;

    @BindView(R.id.sleep_day_hour_text)
    TextView mDayHourText;

    @BindView(R.id.sleep_day_min_tv)
    TextView mDayMinTv;

    @BindView(R.id.sleep_day_min_text)
    TextView mDayMinText;

    @BindView(R.id.sleep_day_deep_hour_tv)
    TextView mDeepHourTv;

    @BindView(R.id.sleep_day_deep_hour_text)
    TextView mDeepHourText;

    @BindView(R.id.sleep_day_deep_min_tv)
    TextView mDeepMinTv;

    @BindView(R.id.sleep_day_deep_min_text)
    TextView mDeepMinText;

    @BindView(R.id.sleep_day_light_hour_tv)
    TextView mLightHourTv;

    @BindView(R.id.sleep_day_light_hour_text)
    TextView mLightHourText;

    @BindView(R.id.sleep_day_light_min_tv)
    TextView mLightMinTv;

    @BindView(R.id.sleep_day_light_min_text)
    TextView mLightMinText;

    @BindView(R.id.sleep_day_total_tv)
    TextView mTotalTv;

    @BindView(R.id.day_deep_sleep_tv)
    TextView mDeepSleepPercent;

    @BindView(R.id.day_light_sleep_tv)
    TextView mLightSleepPercent;

    @BindView(R.id.sleep_item_start_tv)
    TextView mStartTv;

    @BindView(R.id.sleep_item_end_tv)
    TextView mEndTv;

    @BindView(R.id.sleep_assessment_tv)
    TextView mAssessmentTv;

    @BindView(R.id.sleep_assessment_text)
    TextView mAssessmentText;

    @BindView(R.id.sleep_assessment_line)
    View mAssessmentLine;

    @BindView(R.id.sleep_day_awake_cl)
    ConstraintLayout mAwakeCl;

    @BindView(R.id.sleep_day_awake_tv)
    TextView mAwakeTv;

    @BindView(R.id.sleep_day_light_cl)
    ConstraintLayout mLightCl;

    @BindView(R.id.sleep_day_deep_cl)
    ConstraintLayout mDeepCl;

    @BindView(R.id.sleep_day_total_ll)
    LinearLayout mDayTotalLl;

    @BindView(R.id.sleep_day_no_data)
    TextView mDayNoData;

    private HashMap<String, SleepDayData> dayInfoMap;

    private static SimpleDateFormat ymdFormat = new SimpleDateFormat("yyyy-MM-dd");

    private IMHintDialog imHintDialog;
    private static String IS_SLEEP_HINT_SHOW = "IS_SLEEP_HINT_SHOW";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sleep_statistic);
        ButterKnife.bind(this);

//        daysInfo = new ArrayList<>();
//        daysInfo.add(new SleepDayData());

//        getDayData(System.currentTimeMillis() - dayTime);

//        dayViewpagerAdapter = new DayViewpagerAdapter();
//        mSleepDayVp.setAdapter(dayViewpagerAdapter);
//        mSleepDayVp.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
//            @Override
//            public void onPageSelected(int position) {
//                currentVpPosition = position;
//                if (position == daysInfo.size() - 1) {
//                    mRightClickIv.setImageResource(R.drawable.sleep_right_disable);
//                } else {
//                    mRightClickIv.setImageResource(R.drawable.sleep_right_enable);
//                }
//                long millions = System.currentTimeMillis() - (daysInfo.size() - position - 1) * dayTime;
//                tempDay = CommonUtils.getFormatDate(millions, Apputils.systemLanguageIsChinese(SleepStatisticActivity.this));
//                mDateTv.setText(tempDay);
////                if (position == 0) {
////                    getDayData((System.currentTimeMillis() - daysInfo.size() * dayTime) / 1000);
////                }
//            }
//        });

        dayInfoMap = new HashMap<>();
        getDayData(System.currentTimeMillis());
        initDayData();

        initWeekData();
        getWeekData();
        initMonthData();
        getMonthData();

        if (!PreferencesUtils.getBoolean(SleepStatisticActivity.this, IS_SLEEP_HINT_SHOW, false)) {
            imHintDialog = new IMHintDialog(SleepStatisticActivity.this, v -> imHintDialog.dismiss(), getString(R.string.sleep_title), getString(R.string.sleep_paragraph1), getString(R.string.sleep_paragraph2),
                    getString(R.string.sleep_paragraph3), getString(R.string.sleep_paragraph4));
            PreferencesUtils.put(SleepStatisticActivity.this, IS_SLEEP_HINT_SHOW, true);
            imHintDialog.show();
        }
    }

    private SleepDayViewGroup sleepDayViewGroup;
    private ConstraintLayout.LayoutParams dayViewGroupLayout;
    private LinearLayout.LayoutParams dayViewLayout;

    private void initDayData() {
        sleepDayViewGroup = new SleepDayViewGroup(SleepStatisticActivity.this);
        dayViewGroupLayout = new ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT);
        dayViewLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.MATCH_PARENT);
        LinearLayout.LayoutParams lineLayout = new LinearLayout.LayoutParams(CommonUtils.dip2px(SleepStatisticActivity.this, 1), LinearLayout.LayoutParams.MATCH_PARENT);

        LinearLayout.LayoutParams spaceLayout = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
        spaceLayout.weight = 1;

        for (int i = 0; i < 12; i++) {
            View line = new View(SleepStatisticActivity.this);
            line.setBackgroundColor(getResources().getColor(R.color.text_color_gray_ee));
            mTimeLineLL.addView(line, lineLayout);
            View space = new View(SleepStatisticActivity.this);
            mTimeLineLL.addView(space, spaceLayout);
        }

        View line = new View(SleepStatisticActivity.this);
        line.setBackgroundColor(getResources().getColor(R.color.text_color_gray_ee));
        mTimeLineLL.addView(line, lineLayout);

        mStateInfoCl.addView(sleepDayViewGroup, dayViewGroupLayout);
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
        setTopTitle(R.string.sleep_record);
        setTopRightIcon(R.drawable.icon_info_black);
        setTopRightOnClick(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MORSLEEP_DETAIL_RIGHTDESC);
            WebViewActivity.loadUrl(SleepStatisticActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_SLEEP_INFO), false);
        });
    }

    @OnClick({R.id.sleep_day_tv, R.id.sleep_week_tv, R.id.sleep_month_tv, R.id.sleep_left_iv, R.id.sleep_right_iv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.sleep_day_tv:
                currentSelect = TYPE_DAY;
                updateTopView();
                updateTopDateAndClick();
                break;
            case R.id.sleep_week_tv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MORSLEEP_DETAIL_CHANGETOWEEK);
                currentSelect = TYPE_WEEK;
                updateTopView();
                updateTopDateAndClick();
                break;
            case R.id.sleep_month_tv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MORSLEEP_DETAIL_CHANGETOMONTH);
                currentSelect = TYPE_MONTH;
                updateTopView();
                updateTopDateAndClick();
                break;
            case R.id.sleep_left_iv:
                clickLeft();
                break;
            case R.id.sleep_right_iv:
                clickRight();
                break;
        }
    }

    private void updateTopView() {
        switch (currentSelect) {
            case TYPE_DAY:
                setSelectTop(mDayTitle);
                mSleepDayView.setVisibility(View.VISIBLE);
                setUnselectTop(mWeekTitle);
                mWeekSv.setVisibility(View.GONE);
                setUnselectTop(mMonthTitle);
                mMonthSv.setVisibility(View.GONE);
                break;
            case TYPE_WEEK:
                setUnselectTop(mDayTitle);
                mSleepDayView.setVisibility(View.GONE);
                setSelectTop(mWeekTitle);
                mWeekSv.setVisibility(View.VISIBLE);
                setUnselectTop(mMonthTitle);
                mMonthSv.setVisibility(View.GONE);
                break;
            case TYPE_MONTH:
                setUnselectTop(mDayTitle);
                mSleepDayView.setVisibility(View.GONE);
                setUnselectTop(mWeekTitle);
                mWeekSv.setVisibility(View.GONE);
                setSelectTop(mMonthTitle);
                mMonthSv.setVisibility(View.VISIBLE);
                break;
        }
    }

    private void updateTopDateAndClick() {
        switch (currentSelect) {
            case TYPE_DAY:
//                if (currentVpPosition == daysInfo.size() - 1) {
//                    mRightClickIv.setImageResource(R.drawable.sleep_right_disable);
//                } else {
//                    mRightClickIv.setImageResource(R.drawable.sleep_right_enable);
//                }
                if (currentDayPosition == 0) {
                    mRightClickIv.setImageResource(R.drawable.sleep_right_disable);
                } else {
                    mRightClickIv.setImageResource(R.drawable.sleep_right_enable);
                }
                mDateTv.setText(tempDay);
                break;
            case TYPE_WEEK:
                long diffMillions = currentWeekPosition * weekTime;
                long targetMillions = System.currentTimeMillis() + diffMillions;
                tempWeek = CommonUtils.getFormatDate(DateUtil.getWeekStart(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this))
                        + " - " + CommonUtils.getFormatDate(DateUtil.getWeekEnd(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this));
                mDateTv.setText(tempWeek);
                if (currentWeekPosition >= -3) {
                    mRightClickIv.setImageResource(R.drawable.sleep_right_disable);
                } else {
                    mRightClickIv.setImageResource(R.drawable.sleep_right_enable);
                }
                break;
            case TYPE_MONTH:
                mDateTv.setText(getYearAndMonth(currentMonthPosition));
                if (currentMonthPosition >= -3) {
                    mRightClickIv.setImageResource(R.drawable.sleep_right_disable);
                } else {
                    mRightClickIv.setImageResource(R.drawable.sleep_right_enable);
                }
                break;
        }
    }

    private void setSelectTop(TextView textView) {
        textView.setTextColor(getResources().getColor(R.color.color_main));
        textView.setBackground(getResources().getDrawable(R.drawable.shape_sleep_top_select_bg));
        textView.setTextSize(17);
        TextPaint paint = textView.getPaint();
        paint.setFakeBoldText(true);
    }

    private void setUnselectTop(TextView textView) {
        textView.setTextColor(getResources().getColor(R.color.text_color_gray_7a));
        textView.setBackground(getResources().getDrawable(R.drawable.shape_sleep_top_unselect_bg));
        textView.setTextSize(15);
        TextPaint paint = textView.getPaint();
        paint.setFakeBoldText(false);
    }

    private void clickLeft() {
        if (currentSelect == TYPE_DAY) {
//            int currentItem = mSleepDayVp.getCurrentItem();
//            if (currentItem != 0) {
//                mSleepDayVp.setCurrentItem(currentItem - 1);
//            }
            currentDayPosition--;
            if (currentDayPosition == 0) {
                mRightClickIv.setImageResource(R.drawable.sleep_right_disable);
            } else {
                mRightClickIv.setImageResource(R.drawable.sleep_right_enable);
            }
            getDayData(System.currentTimeMillis() + currentDayPosition * dayTime);
        } else if (currentSelect == TYPE_WEEK) {
            currentWeekPosition -= 4;
            updateTopDateAndClick();
            getWeekData();
        } else if (currentSelect == TYPE_MONTH) {
            currentMonthPosition -= 4;
            updateTopDateAndClick();
            getMonthData();
        }
    }

    private void clickRight() {
        if (currentSelect == TYPE_DAY) {
//            int currentItem = mSleepDayVp.getCurrentItem();
//            if (currentItem != daysInfo.size() - 1) {
//                mSleepDayVp.setCurrentItem(currentItem + 1);
//            }
            if (currentDayPosition == 0) {
                return;
            }
            currentDayPosition++;
            if (currentDayPosition == 0) {
                mRightClickIv.setImageResource(R.drawable.sleep_right_disable);
            } else {
                mRightClickIv.setImageResource(R.drawable.sleep_right_enable);
            }
            getDayData(System.currentTimeMillis() + currentDayPosition * dayTime);
        } else if (currentSelect == TYPE_WEEK) {
            if (currentWeekPosition < -3) {
                currentWeekPosition += 4;
                updateTopDateAndClick();
                getWeekData();
            }
        } else if (currentSelect == TYPE_MONTH) {
            if (currentMonthPosition < -3) {
                currentMonthPosition += 4;
                updateTopDateAndClick();
                getMonthData();
            }
        }
    }

    private void getDayData(long current_time) {
        tempDay = CommonUtils.getFormatDate(current_time, Apputils.systemLanguageIsChinese(SleepStatisticActivity.this));
        mDateTv.setText(tempDay);

        if (dayInfoMap.get(ymdFormat.format(new Date(current_time))) != null) {
            setDayData(dayInfoMap.get(ymdFormat.format(new Date(current_time))));
        } else {
            HttpHelper.sleepDataService.getDayData(current_time / 1000, "my", "")
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Observer<HttpBaseBean<SleepDayData>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<SleepDayData> sleepDayDataHttpBaseBean) {
                            if (sleepDayDataHttpBaseBean.getErrorCode() == 0) {
//                            daysInfo.set(0, sleepDayDataHttpBaseBean.getData());
//                            dayViewpagerAdapter.notifyDataSetChanged();
                                currentSleepDayData = sleepDayDataHttpBaseBean.getData();
                                dayInfoMap.put(ymdFormat.format(new Date(current_time)), currentSleepDayData);
                                setDayData(currentSleepDayData);
                            }
                        }
                    });
        }

    }

    private void initWeekData() {
        LinearLayout.LayoutParams weekViewLayout = new LinearLayout.LayoutParams(CommonUtils.dip2px(SleepStatisticActivity.this, 43), LinearLayout.LayoutParams.MATCH_PARENT);
        weekViewLayout.gravity = Gravity.BOTTOM;

        LinearLayout.LayoutParams spaceLayout = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
        spaceLayout.weight = 1;

        sleepWeekView = new SleepWeekView(SleepStatisticActivity.this);
        mWeekStateInfoLl.addView(sleepWeekView, weekViewLayout);

        View space = new View(SleepStatisticActivity.this);
        mWeekStateInfoLl.addView(space, spaceLayout);

        sleepWeekView1 = new SleepWeekView(SleepStatisticActivity.this);
        mWeekStateInfoLl.addView(sleepWeekView1, weekViewLayout);

        View space1 = new View(SleepStatisticActivity.this);
        mWeekStateInfoLl.addView(space1, spaceLayout);

        sleepWeekView2 = new SleepWeekView(SleepStatisticActivity.this);
        mWeekStateInfoLl.addView(sleepWeekView2, weekViewLayout);

        View space2 = new View(SleepStatisticActivity.this);
        mWeekStateInfoLl.addView(space2, spaceLayout);

        sleepWeekView3 = new SleepWeekView(SleepStatisticActivity.this);
        mWeekStateInfoLl.addView(sleepWeekView3, weekViewLayout);

        sleepWeekView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setWeekClick(0);
                long diffMillions = currentWeekPosition * weekTime - 3 * weekTime;
                long targetMillions = System.currentTimeMillis() + diffMillions;
                tempWeek = CommonUtils.getFormatDate(DateUtil.getWeekStart(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this))
                        + " - " + CommonUtils.getFormatDate(DateUtil.getWeekEnd(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this));
                mDateTv.setText(tempWeek);
            }
        });
        sleepWeekView1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setWeekClick(1);
                long diffMillions = currentWeekPosition * weekTime - 2 * weekTime;
                long targetMillions = System.currentTimeMillis() + diffMillions;
                tempWeek = CommonUtils.getFormatDate(DateUtil.getWeekStart(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this))
                        + " - " + CommonUtils.getFormatDate(DateUtil.getWeekEnd(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this));
                mDateTv.setText(tempWeek);
            }
        });
        sleepWeekView2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                long diffMillions = currentWeekPosition * weekTime - weekTime;
                long targetMillions = System.currentTimeMillis() + diffMillions;
                tempWeek = CommonUtils.getFormatDate(DateUtil.getWeekStart(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this))
                        + " - " + CommonUtils.getFormatDate(DateUtil.getWeekEnd(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this));
                mDateTv.setText(tempWeek);
                setWeekClick(2);
            }
        });
        sleepWeekView3.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                long diffMillions = currentWeekPosition * weekTime;
                long targetMillions = System.currentTimeMillis() + diffMillions;
                tempWeek = CommonUtils.getFormatDate(DateUtil.getWeekStart(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this))
                        + " - " + CommonUtils.getFormatDate(DateUtil.getWeekEnd(targetMillions), Apputils.systemLanguageIsChinese(SleepStatisticActivity.this));
                mDateTv.setText(tempWeek);
                setWeekClick(3);
            }
        });
    }

    private void initMonthData() {
        LinearLayout.LayoutParams weekViewLayout = new LinearLayout.LayoutParams(CommonUtils.dip2px(SleepStatisticActivity.this, 43), LinearLayout.LayoutParams.MATCH_PARENT);
        weekViewLayout.gravity = Gravity.BOTTOM;

        LinearLayout.LayoutParams spaceLayout = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
        spaceLayout.weight = 1;

        sleepMonthView = new SleepWeekView(SleepStatisticActivity.this);
        mMonthStateInfoLl.addView(sleepMonthView, weekViewLayout);

        View space = new View(SleepStatisticActivity.this);
        mMonthStateInfoLl.addView(space, spaceLayout);

        sleepMonthView1 = new SleepWeekView(SleepStatisticActivity.this);
        mMonthStateInfoLl.addView(sleepMonthView1, weekViewLayout);

        View space1 = new View(SleepStatisticActivity.this);
        mMonthStateInfoLl.addView(space1, spaceLayout);

        sleepMonthView2 = new SleepWeekView(SleepStatisticActivity.this);
        mMonthStateInfoLl.addView(sleepMonthView2, weekViewLayout);

        View space2 = new View(SleepStatisticActivity.this);
        mMonthStateInfoLl.addView(space2, spaceLayout);

        sleepMonthView3 = new SleepWeekView(SleepStatisticActivity.this);
        mMonthStateInfoLl.addView(sleepMonthView3, weekViewLayout);

        sleepMonthView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setMonthClick(0);
                mDateTv.setText(getYearAndMonth(currentMonthPosition - 3));
            }
        });
        sleepMonthView1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setMonthClick(1);
                mDateTv.setText(getYearAndMonth(currentMonthPosition - 2));
            }
        });
        sleepMonthView2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setMonthClick(2);
                mDateTv.setText(getYearAndMonth(currentMonthPosition - 1));
            }
        });
        sleepMonthView3.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setMonthClick(3);
                mDateTv.setText(getYearAndMonth(currentMonthPosition));
            }
        });
    }

    private void getWeekData() {
        long diffMillions = currentWeekPosition * weekTime;
        long targetMillions = System.currentTimeMillis() + diffMillions;
        setWeekTv(mWeekTv0, targetMillions - 3 * weekTime);
        setWeekTv(mWeekTv1, targetMillions - 2 * weekTime);
        setWeekTv(mWeekTv2, targetMillions - weekTime);
        setWeekTv(mWeekTv3, targetMillions);

        LogUtils.e("aab sleepTime DateUtil.getWeekStart(targetMillions) = " + DateUtil.getDateAllFormatToString(DateUtil.getWeekStart(targetMillions)));

        HttpHelper.sleepDataService.getWeekData("week", DateUtil.getWeekStart(targetMillions) / 1000, DateUtil.getWeekEnd(targetMillions) / 1000)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<SleepWeekBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<SleepWeekBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            weekBeans = (ArrayList<SleepWeekBean>) listHttpBaseBean.getData();
                            if (weekBeans != null && weekBeans.size() > 0) {
                                for (SleepWeekBean sleepWeekBean : weekBeans) {
                                    maxWeekSleep = Math.max(maxWeekSleep, sleepWeekBean.getAverage_sleep());
                                }
                                sleepWeekView.setInfo(weekBeans.get(0).getAverage_light_sleep(), weekBeans.get(0).getAverage_deep_sleep(), maxWeekSleep);
                                sleepWeekView1.setInfo(weekBeans.get(1).getAverage_light_sleep(), weekBeans.get(1).getAverage_deep_sleep(), maxWeekSleep);
                                sleepWeekView2.setInfo(weekBeans.get(2).getAverage_light_sleep(), weekBeans.get(2).getAverage_deep_sleep(), maxWeekSleep);
                                sleepWeekView3.setInfo(weekBeans.get(3).getAverage_light_sleep(), weekBeans.get(3).getAverage_deep_sleep(), maxWeekSleep);
                                setWeekClick(3);
                                mWeekNoneTv.setVisibility(View.GONE);
                                mWeekStateInfoLl.setVisibility(View.VISIBLE);
                                mWeekLightInfo.setVisibility(View.VISIBLE);
                                mWeekDeepInfo.setVisibility(View.VISIBLE);
                                mWeekAvgInfo.setVisibility(View.VISIBLE);
                            } else {
                                mWeekNoneTv.setVisibility(View.VISIBLE);
                                mWeekStateInfoLl.setVisibility(View.GONE);
                                mWeekLightInfo.setVisibility(View.INVISIBLE);
                                mWeekDeepInfo.setVisibility(View.INVISIBLE);
                                mWeekAvgInfo.setVisibility(View.INVISIBLE);
                            }
                        }
                    }
                });
    }

    private void getMonthData() {
        mMonthTv0.setText(getMonth(currentMonthPosition - 3));
        mMonthTv1.setText(getMonth(currentMonthPosition - 2));
        mMonthTv2.setText(getMonth(currentMonthPosition - 1));
        mMonthTv3.setText(getMonth(currentMonthPosition));
        long targetMillions = 0;
        try {
            targetMillions = ymdFormat.parse(getYmd(currentMonthPosition)).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        HttpHelper.sleepDataService.getWeekData("month", DateUtil.getMonthStart(targetMillions) / 1000, DateUtil.getMonthEnd(targetMillions) / 1000)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<List<SleepWeekBean>>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<List<SleepWeekBean>> listHttpBaseBean) {
                        if (listHttpBaseBean.getErrorCode() == 0) {
                            monthBeans = (ArrayList<SleepWeekBean>) listHttpBaseBean.getData();
                            if (monthBeans != null && monthBeans.size() > 0) {
                                for (SleepWeekBean sleepWeekBean : monthBeans) {
                                    maxMonthSleep = Math.max(maxMonthSleep, sleepWeekBean.getAverage_sleep());
                                }
                                sleepMonthView.setInfo(monthBeans.get(0).getAverage_light_sleep(), monthBeans.get(0).getAverage_deep_sleep(), maxMonthSleep);
                                sleepMonthView1.setInfo(monthBeans.get(1).getAverage_light_sleep(), monthBeans.get(1).getAverage_deep_sleep(), maxMonthSleep);
                                sleepMonthView2.setInfo(monthBeans.get(2).getAverage_light_sleep(), monthBeans.get(2).getAverage_deep_sleep(), maxMonthSleep);
                                sleepMonthView3.setInfo(monthBeans.get(3).getAverage_light_sleep(), monthBeans.get(3).getAverage_deep_sleep(), maxMonthSleep);
                                setMonthClick(3);
                                mMonthNoneTv.setVisibility(View.GONE);
                                mMonthStateInfoLl.setVisibility(View.VISIBLE);
                                mMonthLightInfo.setVisibility(View.VISIBLE);
                                mMonthDeepInfo.setVisibility(View.VISIBLE);
                                mMonthAvgInfo.setVisibility(View.VISIBLE);
                            } else {
                                mMonthNoneTv.setVisibility(View.VISIBLE);
                                mMonthStateInfoLl.setVisibility(View.GONE);
                                mMonthLightInfo.setVisibility(View.INVISIBLE);
                                mMonthDeepInfo.setVisibility(View.INVISIBLE);
                                mMonthAvgInfo.setVisibility(View.INVISIBLE);
                            }
                        }
                    }
                });
    }

    private void setDayData(SleepDayData sleepDayData) {
        setTime(sleepDayData.getDay_total_sleep(), mDayHourTv, mDayHourText, mDayMinTv, mDayMinText);
        setTime(sleepDayData.getDeep_sleep(), mDeepHourTv, mDeepHourText, mDeepMinTv, mDeepMinText);
        setTime(sleepDayData.getLight_sleep(), mLightHourTv, mLightHourText, mLightMinTv, mLightMinText);
        mTotalTv.setText(sleepDayData.getDay_time_sleep());
        mDeepSleepPercent.setText(sleepDayData.getDeep_sleep_percentage());
        mLightSleepPercent.setText(sleepDayData.getLight_sleep_percentage());
        mStartTv.setText(getDayEight(currentDayPosition - 1));
        mEndTv.setText(getDayEight(currentDayPosition));

        sleepDayViewGroup.removeAllViewsInLayout();
        ArrayList<SleepUpdateBean> sleepUpdateBeans = sleepDayData.getList();
        if (sleepUpdateBeans != null && sleepUpdateBeans.size() > 0) {
            //TODO 补足数据。把小于30分的状态1改为4
            sleepUpdateBeans = SleepCalculateUtil.fillInfos(currentDayPosition - 1, sleepUpdateBeans);
            int awakeTime = SleepCalculateUtil.countAwake(sleepUpdateBeans);
            for (int i = 0; i < sleepUpdateBeans.size(); i++) {
                SleepDayView sleepDayView = new SleepDayView(SleepStatisticActivity.this);
                sleepDayView.setViewInfo(sleepUpdateBeans.get(i).getStatus(), sleepUpdateBeans.get(i).getContinuity_time(), sleepUpdateBeans.get(i));
                sleepDayView.setOnTouchItemListener(SleepStatisticActivity.this);
                sleepDayViewGroup.addView(sleepDayView, dayViewLayout);
            }
            mAssessmentTv.setText(getStatement(currentDayPosition - 1, sleepUpdateBeans, awakeTime, sleepDayData.getDay_total_sleep()));
            mAwakeTv.setText("" + awakeTime);
            mDayTotalLl.setVisibility(View.VISIBLE);
            mTotalTv.setVisibility(View.VISIBLE);
            mDayNoData.setVisibility(View.GONE);
            mDeepCl.setVisibility(View.VISIBLE);
            mLightCl.setVisibility(View.VISIBLE);
            mAwakeCl.setVisibility(View.VISIBLE);
            mAssessmentLine.setVisibility(View.VISIBLE);
            mAssessmentText.setVisibility(View.VISIBLE);
            mAssessmentTv.setVisibility(View.VISIBLE);
        } else {
            mDayTotalLl.setVisibility(View.INVISIBLE);
            mTotalTv.setVisibility(View.INVISIBLE);
            mDayNoData.setVisibility(View.VISIBLE);
            mDeepCl.setVisibility(View.GONE);
            mLightCl.setVisibility(View.GONE);
            mAwakeCl.setVisibility(View.GONE);
            mAssessmentLine.setVisibility(View.GONE);
            mAssessmentText.setVisibility(View.GONE);
            mAssessmentTv.setVisibility(View.GONE);
        }
    }

    private static SimpleDateFormat hourFormat = new SimpleDateFormat("HH:mm");

    @Override
    public void onTouchItem(boolean isTouch, SleepUpdateBean sleepUpdateBean) {
        if (isTouch) {
            mTotalTv.setText(hourFormat.format(new Date(sleepUpdateBean.getStart_time() * 1000)) + "-" + hourFormat.format(new Date(sleepUpdateBean.getEnd_time() * 1000)));
            if (sleepUpdateBean.getStatus() == SleepStateBean.STATE_LIGHT_SLEEP) {
                mDayPreTv.setText(R.string.sleep_light_sleep);
            } else if (sleepUpdateBean.getStatus() == SleepStateBean.STATE_DEEP_SLEEP) {
                mDayPreTv.setText(R.string.sleep_deep_sleep);
            } else if (sleepUpdateBean.getStatus() == SleepStateBean.STATE_AWAKE) {
                mDayPreTv.setText(R.string.sleep_awake);
            }
            setTime(sleepUpdateBean.getContinuity_time() - 1, mDayHourTv, mDayHourText, mDayMinTv, mDayMinText);
        } else {
            mTotalTv.setText(currentSleepDayData.getDay_time_sleep());
            mDayPreTv.setText(R.string.sleep_total_sleep);
            setTime(currentSleepDayData.getDay_total_sleep(), mDayHourTv, mDayHourText, mDayMinTv, mDayMinText);
        }
    }

//    private boolean isInitLine = false;
//
//    public class DayViewpagerAdapter extends RecyclerView.Adapter<DayViewpagerAdapter.ViewHolder> {
//        @NonNull
//        @Override
//        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
//            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.sleep_day_view_item, parent, false);
//            return new ViewHolder(view);
//        }
//
//        @Override
//        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
//            setTime(daysInfo.get(position).getDay_total_sleep(), holder.mDayHourTv, holder.mDayHourText, holder.mDayMinTv, holder.mDayMinText);
//            setTime(daysInfo.get(position).getDeep_sleep(), holder.mDeepHourTv, holder.mDeepHourText, holder.mDeepMinTv, holder.mDeepMinText);
//            setTime(daysInfo.get(position).getLight_sleep(), holder.mLightHourTv, holder.mLightHourText, holder.mLightMinTv, holder.mLightMinText);
//            holder.mTotalTv.setText(daysInfo.get(position).getDay_time_sleep());
//            holder.mDeepSleepPercent.setText(daysInfo.get(position).getDeep_sleep_percentage());
//            holder.mLightSleepPercent.setText(daysInfo.get(position).getLight_sleep_percentage());
//            holder.mStartTv.setText(getDayEight(currentDayPosition - 1));
//            holder.mEndTv.setText(getDayEight(currentDayPosition));
//            holder.mAssessmentTv.setText(getString(R.string.sleep_assessment2));
//
//            if (!isInitLine) {
//                LinearLayout.LayoutParams lineLayout = new LinearLayout.LayoutParams(CommonUtils.dip2px(SleepStatisticActivity.this, 1), LinearLayout.LayoutParams.MATCH_PARENT);
//
//                LinearLayout.LayoutParams spaceLayout = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
//                spaceLayout.weight = 1;
//
//                for (int i = 0; i < 12; i++) {
//                    View line = new View(SleepStatisticActivity.this);
//                    line.setBackgroundColor(getResources().getColor(R.color.text_color_gray_ee));
//                    holder.mTimeLineLL.addView(line, lineLayout);
//                    View space = new View(SleepStatisticActivity.this);
//                    holder.mTimeLineLL.addView(space, spaceLayout);
//                }
//
//                View line = new View(SleepStatisticActivity.this);
//                line.setBackgroundColor(getResources().getColor(R.color.text_color_gray_ee));
//                holder.mTimeLineLL.addView(line, lineLayout);
//                isInitLine = true;
//                holder.mStateInfoCl.addView(sleepDayViewGroup, dayViewGroupLayout);
//            }
//
//            sleepDayViewGroup.removeAllViewsInLayout();
//            ArrayList<SleepUpdateBean> sleepUpdateBeans = daysInfo.get(position).getList();
//            if (sleepUpdateBeans != null && sleepUpdateBeans.size() > 0) {
//                //TODO 补足数据。把小于30分的状态1改为4
//                sleepUpdateBeans = fillInfos(currentDayPosition - 1, sleepUpdateBeans);
//                int awakeTime = countAwake(sleepUpdateBeans);
//                for (int i = 0; i < sleepUpdateBeans.size(); i++) {
//                    SleepDayView sleepDayView = new SleepDayView(SleepStatisticActivity.this);
//                    sleepDayView.setViewInfo(sleepUpdateBeans.get(i).getStatus(), sleepUpdateBeans.get(i).getContinuity_time(), sleepUpdateBeans.get(i));
//                    sleepDayView.setOnTouchItemListener(SleepStatisticActivity.this);
//                    sleepDayViewGroup.addView(sleepDayView, dayViewLayout);
//                }
//                holder.mDayTotalLl.setVisibility(View.VISIBLE);
//                holder.mTotalTv.setVisibility(View.VISIBLE);
//                holder.mDayNoData.setVisibility(View.GONE);
//                holder.mDeepCl.setVisibility(View.VISIBLE);
//                holder.mLightCl.setVisibility(View.VISIBLE);
//                holder.mAwakeCl.setVisibility(View.VISIBLE);
//                holder.mAssessmentLine.setVisibility(View.VISIBLE);
//                holder.mAssessmentText.setVisibility(View.VISIBLE);
//                holder.mAssessmentTv.setVisibility(View.VISIBLE);
//            } else {
//                holder.mDayTotalLl.setVisibility(View.INVISIBLE);
//                holder.mTotalTv.setVisibility(View.INVISIBLE);
//                holder.mDayNoData.setVisibility(View.VISIBLE);
//                holder.mDeepCl.setVisibility(View.GONE);
//                holder.mLightCl.setVisibility(View.GONE);
//                holder.mAwakeCl.setVisibility(View.GONE);
//                holder.mAssessmentLine.setVisibility(View.GONE);
//                holder.mAssessmentText.setVisibility(View.GONE);
//                holder.mAssessmentTv.setVisibility(View.GONE);
//            }
//        }
//
//        @Override
//        public int getItemCount() {
//            return daysInfo != null ? daysInfo.size() : 0;
//        }
//
//        public class ViewHolder extends RecyclerView.ViewHolder {
//
//            @BindView(R.id.sleep_day_timeline_ll)
//            LinearLayout mTimeLineLL;
//
//            @BindView(R.id.sleep_day_state_info_cl)
//            ConstraintLayout mStateInfoCl;
//
//            @BindView(R.id.sleep_day_hour_tv)
//            TextView mDayHourTv;
//
//            @BindView(R.id.sleep_day_hour_text)
//            TextView mDayHourText;
//
//            @BindView(R.id.sleep_day_min_tv)
//            TextView mDayMinTv;
//
//            @BindView(R.id.sleep_day_min_text)
//            TextView mDayMinText;
//
//            @BindView(R.id.sleep_day_deep_hour_tv)
//            TextView mDeepHourTv;
//
//            @BindView(R.id.sleep_day_deep_hour_text)
//            TextView mDeepHourText;
//
//            @BindView(R.id.sleep_day_deep_min_tv)
//            TextView mDeepMinTv;
//
//            @BindView(R.id.sleep_day_deep_min_text)
//            TextView mDeepMinText;
//
//            @BindView(R.id.sleep_day_light_hour_tv)
//            TextView mLightHourTv;
//
//            @BindView(R.id.sleep_day_light_hour_text)
//            TextView mLightHourText;
//
//            @BindView(R.id.sleep_day_light_min_tv)
//            TextView mLightMinTv;
//
//            @BindView(R.id.sleep_day_light_min_text)
//            TextView mLightMinText;
//
//            @BindView(R.id.sleep_day_total_tv)
//            TextView mTotalTv;
//
//            @BindView(R.id.day_deep_sleep_tv)
//            TextView mDeepSleepPercent;
//
//            @BindView(R.id.day_light_sleep_tv)
//            TextView mLightSleepPercent;
//
//            @BindView(R.id.sleep_item_start_tv)
//            TextView mStartTv;
//
//            @BindView(R.id.sleep_item_end_tv)
//            TextView mEndTv;
//
//            @BindView(R.id.sleep_assessment_tv)
//            TextView mAssessmentTv;
//
//            @BindView(R.id.sleep_assessment_text)
//            TextView mAssessmentText;
//
//            @BindView(R.id.sleep_assessment_line)
//            View mAssessmentLine;
//
//            @BindView(R.id.sleep_day_awake_cl)
//            ConstraintLayout mAwakeCl;
//
//            @BindView(R.id.sleep_day_light_cl)
//            ConstraintLayout mLightCl;
//
//            @BindView(R.id.sleep_day_deep_cl)
//            ConstraintLayout mDeepCl;
//
//            @BindView(R.id.sleep_day_total_ll)
//            LinearLayout mDayTotalLl;
//
//            @BindView(R.id.sleep_day_no_data)
//            TextView mDayNoData;
//
//            public ViewHolder(@NonNull View itemView) {
//                super(itemView);
//                ButterKnife.bind(this, itemView);
//            }
//        }
//    }

    private void setTime(int time, TextView tvHour, TextView tvHourText, TextView tvMin, TextView tvMinText) {
        int hour = time / 60;
        int min = time % 60;
        if (hour == 0) {
            tvHour.setVisibility(View.GONE);
            tvHourText.setVisibility(View.GONE);
            tvMin.setVisibility(View.VISIBLE);
            tvMinText.setVisibility(View.VISIBLE);
            tvMin.setText(min + "");
        } else if (min == 0) {
            tvHour.setVisibility(View.VISIBLE);
            tvHourText.setVisibility(View.VISIBLE);
            tvMin.setVisibility(View.GONE);
            tvMinText.setVisibility(View.GONE);
            tvHour.setText(hour + "");
        } else {
            tvHour.setText(hour + "");
            tvMin.setText(min + "");
            tvHour.setVisibility(View.VISIBLE);
            tvHourText.setVisibility(View.VISIBLE);
            tvMin.setVisibility(View.VISIBLE);
            tvMinText.setVisibility(View.VISIBLE);
        }
    }

    private void setWeekClick(int index) {
        switch (index) {
            case 0:
                sleepWeekView.setSelected(true);
                sleepWeekView1.setSelected(false);
                sleepWeekView2.setSelected(false);
                sleepWeekView3.setSelected(false);
                break;
            case 1:
                sleepWeekView.setSelected(false);
                sleepWeekView1.setSelected(true);
                sleepWeekView2.setSelected(false);
                sleepWeekView3.setSelected(false);
                break;
            case 2:
                sleepWeekView.setSelected(false);
                sleepWeekView1.setSelected(false);
                sleepWeekView2.setSelected(true);
                sleepWeekView3.setSelected(false);
                break;
            case 3:
                sleepWeekView.setSelected(false);
                sleepWeekView1.setSelected(false);
                sleepWeekView2.setSelected(false);
                sleepWeekView3.setSelected(true);
                break;
        }
        setTime(weekBeans.get(index).getAverage_sleep(), mWeekAvgHourTv, mWeekAvgHourText, mWeekAvgMinTv, mWeekAvgMinText);
        mWeekDeepPercent.setText(weekBeans.get(index).getAverage_deep_sleep_percentage());
        setTime(weekBeans.get(index).getAverage_deep_sleep(), mWeekDeepHourTv, mWeekDeepHourText, mWeekDeepMinTv, mWeekDeepMinText);
        mWeekLightPercent.setText(weekBeans.get(index).getAverage_light_sleep_percentage());
        setTime(weekBeans.get(index).getAverage_light_sleep(), mWeekLightHourTv, mWeekLightHourText, mWeekLightMinTv, mWeekLightMinText);
    }

    private void setMonthClick(int index) {
        switch (index) {
            case 0:
                sleepMonthView.setSelected(true);
                sleepMonthView1.setSelected(false);
                sleepMonthView2.setSelected(false);
                sleepMonthView3.setSelected(false);
                break;
            case 1:
                sleepMonthView.setSelected(false);
                sleepMonthView1.setSelected(true);
                sleepMonthView2.setSelected(false);
                sleepMonthView3.setSelected(false);
                break;
            case 2:
                sleepMonthView.setSelected(false);
                sleepMonthView1.setSelected(false);
                sleepMonthView2.setSelected(true);
                sleepMonthView3.setSelected(false);
                break;
            case 3:
                sleepMonthView.setSelected(false);
                sleepMonthView1.setSelected(false);
                sleepMonthView2.setSelected(false);
                sleepMonthView3.setSelected(true);
                break;
        }
        setTime(monthBeans.get(index).getAverage_sleep(), mMonthAvgHourTv, mMonthAvgHourText, mMonthAvgMinTv, mMonthAvgMinText);
        mMonthDeepPercent.setText(monthBeans.get(index).getAverage_deep_sleep_percentage());
        setTime(monthBeans.get(index).getAverage_deep_sleep(), mMonthDeepHourTv, mMonthDeepHourText, mMonthDeepMinTv, mMonthDeepMinText);
        mMonthLightPercent.setText(monthBeans.get(index).getAverage_light_sleep_percentage());
        setTime(monthBeans.get(index).getAverage_light_sleep(), mMonthLightHourTv, mMonthLightHourText, mMonthLightMinTv, mMonthLightMinText);
    }

    private void setWeekTv(TextView textView, long targetMillions) {
        long currentMillions = System.currentTimeMillis();
        if (TextUtils.equals(DateUtil.getWeekStartStringYmd(targetMillions), DateUtil.getWeekStartStringYmd(currentMillions))) {
            textView.setText(R.string.sleep_this_week);
        } else if (TextUtils.equals(DateUtil.getWeekStartStringYmd(targetMillions), DateUtil.getWeekStartStringYmd(currentMillions - weekTime))) {
            textView.setText(R.string.sleep_last_week);
        } else {
            textView.setText(DateUtil.getWeekStartString(targetMillions) + "-\n " + DateUtil.getWeekEndString(targetMillions));
        }
    }

    private String getMonth(int diff) {
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH);
        month = month + diff;
        LogUtils.e("aab month = " + month);
        if (diff == 0) {
            return getString(R.string.sleep_this_month);
        } else if (diff == -1) {
            return getString(R.string.sleep_last_month);
        } else {
            LogUtils.e("aab getMonthIndex(month) = " + getMonthIndex(month));
            return getResources().getStringArray(R.array.month_names)[getMonthIndex(month)];
        }
    }

    private int getMonthIndex(int month) {
        if (month >= 0) {
            return month;
        }
        month = month + 12;
        if (month >= 0) {
            return month;
        } else {
            return getMonthIndex(month);
        }
    }

    private String getYearAndMonth(int diff) {
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH);
        int year = calendar.get(Calendar.YEAR);
        month = month + diff;
        if (month >= 0) {
            month += 1;
            return Apputils.systemLanguageIsChinese(SleepStatisticActivity.this) ? year + "年" + month + "月" : year + "-" + month;
        }
        return getYearAndMonth(month, year);
    }

    private String getYearAndMonth(int month, int year) {
        month = month + 12;
        year = year - 1;
        if (month >= 0) {
            month += 1;
            return Apputils.systemLanguageIsChinese(SleepStatisticActivity.this) ? year + "年" + month + "月" : year + "-" + month;
        } else {
            return getYearAndMonth(month, year);
        }
    }

//    private String getYearAndMonth(int diff) {
//        Calendar calendar = Calendar.getInstance();
//        int month = calendar.get(Calendar.MONTH);
//        int year = calendar.get(Calendar.YEAR);
//        month = month + diff;
//        if (month < 0) {
//            month = month + 12;
//            year = year - 1;
//        }
//        month += 1;
//        return Apputils.systemLanguageIsChinese(SleepStatisticActivity.this) ? year + "年" + month + "月" : year + "-" + month;
//    }

    private String getYmd(int diff) {
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH);
        int year = calendar.get(Calendar.YEAR);
        month = month + diff;
        if (month < 0) {
            month = month + 12;
            year = year - 1;
        }
        month += 1;
        return year + "-" + month + "- 01";
    }

    private String getDayEight(int diff) {
        Calendar calendar = Calendar.getInstance();
        long targetTime = System.currentTimeMillis() + diff * dayTime;
        calendar.setTime(new Date(targetTime));
        int month = calendar.get(Calendar.MONTH);
        month += 1;
        return month + "/" + calendar.get(Calendar.DAY_OF_MONTH) + "\n20:00";
    }

    private String getStatement(int diff, ArrayList<SleepUpdateBean> beans, int awakeTime, int totalMin) {
        int type = SleepCalculateUtil.getStatement(diff, beans, awakeTime, totalMin);
        String statement = "";
        switch (type) {
            case 1:
                statement = getString(R.string.sleep_assessment1);
                break;
            case 2:
                statement = getString(R.string.sleep_assessment2);
                break;
            case 3:
                statement = getString(R.string.sleep_assessment3);
                break;
            case 4:
                statement = getString(R.string.sleep_assessment4);
                break;
            case 5:
                statement = getString(R.string.sleep_assessment5);
                break;
            default:
                getString(R.string.sleep_assessment4);
                break;
        }
        return statement;
    }
}
