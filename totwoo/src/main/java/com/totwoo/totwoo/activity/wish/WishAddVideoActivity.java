package com.totwoo.totwoo.activity.wish;

import android.content.Intent;
import android.graphics.Bitmap;
import android.hardware.Camera;
import android.media.ThumbnailUtils;
import android.os.Build;
import android.os.Bundle;
import android.os.Message;
import android.provider.MediaStore;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.coremedia.iso.IsoFile;
import com.coremedia.iso.boxes.TrackBox;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.googlecode.mp4parser.authoring.Movie;
import com.googlecode.mp4parser.authoring.Mp4TrackImpl;
import com.googlecode.mp4parser.util.Matrix;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.VideoSelectActivity;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.WeakReferenceHandler;
import com.totwoo.totwoo.widget.MagicProgressCircle;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import sz.itguy.wxlikevideo.camera.CameraHelper;
import sz.itguy.wxlikevideo.recorder.WXLikeVideoRecorderFullScreen;
import sz.itguy.wxlikevideo.views.CameraFullsrceenPreviewView;

// 弃用, 使用统一封装的 CommonVideoRecorderActivity
@Deprecated
public class WishAddVideoActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.wish_add_video_start_iv)
    ImageView mReVideoIv;

    @BindView(R.id.cancel_action_rect_ll)
    LinearLayout mCancelActionRectLl;

    @BindView(R.id.wish_add_video_start_tv)
    TextView mGuideTextTv;

    @BindView(R.id.video_discount_mpc)
    MagicProgressCircle mMPC;

    @BindView(R.id.video_preview)
    CameraFullsrceenPreviewView video_preview;

    @BindView(R.id.wish_add_video_open_iv)
    ImageView videoOpen;

    WXLikeVideoRecorderFullScreen mRecorder;

    private static final int SELECT_LOCAL_VIDEO = 303;

    private boolean reVideoing;

    private long reSec;
    Subscription reVideoStart;
    int[] cancelPostion = new int[2];
    int cancelTop;
    int cancelBottom;
    private int cameraPosition = 0;

    Camera mCamera;

    // 输出宽度
    private static final int OUTPUT_WIDTH = 720;
    // 输出高度
    private static final int OUTPUT_HEIGHT = 1280;

    private DisCountHandler handler;
    private long perTime = 100;
    private long allTime = 30000;
    private long endTime;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_add_video);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        init();
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopRightIcon(R.drawable.wish_video_switch);
        setTopRightOnClick(v -> {
            Camera.CameraInfo cameraInfo = new Camera.CameraInfo();

            Camera.getCameraInfo(cameraPosition, cameraInfo);//得到每一个摄像头的信息
            LogUtils.i(cameraInfo.orientation + "");

            //现在是前置， 变更为后置
            if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {//代表摄像头的方位，CAMERA_FACING_FRONT前置      CAMERA_FACING_BACK后置
                mCamera.stopPreview();//停掉原来摄像头的预览
                mCamera.release();//释放资源
                mCamera = null;//取消原来摄像头
                mCamera = Camera.open(Camera.CameraInfo.CAMERA_FACING_BACK);//打开当前选中的摄像头
                cameraPosition = Camera.CameraInfo.CAMERA_FACING_BACK;
                mRecorder.setOutputSize(OUTPUT_WIDTH, OUTPUT_HEIGHT);

                video_preview.setCamera(mCamera, cameraPosition);

                mRecorder.setCameraPreviewView(video_preview);
            }
            //现在是后置，变更为前置

            if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_BACK) {//代表摄像头的方位，CAMERA_FACING_FRONT前置      CAMERA_FACING_BACK后置
                if (CameraHelper.getFrontCameraID() == -1) {
                    ToastUtils.show(WishAddVideoActivity.this, getString(R.string.open_front_camera_error), Toast.LENGTH_SHORT);
                }
                mCamera.stopPreview();//停掉原来摄像头的预览
                mCamera.release();//释放资源
                mCamera = null;//取消原来摄像头

                mCamera = Camera.open(Camera.CameraInfo.CAMERA_FACING_FRONT);//打开当前选中的摄像头
                cameraPosition = Camera.CameraInfo.CAMERA_FACING_FRONT;

                mRecorder.setOutputSize(OUTPUT_WIDTH, OUTPUT_HEIGHT);

                video_preview.setCamera(mCamera, cameraPosition);

                mRecorder.setCameraPreviewView(video_preview);

            }
        });
    }

    private void init() {
        int cameraId = CameraHelper.getDefaultCameraID();
        // Create an instance of Camera
        mCamera = CameraHelper.getCameraInstance(cameraId);
        // 初始化录像机
        mRecorder = new WXLikeVideoRecorderFullScreen(this, CommonArgs.CACHE_WISH_VIDEO_PATH_CACHE);
//        mRecorder.setFrameSize();
        mRecorder.setOutputSize(OUTPUT_WIDTH, OUTPUT_HEIGHT);

        video_preview.setCamera(mCamera, cameraId);

        mRecorder.setCameraPreviewView(video_preview);

        handler = new DisCountHandler(WishAddVideoActivity.this);
        mMPC.setPercent(1f);
        controlButton();
    }

    private void controlButton() {
        mReVideoIv.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    //不显示拿不到区域
                    mReVideoIv.setBackgroundResource(R.drawable.make_card_select_img_bg);
                    if (mCancelActionRectLl.getVisibility() == View.GONE) {
                        mCancelActionRectLl.setAlpha(0);
                        mCancelActionRectLl.setVisibility(View.VISIBLE);
                    }
                    reSec = System.currentTimeMillis();
                    endTime = System.currentTimeMillis() + 30000;
                    mGuideTextTv.setText(R.string.up_drag_cancel_video);
                    handler.sendEmptyMessageDelayed(0, perTime);
                    reVideoStart = Observable.timer(300, TimeUnit.MILLISECONDS, Schedulers.newThread())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(aLong -> {
                                if (!reVideoing) {
                                    startRecord();
                                }
                                reVideoing = true;
                            });
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (cancelTop == 0 || cancelBottom == 0) {
                        mCancelActionRectLl.getLocationOnScreen(cancelPostion);
                        cancelTop = cancelPostion[1];
                        cancelBottom = cancelTop + mCancelActionRectLl.getHeight();
                    }
                    //在区域内则取消
                    if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom) {
                        if (mCancelActionRectLl.getAlpha() == 0) {
                            mCancelActionRectLl.setAlpha(1);
                        }
                    } else {
                        if (mCancelActionRectLl.getAlpha() == 1) {
                            mCancelActionRectLl.setAlpha(0);
                        }
                    }
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    if (!reVideoing) {
                        reVideoStart.unsubscribe();
                    }
                    mReVideoIv.setBackgroundColor(getResources().getColor(R.color.transparent));
                    reSec = System.currentTimeMillis() - reSec;
                    if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom) {
                        cancelRecord();
                    } else if (reSec < 1300) {
                        cancelRecord();
                        ToastUtils.showShort(WishAddVideoActivity.this, getString(R.string.audio_re_time_low_toast));
                    } else {
                        finishRecord();
                    }
                    mCancelActionRectLl.setVisibility(View.GONE);
                    mGuideTextTv.setText(R.string.medio_guide_text);
                    mReVideoIv.performClick();
                    break;
            }
            return true;
        });
    }

    /**
     * 开始录制
     */
    private void startRecord() {
        if (mRecorder.isRecording()) {
            Toast.makeText(this, R.string.make_card_reing_video, Toast.LENGTH_SHORT).show();
            return;
        }
        goneView();

        // initialize video camera
        // 录制视频
        if (!mRecorder.startRecording())
            Toast.makeText(this, R.string.make_card_reing_video_failure, Toast.LENGTH_SHORT).show();
    }

    private boolean isSuccessed = false;

    /**
     * 停止录制
     */
    private void finishRecord() {
        if (isSuccessed) {
            return;
        }
        isSuccessed = true;
        mRecorder.stopRecording();

        String videoPath = mRecorder.getFilePath();

        if ((!Build.MODEL.equals("Nexus 6")) && cameraPosition == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            rotateVideo(videoPath, Matrix.ROTATE_180);
        } else {
            rotateVideo(videoPath, Matrix.ROTATE_0);
        }

        Bitmap videoThumbnail = ThumbnailUtils.createVideoThumbnail(CommonArgs.CACHE_WISH_VIDEO_PATH, MediaStore.Video.Thumbnails.FULL_SCREEN_KIND);
        if (videoThumbnail != null) {
//            FileUtils.saveBitmapToPath(videoThumbnail, CommonArgs.CACHE_WISH_VIDEO_COVER);
        }
        startActivity(new Intent(WishAddVideoActivity.this, WishAddInfoActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_VIDEO).putExtra(CommonArgs.VIDEO_PATH, CommonArgs.CACHE_WISH_VIDEO_PATH));
        reStartProgress();
    }

    private void cancelRecord() {
        mRecorder.stopRecording();
        reStartProgress();
    }

    private void reStartProgress() {
        reSec = 0;
        handler.removeCallbacksAndMessages(null);
        mMPC.setPercent(1f);
        allTime = 30000;
        reVideoing = false;
        visibleView();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @OnClick({R.id.wish_add_video_open_iv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.wish_add_video_open_iv:
                Intent intent = new Intent(Intent.ACTION_PICK, null);
                intent.setDataAndType(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        "video/*");
                startActivityForResult(intent, SELECT_LOCAL_VIDEO);
//                startActivityForResult(new Intent(WishAddVideoActivity.this, VideoSelectActivity.class),0);
                break;
        }
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    public class DisCountHandler extends WeakReferenceHandler<WishAddVideoActivity> {

        public DisCountHandler(WishAddVideoActivity wishAddVideoActivity) {
            super(wishAddVideoActivity);
        }

        @Override
        public void handleLiveMessage(Message msg) {
            if (allTime > perTime) {
                allTime = endTime - System.currentTimeMillis();
                float percent = allTime / 30000f;
                mMPC.setPercent(percent);
                handler.sendEmptyMessageDelayed(0, perTime);
            } else {
                mMPC.setPercent(0f);
                finishRecord();
            }
        }
    }

    private void rotateVideo(String videoPath, Matrix rotate) {
        try {
            IsoFile isoFile = new IsoFile(videoPath);
            Movie m = new Movie();
            List<TrackBox> trackBoxes = isoFile.getMovieBox().getBoxes(
                    TrackBox.class);
            for (TrackBox trackBox : trackBoxes) {
                trackBox.getTrackHeaderBox().setMatrix(rotate);//旋转
                m.addTrack(new Mp4TrackImpl(trackBox));
            }

            String path = CommonArgs.CACHE_WISH_VIDEO_PATH;//视频旋转后新的地址

            File outputVideoName = new File(path);
            if (!outputVideoName.exists())
                outputVideoName.createNewFile();
            FileOutputStream fos = new FileOutputStream(outputVideoName);
            isoFile.writeContainer(fos.getChannel());
            fos.close();
            isoFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        if (requestCode == SELECT_LOCAL_VIDEO && resultCode == RESULT_OK){
//            if (data != null && data.getData() != null){
//                String path ;
//                long size;
//                Uri uri = data.getData();
//                if (uri.getScheme().equals("content")) {
//                    ContentResolver cr = getBaseContext().getContentResolver();
//                    Cursor cursor = cr.query(uri, null, null, null, null);
//                    if (cursor != null) {
//                        cursor.moveToFirst();
//                        path = cursor.getString(cursor.getColumnIndex("_data"));
//                        size = cursor.getLong(cursor.getColumnIndex("_size"));
//                        checkVideoSize(path, size);
//                    }
//                    cursor.close();
//                } else if (uri.getScheme().equals("file")) {
//                    File file =  new File(uri.getPath());
//                    if (file.exists()){
//                        path = uri.getPath();
//                        size = file.length();
//                        checkVideoSize(path, size);
//                    }
//                }
//            }
//        }
//        super.onActivityResult(requestCode, resultCode, data);
//    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == 0 && resultCode == VideoSelectActivity.VIDEO_SELECT_SUCCESS) {
            if (data != null) {
                checkVideoSize(data.getStringExtra(VideoSelectActivity.APP_SELECT_VIDEO_PATH), data.getLongExtra(VideoSelectActivity.APP_SELECT_VIDEO_SIZE, 0));
            }
        } else if (requestCode == SELECT_LOCAL_VIDEO && resultCode == RESULT_OK && data != null) {
            CommonUtils.copyUriToPath(this, data.getData(), CommonArgs.CACHE_WISH_VIDEO_PATH);
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 检测视频是否合法
     *
     * @param path
     * @param size
     */
    private void checkVideoSize(String path, long size) {
//        if (size > 10 * 1024 * 1024){
//            Toast.makeText(this, R.string.error_video_size, Toast.LENGTH_LONG).show();
//            return;
//        }

        Bitmap videoThumbnail = ThumbnailUtils.createVideoThumbnail(path, MediaStore.Video.Thumbnails.FULL_SCREEN_KIND);
        if (videoThumbnail != null) {
//            FileUtils.saveBitmapToPath(videoThumbnail, CommonArgs.CACHE_WISH_VIDEO_COVER);
        }

        startActivity(new Intent(WishAddVideoActivity.this, WishAddInfoActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_VIDEO).putExtra(CommonArgs.VIDEO_PATH, path));
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mRecorder != null) {
            // 页面不可见就要停止录制
            mRecorder.stopRecording();
        }
        releaseCamera();              // release the camera immediately on pause event
    }

    @Override
    protected void onResume() {
        super.onResume();
        isSuccessed = false;

        try {
            if (mCamera == null) {
                mCamera = Camera.open(cameraPosition);//打开当前选中的摄像头
                mRecorder.setOutputSize(OUTPUT_WIDTH, OUTPUT_HEIGHT);
                video_preview.setCamera(mCamera, cameraPosition);
                mRecorder.setCameraPreviewView(video_preview);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void releaseCamera() {
        if (mCamera != null) {
            mCamera.setPreviewCallback(null);
            // 释放前先停止预览
            mCamera.stopPreview();
            mCamera.release();        // release the camera for other applications
            mCamera = null;
        }
    }

    private void goneView() {
        videoOpen.setVisibility(View.GONE);
        getTopRightIcon().setVisibility(View.GONE);
    }

    private void visibleView() {
        videoOpen.setVisibility(View.VISIBLE);
        getTopRightIcon().setVisibility(View.VISIBLE);
    }

    /**
     * 心愿发布成功，干掉这个页面
     * WishAddInfoActivity
     */
    @EventInject(eventType = S.E.E_WISH_POST_SUCCESSED, runThread = TaskType.UI)
    public void postSuccess(EventData data) {
        finish();
    }

    /**
     * 心愿发布成功，干掉这个页面
     * WishAddInfoActivity
     */
    @EventInject(eventType = S.E.E_WISH_POST_BACK, runThread = TaskType.UI)
    public void postBack(EventData data) {
        finish();
    }
}
