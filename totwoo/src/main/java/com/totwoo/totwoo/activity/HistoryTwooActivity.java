package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.ToTwooApplication.baseContext;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.BarUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.wish.MLoadMoreView;
import com.totwoo.totwoo.adapter.HistoryTwooAdapterV2;
import com.totwoo.totwoo.bean.TwooHistoryData;
import com.totwoo.totwoo.bean.TwooHistoryItem;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PopupMenuUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observer;

/**
 * 兼容自定义表情。采用不同的适配器 v2是定义表情的适配器
 */
public class HistoryTwooActivity extends BaseActivity {
    @BindView(R.id.history_rv)
    RecyclerView mRv;
    @BindView(R.id.history_no_record)
    TextView mNoRecord;
    private ArrayList<TwooHistoryItem> twooHistoryItems;
    private ArrayList<TwooHistoryItem> twooHistoryAllItems;
    private BaseQuickAdapter<TwooHistoryItem, BaseViewHolder> historyTwooAdapter;

    private int currentPage;
    private String lastId;
    public final static String TWOO_DAY = "twoo_day";
    public final static String TWOO_TIMES = "twoo_times";
    public final static String HISTORY_TOGETHER_DAY = "history_together_day";
    public final static String SEND_COUNT = "send_count";
    public final static String RECEIVER_COUNT = "receiver_count";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_history_twoo);
        ButterKnife.bind(this);

        View headerView = getLayoutInflater().inflate(R.layout.header_twoo_history, null);
//        ButterKnife.bind(this, headerView);
//        ACache aCache = ACache.get(HistoryTwooActivity.this);
//        String togetherDay = aCache.getAsString(HistoryTwooActivity.HISTORY_TOGETHER_DAY);
//        if (TextUtils.isEmpty(togetherDay)) {
//            togetherDay = "1";
//        }
        TextView tvTogetherDay = headerView.findViewById(R.id.history_together_day_tv);
        TextView twooCountTv = headerView.findViewById(R.id.history_count_twoo_tv);
        TextView twooDayTv = headerView.findViewById(R.id.history_first_twoo_tv_info);
        TextView receiverTv = headerView.findViewById(R.id.history_receiver_count_tv);
        TextView sendTv = headerView.findViewById(R.id.history_send_count_tv);

        if (ToTwooApplication.cacheData != null) {
            String togetherDay = ToTwooApplication.cacheData.getTogetherDay();
            tvTogetherDay.setText(togetherDay);
        }
        twooCountTv.setText(getIntent().getIntExtra(TWOO_TIMES, 0) + " " + getString(R.string.history_count_twoo));
        twooDayTv.setText(CommonUtils.getFormatDatePoint(getIntent().getStringExtra(TWOO_DAY), Apputils.systemLanguageIsChinese(HistoryTwooActivity.this)));
        receiverTv.setText(getIntent().getStringExtra(RECEIVER_COUNT));
        sendTv.setText(getIntent().getStringExtra(SEND_COUNT));

        twooHistoryItems = new ArrayList<>();
        twooHistoryAllItems = new ArrayList<>();
        mRv.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
//        if (JewInfoSingleton.getInstance().getJewVersion() == 1) {
//            historyTwooAdapter = new HistoryTwooAdapter(twooHistoryItems);
//        } else {
            historyTwooAdapter = new HistoryTwooAdapterV2(twooHistoryItems);
//        }

        historyTwooAdapter.setHeaderView(headerView);
        mRv.setAdapter(historyTwooAdapter);
        historyTwooAdapter.setLoadMoreView(new MLoadMoreView());
        historyTwooAdapter.setOnLoadMoreListener(this::getInfo, mRv);
        mRv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
            }
        });
        getInfo();
        addScrollListener();

        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_PUSH_LOVECODE);

    }

    private RelativeLayout mTopBg;


    public void setTopBarPadding() {
        //设置margin
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) getTopBar().getLayoutParams();
        layoutParams.topMargin = BarUtils.getStatusBarHeight();
        getTopBar().setLayoutParams(layoutParams);
    }

    @Override
    protected void initTopBar() {
        mTopBg = getTopBar();
        setTopbarBackground(R.color.transparent);

        setTopBackIcon(R.drawable.back_icon_black);
        TextView titleView = getTopTitleView();
        titleView.setText(R.string.history_t4);
        titleView.setTextColor(getResources().getColor(R.color.text_color_black));
        setTopRightIcon(R.drawable.delete_white_history);

        getTopRightIcon().setOnClickListener(v -> {
            final CommonMiddleDialog dialog = new CommonMiddleDialog(HistoryTwooActivity.this);
            dialog.setMessage(getString(R.string.history_t1));
            dialog.setSure(getString(R.string.confirm), v1 -> {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVECODE_DELETELOVEREMIND);
                deleteTotwooHistory();
                dialog.dismiss();
            });
            dialog.setCancel(getString(R.string.cancel));
            dialog.show();
        });
    }

    private void addScrollListener() {
        mRv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                int scrollY = recyclerView.computeVerticalScrollOffset();

                int alpha = calculateAlpha(scrollY);
                // Edge-to-Edge模式下，状态栏已透明，只需设置TopBar背景色
                mTopBg.setBackgroundColor(Color.argb(alpha,  255, 246, 246));
            }
        });
    }


    // 计算透明度的方法
    private int calculateAlpha(int scrollY) {
        int maxHeight = PopupMenuUtil.dip2px(baseContext, 100);; // 顶部状态栏和标题栏透明的最大高度

        // 根据滑动距离计算透明度
        int alpha = (int) (255 * ((float) scrollY / maxHeight));

        // 控制透明度范围在 0 到 255 之间
        return Math.min(Math.max(alpha, 0), 255);
    }


    private void getInfo() {
        HttpHelper.commonServiceV2.getHistoryInfoV3(ToTwooApplication.owner.getPairedId(), currentPage++,
                        10, TextUtils.isEmpty(lastId) ? "-1" : lastId)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<TwooHistoryData>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(HistoryTwooActivity.this, R.string.error_net);
                        LogUtils.e("e = " + e);
                    }

                    @Override
                    public void onNext(HttpBaseBean<TwooHistoryData> twooHistoryDataHttpBaseBean) {
                        if (twooHistoryDataHttpBaseBean.getErrorCode() == 0) {
                            ArrayList<TwooHistoryItem> totwooList = (ArrayList<TwooHistoryItem>) twooHistoryDataHttpBaseBean.getData().getTotwooList();
                            if (totwooList.size() == 0) {
                                if (currentPage == 1) {
                                    mNoRecord.setVisibility(View.VISIBLE);
                                } else {
                                    ToastUtils.showShort(HistoryTwooActivity.this, getString(R.string.memory_list_no));
                                    historyTwooAdapter.loadMoreEnd();
                                }
                            } else {
                                historyTwooAdapter.loadMoreComplete();
                                addList(totwooList);
                                historyTwooAdapter.notifyDataSetChanged();
                                lastId = totwooList.get(totwooList.size() - 1).getId();
                            }
                        }
                    }
                });
    }

    public void addList(ArrayList<TwooHistoryItem> srcList) {
        twooHistoryAllItems.addAll(srcList);
        ArrayList<TwooHistoryItem> tempList = new ArrayList<>();
        TwooHistoryItem tempItem = null;
        int tmpFlag = 0;
        for (int i = 0; i < twooHistoryAllItems.size(); i++) {
            TwooHistoryItem item = twooHistoryAllItems.get(i);
            String meanTextByMeaningCode = CommonUtils.getCustomBQMeaning(item.getText());
            item.setText(meanTextByMeaningCode);
            if (item.getConsonance() == 0) {
                tempList.add(item);
            } else {
                if (tmpFlag != item.getConsonance()) {
                    if (tempItem != null)
                        tempList.add(tempItem);

                    tmpFlag = item.getConsonance();


                    tempItem = item;


                    //如果最后一条数据没有匹配上，那么按一条独立都数据处理
                    if (i == srcList.size() - 1)
                        tempList.add(item);
                } else {
                    tmpFlag = 0;
                    tempItem.setOtherTime(item.getCreateTime());
                    tempItem.setOtherContent(item.getContent());
                    tempItem.setOtherText(item.getText());
                    tempList.add(tempItem);
                    tempItem = null;
                }
            }
        }
        twooHistoryItems.clear();
        twooHistoryItems.addAll(tempList);
    }

    private void deleteTotwooHistory() {
        HttpHelper.commonServiceV2.removeHistory(ToTwooApplication.owner.getPairedId())
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showLong(HistoryTwooActivity.this, R.string.custom_notify_list_delete_fail);
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            twooHistoryItems.clear();
                            currentPage = 0;
                            historyTwooAdapter.notifyDataSetChanged();
                            mNoRecord.setVisibility(View.VISIBLE);
                            ToastUtils.showLong(HistoryTwooActivity.this, R.string.delete_success);
                        } else {
                            ToastUtils.showLong(HistoryTwooActivity.this, R.string.custom_notify_list_delete_fail);
                        }
                    }
                });
    }
}
