package com.totwoo.totwoo.activity.nfc;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.data.nfc.SecretInfoBean;
import com.totwoo.totwoo.data.nfc.SecretType;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.RoundImageView;

import java.util.HashMap;

public class NfcCardPrewActivity extends BaseActivity {
    /**
     * extra data type: 名片数据
     */
    public static final String EXTRA_NFC_SECRET_CARD_DATA = "extra_nfc_secret_card_data";
    public static final String EXTRA_SHOW_SUCCESS_NOTIFY = "extra_show_success_notify";

    private SecretInfoBean beanData;

    private HashMap<String, TextView> valueViews;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        beanData = (SecretInfoBean) getIntent().getSerializableExtra(EXTRA_NFC_SECRET_CARD_DATA);

        if (beanData == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        setContentView(isBusinessCard() ? R.layout.activity_card_prew_business : R.layout.activity_card_prew_social);
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        valueViews = new HashMap<>();
        // 汇总两个卡片所需的字段
        if (isBusinessCard()) {
            //姓名
            valueViews.put("fullname", (TextView) findViewById(R.id.nfc_card_prew_name));
            //公司
            valueViews.put("company", (TextView) findViewById(R.id.nfc_card_prew_company));
            //电话
            valueViews.put("tel", (TextView) findViewById(R.id.nfc_card_prew_phone));
            //职位
            valueViews.put("position", (TextView) findViewById(R.id.nfc_card_prew_job));
            //地址
            valueViews.put("address", (TextView) findViewById(R.id.nfc_card_prew_address));
            //邮箱
            valueViews.put("email", (TextView) findViewById(R.id.nfc_card_prew_email));
            //微信
//            valueViews.put("wechat", (TextView) findViewById(R.id.nfc_card_prew_wechat));
//            //qq
//            valueViews.put("qq", (TextView) findViewById(R.id.nfc_card_prew_qq));
            //备注
            valueViews.put("remarks", (TextView) findViewById(R.id.nfc_card_prew_note));
        } else {
            valueViews.put("city", (TextView) findViewById(R.id.nfc_card_prew_city));

            valueViews.put("tel", (TextView) findViewById(R.id.nfc_card_prew_phone));

            valueViews.put("nickname", (TextView) findViewById(R.id.nfc_card_prew_name));

//            valueViews.put("constellation", (TextView) findViewById(R.id.nfc_card_prew_astro));

            // 爱好
            valueViews.put("hobby", (TextView) findViewById(R.id.nfc_card_prew_hobby));
            // 介绍
            valueViews.put("introduce", (TextView) findViewById(R.id.nfc_card_prew_introduce));
            // 微信
//            valueViews.put("wechat", (TextView) findViewById(R.id.nfc_card_prew_wechat));
//            // QQ
//            valueViews.put("qq", (TextView) findViewById(R.id.nfc_card_prew_qq));
        }

        findViewById(R.id.nfc_card_prew_back).setOnClickListener(v -> finish());
        findViewById(R.id.nfc_card_prew_edit_btn).setOnClickListener(v -> {
            startActivity(
                    new Intent(this, NfcCardEditActivity.class)
                            .putExtra(NfcCardEditActivity.EXTRA_NFC_SECRET_CARD_DATA, beanData));
            finish();
        });
        refreshUI();
    }

    private void refreshUI() {
        HashMap<String, String> data = beanData.getExtraData();
        if (data == null) {
            return;
        }

        // 基础信息
        RequestOptions options = new RequestOptions()
                .error(ToTwooApplication.owner.getGender() == 0 ? R.drawable.default_head_yellow : R.drawable.default_head_yellow);
        Glide.with(this).load(BitmapHelper.checkRealPath(data.get("head_portrait"))).apply(options).into(((RoundImageView) findViewById(R.id.nfc_card_prew_head_icon)));

        for (String key : valueViews.keySet()) {
            TextView view = valueViews.get(key);
            if (!TextUtils.isEmpty(data.get(key))) {
                view.setText(data.get(key));
            } else {
                if (view.getParent() instanceof RelativeLayout) {
                    ((RelativeLayout) view.getParent()).setVisibility(View.GONE);
                } else {
                    view.setText("");
                }
            }
        }

        // 展示添加成功的提示框
        if (getIntent().getBooleanExtra(EXTRA_SHOW_SUCCESS_NOTIFY, false)) {
            ToastUtils.showLong(this, R.string.saved_success);
//            BlackAlertDialogUtil.showSingleButtonDialog(this, R.string.nfc_secret_add_success_info, R.string.i_know, null);
        }
    }


    /**
     * 是否商务卡片
     *
     * @return
     */
    private boolean isBusinessCard() {
        return SecretType.TYPE_BUSINESS.equals(beanData.getType().type);
    }

}
