package com.totwoo.totwoo.activity;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.provider.MediaStore;
import android.text.format.DateUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PictureAddWatermark;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.JCameraView.JCameraView;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.totwoo.totwoo.widget.SceneAnimation;
import com.totwoo.totwoo.widget.SupportRenderScriptBlur;
import com.umeng.analytics.MobclickAgent;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import eightbitlab.com.blurview.BlurView;

public class CameraActivity extends AppCompatActivity implements ShakeMonitor.OnEventListener, SubscriberListener, BluetoothManage.ConnectSuccessListener {
    @BindView(R.id.camera_share_facebook_iv)
    ImageView shareIconFacebook;
    @BindView(R.id.camera_share_twitter_iv)
    ImageView shareIconTwitter;
    @BindView(R.id.camera_share_weibo_iv)
    ImageView shareIconWeibo;
    @BindView(R.id.camera_share_qzone_iv)
    ImageView shareIconQzone;
    @BindView(R.id.camera_share_qq_iv)
    ImageView shareIconQQ;
    @BindView(R.id.camera_share_title)
    TextView camera_share_title;
    @BindView(R.id.root)
    ViewGroup root;
    @BindView(R.id.backgroundBlurView)
    BlurView backgroundBlurView;
    @BindView(R.id.reminder_hint_lv)
    LottieAnimationView mLottieView;
    @BindView(R.id.camera_hint_iv)
    ImageView ivHint;
    @BindView(R.id.camera_anim_tv)
    TextView camera_anim_tv;
    @BindView(R.id.jcameraview)
    JCameraView jCameraView;
    @BindView(R.id.face_camera_water_mark_totwoo_iv)
    ImageView faceCameraWatermarkIv;
    @BindView(R.id.face_camera_water_mark_select_iv)
    ImageView faceCameraWatermarkSelectIv;
    @BindView(R.id.face_camera_done_iv)
    ImageView ivPicShow;
    @BindView(R.id.face_camera_control_cl)
    ConstraintLayout clControl;
    @BindView(R.id.face_camera_beauty_cl)
    ConstraintLayout clBeauty;
    @BindView(R.id.face_camera_flash_iv)
    ImageView face_camera_flash_iv;
    private Bitmap showImage;

    private Animation translateAnimation;
    private NewUserGiftDialog newUserGiftDialog;
    private FacebookCallback<Sharer.Result> facebookCallback;
    private ShakeMonitor shakeMonitor;
    private GifStopHandler gifStopHandler;
    private int countDownTime = 3;
    private boolean isCountDowning = false;
    private boolean isStarted = false;
    private boolean isShareShow = false;
    private boolean isWaterMark = true;
    private static final String PHOTO_PREFIX = "totwoo_camera_";
    private static final String imgCachePre = "totwoo_cache_img_";
    private AnimHandler animHandler;
    private HandlerThread previewHandlerThread;
    private Handler perviewHandler;
    private boolean isFrontCamera = true;
    private static final String REMIND_CAMERA_SHOW = "remind_camera_show";

    public static final int[] tipGif = new int[]{
            R.drawable.qj_00000, R.drawable.qj_00001, R.drawable.qj_00002, R.drawable.qj_00003, R.drawable.qj_00004,
            R.drawable.qj_00005, R.drawable.qj_00006, R.drawable.qj_00007, R.drawable.qj_00008, R.drawable.qj_00009,
            R.drawable.qj_00010, R.drawable.qj_00011, R.drawable.qj_00012, R.drawable.qj_00013, R.drawable.qj_00014,
            R.drawable.qj_00015, R.drawable.qj_00016, R.drawable.qj_00017, R.drawable.qj_00018, R.drawable.qj_00019,
            R.drawable.qj_00020, R.drawable.qj_00021, R.drawable.qj_00022, R.drawable.qj_00023, R.drawable.qj_00024,
            R.drawable.qj_00025, R.drawable.qj_00026, R.drawable.qj_00027, R.drawable.qj_00028, R.drawable.qj_00029,
            R.drawable.qj_00030, R.drawable.qj_00031, R.drawable.qj_00032, R.drawable.qj_00033, R.drawable.qj_00034,
            R.drawable.qj_00035, R.drawable.qj_00036, R.drawable.qj_00037, R.drawable.qj_00038, R.drawable.qj_00039,
            R.drawable.qj_00040, R.drawable.qj_00041, R.drawable.qj_00042, R.drawable.qj_00043, R.drawable.qj_00044,
            R.drawable.qj_00045, R.drawable.qj_00046, R.drawable.qj_00047, R.drawable.qj_00048, R.drawable.qj_00049,
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // CameraActivity手动启用Edge-to-Edge（不继承BaseActivity）
        EdgeToEdgeUtils.enableEdgeToEdge(this);

        setContentView(R.layout.activity_totwoo_camera);
        ButterKnife.bind(this);

        if (Apputils.systemLanguageIsChinese(CameraActivity.this)) {
            shareIconFacebook.setVisibility(View.GONE);
            shareIconTwitter.setVisibility(View.GONE);
        } else {
            shareIconQQ.setVisibility(View.GONE);
            shareIconQzone.setVisibility(View.GONE);
            shareIconWeibo.setVisibility(View.GONE);
        }

        if (Build.VERSION.SDK_INT >= 23) {
            backgroundBlurView.setupWith(root)
                    .setBlurAlgorithm(new SupportRenderScriptBlur(CameraActivity.this))
                    .setBlurRadius(25f)
                    .setHasFixedTransformationMatrix(true);
        } else {
            backgroundBlurView.setBackgroundColor(getResources().getColor(R.color.text_color_black_note));
        }

        translateAnimation = AnimationUtils.loadAnimation(this, R.anim.camera_tran);
        translateAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(this, R.string.error_jewelry_connect);
        } else {
            if (BleParams.isMWJewlery()) {
                ToastUtils.showLong(this, R.string.came_guide_33);
//                if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, REMIND_CAMERA_SHOW, false)) {
//                    showUseDialog();
//                }
//                else{
//                    ToastUtils.showLong(this, R.string.camera_touch_hint);
//                    mLottieView.setVisibility(View.VISIBLE);
//                    mLottieView.playAnimation();
//                    mLottieView.addAnimatorListener(new Animator.AnimatorListener() {
//                        @Override
//                        public void onAnimationStart(Animator animation) {
//
//                        }
//
//                        @Override
//                        public void onAnimationEnd(Animator animation) {
//                            mLottieView.setVisibility(View.GONE);
//                        }
//
//                        @Override
//                        public void onAnimationCancel(Animator animation) {
//
//                        }
//
//                        @Override
//                        public void onAnimationRepeat(Animator animation) {
//
//                        }
//                    });
//                }
            } else {
                ivHint.setVisibility(View.VISIBLE);
                gifStopHandler = new GifStopHandler(CameraActivity.this);
                gifStopHandler.sendEmptyMessageDelayed(0, 2500);

                new SceneAnimation(ivHint, tipGif, 20, true);

                ToastUtils.showLong(this, R.string.camera_click_hint);
            }
        }

        newUserGiftDialog = new NewUserGiftDialog(CameraActivity.this, v -> {
            if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_CAMERA_LUCKY_CLICK);
            } else {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_CAMERA_LUCKY_CLICK);
            }
            WebViewActivity.loadUrl(CameraActivity.this, HttpHelper.URL_GIFT, false);
            newUserGiftDialog.dismiss();
        }, v -> newUserGiftDialog.dismiss(), CommonUtils.setNumberGoldenSpan("感谢您的分享\n请抽取88元兔兔代金券", 88, 20), "立即抽奖");

        facebookCallback = new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                ToastUtils.showShort(CameraActivity.this, getResources().getString(R.string.share_complete));
            }

            @Override
            public void onCancel() {
                ToastUtils.showShort(CameraActivity.this, getResources().getString(R.string.share_cancel));
            }

            @Override
            public void onError(FacebookException error) {
                ToastUtils.showShort(CameraActivity.this, getResources().getString(R.string.share_error));
            }
        };

        if (Apputils.systemLanguageIsChinese(CameraActivity.this)) {
            camera_share_title.setText(CommonUtils.setNumberGoldenSpan(getResources().getString(R.string.share_text_head_info_camera), 88, 16));
        }

        animHandler = new AnimHandler(CameraActivity.this);

        jCameraView.setFeatures(JCameraView.BUTTON_STATE_ONLY_CAPTURE);
        jCameraView.setTip("");
//        jCameraView.setErrorLisenter(new ErrorListener() {
//            @Override
//            public void onError() {
//                //错误监听
//                Log.i("CJT", "camera error");
//            }
//
//            @Override
//            public void AudioPermissionError() {
//
//            }
//        });
//        //JCameraView监听
//        jCameraView.setJCameraLisenter(new JCameraListener() {
//            @Override
//            public void captureSuccess(Bitmap bitmap) {
//
//            }
//
//            @Override
//            public void recordSuccess(String url, Bitmap firstFrame) {
//                //获取视频路径
//                String path = FileUtil.saveBitmap("JCamera", firstFrame);
//            }
//        });
        jCameraView.setJCameraStatusChange(new JCameraView.JCameraStatusChange() {
            @Override
            public void captureSucceed(Bitmap bitmap) {
                LogUtils.e("aab bitmap = " + bitmap);
                showImage = bitmap;
                ivPicShow.setImageBitmap(showImage);
            }
        });

        previewHandlerThread = new HandlerThread("Preview");
        previewHandlerThread.start();
        perviewHandler = new Handler(previewHandlerThread.getLooper());
    }

    private void showUseDialog() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(CameraActivity.this);
        commonMiddleDialog.setMessage(R.string.reminder_camera_info);
        commonMiddleDialog.setIntroIcon(R.drawable.reminder_touch_camera);
        commonMiddleDialog.setTitle(R.string.reminder_camera_title);
        commonMiddleDialog.setCanceledOnTouchOutside(false);
        commonMiddleDialog.setSure(R.string.reminder_dialog_close, v -> commonMiddleDialog.dismiss());
        commonMiddleDialog.setCancel(R.string.reminder_dialog_cancel, v -> {
            PreferencesUtils.put(ToTwooApplication.baseContext, REMIND_CAMERA_SHOW, true);
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.show();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    public void onConnectSuccessd() {
        perviewHandler.postDelayed(() -> BluetoothManage.getInstance().stayIn(true), 2000);
    }

    @Override
    public void onEvent(int type) {
        startCountDownAnim();
    }

    private class AnimHandler extends Handler {
        private final WeakReference<CameraActivity> mActivity;

        private AnimHandler(CameraActivity activity) {
            this.mActivity = new WeakReference<CameraActivity>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            CameraActivity cameraActivity = mActivity.get();
            if (cameraActivity == null) {
                return;
            }
            if (countDownTime > 0) {
                camera_anim_tv.setVisibility(View.VISIBLE);
                camera_anim_tv.setText(countDownTime + "");
                camera_anim_tv.startAnimation(translateAnimation);
                countDownTime--;
                animHandler.sendEmptyMessageDelayed(0, 1000);
            } else {
                countDownTime = 3;
                if (isStarted) {
                    jCameraView.takePicture();
                    showControl(true);
                }
                isCountDowning = false;
                camera_anim_tv.setText("");
                camera_anim_tv.setVisibility(View.GONE);
            }
        }
    }

    private void startCountDownAnim() {
        if (jCameraView.isTaked) {
            return;
        }
        jCameraView.isTaked = true;
        if (!isCountDowning) {
            animHandler.sendEmptyMessage(0);
            isCountDowning = !isCountDowning;
        }
    }

    private class GifStopHandler extends Handler {
        private final WeakReference<CameraActivity> mActivity;

        public GifStopHandler(CameraActivity activity) {
            this.mActivity = new WeakReference<CameraActivity>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            CameraActivity mainActivity = mActivity.get();
            if (mainActivity == null) {
                return;
            }
            ivHint.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.face_camera_close_iv, R.id.face_camera_flash_iv, R.id.camera_share_facebook_iv, R.id.camera_share_twitter_iv,
            R.id.face_camera_change_iv, R.id.face_camera_take_iv, R.id.face_camera_watermark_cl, R.id.face_camera_share_dialog_cancel,
            R.id.face_camera_back_tv, R.id.face_camera_save_iv, R.id.face_camera_share_tv, R.id.camera_share_wechat_iv,
            R.id.camera_share_friend_iv, R.id.camera_share_weibo_iv, R.id.camera_share_qzone_iv, R.id.camera_share_qq_iv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.face_camera_close_iv:
                if (isCountDowning) {
                    return;
                }
                finish();
                break;
            case R.id.face_camera_flash_iv:
                if (isCountDowning) {
                    return;
                }
                changeFlash();
                break;
            case R.id.face_camera_change_iv:
                if (isCountDowning) {
                    return;
                }
                changeCameraFront();
                break;
            case R.id.face_camera_watermark_cl:
                if (isWaterMark) {
                    isWaterMark = false;
                    faceCameraWatermarkIv.setVisibility(View.GONE);
                    faceCameraWatermarkSelectIv.setImageResource(R.drawable.watermark_un_press);
                } else {
                    isWaterMark = true;
                    faceCameraWatermarkIv.setVisibility(View.VISIBLE);
                    faceCameraWatermarkSelectIv.setImageResource(R.drawable.watermark_press);
                }
                break;
            case R.id.face_camera_take_iv:
                startCountDownAnim();
                break;
            case R.id.face_camera_back_tv:
                showControl(false);
                ivPicShow.setImageDrawable(null);
                break;
            case R.id.face_camera_save_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CAMERA_SAVE);
                String path = PHOTO_PREFIX + System.currentTimeMillis() + ".jpg";
                if (isWaterMark) {
                    saveBitmapToSystem(PictureAddWatermark.createWaterMaskLeftBottom(CameraActivity.this, showImage, BitmapFactory.decodeResource(getResources(), R.drawable.watermark_src), 20, 20), path);
                } else {
                    saveBitmapToSystem(showImage, path);
                }
                ToastUtils.showShort(CameraActivity.this, getString(R.string.camera_save_success));
                showControl(false);
                ivPicShow.setImageDrawable(null);
                break;
            case R.id.face_camera_share_tv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CAMERA_SHARE);
                if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_CAMERA_CLICK);
                } else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_CAMERA_CLICK);
                }
                isShareShow = true;
                backgroundBlurView.setVisibility(View.VISIBLE);
                clControl.setVisibility(View.GONE);
                break;
            case R.id.face_camera_share_dialog_cancel:
                isShareShow = false;
                backgroundBlurView.setVisibility(View.GONE);
                clControl.setVisibility(View.VISIBLE);
                break;
            case R.id.camera_share_wechat_iv:
                ShareUtilsSingleton.getInstance().shareImageToWechat(getWaterMarkPath());
                break;
            case R.id.camera_share_friend_iv:
                ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getWaterMarkPath());
                break;
            case R.id.camera_share_weibo_iv:
                ShareUtilsSingleton.getInstance().shareImageToWeibo(CameraActivity.this, getWaterMarkPath(), "");
                break;
            case R.id.camera_share_qzone_iv:
                ShareUtilsSingleton.getInstance().shareImageToQzone(getWaterMarkPath(), "");
                break;
            case R.id.camera_share_facebook_iv:
                ShareUtilsSingleton.getInstance().shareImageToFacebook(getWaterMarkPath(), CameraActivity.this, facebookCallback);
                break;
            case R.id.camera_share_twitter_iv:
                ShareUtilsSingleton.getInstance().shareImageToTwitter(getWaterMarkPath(), "");
                break;
            case R.id.camera_share_qq_iv:
                ShareUtilsSingleton.getInstance().shareImageToQQ(getWaterMarkPath());
                break;
        }
    }

    private boolean IsFlashOn = false;

    private void changeFlash() {
        if (!IsFlashOn) {
            IsFlashOn = true;
            jCameraView.setFlashModeOn(true);
            face_camera_flash_iv.setImageResource(R.drawable.face_camera_flash_off);
            ToastUtils.showShort(CameraActivity.this, getResources().getString(R.string.camera_flash_on));
        } else {
            IsFlashOn = false;
            jCameraView.setFlashModeOn(false);
            face_camera_flash_iv.setImageResource(R.drawable.face_camera_flash_on);
            ToastUtils.showShort(CameraActivity.this, getResources().getString(R.string.camera_flash_off));
        }
    }

    private void changeCameraFront() {
        isFrontCamera = !isFrontCamera;
        jCameraView.switchCamera();
        if (isFrontCamera) {
            face_camera_flash_iv.setVisibility(View.GONE);
        } else {
            face_camera_flash_iv.setVisibility(View.VISIBLE);
        }
    }

    private String getWaterMarkPath() {
        String path;
        if (isWaterMark) {
            Bitmap tempBitmap = PictureAddWatermark.createWaterMaskLeftBottom(CameraActivity.this, showImage, BitmapFactory.decodeResource(getResources(), R.drawable.watermark_src), 20, 20);
            path = FileUtils.saveBitmapFromSDCard(tempBitmap,
                    imgCachePre + System.currentTimeMillis());
        } else {
            path = FileUtils.saveBitmapFromSDCard(showImage,
                    imgCachePre + System.currentTimeMillis());
        }
        return path;
    }

    private void showControl(boolean isVis) {
        if (isVis) {
            ivPicShow.setVisibility(View.VISIBLE);
            clControl.setVisibility(View.VISIBLE);
            clBeauty.setVisibility(View.GONE);
        } else {
            jCameraView.cancel();
            ivPicShow.setVisibility(View.GONE);
            clControl.setVisibility(View.GONE);
            clBeauty.setVisibility(View.VISIBLE);
            isWaterMark = true;
            faceCameraWatermarkIv.setVisibility(View.VISIBLE);
            faceCameraWatermarkSelectIv.setImageResource(R.drawable.watermark_press);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        fullScreen();
        isStarted = true;
        checkAndSetShakeState();
        BluetoothManage.getInstance().connectedStatus();
        BluetoothManage.getInstance().setConnectSuccessListener(this);
        BluetoothManage.getInstance().stayIn(true);
    }

    private void checkAndSetShakeState() {

        if (shakeMonitor == null) {
            shakeMonitor = new ShakeMonitor(this);
            shakeMonitor.setOnEventListener(this);
        }
        shakeMonitor.start();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (shakeMonitor != null) {
            shakeMonitor.stop();
        }
        isStarted = false;
        BluetoothManage.getInstance().stayIn(false);
        BluetoothManage.getInstance().setConnectSuccessListener(null);
        jCameraView.onPause();
        if (isFinishing()) {
            destroy();
        }
    }

    private boolean isDestroyed = false;

    private void destroy() {
        if (isDestroyed) {
            return;
        }
        if (!isFrontCamera) {
            jCameraView.switchCamera();
        }
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CAMERA_OFF, null);
        InjectUtils.injectUnregisterListenerAll(this);
        isDestroyed = true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        destroy();
    }

    @Override
    public void onBackPressed() {
        if (isShareShow) {
            backgroundBlurView.setVisibility(View.GONE);
            clControl.setVisibility(View.VISIBLE);
            isShareShow = false;
            return;
        }
        if (jCameraView.isTaked) {
            showControl(false);
            return;
        }
        super.onBackPressed();
    }

    /**
     * CameraActivity需要全屏显示，不需要任何Insets处理
     * 注意：CameraActivity继承AppCompatActivity，不是BaseActivity
     */
    private void setupEdgeToEdgeForCamera() {
        // CameraActivity需要全屏显示，不需要任何Insets处理
        // 在onCreate中手动启用Edge-to-Edge即可
    }

    private void fullScreen() {
        //全屏显示 - Edge-to-Edge模式下，系统栏已透明，只需隐藏系统栏
        if (Build.VERSION.SDK_INT >= 19) {
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        } else {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_FULLSCREEN;
            decorView.setSystemUiVisibility(option);
        }
    }

    /**
     * android 11及以上保存图片到相册
     *
     * @param image
     */
    public void saveBitmapToSystem(Bitmap image, String fileName) {
        long mImageTime = System.currentTimeMillis();
        String imageDate = new SimpleDateFormat("yyyyMMdd-HHmmss").format(new Date(mImageTime));
        String SCREENSHOT_FILE_NAME_TEMPLATE = "winetalk_%s.png";//图片名称，以"winetalk"+时间戳命名
        String mImageFileName = String.format(SCREENSHOT_FILE_NAME_TEMPLATE, imageDate);

        final ContentValues values = new ContentValues();
        values.put(MediaStore.MediaColumns.RELATIVE_PATH, "Pictures"
                + File.separator + fileName);
        values.put(MediaStore.MediaColumns.DISPLAY_NAME, mImageFileName);
        values.put(MediaStore.MediaColumns.MIME_TYPE, "image/png");
        values.put(MediaStore.MediaColumns.DATE_ADDED, mImageTime / 1000);
        values.put(MediaStore.MediaColumns.DATE_MODIFIED, mImageTime / 1000);
        values.put(MediaStore.MediaColumns.DATE_EXPIRES, (mImageTime + DateUtils.DAY_IN_MILLIS) / 1000);
        values.put(MediaStore.MediaColumns.IS_PENDING, 1);

        ContentResolver resolver = ToTwooApplication.baseContext.getContentResolver();
        final Uri uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
        try {
            // First, write the actual data for our screenshot
            try (OutputStream out = resolver.openOutputStream(uri)) {
                if (!image.compress(Bitmap.CompressFormat.PNG, 100, out)) {
                    throw new IOException("Failed to compress");
                }
            }
            // Everything went well above, publish it!
            values.clear();
            values.put(MediaStore.MediaColumns.IS_PENDING, 0);
            values.putNull(MediaStore.MediaColumns.DATE_EXPIRES);
            resolver.update(uri, values, null, null);
        } catch (Exception e) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                resolver.delete(uri, null);
            }
            e.printStackTrace();
        }
    }
}
