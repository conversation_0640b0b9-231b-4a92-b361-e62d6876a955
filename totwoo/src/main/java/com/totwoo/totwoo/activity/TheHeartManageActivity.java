package com.totwoo.totwoo.activity;

import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ContactsBean;
import com.totwoo.totwoo.bean.NotifyDataModel;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.data.CoupleLogic.CoupleCallback;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomProgressBarDialog;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 心有灵犀配对管理界面
 *
 * <AUTHOR>
 * @date 2015-2015年7月16日
 */
public class TheHeartManageActivity extends BaseActivity {

    /**
     * 配对管理的列表
     */
    @BindView(R.id.the_heart_manage_list)
    ListView coupleListView;

    /**
     * 管理列表对应的 ListView
     */
    ArrayList<ContactsBean> coupleListData;

    /**
     * 对应 Adapter
     */
    CoupleListAdapter mAdapter;

    /**
     * 进度框
     */
    CustomProgressBarDialog progressBar;

    /**
     * 无数据的时候 显示的View
     */
    @BindView(R.id.heart_manage_no_view_top)
    View emptyViewTop;
    @BindView(R.id.heart_manage_no_view_center)
    LinearLayout emptyViewCenter;

    /**
     * 选择联系人的布局
     */
    private LinearLayout chooseLayout;

    /**
     * 配对管理操作类
     */
    private CoupleLogic mCoupleLogic;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_the_heart_manage);
        ButterKnife.bind(this);

        mAdapter = new CoupleListAdapter();
        coupleListView.setAdapter(mAdapter);

        coupleListView.setOnItemLongClickListener((parent, view, position, id) -> {
            if (position > 0) {
                showDelDialog(coupleListData.get(position - 1));
            }
            return true;
        });

        mCoupleLogic = new CoupleLogic(this);

        // 联网获取列表数据
        getCoupleListData();

        initListHeader();

        clearNotify();
        emptyViewCenter.setOnClickListener(v -> checkAndToContacts());

    }

    private void showDelDialog(final ContactsBean contactsBean) {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
        dialog.setMessage(R.string.delete_coule_item_prompt);
        dialog.setSure(R.string.confirm, v -> {
            deleteCoupleItem(contactsBean);
            dialog.dismiss();
        });
        dialog.setCancel(R.string.cancel);
        dialog.show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        EventBus.getDefault().unregister(this);
    }

    /**
     * 清空用户管理的相关推送信息
     */
    private void clearNotify() {
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        manager.cancel(NotifyDataModel.NOTI_REPLAY_ID);
        manager.cancel(NotifyDataModel.NOTI_REQUEST_ID);
        manager.cancel(NotifyDataModel.NOTI_APART_ID);
    }

    private void checkAndToContacts() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_WANTPAIR_CLICK_ADDRESSBOOK);
        if (!PermissionUtil.hasContactsPermission(TheHeartManageActivity.this)) {
            return;
        }
        startActivityForResult(new Intent(TheHeartManageActivity.this,
                TheHeartChooseActivity.class), 100);
    }

    /**
     * 初始化ListHeaderView
     */
    private void initListHeader() {
        View header = LayoutInflater.from(this).inflate(
                R.layout.activity_the_heart_manager_list_header, null);

        coupleListView.addHeaderView(header);

        chooseLayout = (LinearLayout) header
                .findViewById(R.id.the_heart_manage_choose_layout);

        TextView mPairHint = header.findViewById(R.id.the_heart_pair_hint);
        if (BleParams.isButtonBatteryJewelry()) {
            mPairHint.setText(R.string.love_unpair_clause1_info_no_vibrate);
        }

        chooseLayout.setOnClickListener(v -> checkAndToContacts());
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        getCoupleListData();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        getCoupleListData();
    }

    private void deleteCoupleItem(final ContactsBean bean) {
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("talkId", bean.getTalkId());
        HttpRequest.post(HttpHelper.URL_DELTET_COUPLE, params, new RequestCallBack<String>() {
            @Override
            public void onLogicSuccess(String o) {
                super.onLogicSuccess(o);
                if (mAdapter != null) {
                    mAdapter.removeList(bean);
                }

                // 如果删除了已配对的列表, 相当于直接解绑
                if (bean.getCoupleShip() == CoupleLogic.COUPLE_STATE_PAIRED) {
                    // 清除配对人信息
                    CoupleLogic.clearCouplePairedData(TheHeartManageActivity.this);
                    //发到HoemAcitivity和 HomeTotwooHolder
                    EventBus.getDefault().post(new TotwooMessage(CoupleLogic.COUPLE_STATE_APART + "", null));
                }
                ToastUtils.showLong(TheHeartManageActivity.this, R.string.delete_success);
            }
        });
    }


    /**
     * 联网获取列表数据
     */
    private void getCoupleListData() {
        RequestParams param = HttpHelper.getBaseParams(true);
        param.addFormDataPart("page", 1);
        param.addFormDataPart("perpage", 1000);
        HttpRequest.get(
                HttpHelper.URL_COUPLE_LIST, param,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
                        // 隐藏空的加载View
                        if (TextUtils.isEmpty(s)) {
                            emptyViewTop.setVisibility(View.VISIBLE);
                            emptyViewCenter.setVisibility(View.VISIBLE);
                            coupleListView.setVisibility(View.GONE);
                        } else {
                            emptyViewTop.setVisibility(View.GONE);
                            emptyViewCenter.setVisibility(View.GONE);
                            coupleListView.setVisibility(View.VISIBLE);
                        }

                        // 清空原来数据
                        if (mAdapter != null) {
                            mAdapter.removeAllData();
                        }

                        // 隐藏进度框
                        showProgressBar(false);

                        if (TextUtils.isEmpty(s)) {
                            return;
                        }

                        JSONArray array = null;
                        try {
                            array = new JSONArray(s);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (array == null) {
                            return;
                        }

                        for (int i = 0; i < array.length(); i++) {
                            JSONObject obj = array.optJSONObject(i);
                            if (obj != null) {
                                // 解析用户数据
                                ContactsBean con = null;

                                int state = obj.optInt("coupleShip");
                                // 解析状态
                                if (state == 1) {
                                    con = new ContactsBean();
                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_REQUEST);
                                } else if (state == 3) {
                                    con = new ContactsBean();
                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_PAIRED);

                                    ToTwooApplication.owner
                                            .setPairedId(obj.optString("talkId"));
                                } else if (state == 4) {
                                    con = new ContactsBean();
                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_APART);
                                } else if (state == 2) {
                                    con = new ContactsBean();
                                    con.setCoupleShip(CoupleLogic.COUPLE_STATE_REPLY);
                                }

                                if (con == null) {
                                    continue;
                                }

                                con.setTalkId(obj.optString("talkId"));

                                JSONObject info = obj
                                        .optJSONObject("userinfo");
                                if (info != null) {
                                    con.setHeadUrl(info
                                            .optString("head_portrait"));
                                    con.setName(info
                                            .optString("nick_name"));

                                    con.setSpecific_id(info.optString("totwoo_id"));
                                    con.setPhoneNumber(info.optString("mobilephone"));
                                }

                                if (mAdapter != null) {
                                    mAdapter.addList(con);
                                }
                            }
                        }
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        ToastUtils.showLong(TheHeartManageActivity.this,
                                R.string.error_net);
                        // 隐藏进度框
                        showProgressBar(false);
                    }
                });

    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.choose_from_contacts_title);
//        setTopRightIcon(R.drawable.help_icon);
//        setTopRightOnClick(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                startActivity(new Intent(TheHeartManageActivity.this,
//                        TheHeartHelpActivity.class));
//            }
//        });
    }

    /**
     * 心有灵犀请求相关的人员列表
     *
     * <AUTHOR>
     * @date 2015-2015年8月26日
     */
    private class CoupleListAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            return coupleListData == null ? 0 : coupleListData.size();
        }

        @Override
        public Object getItem(int position) {
            return coupleListData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        /**
         * 添加数据, 及时刷新列表
         */
        public void addList(ContactsBean bean) {
            if (coupleListData == null) {
                coupleListData = new ArrayList<>();
            }

            if (bean != null) {
                coupleListData.add(bean);
                notifyDataSetChanged();
            }
        }

        /**
         * 添加数据, 及时刷新列表
         */
        public void removeAllData() {
            if (coupleListData != null) {
                coupleListData.clear();
                notifyDataSetChanged();
            }
        }

        /**
         * 删除某条数据, 及时刷新列表
         *
         * @param bean
         */
        public void removeList(ContactsBean bean) {
            if (coupleListData == null) {
                return;
            }

            if (bean != null) {
                coupleListData.remove(bean);
                notifyDataSetChanged();
            }
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(TheHeartManageActivity.this)
                        .inflate(R.layout.couple_list_item, null);

                holder = new ViewHolder();
                holder.headIcon = (ImageView) convertView
                        .findViewById(R.id.couple_item_icon);
                holder.nameTv = (TextView) convertView
                        .findViewById(R.id.couple_item_name);
                holder.stateInfo = (TextView) convertView
                        .findViewById(R.id.couple_item_state_info);
                holder.actionBtn = (TextView) convertView
                        .findViewById(R.id.couple_item_btn);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }

            ContactsBean bean = coupleListData.get(position);

            // 头像, 优先显示网络
            if (!TextUtils.isEmpty(bean.getHeadUrl())) {
                BitmapHelper.display(TheHeartManageActivity.this, holder.headIcon,
                        bean.getHeadUrl());
            } else {
                BitmapHelper.display(TheHeartManageActivity.this, holder.headIcon, R.drawable.default_head_yellow);
            }

            // 用户姓名, 如果为空, 显示用户Id
            if (!TextUtils.isEmpty(bean.getName())) {
                holder.nameTv.setText(bean.getName());
            } else {
                holder.nameTv.setText(bean.getPhoneNumber());
            }

            holder.stateInfo.setTextColor(getResources().getColor(
                    R.color.text_color_black_nomal));
            // 根据状态不同, 处理相关逻辑
            holder.actionBtn.setTag(bean);
            switch (bean.getCoupleShip()) {
                case CoupleLogic.COUPLE_STATE_APART:
                    holder.stateInfo.setText(R.string.once_paired);
                    holder.actionBtn.setText(R.string.request_again);

                    // 曾经配对状态, 点击提交申请
                    holder.actionBtn.setOnClickListener(v -> {
                        final ContactsBean bean1 = (ContactsBean) v.getTag();
                        sendRequest(bean1);
                    });

                    break;
                case CoupleLogic.COUPLE_STATE_PAIRED:
                    holder.stateInfo.setText(R.string.paired);
                    holder.stateInfo.setTextColor(getResources().getColor(
                            R.color.text_color_golden));
                    holder.actionBtn.setText(R.string.apart_paired);

                    // 已配对状态, 点击解除配对, 成功之后跳转介绍页
                    holder.actionBtn.setOnClickListener(v -> mCoupleLogic.apartCouple(ToTwooApplication.owner.getPairedId(), new CoupleCallback() {
                        @Override
                        public void onResult(boolean success) {
                            if (success) {
                                ContactsBean bean12 = (ContactsBean) v
                                        .getTag();
                                if (bean12 != null) {
                                    // 更新列表
                                    bean12.setCoupleShip(CoupleLogic.COUPLE_STATE_APART);
                                    if (mAdapter != null) {
                                        mAdapter.notifyDataSetChanged();
                                    }
                                }
                            }
                        }
                    }));

                    break;
                // 待回复状态, 点击回复同意(目前不能拒绝)
                case CoupleLogic.COUPLE_STATE_REPLY:
                    holder.stateInfo.setText(R.string.want_pair);
                    holder.actionBtn.setText(R.string.agree);
                    holder.actionBtn.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            final ContactsBean bean = (ContactsBean) v.getTag();
                            if (bean != null) {
                                mCoupleLogic.replyRequest(bean.getTalkId(), new CoupleCallback() {
                                    @Override
                                    public void onResult(boolean success) {
                                        if (success) {
                                            // 回复成功, 跳转心有灵犀详情页
                                            Intent intent = new Intent(TheHeartManageActivity.this,
                                                    LoveSpacePinkActivity.class);
//                                            intent.putExtra(
//                                                    TheHeartActivity.PAIRED_PERSON_DATA_TAG,
//                                                    bean);
                                            startActivity(intent);
                                            finish();
                                        } else {
                                            // 刷新列表
                                            getCoupleListData();
                                        }
                                    }
                                });
                            }
                        }
                    });

                    break;
                // 请求状态, 点击取消请求
                case CoupleLogic.COUPLE_STATE_REQUEST:
                    holder.stateInfo.setText(R.string.wait_reply);
                    holder.actionBtn.setText(R.string.cancel_request);
                    holder.actionBtn.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            final ContactsBean bean = (ContactsBean) v.getTag();
                            mCoupleLogic.cancelRequest(bean, new CoupleCallback() {

                                @Override
                                public void onResult(boolean success) {
                                    // 取消成功, 重新刷新列表
                                    getCoupleListData();
                                }
                            });

                        }
                    });

                    break;
            }

            return convertView;
        }

        class ViewHolder {
            ImageView headIcon;
            TextView nameTv;
            TextView stateInfo;
            TextView actionBtn;
        }
    }

    /**
     * 发送配对请求请求
     *
     * @param bean
     */
    private void sendRequest(final ContactsBean bean) {
        if (bean != null) {
            mCoupleLogic.sendRequest(bean, success -> {
                if (success) {
                    // 弹窗提示发送成功
                    final CommonMiddleDialog dialog = new CommonMiddleDialog(
                            TheHeartManageActivity.this);
//                    dialog.setTitle(R.string.send_success);
                    dialog.setMessage(R.string.send_success);

                    dialog.setSure(v -> dialog.dismiss());
                    dialog.show();

                    if (mAdapter != null) {
                        mAdapter.notifyDataSetChanged();
                    }

                    // 重新刷新数据
                    getCoupleListData();
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onStateChange(TotwooMessage message) {
        if (message.getTotwooState().equals(CoupleLogic.COUPLE_STATE_REPLY + "")) {
            // 回复成功, 跳转心有灵犀详情页
            Intent tar = new Intent(TheHeartManageActivity.this, LoveSpacePinkActivity.class);
            startActivity(tar);
            finish();
        } else {
            getCoupleListData();
        }
    }

    /**
     * 展示进度框
     *
     * @param show true 为展示, false 为隐藏
     */
    private void showProgressBar(boolean show) {
        if (show) {
            if (progressBar == null) {
                progressBar = new CustomProgressBarDialog(
                        TheHeartManageActivity.this);
            }
            progressBar.show();
        } else {

            if (progressBar != null && progressBar.isShowing()) {
                progressBar.dismiss();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
