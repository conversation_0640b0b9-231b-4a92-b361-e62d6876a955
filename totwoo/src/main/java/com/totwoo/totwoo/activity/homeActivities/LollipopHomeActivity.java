package com.totwoo.totwoo.activity.homeActivities;

import android.os.Bundle;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.CustomReminderFragment;
import com.totwoo.totwoo.fragment.LollipopFragment;
import com.totwoo.totwoo.fragment.LoveFragment;
import com.totwoo.totwoo.fragment.MeFragment;

import java.util.ArrayList;

public class LollipopHomeActivity extends HomeBaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Class<? extends BaseFragment>[] baseFragments = new Class[4];
        baseFragments[0] = LollipopFragment.class;
        baseFragments[1] = LoveFragment.class;
        baseFragments[2] = CustomReminderFragment.class;
        baseFragments[3] = MeFragment.class;

        ArrayList<HomepageBottomInfo> infos = new ArrayList<>();
        infos.add(new HomepageBottomInfo(R.drawable.lollipop_fun_un, R.drawable.lollipop_fun, R.string.fun));
        infos.add(new HomepageBottomInfo(R.drawable.lollipop_xylx_un, R.drawable.lollipop_xylx, R.string.notify));
        infos.add(new HomepageBottomInfo(R.drawable.lollipop_reminder_un, R.drawable.lollipop_reminder, R.string.reminder));
        infos.add(new HomepageBottomInfo(R.drawable.lollipop_me_un, R.drawable.lollipop_me, R.string.user));
        super.setBottomInfo(infos);
        super.setTextColor(getResources().getColor(R.color.bottom_lollipop_select_color), getResources().getColor(R.color.bottom_lollipop_default_color));
        super.setFragmentsAndInitViewpager(baseFragments);
        super.setCurrentFromType(5);
        super.setTotwooIndex(1);
    }
}
