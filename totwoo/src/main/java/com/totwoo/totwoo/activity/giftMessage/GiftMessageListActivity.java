package com.totwoo.totwoo.activity.giftMessage;

import android.animation.Animator;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.blankj.utilcode.util.SizeUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.wish.MLoadMoreView;
import com.totwoo.totwoo.bean.GiftMessageBean;
import com.totwoo.totwoo.bean.GiftMessageReceiverInfo;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.utils.CenterCropRoundCornerTransform;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.SendGiftSuccessDialog;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observer;
import rx.Subscriber;

/**
 * 智能情书列表页面, 有传入参数决定是收到的情书还是发出的情书
 */
public class GiftMessageListActivity extends BaseActivity implements BluetoothManage.ConnectSuccessListener {
    @BindView(R.id.gift_message_list)
    RecyclerView mRecycler;
    @BindView(R.id.gift_message_list_cover)
    RelativeLayout mCoverRl;
    @BindView(R.id.card_open_white)
    ImageView mCardOpenWhiteIv;
    @BindView(R.id.card_open_lottie)
    LottieAnimationView mCardOpenLv;
    @BindView(R.id.card_open_tv)
    TextView mCardOpenTv;
    @BindView(R.id.card_open_close)
    ImageView mCloseIv;
    @BindView(R.id.gift_message_all_content)
    LinearLayout mAllContent;
    @BindView(R.id.gift_message_empty_text)
    TextView mEmptyTv;

    public static final String LIST_RECEIVER = "list_receiver";
    public static final String LIST_SEND = "list_send";
    public static final String SEND_SUCCESS = "send_success";
    private ArrayList<GiftMessageBean> beans;
    private GiftMessageAdapter giftMessageAdapter;
    private int currentPage = 0;
    private int imageHeight = 0;
    private int imageWidth = 0;
    private SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
    private ShakeMonitor mShakeMonitor;

    //    private SoundPool soundPool;
    private String type;
    private boolean isOpened;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gift_message_list);
        ButterKnife.bind(this);
        beans = new ArrayList<>();
        mRecycler.setLayoutManager(new LinearLayoutManager(GiftMessageListActivity.this));
        giftMessageAdapter = new GiftMessageAdapter(R.layout.gift_message_item, beans);
        mRecycler.setAdapter(giftMessageAdapter);

        type = getIntent().getStringExtra(CommonArgs.FROM_TYPE);

        giftMessageAdapter.setLoadMoreView(new MLoadMoreView());
        giftMessageAdapter.setOnItemClickListener((adapter, view, position) -> {
            Intent intent = new Intent(GiftMessageListActivity.this, GiftDataActivity.class);
            if (TextUtils.equals(type, LIST_RECEIVER)) {
                intent.putExtra(CommonArgs.FROM_TYPE, GiftDataActivity.RECEIVER_LIST);
            } else {
                intent.putExtra(CommonArgs.FROM_TYPE, GiftDataActivity.SEND_LIST);
            }
            intent.putExtra(GiftDataActivity.ITEM, beans.get(position));
            startActivity(intent);
        });

//        CommonUtils.setStateBar(this, false);

        giftMessageAdapter.setOnItemLongClickListener(new BaseQuickAdapter.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(BaseQuickAdapter adapter, View view, int position) {
//                final CustomDialog customDialog = new CustomDialog(GiftMessageListActivity.this);
//                customDialog.setMessage(R.string.delete_greeting_card_prompt);
//                customDialog.setTitle(R.string.tips);
//                customDialog.setPositiveButton(v -> {
//                    delete(position);
//                    customDialog.dismiss();
//                });
//                customDialog.setNegativeButton(R.string.give_up, v -> customDialog.dismiss());
//                customDialog.show();

                CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(GiftMessageListActivity.this);
                commonMiddleDialog.setMessage(R.string.delete_greeting_card_prompt);
                commonMiddleDialog.setCancel(R.string.cancel);
                commonMiddleDialog.setSure(v -> {
                    delete(position);
                    commonMiddleDialog.dismiss();
                });
                commonMiddleDialog.show();
                return true;
            }
        });

        giftMessageAdapter.setOnLoadMoreListener(() -> {
            currentPage++;
            getInfo();
        }, mRecycler);

        getInfo();
        imageWidth = CommonUtils.getScreenWidth() - CommonUtils.dip2px(GiftMessageListActivity.this, 114);
        imageHeight = imageWidth * 3 / 4;

        if (TextUtils.equals(type, LIST_RECEIVER)) {
            if (false) {
                mCoverRl.setVisibility(View.GONE);
                mAllContent.setVisibility(View.VISIBLE);
            } else {
                mCoverRl.setVisibility(View.VISIBLE);
                mAllContent.setVisibility(View.GONE);
            }
//            mCardOpenLv.setImageAssetsFolder("lottie_greeting_card/");
//            mCardOpenLv.setAnimation("greeting_card_list.json");
            String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");

            String letterTip = JewelryOnlineDataManager.getInstance().getConnectedJewInfo().getLetter_tip();
//            if (BleParams.isMWJewlery()) {
//                mCardOpenTv.setText(R.string.open_love_msg_33);
//            }else {
            if (!TextUtils.isEmpty(letterTip)) {
                mCardOpenTv.setText(letterTip);
            } else if (TextUtils.isEmpty(jewName)) {
                mCardOpenTv.setText(R.string.receive_card_no_jewelry);
            } else if (BleParams.isSM2(jewName) && !TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_80) && !TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_81)) {
                // 8x 系列新款首饰样式调整, 追加新的文言, 后续可能调整
                mCardOpenTv.setText(R.string.receive_card_info2_touch_sm);
            } else if (BleParams.isTouchJewelry(jewName)) {
                if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {
                    mCardOpenTv.setText(R.string.open_love_msg_33);
                } else {
                    mCardOpenTv.setText(R.string.receive_card_info2_touch);
                }
            } else {
                mCardOpenTv.setText(R.string.receive_card_info2);
            }


            mCloseIv.setOnClickListener(v -> finish());
            initJewelryShake();
        } else if (TextUtils.equals(type, SEND_SUCCESS)) {
            SendGiftSuccessDialog sendGiftSuccessDialog = new SendGiftSuccessDialog(GiftMessageListActivity.this);
            sendGiftSuccessDialog.show();
        }
    }

    private void delete(int position) {
        String giftId = "[" + beans.get(position).getGreetingCardId() + "]";
        if (TextUtils.equals(type, LIST_RECEIVER)) {
            HttpHelper.card.deleteReceiveGift(ToTwooApplication.owner.getTotwooId(), ToTwooApplication.owner.getPhone(), 0, giftId)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Observer<HttpBaseBean>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(GiftMessageListActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean httpBaseBean) {
                            beans.remove(position);
                            giftMessageAdapter.notifyDataSetChanged();
                            if (beans.size() == 0) {
                                mEmptyTv.setVisibility(View.VISIBLE);
                            } else {
                                mEmptyTv.setVisibility(View.GONE);
                            }
                        }
                    });
        } else {
            HttpHelper.card.deleteSentGift(ToTwooApplication.owner.getTotwooId(), 0, giftId)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Observer<HttpBaseBean>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(GiftMessageListActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean httpBaseBean) {
                            beans.remove(position);
                            giftMessageAdapter.notifyDataSetChanged();
                            if (beans.size() == 0) {
                                mEmptyTv.setVisibility(View.VISIBLE);
                            } else {
                                mEmptyTv.setVisibility(View.GONE);
                            }
                        }
                    });
        }

    }

//    private void initShakerListener() {
//        soundPool = new SoundPool(10, AudioManager.STREAM_RING, 5);
//        shakeListener = new ShakeListener(this);
//        shakeListener.setSupportJewelry(false);
//        shakeListener.setOnShakeListener(() -> {
//            // 跳转贺卡制作界面
//            startActivity(new Intent(GiftMessageListActivity.this, SendGiftGalleryActivity.class));
//            // 音效
//            playSound();
//            if (shakeListener != null) {
//                shakeListener.stop();
//            }
//            finish();
//        });
//        shakeListener.start();
//    }

    @Override
    protected void onResume() {
        super.onResume();
        if (TextUtils.equals(type, LIST_RECEIVER) && !isOpened) {
//            shakeListener.start();
            if (mShakeMonitor != null)
                mShakeMonitor.start();

            BluetoothManage.getInstance().stayIn(true);
            BluetoothManage.getInstance().setConnectSuccessListener(this);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        if (TextUtils.equals(type, LIST_RECEIVER) && !isOpened) {
            if (mShakeMonitor != null)
                mShakeMonitor.stop();

            BluetoothManage.getInstance().stayIn(false);
            BluetoothManage.getInstance().setConnectSuccessListener(null);
        }
    }

//    /**
//     * 播放声音
//     */
//    private void playSound() {
//        soundPool.setOnLoadCompleteListener((soundPool, sampleId, status) -> soundPool.play(sampleId, 1, 1, 0, 0, 1));
//        soundPool.load(this, R.raw.qian_sound, 1);
//    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        if (TextUtils.equals(type, LIST_RECEIVER)) {
            setTopTitle(R.string.card_receive);
            setTopRightIcon(R.drawable.add);
            setTopRightOnClick(v -> startActivity(new Intent(GiftMessageListActivity.this, SendGiftGalleryActivity.class)));
        } else {
            setTopTitle(R.string.card_send);
        }
    }

    private void getInfo() {
        if (TextUtils.equals(type, LIST_RECEIVER)) {
            HttpHelper.card.getGiftReceiveList(ToTwooApplication.owner.getTotwooId(), ToTwooApplication.owner.getPhone(), currentPage, 10, 2)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<ArrayList<GiftMessageBean>>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(GiftMessageListActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean<ArrayList<GiftMessageBean>> arrayListHttpBaseBean) {
                            setInfo(arrayListHttpBaseBean);
                        }
                    });
        } else {
            HttpHelper.card.getGiftSendList(ToTwooApplication.owner.getTotwooId(), currentPage, 10, 2)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<ArrayList<GiftMessageBean>>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(GiftMessageListActivity.this, R.string.error_net);
                        }

                        @Override
                        public void onNext(HttpBaseBean<ArrayList<GiftMessageBean>> arrayListHttpBaseBean) {
                            setInfo(arrayListHttpBaseBean);
                        }
                    });
        }
    }

    private void setInfo(HttpBaseBean<ArrayList<GiftMessageBean>> arrayListHttpBaseBean) {
        if (arrayListHttpBaseBean.getErrorCode() == 0) {
            beans.addAll(arrayListHttpBaseBean.getData());
            giftMessageAdapter.notifyDataSetChanged();
            if (arrayListHttpBaseBean.getData() == null || arrayListHttpBaseBean.getData().size() == 0 || arrayListHttpBaseBean.getData().size() < 10) {
                giftMessageAdapter.loadMoreEnd();
            } else {
                giftMessageAdapter.loadMoreComplete();
            }
            if (beans.size() == 0) {
                mEmptyTv.setVisibility(View.VISIBLE);
            } else {
                mEmptyTv.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onConnectSuccessd() {
        mHandler.postDelayed(() -> BluetoothManage.getInstance().stayIn(true), 2000);
    }

    private class GiftMessageAdapter extends BaseQuickAdapter<GiftMessageBean, BaseViewHolder> {

        public GiftMessageAdapter(int layoutResId, @Nullable List<GiftMessageBean> data) {
            super(layoutResId, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, GiftMessageBean item) {
//            View mDownLine = helper.getView(R.id.gift_down_line_view);
            ImageView mMainIv = helper.getView(R.id.gift_item_main_iv);
            ImageView mPointIv = helper.getView(R.id.gift_item_point_iv);
            TextView mDateTv = helper.getView(R.id.gift_item_date_tv);
            View mUpLine = helper.getView(R.id.gift_up_line_view);
//            int lineHeight = CommonUtils.dip2px(GiftMessageListActivity.this, 300);
//            RelativeLayout.LayoutParams lineLayoutParams = new RelativeLayout.LayoutParams(CommonUtils.dip2px(GiftMessageListActivity.this, 1), lineHeight);
//            lineLayoutParams.addRule(RelativeLayout.BELOW, R.id.gift_item_point_iv);
//            lineLayoutParams.setMargins(CommonUtils.dip2px(GiftMessageListActivity.this, 38), 0, 0, 0);
//            mDownLine.setLayoutParams(lineLayoutParams);

            RelativeLayout.LayoutParams mainIvLayoutParams = new RelativeLayout.LayoutParams(imageWidth, imageHeight);
            mainIvLayoutParams.addRule(RelativeLayout.BELOW, R.id.gift_item_date_tv);
            mainIvLayoutParams.addRule(RelativeLayout.ALIGN_PARENT_END, RelativeLayout.TRUE);
            mainIvLayoutParams.setMargins(0, CommonUtils.dip2px(GiftMessageListActivity.this, 20), CommonUtils.dip2px(GiftMessageListActivity.this, 35), 0);
            mMainIv.setLayoutParams(mainIvLayoutParams);


            String date = "";
            String lastDate = "";
            if (TextUtils.equals(type, LIST_RECEIVER)) {
                date = getDate(item.getReceiveTime());
                if (helper.getAdapterPosition() != 0) {
                    lastDate = getDate(beans.get(helper.getAdapterPosition() - 1).getReceiveTime());
                }
            } else {
                date = getDate(item.getSendTime());
                if (helper.getAdapterPosition() != 0) {
                    lastDate = getDate(beans.get(helper.getAdapterPosition() - 1).getSendTime());
                }
            }
            SpannableString spannableString = new SpannableString(date);
            helper.setText(R.id.gift_item_date_tv, setStyle(spannableString));

            RelativeLayout.LayoutParams mDateLayoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
            if (helper.getAdapterPosition() == 0) {
                mUpLine.setVisibility(View.INVISIBLE);
                mPointIv.setVisibility(View.VISIBLE);
                mDateTv.setVisibility(View.VISIBLE);
                mDateLayoutParams.setMargins(CommonUtils.dip2px(GiftMessageListActivity.this, 64), CommonUtils.dip2px(GiftMessageListActivity.this, 12), 0, 0);
                mDateTv.setLayoutParams(mDateLayoutParams);
            } else if (TextUtils.equals(date, lastDate)) {
                mUpLine.setVisibility(View.GONE);
                mPointIv.setVisibility(View.GONE);
                mDateTv.setVisibility(View.GONE);
            } else {
                mUpLine.setVisibility(View.VISIBLE);
                mPointIv.setVisibility(View.VISIBLE);
                mDateTv.setVisibility(View.VISIBLE);
                mDateLayoutParams.setMargins(CommonUtils.dip2px(GiftMessageListActivity.this, 64), CommonUtils.dip2px(GiftMessageListActivity.this, 12), 0, 0);
                mDateTv.setLayoutParams(mDateLayoutParams);
            }

            if (TextUtils.equals(type, LIST_RECEIVER)) {
                helper.setGone(R.id.gift_item_sender_name_tv, true);

                helper.setText(R.id.gift_item_sender_count_tv, "By: " + item.getSenderName());
                helper.setText(R.id.gift_item_sender_name_tv, getString(R.string.gift_list_sent_by_time, getDate(item.getSendTime())));
            } else {
                helper.setGone(R.id.gift_item_sender_name_tv, false);

                if (item.getReceivers() != null && item.getReceivers().size() > 0) {
//                    helper.setText(R.id.gift_item_sender_count_tv, R.string.gift_list_send_to);
                    StringBuilder stringBuilder = new StringBuilder();
                    for (GiftMessageReceiverInfo giftMessageReceiverInfo : item.getReceivers()) {
                        stringBuilder.append(giftMessageReceiverInfo.getReceiverName());
                        stringBuilder.append("  |  ");
                    }
                    stringBuilder.deleteCharAt(stringBuilder.lastIndexOf("|"));
                    String string = stringBuilder.toString().trim();
                    helper.setText(R.id.gift_item_sender_count_tv, getString(R.string.gift_list_send_to, string));

                } else {
                    helper.setText(R.id.gift_item_sender_count_tv, getString(R.string.gift_list_send_to, ""));
                }
            }

            if (item.getGreetingCardType() == 2 || item.getGreetingCardType() == 3) {
                setImageUrl(item.getGreetingCardData().getImageUrl(), mMainIv);
                helper.setVisible(R.id.gift_data_play_iv, false);
            } else if (item.getGreetingCardType() == 4 || item.getGreetingCardType() == 5) {
                mMainIv.setImageResource(R.drawable.gift_voice_info);
                helper.setVisible(R.id.gift_data_play_iv, true);

                setRelaBottomMargin(helper.getView(R.id.gift_data_play_iv), 0f);

            } else if (item.getGreetingCardType() == 8 || item.getGreetingCardType() == 9) {
                setImageUrl(item.getGreetingCardData().getVedioPreviewImageUrl(), mMainIv);
                helper.setVisible(R.id.gift_data_play_iv, true);
                setRelaBottomMargin(helper.getView(R.id.gift_data_play_iv), -20f);

            }
        }


        private void setRelaBottomMargin(View view, float dpValue) {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) view.getLayoutParams();
            params.bottomMargin = SizeUtils.dp2px(dpValue); // 设置新的上边距
            view.setLayoutParams(params);
            view.requestLayout();
        }
    }

    private void setImageUrl(String imageUrl, ImageView mMainIv) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.gift_placeholder);
        Glide.with(this).load(BitmapHelper.checkRealPath(imageUrl)).apply(RequestOptions.bitmapTransform(new CenterCropRoundCornerTransform(40))).apply(options).into(mMainIv);
    }

    private String getDate(long millions) {
        Date date = new Date(millions * 1000);
        return format.format(date);
    }

    //"yyyy/MM/dd"
    private String getTransformDate(String date) {
        StringBuilder stringBuilder = new StringBuilder();
        int month = Integer.valueOf(date.substring(5, 7)) - 1;
        stringBuilder.append(getResources().getStringArray(R.array.month_names_en)[month]);
        stringBuilder.append(" ");
        stringBuilder.append(date.substring(8, 10));
        stringBuilder.append(", ");
        stringBuilder.append(date.substring(0, 4));
        return stringBuilder.toString();
    }

    private SpannableString setStyle(SpannableString spannableString) {
        int index = spannableString.length() - 2;
        int endIndex = spannableString.length();
        spannableString.setSpan(new AbsoluteSizeSpan(24, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private void initJewelryShake() {
        mShakeMonitor = new ShakeMonitor(this);
        mShakeMonitor.isEnablePhoneShake(false);
        BluetoothManage.getInstance().connectedStatus();
        // 设置摇首饰的监听
        mShakeMonitor.setOnEventListener((type) -> {
            //弹出信纸
            openCardAnim();
            if (mShakeMonitor != null) {
                mShakeMonitor.stop();
            }

            isOpened = true;
            BluetoothManage.getInstance().stayIn(false);
            BluetoothManage.getInstance().setConnectSuccessListener(null);
        });
    }

    private void openCardAnim() {
        AlphaAnimation whitePageAnim = new AlphaAnimation(0, 1.0f);
        whitePageAnim.setFillAfter(true);
        whitePageAnim.setDuration(1000);

        AlphaAnimation alphaAnimation = new AlphaAnimation(0, 1.0f);
        alphaAnimation.setFillAfter(true);
        alphaAnimation.setDuration(1000);

        mCardOpenLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mCardOpenWhiteIv.setVisibility(View.VISIBLE);
                mCardOpenWhiteIv.startAnimation(whitePageAnim);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        whitePageAnim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

                mAllContent.setVisibility(View.VISIBLE);
                mAllContent.startAnimation(alphaAnimation);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        alphaAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mCoverRl.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mCardOpenLv.playAnimation();
    }

    private SpannableString setStyle() {
        String string = getString(R.string.receive_card_info2);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = "敲击2下";
        String sub2 = "情书";
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        index = string.indexOf(sub2);
        endIndex = index + sub2.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private SpannableString setStyleTouch() {
        String string = getString(R.string.receive_card_info2_touch);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = "触摸兔兔正面";
        String sub2 = "情书";
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        index = string.indexOf(sub2);
        endIndex = index + sub2.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    private SpannableString setStyleTouchNoVibrate() {
        String string = getString(R.string.receive_card_info2_touch_no_vibrate);
        SpannableString spannableString = new SpannableString(string);
        String sub1 = "轻触兔兔至闪光";
        String sub2 = "开启藏在兔兔里的情书";
        int index = string.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        index = string.indexOf(sub2);
        endIndex = index + sub2.length();
        spannableString.setSpan(new AbsoluteSizeSpan(18, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff3a6f64")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 确保清理 ShakeMonitor 和 BluetoothManage 监听器
        if (mShakeMonitor != null) {
            mShakeMonitor.stop();
            mShakeMonitor = null;
        }
        BluetoothManage.getInstance().setConnectSuccessListener(null);
    }
}
