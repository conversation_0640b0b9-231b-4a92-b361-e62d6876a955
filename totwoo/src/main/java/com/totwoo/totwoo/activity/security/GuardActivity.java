//package com.totwoo.totwoo.activity.security;
//
//import android.content.Intent;
//import android.graphics.BitmapFactory;
//import android.location.Location;
//import android.net.Uri;
//import android.os.Bundle;
//import android.os.Message;
//import android.provider.Settings;
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.constraintlayout.widget.ConstraintLayout;
//
//import com.amap.api.maps.AMap;
//import com.amap.api.maps.CameraUpdateFactory;
//import com.lk.mapsdk.map.mapapi.map.LKMap;
//import com.lk.mapsdk.map.mapapi.map.MapView;
//import com.amap.api.maps.model.LatLng;
//import com.amap.api.maps.model.MyLocationStyle;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.activity.BaseActivity;
//import com.totwoo.totwoo.bean.GuardStartBean;
//import com.totwoo.totwoo.bean.SafeTypeBean;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.utils.CommonUtils;
//import com.totwoo.totwoo.utils.DesUtil;
//import com.totwoo.totwoo.utils.FileUtils;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.NetUtils;
//import com.totwoo.totwoo.utils.PermissionUtil;
//import com.totwoo.totwoo.utils.ShareUtilsSingleton;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.utils.TrackEvent;
//import com.totwoo.totwoo.utils.WeakReferenceHandler;
//import com.totwoo.totwoo.widget.CommonMiddleDialog;
//import com.totwoo.totwoo.widget.CommonShareDialog;
//import com.totwoo.totwoo.widget.CommonShareType;
//import com.totwoo.totwoo.widget.CustomDialog;
//import com.totwoo.totwoo.widget.SafeTimeSelectDialog;
//import com.umeng.analytics.MobclickAgent;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//import rx.Observer;
//import rx.Subscriber;
//
//public class GuardActivity extends BaseActivity{
//    @BindView(R.id.guard_mapview)
//    MapView mMapView;
//    @BindView(R.id.guard_start_tv)
//    TextView mStartTv;
//    @BindView(R.id.guard_bottom_start_iv)
//    ImageView mStartIv;
//    @BindView(R.id.guard_bottom_start_tv)
//    TextView mStartInfoTv;
//    @BindView(R.id.safe_guard_discount_tv)
//    TextView mGuardDiscountTv;
//    @BindView(R.id.guard_share_iv)
//    ImageView mShareIv;
//    @BindView(R.id.guard_discount_cl)
//    ConstraintLayout mGuardDiscountCl;
//
//    private AMap aMap;
//    private LatLng myLocationLatLng;
//    private CommonShareDialog shareDialog;
//    private CommonShareDialog guardCancelShareDialog;
//    private CommonMiddleDialog cancelGuardDialog;
//    private boolean isGuardMode = false;
//    private DisCountHandler handler;
//    private String id;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_guard);
//        ButterKnife.bind(this);
//        mMapView.onCreate(savedInstanceState);
//        setMapView();
//        handler = new DisCountHandler(GuardActivity.this);
//
//        setTopLeftIcon(R.drawable.back_icon_black);
//        setTopTitle(R.string.safe_guard);
//        setTopTitleColor(getResources().getColor(R.color.black));
//
//        setTopbarBackground(R.drawable.shape_common_top_bg_trans);
////        setExtraHeight();
//        CommonUtils.setStateBar(GuardActivity.this, false);
//        setTopLeftOnclik(v -> finish());
//        getGuardState();
//    }
//
//    private void getGuardState() {
//        HttpHelper.safeService.getGuardState(2001)
//                .compose(HttpHelper.rxSchedulerHelper())
//                .subscribe(new Observer<HttpBaseBean<SafeTypeBean>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        ToastUtils.showShort(GuardActivity.this, getString(R.string.error_net));
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<SafeTypeBean> safeTypeBeanHttpBaseBean) {
//                        if (safeTypeBeanHttpBaseBean.getErrorCode() == 0) {
//                            if(TextUtils.equals(safeTypeBeanHttpBaseBean.getData().getType(),"GUARD")){
//                                id = safeTypeBeanHttpBaseBean.getData().getId();
//                                startGuardMode(Long.valueOf(safeTypeBeanHttpBaseBean.getData().getSurplus_time()),id);
//                            }
//                        }
//                    }
//                });
//    }
//
//    @OnClick({R.id.guard_local_iv,R.id.guard_share_iv,R.id.guard_start_tv})
//    protected void onClick(View view){
//        switch (view.getId()){
//            case R.id.guard_local_iv:
//                if (myLocationLatLng != null) {
//                    //可以手动设置回自己的坐标位置
//                    aMap.moveCamera(CameraUpdateFactory.changeLatLng(myLocationLatLng));
//                }
//                break;
//            case R.id.guard_share_iv:
//                showShareDialog();
//                break;
//            case R.id.guard_start_tv:
//                if(isGuardMode){
//                    cancelGuard();
//                }else{
//                    if (!isEnableLocate()) {
//                        return;
//                    }
//                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FINE_GUARD_ON);
//                    showSelectTimeDialog();
//                }
//                break;
//        }
//    }
//
//    private void startGuardMode(long sec,String guard_id){
//        ToTwooApplication.mService.startLocation(sec,guard_id);
//        startTimer(sec);
//        mGuardDiscountCl.setVisibility(View.VISIBLE);
//        mShareIv.setVisibility(View.VISIBLE);
//        mStartTv.setText(R.string.safe_guard_bottom_off);
//        mStartIv.setImageResource(R.drawable.guard_end_icon);
//        mStartInfoTv.setText(R.string.safe_guard_bottom_off_text);
//        isGuardMode = true;
//    }
//
//    private void cancelGuardMode(){
//        ToTwooApplication.mService.endLocation();
//        mGuardDiscountCl.setVisibility(View.GONE);
//        mShareIv.setVisibility(View.GONE);
//        mStartTv.setText(R.string.safe_guard_bottom_on);
//        mStartIv.setImageResource(R.drawable.guard_local_icon);
//        mStartInfoTv.setText(R.string.safe_guard_bottom_on_text);
//        isGuardMode = false;
//        cancelTimer();
//    }
//
//    private void cancelTimer() {
//
//    }
//
//    @Override
//    public void onResume() {
//        super.onResume();
//        //在activity执行onResume时执行mMapView.onResume()，重新绘制加载地图
//        mMapView.onResume();
//    }
//
//    @Override
//    public void onPause() {
//        super.onPause();
//        //在activity执行onPause时执行mMapView.onPause()，暂停地图的绘制
//        mMapView.onPause();
//    }
//
//    @Override
//    public void onDestroy() {
//        super.onDestroy();
//        handler.removeCallbacksAndMessages(null);
//        aMap.clear();
//        mMapView.onDestroy();
//    }
//
//    private void setMapView() {
//        if (aMap == null) {
//            aMap = mMapView.getMap();
//        }
//        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
//            aMap.setMapLanguage("zh_ch");
//        } else {
//            aMap.setMapLanguage("en");
//        }
//        MyLocationStyle myLocationStyle;
//        myLocationStyle = new MyLocationStyle();//初始化定位蓝点样式类myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE);//连续定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。（1秒1次定位）如果不设置myLocationType，默认也会执行此种模式。
//        myLocationStyle.interval(5000); //设置连续定位模式下的定位间隔，只在连续定位模式下生效，单次定位模式下不会生效。单位为毫秒。
//        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_FOLLOW_NO_CENTER);
//        myLocationStyle.showMyLocation(true);
//        aMap.setMyLocationStyle(myLocationStyle);//设置定位蓝点的Style
////        aMap.getUiSettings().setMyLocationButtonEnabled(true);//设置默认定位按钮是否显示，非必需设置。
//        aMap.setMyLocationEnabled(true);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false。
//        aMap.setOnMyLocationChangeListener(this);
//        aMap.getUiSettings().setZoomControlsEnabled(false);
//        aMap.moveCamera(CameraUpdateFactory.zoomTo(16));
//
//        if (!NetUtils.isConnected(ToTwooApplication.baseContext)) {
//            ToastUtils.showShort(ToTwooApplication.baseContext, getString(R.string.error_net));
//        }
//    }
//
//    @Override
//    public void onMyLocationChange(Location location) {
//        myLocationLatLng = new LatLng(location.getLatitude(), location.getLongitude());
//    }
//
//    private SafeTimeSelectDialog safeTimeSelectDialog;
//
//    private void showSelectTimeDialog() {
//        if (safeTimeSelectDialog == null) {
//            safeTimeSelectDialog = new SafeTimeSelectDialog(GuardActivity.this);
//            safeTimeSelectDialog.setSureClickListener(this::startGuard);
//        } else {
//            safeTimeSelectDialog.resetSeletion();
//        }
//        safeTimeSelectDialog.show();
//    }
//
//    private void startGuard(String hour, String min) {
//        HttpHelper.safeService.startGuard(hour + ":" + min, myLocationToServerString())
//                .compose(HttpHelper.rxSchedulerHelper())
//                .subscribe(new Subscriber<HttpBaseBean<GuardStartBean>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        ToastUtils.showShort(GuardActivity.this, R.string.error_net);
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<GuardStartBean> guardStartBeanHttpBaseBean) {
//                        if (guardStartBeanHttpBaseBean.getErrorCode() == 0) {
//                            id = guardStartBeanHttpBaseBean.getData().getId();
//                            startGuardMode(Integer.valueOf(hour) * 3600 + Integer.valueOf(min) * 60,id);
//                            safeTimeSelectDialog.dismiss();
//                            showShareDialog();
//                        }
//                    }
//                });
//    }
//
//    private long endTime;
//    private long perTime = 500;
//    private int count;
//
//    private void startTimer(long sec) {
//        endTime = sec * 1000 + System.currentTimeMillis();
//        showTime();
//        handler.sendEmptyMessage(0);
//    }
//
//    public class DisCountHandler extends WeakReferenceHandler<GuardActivity> {
//
//        public DisCountHandler(GuardActivity guardActivity) {
//            super(guardActivity);
//        }
//
//        @Override
//        public void handleLiveMessage(Message msg) {
//            if (endTime - System.currentTimeMillis() > perTime) {
//                showTime();
//                handler.sendEmptyMessageDelayed(0, perTime);
//                count++;
////                if (count % 120 == 0) {
////                    timingGuard();
////                }
//            } else {
//                mGuardDiscountTv.setText("00 : 00 : 00");
//                cancelGuardMode();
//            }
//        }
//    }
//
//    private void showTime() {
//        long time = endTime - System.currentTimeMillis();
//        int sec = (int) (time / 1000 % 60);
//        int min = (int) (time / 1000 / 60 % 60);
//        int hour = (int) (time / 1000 / 60 / 60);
//        mGuardDiscountTv.setText(CommonUtils.getZeroStart(hour) + " : " + CommonUtils.getZeroStart(min) + " : " + CommonUtils.getZeroStart(sec));
//    }
//
////    private void timingGuard() {
////        HttpHelper.safeService.timingGuard(myLocationToServerString())
////                .compose(HttpHelper.rxSchedulerHelper())
////                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
////                    @Override
////                    public void onCompleted() {
////
////                    }
////
////                    @Override
////                    public void onError(Throwable e) {
////
////                    }
////
////                    @Override
////                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
////
////                    }
////                });
////    }
//
//    private String myLocationToServerString() {
//        return myLocationLatLng.longitude + "," + myLocationLatLng.latitude;
//    }
//
//    private void cancelGuard() {
//        if (cancelGuardDialog == null) {
//            cancelGuardDialog = new CommonMiddleDialog(GuardActivity.this);
//            cancelGuardDialog.setMessage(R.string.safe_guard_stop_title);
//            cancelGuardDialog.setSure(v -> {
//                HttpHelper.safeService.cancelGuard(2001)
//                        .compose(HttpHelper.rxSchedulerHelper())
//                        .subscribe(new Subscriber<HttpBaseBean<Object>>() {
//                            @Override
//                            public void onCompleted() {
//
//                            }
//
//                            @Override
//                            public void onError(Throwable e) {
//                                ToastUtils.showShort(GuardActivity.this, R.string.error_net);
//                            }
//
//                            @Override
//                            public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
//                                if (objectHttpBaseBean.getErrorCode() == 0) {
//                                    cancelGuardMode();
//                                    showGuardCancelDialog();
//                                }
//                            }
//                        });
//                cancelGuardDialog.dismiss();
//            });
//            cancelGuardDialog.setCancel(R.string.cancel);
//
//        }
//        cancelGuardDialog.show();
//    }
//
//    private void showShareDialog() {
//        if (shareDialog == null) {
//
//            final String title = getString(R.string.safe_guard_share_title);
//            final String content = getString(R.string.safe_guard_share_content);
//
//            List<CommonShareType> types = new ArrayList<>();
//            types.add(CommonShareType.WECHAT);
//            types.add(CommonShareType.MESSAGE);
//            shareDialog = new CommonShareDialog(GuardActivity.this, types, v -> {
//                switch ((CommonShareType) v.getTag()) {
//                    case WECHAT:
//                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FINE_GUARD_ON_SHARE_WECHAT);
//                        ShareUtilsSingleton.getInstance().shareUrlToWechat(title, content, getImagePath(), getGuardUrl());
//                        shareDialog.dismiss();
//                        break;
//                    case MESSAGE:
//                        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FINE_GUARD_ON_SHARE_MESSAGE);
//                        shareGuardByMessage();
//                        shareDialog.dismiss();
//                        break;
//                }
//
//            });
//            shareDialog.setCustomTitle(getResources().getString(R.string.safe_guard_share_info));
//        }
//        shareDialog.show();
//    }
//
//    private void showGuardCancelDialog() {
//        String message = getString(R.string.cancel_guard_share_message);
//        if (guardCancelShareDialog == null) {
//            List<CommonShareType> types = new ArrayList<>();
//            types.add(CommonShareType.WECHAT);
//            types.add(CommonShareType.MESSAGE);
//            guardCancelShareDialog = new CommonShareDialog(GuardActivity.this, types, v -> {
//                switch ((CommonShareType) v.getTag()) {
//                    case WECHAT:
//                        ShareUtilsSingleton.getInstance().shareTextToWechat(message);
//                        guardCancelShareDialog.dismiss();
//                        break;
//                    case MESSAGE:
//                        shareGuardCancelByMessage();
//                        guardCancelShareDialog.dismiss();
//                        break;
//                }
//            });
//            guardCancelShareDialog.setCustomTitle(getResources().getString(R.string.cancel_guard_share_info));
//        }
//        guardCancelShareDialog.show();
//    }
//
//    protected void shareGuardByMessage() {
//        Uri smsToUri = Uri.parse("smsto:");
//        Intent intent = new Intent(Intent.ACTION_SENDTO, smsToUri);
//        String text = getString(R.string.safe_guard_share_msg) + getGuardUrl();
//        intent.putExtra("sms_body", text);
//        startActivity(intent);
//    }
//
//    protected void shareGuardCancelByMessage() {
//        Uri smsToUri = Uri.parse("smsto:");
//        Intent intent = new Intent(Intent.ACTION_SENDTO, smsToUri);
//        String text = getString(R.string.cancel_guard_share_message);
//        intent.putExtra("sms_body", text);
//        startActivity(intent);
//    }
//
//    private String getImagePath() {
//        return FileUtils.saveBitmapFromSDCard(BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.icon_share_temp),
//                "totwoo_cache_img_share_logo");
//    }
//
//    private String getGuardUrl() {
//        int language = Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext) ? 1 : 0;
//        return "http://manage.totwoo.com/Safety/t?t=" + DesUtil.getPhoneNumberSign(ToTwooApplication.owner.getTotwooId()) + "&cn=" + language + "&id=" +id;
//    }
//
//    private boolean isEnableLocate() {
//        if (!hasLocationPermission()) {
//            return false;
//        } else if (!CommonUtils.isLocServiceEnable(GuardActivity.this)) {
//            showLocationDenyDialog();
//            return false;
//        }
//        return true;
//    }
//
//    private boolean hasLocationPermission() {
//        return PermissionUtil.hasLocationPermission(GuardActivity.this);
//    }
//
//    private CustomDialog locationCustomDialog;
//
//    private void showLocationDenyDialog() {
//        if (locationCustomDialog == null) {
//            locationCustomDialog = new CustomDialog(GuardActivity.this);
//            locationCustomDialog.setTitle(R.string.tips);
//            locationCustomDialog.setMessage(R.string.gps_request_hint);
//            locationCustomDialog.setPositiveButton(R.string.set_open_hint, v -> startActivity(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)));
//        }
//        locationCustomDialog.show();
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
//    }
//}
