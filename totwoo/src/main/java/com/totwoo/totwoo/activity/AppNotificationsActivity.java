package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ease.adapter.BaseAdapter;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.AppNotifyAdapter;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.holderBean.AppNotifyBean;
import com.totwoo.totwoo.holder.AppNotifyListHeader;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomMiddleTextDialog;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 首饰提醒方式设置
 * <p>
 * Created by huanggaowei on 16/8/24.
 */
public class AppNotificationsActivity extends BaseActivity implements SubscriberListener {

    public static String APP_NOTIFY_REMIND_PACKAGES = "app_notify_remind_packages";


    private static final int REQUEST_NOTIFICATION = 1;
    public static String IS_OPEN_OUTSIDE = "is_open_outside";

    @BindView(R.id.notify_app_rclv)
    RecyclerView mNotifyAppRclv;

    private JewelryNotifyModel mJewelryNotifyModel;

    private ArrayList<String> mNotifyApps;

    private AppNotifyAdapter mAppNotifyAdapter;

    private Gson mGson = new Gson();
    private AppNotifyListHeader header;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_app_notifications);

        ButterKnife.bind(this);
        EventBus.getDefault().register(this);
        InjectUtils.injectOnlyEvent(this);
        initData();
//        toggleNotificationListenerService();
        PermissionUtil.hasInstallPackagesPermission(this);
    }


    boolean isOpenOutside = false;

    private void initData() {
        mNotifyAppRclv.setLayoutManager(new LinearLayoutManager(this));
        mAppNotifyAdapter = new AppNotifyAdapter(this);
        mAppNotifyAdapter.setHeaderDelegate(new BaseAdapter.HeaderDelegate() {
            @Override
            public RecyclerView.ViewHolder onCreateHeader(ViewGroup parent, int viewType) {
                header = AppNotifyListHeader.create(parent);
                header.setOnSwitchChangeListener(new AppNotifyListHeader.OnSwitchChangeListener() {
                    @Override
                    public void onChange(boolean isOpen) {

                        if (mAppNotifyAdapter != null) {
                            mAppNotifyAdapter.hideAllItem(!isOpen);
                            if (!mNotifyAppRclv.isComputingLayout()) {
                                mAppNotifyAdapter.notifyDataSetChanged();
                            }
                            if(isOpen){
                                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.APPREMINDER_OPEN);
                            }
                        }
                    }
                });
                if (getIntent().getBooleanExtra(IS_OPEN_OUTSIDE, false) && !isOpenOutside) {
                    isOpenOutside = true;
                    header.switchClick();
                }
                return header;
            }

            @Override
            public void onBindHeader(RecyclerView.ViewHolder holder, int position) {
                ((AppNotifyListHeader) holder).binding(null);
            }

            @Override
            public int getHeaderCount() {
                return 1;
            }

            @Override
            public int getHeaderType(int position) {
                return 0;
            }

            @Override
            public boolean isHeaderType(int viewType) {
                return viewType == 0;
            }
        });
        mNotifyAppRclv.setAdapter(mAppNotifyAdapter);

        mNotifyApps = mGson.fromJson(PreferencesUtils.getString(this,
                APP_NOTIFY_REMIND_PACKAGES, ""), new TypeToken<List<String>>() {
        }.getType());

        if (mNotifyApps == null) {
            mNotifyApps = new ArrayList<>();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    CustomMiddleTextDialog customMiddleTextDialog;
    private void initDialog() {
        customMiddleTextDialog = new CustomMiddleTextDialog(this);
        customMiddleTextDialog.setTitleTv("请依次检查确认", true);
        customMiddleTextDialog.setInfoText("1.网络和兔兔当前都是正常连接状态\n" +
                "\n" +
                "2.已经收到了消息通知，如果没收到，查看[设置]里是否已开启totwoo的通知权限\n" +
                "\n" +
                "3.如果微信或QQ同时在电脑和手机上登录了，务必退出电脑端登录\n" +
                "\n" +
                "4.手机通知栏里收到的消息是否过多，过多的话建议全部清除后再重新接收");
        customMiddleTextDialog.setConfirmTv("我知道了", v -> customMiddleTextDialog.dismiss());
        customMiddleTextDialog.setTextAdd("还是不提醒怎么办？>", v -> {
            WebViewActivity.loadUrl(AppNotificationsActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_HELP), false);
            customMiddleTextDialog.dismiss();
        });
    }

    @Override
    protected void initTopBar() {
        setTopTitle(Apputils.getUpStar(getString(R.string.notify_remind_app_title)));

        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finishAndNotify());
        if (Apputils.systemLanguageIsChinese(AppNotificationsActivity.this)) {
            setTopRightString("不提醒?");
            setTopRightOnClick(v -> customMiddleTextDialog.show());
            initDialog();
        }
        super.initTopBar();
    }

    private void finishAndNotify() {
        finish();
    }

    @Override
    protected void onPause() {
        super.onPause();
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_APP_NOTIFICATION_ACTIVITY_FINISH, null);
    }

    @Override
    public void onBackPressed() {
        finishAndNotify();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updataNotifyAppInfo(AppNotifyBean appNotifyBean) {
        if (appNotifyBean.isAllow()) {
            if (mNotifyApps.contains(appNotifyBean.getPackageName())) {
                return;
            }
            mNotifyApps.add(appNotifyBean.getPackageName());
        } else {
            if (!mNotifyApps.remove(appNotifyBean.getPackageName())) {
                return;
            }
        }
        PreferencesUtils.put(this, APP_NOTIFY_REMIND_PACKAGES, mGson.toJson(mNotifyApps));
    }

//    private void toggleNotificationListenerService() {
//        PackageManager pm = getPackageManager();
//        pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
//                PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
//
////        pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
////                PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
//    }

    private void showOpenNotifyPermission() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(AppNotificationsActivity.this);
        commonMiddleDialog.setMessage(R.string.open_notify_permission_message);
        commonMiddleDialog.setSure(v -> {
            try {
                Intent intent = new Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS);
                startActivityForResult(intent, REQUEST_NOTIFICATION);
                commonMiddleDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    @EventInject(eventType = S.E.E_APP_NOTIFICATION_OPEN, runThread = TaskType.UI)
    public void onShouldOpen(EventData data) {
        showOpenNotifyPermission();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_NOTIFICATION) {
            if (!CommonUtils.isNotificationServiceEnabled())
                ToastUtils.showShort(AppNotificationsActivity.this, "通知权限获取失败");
            else
                header.switchClick();
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        for (String permission : permissions) {

        }

        for (int grantResult : grantResults) {

        }
//        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults,this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
