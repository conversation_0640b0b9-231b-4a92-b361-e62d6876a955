package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.blankj.utilcode.util.BarUtils;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.utils.ApiException;
import com.totwoo.totwoo.utils.CaptchaController;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class ForgetPasswordPhoneActivity extends BaseActivity implements SubscriberListener {
    public static final String PHONENUMBER = "phoneNumber";
    @BindView(R.id.password_country_code_tv)
    TextView mCountryCodeTv;
    @BindView(R.id.password_phone_et)
    EditText password_phone_et;
    private String country_code_value;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_forget_password_phone);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        BarUtils.setStatusBarLightMode(this, true);

        country_code_value = PreferencesUtils.getString(ToTwooApplication.baseContext,
                CommonArgs.COUNTRY_CODE_KEY,
                Apputils.getSystemLanguageCountryCode(this));

        setCountryCode();

        String phoneNumber = getIntent().getStringExtra(PHONENUMBER);
        if (!TextUtils.isEmpty(phoneNumber)) {
            password_phone_et.setText(phoneNumber);
            password_phone_et.setSelection(phoneNumber.length());
        }
        mHandler.postDelayed(() -> {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

            inputMethodManager.toggleSoftInput(0,
                    InputMethodManager.HIDE_NOT_ALWAYS);
            password_phone_et.requestFocus();
        }, 100);
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopbarBackground(Color.TRANSPARENT);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
    }

    @OnClick({R.id.forget_password_next_iv, R.id.password_country_code_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.password_country_code_tv:
                Intent intent = new Intent(this, CountryCodeListActivity.class);
                startActivityForResult(intent, 0);
                // 切换动画
                overridePendingTransition(R.anim.activity_fade_in,
                        R.anim.activity_fade_out);
                break;
            case R.id.forget_password_next_iv:
                String phone = password_phone_et.getText().toString();
                if (TextUtils.isEmpty(phone)) {
                    ToastUtils.showShort(this, R.string.error_invalid_phone);
                } else if (!isPhoneValid(phone)) {
                    ToastUtils.showShort(this, R.string.error_incorrect_phone);
                } else {
                    CommonUtils.phoneSmsPre0CheckAndDo(this, country_code_value, phone, () -> {
                        CaptchaController.getInstance().checkAndShowCaptcha(this, "pwd",country_code_value + phone, country_code_value, (ret, ticket, randomStr) -> {
                            if (ret == 0) {
                                requestSms(phone, ticket, randomStr);
                            }
                        });
                    });
                }
        }
    }

    private void requestSms(String phone, String ticket, String randomStr) {
        int time = (int) (System.currentTimeMillis() / 1000);
        String sourceStr = time + "+" + (country_code_value + phone) + "+" + "totwoo_safe_202311";

        String firmwareType = null;
        if (ticket != null && ticket.startsWith(BleParams.COMMON_JEWELEY_PRE)) {
            firmwareType = ticket;
            ticket = "";
        }

        HttpHelper.loginV3Service.getSmsT(country_code_value + phone, time, "pwd", CommonUtils.md5(sourceStr), firmwareType, ticket, randomStr)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<String>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showLong(ForgetPasswordPhoneActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            startActivity(new Intent(ForgetPasswordPhoneActivity.this, ForgetPasswordCodeActivity.class)
                                    .putExtra(ForgetPasswordCodeActivity.COUNTRY_CODE_VALUE, country_code_value)
                                    .putExtra(ForgetPasswordCodeActivity.PHONENUMBER, phone));
//                                    ToastUtils.showLong(ForgetPasswordPhoneActivity.this, R.string.vercode_has_send);
                        } else if (stringHttpBaseBean.getErrorCode() == 800) {
                            // 临时验证码
                            String msg = stringHttpBaseBean.getErrorMsg();
                            ToastUtils.showLong(ForgetPasswordPhoneActivity.this, msg);

                            String code = CommonUtils.extractVerCode(msg);

                            startActivity(new Intent(ForgetPasswordPhoneActivity.this, ForgetPasswordCodeActivity.class)
                                    .putExtra(ForgetPasswordCodeActivity.COUNTRY_CODE_VALUE, country_code_value)
                                    .putExtra(ForgetPasswordCodeActivity.COUNTRY_TEMP_CODE, code)
                                    .putExtra(ForgetPasswordCodeActivity.PHONENUMBER, phone));
                        } else {
                            String errMsg = ApiException.getHttpErrMessage(stringHttpBaseBean.getErrorCode(), stringHttpBaseBean.getErrorMsg());
                            ToastUtils.showLong(ForgetPasswordPhoneActivity.this, errMsg);
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (resultCode) {
            case 0:
                if (data != null) {
                    country_code_value = data.getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                    setCountryCode();
                }
                break;

            default:
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    private void setCountryCode() {
        mCountryCodeTv.setText("+" + country_code_value);
    }

    /**
     * 验证手机号是否有效
     *
     * @param phone
     * @return
     */
    private boolean isPhoneValid(String phone) {
        if (country_code_value.equals("86")) {
            Pattern p = Pattern.compile("^1\\d{10}$");
            Matcher m = p.matcher(phone);
            return m.matches();
        }
        return true;

    }

    /**
     * 忘记密码验证成功
     * ForgetPasswordCodeActivity
     */
    @EventInject(eventType = S.E.E_LOGIN_VERIFY_SUCCESS, runThread = TaskType.UI)
    public void verifySuccess(EventData data) {
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
