//package com.totwoo.totwoo.activity.security;
//
//import android.annotation.SuppressLint;
//import android.content.Intent;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.Message;
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.ScrollView;
//import android.widget.TextView;
//
//import androidx.constraintlayout.widget.ConstraintLayout;
//
//import com.alipay.sdk.app.PayTask;
//import com.etone.framework.annotation.EventInject;
//import com.etone.framework.annotation.InjectUtils;
//import com.etone.framework.event.EventBus;
//import com.etone.framework.event.EventData;
//import com.etone.framework.event.SubscriberListener;
//import com.etone.framework.event.TaskType;
//import com.tencent.mm.opensdk.modelpay.PayReq;
//import com.tencent.mm.opensdk.openapi.IWXAPI;
//import com.tencent.mm.opensdk.openapi.WXAPIFactory;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.S;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.activity.BaseActivity;
//import com.totwoo.totwoo.bean.AliPayRespBean;
//import com.totwoo.totwoo.bean.SafeExpireTimeBean;
//import com.totwoo.totwoo.bean.WechatRespBean;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.ble.BleParams;
//import com.totwoo.totwoo.ble.DateUtil;
//import com.totwoo.totwoo.utils.ACache;
//import com.totwoo.totwoo.utils.CommonArgs;
//import com.totwoo.totwoo.utils.CommonUtils;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.PayResult;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.widget.CommonMiddleDialog;
//
//import java.util.Map;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//import rx.Subscriber;
//
//public class SecurityPayActivity extends BaseActivity implements SubscriberListener {
//    @BindView(R.id.pay_content_sv)
//    ScrollView mContentSv;
//    @BindView(R.id.pay_success_ll)
//    LinearLayout mSuccessLl;
//    @BindView(R.id.pay_type_select_wechat_iv)
//    ImageView mWechatSelectIv;
//    @BindView(R.id.pay_type_select_alipay_iv)
//    ImageView mAliPaySelectIv;
//    @BindView(R.id.pay_date_tv)
//    TextView mPayDate;
//    @BindView(R.id.pay_imei_tv)
//    TextView mImeiTv;
//    @BindView(R.id.pay_succeed_date)
//    TextView mPaySucceedDate;
//    @BindView(R.id.loading_layout)
//    ConstraintLayout mLoadingLayout;
//    @BindView(R.id.pay_price_tv)
//    TextView mPayPrice;
//    private boolean isPaySucceedStatus;
//
//    private int payType = 0;
//    private ACache aCache;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_security_pay);
//        ButterKnife.bind(this);
//        InjectUtils.injectOnlyEvent(this);
//        aCache = ACache.get(SecurityPayActivity.this);
//        mImeiTv.setText("当前首饰的IMEI为：" + PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, ""));
//        mImeiTv.setVisibility(View.GONE);
//        setDate();
//        setPrice();
//    }
//
//    private void setDate() {
//        String deadline_time = aCache.getAsString(CommonArgs.IMEI_DEAD_LINE_TIME);
//        if (!TextUtils.isEmpty(deadline_time)) {
//            try {
//                long time = Long.valueOf(deadline_time);
//                mPayDate.setText(DateUtil.getStringDateByMillions(time));
//            } catch (NumberFormatException e) {
//                LogUtils.e("e = " + e);
//            }
//        } else {
//            HttpHelper.safeService.showExpireTime(PreferencesUtils.getString(SecurityPayActivity.this, BleParams.SAFE_JEWLERY_IMEI, ""))
//                    .compose(HttpHelper.rxSchedulerHelper())
//                    .subscribe(new Subscriber<HttpBaseBean<SafeExpireTimeBean>>() {
//                        @Override
//                        public void onCompleted() {
//
//                        }
//
//                        @Override
//                        public void onError(Throwable e) {
//
//                        }
//
//                        @Override
//                        public void onNext(HttpBaseBean<SafeExpireTimeBean> safeExpireTimeBeanHttpBaseBean) {
//                            if (safeExpireTimeBeanHttpBaseBean.getErrorCode() == 0) {
//                                try {
//                                    long millions = Long.valueOf(safeExpireTimeBeanHttpBaseBean.getData().getExpire_time()) * 1000;
//                                    aCache.put(CommonArgs.IMEI_DEAD_LINE_TIME, millions + "", 24 * 60 * 3600);
//                                    mPayDate.setText(DateUtil.getStringDateByMillions(millions));
//                                } catch (NumberFormatException e) {
//                                    e.printStackTrace();
//                                }
//                            }
//                        }
//                    });
//        }
//    }
//
//    private void setPrice() {
//        String imei = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.SAFE_JEWLERY_IMEI, "");
//        if (!TextUtils.isEmpty(imei)) {
//            HttpHelper.safeService.showExpireTime(imei)
//                    .compose(HttpHelper.rxSchedulerHelper())
//                    .subscribe(new Subscriber<HttpBaseBean<SafeExpireTimeBean>>() {
//                        @Override
//                        public void onCompleted() {
//
//                        }
//
//                        @Override
//                        public void onError(Throwable e) {
//
//                        }
//
//                        @Override
//                        public void onNext(HttpBaseBean<SafeExpireTimeBean> safeExpireTimeBeanHttpBaseBean) {
//                            if (safeExpireTimeBeanHttpBaseBean.getErrorCode() == 0) {
//                                mPayPrice.setText(safeExpireTimeBeanHttpBaseBean.getData().getMoney());
//                            }
//                        }
//                    });
//        }
//    }
//
//    @Override
//    protected void initTopBar() {
//        super.initTopBar();
//        setTopBackIcon(R.drawable.back_icon_black);
//        setTopLeftOnclik(v -> back());
//        setTopTitle(getString(R.string.safe_security_fee_title));
//        setTopRightString(getString(R.string.safe_security_fee_record));
//        setTopRightOnClick(v -> startActivity(new Intent(SecurityPayActivity.this, SecurityPayRecordActivity.class)));
//    }
//
//    @OnClick({R.id.pay_sure_tv, R.id.pay_alipay_cl, R.id.pay_wechat_cl, R.id.pay_succeed_know_tv, R.id.loading_layout})
//    protected void onClick(View view) {
//        switch (view.getId()) {
//            case R.id.pay_sure_tv:
//                mLoadingLayout.setVisibility(View.VISIBLE);
//                doPay();
//                break;
//            case R.id.pay_succeed_know_tv:
//                back();
//                break;
//            case R.id.pay_wechat_cl:
//                payType = 0;
//                mWechatSelectIv.setImageResource(R.drawable.pay_selected);
//                mAliPaySelectIv.setImageResource(R.drawable.pay_unselected);
//                break;
//            case R.id.pay_alipay_cl:
//                payType = 1;
//                mAliPaySelectIv.setImageResource(R.drawable.pay_selected);
//                mWechatSelectIv.setImageResource(R.drawable.pay_unselected);
//                break;
//            case R.id.loading_layout:
//                break;
//
//        }
//    }
//
//    private void doPay() {
//        if (payType == 0) {
//            HttpHelper.payService.wechatPay(1f, 1, PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, ""))
//                    .compose(HttpHelper.rxSchedulerHelper())
//                    .subscribe(new Subscriber<HttpBaseBean<WechatRespBean>>() {
//                        @Override
//                        public void onCompleted() {
//
//                        }
//
//                        @Override
//                        public void onError(Throwable e) {
//                            ToastUtils.showShort(SecurityPayActivity.this, R.string.error_net);
//                            mLoadingLayout.setVisibility(View.GONE);
//                        }
//
//                        @Override
//                        public void onNext(HttpBaseBean<WechatRespBean> objectHttpBaseBean) {
//                            mLoadingLayout.setVisibility(View.GONE);
//                            if (objectHttpBaseBean.getErrorCode() == 0) {
//                                WechatRespBean bean = objectHttpBaseBean.getData();
//                                wechatPay(bean.getPrepayid(), bean.getPackageValue(), bean.getNoncestr(), bean.getTimestamp(), bean.getSign());
//                            } else if (objectHttpBaseBean.getErrorCode() == 905) {
//                                showDateFalseDialog();
//                            } else {
//                                ToastUtils.showShort(SecurityPayActivity.this, R.string.error_net);
//                            }
//                        }
//                    });
//        } else {
//            HttpHelper.payService.aliPay(1f, 1, PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, ""))
//                    .compose(HttpHelper.rxSchedulerHelper())
//                    .subscribe(new Subscriber<HttpBaseBean<AliPayRespBean>>() {
//                        @Override
//                        public void onCompleted() {
//
//                        }
//
//                        @Override
//                        public void onError(Throwable e) {
//                            mLoadingLayout.setVisibility(View.GONE);
//                            ToastUtils.showShort(SecurityPayActivity.this, R.string.error_net);
//                        }
//
//                        @Override
//                        public void onNext(HttpBaseBean<AliPayRespBean> objectHttpBaseBean) {
//                            mLoadingLayout.setVisibility(View.GONE);
//                            if (objectHttpBaseBean.getErrorCode() == 0) {
//                                AliPayRespBean bean = objectHttpBaseBean.getData();
//                                aliPay(bean.getInfo());
//                            } else if (objectHttpBaseBean.getErrorCode() == 905) {
//                                showDateFalseDialog();
//                            } else if(objectHttpBaseBean.getErrorCode() == 906){
//                                showDisableDialog();
//                            }else {
//                                ToastUtils.showShort(SecurityPayActivity.this, R.string.error_net);
//                            }
//                        }
//                    });
//        }
//    }
//
//    private void showDateFalseDialog() {
//        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(SecurityPayActivity.this);
//        commonMiddleDialog.setMessage(getString(R.string.safe_security_fee_pay_date_false));
//        commonMiddleDialog.setSure(getString(R.string.ok), v -> commonMiddleDialog.dismiss());
//        commonMiddleDialog.show();
//    }
//
//    private void showDisableDialog() {
//        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(SecurityPayActivity.this);
//        commonMiddleDialog.setMessage(getString(R.string.safe_security_fee_pay_date_false));
//        commonMiddleDialog.setSure(getString(R.string.ok), v -> commonMiddleDialog.dismiss());
//        commonMiddleDialog.show();
//    }
//
//    private void wechatPay(String prepayId, String packageValue, String nonceStr, String timeStamp, String sign) {
//        IWXAPI api = WXAPIFactory.createWXAPI(this, "wx7e9b33b41b70a0ce");
//        PayReq request = new PayReq();
//        request.appId = "wx7e9b33b41b70a0ce";
//        request.partnerId = "1267416701";
//        request.prepayId = prepayId;
//        request.packageValue = packageValue;
//        request.nonceStr = nonceStr;
//        request.timeStamp = timeStamp;
//        request.sign = sign;
//        api.sendReq(request);
//    }
//
//    @SuppressLint("HandlerLeak")
//    private Handler mHandler = new Handler() {
//        public void handleMessage(Message msg) {
//            PayResult payResult = new PayResult((Map<String, String>) msg.obj);
//            /**
//             * 对于支付结果，请商户依赖服务端的异步通知结果。同步通知结果，仅作为支付结束的通知。
//             */
//            String resultInfo = payResult.getResult();// 同步返回需要验证的信息
//            String resultStatus = payResult.getResultStatus();
//            // 判断resultStatus 为9000则代表支付成功
//            if (TextUtils.equals(resultStatus, "9000")) {
//                // 该笔订单是否真实支付成功，需要依赖服务端的异步通知。
//                paySucceed(null);
//            } else {
//                // 该笔订单真实的支付结果，需要依赖服务端的异步通知。
//                ToastUtils.showShort(SecurityPayActivity.this, getString(R.string.safe_security_fee_pay_cancel));
//            }
//        }
//    };
//
//    private void aliPay(String info) {
//        final String orderInfo = info;   // 订单信息
//
//        Runnable payRunnable = () -> {
//            PayTask alipay = new PayTask(SecurityPayActivity.this);
//            Map<String, String> result = alipay.payV2(orderInfo, true);
//
//            Message msg = new Message();
//            msg.obj = result;
//            mHandler.sendMessage(msg);
//        };
//        // 必须异步调用
//        Thread payThread = new Thread(payRunnable);
//        payThread.start();
//    }
//
//    @EventInject(eventType = S.E.E_WECHAT_PAY_SUCCEED, runThread = TaskType.UI)
//    public void paySucceed(EventData data) {
//        isPaySucceedStatus = true;
//        mContentSv.setVisibility(View.GONE);
//        mSuccessLl.setVisibility(View.VISIBLE);
//        HttpHelper.safeService.showExpireTime(PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, ""))
//                .compose(HttpHelper.rxSchedulerHelper())
//                .subscribe(new Subscriber<HttpBaseBean<SafeExpireTimeBean>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<SafeExpireTimeBean> safeExpireTimeBeanHttpBaseBean) {
//                        if (safeExpireTimeBeanHttpBaseBean.getErrorCode() == 0) {
//                            try {
//                                aCache.put(CommonArgs.IMEI_DEAD_LINE_TIME, Long.valueOf(safeExpireTimeBeanHttpBaseBean.getData().getExpire_time()) * 1000 + "", 24 * 60 * 3600);
//                                mPaySucceedDate.setText(getString(R.string.safe_security_fee_pay_success_info,
//                                        CommonUtils.getFormatDate(Long.valueOf(safeExpireTimeBeanHttpBaseBean.getData().getExpire_time()) * 1000, Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext))));
//                            } catch (NumberFormatException e) {
//                                e.printStackTrace();
//                            }
//                            setDate();
//                            EventBus.onPostReceived(S.E.E_IMEI_CHARGE_SUCCEED, null);
//                        }
//                    }
//                });
//
//    }
//
//    private void back() {
//        if (isPaySucceedStatus) {
//            isPaySucceedStatus = false;
//            mContentSv.setVisibility(View.VISIBLE);
//            mSuccessLl.setVisibility(View.GONE);
//        } else {
//            finish();
//        }
//    }
//
//    @Override
//    public void onBackPressed() {
//        back();
//    }
//
//    @Override
//    public void onEventException(String eventType, EventData data, Throwable e) {
//        LogUtils.e("aab e = " + e);
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        InjectUtils.injectUnregisterListenerAll(this);
//    }
//}
