package com.totwoo.totwoo.activity.security;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.etone.framework.event.EventBus;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity;
import com.totwoo.totwoo.bean.EmergencyDocBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.WheelView;

import java.util.Arrays;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class EmergencyDocActivity extends BaseActivity {
    @BindView(R.id.emergency_doc_name_et)
    EditText mNameEt;
    @BindView(R.id.emergency_doc_blood_cl)
    ConstraintLayout mBloodCl;
    @BindView(R.id.emergency_doc_blood_tv)
    TextView mBloodTv;
    @BindView(R.id.emergency_doc_info_et)
    EditText mInfoEt;
    @BindView(R.id.emergency_doc_info_count_tv)
    TextView mCountTv;
    @BindView(R.id.emergency_doc_scrollview)
    ScrollView mScrollView;

    private int selectBloodType = 0;
    private CustomDialog dialog;
    private List<String> bloodTypes;
    public static int INIT_STATUS = 0;
    public static int ADD_STATUS = 1;
    public static int MANAGER_STATUS = 2;
    private int current_status;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_emergency_doc);

        ButterKnife.bind(this);
        current_status = getIntent().getIntExtra(CommonArgs.FROM_TYPE,2);
        bloodTypes = Arrays.asList(getResources().getStringArray(R.array.boold_type));
        mInfoEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null && s.length() > 0) {
                    int len = s.length();
                    if (len > 200) {
                        s = s.subSequence(0, 200);
                        mInfoEt.setText(s);
                        ToastUtils.showShort(EmergencyDocActivity.this, getString(R.string.wish_out_of_limit));
                        mInfoEt.setSelection(200);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null && !TextUtils.isEmpty(s.toString())) {
                    int length = s.toString().length();
                    if(length >= 200){
                        length = 200;
                    }
                    setEditCount(length);
                } else {
                    setEditCount(0);
                }
            }
        });
        getEmergencyDoc();
        ImageView backIv = findViewById(R.id.top_bar_back_btn);
        backIv.setVisibility(View.VISIBLE);
        backIv.setImageResource(R.drawable.back_icon_black);
        backIv.setOnClickListener(v -> finish());
        TextView titleView = findViewById(R.id.top_bar_title_view);
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(R.string.safe_contacts_doc_title);
        TextView rightView = findViewById(R.id.top_bar_right_tv);
        if (current_status == INIT_STATUS || current_status == ADD_STATUS) {
            rightView.setVisibility(View.VISIBLE);
        } else {
            rightView.setVisibility(View.GONE);
        }
        rightView.setText(R.string.skip);
        rightView.setOnClickListener(v -> {
            if(current_status == INIT_STATUS || current_status == ADD_STATUS){
                setInitFinish();
            }
            finish();
        });
    }

    private void setInitFinish(){
        EventBus.onPostReceived(S.E.E_IMEI_UPDATE_SUCCEED, null);
        PreferencesUtils.put(EmergencyDocActivity.this, SecurityHomeActivity.READ_CONTACT_HINT,true);
    }

    @OnClick({R.id.emergency_bottom_add_tv, R.id.emergency_doc_blood_cl})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.emergency_bottom_add_tv:
                saveEmergencyDoc();
                break;
            case R.id.emergency_doc_blood_cl:
                showBloodTypeDialog();
                break;
        }
    }

    private void getEmergencyDoc() {
        HttpHelper.safeService.getEmergencyDoc(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<EmergencyDocBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(EmergencyDocActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<EmergencyDocBean> emergencyDocBeanHttpBaseBean) {
                        if (emergencyDocBeanHttpBaseBean.getErrorCode() == 0) {
                            mNameEt.setText(emergencyDocBeanHttpBaseBean.getData().getFull_name());
                            mInfoEt.setText(emergencyDocBeanHttpBaseBean.getData().getDetails());
//                            try {
//                                setEditCount(emergencyDocBeanHttpBaseBean.getData().getDetails().length());
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
                            setFullBooldType(emergencyDocBeanHttpBaseBean.getData().getBlood_type());
                        }
                    }
                });
    }

    private void setEditCount(int number){
        mCountTv.setText(number + "/200");
    }

    private void saveEmergencyDoc() {
        String full_name = mNameEt.getText().toString().trim();
        if (TextUtils.isEmpty(full_name)) {
            ToastUtils.showShort(this, R.string.safe_doc_empty_name);
            return;
        }
        String info = mInfoEt.getText().toString().trim();
        HttpHelper.safeService.saveEmergencyDoc(full_name, simpleBooldType(), info,1)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(EmergencyDocActivity.this, getString(R.string.safe_doc_save_error));
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            ToastUtils.showShort(EmergencyDocActivity.this, getString(R.string.safe_doc_save_success));
                            InputMethodManager imm = (InputMethodManager) EmergencyDocActivity.this.getSystemService(Context.INPUT_METHOD_SERVICE);
                            imm.hideSoftInputFromWindow(mScrollView.getWindowToken(), 0);
                            if(current_status == INIT_STATUS || current_status == ADD_STATUS){
                                setInitFinish();
                            }
                            finish();
                        }
                    }
                });
    }

    private void showBloodTypeDialog() {
        dialog = new CustomDialog(this);
        dialog.setTitle(R.string.safe_doc_blood_type);
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(bloodTypes, 3, " ");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(selectBloodType);
        weekLayout.addView(weekWheelView);
        dialog.setMainLayoutView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setNegativeButtonText(R.string.cancel);
        dialog.setPositiveButton(R.string.picker_sure, v -> {
            mBloodTv.setText(weekWheelView.getSeletedItem());
            mBloodTv.setTextColor(getResources().getColor(R.color.text_color_black_33));
            selectBloodType = weekWheelView.getSeletedIndex();
            dialog.dismiss();
        });
        dialog.show();
    }

    private String simpleBooldType() {
        switch (selectBloodType) {
            case 0:
                return "A";
            case 1:
                return "B";
            case 2:
                return "AB";
            case 3:
                return "O";
            case 4:
                return "RH";
        }
        return "A";
    }

    private void setFullBooldType(String booldType) {
        switch (booldType) {
            case "A":
                selectBloodType = 0;
                break;
            case "B":
                selectBloodType = 1;
                break;
            case "AB":
                selectBloodType = 2;
                break;
            case "O":
                selectBloodType = 3;
                break;
            case "RH":
                selectBloodType = 4;
                break;
            default:
                return;

        }
        mBloodTv.setText(bloodTypes.get(selectBloodType));
        mBloodTv.setTextColor(getResources().getColor(R.color.text_color_black_33));
    }
}
