//package com.totwoo.totwoo.activity;
//
//import android.animation.ObjectAnimator;
//import android.animation.ValueAnimator;
//import android.content.ContentUris;
//import android.content.Intent;
//import android.graphics.Bitmap;
//import android.graphics.BitmapFactory;
//import android.graphics.Rect;
//import android.graphics.drawable.AnimationDrawable;
//import android.location.LocationManager;
//import android.net.Uri;
//import android.os.Bundle;
//import android.provider.ContactsContract;
//import android.provider.Settings;
//import android.support.v4.graphics.ColorUtils;
//import android.text.TextUtils;
//import android.view.View;
//import android.view.animation.Animation;
//import android.view.animation.AnimationUtils;
//import android.widget.CheckBox;
//import android.widget.FrameLayout;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.RelativeLayout;
//import android.widget.ScrollView;
//import android.widget.TextView;
//import android.widget.Toast;
//
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import com.totwoo.library.util.Apputils;
//import com.totwoo.library.util.LogUtils;
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.S;
//import com.totwoo.totwoo.ToTwooApplication;
//import com.totwoo.totwoo.bean.CallRamindContact;
//import com.totwoo.totwoo.bean.SosContactsBean;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.NotifyUtil;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//import com.totwoo.totwoo.utils.StringUtils;
//import com.totwoo.totwoo.utils.ToastUtils;
//import com.totwoo.totwoo.widget.CustomDialog;
//import com.totwoo.totwoo.widget.PullZoomScrollView;
//
//import org.greenrobot.eventbus.EventBus;
//import org.greenrobot.eventbus.Subscribe;
//import org.greenrobot.eventbus.ThreadMode;
//
//import java.io.InputStream;
//import java.util.ArrayList;
//import java.util.List;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//import rx.Observable;
//import rx.Subscriber;
//import rx.functions.Action1;
//
///**
// * 紧急求助设置界面
// * <p>
// * Created by lixingmao on 2017/05/05.
// */
//public class SOSActivity extends BaseActivity {
//
//    /**
//     * 紧急联系人保存的TAG ,  一个josn串
//     */
//    public static final String SOS_CONTACT_DATA_KEY = "sos_contact_data_key";
//
//    private static final int HEAD_ANIM_DUIRING = 600;
//    private static final int REQUEST_CODE_LOCATION = 22;
//    private static final int REQUEST_CODE_CONTACT = 11;
//
//    @BindView(R.id.sos_scroll_content_view)
//    PullZoomScrollView mSosScrollContentView;
//    @BindView(R.id.sos_head_layout)
//    FrameLayout mSosHeadLayout;
//    @BindView(R.id.sos_head_imageview)
//    ImageView mSosHeadImageView;
//    @BindView(R.id.sos_switch_title_tv)
//    TextView mSosSwitchTitleTv;
//    @BindView(R.id.sos_switch_cb)
//    CheckBox mSosSwitchCb;
//    @BindView(R.id.sos_switch_info_tv)
//    TextView mSosSwitchInfoTv;
//    @BindView(R.id.sos_switch_click_item)
//    RelativeLayout mSosSwitchClickItem;
//    @BindView(R.id.sos_message_tv)
//    TextView mSosMessageTv;
//    @BindView(R.id.important_call_ramind_contact_ll)
//    LinearLayout mImportantCallRamindContactLl;
//    @BindView(R.id.sos_add_important_contact_tv)
//    TextView mSosAddImportantContactTv;
//    @BindView(R.id.sos_switch_content_layout)
//    LinearLayout mSosSwitchContentLayout;
//
//    @BindView (R.id.activity_sos_content)
//    TextView mContent;
//
//    private List<SosContactsBean.EmergencyPhoneDataBean> mSosContacts;
//
//    private Gson gson = new Gson();
//
//    private int actionViewHeight;
//
//    //最大联系人
//    private int maxContact = 3;
//    private int titleBgColor;
//
//    private int topBarHeight;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_sos);
//        ButterKnife.bind(this);
//        EventBus.getDefault().register(this);
//
//        initTopBar1();
//        boolean isSosOn = NotifyUtil.getSosNotifySwitch(this);
//
//        initData(isSosOn);
//
//        initView(isSosOn);
//    }
//
//    private void initTopBar1()
//    {
//        View v = getTopBarBackgroundObject();
//        v.setVisibility(View.VISIBLE);
//        v.setAlpha(0);
//
//        setTopTitleColor(0xff000000);
//        setTopTitle(R.string.sos);
//        setTopBackIcon(R.drawable.back_icon_black);
//        setTopRightIcon(R.drawable.help_icon);
//        Rect rectangle = new Rect();
//        getWindow().getDecorView().getWindowVisibleDisplayFrame(rectangle);
//        topBarHeight = rectangle.top;
//
//        setTopRightOnClick(new View.OnClickListener()
//        {
//            @Override
//            public void onClick(View v)
//            {
//                Intent i = new Intent (SOSActivity.this, HelpHelpActivity.class);
//                SOSActivity.this.startActivity(i);
//            }
//        });
//
//    }
//
//    private void initLocation()
//    {
//        if (ToTwooApplication.baseContext.mLocationClient != null)
//        {
//            if (ToTwooApplication.baseContext.mLocationClient.isStarted()) {
//                ToTwooApplication.baseContext.mLocationClient.requestLocation();
//            } else {
//                ToTwooApplication.baseContext.mLocationClient.start();
//            }
//        }
//    }
//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onReceivedLocation(BDLocation location)
//    {
//        String res = location.getAddrStr() + location.getLocationDescribe().replace("在", "");
//        LogUtils.e("location:" + location.getAddrStr());
//        EventBus.getDefault().unregister(this);
//        ToTwooApplication.baseContext.mLocationClient.stop();
//
//        String name = ToTwooApplication.owner.getNickName();
//        String phone = ToTwooApplication.owner.getPhone();
//        if (phone.startsWith("86"))
//            phone = "+" + phone;
//
//        mSosMessageTv.setText(getString(R.string.sos_message_content, name, phone, res));
//    }
//
//    private void initView(final boolean isSosOn) {
//        if (isSosOn)
//            mContent.setText(getString(R.string.jjqz_content));
//        else
//            mContent.setText(getString(R.string.jjqz_content_not));
//        initLocation();
//        actionViewHeight = Apputils.dp2px(this, 56);
//        titleBgColor = getResources().getColor(R.color.layer_bg_white);
//        String name = ToTwooApplication.owner.getNickName();
//        String phone = ToTwooApplication.owner.getPhone();
//        if (phone.startsWith("86"))
//            phone = "+" + phone;
//
//        mSosMessageTv.setText(getString(R.string.sos_message_content, name, phone, "***"));
//
//        mSosScrollContentView.setOnScrollListener(new PullZoomScrollView.OnScrollListener() {
//            @Override
//            public void onScrollStateChanged(ScrollView view, int scrollState)
//            {
//                if (scrollState == PullZoomScrollView.SCROLL_STATE_IDLE)
//                {
//                    switch (isOnBottom)
//                    {
//                        case 1:
//                        case 3:
//                            if (!isShowing)
//                                setAnim(false);
//                            break;
//                        case 2:
//                            if (isShowing)
//                                setAnim(true);
//                            break;
//                    }
//                }
//            }
//
//            @Override
//            public void onScroll(ScrollView view, boolean isTouchScroll, int l, int t, int oldl, int oldt)
//            {
//                /*if (t > 0 && t < actionViewHeight * 1.5f) {
//                    float alpha = t / (actionViewHeight * 1.5f);
//                    getTopBar().setBackgroundColor(Color.argb((int) (alpha * 255), Color.red(titleBgColor), Color.green(titleBgColor), Color.blue(titleBgColor)));
//                }*/
//                int scrollY = view.getScrollY();
//
//
//                int height = view.getHeight();
//                View v = getTopBarBackgroundObject();
//                int realHeight = view.getChildAt(0).getHeight();
//                if (scrollY + height == realHeight) //判断滑到最底部
//                {
//                    //setTopTitleColor(0xff000000);
//                    //setTopRightIcon(R.drawable.the_heart_manage_icon);
//                    //v.setAlpha(1);
//                    isOnBottom = 1;
//                }
//                else
//                {
//                    if (scrollY == 0)
//                    {
//                        //到顶部了
//                        isOnBottom = 2;
//                    }
//                    else
//                    {
//                        isOnBottom = 3; //在中间
//                    }
//                    //setTopTitleColor(0xffffffff);
//                    //setTopRightIcon(R.drawable.the_heart_manage_icon_white);
//                    /*float alaph = 1 - (((float)realHeight) - height - ((float)scrollY)) / ((float)realHeight-height);
//                    System.out.println("alaph:" + alaph);
//                    v.setAlpha(alaph);*/
//                }
//
//                if (scrollY == 0)
//                {
//
//                    //到顶部了
//                    isOnBottom = 2;
//                }
//                else
//                {
//
//                }
//            }
//        });
//
//        mSosSwitchCb.setChecked(isSosOn);
//        mSosSwitchTitleTv.setText(mSosSwitchCb.isChecked() ? R.string.notify_on : R.string.notify_off);
//
//        if (!isSosOn) {
//            mSosSwitchContentLayout.setVisibility(View.GONE);
//
//            mSosHeadImageView.setImageResource(R.drawable.sos_off_head_image);
//            mSosHeadLayout.setBackgroundResource(R.color.sos_head_off_bg);
//        } else {
//            mSosHeadImageView.setImageResource(R.drawable.sos_on_head_image);
//            mSosHeadLayout.setBackgroundResource(R.color.sos_head_on_bg);
//        }
//
//        ((AnimationDrawable) mSosHeadImageView.getDrawable()).start();
//    }
//
//    int isOnBottom;
//    boolean isShowing;
//
//    private void setAnim(boolean canSee)
//    {
//        ObjectAnimator anim;
//        View v = getTopBarBackgroundObject();
//        if (canSee)
//        {
//            anim = ObjectAnimator.ofFloat(v, "alpha", 1f, 0f);
//            isShowing = false;
//        }
//        else
//        {
//            v.setVisibility(View.VISIBLE);
//            anim = ObjectAnimator.ofFloat(v, "alpha", 0f, 1f);
//            isShowing = true;
//        }
//
//        anim.setDuration(300);
//        anim.start();
//    }
//
//    private void initData(boolean isSosOn) {
//        String data = PreferencesUtils.getString(this, SOS_CONTACT_DATA_KEY, null);
//
//        if (!TextUtils.isEmpty(data)) {
//            mSosContacts = gson.fromJson(data, new TypeToken<List<SosContactsBean.EmergencyPhoneDataBean>>() {
//            }.getType());
//
//            if (mSosContacts != null && mSosContacts.size() == maxContact) {
//                mSosAddImportantContactTv.setVisibility(View.GONE);
//            }
//            setSosContactData();
//        } else {
//            //服务器获取已设置紧急联系人
//            HttpHelper.card.getSosContacts()
//                    .compose(HttpHelper.<HttpBaseBean<SosContactsBean>>rxSchedulerHelper())
//                    .map(new HttpHelper.HttpResultFunc<SosContactsBean>())
//                    .subscribe(new Subscriber<SosContactsBean>() {
//                        @Override
//                        public void onCompleted() {
//                        }
//
//                        @Override
//                        public void onError(Throwable e) {
//                            e.printStackTrace();
//                            //ToastUtils.showLong(SOSActivity.this, e.getMessage());
//                        }
//
//                        @Override
//                        public void onNext(SosContactsBean sosContactsBean) {
//                            if (!TextUtils.isEmpty(sosContactsBean.getEmergencyPhoneData())) {
//                                mSosContacts = gson.fromJson(sosContactsBean.getEmergencyPhoneData(), new TypeToken<List<SosContactsBean.EmergencyPhoneDataBean>>() {
//                                }.getType());
//                                setSosContactData();
//                            }
//                        }
//                    });
//        }
//    }
//
//    @Override
//    protected void onResume() {
//        super.onResume();
//
//        if (mSosHeadImageView.getDrawable() != null) {
//            ((AnimationDrawable) mSosHeadImageView.getDrawable()).start();
//        }
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//        if (mSosHeadImageView.getDrawable() != null) {
//            ((AnimationDrawable) mSosHeadImageView.getDrawable()).stop();
//        }
//    }
//
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//
//        if (data != null && requestCode == REQUEST_CODE_CONTACT) {
//            ArrayList<CallRamindContact> contacts = data.getParcelableArrayListExtra("contact");
//            if (mSosContacts == null) {
//                mSosContacts = new ArrayList<>();
//            }
//            /*修改：因为把联系人传过去，所以这里每次联系人都会更新*/
//            mSosContacts.clear();
//            for (CallRamindContact con : contacts) {
//                if (con == null) {
//                    continue;
//                }
//                SosContactsBean.EmergencyPhoneDataBean bean = new SosContactsBean.EmergencyPhoneDataBean();
//                bean.setEmergencyIcon(con.getHeadIcon());
//                bean.setEmergencyName(con.getName());
//                bean.setEmergencyPhone(con.getPhoneNumber());
//
//                mSosContacts.add(bean);
//            }
//
//            mImportantCallRamindContactLl.removeAllViews();
//
//            setSosContactData();
//
//            if (mSosContacts.size() == maxContact) {
//                mSosAddImportantContactTv.setVisibility(View.GONE);
//            }
//
//            uploadContaceData(mSosContacts);
//        } else if (requestCode == REQUEST_CODE_LOCATION){
//            if (checkLocationType()){
//                mSosSwitchClickItem.performClick();
//            }
//        }
//    }
//
//    private void setSosContactData() {
//        if (mSosContacts != null)
//        {
//            if (mSosContacts != null && mSosContacts.size() == maxContact) {
//                mSosAddImportantContactTv.setVisibility(View.GONE);
//            }
//            Observable.from(mSosContacts)
//                    .subscribe(new Action1<SosContactsBean.EmergencyPhoneDataBean>() {
//                        @Override
//                        public void call(final SosContactsBean.EmergencyPhoneDataBean callRamindContact) {
//                            final View contactItem = View.inflate(SOSActivity.this, R.layout.sos_contact_item, null);
//                            final SOSActivity.ViewHolder viewHolder = new SOSActivity.ViewHolder(contactItem);
//                            if (callRamindContact.getEmergencyIcon() > 0) {
//                                viewHolder.contact_head_icon_iv.setImageBitmap(getPhotoById(callRamindContact.getEmergencyIcon()));
//                            } else {
//                                viewHolder.contact_head_icon_iv.setImageResource(R.drawable.default_head_yellow);
//                            }
//                            viewHolder.cantact_name_tv.setText(callRamindContact.getEmergencyName());
//                            // 删除重要联系人
//                            viewHolder.call_remind_contact_delete_iv.setOnClickListener(new View.OnClickListener() {
//                                @Override
//                                public void onClick(View v) {
//                                    mImportantCallRamindContactLl.removeView(contactItem);
//                                    SOSActivity.this.mSosContacts.remove(callRamindContact);
//                                    mSosAddImportantContactTv.setVisibility(View.VISIBLE);
//
//                                    uploadContaceData(mSosContacts);
//                                }
//                            });
//                            mImportantCallRamindContactLl.addView(contactItem);
//                        }
//                    });
//        }
//    }
//
//    private void uploadContaceData(final List<SosContactsBean.EmergencyPhoneDataBean> mSosContacts) {
//        HttpHelper.card.setSosContacts(gson.toJson(mSosContacts))
//                .compose(HttpHelper.<HttpBaseBean<String>>rxSchedulerHelper())
//                .map(new HttpHelper.HttpResultFunc<String>())
//                .subscribe(new Subscriber<String>() {
//                    @Override
//                    public void onCompleted() {
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        ToastUtils.showLong(SOSActivity.this, e.getMessage());
//                    }
//
//                    @Override
//                    public void onNext(String s) {
//                        PreferencesUtils.put(SOSActivity.this, SOS_CONTACT_DATA_KEY, gson.toJson(mSosContacts));
//                    }
//                });
//    }
//
//    private Bitmap getPhotoById(long contactid) {
//        Uri uri = ContentUris.withAppendedId(
//                ContactsContract.Contacts.CONTENT_URI, contactid);
//        InputStream input = ContactsContract.Contacts
//                .openContactPhotoInputStream(getContentResolver(),
//                        uri);
//        return BitmapFactory.decodeStream(input);
//    }
//
//    public class ViewHolder {
//        @BindView(R.id.contact_head_icon_iv)
//        ImageView contact_head_icon_iv;
//        @BindView(R.id.cantact_name_tv)
//        TextView cantact_name_tv;
//        @BindView(R.id.call_remind_contact_delete_iv)
//        ImageView call_remind_contact_delete_iv;
//
//        ViewHolder(View view) {
//            ButterKnife.bind(this, view);
//        }
//    }
//
//
//    @OnClick({R.id.sos_switch_click_item, R.id.sos_add_important_contact_tv})
//    public void onViewClicked(View view) {
//        switch (view.getId()) {
//            case R.id.sos_switch_click_item:
//                if (! mSosSwitchCb.isChecked() && !checkLocationType()){
//                    return;
//                }
//                mSosSwitchCb.setChecked(!mSosSwitchCb.isChecked());
//
//                NotifyUtil.setSosNotifySwitch(this, mSosSwitchCb.isChecked());
//                if (!mSosSwitchCb.isChecked())
//                    ToTwooApplication.isSOSJewelry = false;
//                mSosSwitchTitleTv.setText(mSosSwitchCb.isChecked() ? R.string.notify_on : R.string.notify_off);
//
//                changeLayout(mSosSwitchCb.isChecked());
//                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_SOS, null);
//
//                break;
//            case R.id.sos_add_important_contact_tv:
//                if (mSosContacts != null && mSosContacts.size() >= maxContact) {
//                    ToastUtils.show(getBaseContext(), getString(R.string.important_contact_exceed), Toast.LENGTH_LONG);
//                    return;
//                }
//
//                Intent intent = new Intent(getBaseContext(), ContactsListActivity.class);
//                intent.putExtra("isImportantContact", true);
//                /*if (mSosContacts != null) {
//                    intent.putExtra("allowAddCount", maxContact - mSosContacts.size());
//                } else {
//                    intent.putExtra("allowAddCount", maxContact);
//                }*/
//                /*因为这里要把当前已经选择的联系人传过去，所以这里直接传最大联系人数量即刻，剩下的都在那边判断*/
//                intent.putExtra("allowAddCount", maxContact);
//                if (mSosContacts!= null)
//                    for (int i=0; i<mSosContacts.size(); i++)
//                        intent.putExtra("Contact" + i, mSosContacts.get(i));
//                startActivityForResult(intent, REQUEST_CODE_CONTACT);
//                break;
//        }
//    }
//
//    /**
//     * 检查当前位置设置类型, 非 GPS 的弹窗让设置
//     */
//    private boolean checkLocationType() {
//        try {
//            LocationManager lm = (LocationManager) getSystemService(LOCATION_SERVICE);
//            List<String> providers = lm.getProviders(true);
//            if (!providers.contains("gps")) {
//                final CustomDialog dialog = new CustomDialog(this);
//                dialog.setMessage(R.string.sos_no_gps_prompt);
//                dialog.setPositiveButton(R.string.immediately_setting1, new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        startActivityForResult(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS), REQUEST_CODE_LOCATION);
//                        dialog.dismiss();
//                    }
//                });
//                dialog.setNegativeButton(R.string.give_up, new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        dialog.dismiss();
//                    }
//                });
//                dialog.show();
//            } else {
//                return true;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return false;
//    }
//
//    private void changeLayout(final boolean changeOn)
//    {
//        if (changeOn)
//            mContent.setText(getString(R.string.jjqz_content));
//        else
//            mContent.setText(getString(R.string.jjqz_content_not));
//        // 头部背景变换
//        ValueAnimator bganim = ValueAnimator.ofFloat(0, 1);
//        bganim.setDuration(HEAD_ANIM_DUIRING);
//        bganim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
//            @Override
//            public void onAnimationUpdate(ValueAnimator animation) {
//                float value = ((Float) animation.getAnimatedValue());
//
//                int color = changeOn ? ColorUtils.blendARGB(getResources().getColor(R.color.sos_head_off_bg), getResources().getColor(R.color.sos_head_on_bg), value)
//                        : ColorUtils.blendARGB(getResources().getColor(R.color.sos_head_on_bg), getResources().getColor(R.color.sos_head_off_bg), value);
//                mSosHeadLayout.setBackgroundColor(color);
//            }
//        });
//        bganim.start();
//
//        Animation headAnim = AnimationUtils.loadAnimation(this, changeOn ? R.anim.sos_head_on_enter : R.anim.sos_head_off_enter);
//        headAnim.setDuration(HEAD_ANIM_DUIRING);
//        mSosHeadImageView.setImageResource(changeOn ? R.drawable.sos_on_head_image : R.drawable.sos_off_head_image);
//        mSosHeadImageView.startAnimation(headAnim);
//        ((AnimationDrawable) mSosHeadImageView.getDrawable()).start();
//
//        // 底部 内容layout 变化
//        Animation layoutAnim = AnimationUtils.loadAnimation(this, changeOn ? R.anim.layout_open : R.anim.layout_close);
//        if (changeOn) {
//            mSosSwitchContentLayout.setVisibility(View.VISIBLE);
//        } else {
//            layoutAnim.setAnimationListener(new Animation.AnimationListener() {
//                @Override
//                public void onAnimationStart(Animation animation) {
//                }
//
//                @Override
//                public void onAnimationEnd(Animation animation) {
//                    mSosSwitchContentLayout.setVisibility(View.GONE);
//                }
//
//                @Override
//                public void onAnimationRepeat(Animation animation) {
//                }
//            });
//        }
//        mSosSwitchContentLayout.startAnimation(layoutAnim);
//    }
//
//}
