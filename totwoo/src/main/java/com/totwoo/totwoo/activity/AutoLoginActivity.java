package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.method.LinkMovementMethod;

import com.blankj.utilcode.util.ClickUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.event.EventBus;
import com.tencent.bugly.crashreport.CrashReport;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.LocalHttpJewelryInfo;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.databinding.ActivityAutoLoginBinding;
import com.totwoo.totwoo.receiver.JpushReceiver;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;

public class AutoLoginActivity extends BaseActivity {
    private ActivityAutoLoginBinding binding;
    private String phone;
    private String encodePassword;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityAutoLoginBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        ClickUtils.expandClickArea(binding.autoPolicyCheckbox, 30);

        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        phone = PreferencesUtils.getString(this, CommonArgs.PREF_LAST_PHONE, "");
        encodePassword = PreferencesUtils.getString(this, CommonArgs.PREF_LAST_ENCODE_PASSWORD, "");

        if (phone.isEmpty() || encodePassword.isEmpty()) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        RequestOptions options;
        if (PreferencesUtils.getInt(this, CommonArgs.PREF_LAST_GENDER, 0) == 0) {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        } else {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        }

        String realImageUrl = HttpHelper.getRealImageUrl(PreferencesUtils.getString(this, CommonArgs.PREF_LAST_HEAD_ICON, ""));
        Glide.with(this).load(realImageUrl).apply(options).into(binding.autoHeadIconRiv);
        binding.autoUserNameTv.setText(PreferencesUtils.getString(this, CommonArgs.PREF_LAST_USERNAME, ""));

        binding.autoLoginUseCurrentTv.setOnClickListener(v -> doLogin());
        binding.autoLoginOtherCurrentTv.setOnClickListener(v -> {
            startActivity(new Intent(this, PasswordLoginActivity.class));
        });

        binding.autoPolicyTv.setText(CommonUtils.stylePolicyString(this));
        binding.autoPolicyTv.setMovementMethod(LinkMovementMethod.getInstance());
    }

    private void doLogin() {
        if (!binding.autoPolicyCheckbox.isChecked()) {
            ToastUtils.showLong(this, getString(R.string.terms_agree_tips));
            return;
        }

        int time = (int) (System.currentTimeMillis() / 1000);
        showProgressDialog();
        launchRequestWithFlexibleError(
                HttpHelper.loginV3Service.loginPassword(phone, time, encodePassword,  PreferencesUtils.getString(this, JpushReceiver.REGISTER_ID, ""), HttpHelper.genNewSign(time, phone)),
                data -> {
                    CrashReport.setUserId(phone);
                    CommonUtils.setInfo(AutoLoginActivity.this, data);
                    TimInitBusiness.login();

                    launchRequestWithFlexibleError(
                            HttpHelper.multiJewelryService.getJewelryList(),
                            listData -> {
                                dismissProgressDialog();
                                if (listData != null && !listData.isEmpty()) {
                                    for (LocalHttpJewelryInfo info : listData) {
                                        LocalJewelryDBHelper.getInstance().addBean(new LocalJewelryInfo(info.getMac_address(), info.getDevice_name(), info.getIs_select(), info.getCreate_time() * 1000));
                                    }
                                }
                                goNext();
                            },
                            fail -> {
                                dismissProgressDialog();
                                return false ;
                            },false
                    );
                },
                fail -> {
                    dismissProgressDialog();
                    return false;
                },false
        );
    }

    private void goNext() {
        try {
            if (!LocalJewelryDBHelper.getInstance().getAllBeans().isEmpty()) {
                LocalJewelryInfo info = LocalJewelryDBHelper.getInstance().getSelectedBean();
                PreferencesUtils.put(AutoLoginActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, info.getMac_address());
                PreferencesUtils.put(AutoLoginActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, info.getName());
                BluetoothManage.getInstance().startBackgroundScan();
//                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
                LogUtils.e("HomeActivityControl");
                HomeActivityControl.getInstance().connectJew(AutoLoginActivity.this);
            } else {
//                if (ToTwooApplication.isInfoSetFinish(ToTwooApplication.owner)) {
//                    startActivity(new Intent(this, JewelrySelectActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
//                } else {
//                    startActivity(new Intent(this, InitInfoActivity.class)
//                            .putExtra(InitInfoActivity.INIT_INFO, true)
//                            .putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
//                }
                HomeActivityControl.getInstance().openHomeActivity(AutoLoginActivity.this);
                finish();
            }
        } catch (DbException e) {
            e.printStackTrace();
        }
        EventBus.onPostReceived(S.E.E_LOGIN_SUCCESS, null);
        finish();
    }
}
