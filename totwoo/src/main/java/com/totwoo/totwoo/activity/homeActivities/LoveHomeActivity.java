package com.totwoo.totwoo.activity.homeActivities;

import android.os.Build;
import android.os.Bundle;

import com.tencent.mars.xlog.Log;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.HomepageBottomInfo;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.fragment.BaseFragment;
import com.totwoo.totwoo.fragment.CustomMagicFragment;
import com.totwoo.totwoo.fragment.LoveFragment;
import com.totwoo.totwoo.fragment.MeFragment;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.ArrayList;


public class LoveHomeActivity extends HomeBaseActivity {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Class<? extends BaseFragment>[] baseFragments = new Class[3];
        baseFragments[0] = LoveFragment.class;
        baseFragments[1] = CustomMagicFragment.class;
        baseFragments[2] = MeFragment.class;

        ArrayList<HomepageBottomInfo> infos = new ArrayList<>();
        infos.add(new HomepageBottomInfo(R.drawable.new_home_xylx_un, R.drawable.new_home_xylx, R.string.notify));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_magic_un, R.drawable.new_home_magic, R.string.heart));
        infos.add(new HomepageBottomInfo(R.drawable.new_home_me_un, R.drawable.new_home_me, R.string.user));
        super.setBottomInfo(infos);
        super.setFragmentsAndInitViewpager(baseFragments);

        Log.e("start",
                "V" + Apputils.getVersionName(ToTwooApplication.baseContext) +
                        ",账号:" + ToTwooApplication.owner.getTotwooId() +
                        ",语言:" + Apputils.getSystemLanguage(ToTwooApplication.baseContext) +
                        ",手机:" + Build.MANUFACTURER + "-" + Build.MODEL +
                        ",固件:" + PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, ""));
    }
}
