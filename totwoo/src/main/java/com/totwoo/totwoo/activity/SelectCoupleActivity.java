package com.totwoo.totwoo.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.inputmethod.InputMethodManager;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;

import com.blankj.utilcode.util.KeyboardUtils;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.ContactsBean;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.databinding.ActivitySelectCoupleBinding;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 新增独立的选择配对页面
 */
public class SelectCoupleActivity extends BaseActivity {
    private static final String TAG = "SelectCoupleActivity";
    private ActivitySelectCoupleBinding binding;

    private CoupleLogic coupleLogic;
    private String countryCodeValue;
    private ActivityResultLauncher<Intent> contractLauncher;
    private ActivityResultLauncher<Intent> countryCodeLauncher;


    @SuppressLint("SetTextI18n")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySelectCoupleBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());


        // 设置默认的区号
        countryCodeValue = Apputils.getSystemLanguageCountryCode(this);
        binding.selectCoupleCountryCodeTv.setText("+" + countryCodeValue);
        binding.selectCoupleCountryCodeTv.setOnClickListener(v -> {
            Intent intent = new Intent(this, CountryCodeListActivity.class);
            countryCodeLauncher.launch(intent);
            // 切换动画
            overridePendingTransition(R.anim.activity_fade_in,
                    R.anim.activity_fade_out);
        });

        contractLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            if (result != null && result.getData() != null && result.getData().getData() != null) {
                // 通过系统 Contract uri, 获取对应联系人信息
                try {
                    Uri contactUri = result.getData().getData();
                    String[] projection = {ContactsContract.CommonDataKinds.Phone.NUMBER, ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME};

                    Cursor cursor = getContentResolver().query(contactUri, projection, null, null, null);
                    if (cursor != null && cursor.moveToFirst()) {
                        int phoneIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER);
                        String phone = cursor.getString(phoneIndex);
//                        int nameIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME);
//                        String name = cursor.getString(nameIndex);


                        if (!TextUtils.isEmpty(phone)) {
                            if (phone.startsWith("+")) {
                                String[] split = phone.split(" ");
                                if (split.length > 1 && split[0].length() <= 5) {
                                    countryCodeValue = split[0].replace("+", "");
                                    binding.selectCoupleCountryCodeTv.setText("+" + countryCodeValue);
                                    phone = phone.replace(split[0], "");
                                }
                            }

                            phone = getSimplePhone(phone);
                            binding.selectCouplePhoneEt.setText(phone);
                        }
                        cursor.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        countryCodeLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            if (result != null && result.getData() != null) {
                countryCodeValue = result.getData().getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                binding.selectCoupleCountryCodeTv.setText("+" + countryCodeValue);

                checkAndToggleButton(binding.selectCouplePhoneEt.getText().toString());
            }
        });

        binding.selectCouplePhoneEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                checkAndToggleButton(s.toString());
            }
        });


        coupleLogic = new CoupleLogic(this);
        // 选择通讯录
        binding.selectCoupleFromContactTv.setText(String.format("%s >", getString(R.string.select_from_contacts)));
        binding.selectCoupleFromContactTv.setOnClickListener(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_WANTPAIR_CLICK_ADDRESSBOOK);
            if (!PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonArgs.ALLOW_CONTACT_GET, false)) {
                showAllowContactsDialog();
            } else {
                contractLauncher.launch(new Intent(Intent.ACTION_PICK, ContactsContract.CommonDataKinds.Phone.CONTENT_URI));
            }
        });

        binding.selectCoupleDoRequestTv.setOnClickListener(v -> {
            // 检查手机号输入, 并发送配对请求
            String phone = binding.selectCouplePhoneEt.getText().toString();

            if (TextUtils.isEmpty(phone)) {
                ToastUtils.showShort(this, R.string.error_invalid_phone);
            } else if (!isPhoneValid(phone)) {
                ToastUtils.showShort(this, R.string.error_incorrect_phone);
            } else if ((countryCodeValue + phone).equals(ToTwooApplication.owner.getPhone())) {
                ToastUtils.showLong(this, R.string.cant_request_yourself);
            } else {

                if (!"46".equals(countryCodeValue) && phone.startsWith("0")) {
                    CommonMiddleDialog dialog = new CommonMiddleDialog(this);
                    dialog.setMessage(getString(R.string.phone_warn_info_start_with_0_for_couple));
                    dialog.setSure(getString(R.string.confirm), v1 -> {
                        doHttpRequest(phone);
                        dialog.dismiss();
                    });
                    dialog.setCancel(R.string.cancel, v1 -> dialog.dismiss());
                    dialog.show();
                } else {
                    doHttpRequest(phone);
                }

            }
        });

        KeyboardUtils.showSoftInput(this);
    }

    /**
     * 发送配对请求
     *
     * @param phone
     */
    private void doHttpRequest(String phone) {
        ContactsBean contactsBean = new ContactsBean();
        contactsBean.setPhoneNumber(countryCodeValue + phone);
        coupleLogic.sendRequest(contactsBean, new CoupleLogic.CoupleFullCallback() {
            @Override
            public void onResult(boolean success) {
            }

            @Override
            public void onResult(boolean success, ContactsBean data) {
                if (success) {
                    startActivity(new Intent(SelectCoupleActivity.this, CoupleRequestInfoActivity.class)
                            .putExtra(CoupleRequestInfoActivity.EXTRA_COUPLE_REQUESTED_BEAN, data));
                    finish();
                }
            }
        });
    }

    /**
     * 检查手机号输入, 并切换提交按钮状态
     *
     * @param phone
     */
    private void checkAndToggleButton(String phone) {
        boolean phoneValid = isPhoneValid(phone);
        binding.selectCoupleDoRequestTv.setEnabled(phoneValid);
        if ("86".equals(countryCodeValue) && phoneValid) {
            // 隐藏键盘, 列出具体实现
            InputMethodManager ins = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
            ins.hideSoftInputFromWindow(binding.selectCouplePhoneEt.getWindowToken(), 0);
        }
    }


    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopbarBackground(R.color.transparent);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.select_paired);
    }


    private void showAllowContactsDialog() {
        CommonMiddleDialog allowDialog = new CommonMiddleDialog(this);
        allowDialog.setTitle(R.string.allow_dialog_title);
        allowDialog.setInfo(R.string.allow_dialog_info);
        allowDialog.setCancel(R.string.allow_dialog_deny);
        allowDialog.setSure(R.string.allow_dialog_allow, v -> {
            PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.ALLOW_CONTACT_GET, true);
            openContacts();
            allowDialog.dismiss();
        });
        allowDialog.show();
    }


    private void openContacts() {
        if (!PermissionUtil.hasContactsPermission(this)) {
            return;
        }
        startActivityForResult(new Intent(this,
                TheHeartChooseActivity.class), 100);
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int grantResult : grantResults) {
            if (grantResult == PackageManager.PERMISSION_GRANTED) {
                contractLauncher.launch(new Intent(Intent.ACTION_PICK, ContactsContract.CommonDataKinds.Phone.CONTENT_URI));
            }
        }
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }

    /**
     * 验证手机号是否有效
     *
     * @param phone
     * @return
     */
    private boolean isPhoneValid(String phone) {
        if (countryCodeValue.equals("86")) {
            Pattern p = Pattern.compile("^1\\d{10}$");
            Matcher m = p.matcher(phone);
            return m.matches();
        } else {
            return phone != null && phone.length() > 0;
        }
    }

    /**
     * 取出手机号码中的无效字符, 返回简单的号码
     *
     * @param phone
     * @return
     */
    private String getSimplePhone(String phone) {
        if (phone == null) {
            return null;
        }
        // 过滤掉号码 中的空格和分割线
        char[] pp = new char[phone.length()];
        int j = 0;
        for (int i = 0; i < phone.length(); i++) {
            char c = phone.charAt(i);
            if (c != '-' && c != ' ') {
                pp[j++] = c;
            }
        }
        return new String(pp).trim();
    }
}
