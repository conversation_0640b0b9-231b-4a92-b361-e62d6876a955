package com.totwoo.totwoo.activity;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.inputmethod.InputMethodManager;
import android.widget.Toast;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ClickUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.event.EventBus;
import com.tencent.bugly.crashreport.CrashReport;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.LocalHttpJewelryInfo;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.databinding.ActivityPasswordLoginBinding;
import com.totwoo.totwoo.receiver.JpushReceiver;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.SimpleTextWatcher;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PasswordLoginActivity extends BaseActivity {
    public static final String PHONENUMBER = "phoneNumber";

    private String country_code_value;

    private ActivityPasswordLoginBinding binding;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityPasswordLoginBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        ClickUtils.expandClickArea(binding.passwordPolicyCheckbox, 30);

        country_code_value = PreferencesUtils.getString(ToTwooApplication.baseContext,
                CommonArgs.COUNTRY_CODE_KEY,
                Apputils.getSystemLanguageCountryCode(this));
        setCountryCode();

        String phoneNumber = getIntent().getStringExtra(PHONENUMBER);
        if (!TextUtils.isEmpty(phoneNumber)) {
            binding.passwordCodeEt.setText(phoneNumber);
            binding.passwordCodeEt.setSelection(phoneNumber.length());
        }
        binding.passwordPhoneEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    binding.passwordPhoneClearIv.setVisibility(View.VISIBLE);
                } else {
                    binding.passwordPhoneClearIv.setVisibility(View.INVISIBLE);
                }
            }
        });

        binding.passwordCodeEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    binding.passwordClearIv.setVisibility(View.VISIBLE);
                    binding.passwordSeeIv.setVisibility(View.VISIBLE);
                } else {
                    binding.passwordClearIv.setVisibility(View.INVISIBLE);
                    binding.passwordSeeIv.setVisibility(View.INVISIBLE);
                }
            }
        });

        binding.passwordPolicyTv.setText(CommonUtils.stylePolicyString(this));
        binding.passwordPolicyTv.setMovementMethod(LinkMovementMethod.getInstance());


        RequestOptions options;
        if (PreferencesUtils.getInt(this, CommonArgs.PREF_LAST_GENDER, 0) == 0) {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        } else {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        }

        if (PreferencesUtils.getString(this, CommonArgs.PREF_LAST_ENCODE_PASSWORD, null) != null
                && PreferencesUtils.getString(this, CommonArgs.PREF_LAST_PHONE, null) != null) {
            binding.autoHeadIconRiv.setVisibility(View.VISIBLE);

            String realImageUrl = HttpHelper.getRealImageUrl(PreferencesUtils.getString(this, CommonArgs.PREF_LAST_HEAD_ICON, ""));
            if (TextUtils.isEmpty(realImageUrl)) {
                binding.autoHeadIconRiv.setVisibility(View.GONE);
            }
            Glide.with(this).load(realImageUrl).apply(options).into(binding.autoHeadIconRiv);

        } else {
            binding.autoHeadIconRiv.setVisibility(View.GONE);
        }

        initListener();
    }

    private void initListener() {
        binding.autoHeadIconRiv.setOnClickListener(v -> {
            finish();
        });

        binding.passwordCountryCodeTv.setOnClickListener(v -> {
            Intent intent = new Intent(this, CountryCodeListActivity.class);
            startActivityForResult(intent, 0);
            // 切换动画
            overridePendingTransition(R.anim.activity_fade_in,
                    R.anim.activity_fade_out);
        });

        binding.passwordPhoneClearIv.setOnClickListener(v -> binding.passwordPhoneEt.setText(""));
        binding.passwordLoginTv.setOnClickListener(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_CLICKPWDLOGIN);
            doLogin();
        });

        binding.passwordLoginRegisterTv.setOnClickListener(v -> {
            Intent intent = new Intent(this, RegisterActivity.class);
            intent.putExtra(CommonArgs.PREF_LAST_PHONE, binding.passwordPhoneEt.getText().toString());
            startActivity(intent);
            // 切换动画
            overridePendingTransition(R.anim.activity_fade_in,
                    R.anim.activity_fade_out);
        });

        binding.passwordClearIv.setOnClickListener(v -> binding.passwordCodeEt.setText(""));
        binding.passwordSeeIv.setOnClickListener(v -> hideOrShowPassword());
        binding.passwordForgetTv.setOnClickListener(v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_CLICKFORGETPWD);

            Intent forgetPasswordIntent = new Intent(this, ForgetPasswordPhoneActivity.class);
            forgetPasswordIntent.putExtra(ForgetPasswordPhoneActivity.PHONENUMBER, binding.passwordPhoneEt.getText().toString());
            startActivity(forgetPasswordIntent);
            // 切换动画
            overridePendingTransition(R.anim.activity_fade_in,
                    R.anim.activity_fade_out);
        });
    }


    private void hideOrShowPassword() {
        if (binding.passwordCodeEt.getInputType() != 128) {
            binding.passwordSeeIv.setImageResource(R.drawable.password_eye_open_black);
            binding.passwordCodeEt.setInputType(128);
        } else {
            binding.passwordSeeIv.setImageResource(R.drawable.password_eye_close_black);
            binding.passwordCodeEt.setInputType(129);
        }
        if (binding.passwordCodeEt.getText() != null) {
            binding.passwordCodeEt.setSelection(binding.passwordCodeEt.getText().length());
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (resultCode) {
            case 0:
                if (data != null) {
                    country_code_value = data.getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                    setCountryCode();
                }
                break;

            default:
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 验证手机号是否有效
     *
     * @param phone
     * @return
     */
    private boolean isPhoneValid(String phone) {
        if (country_code_value.equals("86")) {
            Pattern p = Pattern.compile("^1\\d{10}$");
            Matcher m = p.matcher(phone);
            return m.matches();
        }
        return true;

    }

    private void setCountryCode() {
        binding.passwordCountryCodeTv.setText("+" + country_code_value);
    }

    private void transverseShakeAnim(View view) {
        ObjectAnimator objectAnimator =
                ObjectAnimator.ofFloat(view, View.TRANSLATION_X, 0f, 20f, 0f, 20f, 0f, 20f, 20f, 0);

        objectAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        objectAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
            }
        });
        objectAnimator.setDuration(300);
        objectAnimator.start();
    }

    private void doLogin() {
        String phone = binding.passwordPhoneEt.getText().toString();
        String vercode = binding.passwordCodeEt.getText().toString();
        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShort(this, R.string.error_invalid_phone);
        } else if (!isPhoneValid(phone)) {
            ToastUtils.showShort(this, R.string.error_incorrect_phone);
        } else if (TextUtils.isEmpty(vercode)) {
            ToastUtils.showShort(this, R.string.error_invalid_password);
        } else if (!CommonUtils.isPasswordValid(vercode)) {
            ToastUtils.show(this, R.string.set_password_error_length, Toast.LENGTH_SHORT);
        } else if (!binding.passwordPolicyCheckbox.isChecked()) {
//            ToastUtils.showShort(this, getString(R.string.terms_agree_tips));
            ToastUtils.showShort(this,getString(R.string.terms_agree_tips));
            KeyboardUtils.hideSoftInput(this);
            transverseShakeAnim(binding.llPolicy);
        } else {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(binding.passwordLoginTv.getWindowToken(), 0);
            String registerId = PreferencesUtils.getString(this, JpushReceiver.REGISTER_ID, "");
            LogUtils.e("registerId = " + registerId);
            int time = (int) (System.currentTimeMillis() / 1000);
            showProgressDialog();
            // 使用新的灵活错误处理方法
            launchRequestWithFlexibleError(
                    HttpHelper.loginV3Service.loginPassword(country_code_value + phone, time, CommonUtils.pwdEncode(vercode), registerId, HttpHelper.genNewSign(time, country_code_value + phone)),
                    loginInfoBeanHttpBaseBean -> {
                        CrashReport.setUserId(phone);
                        PreferencesUtils.put(PasswordLoginActivity.this, CommonArgs.PREF_LAST_ENCODE_PASSWORD, CommonUtils.pwdEncode(vercode));
                        PreferencesUtils.put(PasswordLoginActivity.this, CommonArgs.COUNTRY_CODE_KEY, country_code_value);
                        CommonUtils.setInfo(PasswordLoginActivity.this, loginInfoBeanHttpBaseBean);
                        TimInitBusiness.login();

                        launchRequestWithFlexibleError(
                                HttpHelper.multiJewelryService.getJewelryList(),
                                listData -> {
                                    dismissProgressDialog();
                                    if (listData!= null && !listData.isEmpty()) {
                                        for (LocalHttpJewelryInfo info : listData) {
                                            LocalJewelryDBHelper.getInstance().addBean(new LocalJewelryInfo(info.getMac_address(), info.getDevice_name(), info.getIs_select(), info.getCreate_time() * 1000));
                                        }
                                    }
                                    goNext();
                                },
                                fail -> {
                                    dismissProgressDialog();
                                    return false;
                                },false
                        );
                    }, fail -> {
                        dismissProgressDialog();
                        if (fail.getErrorCode() == 703) {
                            ToastUtils.showShort(PasswordLoginActivity.this, R.string.no_password_yet);
                            return true; // 已处理，不执行默认Toast
                        }
                        // 其他错误使用默认处理（WrapperSubscriber的Toast）
                        return false;
                    },
                    false // 不显示加载框（已经手动显示了）
            );
        }
    }

    private void goNext() {
        try {
            if (!LocalJewelryDBHelper.getInstance().getAllBeans().isEmpty()) {
                LocalJewelryInfo info = LocalJewelryDBHelper.getInstance().getSelectedBean();
                PreferencesUtils.put(PasswordLoginActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, info.getMac_address());
                PreferencesUtils.put(PasswordLoginActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, info.getName());
                BluetoothManage.getInstance().startBackgroundScan();
//                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
                LogUtils.e("HomeActivityControl");
                HomeActivityControl.getInstance().connectJew(PasswordLoginActivity.this);
            } else {
//                if (ToTwooApplication.isInfoSetFinish(ToTwooApplication.owner)) {
//                    startActivity(new Intent(this, JewelrySelectActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
//                } else {
//                    startActivity(new Intent(this, InitInfoActivity.class)
//                            .putExtra(InitInfoActivity.INIT_INFO, true)
//                            .putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
//                }
                HomeActivityControl.getInstance().openHomeActivity(PasswordLoginActivity.this);
                finish();
            }
        } catch (DbException e) {
            e.printStackTrace();
        }
        EventBus.onPostReceived(S.E.E_LOGIN_SUCCESS, null);
        ActivityUtils.finishActivity(AutoLoginActivity.class);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理 binding 引用
        binding = null;
    }
}
