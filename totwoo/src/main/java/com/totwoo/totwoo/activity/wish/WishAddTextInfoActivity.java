package com.totwoo.totwoo.activity.wish;

import android.animation.Animator;
import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.event.EventBus;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.WishInfoBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Subscriber;

public class WishAddTextInfoActivity extends BaseActivity {
    @BindView(R.id.wish_add_text_cv)
    CardView mAddTextBg;
    @BindView(R.id.wish_add_text_et)
    EditText mAddEt;
    @BindView(R.id.wish_add_text_count_tv)
    TextView mCountBottomTv;
    @BindView(R.id.wish_text_save_lv)
    LottieAnimationView mSaveLv;
    @BindView(R.id.wish_text_save_bg)
    View saveBg;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_add_text);
        ButterKnife.bind(this);
        mAddEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null && s.length() > 0) {
                    int len = s.length();
                    if (len > 200) {
                        s = s.subSequence(0, 200);
                        mAddEt.setText(s);
                        ToastUtils.showShort(WishAddTextInfoActivity.this, getString(R.string.wish_out_of_limit));
                        mAddEt.setSelection(200);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null && !TextUtils.isEmpty(s.toString())) {
                    int length = s.toString().length();
                    if(length >= 200){
                        length = 200;
                    }
                    mCountBottomTv.setText(length + "/200");
                } else {
                    mCountBottomTv.setText("0/200");
                }
            }
        });
        //当键盘弹出隐藏的时候会 调用此方法。
        mAddEt.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
            Rect r = new Rect();
            //获取当前界面可视部分
            WishAddTextInfoActivity.this.getWindow().getDecorView().getWindowVisibleDisplayFrame(r);
            //获取屏幕的高度
            int screenHeight = WishAddTextInfoActivity.this.getWindow().getDecorView().getRootView().getHeight();
            //此处就是用来获取键盘的高度的， 在键盘没有弹出的时候 此高度为0 键盘弹出的时候为一个正数
            setCardLayout(r.bottom);
        });
        BluetoothManage.getInstance().connectedStatus();
        setCardLayout(CommonUtils.getScreenHeight());
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopLeftOnclik(v -> hintAndBack());
        setTopTitleColor(getResources().getColor(R.color.white));
        setTopRightString(R.string.wish_add_text_finish);
        getTopRightTv().setTextColor(getResources().getColor(R.color.white));
        setTopRightOnClick(v -> postInfo());
    }

    private void setCardLayout(int height) {
        RelativeLayout.LayoutParams layoutParams;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            layoutParams = new RelativeLayout.LayoutParams(CommonUtils.getScreenWidth() - CommonUtils.dip2px(WishAddTextInfoActivity.this, 28), height - CommonUtils.dip2px(WishAddTextInfoActivity.this, 80) - Apputils.getStatusHeight(this));
        } else {
            layoutParams = new RelativeLayout.LayoutParams(CommonUtils.getScreenWidth() - CommonUtils.dip2px(WishAddTextInfoActivity.this, 28), height - CommonUtils.dip2px(WishAddTextInfoActivity.this, 80));
        }
        layoutParams.addRule(RelativeLayout.BELOW, R.id.totwoo_topbar_layout);
        layoutParams.setMargins(CommonUtils.dip2px(this, 14), CommonUtils.dip2px(this, 10), CommonUtils.dip2px(this, 14), CommonUtils.dip2px(this, 14));
        mAddTextBg.setLayoutParams(layoutParams);
    }
    private boolean postClicked = false;
    private void postInfo() {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(this, R.string.wish_add_text_not_connect);
            return;
        }
        String content = mAddEt.getText().toString().trim();
        if (TextUtils.isEmpty(content)) {
            ToastUtils.showShort(WishAddTextInfoActivity.this, getString(R.string.wish_add_text_empty));
            return;
        }
        if(postClicked){
            return;
        }
        postClicked = true;
        HttpHelper.wishService.saveWish(CommonArgs.COMMON_SEND_TYPE_TEXT, null, null, null, null, content)
                .compose(HttpHelper.<HttpBaseBean<WishInfoBean>>rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<WishInfoBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        postClicked = false;
                        ToastUtils.showShort(WishAddTextInfoActivity.this,getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<WishInfoBean> wishInfoHttpBaseBean) {
                        if (wishInfoHttpBaseBean.getErrorCode() == 0) {
                            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                            // 隐藏软键盘
                            try {
                                if (imm != null)
                                    imm.hideSoftInputFromWindow(getWindow().getDecorView().getWindowToken(), 0);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            WishInfoBean wishInfoBean = wishInfoHttpBaseBean.getData();
                            showSuccessAnim(wishInfoBean);
                        }
                    }
                });
    }

    @Override
    public void onBackPressed() {
        hintAndBack();
    }

    private void hintAndBack() {
        if (TextUtils.isEmpty(mAddEt.getText().toString())) {
            finish();
        } else {
            showNotSaveDialog();
        }
    }

    private void showNotSaveDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(WishAddTextInfoActivity.this);
        commonMiddleDialog.setMessage(R.string.wish_add_cancel);
        commonMiddleDialog.setSure(v -> finish());
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    private void showSuccessAnim(final WishInfoBean wishInfoBean) {
        saveBg.setVisibility(View.VISIBLE);
        mSaveLv.setVisibility(View.VISIBLE);
        mSaveLv.setImageAssetsFolder("lottie_wish_save/");
        mSaveLv.setAnimation("wish_save.json");
        mSaveLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                startActivity(new Intent(WishAddTextInfoActivity.this, WishDetailInfoActivity.class).putExtra(WishDetailInfoActivity.WISH_BEAN, wishInfoBean).putExtra(WishDetailInfoActivity.SHOW_SHARE,true));
                EventBus.onPostReceived(S.E.E_WISH_POST_SUCCESSED, null);
                finish();
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mSaveLv.playAnimation();

        BluetoothManage.getInstance().notifyJewelry(6,0x0000ff);
    }
}
