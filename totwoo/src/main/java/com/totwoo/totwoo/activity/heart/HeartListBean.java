package com.totwoo.totwoo.activity.heart;

import com.etone.framework.utils.JSONUtils;

/**
 * Created by x<PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/5/26.
 */

public class HeartListBean
{
    public String id;
    public String talkId;
    public String senderTotwoo_id;
    public String receiverTotwoo_id;
    public String send_from;
    public String offlineTime;
    public long createTime;
    public String updateTime;
    public int consonance;
    public String content;

    public String otherContent;
    public long otherTime;

    public boolean isSelf;  //true:self, false:paired

    public HeartListBean(String json, String pairedId)
    {
        this.id = JSONUtils.getString (json, "id", "");
        this.talkId = JSONUtils.getString (json, "talkId", "");
        this.senderTotwoo_id = JSONUtils.getString (json, "senderTotwoo_id", "");
        this.receiverTotwoo_id = JSONUtils.getString (json, "receiverTotwoo_id", "");
        this.send_from = JSONUtils.getString(json, "send_from", "");
        this.offlineTime = JSONUtils.getString(json, "offlineTime", "");
        this.consonance = JSONUtils.getInt(json, "consonance", 0);
        this.createTime = JSONUtils.getLong(json, "createTime", 0);
        this.updateTime = JSONUtils.getString(json, "updateTime", "");
        this.content = JSONUtils.getString(json, "content", "");

        if (senderTotwoo_id.equals(pairedId))
            isSelf = false;
    }
}
