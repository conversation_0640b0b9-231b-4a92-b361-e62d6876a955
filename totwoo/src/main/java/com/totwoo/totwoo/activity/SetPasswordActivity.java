package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Subscriber;

public class SetPasswordActivity extends BaseActivity {
    @BindView(R.id.set_password_clear_iv)
    ImageView set_password_clear_iv;
    @BindView(R.id.set_password_see_iv)
    ImageView set_password_see_iv;
    @BindView(R.id.set_password_again_clear_iv)
    ImageView set_password_again_clear_iv;
    @BindView(R.id.set_password_again_see_iv)
    ImageView set_password_again_see_iv;
    @BindView(R.id.set_password_et)
    EditText set_password_et;
    @BindView(R.id.set_password_again_et)
    EditText set_password_again_et;
    @BindView(R.id.set_password_hint_tv)
    TextView set_password_hint_tv;
    @BindView(R.id.set_password_finish_tv)
    TextView mFinishTv;
    private boolean isFromLogin = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_set_password);
        ButterKnife.bind(this);
        isFromLogin = TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN);

        if(!isFromLogin){
            set_password_hint_tv.setVisibility(View.GONE);
        }

        set_password_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if(!TextUtils.isEmpty(s.toString())){
                    set_password_clear_iv.setVisibility(View.VISIBLE);
                    setFinishTvClickable(!TextUtils.isEmpty(set_password_again_et.getText().toString().trim()));
                }else{
                    set_password_clear_iv.setVisibility(View.INVISIBLE);
                    setFinishTvClickable(false);
                }
            }
        });

        set_password_again_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if(!TextUtils.isEmpty(s.toString())){
                    set_password_again_clear_iv.setVisibility(View.VISIBLE);
                    setFinishTvClickable(!TextUtils.isEmpty(set_password_et.getText().toString().trim()));
                }else{
                    set_password_again_clear_iv.setVisibility(View.INVISIBLE);
                    setFinishTvClickable(false);
                }
            }
        });
    }

    private void setFinishTvClickable(boolean clickable){
        if(clickable){
            mFinishTv.setEnabled(true);
        }else{
            mFinishTv.setEnabled(false);
        }
    }

    @OnClick({R.id.set_password_clear_iv, R.id.set_password_see_iv, R.id.set_password_again_clear_iv,
            R.id.set_password_again_see_iv, R.id.set_password_finish_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.set_password_clear_iv:
                set_password_et.setText("");
                break;
            case R.id.set_password_see_iv:
                hideOrShowPassword(set_password_et,set_password_see_iv);
                break;
            case R.id.set_password_again_clear_iv:
                set_password_again_et.setText("");
                break;
            case R.id.set_password_again_see_iv:
                hideOrShowPassword(set_password_again_et,set_password_again_see_iv);
                break;
            case R.id.set_password_finish_tv:
                confirm();
                break;
        }
    }

    private void hideOrShowPassword(EditText mCodeEt,ImageView mSeeIv){
        if(mCodeEt.getInputType() != 128){
            mSeeIv.setImageResource(R.drawable.password_eye_open_black);
            mCodeEt.setInputType(128);
        }else{
            mSeeIv.setImageResource(R.drawable.password_eye_close_black);
            mCodeEt.setInputType(129);
        }
        if(mCodeEt.getText() != null){
            mCodeEt.setSelection(mCodeEt.getText().length());
        }
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        if (isFromLogin) {
            setTopRightString(R.string.skip);
            setTopRightOnClick(v -> {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_SETPWD_CLICKSKIP);
                HttpHelper.loginService.skipPwd(2001)
                        .compose(HttpHelper.rxSchedulerHelper())
                        .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {
                                ToastUtils.show(SetPasswordActivity.this,R.string.error_net,Toast.LENGTH_SHORT);
                            }

                            @Override
                            public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                                if(objectHttpBaseBean.getErrorCode() == 0){
                                    goNext();
                                }
                            }
                        });
                });
        }else{
            setTopBackIcon(R.drawable.back_icon_black);
            setTopLeftOnclik(v -> finish());
        }
    }

    private void confirm(){
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_SETPWD_CLICKDONE);
        String password = set_password_et.getText().toString().trim();
        String password_check = set_password_again_et.getText().toString().trim();

        if(TextUtils.isEmpty(password)){
            ToastUtils.show(SetPasswordActivity.this,R.string.set_password_error_length,Toast.LENGTH_SHORT);
            return;
        }
        else if (TextUtils.isEmpty(password_check)) {
            ToastUtils.show(SetPasswordActivity.this, R.string.set_password_error_different, Toast.LENGTH_SHORT);
            return;
        }
//        else if(password.length() < 6 || password.length() > 16){
//            ToastUtils.show(SetPasswordActivity.this,R.string.set_password_error_length,Toast.LENGTH_SHORT);
//            return;
//        }
        else if (!CommonUtils.isPasswordValid(password)) {
            ToastUtils.show(SetPasswordActivity.this, R.string.set_password_error_length, Toast.LENGTH_SHORT);
            return;
        } else if (!TextUtils.equals(password, password_check)) {
            ToastUtils.show(SetPasswordActivity.this, R.string.set_password_error_different, Toast.LENGTH_SHORT);
            return;
        }
        HttpHelper.loginService.setPwd(ToTwooApplication.owner.getPhone(),CommonUtils.pwdEncode(password),CommonUtils.pwdEncode(password_check))
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.show(SetPasswordActivity.this,R.string.error_net,Toast.LENGTH_SHORT);
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> loginInfoBeanHttpBaseBean) {
                        if(loginInfoBeanHttpBaseBean.getErrorCode() == 0){
                            PreferencesUtils.put(SetPasswordActivity.this, CommonArgs.PREF_LAST_ENCODE_PASSWORD, CommonUtils.pwdEncode(password));
                            ToastUtils.show(SetPasswordActivity.this,R.string.set_password_success,Toast.LENGTH_SHORT);
                            setResult(RESULT_OK);
                            goNext();
                        }
                    }
                });
    }

    private void goNext(){
        if(isFromLogin){
            startActivity(new Intent(this, JewelrySelectActivity.class).putExtra(CommonArgs.FROM_TYPE,CommonArgs.LOGIN));
        }
        finish();
    }
}
