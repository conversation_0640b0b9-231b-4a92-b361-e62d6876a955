package com.totwoo.totwoo.activity;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.VideoInfo;
import com.totwoo.totwoo.record.RecorderConfig;
import com.totwoo.totwoo.utils.CommonUtils;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

public class VideoSelectActivity extends BaseActivity {
    @BindView(R.id.video_select_rv)
    RecyclerView mSelectRv;
    public static String EXTRA_MAX_VIDEO_FILE_SIZE_LIMIT = "extra_max_video_file_size_limit";

    private ArrayList<VideoInfo> videos;
    private LoaderManager.LoaderCallbacks<Cursor> loaderCallbacks;
    private VideoSelectAdapter videoSelectAdapter;
    private int width;
    public static final int VIDEO_SELECT_SUCCESS = 0;
    public static final String APP_SELECT_VIDEO_PATH = "APP_SELECT_VIDEO_PATH";
    public static final String APP_SELECT_VIDEO_SIZE = "APP_SELECT_VIDEO_SIZE";
    private int maxVideoFileSizeLimit;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_select);
        ButterKnife.bind(this);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(VideoSelectActivity.this, 4);
        videos = new ArrayList<>();
        mSelectRv.setLayoutManager(gridLayoutManager);
        videoSelectAdapter = new VideoSelectAdapter();
        mSelectRv.setAdapter(videoSelectAdapter);
        width = (CommonUtils.getScreenWidth()) / 4;

        maxVideoFileSizeLimit = getIntent().getIntExtra(EXTRA_MAX_VIDEO_FILE_SIZE_LIMIT, RecorderConfig.DEFAULT_MAX_VIDEO_FILE_SIZE_LIMIT);

        String permission = Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ? Manifest.permission.READ_MEDIA_VIDEO : Manifest.permission.READ_EXTERNAL_STORAGE;

        if (ActivityCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
            initData();
        } else {
            ActivityCompat.requestPermissions(this, new String[]{permission}, 33);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == 33 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            initData();
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.select_video);
        setTopLeftOnclik(v -> {
            setResult(1);
            finish();
        });
    }

    private void initData() {
        loaderCallbacks = new LoaderManager.LoaderCallbacks<Cursor>() {
            private final String[] VIDEO_PROJECTION = {
                    MediaStore.Video.Media.DATA,
                    MediaStore.Video.Media.ALBUM,
                    MediaStore.Video.Media.DATE_ADDED,
                    MediaStore.Video.Media.SIZE,
                    MediaStore.Video.Media.DURATION
            };

            @Override
            public Loader<Cursor> onCreateLoader(int i, Bundle bundle) {
                LogUtils.e("aab onCreateLoader");
                return new CursorLoader(VideoSelectActivity.this, MediaStore.Video.Media.EXTERNAL_CONTENT_URI, VIDEO_PROJECTION, null, null, VIDEO_PROJECTION[2] + " DESC");
            }

            @Override
            public void onLoadFinished(Loader<Cursor> loader, Cursor cursor) {
                LogUtils.e("aab onLoadFinished");
                if (cursor != null) {
                    int count = cursor.getCount();
                    if (count > 0) {
                        cursor.moveToFirst();
                        do {
                            String path = cursor.getString(cursor.getColumnIndexOrThrow(VIDEO_PROJECTION[0]));
                            String thumbnail = cursor.getString(cursor.getColumnIndexOrThrow(VIDEO_PROJECTION[1]));
                            long size = cursor.getLong(cursor.getColumnIndexOrThrow(VIDEO_PROJECTION[3]));
                            long duration = cursor.getLong(cursor.getColumnIndexOrThrow(VIDEO_PROJECTION[4]));
                            if (size > 1024 * 5) {
                                VideoInfo videoInfo = new VideoInfo(path, thumbnail, size, duration);
                                videos.add(videoInfo);
                            }
                        } while (cursor.moveToNext());

                        LogUtils.e("aab videos.size() = " + videos.size());
                        videoSelectAdapter.notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onLoaderReset(Loader<Cursor> loader) {

            }
        };
        LogUtils.e("aab restartLoader");
        LoaderManager.getInstance(this).restartLoader(0, null, loaderCallbacks);
    }

    public class VideoSelectAdapter extends RecyclerView.Adapter<VideoSelectAdapter.ViewHolder> {
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.video_select_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {

            ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(width, width);
            holder.mMainCl.setLayoutParams(layoutParams);
            Glide.with(VideoSelectActivity.this).load(videos.get(position).getPath()).into(holder.mMainIv);
            holder.mSizeTv.setText(CommonUtils.byteTrans(videos.get(position).getSize()));
            holder.mTimeTv.setText(CommonUtils.millionsTrans(videos.get(position).getDuration()));
            holder.mMainCl.setOnClickListener(v -> {
                if (videos.get(position).getSize() > 50 * 1024 * 1024) {
                    Toast.makeText(VideoSelectActivity.this, getString(R.string.error_video_size, maxVideoFileSizeLimit / 1024 / 1024), Toast.LENGTH_LONG).show();
                    return;
                }
                Intent intent = new Intent();
                intent.putExtra(APP_SELECT_VIDEO_PATH, videos.get(position).getPath());
                intent.putExtra(APP_SELECT_VIDEO_SIZE, videos.get(position).getSize());
                setResult(VIDEO_SELECT_SUCCESS, intent);
                finish();
            });
        }

        @Override
        public int getItemCount() {
            return videos == null ? 0 : videos.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.video_select_main_cl)
            ConstraintLayout mMainCl;
            @BindView(R.id.video_select_main_iv)
            ImageView mMainIv;
            @BindView(R.id.video_select_size_tv)
            TextView mSizeTv;
            @BindView(R.id.video_select_time_tv)
            TextView mTimeTv;

            public ViewHolder(View itemView) {
                super(itemView);
                ButterKnife.bind(this, itemView);
            }
        }
    }
}
