package com.totwoo.totwoo.activity;

import android.graphics.Typeface;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.databinding.ActivityNotifitionGuideBinding;
import com.totwoo.totwoo.utils.PermissionUtil;


/**
 * <AUTHOR>
 * @des:
 * @date 2024/9/19 15:37
 */
public class NotificationGuideActivity extends BaseActivity {
    private ActivityNotifitionGuideBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityNotifitionGuideBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        initView();

        //加粗
        String content = getString(R.string.page_nofiti_top_des);
        String contentBold = getString(R.string.lock_screen);
        SpannableString spannableString = new SpannableString(content);

        int startIndex = content.lastIndexOf(contentBold);  // 从头开始查找
        if (startIndex >= 0) {  // 确保找到了“锁屏”
            int endIndex = startIndex + contentBold.length();

            // 设置加粗样式
            spannableString.setSpan(new StyleSpan(Typeface.BOLD), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

// 设置到 TextView
        binding.tvTips.setText(spannableString);

    }


    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v ->finish());
        setTopTitle(R.string.page_permission__notification);
    }




    private void initView() {
        String language = Apputils.getSystemLanguage(this);
        switch (language) {
            case "zh":
               binding.notificationIv.setImageResource(R.drawable.notifi_zh);
               break;
            case "fr":
                binding.notificationIv.setImageResource(R.drawable.notifi_fr);
                break;
            case "de":
                binding.notificationIv.setImageResource(R.drawable.notifi_de);
                break;
            case "it":
                binding.notificationIv.setImageResource(R.drawable.notifi_it);
                break;
            case "ja":
                binding.notificationIv.setImageResource(R.drawable.notifi_ja);
                break;
            case "es":
                binding.notificationIv.setImageResource(R.drawable.notifi_es);
                break;
            case "ko":
                binding.notificationIv.setImageResource(R.drawable.notifi_ko);
                break;
            case "ru":
                binding.notificationIv.setImageResource(R.drawable.notifi_ru);
                break;
            case "en":
            default:
                binding.notificationIv.setImageResource(R.drawable.notifi);
        }

        binding.toSet.setOnClickListener(v -> {
            PermissionUtil.gotoNotificationSetting(this);
        });
    }
}
