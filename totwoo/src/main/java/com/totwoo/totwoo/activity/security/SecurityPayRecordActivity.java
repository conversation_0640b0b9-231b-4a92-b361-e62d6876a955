//package com.totwoo.totwoo.activity.security;
//
//import android.os.Bundle;
//import android.text.TextUtils;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.recyclerview.widget.LinearLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.totwoo.totwoo.R;
//import com.totwoo.totwoo.activity.BaseActivity;
//import com.totwoo.totwoo.bean.PayRecordBean;
//import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
//import com.totwoo.totwoo.ble.BleParams;
//import com.totwoo.totwoo.ble.DateUtil;
//import com.totwoo.totwoo.utils.HttpHelper;
//import com.totwoo.totwoo.utils.PreferencesUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import rx.Subscriber;
//
//public class SecurityPayRecordActivity extends BaseActivity {
//    @BindView(R.id.pay_record_rv)
//    RecyclerView mRecyclerView;
//    @BindView(R.id.pay_record_empty_ll)
//    LinearLayout mEmptyLl;
//
//    private RecordAdapter recordAdapter;
//    private ArrayList<PayRecordBean> recordBeans;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_security_pay_record);
//        ButterKnife.bind(this);
//
//        recordBeans = new ArrayList<>();
//
//        mRecyclerView.setLayoutManager(new LinearLayoutManager(SecurityPayRecordActivity.this));
//        recordAdapter = new RecordAdapter();
//        mRecyclerView.setAdapter(recordAdapter);
//
//        HttpHelper.payService.listOrders(PreferencesUtils.getString(this, BleParams.SAFE_JEWLERY_IMEI, ""))
//                .compose(HttpHelper.rxSchedulerHelper())
//                .subscribe(new Subscriber<HttpBaseBean<List<PayRecordBean>>>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//
//                    }
//
//                    @Override
//                    public void onNext(HttpBaseBean<List<PayRecordBean>> listHttpBaseBean) {
//                        if (listHttpBaseBean.getErrorCode() == 0 && listHttpBaseBean.getData() != null && listHttpBaseBean.getData().size() > 0) {
//                            recordBeans = (ArrayList<PayRecordBean>) listHttpBaseBean.getData();
//                            recordAdapter.notifyDataSetChanged();
//                        } else {
//                            mEmptyLl.setVisibility(View.VISIBLE);
//                            mRecyclerView.setVisibility(View.GONE);
//                        }
//                    }
//                });
//    }
//
//    @Override
//    protected void initTopBar() {
//        super.initTopBar();
//        setTopBackIcon(R.drawable.back_icon_black);
//        setTopLeftOnclik(v -> finish());
//        setTopTitle(R.string.safe_security_fee_record);
//        setTopLeftOnclik(v -> finish());
//    }
//
//    protected class RecordAdapter extends RecyclerView.Adapter<RecordAdapter.ViewHolder> {
//        @NonNull
//        @Override
//        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
//            View view = LayoutInflater.from(SecurityPayRecordActivity.this).inflate(R.layout.pay_record_item, null);
//            return new ViewHolder(view);
//        }
//
//        @Override
//        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
//            holder.tvTitle.setText(getString(R.string.safe_security_fee_pay_plan,recordBeans.get(position).getPay_count()));
//            try {
//                holder.tvTime.setText(DateUtil.getDateAllFormatToString(Long.valueOf(recordBeans.get(position).getPay_time()) * 1000));
//            } catch (NumberFormatException e) {
//                e.printStackTrace();
//            }
//            holder.tvCost.setText("- " + recordBeans.get(position).getPay_price());
//            if(TextUtils.equals(recordBeans.get(position).getPay_type(),"ali")){
//                holder.tvType.setText(R.string.safe_security_fee_type_ali);
//            }else{
//                holder.tvType.setText(R.string.safe_security_fee_type_wechat);
//            }
//        }
//
//        @Override
//        public int getItemCount() {
//            return recordBeans != null ? recordBeans.size() : 0;
//        }
//
//        public class ViewHolder extends RecyclerView.ViewHolder {
//            @BindView(R.id.pay_record_item_title_tv)
//            TextView tvTitle;
//            @BindView(R.id.pay_record_item_time_tv)
//            TextView tvTime;
//            @BindView(R.id.pay_record_item_cost_tv)
//            TextView tvCost;
//            @BindView(R.id.pay_record_item_type_tv)
//            TextView tvType;
//
//            public ViewHolder(@NonNull View itemView) {
//                super(itemView);
//                ButterKnife.bind(this, itemView);
//            }
//        }
//    }
//}
