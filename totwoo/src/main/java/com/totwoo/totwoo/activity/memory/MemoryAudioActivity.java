package com.totwoo.totwoo.activity.memory;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.czt.mp3recorder.MP3Recorder;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.SceneAnimation;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

/**
 * Created by xinyoulingxi on 2017/8/8.
 */

public class MemoryAudioActivity extends BaseActivity
{
    public static final int[] gifs = new int[]{
            R.drawable.voice_01849, R.drawable.voice_01851, R.drawable.voice_01853, R.drawable.voice_01855, R.drawable.voice_01857, R.drawable.voice_01859, R.drawable.voice_01861, R.drawable.voice_01863, R.drawable.voice_01865,
            R.drawable.voice_01867, R.drawable.voice_01869, R.drawable.voice_01871, R.drawable.voice_01873, R.drawable.voice_01875, R.drawable.voice_01877, R.drawable.voice_01879, R.drawable.voice_01881,
            R.drawable.voice_01883, R.drawable.voice_01885, R.drawable.voice_01887, R.drawable.voice_01889, R.drawable.voice_01891, R.drawable.voice_01893, R.drawable.voice_01895, R.drawable.voice_01897,
            R.drawable.voice_01899, R.drawable.voice_01901, R.drawable.voice_01903, R.drawable.voice_01905, R.drawable.voice_01907, R.drawable.voice_01909, R.drawable.voice_01911, R.drawable.voice_01913,
            R.drawable.voice_01915, R.drawable.voice_01917, R.drawable.voice_01919, R.drawable.voice_01921, R.drawable.voice_01923, R.drawable.voice_01925, R.drawable.voice_01927, R.drawable.voice_01929,
            R.drawable.voice_01931, R.drawable.voice_01933, R.drawable.voice_01935, R.drawable.voice_01937, R.drawable.voice_01939, R.drawable.voice_01941, R.drawable.voice_01943, R.drawable.voice_01945,
            R.drawable.voice_01947, R.drawable.voice_01949, R.drawable.voice_01951
    };
    public static final String MAKE_CARD_AUDIO_RE_PATH = FileUtils.getCacheDir() + File.separator + "memory_audio.mp3";
    private MP3Recorder mRecorder = new MP3Recorder(new File(MAKE_CARD_AUDIO_RE_PATH));

    public static int VIDEO_RE_COMPLETE = 1;
    public static int VIDEO_RE_CANCEL = 0;

    @BindView(R.id.progress_left_iv)
    ImageView mProgressLeftIv;
    @BindView(R.id.progress_right_iv)
    ImageView mProgressRightIv;
    @BindView(R.id.guide_text_tv)
    TextView mGuideTextTv;
    @BindView(R.id.re_video_iv)
    ImageView mReVideoIv;
    @BindView(R.id.cancel_action_rect_ll)
    LinearLayout mCancelActionRectLl;
    @BindView (R.id.audio_gif)
    ImageView audioGif;

    @BindView (R.id.activity_memory_secs)
    TextView secs;

    int[] cancelPostion = new int[2];

    int cancelTop;

    int cancelBottom;


    private boolean reVideoing;
    private SecsRunnable runnable;

//    private MediaRecorder mMediaRecorder = new MediaRecorder();


    private long reSec;
    Subscription reVideoStart;

    // 输出宽度
    private static final int OUTPUT_WIDTH = 480;
    // 输出高度
    private static final int OUTPUT_HEIGHT = 480;

    private String content;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_audio);
        ButterKnife.bind(this);
        setResult(VIDEO_RE_CANCEL);
        init();
        runnable = new SecsRunnable();
        content = getIntent().getStringExtra(S.M.M_CONTENT);
    }

    private static final Object lock = new Object();

    private synchronized void gotoVedio2Activity()
    {
        synchronized (lock)
        {
            Intent intent = new Intent(this, MemoryAudioActivity2.class);
            intent.putExtra(S.M.M_CONTENT, content);
            startActivity(intent);
            this.finish();
        }
    }

    //AnimationDrawable ad;
    SceneAnimation sa;
    private void init()
    {
        //ad = (AnimationDrawable) audioGif.getDrawable();
        sa = new SceneAnimation(audioGif, gifs, 40, false);
        mReVideoIv.setOnTouchListener(new View.OnTouchListener()
        {
            @Override
            public boolean onTouch(View v, MotionEvent event)
            {
                switch (event.getAction())
                {
                    case MotionEvent.ACTION_DOWN:
                        //不显示拿不到区域
                        mGuideTextTv.setText(R.string.memory_audio_up_cancel);
                        mRecorder.setRecordSec(0);
                        mReVideoIv.setBackgroundResource(R.drawable.memory_vedioi_reing);
                        if (mCancelActionRectLl.getVisibility() == View.GONE)
                        {
                            mCancelActionRectLl.setAlpha(0);
                            mCancelActionRectLl.setVisibility(View.VISIBLE);
                        }
                        reSec = System.currentTimeMillis();
                        secs.setText("60s");
                        runnable.secs = 60;
                        mHandler.postDelayed(runnable, 1000);
                        secs.setVisibility(View.VISIBLE);
                        reVideoStart = Observable.timer(600, TimeUnit.MILLISECONDS, Schedulers.newThread())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Action1<Long>()
                                {
                                    @Override
                                    public void call(Long aLong)
                                    {
                                        if (!reVideoing)
                                        {
                                            startRecord();
                                            startPrgAnim();
                                        }
                                        reVideoing = true;
                                    }
                                });
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (cancelTop == 0 || cancelBottom == 0)
                        {
                            mCancelActionRectLl.getLocationOnScreen(cancelPostion);
                            cancelTop = cancelPostion[1];
                            cancelBottom = cancelTop + mCancelActionRectLl.getHeight();
                        }
                        //在区域内则取消
                        if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom)
                        {
                            if (mCancelActionRectLl.getAlpha() == 0)
                            {
                                mCancelActionRectLl.setAlpha(1);
                            }
                        }
                        else
                        {
                            if (mCancelActionRectLl.getAlpha() == 1)
                            {
                                mCancelActionRectLl.setAlpha(0);
                            }
                        }
                        break;
                    case MotionEvent.ACTION_CANCEL:
                    case MotionEvent.ACTION_UP:
                        mProgressRightIv.clearAnimation();
                        mProgressLeftIv.clearAnimation();
                        if (!reVideoing)
                        {
                            reVideoStart.unsubscribe();
                        }
                        mReVideoIv.setImageResource(R.drawable.memory_vedio_re);
                        reSec = System.currentTimeMillis() - reSec;
                        secs.setVisibility(View.INVISIBLE);
                        if (event.getRawY() > cancelTop && event.getRawY() < cancelBottom)
                        {
                        }
                        else if (reSec < 1300)
                        {
                            ToastUtils.showShort(MemoryAudioActivity.this, getString(R.string.memory_audio_time_short));
                        }
                        else
                        {
                            if (reVideoing)
                            {
                                mReVideoIv.postDelayed(new Runnable()
                                {
                                    @Override
                                    public void run()
                                    {
                                        stopRecord();
                                        reVideoing = false;
                                        LogUtils.i("stop");
                                    }
                                }, 1000);
                            }
                            break;
                        }

                        if (reVideoing)
                        {
                            stopRecord();
                            reVideoing = false;
                        }

                        mCancelActionRectLl.setVisibility(View.GONE);
                        mGuideTextTv.setText(R.string.memory_audio_start);
                        break;
                }
                return true;
            }
        });
    }

    private void startPrgAnim()
    {
        TranslateAnimation leftTa = new TranslateAnimation(0, -mProgressLeftIv.getWidth(), 0, 0);
        leftTa.setDuration(60000);
        mProgressLeftIv.startAnimation(leftTa);
        TranslateAnimation rightTa = new TranslateAnimation(0, mProgressLeftIv.getWidth(), 0, 0);
        rightTa.setDuration(60000);
        mProgressRightIv.startAnimation(rightTa);
        rightTa.setAnimationListener(new Animation.AnimationListener()
        {
            @Override
            public void onAnimationStart(Animation animation)
            {

            }

            @Override
            public void onAnimationEnd(Animation animation)
            {
                if (reVideoing)
                {
                    try
                    {
                        stopRecord();
                        reVideoing = false;
                        LogUtils.i("stop");
                        gotoVedio2Activity();
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation)
            {

            }
        });
    }

    @Override
    protected void initTopBar()
    {
        setTopBackIcon(R.drawable.memory_vedio_back);
        super.initTopBar();
    }

    /**
     * 开始录制
     */
    private void startRecord()
    {
        if (mRecorder.isRecording())
        {
            Toast.makeText(this, R.string.make_card_reing_video, Toast.LENGTH_SHORT).show();
            return;
        }

        // initialize video camera
        // 录制视频
        try
        {
            mRecorder.start();
            //ad.start();
            sa.start();
            if (!mRecorder.isRecording1())
            {
                reSec = System.currentTimeMillis();
                stopRecord();
                final CustomDialog dialog = new CustomDialog(this);
                dialog.setMessage(R.string.open_audio_error);
                dialog.setPositiveButton(R.string.immediately_receive, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + getPackageName())));
                        dialog.dismiss();
                    }
                });
                dialog.show();
                return;
            }
        }
        catch (IOException e)
        {
            Toast.makeText(this, R.string.make_card_reing_video_failure, Toast.LENGTH_SHORT).show();
        }
    }

    private class SecsRunnable implements Runnable
    {
        public int secs = 30;
        public SecsRunnable()
        {
        }

        @Override
        public void run()
        {
            MemoryAudioActivity.this.secs.setText(secs + "s");
            this.secs--;
            mHandler.postDelayed(this, 1000);
        }
    }

    /**
     * 停止录制
     */
    private void stopRecord()
    {
        mRecorder.stop();
        secs.setVisibility(View.INVISIBLE);
        mHandler.removeCallbacks(runnable);
        //ad.stop();
        sa.stop();
    }

    @Override
    protected void onPause()
    {
        super.onPause();
        mRecorder.stop();
    }
}