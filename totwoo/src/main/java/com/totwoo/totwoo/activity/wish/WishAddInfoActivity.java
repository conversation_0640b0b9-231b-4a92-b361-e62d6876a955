package com.totwoo.totwoo.activity.wish;

import android.animation.Animator;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.cardview.widget.CardView;

import com.airbnb.lottie.LottieAnimationView;
import com.etone.framework.event.EventBus;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.WishInfoBean;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import org.json.JSONArray;

import java.io.File;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class WishAddInfoActivity extends BaseActivity {
    @BindView(R.id.wish_add_edit_count_tv)
    TextView mCountTv;
    @BindView(R.id.wish_add_et)
    EditText mTextEt;
    @BindView(R.id.top_bar_back_btn)
    ImageView mBackIv;
    @BindView(R.id.top_bar_title_view)
    TextView mTitleTv;
    @BindView(R.id.top_bar_right_tv)
    TextView mRightTv;
    @BindView(R.id.wish_add_iv)
    ImageView mAddIv;
    @BindView(R.id.wish_add_info_cv)
    CardView mAddInfoCv;
    @BindView(R.id.wish_add_info_middle_control)
    ImageView mAddInfoControl;
    @BindView(R.id.wish_info_save_lv)
    LottieAnimationView mSaveLv;
    @BindView(R.id.wish_add_save_bg)
    View saveBg;
    @BindView(R.id.wish_add_save_progress)
    ProgressBar saveProgress;
    private int type;
    private String coverPath;
    private MediaPlayer mPlayer;
    private boolean isVoicePlaying = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_add_info);
        ButterKnife.bind(this);
        mTextEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null && s.length() > 0) {
                    int len = s.length();
                    if (len > 100) {
                        s = s.subSequence(0, 100);
                        mTextEt.setText(s);
                        ToastUtils.showShort(WishAddInfoActivity.this, getString(R.string.wish_out_of_limit));
                        mTextEt.setSelection(100);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null && !TextUtils.isEmpty(s.toString())) {
                    int length = s.toString().length();
                    if (length >= 100) {
                        length = 100;
                    }
                    mCountTv.setText(length + "/100");
                } else {
                    mCountTv.setText("0/100");
                }
            }
        });
        mBackIv.setImageResource(R.drawable.back_icon_black);
        mRightTv.setText(getString(R.string.wish_add_text_finish));
        mRightTv.setOnClickListener(v -> {
            switch (type) {
                case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                    upload(WishAddInfoActivity.this, CommonArgs.CACHE_WISH_IMAGE, null, 1);
                    break;
                case CommonArgs.COMMON_SEND_TYPE_SOUND:
                    upload(WishAddInfoActivity.this, CommonArgs.CACHE_WISH_AUDIO_PATH, null, 2);
                    break;
                case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                    uploadCover(WishAddInfoActivity.this, getIntent().getStringExtra(CommonArgs.COVER_PATH));
                    break;
            }
        });
        type = getIntent().getIntExtra(CommonArgs.FROM_TYPE, 0);
        coverPath = getIntent().getStringExtra(CommonArgs.COVER_PATH);

        int width = CommonUtils.getScreenWidth() - CommonUtils.dip2px(this, 28);
        int height = width / 8 * 5;
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(width, height);
        layoutParams.setMargins(CommonUtils.dip2px(this, 14), 0, CommonUtils.dip2px(this, 14), 0);
        mAddInfoCv.setLayoutParams(layoutParams);

        if (type == CommonArgs.COMMON_SEND_TYPE_IMAGE) {
            mAddIv.setImageBitmap(BitmapFactory.decodeFile(CommonArgs.CACHE_WISH_IMAGE));
            mTextEt.setHint(R.string.wish_add_image_hint);
        } else if (type == CommonArgs.COMMON_SEND_TYPE_SOUND) {
            mAddIv.setImageResource(R.drawable.wish_add_info_voice);
            mAddInfoControl.setImageResource(R.drawable.wish_add_info_voice_play);
            if (mPlayer == null)
                mPlayer = new MediaPlayer();
            try {
                mPlayer.setDataSource(CommonArgs.CACHE_WISH_AUDIO_PATH);
                mPlayer.prepare();
            } catch (Exception e) {
                e.printStackTrace();
            }
            mTextEt.setHint(R.string.wish_add_other_hint);
        } else if (type == CommonArgs.COMMON_SEND_TYPE_VIDEO) {
            mAddIv.setImageBitmap(BitmapFactory.decodeFile(coverPath));
            mAddInfoControl.setImageResource(R.drawable.wish_add_info_video_play);
            mTextEt.setHint(R.string.wish_add_other_hint);
        }
        BluetoothManage.getInstance().connectedStatus();
    }

    @OnClick({R.id.top_bar_back_btn, R.id.top_bar_right_tv, R.id.wish_add_iv, R.id.wish_add_info_delete_iv, R.id.wish_add_info_middle_control})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.top_bar_back_btn:
                showNotSaveDialog();
                break;
            case R.id.top_bar_right_tv:

                break;
            case R.id.wish_add_iv:
                if (type == CommonArgs.COMMON_SEND_TYPE_VIDEO) {
                    new PreviewConfig(getIntent().getStringExtra(CommonArgs.VIDEO_PATH), coverPath, null).goPreview(this);
                }
                break;
            case R.id.wish_add_info_delete_iv:
                showDeleteDialog();
                break;
            case R.id.wish_add_info_middle_control:
                if (type == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                    if (isVoicePlaying) {
                        mAddInfoControl.setImageResource(R.drawable.wish_add_info_voice_play);
                        mPlayer.pause();
                        isVoicePlaying = false;
                    } else {
                        mAddInfoControl.setImageResource(R.drawable.wish_add_info_voice_pause);
                        mPlayer.start();
                        isVoicePlaying = true;
                        //((AnimationDrawable) audioGif.getDrawable()).start();
                        mPlayer.setOnCompletionListener(mp -> {
                            mAddInfoControl.setImageResource(R.drawable.wish_add_info_voice_play);
                            isVoicePlaying = false;
                        });
                    }

                } else if (type == CommonArgs.COMMON_SEND_TYPE_VIDEO) {
                    new PreviewConfig(
                            getIntent().getStringExtra(CommonArgs.VIDEO_PATH),
                            coverPath, null).goPreview(this);
                }
                break;
        }
    }

    private void showNotSaveDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(WishAddInfoActivity.this);
        commonMiddleDialog.setMessage(R.string.wish_add_cancel);
        commonMiddleDialog.setSure(v -> {
            EventBus.onPostReceived(S.E.E_WISH_POST_BACK, null);
            commonMiddleDialog.dismiss();
            finish();
        });
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    private void showDeleteDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(WishAddInfoActivity.this);
        commonMiddleDialog.setMessage(R.string.wish_add_delete);
        commonMiddleDialog.setSure(v -> {
            commonMiddleDialog.dismiss();
            finish();
        });
        commonMiddleDialog.setCancel(R.string.give_up);
        commonMiddleDialog.show();
    }

    public void uploadCover(final Context context, final String filePath) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(this, R.string.wish_add_text_not_connect);
            return;
        }
        HttpHelper.card.getQiNiuToken(1, "wish")
                .subscribeOn(Schedulers.io())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(WishAddInfoActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0) {
                            File file = new File(filePath);
                            if (!file.exists()) {
                                ToastUtils.showShort(context, R.string.error_net);
                            }

                            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
//                                            ToastUtils.showLong(context, R.string.upload_filed);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            upload(WishAddInfoActivity.this, getIntent().getStringExtra(CommonArgs.VIDEO_PATH), HttpHelper.HOSTURL_RESCOURCE + url, 3);
                                        }
                                    });
                        }
                    }
                });
    }

    public void upload(final Context context, final String filePath, final String coverPath, int qiniuType) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(this, R.string.wish_add_text_not_connect);
            return;
        }

        HttpHelper.card.getQiNiuToken(qiniuType, "wish")
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
//                        ToastUtils.showShort(WishAddInfoActivity.this,getString(R.string.error_net));
                        Toast.makeText(WishAddInfoActivity.this, getString(R.string.error_net), Toast.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        saveBg.setVisibility(View.VISIBLE);
                        saveProgress.setVisibility(View.VISIBLE);
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0) {
                            File file = new File(filePath);
                            if (!file.exists()) {
                                ToastUtils.showShort(context, R.string.error_net);
                            }

                            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
                            LogUtils.e("aab getQiNiuTokenHttpBaseBean.getData().getFilePath()) = " + getQiNiuTokenHttpBaseBean.getData().getFilePath());
                            LogUtils.e("aab getQiNiuTokenHttpBaseBean.getData().getUpToken()) = " + getQiNiuTokenHttpBaseBean.getData().getUpToken());

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
//                                            ToastUtils.showLong(context, R.string.upload_filed);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            switch (type) {
                                                case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                                                    JSONArray jsonArray = new JSONArray();
                                                    jsonArray.put(HttpHelper.HOSTURL_RESCOURCE + url);
                                                    saveInfoServer(CommonArgs.COMMON_SEND_TYPE_IMAGE, jsonArray.toString(), null, null, null);
                                                    break;
                                                case CommonArgs.COMMON_SEND_TYPE_SOUND:
                                                    saveInfoServer(CommonArgs.COMMON_SEND_TYPE_SOUND, null, HttpHelper.HOSTURL_RESCOURCE + url, null, null);
                                                    break;
                                                case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                                                    saveInfoServer(CommonArgs.COMMON_SEND_TYPE_VIDEO, null, null, HttpHelper.HOSTURL_RESCOURCE + url, coverPath);
                                                    break;
                                            }
                                        }
                                    });
                        }
                    }
                });
    }

    private boolean postClicked = false;

    private void saveInfoServer(int type, String imgUrl, String audioUrl, String vedioUrl, String coverUrl) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(this, R.string.wish_add_text_not_connect);
            return;
        }
        if (postClicked) {
            return;
        }
        postClicked = true;
        String inputText = mTextEt.getText().toString().trim();
        HttpHelper.wishService.saveWish(type, imgUrl, audioUrl, vedioUrl, coverUrl, inputText)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<HttpBaseBean<WishInfoBean>>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                        postClicked = false;
                        ToastUtils.showShort(WishAddInfoActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<WishInfoBean> wishInfoHttpBaseBean) {
                        if (wishInfoHttpBaseBean.getErrorCode() == 0) {
                            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                            // 隐藏软键盘
                            try {
                                if (imm != null)
                                    imm.hideSoftInputFromWindow(getWindow().getDecorView().getWindowToken(), 0);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            WishInfoBean wishInfoBean = wishInfoHttpBaseBean.getData();
                            showSuccessAnim(wishInfoBean);
                        }
                    }
                });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        try {
            if (mPlayer != null) {
                mPlayer.release();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        try {
            if (mPlayer != null && mPlayer.isPlaying()) {
                mAddInfoControl.setVisibility(View.VISIBLE);
                mPlayer.pause();
                //((AnimationDrawable) audioGif.getDrawable()).stop();
            }
        } catch (IllegalStateException e) {

        } catch (Exception e) {

        }
    }

    private void showSuccessAnim(final WishInfoBean wishInfoBean) {
        saveProgress.setVisibility(View.GONE);
        mSaveLv.setVisibility(View.VISIBLE);
        mSaveLv.setImageAssetsFolder("lottie_wish_save/");
        mSaveLv.setAnimation("wish_save.json");
        mSaveLv.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                startActivity(new Intent(WishAddInfoActivity.this, WishDetailInfoActivity.class).putExtra(WishDetailInfoActivity.WISH_BEAN, wishInfoBean).putExtra(WishDetailInfoActivity.SHOW_SHARE, true));
                EventBus.onPostReceived(S.E.E_WISH_POST_SUCCESSED, null);
                finish();
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mSaveLv.playAnimation();
        BluetoothManage.getInstance().notifyJewelry(6, 0x0000ff);
    }
}
