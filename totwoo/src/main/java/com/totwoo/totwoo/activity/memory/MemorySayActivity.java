package com.totwoo.totwoo.activity.memory;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.StringUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.MemoryBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.MemoryController;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.LoadingDialog;
import com.totwoo.totwoo.widget.SceneAnimation;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/8.
 */

public class MemorySayActivity extends BaseActivity implements View.OnClickListener, SubscriberListener
{
    @BindView (R.id.memory_vedio_add_edit)
    EditText mEdit;

    @BindView (R.id.memory_vedio_add_edit_num)
    TextView num;

    @BindView (R.id.memory_vedio_add_save)
    TextView save;

    @BindView (R.id.memory_save_gif)
    ImageView saveGif;

    @BindView (R.id.memory_save_bg)
    View saveBg;

    private Dialog dialog;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_say);
        ButterKnife.bind(this);

        InjectUtils.injectOnlyEvent(this);

        initListener();
    }

    private void initListener()
    {
        mEdit.addTextChangedListener(new TextWatcher()
        {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after)
            {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count)
            {

            }

            @Override
            public void afterTextChanged(Editable s)
            {
                String content = mEdit.getText().toString();
                num.setText(content.length() + "/500");
            }
        });
        save.setOnClickListener(this);
    }

    protected void initTopBar()
    {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.memory_diary_title);
    }

    @Override
    public void onClick(View v)
    {
        switch (v.getId())
        {
            case R.id.memory_vedio_add_save:
                saveSay();
                break;
            default:
                break;
        }
    }

    private void saveSay()
    {
        String res = mEdit.getText().toString().trim();
        if (res.length() == 0)
        {
            ToastUtils.showShort(this, R.string.memory_diary_content);
            return;
        }

        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED)
        {
            ToastUtils.showShort(this, R.string.memory_connect_not);
            return;
        }

        dialog = new LoadingDialog(this, getString(R.string.memory_store_in));

        MemoryBean memoryBean = new MemoryBean();
        memoryBean.memory_type = MemoryBean.TYPE_SAY;
        memoryBean.content = mEdit.getText().toString();
        MemoryController.getInstance().save(memoryBean);
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_SUCCESSED, runThread = TaskType.UI)
    public void onSaveSuccessed(EventData data)
    {
        if (dialog != null)
            dialog.dismiss();

        HttpValues hv = (HttpValues) data;
        MemoryBean mb = (MemoryBean) hv.getUserDefine(S.M.M_IMAGES);
        showSuccessAnim(this, mb);
    }

    private void showSuccessAnim(final Activity activity, final MemoryBean mb)
    {
        saveBg.setVisibility(View.VISIBLE);
        saveGif.setVisibility(View.VISIBLE);
        /*AnimationDrawable ad = ((AnimationDrawable)saveGif.getDrawable());
        ad.setOneShot(true);
        ad.start();*/
        new SceneAnimation(saveGif, MemoryListActivity.successGif, 20, true);
        BluetoothManage.getInstance().notifyJewelry(6, 0x0000ff);
        mHandler.postDelayed(new Runnable()
        {
            @Override
            public void run()
            {
                Intent intent = new Intent(MemorySayActivity.this, MemoryPageActivity.class);
                intent.putExtra(MemoryPageActivity.IS_OPENED, true);
                MemorySayActivity.this.startActivity(intent);
                /*intent = new Intent(MemorySayActivity.this, MemorySayShowActivity.class);
                intent.putExtra(S.M.M_IMAGES, mb);
                MemorySayActivity.this.startActivity(intent);*/
                activity.finish();
            }
        }, 80*40);
    }

    @EventInject(eventType = S.E.E_MEMORY_SAVE_FAILED, runThread = TaskType.UI)
    public void onSaveFailed(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (StringUtils.isEmpty(hv.errorMesg))
            hv.errorMesg = getString(R.string.error_net);
        ToastUtils.showShort(this, hv.errorMesg);
        save.setClickable(true);
        if (dialog != null)
            dialog.dismiss();
    }

    @EventInject(eventType = S.E.E_MEMORY_RESOURCE_UPLOAD_FAILED, runThread = TaskType.UI)
    public void onUploadResourceFailed(EventData data)
    {
        ToastUtils.showShort(this, R.string.error_net);
        save.setClickable(true);
        if (dialog != null)
            dialog.dismiss();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        if (dialog != null)
            dialog.dismiss();
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        EventBus.unregisterListenerAll(this);
    }

    @Override
    protected void onPause()
    {
        super.onPause();
    }
}