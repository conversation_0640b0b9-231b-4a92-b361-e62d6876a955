package com.totwoo.totwoo.activity.wish;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CustomDialog;

import java.io.FileOutputStream;

import butterknife.ButterKnife;
import butterknife.OnClick;

public class WishCreatTypeActivity extends BaseActivity {
    private CustomDialog changeBgDialog;
    private Uri uri;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wish_creat);
        ButterKnife.bind(this);
    }

    @OnClick({R.id.wish_type_photo_tv, R.id.wish_type_audio_tv, R.id.wish_type_video_tv, R.id.wish_type_text_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.wish_type_photo_tv:
                getPhotoDialog(WishCreatTypeActivity.this);
                break;
            case R.id.wish_type_audio_tv:
                startActivity(new Intent(this, WishAddVoiceInfoActivity.class));
                break;
            case R.id.wish_type_video_tv:
                break;
            case R.id.wish_type_text_tv:
                startActivity(new Intent(this, WishAddTextInfoActivity.class));
                break;
        }
    }

    @Override
    protected void initTopBar() {
        setTopbarBackground(R.color.layer_bg_white);
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(getString(R.string.memory_list_title1));
    }

    public void getPhotoDialog(Context context) {
        changeBgDialog = new CustomDialog(context);
        changeBgDialog.setTitle(R.string.modify_background);
        LinearLayout modify_head_dialog_ll = new LinearLayout(context);
        modify_head_dialog_ll
                .setLayoutParams(new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT));
        modify_head_dialog_ll.setOrientation(LinearLayout.VERTICAL);
        TextView album_tv = new TextView(context);
        TextView camera_tv = new TextView(context);
        modify_head_dialog_ll.addView(album_tv);
        modify_head_dialog_ll.addView(camera_tv);

        changeBgDialog.setMainLayoutView(modify_head_dialog_ll);
        album_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        camera_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        album_tv.setPadding(Apputils.dp2px(context, 20), Apputils.dp2px(context, 15), Apputils.dp2px(context, 20), Apputils.dp2px(context, 15));
        camera_tv.setPadding(Apputils.dp2px(context, 20), Apputils.dp2px(context, 15), Apputils.dp2px(context, 20), Apputils.dp2px(context, 15));
        album_tv.setBackgroundResource(R.drawable.item_bg);
        camera_tv.setBackgroundResource(R.drawable.item_bg);
        album_tv.setText(R.string.album_select_usericon);
        camera_tv.setText(R.string.use_camera);
        album_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        camera_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        album_tv.setTextSize(16);
        camera_tv.setTextSize(16);
        changeBgDialog.setNegativeButtonText(R.string.cancel);
        // 相册tv监听点击开启选择图片app
        // 相册tv监听点击开启选择图片app
        album_tv.setOnClickListener(v -> {
            PictureSelectUtil.with(this).gallery().crop(8, 5).setCallback(uri -> {
                receivePhoto(uri);
                changeBgDialog.dismiss();
            }).select();
        });
        // 相机tv监听点击开启拍照app
        camera_tv.setOnClickListener(v -> {

            PictureSelectUtil.with(this).camera().crop(8, 5).setCallback(uri -> {
                receivePhoto(uri);
                changeBgDialog.dismiss();
            }).select();
        });
        changeBgDialog.show();
    }

    private void receivePhoto(Uri uri) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = false;
        options.inSampleSize = 2;

        try {
            Bitmap bitmap = BitmapFactory.decodeStream(getContentResolver().openInputStream(uri));
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, new FileOutputStream(CommonArgs.CACHE_WISH_IMAGE));
            startActivity(new Intent(this, WishAddInfoActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_IMAGE));
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showLong(this, R.string.data_error);
        }
    }
}
