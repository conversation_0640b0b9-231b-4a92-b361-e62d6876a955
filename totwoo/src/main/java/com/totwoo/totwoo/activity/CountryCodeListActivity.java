package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.BaseAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.StringUtils;

import java.text.Collator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 国家区号代码
 *
 * <AUTHOR>
 */
public class CountryCodeListActivity extends BaseActivity {

    /**
     * 国家代码列表
     */
    @BindView(R.id.country_code_lv)
    ListView country_code_lv;

    // 国家代码数据
    private HashMap<String, String> readCountryCode;

    // 用来排序的集合
    private List<String> sortCodeList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_country_code_list);
        ButterKnife.bind(this);
        initData();
    }

    private void initData() {
        readCountryCode = ConfigData.readCountryCode(this);
        Set<String> keySet = readCountryCode.keySet();
        sortCodeList = new ArrayList<>();
        // 排序
        for (String string : keySet) {
            switch (string) {
                case "中国":
                case "中国香港":
                case "中国澳门":
                case "中国台湾":
                case "Italy":
                    continue;
            }
            sortCodeList.add(string);
        }
        Collections.sort(sortCodeList, Collator.getInstance(Locale.CHINESE));

        for (int i = 0; i < sortCodeList.size(); i++) {
            // 判断是否跟上一个元素首字母相同 不同则加tag
            if (i == 0
                    || !StringUtils.getSortKey(sortCodeList.get(i)).equals(
                    StringUtils.getSortKey(sortCodeList.get(i - 1)))) {
                sortCodeList.add(i, StringUtils.getSortKey(sortCodeList.get(i)));
                i++;
            }
        }
        sortCodeList.add(0, "中国");
        sortCodeList.add(1, "中国香港");
        sortCodeList.add(2, "中国台湾");
        sortCodeList.add(3, "中国澳门");
        sortCodeList.add(4, "Italy");

        country_code_lv.setAdapter(new CountryCodeAdapter());
        country_code_lv.setOnItemClickListener(new OnItemClickListener() {

            @Override
            public void onItemClick(AdapterView<?> parent, View view,
                                    int position, long id) {
                String countryCodeValue = (String) parent.getAdapter().getItem(position);
                getIntent().putExtra(CommonArgs.COUNTRY_CODE_KEY,
                        countryCodeValue);
                PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.COUNTRY_CODE_KEY, countryCodeValue);

                setResult(0, getIntent());
                finish();
            }
        });

    }

    private class CountryCodeAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            return sortCodeList != null ? sortCodeList.size() : 0;
        }

        @Override
        public Object getItem(int position) {
            return readCountryCode.get(sortCodeList.get(position));
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public boolean isEnabled(int position) {
            if (Arrays.asList(ConfigData.Alphabets).contains(
                    sortCodeList.get(position))) {
                LogUtils.i("isEnabled", position + "");
                return false;
            }
            return super.isEnabled(position);
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            // 判断是否是tag
            String key = sortCodeList.get(position);
            if (Arrays.asList(ConfigData.Alphabets).contains(
                    key)) {
                convertView = View.inflate(CountryCodeListActivity.this,
                        R.layout.select_city_tag, null);
                TextView select_city_item_tag = (TextView) convertView
                        .findViewById(R.id.select_city_item_tag);
                select_city_item_tag.setText(key);
            } else {
                if (convertView == null
                        || convertView.getId() == R.id.select_city_tag) {
                    convertView = View.inflate(CountryCodeListActivity.this,
                            R.layout.country_code_layout, null);
                }
                TextView country_code_key_tv = (TextView) convertView
                        .findViewById(R.id.country_code_key_tv);
                country_code_key_tv.setText(key);
                TextView country_code_value_tv = (TextView) convertView
                        .findViewById(R.id.country_code_value_tv);
                country_code_value_tv.setText("+"
                        + readCountryCode.get(key));
            }

            return convertView;
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.country_code_title);
        super.initTopBar();
//        // 可以检查是否有效
//        //
//        PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
//
//        try {
//            // phone must begin with '+'
//
//            PhoneNumber numberProto = phoneUtil.parse("+33123456789", "");
//            phoneUtil.isValidNumber(numberProto);
//            int countryCode = numberProto.getCountryCode();
//        } catch (NumberParseException e) {
//            System.err.println("NumberParseException was thrown: "
//                    + e.toString());
//        }
    }
}
