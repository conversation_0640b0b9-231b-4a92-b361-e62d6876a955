package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.CalorieBean;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class CalorieActivity extends BaseActivity {
    @BindView(R.id.calorie_rv)
    RecyclerView mCalorieRv;

    List<CalorieBean> beans;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_calorie);
        ButterKnife.bind(this);
        mCalorieRv.setLayoutManager(new LinearLayoutManager(this));
        beans = new ArrayList<>();
        if(Apputils.systemLanguageIsChinese(CalorieActivity.this)){
            beans.add(new CalorieBean("鲜奶","250ml","163千卡"));
            beans.add(new CalorieBean("脱脂奶","250ml","88千卡"));
            beans.add(new CalorieBean("蛋","一只","75千卡"));
            beans.add(new CalorieBean("煎蛋","一只","105千卡"));
            beans.add(new CalorieBean("玉米","一根","107千卡"));
            beans.add(new CalorieBean("小笼包","小的5个","200千卡"));
            beans.add(new CalorieBean("肉包子","1个","250千卡"));
            beans.add(new CalorieBean("水饺","10个","420千卡"));
            beans.add(new CalorieBean("菜包","1个","200千卡"));
            beans.add(new CalorieBean("猪肉水饺","一个","40千卡"));
            beans.add(new CalorieBean("豆沙包","一个","215千卡"));
            beans.add(new CalorieBean("鲜肉包","一个","225-280千卡"));
            beans.add(new CalorieBean("叉烧包","一个","160千卡"));
        }else{
            beans.add(new CalorieBean("Apple","medium","72"));
            beans.add(new CalorieBean("Bagel","","289"));
            beans.add(new CalorieBean("Banana","medium","105"));
            beans.add(new CalorieBean("Beer","regular, 12 ounces","153"));
            beans.add(new CalorieBean("Bread","one slice, wheat or white","66"));
            beans.add(new CalorieBean("Butter","salted, 1 tablespoon","102"));
            beans.add(new CalorieBean("Carrots","raw, 1 cup","52"));
            beans.add(new CalorieBean("Cheddar cheese","1 slice","113"));
            beans.add(new CalorieBean("Chicken breast","boneless, skinless, roasted, 3 ounces","142"));
            beans.add(new CalorieBean("Chili with beans","canned, 1 cup","287"));
        }

        mCalorieRv.setAdapter(new CalorieItemAdapter());
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
    }

    public class CalorieItemAdapter extends RecyclerView.Adapter<CalorieItemAdapter.ViewHolder> {
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.calorie_item, parent, false);
            return new CalorieItemAdapter.ViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.mCalorieTitle.setText(beans.get(position).getTitle());
            holder.mCalorieUnit.setText(beans.get(position).getUnit());
            holder.mCalorieNumbers.setText(beans.get(position).getCalorie());
        }

        @Override
        public int getItemCount() {
            return beans == null ? 0 : beans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            @BindView(R.id.calorie_title)
            TextView mCalorieTitle;
            @BindView(R.id.calorie_unit)
            TextView mCalorieUnit;
            @BindView(R.id.calorie_numbers)
            TextView mCalorieNumbers;

            public ViewHolder(View itemView) {
                super(itemView);
                ButterKnife.bind(this,itemView);
            }
        }
    }
}
