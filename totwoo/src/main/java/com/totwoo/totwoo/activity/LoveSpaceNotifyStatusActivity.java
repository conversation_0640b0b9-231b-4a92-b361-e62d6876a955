package com.totwoo.totwoo.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.PeriodNotifyStatus;
import com.totwoo.totwoo.bean.SleepNotifyStatus;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;

public class LoveSpaceNotifyStatusActivity extends BaseActivity {
    @BindView(R.id.love_notify_status_cb)
    CheckBox mStatusCb;
    @BindView(R.id.love_notify_status_tv)
    TextView mStatusTv;
    @BindView(R.id.love_notify_status_info)
    TextView mStatusInfo;
    private static final String SLEEP_NOTIFY_STATUS = "sleep_notify_status";
    private static final String PERIOD_NOTIFY_STATUS = "period_notify_status";
    public static final String SLEEP_TYPE = "sleep_type";
    public static final String PERIOD_TYPE = "period_type";

    private boolean notifyStatus;
    private String from_type;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sleep_status);
        ButterKnife.bind(this);
        from_type = getIntent().getStringExtra(CommonArgs.FROM_TYPE);
        if (TextUtils.equals(from_type, PERIOD_TYPE)) {
            notifyStatus = PreferencesUtils.getBoolean(this, PERIOD_NOTIFY_STATUS, true);
            mStatusInfo.setText(R.string.period_reminder_hint);
        } else {
            notifyStatus = PreferencesUtils.getBoolean(this, SLEEP_NOTIFY_STATUS, true);
            mStatusInfo.setText(R.string.sleep_reminder_hint);
        }
        onStatusChanged();
        if (TextUtils.equals(from_type, PERIOD_TYPE)) {
            getPeriodStatus();
        } else {
            getSleepStatus();
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
        if (TextUtils.equals(from_type, PERIOD_TYPE)) {
            setTopTitle(R.string.period_reminder);
        } else {
            setTopTitle(R.string.sleep_reminder);
        }
    }

    private void getSleepStatus() {
        HttpHelper.sleepDataService.getSleepStatus()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SleepNotifyStatus>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<SleepNotifyStatus> sleepNotifyStatusHttpBaseBean) {
                        if (sleepNotifyStatusHttpBaseBean.getErrorCode() == 0) {
                            notifyStatus = sleepNotifyStatusHttpBaseBean.getData().getSleep_remind() == 1;
                            PreferencesUtils.put(LoveSpaceNotifyStatusActivity.this, SLEEP_NOTIFY_STATUS, notifyStatus);
                            onStatusChanged();
                        }
                    }
                });
    }

    private void getPeriodStatus() {
        HttpHelper.periodSave.getFriendMenstPush(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<PeriodNotifyStatus>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodNotifyStatus> periodNotifyStatusHttpBaseBean) {
                        if (periodNotifyStatusHttpBaseBean.getErrorCode() == 0) {
                            notifyStatus = periodNotifyStatusHttpBaseBean.getData().getIs_push_menst() == 1;
                            PreferencesUtils.put(LoveSpaceNotifyStatusActivity.this, PERIOD_NOTIFY_STATUS, notifyStatus);
                            onStatusChanged();
                        }
                    }
                });
    }

    private void updateSleepStatus() {
        HttpHelper.sleepDataService.updateSleepStatus(notifyStatus ? 1 : 0)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            PreferencesUtils.put(LoveSpaceNotifyStatusActivity.this, SLEEP_NOTIFY_STATUS, notifyStatus);
                        }
                    }
                });
    }

    private void updatePeriodStatus() {
        HttpHelper.periodSave.setFriendMenstPush(notifyStatus ? 1 : 0)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<PeriodNotifyStatus>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodNotifyStatus> periodNotifyStatusHttpBaseBean) {
                        if (periodNotifyStatusHttpBaseBean.getErrorCode() == 0) {
                            PreferencesUtils.put(LoveSpaceNotifyStatusActivity.this, PERIOD_NOTIFY_STATUS, notifyStatus);
                        }
                    }
                });
    }

    private void onStatusChanged() {
        if (notifyStatus) {
            mStatusTv.setText(R.string.notify_on);
        } else {
            mStatusTv.setText(R.string.notify_off);
        }
        mStatusCb.setChecked(notifyStatus);
    }

    @OnClick(R.id.love_notify_status_cb)
    protected void onClick(View view) {
        if (view.getId() == R.id.love_notify_status_cb) {
            notifyStatus = !notifyStatus;
            if (TextUtils.equals(from_type, PERIOD_TYPE)) {
                updatePeriodStatus();
                if(!notifyStatus){
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_HERMENCLOSE);
                }
            } else {
                updateSleepStatus();
                if(!notifyStatus){
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVEZONE_CLICK_SHESLEEPCLOSE);
                }
            }
            onStatusChanged();
        }
    }
}
