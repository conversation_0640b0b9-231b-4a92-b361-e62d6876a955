package com.totwoo.totwoo.activity.memory;

import android.app.Activity;
import android.app.Instrumentation;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.ShakeMonitor;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.PageWidget;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by xinyoulingxi on 2017/8/15.
 */

public class MemoryEnterActivity extends BaseActivity implements SubscriberListener, View.OnClickListener {
    @BindView(R.id.memory_enter_close)
    public TextView close;

    @BindView(R.id.memory_enter_logo)
    public ImageView logo;

    @BindView(R.id.memory_enter_tap)
    public ImageView tap;

    @BindView(R.id.memory_enter_diary)
    public ImageView diary;

    @BindView(R.id.page_view)
    public PageWidget pageWidget;

    private ShakeMonitor mShakeMonitor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_enter);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        initViews();
        initShakeListener();
        initAnims();

        BluetoothManage.getInstance().connectedStatus();

        close.setOnClickListener(this);
    }

    private void initViews() {
        if (Apputils.systemLanguageIsChinese(this)) {
            logo.setImageResource(R.drawable.memory_enter_logo_ch);
            tap.setImageResource(R.drawable.memory_enter_tap_ch);
            diary.setImageResource(R.drawable.memory_enter_diary_ch);
        } else {
            logo.setImageResource(R.drawable.memory_enter_logo_en);
            tap.setImageResource(R.drawable.memory_enter_tap_en);
            diary.setImageResource(R.drawable.memory_enter_diary_en);
        }
    }

    private void initAnims() {
        final Animation a1 = AnimationUtils.loadAnimation(this, R.anim.alaph_to20);
        final Animation a2 = AnimationUtils.loadAnimation(this, R.anim.alaph_to100);
        a1.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                tap.startAnimation(a2);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        a2.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                tap.startAnimation(a1);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        tap.startAnimation(a1);
    }

    private void initShakeListener() {
        mShakeMonitor = new ShakeMonitor(this);
        // 设置摇首饰的监听
        mShakeMonitor.setOnEventListener(type -> {
            if (!NetUtils.isConnected(MemoryEnterActivity.this)) {
                ToastUtils.showShort(MemoryEnterActivity.this, R.string.error_net);
                return;
            }
            //弹出信纸
            final int width = Apputils.getScreenWidth(MemoryEnterActivity.this);
            final int height = Apputils.getScreenHeight(MemoryEnterActivity.this);
            //pageWidget = new PageWidget(MemoryEnterActivity.this, width, height);
            final Bitmap bitmap = takeScreenShot(MemoryEnterActivity.this);
            pageWidget.setVisibility(View.VISIBLE);
            pageWidget.setScreen(width, height);

            final Bitmap mCurPageBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            final Bitmap mNextPageBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            final Canvas mCurPageCanvas = new Canvas(mCurPageBitmap);
            final Canvas mNextPageCanvas = new Canvas(mNextPageBitmap);
            final Rect c = new Rect(0, 0, width, height);
            mCurPageCanvas.drawBitmap(bitmap, c, c, null);
            pageWidget.setBitmaps(mCurPageBitmap, mCurPageBitmap);
            pageWidget.setOnTouchListener((v, e) -> {
                boolean ret = false;
                if (v == pageWidget) {
                    if (e.getAction() == MotionEvent.ACTION_DOWN) {
                        pageWidget.abortAnimation();
                        pageWidget.calcCornerXY(e.getX(), e.getY());

                        mCurPageCanvas.drawBitmap(bitmap, c, c, null);
                        if (pageWidget.DragToRight()) {
                            mNextPageCanvas.drawBitmap(bitmap, c, c, null);
                        } else {
                            mNextPageCanvas.drawBitmap(bitmap, c, c, null);
                        }
                        pageWidget.setBitmaps(mCurPageBitmap, mNextPageBitmap);
                    }

                    ret = pageWidget.doTouchEvent(e);
                    return ret;
                }
                return false;
            });

            mHandler.postDelayed(() -> new Thread(new Runnable() {
                @Override
                public void run() {
                    Instrumentation inst = new Instrumentation();
                    long dowTime = SystemClock.uptimeMillis();
                    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime, MotionEvent.ACTION_DOWN, width - 300, height - 400, 0));
                    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime, MotionEvent.ACTION_MOVE, width - 310, height - 410, 0));
                    for (int i = 0; i < 5; i++)
                        inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + i * 10, MotionEvent.ACTION_MOVE, width - 310, height - 410, 0));
                        /*for (int i=0; i<20; i++)
                        {
                            inst.sendPointerSync(MotionEvent.obtain(dowTime,dowTime, MotionEvent.ACTION_MOVE, width-100-i*10, height-50-i*20,0));
                            dowTime += 5;
                        }
                        for (int i=0; i<10; i++)
                        {
                            inst.sendPointerSync(MotionEvent.obtain(dowTime,dowTime, MotionEvent.ACTION_MOVE, width-300-i*10, height-450-i*20,0));
                            dowTime += 10;
                        }*/
                    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + 40, MotionEvent.ACTION_UP, width - 310, height - 410, 0));
                }
            }).start(), 100);
        });
    }

    private static Bitmap takeViewShot(Activity activity, View view) {
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();
        Rect rect = new Rect();
        activity.getWindow().getDecorView().getWindowVisibleDisplayFrame(rect);
        int width = activity.getWindowManager().getDefaultDisplay().getWidth();
        int height = activity.getWindowManager().getDefaultDisplay().getHeight();
        int statusBarHeight = rect.top;

        Bitmap bitmap2 = Bitmap.createBitmap(bitmap, 0, 0, width, height);
        view.destroyDrawingCache();
        return bitmap2;
    }

    private static Bitmap takeScreenShot2(Activity activity) {
        View view = activity.getWindow().getDecorView();
        return takeViewShot(activity, view);
    }

    private static Bitmap takeScreenShot(Activity activity) {
        View view = activity.getWindow().getDecorView();
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();
        Rect rect = new Rect();
        activity.getWindow().getDecorView().getWindowVisibleDisplayFrame(rect);
        //int statusBarHeight = rect.top;
        //System.out.println(statusBarHeight);

        int width = activity.getWindowManager().getDefaultDisplay().getWidth();
        int height = activity.getWindowManager().getDefaultDisplay().getHeight();

        Bitmap bitmap2 = Bitmap.createBitmap(bitmap, 0, 0/*statusBarHeight*/, width, height /*- statusBarHeight*/);
        view.destroyDrawingCache();
        return bitmap2;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mShakeMonitor != null)
            mShakeMonitor.start();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mShakeMonitor != null)
            mShakeMonitor.stop();
    }

    @Override
    public void onClick(View v) {
        this.finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
