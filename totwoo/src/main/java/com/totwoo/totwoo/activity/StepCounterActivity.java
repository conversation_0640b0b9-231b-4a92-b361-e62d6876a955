package com.totwoo.totwoo.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.database.Cursor;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.db.sqlite.Selector;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.Step;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.newConrtoller.StepController;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.DbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.StepUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.ExpandableTextView;
import com.totwoo.totwoo.widget.StepDataTable;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.TimeZone;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 健步瘦身界面， 界面内部所有数据基于计步功能
 *
 * <AUTHOR>
 * @date 2015-2015年7月10日
 */
public class StepCounterActivity extends BaseActivity implements OnClickListener, SubscriberListener {
    private static final int STEPTARGETSETTING = 1;

    protected static final int SCROLL = 0;

    public final static int REQUESTCODE_STEP = 3;

    /**
     * 整体布局的scrollview
     */
    @BindView(R.id.step_counte_sv)
    ScrollView step_counte_sv;

    /**
     * 自定义的步数展示表格视图
     */
    @BindView(R.id.step_data_table)
    StepDataTable tableView;

    /**
     * 日期title
     */
    @BindView(R.id.step_title_tv)
    TextView dateTitleView;

    /**
     * 修改健步目标TextView
     */
    @BindView(R.id.step_step_target_modify_btn)
    TextView modifyTargetTv;

    @BindView(R.id.step_step_set_btn)
    TextView stepSetTv;

    /**
     * 行走里程TextView
     */
    @BindView(R.id.step_walk_distance_value_tv)
    TextView walkDisTv;

    /**
     * 对应步数TextView
     */
    @BindView(R.id.step_walk_step_value_tv)
    TextView stepTv;

    /**
     * 卡路里 TextView
     */
    @BindView(R.id.step_calorie_value_tv)
    TextView calorieTv;

    /**
     * 行走里程Tkey
     */
    @BindView(R.id.step_walk_distance_key_tv)
    TextView step_walk_distance_key_tv;

    /**
     * 对应步数key
     */
    @BindView(R.id.step_walk_step_key_tv)
    TextView step_walk_step_key_tv;

    /**
     * 卡路里 key
     */
    @BindView(R.id.step_calorie_key_tv)
    TextView step_calorie_key_tv;

    /**
     * 健步目标完成度人偶
     */
    @BindView(R.id.step_doll_iv)
    ImageView step_doll_iv;

    /**
     * 健步目标完成度对应标题
     */
    @BindView(R.id.step_situation_tv)
    TextView step_situation_tv;

    /**
     * 健步目标完成度对应文字信息
     */
    @BindView(R.id.step_situation_info_tv)
    TextView step_situation_info_tv;

    /**
     * 单位换算信息动画上面按钮
     */
    @BindView(R.id.unit_conversion_gv_top_iv)
    ImageView unit_conversion_gv_top_iv;

    /**
     * 单位换算信息tv
     */
    @BindView(R.id.unit_conversion_info_tv)
    ExpandableTextView unit_conversion_info_tv;

    /**
     * 单位换算信息动画下面按钮
     */
    @BindView(R.id.unit_conversion_gv_down_iv)
    ImageView unit_conversion_gv_down_iv;

    /**
     * 热量单位换算信息动画上面按钮
     */
    @BindView(R.id.food_calries_conversion_gv_top_iv)
    ImageView food_calries_conversion_gv_top_iv;

    /**
     * 热量单位换算信息tv
     */
    @BindView(R.id.food_calries_conversion_info_tv)
    ExpandableTextView food_calries_conversion_info_tv;

    /**
     * 热量单位换算信息动画下面按钮
     */
    @BindView(R.id.food_calries_conversion_gv_down_iv)
    ImageView food_calries_conversion_gv_down_iv;

    /**
     * 最近一周的步数数据
     */
    private List<Integer> stepWeekData;

    /**
     * 需要同步数据的时间戳
     */
    private List<Long> needSynDate;

    /**
     * 最近一周步数的最大值
     */
    private int maxStep;

    /**
     * 今天健步数值
     */
    private int todayStep;

    /**
     * 目标健步数值
     */
    private int stepTarget;

//	private BroadcastReceiver receiverStep;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_step_counter);
        ButterKnife.bind(this);

        tableView.setOnSelectChangeListener(day_of_week -> showDateTitle(day_of_week));

//        setTextData();

        initData();
        setListener();

        EventBus.getDefault().register(this);
        InjectUtils.injectOnlyEvent(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        BluetoothManage.getInstance().connectedStatus();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    /**
     * 注册监听健步数据变化广播
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceive(Step step) {
        if (stepWeekData == null)
            stepWeekData = new ArrayList<>();
        if (stepWeekData.size() == 0)
            stepWeekData.add(0);
        stepWeekData.set(stepWeekData.size() - 1, step.getSteps());
        todayStep = step.getSteps();
        setTodayStepSituation();
        tableView.setStepData(stepWeekData);
        int selectDay = tableView.getSelectDay();
        if (selectDay == 6) {
            showDateTitle(selectDay);
        }
    }

    private void setListener() {
        unit_conversion_info_tv.setOnClickListener(this);
        modifyTargetTv.setOnClickListener(this);
        stepSetTv.setOnClickListener(this);
        unit_conversion_gv_top_iv.setOnClickListener(this);
        food_calries_conversion_gv_top_iv.setOnClickListener(this);
        // 单位换算文字展开监听
        unit_conversion_info_tv.setOnExpandListener(parent -> {
            // 文字展开后把屏幕跟文字底部对齐
            int[] position = new int[2];
            unit_conversion_info_tv.getLocationOnScreen(position);
//            System.out.println("getLocationOnScreen:" + position[0] + ","
//                    + position[1]);
            final int value = position[1] + step_counte_sv.getScrollY()
                    + unit_conversion_info_tv.getHeight()
                    - Apputils.getScreenHeight(StepCounterActivity.this)
                    + 50;
            new Thread() {
                public void run() {
                    for (int i = step_counte_sv.getScrollY(); i < value; i += 10) {
                        SystemClock.sleep(5);
                        Message message = Message.obtain();
                        message.what = SCROLL;
                        message.arg1 = i;
                        mHandler.sendMessage(message);
                    }
                }

            }.start();

        });

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.step_step_target_modify_btn: // 健步设置目标跳转
                if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_STEP_TRACK_CLICK);
                } else {
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_STEP_TRACK_CLICK);
                }
                startActivityForResult(new Intent(StepCounterActivity.this,
                        StepTargetSettingActivity.class), STEPTARGETSETTING);
                break;
            case R.id.step_step_set_btn:
                startActivity(new Intent(StepCounterActivity.this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_STEP));
                break;
            case R.id.unit_conversion_info_tv:

            case R.id.unit_conversion_gv_top_iv:// 单位换算
                if (!unit_conversion_info_tv.isExpanded()) {
                    unit_conversion_gv_top_iv
                            .setImageResource(R.drawable.cloes_minus_sign);
                    unit_conversion_gv_down_iv
                            .setImageResource(R.drawable.cloes_arrow);
                } else {
                    unit_conversion_gv_top_iv
                            .setImageResource(R.drawable.open_plus);
                    unit_conversion_gv_down_iv
                            .setImageResource(R.drawable.open_arrow);
                }
                unit_conversion_info_tv.toggle();
                startActivity(new Intent(StepCounterActivity.this, CalorieActivity.class));
                break;
            case R.id.food_calries_conversion_tv:

            case R.id.food_calries_conversion_gv_top_iv:// 食物热量换算
                if (!food_calries_conversion_info_tv.isExpanded()) {
                    food_calries_conversion_gv_down_iv
                            .setImageResource(R.drawable.cloes_arrow);
                    food_calries_conversion_gv_top_iv
                            .setImageResource(R.drawable.cloes_minus_sign);
                } else {
                    food_calries_conversion_gv_top_iv
                            .setImageResource(R.drawable.open_plus);
                    food_calries_conversion_gv_down_iv
                            .setImageResource(R.drawable.open_arrow);
                }
                food_calries_conversion_info_tv.toggle();
                break;

        }
    }

    private void initData() {

        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case SCROLL:
                        step_counte_sv.setScrollY(msg.arg1);
                        break;
                }
            }
        };

        // 后台加载最近七天健步数据
        //getWeekStepData(true);
        StepController.getInstance().getStepList();

        stepTarget = ToTwooApplication.owner.getWalkTarget();
        // 设置当前日期
        Calendar cal = Calendar.getInstance();
        dateTitleView.setText((cal.get(Calendar.MONTH) + 1) + "/"
                + cal.get(Calendar.DAY_OF_MONTH));

        if (!Apputils.systemLanguageIsChinese(this)) {
            step_walk_distance_key_tv.setVisibility(View.INVISIBLE);
            step_calorie_key_tv.setVisibility(View.INVISIBLE);
            step_walk_step_key_tv.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        setTodayStepSituation();
        if (tableView != null) {
            tableView.setTargetStep(ToTwooApplication.owner.getWalkTarget());
        }
    }

    /**
     * 后台加载最近七天健步数据, 加载完成自动通知更新界面<br>
     * 首先获取本地数据, 如果一周中最早的一天没有数据, 说明可能本地数据不足, 尝试通过服务器获取相应数据, 补充本地数据库
     *
     * @param needNet 是否需要通过网络加载数据
     */
    private void getWeekStepData(final boolean needNet) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                if (stepWeekData == null) {
                    stepWeekData = new ArrayList<Integer>();
                } else {
                    stepWeekData.clear();
                }

                // 数据需要 顺序存储, 直接定位到一周前的一天
                Calendar cal = Apputils.getZeroCalendar(null);
                cal.add(Calendar.DAY_OF_MONTH, -5);
                Step step = null;
                try {
                    List<Step> l = DbHelper.getDbUtils().findAll(Selector.from(Step.class));
                    for (int i = 0; i < l.size(); i++) {
                        Step ss = l.get(i);
                        LogUtils.i("walkdataxxx", "step:" + ss.getSteps() + ", time:" + (ss.getDateTime()));
                    }

//                    Step s = DbHelper.getDbUtils().findById(Step.class, 1495382400000l);
//                    LogUtils.i("walkdatadddd", "step:" + s.getSteps() + ", time:" + (s.getDateTime()));
                    Cursor c = DbHelper.getDbUtils().execQuery("SELECT * FROM step");
                    while (c.moveToNext()) {
                        long time = c.getLong(0);
                        int sss = c.getInt(1);
                        LogUtils.e("stepabc:" + time + ", " + sss);
                    }
                    LogUtils.e("step:count." + c.getCount());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                for (int i = 0; i < 7; i++) {
                    try {
                        long ll = cal.getTimeInMillis() - TimeZone.getDefault().getRawOffset();
                        long ll1 = ll + 30000000;
                        long ll2 = ll - 30000000;
                        //long ll2 = 0;
                        step = DbHelper.getDbUtils().findById(Step.class, ll);


                        //List<Step> l = DbHelper.getDbUtils().findAll(Selector.from(Step.class).where("date_time", "between", new long[]{ll1, ll2}));
                        //List<Step> l = DbHelper.getDbUtils().findAll(Selector.from(Step.class).where("date_time", "<", ll1).and("date_time", ">", ll2));
                        /*if (l == null || l.size() == 0)
                            LogUtils.e("stepbbb, ll=null||ll==0");
                        else
                            LogUtils.e("stepbbb:" + l.size());
                        if (l == null || l.size() == 0)
                            step = null;
                        else
                            step = l.get(0);
                        LogUtils.i("stepaaa", ll1 + ", " + ll + ", " + ll2);*/
                        if (step == null)
                            LogUtils.e("step is null");
                        else
                            LogUtils.e("step:" + step.getSteps());
                    } catch (DbException e) {
                        step = null;
                        e.printStackTrace();
                    }
                    if (step != null) {
                        stepWeekData.add(step.getSteps());
                        if (step.getSteps() > maxStep) {
                            maxStep = step.getSteps();
                        }
                    } else {
                        stepWeekData.add(0);
                    }
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }

                for (int i = 0; i < stepWeekData.size(); i++) {
                    LogUtils.i("step111", "step:" + stepWeekData.get(i));
                }
                // needSynDate = getNeedSynDate();
                // 通过服务器加载不足的数据

                // if (needNet && needSynDate.size() > 0) {
                // RequestParams params = HttpHelper.getBaseParams(true);
                // HttpHelper.getHttpUtils().send(HttpMethod.GET,
                // HttpHelper.URL_WALK_DATA, params,
                // new RequestCallBack<String>() {
                // @Override
                // public void onSuccess(
                // ResponseInfo<String> responseInfo) {
                //
                // JSONObject data = HttpHelper
                // .parserStringResponse(responseInfo.result);
                //
                // if (data != null) {
                // JSONArray array = data
                // .optJSONArray("walkdata");
                // if (array != null) {
                // synLocalData(array);
                // }
                // }
                // }
                //
                // @Override
                // public void onFailure(HttpException error,
                // String msg) {
                // getWeekStepData(false);
                // }
                // });
                // }

                // 通知界面, 更新数据
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        // 设置数据表格相关数据, 最大值, 目标值, 具体数据
                        if (maxStep > tableView.maxStep) {
                            tableView.setMaxStep(maxStep);
                        }
                        tableView.setTargetStep(ToTwooApplication.owner
                                .getWalkTarget());
                        tableView.setStepData(stepWeekData);
                        todayStep = stepWeekData.get(stepWeekData.size() - 1);

                        tableView.startInitAm();
                        setTodayStepSituation();
                    }

                });
            }
        }).start();

    }

    @EventInject(eventType = S.E.E_STEP_GET_LIST_SUCCESSED, runThread = TaskType.Async)
    public void onGetStepListSuccessed(EventData data) {
        HttpValues hv = (HttpValues) data;
        Step step = null;
        ArrayList<Step> list = (ArrayList<Step>) hv.getUserDefine("stepList");
        if (stepWeekData == null)
            stepWeekData = new ArrayList<Integer>();
        else
            stepWeekData.clear();

        for (int i = 0; i < list.size(); i++) {
            step = list.get(i);
            stepWeekData.add(step.getSteps());
            if (step.getSteps() > maxStep)
                maxStep = step.getSteps();
        }

        if (stepWeekData.size() == 0)
            for (int i = 0; i < 6; i++)
                stepWeekData.add(0);

        stepWeekData.add(0);    //最后加一条今天的数据为0

        // 通知界面, 更新数据
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                // 设置数据表格相关数据, 最大值, 目标值, 具体数据
                if (maxStep > tableView.maxStep) {
                    tableView.setMaxStep(maxStep);
                }
                tableView.setTargetStep(ToTwooApplication.owner.getWalkTarget());
                tableView.setStepData(stepWeekData);
                todayStep = stepWeekData.get(stepWeekData.size() - 1);

                tableView.startInitAm();
                setTodayStepSituation();
            }
        });
    }

    @EventInject(eventType = S.E.E_STEP_GET_LIST_FAILED, runThread = TaskType.UI)
    public void onGetStepListFailed(EventData data) {
        ToastUtils.show(this, R.string.error_net, Toast.LENGTH_SHORT);
    }

    /**
     * 后台加载最近七天健步数据, 加载完成自动通知更新界面<br>
     * 首先获取本地数据, 如果一周中最早的一天没有数据, 说明可能本地数据不足, 尝试通过服务器获取相应数据, 补充本地数据库
     */
    private void getWeekStepData() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                stepWeekData = new ArrayList<Integer>();
                // 数据需要 顺序存储, 直接定位到一周前的一天
                Calendar cal = Apputils.getZeroCalendar(null);
                cal.add(Calendar.DAY_OF_MONTH, -6);
                Step step = null;
                for (int i = 0; i < 7; i++) {
                    try {
                        step = DbHelper.getDbUtils().findById(Step.class,
                                cal.getTimeInMillis());
                        LogUtils.i("step", cal.getTimeInMillis() + "");
                    } catch (DbException e) {
                        step = null;
                        e.printStackTrace();
                    }
                    if (step != null) {
                        stepWeekData.add(step.getSteps());
                    } else {
                        stepWeekData.add(0);
                    }
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }
                needSynDate = getNeedSynDate();
                // 通过服务器加载不足的数据

                if (needSynDate.size() > 0) {
                    RequestParams params = HttpHelper.getBaseParams(true);
                    HttpRequest.get(
                            HttpHelper.URL_WALK_DATA, params,
                            new RequestCallBack<String>() {
                                @Override
                                public void onLogicSuccess(String s) {
                                    super.onLogicSuccess(s);

                                    JSONObject data = HttpHelper
                                            .parserStringResponse(s);

                                    if (data != null) {
                                        JSONArray array = data
                                                .optJSONArray("walkdata");
                                        if (array != null) {
                                            synLocalData(array);
                                        }
                                    }
                                }
                            });
                }
            }
        }).start();

    }

    // 为0的健步数据都需要去服务器同步
    private List<Long> getNeedSynDate() {
        List<Long> integers = new ArrayList<Long>();
        Calendar cal = Apputils.getZeroCalendar(null);
        cal.add(Calendar.DAY_OF_MONTH, -6);
        for (int i = 0; i < stepWeekData.size(); i++) {
            if (stepWeekData.get(i) == 0) {
                integers.add(cal.getTimeInMillis());
            }
            cal.add(Calendar.DAY_OF_MONTH, 1);
        }
        return integers;
    }

    /**
     * 将服务器的数据与本地数据结合, 然后重新获取数据<br>
     * 以本地数据为主, 对于无本地数据的情况再去服务器数据
     *
     * @param array
     */
    private void synLocalData(JSONArray array) {
        Step step = null;
        for (int i = 0; i < array.length(); i++) {
            JSONObject data = array.optJSONObject(i);
            if (data.optInt("ts") != 0 && data.optInt("step") != 0) {
                try {
                    step = DbHelper.getDbUtils().findById(Step.class,
                            data.optInt("ts") == 0);
                    if (step == null || step.getSteps() == 0) {
                        step = new Step();
                        long optLong = data.optLong("ts") * 1000;
                        LogUtils.i("walkdata", DateUtil.getDateToString(
                                "yyyy-MM-dd HH:mm:ss", optLong));
                        // 必须先转换成服务器规则的UTC时间搓然后再转换成本地的
                        optLong = DateUtil
                                .getStringToDate("yyyy-MM-dd HH:mm:ss",
                                        DateUtil.getUTCDateToString(
                                                "yyyy-MM-dd HH:mm:ss", optLong));
                        LogUtils.i(
                                "walkdata",
                                optLong
                                        + DateUtil.getDateToString(
                                        "yyyy-MM-dd HH:mm:ss", optLong));
                        step.setDateTime(optLong);
                        step.setSteps(data.optInt("step"));
                        if (needSynDate.contains(step.getDateTime())) {
                            DbHelper.getDbUtils().saveOrUpdate(step);
                            LogUtils.i("step", step.getDateTime() + "");
                        }
                    }
                } catch (DbException e) {
                    e.printStackTrace();
                }
            }
            step = null;
        }
    }


    private void setTextData() {
        // 数据需要 顺序存储, 直接定位到一周前的一天
        Calendar cal = Apputils.getZeroCalendar(null);
        cal.add(Calendar.DAY_OF_MONTH, -6);

        ArrayList<Step> list = new ArrayList<Step>();
        for (int i = 0; i < 7; i++) {
            Step bean = new Step();
            bean.setDateTime(cal.getTimeInMillis());
            bean.setSteps((int) (Math.random() * 25000 + 5000));
            list.add(bean);
            cal.add(Calendar.DAY_OF_MONTH, 1);
        }
        try {
            DbHelper.getDbUtils().saveOrUpdateAll(list);

        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);

        setTopTitle(R.string.step_counter);

//        if (Apputils.systemLanguageIsChinese(StepCounterActivity.this)) {
//            setTopRightIcon(R.drawable.icon_share_gift);
//        } else {
//            setTopRightIcon(R.drawable.share_ico_2);
//        }
//        setTopRightOnClick(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LIVE_STEP_SHARE);
//                if (getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1) == 1) {
//                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_SHARE_STEP_CLICK);
//                } else {
//                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SHARE_STEP_CLICK);
//                }
//                if (!PermissionUtil.hasStoragePermission(StepCounterActivity.this)) {
//                    return;
//                }
//                Intent intent = new Intent(StepCounterActivity.this, ShareStepActivity.class).putExtra(CommonArgs.FROM_TYPE, getIntent().getIntExtra(CommonArgs.FROM_TYPE, 1));
//
//                int step = 0;
//                if (stepWeekData == null || stepWeekData.size() == 0)
//                    step = 0;
//                else
//                    step = stepWeekData.get(stepWeekData.size() - 1);
//
//                intent.putExtra("step", step);
//                startActivity(intent);
//
//            }
//        });
//
////        if (!BleParams.isSecurityJewlery()) {
////            setTopRigh2tIcon(R.drawable.step_target);
////        }
//        getTopRight2Icon().setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                startActivityForResult(new Intent(StepCounterActivity.this, NotifySettingActivity.class).putExtra(NotifySettingActivity.NOTIFY_TYPE_TAG, NotifySettingActivity.TYPE_STEP), REQUESTCODE_STEP);
//            }
//        });
    }

    /**
     * 根据选择的情况, 显示对应的数据
     *
     * @param day_of_week
     */
    protected void showDateTitle(int day_of_week) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -(6 - day_of_week));
        if (6 == day_of_week) {
            dateTitleView.setText("Today");
        } else {
            dateTitleView.setText((cal.get(Calendar.MONTH) + 1) + "/"
                    + cal.get(Calendar.DAY_OF_MONTH));
        }
        if (stepWeekData != null && stepWeekData.size() >= 7) {
            int step = stepWeekData.get(day_of_week);

            SpannableString stepss = new SpannableString(step
                    + getString(R.string.step));
            stepss.setSpan(new AbsoluteSizeSpan(16, true), 0, stepss.length()
                            - getString(R.string.step).length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            stepTv.setText(stepss);
            // 公里数保留一位小数
            DecimalFormat format = new DecimalFormat("#.#");
            SpannableString walk = new SpannableString(
                    format.format(StepUtils.getWalkDistance(step, true))
                            + getString(R.string.km));
            walk.setSpan(new AbsoluteSizeSpan(16, true), 0, walk.length()
                            - getString(R.string.km).length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

            walkDisTv.setText(walk);

            // 四舍五入取整
            SpannableString calorie = new SpannableString(StepUtils.getCaliore(step, true)
                    + getString(R.string.kcal));
            calorie.setSpan(new AbsoluteSizeSpan(16, true), 0, calorie.length()
                            - getString(R.string.kcal).length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            calorieTv.setText(calorie);
        }
    }


    @SuppressLint({"StringFormatInvalid", "StringFormatMatches"})
    private void setTodayStepSituation() {
        if (todayStep >= stepTarget) {// 完成目标
            if (ToTwooApplication.owner.getGender() == 0) {
                step_doll_iv.setImageResource(R.drawable.step_boy_perfect);
            } else {
                step_doll_iv.setImageResource(R.drawable.step_gril_perfect);
            }
            step_situation_tv.setText("Great!");

            step_situation_info_tv.setText(String
                    .format(getString(R.string.step_great_info), todayStep
                            - stepTarget));
        } else if (todayStep > 0) {// 完成一部分
            if (ToTwooApplication.owner.getGender() == 0) {
                step_doll_iv.setImageResource(R.drawable.step_boy_great);
            } else {
                step_doll_iv.setImageResource(R.drawable.step_gril_great);
            }
            step_situation_tv.setText("Fighting!");
            if (BleParams.isSecurityJewlery()) {
                step_situation_info_tv.setText(String.format(
                        getString(R.string.safe_step_fighting_info), stepTarget - todayStep));
            } else {
                step_situation_info_tv.setText(String.format(
                        getString(R.string.step_fighting_info), stepTarget - todayStep));
            }
        } else {// 还没开始
            if (ToTwooApplication.owner.getGender() == 0) {
                step_doll_iv.setImageResource(R.drawable.step_boy_oh_no);
            } else {
                step_doll_iv.setImageResource(R.drawable.step_gril_oh_no);
            }
            step_situation_tv.setText("Go!");
            if (BleParams.isSecurityJewlery()) {
                step_situation_info_tv.setText(R.string.safe_step_oh_no_info);
            } else {
                step_situation_info_tv.setText(R.string.step_oh_no_info);
            }
        }
    }

    @Override
    protected void onDestroy() {
//        if(receiverStep != null){
//            unregisterReceiver(receiverStep);
//        }
        EventBus.getDefault().unregister(this);

        super.onDestroy();
    }

//	/**
//	 * 同步健步数据
//	 */
//	private void synchronousStepInfo() {
//		Calendar cal = Apputils.getZeroCalendar(null);
//		cal.add(Calendar.DAY_OF_MONTH, -6);
//		List<Map<String, String>> walkdata = new ArrayList<>();
//		for (int i = 0; i < 7; i++) {
//			Map<String, String> data = new HashMap<>();
//			data.put("ts", cal.getTimeInMillis() / 1000 + "");
//			data.put("step", stepWeekData.get(i) + "");
//			walkdata.add(data);
//			cal.add(Calendar.DAY_OF_MONTH, 1);
//		}
//
//		Gson gson = new Gson();
//		String json = gson.toJson(walkdata);
//
//		RequestParams params = new HttpHelper().getBaseParams(true);
//		params.addFormDataPart("walkdata", json);
//
//		HttpHelper.getHttpUtils().send(HttpMethod.POST,
//				HttpHelper.URL_WALK_DATA_UP, params,
//				new RequestCallBack<String>() {
//					@Override
//					public void onSuccess(ResponseInfo<String> responseInfo) {
//						LogUtils.i("step", "onSuccess");
//					}
//
//					@Override
//					public void onFailure(HttpException error, String msg) {
//						LogUtils.i("step",
//								"onFailure :" + msg + error.getExceptionCode());
//					}
//				});
//
//	}

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case STEPTARGETSETTING:
                stepTarget = ToTwooApplication.owner.getWalkTarget();
                tableView.setTargetStep(stepTarget);
                setTodayStepSituation();
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    //	// 为0的健步数据都需要去服务器同步
//	private List<Long> getNeedSynDate() {
//		List<Long> integers = new ArrayList<Long>();
//		Calendar cal = Apputils.getZeroCalendar(null);
//		cal.add(Calendar.DAY_OF_MONTH, -6);
//		for (int i = 0; i < stepWeekData.size(); i++) {
//			if (stepWeekData.get(i) == 0) {
//				integers.add(cal.getTimeInMillis());
//			}
//			cal.add(Calendar.DAY_OF_MONTH, 1);
//		}
//		return integers;
//	}
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
