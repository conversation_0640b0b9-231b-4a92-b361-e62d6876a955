package com.totwoo.totwoo.activity.nfc;

import android.app.PendingIntent;
import android.content.Intent;
import android.nfc.NdefMessage;
import android.nfc.NdefRecord;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.os.Bundle;
import android.os.Parcelable;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import com.blankj.utilcode.util.NetworkUtils;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.activity.WebActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.BindStateHttpBean;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.service.TotwooPlayService;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import java.util.ArrayList;

import rx.Observer;
import rx.Subscriber;

public class NfcBoundActivity extends BaseActivity {
    public static final int REQUEST_CODE_NFC_TAG = 345;
    public static final int REQUEST_CODE_NFC_SWITCH = 346;

    public static final int TIME_OUT_NFC_READ = 30 * 1000;

    private NfcAdapter nfcAdapter;
    private ViewGroup successLayout;
    private ViewGroup failedLayout;

    /**
     * 是否正在网络绑定中, 表示识别到 NFC 后, 联网的过程; 此阶段仍监控 NFC, 防止跳转浏览器, 但是不做新的请求相应;
     */
    private boolean isBounding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_nfc_bound);

        CommonUtils.setStateBar(this, false);

        nfcAdapter = NfcAdapter.getDefaultAdapter(this);

        if (nfcAdapter == null || !nfcAdapter.isEnabled()) {
            showErrorDialog(getString(R.string.nfc_error));
            return;
        } else if (!nfcAdapter.isEnabled()) { // 暂时屏蔽这个分支
            ToastUtils.showLong(this, getString(R.string.nfc_disable_info));
            startActivityForResult(new Intent(Settings.ACTION_NFC_SETTINGS), REQUEST_CODE_NFC_SWITCH);
        }

        successLayout = findViewById(R.id.nfc_bound_success_layout);
        failedLayout = findViewById(R.id.nfc_bound_failed_layout);

        findViewById(R.id.nfc_bound_back).setOnClickListener(v -> finish());
        findViewById(R.id.nfc_bound_failed_rebound_tv).setOnClickListener(v -> {
            failedLayout.setVisibility(View.GONE);
            stopMonitorNfc();
        });
        findViewById(R.id.nfc_bound_help_link).setOnClickListener(v -> WebActivity.showWeb(this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_NFC_BOUND_HELP)));

        if (ToTwooApplication.isDebug) {
            findViewById(R.id.nfc_bound_image).setOnLongClickListener(this::testBound);
        }
        parseNfcIntent(getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        parseNfcIntent(intent);
    }

    /**
     * 解析 NFC 信息
     *
     * @param intent
     */
    private void parseNfcIntent(Intent intent) {
        if (isBounding) {
            LogUtils.w("Is bounding, skip!!!");
            return;
        }

        String action = intent.getAction();

        LogUtils.d("parseNfcIntent: " + intent);

        Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
        // 查看 NFC 芯片的硬件信息
        LogUtils.d("Find nfc tag: " + tag);

        boolean matched = false;

        // 仅处理 NDEF 格式数据
        if (NfcAdapter.ACTION_NDEF_DISCOVERED.equals(action)) {
            // 原始数据获取
            Parcelable[] rawMessages =
                    intent.getParcelableArrayExtra(NfcAdapter.EXTRA_NDEF_MESSAGES);
            if (rawMessages != null && rawMessages.length > 0) {
                NdefMessage message = (NdefMessage) rawMessages[0];
                NdefRecord[] records = message.getRecords();

                // 格式: 第一条为 URL, 第二条为 TOTWOO_NFC 开头的名称
                if (records.length > 1) {
                    try {
                        String url = records[0].toUri().toString();
                        byte[] nameBytes = records[1].getPayload();// 格式: codeLength+code_byte+text_byte
                        int codeLength = nameBytes[0] & 0xff;
                        String name = new String(nameBytes, codeLength + 1, nameBytes.length - (codeLength + 1));
                        if (!TextUtils.isEmpty(url)
                                && url.contains(CommonArgs.BRAND_IDENTITY)
                                && !TextUtils.isEmpty(name)
                                && name.startsWith(BleParams.JEWELRY_NFC_NAME_PREF)) {
                            matched = true;

//                            CommonArgs.NFC_HARDWARE_URL_PREFIX = url.substring(0, url.lastIndexOf("/") + 1);

                            boundNfcJewelry(url.substring(url.lastIndexOf("/") + 1), name);
                        } else {
                            // 测试期间, 针对高概率数据异常的情况给予提示
                            for (int i = 0; i < records.length; i++) {
                                NdefRecord record = records[i];
                                if (record.toUri().toString().contains(CommonArgs.NFC_HARDWARE_URL_PREFIX) && i != 0) {
                                    // Url 非首条数据
                                    ToastUtils.showLong(this, R.string.nfc_format_error_url_not_first);
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            if (!matched) {
                showErrorDialog(getString(R.string.nfc_format_error));
            }
        }
    }

    /**
     * 展示绑定失败的 Dialog
     */
    private void showErrorDialog(String msg) {
        try {
            CommonMiddleDialog dialog = new CommonMiddleDialog(this);
            dialog.setMessage(msg);
            dialog.setSure(getString(R.string.i_know), v -> finish());
            dialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 开始绑定流程
     *
     * @param hardwareId
     */
    private void boundNfcJewelry(String hardwareId, String name) {
        if (!NetworkUtils.isConnected()) {
            showErrorDialog(getResources().getString(R.string.error_net));
            return;
        }

        isBounding = true;
        cancelTimeoutTimer();
        LogUtils.d("Start bound Nfc jewelry: " + hardwareId);
        NfcLoading.show(this);
        HttpHelper.commonService.bindState(name, hardwareId, "", "connect")
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<BindStateHttpBean>>() {
                    @Override
                    public void onCompleted() {
                        isBounding = false;
                        NfcLoading.dismiss();
                    }

                    @Override
                    public void onError(Throwable e) {
                        LogUtils.e(e.getMessage() + "\n" + Log.getStackTraceString(e));
                        showErrorDialog(HttpHelper.getUnknownErrorMessage(NfcBoundActivity.this, e.getMessage()));
                    }

                    @Override
                    public void onNext(HttpBaseBean<BindStateHttpBean> bean) {
                        if (bean.getErrorCode() == 0) {
                            saveJewelryData(hardwareId, name);
                        } else if (bean.getErrorCode() == 902) {
                            showErrorDialog(getString(R.string.nfc_bound_error_is_binding));
                        } else {
                            ToastUtils.showLong(NfcBoundActivity.this, bean.getErrorMsg());
                        }
                    }
                });
    }


    /**
     * 做设备首次连接要做的数据保存工作
     */
    private void saveJewelryData(String hardwareId, String name) {

        try {

            ArrayList<LocalJewelryInfo>  infos = (ArrayList<LocalJewelryInfo>) LocalJewelryDBHelper.getInstance().getAllBeans();
            for (LocalJewelryInfo info : infos) {
                //设备列表,判断是否已经包含了hardwareId
                if (info.getMac_address().equals(hardwareId)) {
                    ToastUtils.showLong(this,getString(R.string.me_has_nfc));
                    return;
                }
            }

        } catch (DbException e) {
            e.printStackTrace();
        }


        // 保存Mac地址
        PreferencesUtils.put(this, BleParams.PAIRED_BLE_ADRESS_TAG, hardwareId);
        // 保存连接设备类型
        PreferencesUtils.put(this, BleParams.PAIRED_JEWELRY_NAME_TAG, name);

        //搜索添加的需要首饰信息到数据库
        LocalJewelryInfo info = new LocalJewelryInfo(hardwareId, name, 1, System.currentTimeMillis());
        LocalJewelryDBHelper.getInstance().addBean(info);
        HttpHelper.multiJewelryService.addJewelry(hardwareId, hardwareId, name, "", "", 1, "")
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            // 绑定成功
                            LogUtils.d("Jewelry bound success: " + hardwareId);
                            if (successLayout != null) {
                                successLayout.setVisibility(View.VISIBLE);
                                notifyBoundSuccess();
                                mHandler.postDelayed(() -> goNext(true), 3000);
                            }
                        } else {
                            ToastUtils.showLong(NfcBoundActivity.this, R.string.nfc_bound_failed);
                        }
                    }
                });
        try {
            if (LocalJewelryDBHelper.getInstance().getAllBeans().size() > 1) {
                LocalJewelryDBHelper.getInstance().setSelected(hardwareId);
            }
        } catch (DbException e) {
            e.printStackTrace();
        }
        PreferencesUtils.put(this, BleParams.PAIRED_JEWELRY_INFO_ADDED, true);
    }

    private Runnable timeOutRunnable = () -> {
        if (failedLayout != null) {
            failedLayout.setVisibility(View.VISIBLE);
            stopMonitorNfc();
        }
    };

    @Override
    protected void onResume() {
        super.onResume();
        monitorNfc();
    }

    @Override
    protected void onPause() {
        super.onPause();

        cancelTimeoutTimer();
        stopMonitorNfc();
    }

    private void monitorNfc() {
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {

            PendingIntent pendingIntent = PendingIntent.getActivity(this, REQUEST_CODE_NFC_TAG,
                    new Intent(this, NfcBoundActivity.class).addFlags(Intent.FLAG_ACTIVITY_NEW_TASK),
                    Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));

            nfcAdapter.enableForegroundDispatch(this, pendingIntent,
                    null,
                    null);

            // 超时处理
            mHandler.postDelayed(timeOutRunnable, TIME_OUT_NFC_READ);
        }
    }

    private void stopMonitorNfc() {
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {
            nfcAdapter.disableForegroundDispatch(this);
        }
    }

    /**
     * 播放绑定成功的音频
     */
    private void notifyBoundSuccess() {
        startService(new Intent(this, TotwooPlayService.class));
    }


    private void cancelTimeoutTimer() {
        mHandler.removeCallbacks(timeOutRunnable);
    }


    private void goNext(boolean isSuccess) {
        //登录页面跳转逻辑
        if (TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN) && isSuccess) {
            HomeActivityControl.getInstance().openHomeActivity(NfcBoundActivity.this);
        } else {
            //连接入口页跳转逻辑
            if (isSuccess) {
                LogUtils.e("HomeActivityControl");
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ORDER_UPDATE, null);
                // 清理原有的页面, 并跳转对应的首页
                HomeActivityControl.getInstance().connectJew(NfcBoundActivity.this);
            }
        }
        overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
        finish();
    }

    /**
     * 添加测试; 仅测试 UI 跳转, 并无实际的数据绑定.
     *
     * @param view
     */
    public boolean testBound(View view) {
        // 绑定成功
//        successLayout.setVisibility(View.VISIBLE);
//        saveJewelryData("nfc_test_" + (System.currentTimeMillis() % 100000000), BleParams.JEWELRY_NFC_NAME_MEET_PREF + "002");
//        notifyBoundSuccess();
//        mHandler.postDelayed(() -> goNext(true), 3000);
//
//
        Intent it = new Intent(NfcAdapter.ACTION_NDEF_DISCOVERED);
        Parcelable[] rawMessages = new Parcelable[1];

        NdefRecord[] records = new NdefRecord[2];
        records[0] = NdefRecord.createUri(CommonArgs.NFC_HARDWARE_URL_PREFIX + "nfc_test_" + (System.currentTimeMillis() % 100000000));

        records[1] = NdefRecord.createTextRecord("en", BleParams.JEWELRY_NFC_NAME_MEET_001);

        NdefMessage message = new NdefMessage(records);
        rawMessages[0] = message;
        it.putExtra(NfcAdapter.EXTRA_NDEF_MESSAGES, rawMessages);
        parseNfcIntent(it);
        return true;
    }
}