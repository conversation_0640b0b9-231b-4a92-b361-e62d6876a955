package com.totwoo.totwoo;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.os.Process;
import android.text.TextUtils;

import androidx.multidex.MultiDexApplication;

import com.blankj.utilcode.util.Utils;
import com.hjq.toast.Toaster;
import com.huawei.hms.maps.MapsInitializer;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.mars.xlog.Log;
import com.tencent.mars.xlog.Xlog;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.activity.WelcomeActivity;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.DataStatisticsClient;
import com.totwoo.totwoo.service.KeepAliveService;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.wearClientUtil.TeleportClient;
import com.umeng.commonsdk.UMConfigure;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import cn.jiguang.api.JCoreInterface;
import cn.jiguang.api.utils.JCollectionAuth;
import cn.jpush.android.api.JPushInterface;

/**
 * 全局的 Application， 保存全局变量，处理应用级任务
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class ToTwooApplication extends MultiDexApplication {
    /**
     * 是否登录
     */
    public static Owner owner;

    /**
     * 提供全局的Context
     */
    public static Context baseContext;

    public static TeleportClient mTeleportClient;

    public static String otherPhone;

    public static AppCacheData cacheData;

    public static KeepAliveService mService;

    public static boolean isForeground;

    /**
     * 默认与包构建方式相同, 特定条件下可修改, 全局控制
     */
    public static boolean isDebug = BuildConfig.DEBUG;

    @Override
    public void onCreate() {
        super.onCreate();
        baseContext = this;

        TimInitBusiness.initOfflinePushConfigs(this);
        Apputils.getGlobalExecutor().submit(this::initThirdPartService);
    }

    /**
     * 是否完成了基本信息设置
     *
     * @return
     */
    public static boolean isInfoSetFinish(Owner owner) {
        return !owner.isNew();
    }

    /**
     * 检查当前进程是否 主进程
     *
     * @param pid
     * @return
     */
    private boolean checkApp(int pid) {
        ActivityManager am = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningApps = am.getRunningAppProcesses();
        if (runningApps != null && !runningApps.isEmpty()) {
            for (ActivityManager.RunningAppProcessInfo procInfo : runningApps) {
                if (procInfo.pid == pid) {
                    if (procInfo.processName.equals("com.totwoo.totwoo")) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 开启连接服务
     */
    public static void startJewelryConnectService(final Context mContext) {
        final Intent i = new Intent(mContext, KeepAliveService.class);
        i.setAction(BleParams.ACTION_START);
        baseContext.startService(i);
    }

    /**
     * 初始化需要全局使用的第三方服务
     * 为提高应用启动速度, 在启动界面启动之后调用
     */
    public void initThirdPartService() {
        // 因为多进程的使用, 会导致 TotwooApplication 多次调用,
        // 下面的所有初始化服务, 只进行一次即可
        int pid = Process.myPid();
        if (!checkApp(pid)) {
            return;
        }

        cacheData = new AppCacheData();

        // 正式发布版本, 屏蔽说明性Log
        if (!ToTwooApplication.isDebug) {
            LogUtils.allowE = false;
            LogUtils.allowD = false;
            LogUtils.allowV = false;
            LogUtils.allowI = false;
            LogUtils.allowW = false;
        }

        // 装置默认的 EventBus, 增加 EventIndex, 提高注册事件的效率
        EventBus.builder().addIndex(new TotwooEventIndex()).installDefaultEventBus();

        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PREF_RECHECK);

        owner = Owner.getCurrOwner();

        HttpHelper.initHttp();

        // 已经同意了服务条款的情况
        if (PreferencesUtils.getBoolean(this, WelcomeActivity.PERMISSION_CHECKED, false)) {
            doAfterTermAgree();
        } else {
            // 需要预加载的情况
            UMConfigure.preInit(this, "55cdc7f4e0f55a36c1004a7c", BuildConfig.CHANNEL);
            JCollectionAuth.setAuth(this, false); // 后续启用推送业务功能逻辑将被拦截
        }

    }


    /**
     * 服务条款同意后才能初始化的工作
     */
    public void doAfterTermAgree() {
        // 初始化 Toast 框架
        Toaster.init(this);
        // 友盟
//        MobSDK.submitPolicyGrantResult(true);
        UMConfigure.submitPolicyGrantResult(getApplicationContext(), true);

        //地图初始化
        MapsInitializer.initialize(this);

        // 添加数据统计, app 启动
        DataStatisticsClient.triggerStartUP();

        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED) {
            if (JewInfoSingleton.getInstance().isPaired()) {
                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
            }
        }

        startJewelryConnectService(this);

//        FacebookSdk.setApplicationId("2165854100108506");
//        FacebookSdk.setClientToken("********************************");
//        FacebookSdk.sdkInitialize(getApplicationContext());
//        AppEventsLogger.activateApp(this);
//        MobSDK.init(getApplicationContext());

        // 初始化友盟统计
        UMConfigure.setLogEnabled(BuildConfig.DEBUG);
        UMConfigure.init(this, "55cdc7f4e0f55a36c1004a7c", BuildConfig.CHANNEL, UMConfigure.DEVICE_TYPE_PHONE, "");
        LogUtils.e("totwoo apk channel: " + BuildConfig.CHANNEL);

        CrashReport.initCrashReport(this, "1104740113", BuildConfig.DEBUG);

        // 开启推送相关服务
        JCollectionAuth.setAuth(this, true);
        JPushInterface.setDebugMode(isDebug); // 设置开启日志,发布时请关闭日志
        JCoreInterface.setWakeEnable(this, false);
        JPushInterface.setGeofenceEnable(this, false);
        JPushInterface.init(ToTwooApplication.baseContext); // 初始化 JPush

        final IWXAPI msgApi = WXAPIFactory.createWXAPI(baseContext, null);
        // 将该app注册到微信
        msgApi.registerApp("wx7e9b33b41b70a0ce");

        //初始化IMSDK
        TimInitBusiness.init(getApplicationContext());

        initXLog();
    }

    private static void initXLog() {
        String totwooId = ToTwooApplication.owner.getTotwooId();
        if(TextUtils.isEmpty(totwooId)){
            return;
        }

        System.loadLibrary("c++_shared");
        System.loadLibrary("marsxlog");

        String cacheDir = Utils.getApp().getFilesDir() + "/twxlog";

        Xlog xlog = new Xlog();
        Log.setLogImp(xlog);
        if (BuildConfig.DEBUG) {
            Log.setConsoleLogOpen(true);
        } else {
            Log.setConsoleLogOpen(false);
        }
        Log.appenderOpen(Xlog.LEVEL_DEBUG, Xlog.AppednerModeAsync, cacheDir, cacheDir, totwooId, 2);
    }
}
