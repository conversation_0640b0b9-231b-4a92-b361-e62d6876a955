package com.totwoo.totwoo.keepalive.companion

import android.Manifest
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.companion.AssociationInfo
import android.companion.CompanionDeviceService
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.text.TextUtils
import androidx.annotation.RequiresApi
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import com.tencent.mars.xlog.Log
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.service.KeepAliveService

/**
 * ToTwoo配套设备服务
 *
 * 功能：
 * 1. 监听配套设备的出现和消失
 * 2. 自动触发重连逻辑
 * 3. 利用配套设备权限启动前台服务
 * 4. 防抖和智能重连机制
 *
 * 注意：此服务需要Android 12+ (API 31+)
 */
@RequiresApi(Build.VERSION_CODES.S)
class TotwooCompanionService : CompanionDeviceService() {

    companion object {
        private const val TAG = "TotwooCompanionService"

        // 防抖机制相关
        private const val DEBOUNCE_DELAY_MS = 3 * 1000L // 3秒防抖
    }

    // 防抖机制状态
    private var lastDeviceAppearedTime = 0L
    private var lastAppearedDeviceMac = ""


    private val bluetoothManager: BluetoothManager by lazy {
        applicationContext.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    override fun onDeviceAppeared(associationInfo: AssociationInfo) {
        super.onDeviceAppeared(associationInfo)
        if (missingPermissions()) {
            return
        }
        val deviceMac = associationInfo.deviceMacAddress?.toString()?.uppercase() ?: return

        val currentTime = System.currentTimeMillis()
        // 防抖检查：如果是同一设备在短时间内重复出现，忽略
        if (deviceMac == lastAppearedDeviceMac &&
            (currentTime - lastDeviceAppearedTime) < DEBOUNCE_DELAY_MS
        ) {
            LogUtils.d(TAG, "设备出现事件防抖，忽略重复触发: $deviceMac")
            return
        }

        Log.d(TAG, "设备出现$deviceMac")

        try {
            // 1. 启动前台服务（利用配套设备权限）
            startForegroundServiceIfNeeded()

            // 2. 智能触发自动重连
            smartReconnect(associationInfo, deviceMac)

        } catch (e: Exception) {
            Log.e(TAG, "处理设备出现事件失败: ${e.message}")
        } finally {
            lastDeviceAppearedTime = currentTime
            lastAppearedDeviceMac = deviceMac
        }
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    private fun smartReconnect(associationInfo: AssociationInfo, deviceMac: String) {
        var device: BluetoothDevice? = null
        if (Build.VERSION.SDK_INT >= 34) {
            device = associationInfo.associatedDevice?.bleDevice?.device
        }

        if (device == null) {
            device = bluetoothManager.adapter.getRemoteDevice(deviceMac)
        }
        if (device != null && !TextUtils.isEmpty(device.name)) {
            BluetoothManage.getInstance().connect(device)
            Log.d(TAG, "配对服务触发")
        } else {
            //
            BluetoothManage.getInstance().reconnect(false)
        }
    }

    override fun onDeviceDisappeared(associationInfo: AssociationInfo) {
        super.onDeviceDisappeared(associationInfo)
        if (missingPermissions()) {
            return
        }
        Log.i(TAG, "配套设备消失")
    }


    /**
     * Check BLUETOOTH_CONNECT is granted and POST_NOTIFICATIONS is granted for devices running
     * Android 13 and above.
     */
    private fun missingPermissions(): Boolean = ActivityCompat.checkSelfPermission(
        this,
        Manifest.permission.BLUETOOTH_CONNECT,
    ) != PackageManager.PERMISSION_GRANTED ||
            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
                    ActivityCompat.checkSelfPermission(
                        this,
                        Manifest.permission.POST_NOTIFICATIONS,
                    ) != PackageManager.PERMISSION_GRANTED)


    /**
     * 启动keepAlive
     */
    private fun startForegroundServiceIfNeeded() {
        val serviceIntent = Intent(applicationContext, KeepAliveService::class.java).apply {
            action = BleParams.ACTION_KEEP_ALIVE
            putExtra("trigger", TAG)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            applicationContext.startForegroundService(serviceIntent)
        } else {
            applicationContext.startService(serviceIntent)
        }
    }

}
