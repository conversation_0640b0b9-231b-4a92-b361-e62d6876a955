package com.totwoo.totwoo.keepalive

import android.content.Context
import android.content.Intent
import android.os.Build
import android.text.TextUtils
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.blankj.utilcode.util.ThreadUtils
import com.tencent.mars.xlog.Log
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.service.KeepAliveService
import com.totwoo.totwoo.utils.PreferencesUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * 蓝牙保活CoroutineWorker - 根据Android官方文档优化
 *
 * 参考文档：
 * https://developer.android.com/develop/background-work/background-tasks/persistent/how-to/long-running
 *
 * 功能：
 * 1. 15分钟周期检查服务状态
 * 2. 检查蓝牙连接状态并尝试重连
 * 3. 复用KeepAliveService的前台服务和通知
 * 4. 异常自动恢复和重试机制
 * 5. 使用协程提供更好的异步处理
 * 6. 声明为connectedDevice前台服务类型
 */
class WorkerKeepAlive(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    companion object {
        private const val TAG = "WorkerKeepAlive"
        private const val WORK_NAME = "bluetooth_keep_alive_work"
        private const val WORK_TAG = "bluetooth_keep_alive"

        /**
         * 调度WorkManager任务 - 根据官方文档优化
         */
        @JvmStatic
        fun scheduleWork(context: Context) {
            Log.d(TAG, "开始调度CoroutineWorker保活任务")

            // 根据官方文档建议的约束条件
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)  // 不需要网络
                .setRequiresBatteryNotLow(false)  // 不要求电池充足，保活更重要
                .setRequiresCharging(false)       // 不要求充电状态
                .setRequiresDeviceIdle(false)     // 不要求设备空闲
                .setRequiresStorageNotLow(true)   // 要求存储空间充足
                .build()

            // 15分钟周期任务
            val workRequest = PeriodicWorkRequestBuilder<WorkerKeepAlive>(15, TimeUnit.MINUTES)
                .setConstraints(constraints)
                .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)  // 失败时指数退避
                .addTag(WORK_TAG)
                .setInitialDelay(1, TimeUnit.MINUTES)  // 1分钟后开始第一次执行
                .build()

            // 使用REPLACE策略确保只有一个实例运行
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,  // 替换现有任务
                workRequest
            )

            Log.d(TAG, "✅ CoroutineWorker保活任务调度成功 - 15分钟周期")
        }

        /**
         * 取消WorkManager任务
         */
        @JvmStatic
        fun cancelWork(context: Context) {
            try {
                val workManager = WorkManager.getInstance(context)

                // 取消特定的工作
                workManager.cancelUniqueWork(WORK_NAME)

                // 也可以通过标签取消
                workManager.cancelAllWorkByTag(WORK_TAG)

                Log.d(TAG, "✅ CoroutineWorker保活任务已取消")
            } catch (e: Exception) {
                Log.e(TAG, "取消WorkManager任务失败: ${e.message}")
            }
        }

    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            // 1. 检查是否有配对的蓝牙设备
            if (!hasBluetoothJewelry()) {
                LogUtils.d(TAG, "没有配对的蓝牙首饰，跳过保活检查")
                return@withContext Result.success()
            }

            // 2. 确保KeepAliveService运行
            if (!ensureServiceRunning()) {
                Log.w(TAG, "KeepAliveService启动失败，将重试")
                return@withContext Result.retry()
            }

            //3. 检查蓝牙连接状态
            // 5. 如果未连接，尝试重连
            BluetoothManage.getInstance().reconnect(false)
            Log.d(TAG, "🔍 CoroutineWorker保活检查完成")

            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "CoroutineWorker保活检查失败: ${e.message}")
            e.printStackTrace()

            // 根据重试次数决定策略
            if (runAttemptCount < 3) {
                LogUtils.d(TAG, "将在1分钟后重试，当前重试次数: $runAttemptCount")
                Result.retry()
            } else {
                LogUtils.e(TAG, "重试次数过多，标记为失败")
                Result.failure()
            }
        }
    }


    /**
     * 检查是否有蓝牙首饰
     */
    private fun hasBluetoothJewelry(): Boolean {
        val jewelryName =
            PreferencesUtils.getString(applicationContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "")
        return !TextUtils.isEmpty(jewelryName) && BleParams.isBluetoothJewelry(jewelryName)
    }

    /**
     * 确保KeepAliveService运行
     * 复用现有的前台服务，简单可靠
     */
    private fun ensureServiceRunning(): Boolean {
        return try {
            val serviceIntent = Intent(applicationContext, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_KEEP_ALIVE
                putExtra("trigger", TAG)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                applicationContext.startForegroundService(serviceIntent)
            } else {
                applicationContext.startService(serviceIntent)
            }
            true
        } catch (e: Exception) {
            false
        }
    }

}
