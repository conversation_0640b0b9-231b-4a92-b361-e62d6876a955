<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=safari">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <!-- 验证码程序依赖(必须)。请勿修改以下程序依赖，如通过其他手段规避加载，会导致验证码无法正常更新，对抗能力无法保证，甚至引起误拦截。 -->
    <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
</head>

<body style="background-color: transparent;">

</body>

<script>

    // 定义回调函数
    function callback(res) {
        // 第一个参数传入回调结果，结果如下：
        // ret         Int       验证结果，0：验证成功。2：用户主动关闭验证码。
        // ticket      String    验证成功的票据，当且仅当 ret = 0 时 ticket 有值。
        // CaptchaAppId       String    验证码应用ID。
        // bizState    Any       自定义透传参数。
        // randstr     String    本次验证的随机串，后续票据校验时需传递该参数。
        console.log('callback:', res);

        // res（用户主动关闭验证码）= {ret: 2, ticket: null}
        // res（验证成功） = {ret: 0, ticket: "String", randstr: "String"}
        // res（请求验证码发生错误，验证码自动返回terror_前缀的容灾票据） = {ret: 0, ticket: "String", randstr: "String",  errorCode: Number, errorMessage: "String"}
        // 此处代码仅为验证结果的展示示例，真实业务接入，建议基于ticket和errorCode情况做不同的业务处理

        /// 回调结果
        CaptchaController.onResult(res['ret'], res['ticket'], res['randstr']);
    }

    // 定义验证码js加载错误处理函数
    function loadErrorCallback() {

        var appid = 'totwoo';

        // 生成容灾票据或自行做其它处理
        var ticket = 'terror_1001_' + appid + '_' + Math.floor(new Date().getTime() / 1000);
        callback({
            ret: 0,
            randstr: '@' + Math.random().toString(36).substr(2),
            ticket: ticket,
            errorCode: 1001,
            errorMessage: 'jsload_error'
        });

    }

    // 获取 URL 查询字符串中的参数
    function getURLParameters() {
      var params = {};
      var queryString = window.location.search.substring(1);
      var pairs = queryString.split("&");

      for (var i = 0; i < pairs.length; i++) {
        var pair = pairs[i].split("=");
        var key = decodeURIComponent(pair[0]);
        var value = decodeURIComponent(pair[1] || "");

        params[key] = value;
      }

      return params;
    }

    // 定义验证码触发事件
    // captchaAppId 服务端获取的代表验证码类型的 id
    // 用户语言
    // 是否显示加载框
    function showCaptchaImage(captchaAppId, userLanguage) {

        try {
            // 生成一个验证码对象
            // CaptchaAppId：登录验证码控制台，从【验证管理】页面进行查看。如果未创建过验证，请先新建验证。注意：不可使用客户端类型为小程序的CaptchaAppId，会导致数据统计错误。
            //callback：定义的回调函数
            var captcha = new TencentCaptcha(captchaAppId, callback, {"needFeedBack":false, "userLanguage": userLanguage, "loading": false, "ready": function () {
                // 验证码ready之后回调给上层
                CaptchaController.onLoadReady();
            }});

            // 调用方法，显示验证码
            captcha.show();

        } catch (error) {
            // 加载异常，调用验证码js加载错误处理函数
            loadErrorCallback();
        }
    }

    window.onload = function () {
        // 获取加载参数
        var parameters = getURLParameters();

        // captchaAppId 服务端获取的代表验证码类型的 id
        // 用户语言
        showCaptchaImage(parameters['captchaAppId'], parameters['language']);
    }




</script>

</html>