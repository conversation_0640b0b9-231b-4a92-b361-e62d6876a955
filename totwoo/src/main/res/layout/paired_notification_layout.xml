<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center_vertical|end"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/paired_notification_me_name_tv"
                style="@style/TextAppearance.Compat.Notification.Title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/paired_notification_jew_connected_icon"
                android:drawablePadding="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textSize="11sp"
                tools:text="亲爱的" />

            <TextView
                android:id="@+id/paired_notification_me_jewelry_state_tv"
                style="@style/TextAppearance.Compat.Notification"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                tools:text="首饰已连接"
                android:textSize="8sp" />
        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/paired_notification_me_head_iv"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_marginHorizontal="8dp"
        android:src="@drawable/love_manage_default_head" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:layout_width="60dp"
            android:layout_height="20dp"
            android:scaleType="centerInside"
            android:src="@drawable/paired_notification_love_icon" />

        <TextView
            android:id="@+id/paired_notification_totwoo_count_tv"
            style="@style/TextAppearance.Compat.Notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/love_collect"
            android:textSize="8sp" />
    </LinearLayout>


    <ImageView
        android:id="@+id/paired_notification_paired_head_iv"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_marginHorizontal="8dp"
        android:src="@drawable/love_manage_default_head" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.8"
        android:gravity="center_vertical|start"
        android:orientation="vertical">

        <TextView
            android:id="@+id/paired_notification_paired_name_tv"
            style="@style/TextAppearance.Compat.Notification.Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="亲爱的"
            android:textSize="11sp" />
    </LinearLayout>
</LinearLayout>