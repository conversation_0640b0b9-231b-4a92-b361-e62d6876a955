apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.huawei.agconnect'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-android'

android {
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        applicationId "com.totwoo.totwoo"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        multiDexEnabled true

        // 为实现动态模糊引入 v8 库
        renderscriptTargetApi 29
        renderscriptSupportModeEnabled true

        versionCode getLocalVersionCode()
        versionName getLocalVersionName() +
                "(" + "git rev-parse --short=7 HEAD".execute().text.trim() +
                "." + (Integer.parseInt("git rev-list HEAD --count".execute().text.trim()) - 1000) + ")"

        lintOptions {
//            checkReleaseBuilds false
//            // Or, if you prefer, you can continue to check for errors in release builds,
//            // but continue the build even when errors are found:
//            abortOnError false
            disable 'GoogleAppIndexingWarning'
            baseline file("lint-baseline.xml") // your choice of filename/path here
        }

        ndk {
            // 设置支持的SO库架构
            // noinspection ChromeOsAbiSupport
            abiFilters 'armeabi-v7a', 'arm64-v8a'//, 'x86', 'x86_64'
        }

        resConfigs "en", "zh", "ja", "fr", "de", "it", "ko", "es","ru"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [eventBusIndex: 'com.totwoo.totwoo.TotwooEventIndex']
            }
        }

        buildFeatures {
            viewBinding true
        }

        manifestPlaceholders = [
                JPUSH_PKGNAME: applicationId,
                JPUSH_APPKEY : "3059168ac662268dbbe3ff73", //JPush上注册的包名对应的appkey.
                JPUSH_CHANNEL: "TOTWOO", //暂时填写默认值即可.

                "VIVO_APPKEY": "8b0aa6c68b20756a69e1f18c059a4ad8",
                "VIVO_APPID" : "100108610",
                "HONOR_APPID": " "
        ]

        buildConfigField("String", "CHANNEL", "\"TOTWOO\"")
    }

    signingConfigs {
        release {
            storeFile file('totwoo.keystore')
            storePassword "totwoo2015"
            keyAlias "totwoo"
            keyPassword "totwoo2015"
        }
    }

    lintOptions {
        abortOnError false
        checkReleaseBuilds false
        disable("InnerClasses")
    }

    flavorDimensions = ["channel"]

    productFlavors {
        common {
            dimension "channel"
            buildConfigField("String", "CHANNEL", "\"TOTWOO\"")
        }

        googleplay {
            dimension "channel"
            buildConfigField("String", "CHANNEL", "\"GOOGLE_PLAY\"")
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
            signingConfig signingConfigs.release

            packagingOptions {
                // 如果CPU架构为ARMv7、ARMv8时，需增加如下配置。
                doNotStrip "*/arm64-v8a/libucs-credential.so"
                doNotStrip "*/armeabi-v7a/libucs-credential.so"
                // 如果CPU架构为x86时，需增加如下配置。
                doNotStrip "*/x86/libucs-credential.so"
                doNotStrip "*/x86_64/libucs-credential.so"
                // 删除X86平台依赖的so文件
                exclude "lib/x86/libucs-credential.so"
                // 删除x86_64平台依赖的so文件
                exclude "lib/x86_64/libucs-credential.so"
            }
        }
        debug {
            minifyEnabled false
            signingConfig signingConfigs.release // 和 Release 包使用同一个签名, 方面相互切换调试

            packagingOptions {
                // 如果CPU架构为ARMv7、ARMv8时，需增加如下配置。
                doNotStrip "*/arm64-v8a/libucs-credential.so"
                doNotStrip "*/armeabi-v7a/libucs-credential.so"
                // 如果CPU架构为x86时，需增加如下配置。
                doNotStrip "*/x86/libucs-credential.so"
                doNotStrip "*/x86_64/libucs-credential.so"
                // 删除X86平台依赖的so文件
                exclude "lib/x86/libucs-credential.so"
                // 删除x86_64平台依赖的so文件
                exclude "lib/x86_64/libucs-credential.so"
            }
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    buildFeatures {
        aidl true
    }
    namespace 'com.totwoo.totwoo'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "tw_${variant.name}_v${variant.versionName}}.apk"
        }
    }

    repositories {
        flatDir {
            dirs 'libs' //this way we can find the .aar file in libs folder
        }
    }
}


// 获取app版本信息，从配置文件读取
def getLocalVersionCode() {
    def versionFile = file('build.properties')

    if (versionFile.exists()) {
        Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionFile))
        def versionCode = versionProps['versionCode'].toInteger()
        return versionCode
    } else {
        throw new GradleException("Could not find version.properties!")
    }
}

def getLocalVersionName() {
    def versionFile = file('build.properties')

    if (versionFile.exists()) {
        Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionFile))
        def versionName = versionProps['versionName'].toString()
        return versionName
    } else {
        throw new GradleException("Could not find version.properties!")
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    if (isLibEaseSource.toBoolean()) {
        implementation project(':ease')
    } else {
        implementation 'com.totwoo:ease:1.0.0-+'
    }

    if (isLibMp3ReSource.toBoolean()) {
        implementation project(':mp3_re')
    } else {
        implementation 'com.totwoo:mp3_re:1.0.0-+'
    }

    if (isLibVideoRecorderSource.toBoolean()) {
        implementation project(':VideoRecorder')
    } else {
        implementation 'com.totwoo:video-recorder:1.0.0-+'
    }

    if (isLibFrameworkSource.toBoolean()) {
        implementation project(':framework')
    } else {
        implementation 'com.totwoo:framework:1.0.0-+'
    }

    if (isLibTotwooLibrarySource.toBoolean()) {
        implementation project(':totwoo_library')
    } else {
        implementation 'com.totwoo:library:1.0.0-+'
    }

    implementation project(':tim:imui_chat')
    implementation project(':tim:im_push')
    implementation project(':tim:imui_contact')

    if (isLibTimCoreSource.toBoolean()) {
        implementation project(':tim:imui_core')
    } else {
        implementation 'com.totwoo.tim:imui-core:1.0.0-+'
    }

//    wearApp project(':totwoo_wear')


    // multidex
    implementation 'androidx.multidex:multidex:2.0.1'
    // Bugly
    implementation 'com.tencent.bugly:crashreport:4.1.9.3'
//    implementation "com.tencent.bugly:nativecrashreport:3.9.2"


    // 固件DFU 升级,
    // 升级最新的版本2.3.1, 要求 compileSdk 到 34, 且 jdk 到 17, butterknife 已不再支持
    implementation 'no.nordicsemi.android:dfu:2.4.2'
    implementation 'no.nordicsemi.android.support.v18:scanner:1.6.0'
    implementation 'no.nordicsemi.android:ble:2.10.1'

    //wear支持库
    //noinspection GradleDependency
    implementation 'com.google.android.gms:play-services-wearable:17.1.0'

    // butterknife
    implementation 'com.jakewharton:butterknife:10.2.3'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.work:work-runtime:2.10.2'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'
    implementation 'com.jakewharton.rxbinding:rxbinding:1.0.0'
    //拼音库
//    implementation 'com.github.promeg:tinypinyin:2.0.1'
    // EventBus 消息机制
    implementation 'org.greenrobot:eventbus:3.3.1'
    annotationProcessor 'org.greenrobot:eventbus-annotation-processor:3.3.1'


    // 极光推送(国内版本) 5.0.0 版本开始可以自动拉取 JCore 包，无需另外配置
    commonImplementation 'cn.jiguang.sdk:jpush:5.7.0'
    implementation 'cn.jiguang.sdk:joperate:2.0.2'     // 可选，集成极光分析SDK后，即可支持行为触发推送消息、推送转化率统计，用户行为分析和用户标签等功能
    // 极光推送(googlePlay 版本)
    googleplayImplementation 'cn.jiguang.sdk:jpush-google:5.7.0'

    implementation 'cn.jiguang.sdk.plugin:fcm:5.7.0'//fcm

    implementation 'cn.jiguang.sdk.plugin:huawei:5.7.0'//hw



    //umeng统计
    implementation 'com.umeng.umsdk:common:9.6.8'// 必选
    implementation 'com.umeng.umsdk:asms:1.8.7.2'// 必选


    //facebook分享
    implementation("com.facebook.android:facebook-share:17.0.0") {
        transitive = true
        exclude group: 'androidx.annotation:annotation'
        exclude group: 'androidx.core:core-ktx', module: 'core-ktx'
        exclude group: 'androidx.legacy', module: 'legacy-support-core-ui'
        exclude group: 'androidx.legacy', module: 'legacy-support-core-utils'
        exclude group: 'androidx.legacy', module: 'legacy-support-v4'
    }

    implementation 'com.tencent.sonic:sdk:3.1.0'

    implementation 'com.liulishuo.magicprogresswidget:library:1.0.2'
    implementation 'com.github.hackware1993:MagicIndicator:1.7.0'

    implementation 'com.eightbitlab:blurview:1.6.3'
    implementation 'com.ldoublem.loadingview:loadingviewlib:1.0'

    // 鲁班压缩 - 高效的图片压缩库
    implementation 'top.zibin:Luban:1.1.8'

    implementation 'com.github.bumptech.glide:glide:4.12.0'
    //noinspection GradleDependency
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.50'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'

//    implementation 'com.lk.mapsdk:sdk-full:2.3.7'
//    implementation 'com.lk.mapsdk:sdk-no-route:2.3.7'
    implementation ('com.huawei.hms:location:6.12.0.300')
//            {
//        exclude group: 'com.huawei.hms.LocationLiteSdk'
//    }
//    implementation("com.huawei.hms.LocationLiteSdk:core:2.16.0.301")

    implementation 'com.huawei.hms:maps:6.11.2.301'
    implementation 'com.huawei.hms:maps-basic:6.11.2.301'

    implementation('com.airbnb.android:lottie:6.0.0') {
        exclude group: 'com.android.support'
    }
    //微信支付
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android-with-mta:5.3.1'

    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.29'

    // Shape 框架：https://github.com/getActivity/ShapeView
    implementation 'com.github.getActivity:ShapeView:6.0'

    // CameraX dependencies (first release for video is: "1.1.0-alpha10")
    def camerax_version = "1.4.2"
    // The following line is optional, as the core library is included indirectly by camera-camera2
    implementation("androidx.camera:camera-core:${camerax_version}") {
        transitive = true
        exclude group: 'androidx.lifecycle', module: 'lifecycle-common'
        exclude group: 'androidx.lifecycle', module: 'lifecycle-livedata'
        exclude group: 'androidx.annotation', module: 'annotation'
        exclude group: 'androidx.annotation', module: 'annotation-experimental'
        exclude group: 'androidx.core:core', module: 'core'
    }
    implementation("androidx.camera:camera-camera2:${camerax_version}") {
        transitive = true
        exclude group: 'androidx.lifecycle', module: 'lifecycle-common'
        exclude group: 'androidx.lifecycle', module: 'lifecycle-livedata'
        exclude group: 'androidx.annotation', module: 'annotation'
        exclude group: 'androidx.annotation', module: 'annotation-experimental'
        exclude group: 'androidx.core:core', module: 'core'
    }
    implementation("androidx.camera:camera-lifecycle:${camerax_version}") {
        transitive = true
        exclude group: 'androidx.lifecycle', module: 'lifecycle-common'
        exclude group: 'androidx.lifecycle', module: 'lifecycle-livedata'
        exclude group: 'androidx.annotation', module: 'annotation'
        exclude group: 'androidx.annotation', module: 'annotation-experimental'
        exclude group: 'androidx.core:core', module: 'core'
    }

    implementation("androidx.camera:camera-view:${camerax_version}") {
        transitive = true
        exclude group: 'androidx.lifecycle', module: 'lifecycle-common'
        exclude group: 'androidx.lifecycle', module: 'lifecycle-livedata'
        exclude group: 'androidx.annotation', module: 'annotation'
        exclude group: 'androidx.annotation', module: 'annotation-experimental'
        exclude group: 'androidx.core:core', module: 'core'
    }

    implementation("androidx.camera:camera-video:${camerax_version}") {
        transitive = true
        exclude group: 'androidx.lifecycle', module: 'lifecycle-common'
        exclude group: 'androidx.lifecycle', module: 'lifecycle-livedata'
        exclude group: 'androidx.annotation', module: 'annotation'
        exclude group: 'androidx.annotation', module: 'annotation-experimental'
        exclude group: 'androidx.core:core', module: 'core'

    }

    implementation("androidx.core:core-splashscreen:1.1.0-alpha02") {
        transitive = true
        exclude group: 'androidx.annotation', module: 'annotation'
    }

    implementation("com.vanniktech:android-image-cropper:4.5.0")


    implementation 'com.github.zhpanvip:bannerviewpager:3.5.12'

//    implementation 'com.github.JessYanCoding:AndroidAutoSize:v1.2.1'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'

    implementation 'com.tencent.tav:libpag:4.4.31'

    implementation 'com.github.getActivity:Toaster:12.6'

    // ExoPlayer for video playback
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'

//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.14'

}

// https://stackoverflow.com/questions/65380359/lomboks-access-to-jdk-compilers-internal-packages-incompatible-with-java-16

// enable internal JDK API access at runtime
tasks.withType(JavaCompile).configureEach {
    options.fork = true
    options.forkOptions.jvmArgs += [
//            # essential for butterknife
'--add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED',

//    # if you need Glide or LomBok
//    # these may not be the exact list, but it works for me
'--add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED',
'--add-opens=jdk.compiler/com.sun.tools.javac.jvm=ALL-UNNAMED',
    ]
}

//
//apply plugin: 'com.mob.sdk'
//// 插件缓存路径: ~/.gradle/cache/modules-2/files-2.1/com.mob./MobSDK-Impl
//
//MobSDK {
//    appKey "b965cbc8cb80"
//    appSecret "091ca0f8f696100e3c13f6e52663a95c"
//    permissions {
//        exclude "android.permission.QUERY_ALL_PACKAGES", "WRITE_EXTERNAL_STORAGE", "READ_PHONE_STATE"
//    }
//    ShareSDK {
//        devInfo {
////            SinaWeibo {
////                appKey "3522343557"
////                appSecret "03bf16baa93170e461f1320cca14b3cf"
////                callbackUri "http://www.totwoo.cn"
////                shareByAppClient true
////            }
//            Facebook {
//                appKey "2165854100108506"
//                appSecret "5340a60744ad78fbe3e3941c5bc37706"
//                callbackUri "http://www.totwoo.cn"
//                shareByAppClient true
//            }
//            Twitter {
//                appKey "*************************"
//                appSecret "ZJRqMJZRLXGskUBxdq2TwUNsLGMUtVKozUzWvsvt5K3phhkD6Q"
//                callbackUri "http://www.totwoo.cn"
//                shareByAppClient true
//            }
//
//            Wechat {
//                appId "wx7e9b33b41b70a0ce"
//                appSecret "968aef4771d506dac661766867473f5f"
//                shareByAppClient true
//                bypassApproval false
//            }
//            WechatMoments {
//                appId "wx7e9b33b41b70a0ce"
//                appSecret "968aef4771d506dac661766867473f5f"
//                shareByAppClient true
//                bypassApproval false
//            }
//            QQ {
//                appId "1104740113"
//                appKey "3TL9XM53VZOkZzIM"
//                shareByAppClient true
//                bypassApproval false
//            }
//            QZone {
//                appId "1104740113"
//                appKey "3TL9XM53VZOkZzIM"
//                shareByAppClient true
//                bypassApproval false
//            }
//        }
//    }
//}