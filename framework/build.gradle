plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lintOptions {
        abortOnError false
    }
    namespace 'com.etone.framework'

    publishing {
        singleVariant('release') {
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly project(":totwoo_library")
}

publishing {
    publications {
        maven(MavenPublication) {
            groupId = 'com.totwoo'
            artifactId = 'framework'
            version = "1.0.0-${new Date().format('yyyyMMdd-HHmm')}"

            afterEvaluate {
                from components.release
            }
        }
    }
    repositories {
        maven {
            name = 'localRepo'
            url = "${rootProject.projectDir}/repo"
        }
    }
}

task makeJar(type: Copy) {
    delete 'build/libs/etoneFramework.jar'
    from('build/intermediates/bundles/release/')
    into('build/libs/')
    include('classes.jar')
    rename ('classes.jar', 'etoneFramework.jar')
}
makeJar.dependsOn(build)