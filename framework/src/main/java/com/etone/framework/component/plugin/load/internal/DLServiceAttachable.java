/*
 * Copyright (C) 2014 singwhatiwanna(任玉刚) <<EMAIL>>
 *
 * collaborator:田啸,宋思宇,Mr.Simple
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.etone.framework.component.plugin.load.internal;

import com.etone.framework.component.plugin.load.DLServicePlugin;

/**
 * <AUTHOR>
 */
public interface DLServiceAttachable {
    
    public void attach(DLServicePlugin remoteService, DLPluginManager pluginManager);
}
