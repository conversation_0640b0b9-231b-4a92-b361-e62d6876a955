apply plugin: 'com.android.application'
//apply plugin: 'com.getkeepsafe.dexcount'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        applicationId "com.totwoo.totwoo"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.versionCode
        versionName rootProject.versionName
        multiDexEnabled true

        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lintOptions {
        abortOnError false
        checkReleaseBuilds false
    }
    lintOptions {
        abortOnError false
        checkReleaseBuilds false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':totwoo_library')


    api 'com.google.android.gms:play-services-wearable:7.8.87'
    api 'com.google.android:wearable:2.5.0'
    api 'me.kaelaela:verticalviewpager:1.0.0@aar'

    // butterkinfe
    api 'com.jakewharton:butterknife:10.1.0'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.1.0'

    // EventBus 消息机制
    api 'org.greenrobot:eventbus:3.0.0'
}
configurations.all {
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion '25.3.0'
            }
        }
    }
}
