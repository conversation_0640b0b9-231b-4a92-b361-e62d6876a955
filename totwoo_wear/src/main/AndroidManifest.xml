<?xml version="1.0" encoding="utf-8"?>
<manifest package="com.totwoo.totwoo"
          xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-feature android:name="android.hardware.type.watch"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- 极光相关 -->
    <application
        android:allowBackup="true"
        android:icon="@mipmap/totwoo_icon"
        android:label="@string/app_name"
        android:name=".TotwooWearApplication"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.DeviceDefault">
        <uses-library
            android:name="com.google.android.wearable"
            android:required="false"/>


        <activity
            android:name=".ui.activitys.HomeActivity"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.DeviceDefault.Light"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
    </application>
</manifest>