package com.totwoo.totwoo.service;

import com.google.android.gms.wearable.MessageEvent;
import com.google.gson.Gson;
import com.totwoo.library.util.LogUtils;
import com.totwoo.library.util.PublicConstant;
import com.totwoo.totwoo.bean.MessageBean;
import com.totwoo.totwoo.util.TeleportClient;
import com.totwoo.totwoo.util.TeleportService;

import org.greenrobot.eventbus.EventBus;

public class WearClientService extends TeleportService {
    public WearClientService() {
    }

    private TeleportClient mTeleportClient;

    @Override
    public void onCreate() {
        super.onCreate();

        //The quick way is to use setOnGetMessageTask, and set a new task

        mTeleportClient =TeleportClient.getInstance();

        setOnGetMessageTask(new StartActivityTask());
        //alternatively, you can use the Builder to create new Tasks
        /*
        setOnGetMessageTaskBuilder(new OnGetMessageTask.Builder() {
            @Override
            public OnGetMessageTask build() {
                return new OnGetMessageTask() {
                    @Override
                    protected void onPostExecute(String path) {
                        if (path.equals("startActivity")){

                            Intent startIntent = new Intent(getBaseContext(), WearActivity.class);
                            startIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(startIntent);
                        }

                    }
                };
            }
        });
        */

    }


    //Task that shows the path of a received message
    public class StartActivityTask extends TeleportService.OnGetMessageTask {

        @Override
        protected void onPostExecute(MessageEvent messageEvent) {
            switch (messageEvent.getPath()){
                case PublicConstant.WEAR_SEDN_TOTWOO_FAILURE://totwoo发送失败
                    break;
                case PublicConstant.WEAR_SEND_TOTWOO_SUCCESS://totwoo发送成功
                    String  beanJson = new String(messageEvent.getData());
                    if (beanJson.isEmpty()){
                        return;
                    }
                    Gson gson = new Gson();
                    MessageBean bean = gson.fromJson(beanJson,MessageBean.class);
                    //发送到totwooholder
                    EventBus.getDefault().post(bean);
                    break;
            }
            LogUtils.i("getMessage");
            //let's reset the task (otherwise it will be executed only once)

            setOnGetMessageTask(new StartActivityTask());
            }

    }

}
