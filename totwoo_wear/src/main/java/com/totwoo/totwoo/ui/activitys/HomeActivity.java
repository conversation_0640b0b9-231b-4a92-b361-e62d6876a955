package com.totwoo.totwoo.ui.activitys;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import androidx.viewpager.widget.ViewPager;
import android.support.wearable.activity.WearableActivity;
import android.support.wearable.view.ProgressSpinner;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.gms.wearable.Asset;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.DataMapItem;
import com.google.android.gms.wearable.MessageEvent;
import com.google.gson.Gson;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;

import com.totwoo.library.util.PublicConstant;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.adapter.HomeViewPagerAdapter;
import com.totwoo.totwoo.bean.MessageBean;
import com.totwoo.totwoo.ui.holder.BaseHolder;
import com.totwoo.totwoo.ui.holder.ConstellationHolder;
import com.totwoo.totwoo.ui.holder.TotwooHolder;
import com.totwoo.totwoo.util.PreferencesUtils;
import com.totwoo.totwoo.util.TeleportClient;
import com.totwoo.totwoo.widget.CircleIndicatorView;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

public class HomeActivity extends WearableActivity {

    public static final String USER_ICON_NAME = "user_icon";

    public static final String PAIRED_ICON_NAME = "paired_icon";

    public static final String USER_LOGING_STATE = "user_login_state";

    @BindView(R.id.page_indicator)
    CircleIndicatorView page_indicator;

    @BindView(R.id.home_viewpager)
    ViewPager home_viewpager;

    @BindView(R.id.home_loding_data_ll)
    LinearLayout mLondingDataLL;

    @BindView(R.id.home_loding_data_tv)
    TextView mLodingDataTv;

    @BindView(R.id.home_loding_data_prg)
    ProgressSpinner mLodingDataPrg;

    private HomeViewPagerAdapter mViewPagerAdapter;

    private TeleportClient mTeleportClient;

    private boolean indicatorDisplayStatus = true;

    Bitmap userIcon;

    Bitmap pairedIcon;

    private boolean initAdapter;

    private Handler mHandler = new Handler();

    Runnable runnable = new Runnable() {
        @Override
        public void run() {
            //显示状态等于ture
            if (indicatorDisplayStatus) {
                Animation anim = AnimationUtils.loadAnimation(HomeActivity.this, R.anim.anim_alpha_1_0);
                page_indicator.startAnimation(anim);
                indicatorDisplayStatus = false;
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);
        ButterKnife.bind(this);
        mTeleportClient = TeleportClient.getInstance();
    }

    Action1 lodindDataFailure;
    Subscription subscribe;
    Observable<Long> longObservable;

    private void syncData() {
        longObservable = Observable.timer(10, TimeUnit.SECONDS, AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.newThread());

        lodindDataFailure = new Action1<Long>() {
            @Override
            public void call(Long aLong) {
                mLodingDataTv.setText(R.string.home_loding_data_failure);
                lodingDataFailure();
            }
        };

        subscribe = longObservable.subscribe(lodindDataFailure);

        mTeleportClient.setOnGetMessageTask(new ShowToastFromOnGetMessageTask());
        mTeleportClient.setOnSyncDataItemCallback(new TeleportClient.OnSyncDataItemCallback() {
            @Override
            public void onDataSync(DataMapItem dataMapItem) {
                LogUtils.w("onDataSync");
                DataMap dataMap = dataMapItem.getDataMap();
                subscribe.unsubscribe();
                //手持设备所有数据同步
                switch (dataMapItem.getUri().getPath()) {

                    case PublicConstant.SYNC_DATA:
                        ArrayList<String> dataList = dataMap.getStringArrayList(PublicConstant.WEAR_LIST_DATA);

                        Boolean isLogin = Boolean.parseBoolean(dataList.get(0));
                        PreferencesUtils.put(HomeActivity.this, USER_LOGING_STATE, isLogin);
                        if (isLogin) {
                            String pairedId = dataList.get(1);
                            String totwooListJson = dataList.get(2);
                            String homePage = dataList.get(4);
                            String todayConstellation = dataList.get(5);
                            String pairedName = dataList.get(6);
                            String userName = dataList.get(7);

                            Asset userIconAsset = dataMap.getAsset(PublicConstant.USER_ICON_KEY);
                            userIcon = mTeleportClient.loadBitmapFromAsset(userIconAsset);
                            if (userIcon != null) {
                                String userIconPath = BitmapHelper.bitmaptoString(userIcon);
                                PreferencesUtils.put(HomeActivity.this, USER_ICON_NAME, userIconPath);
                            }
                            Asset pairedIconAsset = dataMap.getAsset(PublicConstant.PAIRED_ICON_KEY);
                            if (pairedIconAsset != null) {
                                pairedIcon = mTeleportClient.loadBitmapFromAsset(pairedIconAsset);
                                String pairedIconPath = BitmapHelper.bitmaptoString(pairedIcon);
                                PreferencesUtils.put(HomeActivity.this, PAIRED_ICON_NAME, pairedIconPath);
                            }

                            PreferencesUtils.put(HomeActivity.this, PublicConstant.PAIRED_ID_KEY, pairedId);
                            PreferencesUtils.put(HomeActivity.this, PublicConstant.TOTWOO_MESSAGE_LIST_KEY, totwooListJson);
                            PreferencesUtils.put(HomeActivity.this, PublicConstant.HOME_PAGE_KEY, homePage);
                            PreferencesUtils.put(HomeActivity.this, PublicConstant.TODAY_CONSTELLATION_KEY, todayConstellation);
                            PreferencesUtils.put(HomeActivity.this, PublicConstant.PAIRED_NICK_NAME, pairedName);
                            PreferencesUtils.put(HomeActivity.this, PublicConstant.USER_NAME, userName);

                        }
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                init();
                            }
                        });
                        break;
                }
            }
        });

        //发送消息到手持设备获取数据
        mTeleportClient.sendMessage(PublicConstant.WEAR_SYNC_DATA, null);
        LogUtils.w("sendsyncDataMessage");
        mTeleportClient.setOnGetMessageCallback(new TeleportClient.OnGetMessageCallback() {
            @Override
            public void onCallback(MessageEvent messageEvent) {
                Observable.from(new MessageEvent[]{messageEvent})
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Action1<MessageEvent>() {
                            @Override
                            public void call(MessageEvent messageEvent) {
                                switch (messageEvent.getPath()) {
                                    case PublicConstant.WEAR_SEDN_TOTWOO_FAILURE://totwoo发送失败
//                                        Toast.makeText(HomeActivity.this, R.string.totwoo_send_failure, Toast.LENGTH_LONG).show();
                                        break;
                                    case PublicConstant.WEAR_SEND_TOTWOO_SUCCESS://totwoo发送成功
                                        String beanJson = new String(messageEvent.getData());
                                        if (beanJson.isEmpty()) {
                                            return;
                                        }
                                        Gson gson = new Gson();
                                        MessageBean bean = gson.fromJson(beanJson, MessageBean.class);
                                        //发送到totwooholder
                                        EventBus.getDefault().post(bean);
                                        break;
                                    case PublicConstant.SYNC_DATA_COMPLETE://数据同步完成 要是之前没有任何响应 就说明数据源没有改动 使用缓存即可
                                        init();
                                        break;
                                }
                            }
                        });
                LogUtils.i("getMessage");
            }
        });

    }

    private void lodingDataFailure() {
        mLodingDataPrg.setVisibility(View.GONE);
        mLodingDataTv.setClickable(true);
        mLodingDataTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mLodingDataPrg.setVisibility(View.VISIBLE);
                mLodingDataTv.setText(R.string.home_loding_data_text);
                mLodingDataTv.setClickable(false);
                subscribe = longObservable.subscribe(lodindDataFailure);
                mTeleportClient.sendMessage(PublicConstant.WEAR_SYNC_DATA, null);
            }
        });
    }

    private void init() {


        subscribe.unsubscribe();
        boolean aBoolean = PreferencesUtils.getBoolean(this, USER_LOGING_STATE, false);
        if (!aBoolean) {
            mLodingDataTv.setText(R.string.loding_failure_text);
            lodingDataFailure();
            return;
        }

        if (initAdapter) {
            return;
        }
        initAdapter = true;
        mLondingDataLL.setVisibility(View.GONE);
        List<BaseHolder> holders = new ArrayList<>();
        holders.add(TotwooHolder.create(home_viewpager, userIcon, pairedIcon));
        //是中文环境才显示星座运势

        String pairedId = PreferencesUtils.getString(this, PublicConstant.PAIRED_ID_KEY, "");
        if (Apputils.systemLanguageIsChinese(this)) {
            if (pairedId.isEmpty()) {
                holders.add(0, ConstellationHolder.create(home_viewpager));
            } else {
                holders.add(ConstellationHolder.create(home_viewpager));
            }
        }
        mViewPagerAdapter = new HomeViewPagerAdapter(holders);

        home_viewpager.setAdapter(mViewPagerAdapter);
        //给顶部小圆点设置相关联的viewpager
        page_indicator.attachViewPager(home_viewpager);
        page_indicator.bringToFront();

        home_viewpager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {

            }

            @Override
            public void onPageScrollStateChanged(int state) {
                switch (state) {
                    case ViewPager.SCROLL_STATE_IDLE:
                        page_indicator.removeCallbacks(runnable);
                        page_indicator.postDelayed(runnable, 2000);
                        break;
                    case ViewPager.SCROLL_STATE_DRAGGING:
                        //显示状态等于ture
                        page_indicator.removeCallbacks(runnable);
                        if (indicatorDisplayStatus) {
                            return;
                        }

                        Animation anim = AnimationUtils.loadAnimation(HomeActivity.this, R.anim.anim_alpha_0_1);
                        page_indicator.startAnimation(anim);
                        indicatorDisplayStatus = true;
                        break;
                }
            }
        });
        page_indicator.postDelayed(runnable, 2000);
    }


    @Override
    protected void onStart() {
        super.onStart();
        mTeleportClient.connect();
        syncData();
    }


    @Override
    protected void onStop() {
        mTeleportClient.disconnect();
        super.onStop();
    }


    @Override
    public void onEnterAmbient(Bundle ambientDetails) {
        super.onEnterAmbient(ambientDetails);
        updateDisplay();
    }

    @Override
    public void onUpdateAmbient() {
        super.onUpdateAmbient();
        updateDisplay();
    }

    @Override
    public void onExitAmbient() {
        updateDisplay();
        super.onExitAmbient();
    }

    private void updateDisplay() {

    }

    public class ShowToastFromOnGetMessageTask extends TeleportClient.OnGetMessageTask {

        @Override
        protected void onPostExecute(String path) {

            Toast.makeText(getApplicationContext(), "Message - " + path, Toast.LENGTH_SHORT).show();

            //let's reset the task (otherwise it will be executed only once)
            mTeleportClient.setOnGetMessageTask(new ShowToastFromOnGetMessageTask());
        }
    }
}
