package com.totwoo.totwoo.ui.holder;

import androidx.viewpager.widget.PagerAdapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.google.gson.Gson;

import com.totwoo.library.util.PublicConstant;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.ConstellationDataModel;
import com.totwoo.totwoo.bean.HomeConstellationBean;
import com.totwoo.totwoo.util.PreferencesUtils;
import com.totwoo.totwoo.widget.ViewPagerVertical;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;


/**
 * Created by huang<PERSON>owei on 16/6/21.
 */

public class ConstellationHolder extends BaseHolder {

    int[] numResIds = new int[]{R.mipmap.zero, R.mipmap.one, R.mipmap.two, R.mipmap.three
            , R.mipmap.four, R.mipmap.fives, R.mipmap.six,
            R.mipmap.seven, R.mipmap.eight, R.mipmap.nine};

    private String[] Constellation = new String[]{"金牛座", "处女座", "摩羯座", "天蝎座", "双子座", "水瓶座", "狮子座", "白羊座", "巨蟹座", "射手座", "天秤座", "双鱼座"};

    private int[] mIconIds = new int[]{
            R.mipmap.jinniu,
            R.mipmap.chunv, R.mipmap.mojie,
            R.mipmap.tianxie,
            R.mipmap.shuangzi, R.mipmap.shuiping,
            R.mipmap.shizi, R.mipmap.baiyang,
            R.mipmap.juxie, R.mipmap.sheshou,
            R.mipmap.tianping, R.mipmap.shuangyu
    };
    private Map<String, Integer> mIcon = new HashMap<>();


    @BindView(R.id.home_vp_itme_vvp)
    ViewPagerVertical mVerticalViewPager;

    private HomeConstellationBean mHomeConstellationBean;

    private ConstellationDataModel mConstellationDataModel;

    private Gson mGson;

    public static ConstellationHolder create(ViewGroup parent) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.home_viewpager_item, parent, false);
        return new ConstellationHolder(view);
    }

    private ConstellationHolder(View itemView) {
        super(itemView);
        ButterKnife.bind(this, itemView);
        init();

    }

    private void init() {
        mGson = new Gson();

        String homeConstellationBeanJson = PreferencesUtils.getString(mContext, PublicConstant.HOME_PAGE_KEY, "");
        if (!homeConstellationBeanJson.isEmpty()) {
            mHomeConstellationBean = mGson.fromJson(homeConstellationBeanJson, HomeConstellationBean.class);
        }
        String conDataModel = PreferencesUtils.getString(mContext, PublicConstant.TODAY_CONSTELLATION_KEY, "");
        if (!conDataModel.isEmpty()) {
            mConstellationDataModel = mGson.fromJson(conDataModel, ConstellationDataModel.class);
        }
        if (mHomeConstellationBean == null) {
            return;
        }
        ArrayList<View> views = new ArrayList<>();
        LayoutInflater inflater = LayoutInflater.from(mContext);

        RelativeLayout con_info_rl = (RelativeLayout) inflater.inflate(R.layout.home_vp_constellation_info, null);


        RatingBar ratingBar = (RatingBar) con_info_rl.findViewById(R.id.home_constellation_ratingBar);
        ratingBar.setRating(Float.parseFloat(mHomeConstellationBean.getConstellation().getAll()));

        TextView con_info_tv = (TextView) con_info_rl.findViewById(R.id.home_con_info_tv);
        con_info_tv.setText(mHomeConstellationBean.getConstellation().getSummery());

        views.add(con_info_rl);
        //颜色
        RelativeLayout conLuckyColorRL = (RelativeLayout) inflater.inflate(R.layout.home_vp_con_single_info, null);

        TextView title = (TextView) conLuckyColorRL.findViewById(R.id.title_tv);
        TextView roundTv = (TextView) conLuckyColorRL.findViewById(R.id.round_tv);

        title.setText(R.string.lucky_color);
        if (mConstellationDataModel != null) {
            roundTv.setText(mConstellationDataModel.getLuckyColor());
        }
        views.add(conLuckyColorRL);
        //数字
        RelativeLayout conLuckyNumberRL = (RelativeLayout) inflater.inflate(R.layout.home_vp_con_single_info, null);

        title = (TextView) conLuckyNumberRL.findViewById(R.id.title_tv);
        roundTv = (TextView) conLuckyNumberRL.findViewById(R.id.round_tv);
        ImageView roundIv = (ImageView) conLuckyNumberRL.findViewById(R.id.con_single_info_iv);
        roundIv.setVisibility(View.INVISIBLE);
        title.setText(R.string.lucky_number);
        if (mConstellationDataModel != null) {
            roundTv.setBackgroundResource(numResIds[mConstellationDataModel.getLuckyNumber()]);
        }

        views.add(conLuckyNumberRL);
        //速配星座
        RelativeLayout conFoConRL = (RelativeLayout) inflater.inflate(R.layout.home_vp_con_single_info, null);

        title = (TextView) conFoConRL.findViewById(R.id.title_tv);
        title.setText(R.string.fo_con_title);
        roundTv = (TextView) conFoConRL.findViewById(R.id.round_tv);
        roundIv = (ImageView) conFoConRL.findViewById(R.id.con_single_info_iv);
        roundIv.setVisibility(View.INVISIBLE);
        TextView conName = (TextView) conFoConRL.findViewById(R.id.con_fo_name);
        conName.setVisibility(View.VISIBLE);


        if (mConstellationDataModel != null) {
            conName.setText(mConstellationDataModel.getOFriend());
            for (int i = 0; i < Constellation.length; i++) {
                if (mConstellationDataModel.getOFriend().equals(Constellation[i])) {
                    roundTv.setBackgroundResource(mIconIds[i]);
                    break;
                }
            }
        }

        views.add(conFoConRL);

        mVerticalViewPager.setAdapter(new ConstellationViewpagerAdapter(views));

    }

    private class ConstellationViewpagerAdapter extends PagerAdapter {

        ArrayList<View> views = new ArrayList<>();

        public ConstellationViewpagerAdapter(ArrayList<View> views) {
            this.views = views;
        }

        @Override
        public int getCount() {
            if (mHomeConstellationBean != null) {
                return 4;
            }
            return 0;

        }

        public ArrayList<View> getViews() {
            return views;
        }

        public void setViews(ArrayList<View> views) {
            this.views = views;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {

            container.addView(views.get(position));
            return views.get(position);
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView(views.get(position));
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }
    }
}
