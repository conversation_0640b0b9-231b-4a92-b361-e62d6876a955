package com.totwoo.totwoo.ui.holder;

import android.graphics.Bitmap;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.support.wearable.view.ProgressSpinner;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Scroller;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.LogUtils;
import com.totwoo.library.util.PublicConstant;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.MessageBean;
import com.totwoo.totwoo.ui.activitys.HomeActivity;
import com.totwoo.totwoo.util.DateUtil;
import com.totwoo.totwoo.util.PreferencesUtils;
import com.totwoo.totwoo.util.TeleportClient;
import com.totwoo.totwoo.widget.FixedSpeedScroller;
import com.totwoo.totwoo.widget.ViewPagerVertical;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;


/**
 * Created by huanggaowei on 16/6/21.
 */

public class TotwooHolder extends BaseHolder {

    @BindView(R.id.home_vp_itme_vvp)
    ViewPagerVertical mVerticalViewPager;

    @BindView(R.id.first_send_totwoo_iv)
    ImageView first_send_totwoo_iv;

    @BindView(R.id.first_send_totwoo_tv)
    TextView first_send_totwoo_tv;

    @BindView(R.id.transparent_view)
    View transparent_view;

    @BindView(R.id.totwoo_holder_nopaired_tv)
    TextView totwoo_holder_nopaired_tv;

    public Map<Integer, View> views;

    private List<MessageBean> mTotwooMessageList;

    private String userName;

    private String pairedName;

    Typeface typeface;

    Typeface totowoTypeface;

    private int oldPosition = 0;

    private int state;

    private int oldState;

    private boolean isScroll;

    Bitmap userIcon;

    Bitmap pairedIcon;

    FixedSpeedScroller scroller;

    Scroller systemScroller;

    Field field;

    private boolean isTimeOut;

    Subscription mTimeOutSub;

    private int sendTotwooIndex;

    TextView sendStatusTv;

    public static TotwooHolder create(ViewGroup parent, Bitmap userIcon, Bitmap pairedIcon) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.home_viewpager_item, parent, false);
        return new TotwooHolder(view, userIcon, pairedIcon);
    }

    private TotwooHolder(View itemView, Bitmap userIcon, Bitmap pairedIcon) {
        super(itemView);
//        this.userIcon=userIcon;
//        this.pairedIcon=pairedIcon;
        EventBus.getDefault().register(this);
        ButterKnife.bind(this, itemView);
        init();
    }


    private void init() {
        Gson gson = new Gson();
        String totwooMessagelist = PreferencesUtils.getString(mContext, PublicConstant.TOTWOO_MESSAGE_LIST_KEY, "");
        mTotwooMessageList = gson.fromJson(totwooMessagelist, new TypeToken<List<MessageBean>>() {
        }.getType());
        String pairedId = PreferencesUtils.getString(mContext, PublicConstant.PAIRED_ID_KEY, "");
        if (pairedId.isEmpty()) {
            totwoo_holder_nopaired_tv.setVisibility(View.VISIBLE);
            totwoo_holder_nopaired_tv.setText(R.string.totwoo_holder_nopaired);
            return;
        }
        userIcon = BitmapHelper.stringtoBitmap(PreferencesUtils.getString(mContext, HomeActivity.USER_ICON_NAME, ""));
        pairedIcon = BitmapHelper.stringtoBitmap(PreferencesUtils.getString(mContext, HomeActivity.PAIRED_ICON_NAME, ""));


        //如果没有totwoo消息就显示发送totwoo页面
        if (mTotwooMessageList != null && mTotwooMessageList.size() > 0) {
            initViewpager();
        } else {
            first_send_totwoo_iv.setVisibility(View.VISIBLE);
            first_send_totwoo_tv.setVisibility(View.VISIBLE);
            transparent_view.setVisibility(View.VISIBLE);
            mView.setBackground(new BitmapDrawable(userIcon));
            first_send_totwoo_iv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TeleportClient.getInstance().sendMessage(PublicConstant.WEAR_SEND_TOTWOO, null);
                }
            });
        }
    }

    private void initViewpager() {
        //用户名称
        userName = PreferencesUtils.getString(mContext, PublicConstant.USER_NAME, "");
        //配对人名称
        pairedName = PreferencesUtils.getString(mContext, PublicConstant.PAIRED_NICK_NAME, "");
        //数据集合倒序
        Collections.reverse(mTotwooMessageList);

        mVerticalViewPager.setAdapter(new TotwooViewpagerAdapter());
        //显示最后一个item
        mVerticalViewPager.setCurrentItem(mTotwooMessageList.size() - 1);

        totowoTypeface = ResourcesCompat.getFont(mContext, R.font.segoeuil);


        mVerticalViewPager.getViewTreeObserver().addOnScrollChangedListener(new ViewTreeObserver.OnScrollChangedListener() {
            @Override
            public void onScrollChanged() {
                isScroll = true;
            }
        });

//        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
//            mVerticalViewPager.setOnScrollChangeListener(new View.OnScrollChangeListener() {
//                @Override
//                public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
//                    isScroll = true;
//                }
//            });
//        }
        mVerticalViewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                doAnimation();
            }

            @Override
            public void onPageSelected(int position) {

                oldPosition = position;

                LogUtils.i("onPageSelected:" + position);

            }

            @Override
            public void onPageScrollStateChanged(int state) {
                TotwooHolder.this.state = state;
                doAnimation();
                LogUtils.i("onPageScrollStateChanged: " + state);
            }
        });
        mVerticalViewPager.setOffscreenPageLimit(5);

        try {
            field = ViewPagerVertical.class.getDeclaredField("mScroller");
            Field systemField = ViewPagerVertical.class.getDeclaredField("mScroller");
            field.setAccessible(true);
            systemField.setAccessible(true);
            scroller = new FixedSpeedScroller(mVerticalViewPager.getContext(),
                    new AccelerateInterpolator());
            scroller.setmDuration(400);
            systemScroller = (Scroller) systemField.get(mVerticalViewPager);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void doAnimation() {
        if (oldState == state || !isScroll) {
            return;
        }

        oldState = state;
        final ImageView sendTotwooIv = (ImageView) views.get(oldPosition);
        Animation.AnimationListener animationListener = new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                sendTotwooIv.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        };

        switch (state) {
            case ViewPager.SCROLL_STATE_IDLE:
                isScroll = false;
                if (sendTotwooIv != null) {
                    sendTotwooIv.setVisibility(View.VISIBLE);
                    Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.anim_scale_0_1);
                    sendTotwooIv.startAnimation(animation);
                }

                try {
                    field.set(mVerticalViewPager, systemScroller);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
                break;
            case ViewPager.SCROLL_STATE_DRAGGING:
                if (sendTotwooIv != null) {
                    Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.anim_scale_1_0);
                    animation.setAnimationListener(animationListener);
                    sendTotwooIv.startAnimation(animation);
                }
                break;
        }
    }

    private class TotwooViewpagerAdapter extends PagerAdapter {
        @Override
        public int getCount() {
            if (views == null) {
                views = new HashMap<>();
            }
            if (mTotwooMessageList != null) {
                return mTotwooMessageList.size();
            }
            return 0;
        }

        @Override
        public Object instantiateItem(ViewGroup container, final int position) {
            ViewGroup vg = null;
            if (mTotwooMessageList.get(position) != null) {
                vg = (RelativeLayout) View.inflate(mContext, R.layout.home_vp_totwoo_item, null);
                ImageView iocn = (ImageView) vg.findViewById(R.id.totwoo_vp_item_portrait_iv);
                TextView nameTv = (TextView) vg.findViewById(R.id.totwoo_vp_item_name_tv);
                TextView timeTv = (TextView) vg.findViewById(R.id.totwoo_vp_item_time_tv);
                TextView totwooTextTv = (TextView) vg.findViewById(R.id.totwoo_text_tv);
                ImageView sendTotwooIv = (ImageView) vg.findViewById(R.id.totwoo_send_iv);

                if (position == mVerticalViewPager.getCurrentItem()) {
                    sendTotwooIv.setVisibility(View.VISIBLE);
                }
                nameTv.setTypeface(typeface);
                timeTv.setTypeface(typeface);
                totwooTextTv.setTypeface(totowoTypeface);
//            BitmapHelper.display(iocn,pairedIcon);
                switch (mTotwooMessageList.get(position).getMsgType()) {
                    case MessageBean.MSG_TYPE_TOTWOO_IN:
                        nameTv.setText(pairedName);
                        if (pairedIcon != null) {
                            iocn.setImageBitmap(pairedIcon);
                        }
                        break;
                    case MessageBean.MSG_TYPE_TOTWOO_OUT:
                        nameTv.setText(userName);
                        if (userIcon != null) {
                            iocn.setImageBitmap(userIcon);
                        }
                        break;
                }
                timeTv.setText(DateUtil.getDateToString("HH:mm", mTotwooMessageList.get(position).getSendTime()));

                sendTotwooIv.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        isTimeOut = false;
                        TeleportClient.getInstance().sendMessage(PublicConstant.WEAR_SEND_TOTWOO, null);
//                        Toast.makeText(mContext, R.string.totwoo_sending_toast, Toast.LENGTH_LONG).show();
                        sendTotwooIndex = position + 1;
                        mTotwooMessageList.add(sendTotwooIndex, null);
                        mVerticalViewPager.getAdapter().notifyDataSetChanged();
                        mVerticalViewPager.setCurrentItem(sendTotwooIndex);

                        mTimeOutSub = Observable.timer(4, TimeUnit.SECONDS, Schedulers.newThread())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Action1<Long>() {
                                    @Override
                                    public void call(Long aLong) {
//                                        Toast.makeText(mContext, R.string.totwoo_send_failure, Toast.LENGTH_LONG).show();
                                        sendStatusTv.setText(R.string.totwoo_send_failure);
                                        isTimeOut = true;
                                        Observable.timer(3, TimeUnit.SECONDS, Schedulers.newThread())
                                                .observeOn(AndroidSchedulers.mainThread())
                                                .subscribe(new Action1<Long>() {
                                                    @Override
                                                    public void call(Long aLong) {
                                                        mVerticalViewPager.setCurrentItem(sendTotwooIndex - 1);
                                                        mTotwooMessageList.remove(sendTotwooIndex);
                                                        sendTotwooIndex = 0;
                                                        mVerticalViewPager.getAdapter().notifyDataSetChanged();
                                                    }
                                                });
                                    }
                                });
                    }
                });
                views.put(position, sendTotwooIv);
                container.addView(vg);
                LogUtils.i("viewpager", "instantiateItem:" + position);
            } else {
                vg = (ViewGroup) View.inflate(mContext, R.layout.send_totwoo_loading, null);
                sendStatusTv = (TextView) vg.findViewById(R.id.send_totwoo_loding_data_tv);
                ProgressSpinner sendTotwooPgs = (ProgressSpinner) vg.findViewById(R.id.send_totwoo_loding_data_prg);
                container.addView(vg);
            }
            return vg;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            LogUtils.i("viewpager", "destroyItem:" + position);
            views.remove(position);
            container.removeView((View) object);
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }
    }

    @Subscribe
    public void onEventMainThread(final MessageBean message) {
        mView.post(new Runnable() {
            @Override
            public void run() {
                if (isTimeOut) {
                    return;
                }
                if (mTimeOutSub != null)
                    mTimeOutSub.unsubscribe();

                if (mTotwooMessageList == null) {
                    mTotwooMessageList = new ArrayList<MessageBean>();
                }
                if (mTotwooMessageList.size() == 0) {
                    first_send_totwoo_iv.setVisibility(View.GONE);
                    first_send_totwoo_tv.setVisibility(View.GONE);
                    transparent_view.setVisibility(View.GONE);
                    mView.setBackgroundColor(mContext.getResources().getColor(R.color.text_color_black_important));
                    mTotwooMessageList.add(message);
                    initViewpager();
                } else {
                    if (mVerticalViewPager.getCurrentItem() == mTotwooMessageList.size() - 1) {
                        mVerticalViewPager.setCurrentItem(mTotwooMessageList.size() - 3);
                    }
                    if (sendTotwooIndex != 0) {
                        mTotwooMessageList.remove(sendTotwooIndex);
                        sendTotwooIndex = 0;
                    }
                    mTotwooMessageList.add(message);
                    (mVerticalViewPager.getAdapter()).notifyDataSetChanged();


                    try {
                        field.set(mVerticalViewPager, scroller);
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                    mVerticalViewPager.setCurrentItem(mTotwooMessageList.size() - 1);
                }
            }
        });
    }
}
