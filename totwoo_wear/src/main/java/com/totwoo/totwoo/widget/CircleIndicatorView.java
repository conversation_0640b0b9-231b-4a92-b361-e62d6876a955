package com.totwoo.totwoo.widget;

import android.content.Context;
import android.content.res.Resources;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import com.totwoo.totwoo.R;


public class CircleIndicatorView extends LinearLayout {

    private final int mIndicatorSize = dpToPx(4);
    private final int mIndicatorMargin = dpToPx(2);

    public int dpToPx(int dp) {
        float density = Resources.getSystem().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
    private ViewPager mViewPager;

    public CircleIndicatorView(Context context) {
        this(context,null);
    }

    public CircleIndicatorView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public CircleIndicatorView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    private void init() {

        setOrientation(HORIZONTAL);
    }

    public void attachViewPager(ViewPager viewPager) {
        detachViewPager();

        mViewPager = viewPager;
        mViewPager.addOnPageChangeListener(mOnPageChangeListener);

        PagerAdapter adapter = mViewPager.getAdapter();
        if (adapter == null) {
            throw new RuntimeException("Set a PagerAdapter in the ViewPager before attaching it");
        }

        updateViewIndicators(adapter.getCount());
        selectIndicatorAtPosition(viewPager.getCurrentItem(), false);
    }

    public void detachViewPager() {
        if (mViewPager != null) {
            mViewPager.removeOnPageChangeListener(mOnPageChangeListener);
            mViewPager = null;
        }
    }

    private void updateViewIndicators(int numIndicators) {
        removeAllViews();

        for (int i = 0; i < numIndicators; i++) {
            addView(makeIndicatorView());
        }
    }

    private View makeIndicatorView() {
        View view = new View(getContext());
        LayoutParams params =
                new LayoutParams(mIndicatorSize, mIndicatorSize);
        params.setMargins(mIndicatorMargin, mIndicatorMargin, mIndicatorMargin, mIndicatorMargin);
        view.setLayoutParams(params);
        view.setBackgroundResource(R.drawable.round_point_white);
        return view;
    }

    private void selectIndicatorAtPosition(int position, boolean animated) {
        for (int i = 0; i < getChildCount(); i++) {
            View child = getChildAt(i);
            if (i == position) {
                child.setBackgroundResource(R.drawable.round_point_white);
                scaleViewTo(child, 1.5f, animated);
            } else {
                child.setBackgroundResource(R.drawable.round_point_white);
                scaleViewTo(child, 1, animated);
            }
        }
    }

    private void scaleViewTo(View view, float scale, boolean animated) {
        if (view.getScaleX() == scale && view.getScaleY() == scale) {
            return; // nothing to do here!
        }

        if (animated) {
            view.animate()
                    .scaleX(scale).scaleY(scale)
                    .setDuration(200)
                    .start();
        } else {
            view.setScaleX(scale);
            view.setScaleY(scale);
        }
    }

    private ViewPager.OnPageChangeListener mOnPageChangeListener =
            new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset,
                                           int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {
                    selectIndicatorAtPosition(position, true);
                }

                @Override
                public void onPageScrollStateChanged(int state) {

                }
            };
}