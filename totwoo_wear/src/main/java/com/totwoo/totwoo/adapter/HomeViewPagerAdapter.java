package com.totwoo.totwoo.adapter;

import androidx.viewpager.widget.PagerAdapter;
import android.view.View;
import android.view.ViewGroup;

import com.totwoo.totwoo.ui.holder.BaseHolder;

import java.util.List;

/**
 * Created by huanggaowei on 16/6/14.
 */

public class HomeViewPagerAdapter  extends PagerAdapter {

    List<BaseHolder> mHolders ;

    public HomeViewPagerAdapter(List<BaseHolder> holders) {
        super();
        mHolders = holders;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        container.addView(mHolders.get(position).getView());
        return mHolders.get(position).getView();
    }

    @Override
    public int getCount() {
        return mHolders.size();
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
//            container.removeView(mViews.get(position));
//        super.destroyItem(container, position, object);
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view==object;
    }

}
