package com.totwoo.totwoo.util;

import android.net.Uri;

import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.api.PendingResult;
import com.google.android.gms.wearable.DataApi;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.DataMapItem;
import com.google.android.gms.wearable.PutDataMapRequest;
import com.google.android.gms.wearable.PutDataRequest;
import com.google.android.gms.wearable.Wearable;
import com.totwoo.totwoo.TotwooWearApplication;

import static android.R.attr.value;


/**
 * 设备数据同步工具类
 * Created by huanggaowei on 16/6/17.
 */

public class DeviceSyncDataUtil {

    //用户信息
    public  static  final String USER_PATH = "/USER";

    //登陆状态
    public  static  final  String LONGIN_STATUS = "LONGIN_STATUS";

    private GoogleApiClient mGoogleApiClient;

    private static DeviceSyncDataUtil DeviceSyncDataUtil;

    /**
     * Convenience singleton for apps using a process-wide EventBus instance.
     */
    public static DeviceSyncDataUtil getInstance() {
        if (DeviceSyncDataUtil == null) {
            synchronized (DeviceSyncDataUtil.class) {
                if (DeviceSyncDataUtil == null) {
                    DeviceSyncDataUtil = new DeviceSyncDataUtil();
                }
            }
        }
        return DeviceSyncDataUtil;
    }

    private DeviceSyncDataUtil() {
        init();
    }

    public void init() {
        mGoogleApiClient = new GoogleApiClient.Builder(TotwooWearApplication.mAppllicationContext)
                // Request access only to the Wearable API
                .addApi(Wearable.API)
                .build();
    }

    /**
     *
     *
     * @param key    必须以/开头
     */
    public PendingResult putData(String path, DataMap value) {
        if (path.indexOf("/") != 0) {
            throw new IllegalArgumentException(" key must start with \"/\"  ");
        }
        PutDataMapRequest putDataMapReq = PutDataMapRequest.create(path);
        putDataMapReq.getDataMap().putDataMap(path, value);
        PutDataRequest putDataReq = putDataMapReq.asPutDataRequest();
        PendingResult<DataApi.DataItemResult> pendingResult =
                Wearable.DataApi.putDataItem(mGoogleApiClient, putDataReq);
        return pendingResult;
    }

//    public DataMap  getData(String path){
//        Uri uri = PutDataMapRequest.create(path).getUri();
//        PendingResult<DataApi.DataItemResult> pendingResult = Wearable.DataApi.getDataItem(mGoogleApiClient, uri);
//
//        DataApi.DataItemResult dataItemResult = pendingResult.await();
//        DataMapItem dataMapItem = DataMapItem.fromDataItem(dataItemResult.getDataItem());
//        DataMap dataMap= dataMapItem.getDataMap().getDataMap(path);
//        return dataMap;
//    }
}
