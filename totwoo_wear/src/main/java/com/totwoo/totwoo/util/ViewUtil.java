package com.totwoo.totwoo.util;

import android.content.Context;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

public class ViewUtil {

	/**
	 * 获取控件的高度，如果获取的高度为0，则重新计算尺寸后再返回高度
	 *
	 * @param view
	 * @return
	 */
	public static int getViewMeasuredHeight(View view) {
		// int height = view.getMeasuredHeight();
		// if(0 < height){
		// return height;
		// }
		calcViewMeasure(view);
		return view.getMeasuredHeight();
	}

	/**
	 * 获取控件的宽度，如果获取的宽度为0，则重新计算尺寸后再返回宽度
	 *
	 * @param view
	 * @return
	 */
	public static int getViewMeasuredWidth(View view) {
		// int width = view.getMeasuredWidth();
		// if(0 < width){
		// return width;
		// }
		calcViewMeasure(view);
		return view.getMeasuredWidth();
	}

	/**
	 * 测量控件的尺寸
	 *
	 * @param view
	 */
	public static void calcViewMeasure(View view) {
		// int width =
		// View.MeasureSpec.makeMeasureSpec(0,View.MeasureSpec.UNSPECIFIED);
		// int height =
		// View.MeasureSpec.makeMeasureSpec(0,View.MeasureSpec.UNSPECIFIED);
		// view.measure(width,height);

		int width = View.MeasureSpec.makeMeasureSpec(0,
				View.MeasureSpec.UNSPECIFIED);
		int expandSpec = View.MeasureSpec.makeMeasureSpec(
				Integer.MAX_VALUE >> 2, View.MeasureSpec.AT_MOST);
		view.measure(width, expandSpec);
	}

	/**
	 * 展示视差的动画序列集合
	 */
	public static void showParallaxAnim(Context context, View[] views,
			int delay, int animRes, Animation.AnimationListener listener) {
		if (views == null) {
			return;
		}

		Animation anim;
		for (int i = 0; i < views.length; i++) {
			anim = AnimationUtils.loadAnimation(context, animRes);
			anim.setStartOffset(delay * i);
			views[i].startAnimation(anim);

			if (i == views.length - 1 && listener != null) {
				anim.setAnimationListener(listener);
			}
		}
	}
}
