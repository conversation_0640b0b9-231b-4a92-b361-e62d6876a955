package com.totwoo.totwoo.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class DateUtil {

	private static SimpleDateFormat sf = null;

	//"yyyy/MM/dd HH:mm:ss"

	/**
	 * 将时间戳转为字符串
	 * 
	 * @param format
	 * @param time
	 * @return
	 */
	public static String getDateToString(String format, long time) {
		Date d = new Date(time);
		sf = new SimpleDateFormat(format);
		return sf.format(d);
	}

	/**
	 * 将UTC时间戳转为字符串
	 * 
	 * @param format
	 * @param time
	 * @return
	 */
	public static String getUTCDateToString(String format, long time) {
		Date d = new Date(time);
		sf = new SimpleDateFormat(format, Locale.CHINA);
		sf.setTimeZone(TimeZone.getTimeZone("UTC"));
		return sf.format(d);
	}

	/**
	 * 
	 * 将字符串转化为UTC时间戳
	 * 
	 * @param format
	 * @param time
	 * @return
	 */
	public static long getUTCStringToDate(String format, String time) {
		sf = new SimpleDateFormat(format, Locale.CHINA);
		sf.setTimeZone(TimeZone.getTimeZone("UTC"));
		Date date = new Date();
		try {
			date = sf.parse(time);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date.getTime();
	}

	/**
	 * 
	 * 将字符串转化为时间戳
	 * 
	 * @param format
	 * @param time
	 * @return
	 */
	public static long getStringToDate(String format, String time) {
		sf = new SimpleDateFormat(format);
		Date date = new Date();
		try {
			date = sf.parse(time);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date.getTime();
	}

	/**
	 * 获取星期
	 * 
	 * @return
	 */
	public static int getWeek() {
		final Calendar c = Calendar.getInstance();
		c.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));

		return c.get(Calendar.DAY_OF_WEEK);

	}

	/**
	 * 获取小时（24）
	 * 
	 * @return
	 */
	public static int getHour() {
		final Calendar c = Calendar.getInstance();
		c.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
		return c.get(Calendar.HOUR_OF_DAY);

	}

	/**
	 * 获取分钟
	 * 
	 * @return
	 */
	public static int getMin() {
		final Calendar c = Calendar.getInstance();
		c.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
		return c.get(Calendar.MINUTE);

	}

	/**
	 * 获取2个时间的月份差
	 * @param time1
	 * @param time2
	 * @return
	 */
	public static  int getMonthDiff(long time1,long time2){
		Calendar c1 = Calendar.getInstance();
		Calendar c2 = Calendar.getInstance();

		c1.setTimeInMillis(time1);
		c2.setTimeInMillis(time2);

		int result = c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH);
		return  Math.abs(result);
	}

}
