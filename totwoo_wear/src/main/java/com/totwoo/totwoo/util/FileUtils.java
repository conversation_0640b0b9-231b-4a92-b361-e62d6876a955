package com.totwoo.totwoo.util;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.StatFs;
import android.provider.MediaStore.Images.ImageColumns;
import android.util.Base64;

import com.totwoo.totwoo.TotwooWearApplication;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.ref.WeakReference;

/**
 * 文件工具类， 提供文件操作类相关方法， 设备存储信息的调用方法
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class FileUtils {
    private static String ROOT_DIR = "totwoo";
    private static String DOWNLOAD_DIR = "download";
    private static String LOG_DIR = "log";
    private static String IMAGE_DIR = "image";
    private static String CACHE_DIR = "cache";
    private static String CACHE_IMAGE_DIR = "cache/image";

    /**
     * 获取app文件存储的根文件夹 private 属性, 非特殊情况, 外部调用
     *
     * @return
     */
    private static String getRootDir() {
        return getSDCardPath() + ROOT_DIR;
    }

    /**
     * 获取图片文件缓存的目录<br>
     * 此目录与 TotwooLibrary路径保持一致
     *
     * @return
     */
    public static String getCacheImageDir() {
        return checkCreatDir(CACHE_IMAGE_DIR);
    }

    /**
     * 检查当前子目录是否存在, 不存在则创建
     *
     * @param name
     * @return
     */
    private static String checkCreatDir(String name) {
        File dir = new File(getRootDir() + File.separator + name);

        if (!dir.exists()) {
            dir.mkdirs();
            noMediaDir(dir);
        }
        return dir.getPath();
    }

    /**
     * 递归遍历所有子文件夹, 添加 nomedia 文件
     *
     * @param dir
     */
    private static void noMediaDir(File dir) {
        if (dir == null) {
            return;
        }
        if (dir.isDirectory()) {
            File nofile = new File(dir, ".nomedia");
            try {
                nofile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }

            if (dir.listFiles() == null) {
                return;
            }

            // 遍历其子文件夹
            for (File f : dir.listFiles()) {
                noMediaDir(f);
            }
        }
    }

    /**
     * 获取文件缓存的根目录目录, 清除缓存时清理的根路径<br>
     * 此目录与 TotwooLibrary路径保持一致
     *
     * @return
     */
    public static String getCacheDir() {
        return checkCreatDir(CACHE_DIR);
    }

    /**
     * 获取图片保存的文件夹目录(此目录下图片适宜长期保存的图片, 不会再清除缓存是清除)
     *
     * @return
     */
    public static String getImageDir() {
        return checkCreatDir(IMAGE_DIR);
    }

    /**
     * 获取下载文件对应的文件夹
     *
     * @return
     */
    public static String getDownloadDir() {
        return checkCreatDir(DOWNLOAD_DIR);
    }

    /**
     * 获取日志保存的对应文件夹目录
     *
     * @return
     */
    public static String getLogDir() {
        return checkCreatDir(LOG_DIR);
    }

    /**
     * 获取SD卡路径
     *
     * @return
     */
    public static String getSDCardPath() {
        return TotwooWearApplication.mAppllicationContext.getExternalFilesDir(null).getAbsolutePath()
                + File.separator;
    }

    /**
     * 获取SD卡的剩余容量 单位byte
     *
     * @return
     */
    public static long getSDCardAllSize() {
        StatFs stat = new StatFs(getSDCardPath());
        // 获取空闲的数据块的数量
        long availableBlocks = (long) stat.getAvailableBlocks() - 4;
        // 获取单个数据块的大小（byte）
        long freeBlocks = stat.getAvailableBlocks();
        return freeBlocks * availableBlocks;
    }


    /**
     * 通过路径生成Base64文件
     *
     * @param path
     * @return
     */
    public static String getBase64FromPath(String path) {
        String base64 = "";
        try {
            File file = new File(path);
            byte[] buffer = new byte[(int) file.length() + 100];
            @SuppressWarnings("resource")
            int length = new FileInputStream(file).read(buffer);
            base64 = Base64.encodeToString(buffer, 0, length, Base64.DEFAULT);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return base64;
    }

    /**
     * 通过文件路径获取到bitmap
     *
     * @param path
     * @param w
     * @param h
     * @return
     */
    public static Bitmap getBitmapFromPath(String path, int w, int h) {
        BitmapFactory.Options opts = new BitmapFactory.Options();
        // 设置为ture只获取图片大小
        opts.inJustDecodeBounds = true;
        opts.inPreferredConfig = Bitmap.Config.ARGB_8888;
        // 返回为空
        BitmapFactory.decodeFile(path, opts);
        int width = opts.outWidth;
        int height = opts.outHeight;
        float scaleWidth = 0.f, scaleHeight = 0.f;
        if (width > w || height > h) {
            // 缩放
            scaleWidth = ((float) width) / w;
            scaleHeight = ((float) height) / h;
        }
        opts.inJustDecodeBounds = false;
        float scale = Math.max(scaleWidth, scaleHeight);
        opts.inSampleSize = (int) scale;
        WeakReference<Bitmap> weak = new WeakReference<Bitmap>(
                BitmapFactory.decodeFile(path, opts));
        return Bitmap.createScaledBitmap(weak.get(), w, h, true);
    }

    /**
     * 把bitmap转换成base64
     *
     * @param bitmap
     * @param bitmapQuality
     * @return
     */
    public static String getBase64FromBitmap(Bitmap bitmap, int bitmapQuality) {
        ByteArrayOutputStream bStream = new ByteArrayOutputStream();
        bitmap.compress(CompressFormat.PNG, bitmapQuality, bStream);
        byte[] bytes = bStream.toByteArray();
        return Base64.encodeToString(bytes, Base64.DEFAULT);
    }

    /**
     * 把base64转换成bitmap
     *
     * @param string
     * @return
     */
    public static Bitmap getBitmapFromBase64(String string) {
        byte[] bitmapArray = null;
        try {
            bitmapArray = Base64.decode(string, Base64.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BitmapFactory
                .decodeByteArray(bitmapArray, 0, bitmapArray.length);
    }

    /**
     * 把Stream转换成String
     *
     * @param is
     * @return
     */
    public static String convertStreamToString(InputStream is) {
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
        StringBuilder sb = new StringBuilder();
        String line = null;

        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line + "/n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return sb.toString();
    }

    /**
     * 保存bitmap到本地
     *
     * @param bmp
     * @return 保存好的path
     */
    public static String saveBitmapFromSDCard(Bitmap bmp, String fileName) {
        String path = FileUtils.getSDCardPath();
        // + "totwoo" + File.separator
        // + "Image" + File.separator;
        File f = new File(path + File.separator + fileName);
        if (f.exists()) {
            f.delete();
        }
        try {
            FileOutputStream out = new FileOutputStream(f);
            bmp.compress(CompressFormat.JPEG, 100, out);
            out.flush();
            out.close();
            return f.getPath();
            // LogUtils.i("已经保存");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *  * 根据Uri获取path  *  *
     *
     * @param context  
     * @param uri      * @return the file path or null  
     */
    public static String getRealFilePath(final Context context, final Uri uri) {
        if (null == uri)
            return null;
        final String scheme = uri.getScheme();
        String data = null;
        if (scheme == null)
            data = uri.getPath();
        else if (ContentResolver.SCHEME_FILE.equals(scheme)) {
            data = uri.getPath();
        } else if (ContentResolver.SCHEME_CONTENT.equals(scheme)) {
            Cursor cursor = context.getContentResolver().query(uri,
                    new String[]{ImageColumns.DATA}, null, null, null);
            if (null != cursor) {
                if (cursor.moveToFirst()) {
                    int index = cursor.getColumnIndex(ImageColumns.DATA);
                    if (index > -1) {
                        data = cursor.getString(index);
                    }
                }
                cursor.close();
            }
        }
        return data;
    }
}
