package com.totwoo.totwoo.bean;

import com.google.gson.annotations.SerializedName;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 代表星座运势相关数据
 * 
 * <AUTHOR>
 * @date 2015年9月11日
 */
public class ConstellationDataModel  {
	public static final int TYPE_TODAY = 0;
	public static final int TYPE_NEXTDAY = 1;
	public static final int TYPE_WEEK = 2;
	public static final int TYPE_MONTH = 3;
	public static final int TYPE_YEARS = 4;

	/** 数据类型 */
	private int dataType;
	/** 整体运势指数 */
	private int totalIndex;
	/** 爱情运势指数 */
	private int loveIndex;
	/** 工作运势指数 */
	private int workIndex;
	/** 财富运势指数 */
	private int moneyIndex;
	/** 幸运数字 */
	@SerializedName("number")
	private int luckyNumber;
	/** 幸运颜色 */
	@SerializedName("color")
	private String luckyColor;
	/** 整体评语 */
	private String totalSummary;
	/** 爱情运势评语 */
	private String loveSummary;
	/** 事业运势评语 */
	private String workSummary;
	/** 速配星座 */
	@SerializedName("OFriend")
	private String OFriend;
	/** 财富运势点评语 */
	private String MoneySummary;

	public String getMoneySummary() {
		return MoneySummary;
	}

	public void setMoneySummary(String moneySummary) {
		MoneySummary = moneySummary;
	}

	/**
	 * public ConstellationDataModel() { super(); }
	 * 
	 * /** 通过Json数据构造实例对象
	 * 
	 * @param jsondata
	 */
	public ConstellationDataModel(String jsondata) {
		super();
		try {
			JSONObject obj = new JSONObject(jsondata);
			setTotalIndex(obj.optInt("all"));
			setTotalSummary(obj.optString("summary"));
			setLoveIndex(obj.optInt("love"));
			setWorkIndex(obj.optInt("work"));
			setMoneyIndex(obj.optInt("money"));
			setLuckyNumber(obj.optInt("number"));
			setLuckyColor(obj.optString("color"));
			setLoveSummary(obj.optString("love_sum"));
			setWorkSummary(obj.optString("work_sum"));
			setOFriend(obj.optString("OFriend"));
			setMoneySummary(obj.optString("money_sum"));
		} catch (JSONException e) {
			e.printStackTrace();
		}
	}

	public int getDataType() {
		return dataType;
	}

	public void setDataType(int dataType) {
		this.dataType = dataType;
	}

	public int getTotalIndex() {
		return totalIndex;
	}

	public void setTotalIndex(int totalIndex) {
		this.totalIndex = totalIndex;
	}

	public int getLoveIndex() {
		return loveIndex;
	}

	public void setLoveIndex(int loveIndex) {
		this.loveIndex = loveIndex;
	}

	public int getWorkIndex() {
		return workIndex;
	}

	public void setWorkIndex(int workIndex) {
		this.workIndex = workIndex;
	}

	public int getMoneyIndex() {
		return moneyIndex;
	}

	public void setMoneyIndex(int moneyIndex) {
		this.moneyIndex = moneyIndex;
	}

	public int getLuckyNumber() {
		return luckyNumber;
	}

	public void setLuckyNumber(int luckyNumber) {
		this.luckyNumber = luckyNumber;
	}

	public String getLuckyColor() {
		return luckyColor;
	}

	public void setLuckyColor(String luckyColor) {
		this.luckyColor = luckyColor;
	}

	public String getTotalSummary() {
		return totalSummary;
	}

	public void setTotalSummary(String totalSummary) {
		this.totalSummary = totalSummary;
	}

	public String getLoveSummary() {
		return loveSummary;
	}

	public void setLoveSummary(String loveSummary) {
		this.loveSummary = loveSummary;
	}

	public String getWorkSummary() {
		return workSummary;
	}

	public void setWorkSummary(String workSummary) {
		this.workSummary = workSummary;
	}

	public String getOFriend() {
		return OFriend;
	}

	public void setOFriend(String oFriend) {
		OFriend = oFriend;
	}
}
