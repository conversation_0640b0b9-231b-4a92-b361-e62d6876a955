package com.totwoo.totwoo.bean;

import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON><PERSON><PERSON>ow<PERSON> on 16/4/14.
 */
public class HomeConstellationBean  {


    @SerializedName("count_send_num")
    private int todaySendNum;

    @SerializedName("count_receive_num")
    private int todayReceiveNum;

    @SerializedName("totwoo_num")
    private  int totwooNum;

    @SerializedName("is_user_paired")
    private  boolean userPairedStatus;

    private HomeConstellation constellation;

    public HomeConstellation getConstellation() {
        return constellation;
    }

    public void setConstellation(HomeConstellation constellation) {
        this.constellation = constellation;
    }

    public int getTodaySendNum() {
        return todaySendNum;
    }

    public void setTodaySendNum(int todaySendNum) {
        this.todaySendNum = todaySendNum;
    }

    public int getTodayReceiveNum() {
        return todayReceiveNum;
    }

    public void setTodayReceiveNum(int todayReceiveNum) {
        this.todayReceiveNum = todayReceiveNum;
    }

    public int getTotwooNum() {
        return totwooNum;
    }

    public void setTotwooNum(int totwooNum) {
        this.totwooNum = totwooNum;
    }

    public boolean isUserPairedStatus() {
        return userPairedStatus;
    }

    public void setUserPairedStatus(boolean userPairedStatus) {
        this.userPairedStatus = userPairedStatus;
    }





    public class HomeConstellation{

        private  String  name;

        private  String all;

        private  String summery;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAll() {
            return all;
        }

        public void setAll(String all) {
            this.all = all;
        }

        public String getSummery() {
            return summery;
        }

        public void setSummery(String summery) {
            this.summery = summery;
        }
    }

}
