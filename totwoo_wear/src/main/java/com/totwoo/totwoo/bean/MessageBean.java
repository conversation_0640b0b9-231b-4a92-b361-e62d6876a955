package com.totwoo.totwoo.bean;

import com.totwoo.library.db.annotation.Column;
import com.totwoo.library.db.annotation.Table;

import java.io.Serializable;

/**
 * Totwoo 消息体， 包含totwoo消息相关信息， 及方法；可直接生成相应的View
 * 
 * <AUTHOR>
 * @date 2015-2015年7月16日
 */
@Table(name = "towoo_message")
public class MessageBean implements Serializable {
	public static final String TAG = MessageBean.class.getSimpleName();

	/** 消息类型: 收到 totwoo */
	public static final int MSG_TYPE_TOTWOO_IN = 1;
	/** 消息类型: 发出 totwoo */
	public static final int MSG_TYPE_TOTWOO_OUT = 2;
	/** 消息类型: 单身模式, 收到人 可以发送请求 */
	public static final int MSG_TYPE_PERSON = 3;
	/** 消息类型: 单身模式, 未中任何奖 */
	public static final int MSG_TYPE_NULL = 4;
	/** 消息类型: 单身模式, 图片格式的 其他奖品 */
	public static final int MSG_TYPE_LOTTERY = 5;
	/** 消息类型: 提示性消息. 比如配对成功了, 有人想我发出邀请 等, 此类消息制作临时存储, 显示之后及时删除即可 */
	public static final int MSG_TYPE_TOTWOO_PROMPT = 6;

	/** 消息Id */
	@Column(column = "_id")
	private int id;

	/** 消息内容 */
	@Column(column = "content")
	private String content;

	/** 消息发送日期, 全部以 毫秒 单位存储, 从服务器下发的时间存储是需要做一步转换 */
	@Column(column = "send_time")
	private long sendTime;

	/** 消息发送用户Id */
	@Column(column = "send_uid")
	private String sendUid;

	/** 消息接受用户Id */
	@Column(column = "receiver_uid")
	private String receiverUid;

	/** 需显示的用户头像, 对于发出的消息是自己的头像, 对于收到的消息 是对方头像, 对于中间记录, 表示中奖图片 */
	@Column(column = "pic_url")
	private String picUrl;

	/** 需要二次跳转的url */
	@Column(column = "target_url")
	private String targetUrl;

	/** 消息类型 */
	@Column(column = "msg_type")
	private int msgType;

	/** 消息标题, (用户昵称, 奖品名称) */
	@Column(column = "msg_title")
	private String msgTitle;

	/** 是否已经展示过 */
	@Column(column = "has_show")
	private boolean hasShow;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public long getSendTime() {
		return sendTime;
	}

	public void setSendTime(long sendTime) {
		this.sendTime = sendTime;
	}

	public String getSendUid() {
		return sendUid;
	}

	public void setSendUid(String sendUid) {
		this.sendUid = sendUid;
	}

	public String getReceiverUid() {
		return receiverUid;
	}

	public void setReceiverUid(String receiverUid) {
		this.receiverUid = receiverUid;
	}

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public String getTargetUrl() {
		return targetUrl;
	}

	public void setTargetUrl(String targetUrl) {
		this.targetUrl = targetUrl;
	}

	public int getMsgType() {
		return msgType;
	}

	public void setMsgType(int msgType) {
		this.msgType = msgType;
	}

	public String getMsgTitle() {
		return msgTitle;
	}

	public void setMsgTitle(String msgTitle) {
		this.msgTitle = msgTitle;
	}

	public boolean isHasShow() {
		return hasShow;
	}

	public void setHasShow(boolean hasShow) {
		this.hasShow = hasShow;
	}

}
