<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <android.support.wearable.view.ProgressSpinner
            android:id="@+id/send_totwoo_loding_data_prg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:color_sequence="@color/white"
            />

        <TextView
            android:id="@+id/send_totwoo_loding_data_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:paddingTop="5dp"
            android:text="@string/totwoo_sending_toast"
            android:textColor="#989898"
            />
    </LinearLayout>
</RelativeLayout>