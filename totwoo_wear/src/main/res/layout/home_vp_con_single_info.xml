<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/text_color_black_important">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        >

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:paddingBottom="10dp"
            android:text="标题"
            android:textColor="@color/white"
            android:textSize="21sp"
            />

        <ImageView
            android:id="@+id/con_single_info_iv"
            android:layout_width="88dp"
            android:layout_height="88dp"
            android:layout_below="@id/title_tv"
            android:layout_centerHorizontal="true"
            android:background="@mipmap/silver_round_bg"
            />

        <TextView
            android:id="@+id/con_fo_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/con_single_info_iv"
            android:layout_centerHorizontal="true"
            android:paddingTop="5dp"
            android:textColor="#989898"
            android:textSize="16sp"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/round_tv"
            android:layout_width="88dp"
            android:layout_height="88dp"
            android:layout_alignBottom="@+id/con_single_info_iv"
            android:layout_alignTop="@+id/con_single_info_iv"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:textColor="#989898"
            android:textSize="18sp"
            />
    </RelativeLayout>
</RelativeLayout>