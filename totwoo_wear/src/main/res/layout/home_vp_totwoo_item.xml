<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/text_color_black_important"
                android:orientation="vertical">

    <ImageView
        android:layout_width="152dp"
        android:layout_height="152dp"
        android:layout_centerInParent="true"
        android:src="@mipmap/totwoo_vp_item_bg"
        />

    <Space
        android:id="@+id/top_sp"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        />

    <com.totwoo.totwoo.widget.RoundImageView
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_below="@+id/top_sp"
        android:layout_centerHorizontal="true"
        android:background="@mipmap/totwoo_vp_item_portrait_bg"
        />

    <com.totwoo.totwoo.widget.RoundImageView
        android:id="@+id/totwoo_vp_item_portrait_iv"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_below="@+id/top_sp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="2dp"
        />

    <TextView
        android:id="@+id/totwoo_vp_item_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/totwoo_vp_item_portrait_iv"
        android:layout_centerHorizontal="true"
        android:paddingTop="1.5dp"
        android:textColor="@color/text_color_black_nomal"
        android:textSize="13sp"
        tools:text="sfsfssdfs"
        />

    <TextView
        android:id="@+id/totwoo_vp_item_time_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/totwoo_vp_item_name_tv"
        android:layout_centerHorizontal="true"
        android:textColor="@color/black_54p"
        android:textSize="14sp"
        tools:text="sfsfssdfs"
        />

    <TextView
        android:id="@+id/totwoo_text_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/totwoo_vp_item_time_tv"
        android:layout_centerHorizontal="true"
        android:text="totwoo"
        android:textColor="@color/text_color_black_important"
        android:textSize="25sp"
        />

    <ImageView
        android:id="@+id/totwoo_send_iv"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="14.5dp"
        android:background="@mipmap/send_towoo"
        android:clickable="true"
        android:scaleType="fitXY"
        android:src="@drawable/item_bg"
        android:visibility="gone"
        />
</RelativeLayout>