<?xml version="1.0" encoding="utf-8"?>
<android.support.wearable.view.BoxInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"

    android:layout_height="match_parent"
    android:background="@color/text_color_black_important">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/home_viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:background="@color/black"
            android:overScrollMode="never"
            >
        </androidx.viewpager.widget.ViewPager>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >

            <com.totwoo.totwoo.widget.CircleIndicatorView
                android:id="@+id/page_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:paddingTop="10dp"/>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/home_loding_data_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            >

            <android.support.wearable.view.ProgressSpinner
                android:id="@+id/home_loding_data_prg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:color_sequence="@color/white"
                />

            <TextView
                android:id="@+id/home_loding_data_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:paddingTop="5dp"
                android:text="@string/home_loding_data_text"
                android:textColor="#989898"
                />
        </LinearLayout>
    </RelativeLayout>
</android.support.wearable.view.BoxInsetLayout>