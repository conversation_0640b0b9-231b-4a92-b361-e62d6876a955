<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/text_color_black_important">

    <com.totwoo.totwoo.widget.ViewPagerVertical
        android:id="@+id/home_vp_itme_vvp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never">
    </com.totwoo.totwoo.widget.ViewPagerVertical>

    <View
        android:id="@+id/transparent_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#30ffffff"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/first_send_totwoo_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@mipmap/first_send_totwoo_bg"
        android:scaleType="center"
        android:src="@mipmap/first_send_totwoo"
        android:visibility="gone"
        />
    <TextView
        android:id="@+id/first_send_totwoo_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/first_send_totwoo_iv"
        android:layout_centerHorizontal="true"
        android:paddingTop="10dp"
        android:text="@string/first_send_totwoo_tv"
        android:textColor="@color/white"
        android:textSize="19sp"
        android:visibility="gone"
        />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/totwoo_holder_nopaired_tv"
        android:textColor="#989898"
        android:layout_centerInParent="true"
        android:visibility="gone"
        />
</RelativeLayout>