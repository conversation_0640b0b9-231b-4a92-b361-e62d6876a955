<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/text_color_black_important"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/hoem_vp_con_title"
            android:textColor="@color/white"
            android:textSize="21sp"
            />

        <RatingBar
            android:id="@+id/home_constellation_ratingBar"
            style="@style/ratingbar_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_margin="5dp"
            android:progressDrawable="@drawable/home_constellation_ratingbar_drawable"
            />

        <TextView
            android:id="@+id/home_con_info_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:maxEms="12"
            android:maxLines="10"
            android:paddingTop="10dp"
            android:text="呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵呵"
            android:textColor="#989898"
            android:textSize="16sp"
            />
    </LinearLayout>
</RelativeLayout>
