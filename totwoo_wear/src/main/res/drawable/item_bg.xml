<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape>
            <corners android:radius="30dp"/>
            <solid android:color="@color/line_color_black_solid1"/>
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="30dp"/>
            <solid android:color="@color/line_color_black_solid1"/>
        </shape>
    </item>

    <item android:drawable="@color/transparent"/>
</selector>

