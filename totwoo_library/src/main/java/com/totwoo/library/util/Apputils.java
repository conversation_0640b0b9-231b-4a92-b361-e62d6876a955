package com.totwoo.library.util;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.ActivityManager.RunningTaskInfo;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.Signature;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.os.LocaleListCompat;

import com.blankj.utilcode.util.Utils;

import java.io.File;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提供与设备信息相关，包信息相关的信息获取通用方法<br>
 * 例如：屏幕宽高，像素密度，版本号， 版本名，等一系列 相关信息
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class Apputils {

    /**
     * 全局通用的线程池
     */
    private static ExecutorService globalExecutor;

    /**
     * 获取应用程序版本名称信息
     *
     * @param context
     * @return 当前应用的版本名称
     */
    public static String getVersionName(Context context) {
        return getVersionName(context, false);
    }

    /**
     * 获取应用程序版本名称信息, 默认仅返回符合标准 x.xx.xx 类型部分, 后缀忽略
     *
     * @param context
     * @param forShow 是否用于展示, 返回原始的 versionName
     * @return
     */
    public static String getVersionName(Context context, boolean forShow) {
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    context.getPackageName(), 0);
            if (forShow) {
                return packageInfo.versionName;
            } else {
                return checkVersionName(packageInfo.versionName, forShow);
            }
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return forShow ? "" : null;
    }

    private static String checkVersionName(String name, boolean forShow) {
        String pattern = "\\d+[.]\\d+[.]\\d+";
        Matcher matcher = Pattern.compile(pattern).matcher(name);
        if (matcher.find()) {
            return matcher.group();
        } else {
            return forShow ? "" : null;
        }
    }

    /**
     * 获取应用程序版本号
     *
     * @param context
     * @return 当前应用的版本名称
     */
    public static int getVersionCode(Context context) {
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return 0;
    }


    /**
     * 获得屏幕高度
     *
     * @param context
     * @return
     */
    public static int getScreenWidth(Context context) {
        WindowManager wm = (WindowManager) context
                .getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(outMetrics);
        return outMetrics.widthPixels;
    }

    /**
     * 获得屏幕宽度
     *
     * @param context
     * @return
     */
    public static int getScreenHeight(Context context) {
        WindowManager wm = (WindowManager) context
                .getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(outMetrics);
        return outMetrics.heightPixels;
    }

    /**
     * 获得状态栏的高度
     *
     * @param context
     * @return
     */
    public static int getStatusHeight(Context context) {
        Rect localRect = new Rect();
//        Main window.decorView.getWindowVisibleDisplayFrame(localRect);
        int mStatusBarHeight = localRect.top;
        if (0 == mStatusBarHeight) {
            try {
                Class<?> clazz = Class.forName("com.android.internal.R$dimen");
                Object object = clazz.newInstance();
                int height = Integer.parseInt(clazz.getField("status_bar_height")
                        .get(object).toString());
                mStatusBarHeight = context.getResources().getDimensionPixelSize(height);
            } catch (Exception var6) {
            }
        }
        if (0 == mStatusBarHeight) {
            int resourceId =
                    context.getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (resourceId > 0) {
                mStatusBarHeight = context.getResources().getDimensionPixelSize(resourceId);
            }
        }
        return mStatusBarHeight;
    }

    /**
     * 获得状态栏的高度
     *
     * @param context
     * @return
     */
    public static int getNaivgationBarHeight(Context context) {
        int statusHeight = -1;
        try {
            Class<?> clazz = Class.forName("com.android.internal.R$dimen");
            Object object = clazz.newInstance();
            int height = Integer.parseInt(clazz.getField("navigation_bar_height")
                    .get(object).toString());
            statusHeight = context.getResources().getDimensionPixelSize(height);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return statusHeight;
    }

    /**
     * 获取当前屏幕截图，包含状态栏
     *
     * @param activity
     * @return
     */
    public static Bitmap snapShotWithStatusBar(Activity activity) {
        View view = activity.getWindow().getDecorView();
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap bmp = view.getDrawingCache();
        int width = getScreenWidth(activity);
        int height = getScreenHeight(activity);
        Bitmap bp = null;
        bp = Bitmap.createBitmap(bmp, 0, 0, width, height);
        view.destroyDrawingCache();
        return bp;

    }

    /**
     * 获取当前屏幕截图，不包含状态栏
     *
     * @param activity
     * @return
     */
    public static Bitmap snapShotWithoutStatusBar(Activity activity) {
        View view = activity.getWindow().getDecorView();
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap bmp = view.getDrawingCache();
        Rect frame = new Rect();
        activity.getWindow().getDecorView().getWindowVisibleDisplayFrame(frame);
        int statusBarHeight = frame.top;

        int width = getScreenWidth(activity);
        int height = getScreenHeight(activity);
        Bitmap bp = null;
        bp = Bitmap.createBitmap(bmp, 0, statusBarHeight, width, height
                - statusBarHeight);
        view.destroyDrawingCache();
        return bp;
    }

    /**
     * dp转px
     *
     * @param context
     * @param dpVal
     * @return
     */
    public static int dp2px(Context context, float dpVal) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                dpVal, context.getResources().getDisplayMetrics());
    }

    /**
     * sp转px
     *
     * @param context
     * @param spVal
     * @return
     */
    public static int sp2px(Context context, float spVal) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP,
                spVal, context.getResources().getDisplayMetrics());
    }

    /**
     * px转dp
     *
     * @param context
     * @param pxVal
     * @return
     */
    public static float px2dp(Context context, float pxVal) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (pxVal / scale);
    }

    /**
     * px转sp
     *
     * @param context
     * @param pxVal
     * @return
     */
    public static float px2sp(Context context, float pxVal) {
        return (pxVal / context.getResources().getDisplayMetrics().scaledDensity);
    }

    /**
     * 缩放/裁剪图片
     *
     * @param bm
     * @param newWidth
     * @param newHeight
     * @return
     */
    public static Bitmap zoomImg(Bitmap bm, int newWidth, int newHeight) {
        // 获得图片的宽高
        int width = bm.getWidth();
        int height = bm.getHeight();
        // 计算缩放比例
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;
        // 取得想要缩放的matrix参数
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        // 得到新的图片
        Bitmap newbm = Bitmap.createBitmap(bm, 0, 0, width, height, matrix,
                true);
        return newbm;
    }

    /**
     * 修改整个界面所有控件的字体
     *
     * @param root
     * @param pathRes
     * @param act
     */
    public static void changeFonts(ViewGroup root, int pathRes, Activity act) {
        // path是字体路径
        Typeface tf = ResourcesCompat.getFont(act, pathRes);
        for (int i = 0; i < root.getChildCount(); i++) {
            View v = root.getChildAt(i);
            if (v instanceof TextView) {
                ((TextView) v).setTypeface(tf);
            } else if (v instanceof Button) {
                ((Button) v).setTypeface(tf);
            } else if (v instanceof EditText) {
                ((EditText) v).setTypeface(tf);
            } else if (v instanceof ViewGroup) {
                changeFonts((ViewGroup) v, pathRes, act);
            }
        }
    }

    /**
     * 根据月日计算星座
     *
     * @param month
     * @param day
     * @return  6.12
     */
    public static String getAstro(Context context, int month, int day) {
        if (month <= 0 || month > 12) {
            month = 1; // 如果月份小于等于0，设置为1
        }
        String[] astro = {
                "摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座",
                "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "摩羯座"
        };
        String[] astroEn = {
                "CAPRICORN", "AQUARIUS", "PISCES", "ARIES", "TAURUS", "GEMINI",
                "CANCER", "LEO", "VIRGO", "LIBRA", "SCORPIO", "SAGITTARIUS", "CAPRICORN"
        };
        int[] days = {20, 19, 20, 20, 21, 21, 22, 22, 22, 23, 22, 21};

        int index = (day < days[month - 1]) ? month - 1 : month;
        // 返回根据系统语言选择的星座名称
        return systemLanguageIsChinese(context) ? astro[index] : astroEn[index];
    }



    /**
     * 获取对应时间点对应的当天0点的日历实例
     *
     * @param cal 为null 时返回当天0点对应日历
     * @return
     */
    public static Calendar getZeroCalendar(Calendar cal) {
        if (cal == null) {
            cal = Calendar.getInstance();
        }

        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal;
    }

    /**
     * 安装pak
     *
     * @param file
     * @param context
     */
    public static void installApk(File file, Context context) {
        Intent intent = new Intent();
        // 执行动作
        intent.setAction(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        // 执行的数据类型
        intent.setDataAndType(Uri.fromFile(file),
                "application/vnd.android.package-archive");// 编者按：此处Android应为android，否则造成安装不了
        context.startActivity(intent);
    }

    /**
     * 通过包名判断程序是否安装
     *
     * @param context
     * @param packagename
     * @return
     */
    public static boolean isAppInstalled(Context context, String packagename) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(
                    packagename, 0);
        } catch (NameNotFoundException e) {
            packageInfo = null;
            e.printStackTrace();
        }
        return packageInfo != null;
    }

    /**
     * 裁剪图片
     *
     * @param uri
     */
//    public static void startPhotoZoom(Uri uri, Activity activity, int requestCode, String outputPath) {
//        Intent intent = new Intent("com.android.camera.action.CROP");
//        intent.setDataAndType(uri, "image/*");
//        intent.putExtra("crop", "true");// crop=true 有这句才能出来最后的裁剪页面.
//        intent.putExtra("aspectX", 4);// 这两项为裁剪框的比例.
//        intent.putExtra("aspectY", 4);// x:y=1:2
//        // intent.putExtra("return-data", true);
//        intent.putExtra("output",
//                Uri.fromFile(new File(outputPath)));
//        intent.putExtra("outputFormat", "JPEG");// 返回格式
//        activity.startActivityForResult(intent, requestCode);
//    }
//
//    public static void startPhotoZoom(Uri uri, Activity activity, int requestCode, String outputPath, int width, int height) {
//        Intent intent = new Intent("com.android.camera.action.CROP");
//        intent.setDataAndType(uri, "image/*");
//        intent.putExtra("crop", "true");// crop=true 有这句才能出来最后的裁剪页面.
//        intent.putExtra("aspectX", width);// 这两项为裁剪框的比例.
//        intent.putExtra("aspectY", height);// x:y=1:2
//        // intent.putExtra("return-data", true);
//        intent.putExtra("output", getImageContentUri(new File(outputPath),activity));
//        intent.putExtra("outputFormat", "JPEG");// 返回格式
//        activity.startActivityForResult(intent, requestCode);
//    }

    /**
     * 转换 content:// uri
     *
     * @param imageFile
     * @return
     */
    public static Uri getImageContentUri(File imageFile, Activity activity) {
        String filePath = imageFile.getAbsolutePath();
        Cursor cursor = activity.getContentResolver().query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                new String[]{MediaStore.Images.Media._ID},
                MediaStore.Images.Media.DATA + "=? ",
                new String[]{filePath}, null);

        if (cursor != null && cursor.moveToFirst()) {
            @SuppressLint("Range") int id = cursor.getInt(cursor
                    .getColumnIndex(MediaStore.MediaColumns._ID));
            Uri baseUri = Uri.parse("content://media/external/images/media");
            return Uri.withAppendedPath(baseUri, "" + id);
        } else {
            if (imageFile.exists()) {
                ContentValues values = new ContentValues();
                values.put(MediaStore.Images.Media.DATA, filePath);
                return activity.getContentResolver().insert(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
            } else {
                return null;
            }
        }
    }

    /**
     * 根据偏移量颜色渐变 从color2到color1 的渐变
     */
    public static int blendColors(int color1, int color2, float ratio) {
        final float inverseRation = 1f - ratio;
        float r = (Color.red(color1) * ratio)
                + (Color.red(color2) * inverseRation);
        float g = (Color.green(color1) * ratio)
                + (Color.green(color2) * inverseRation);
        float b = (Color.blue(color1) * ratio)
                + (Color.blue(color2) * inverseRation);
        return Color.rgb((int) r, (int) g, (int) b);
    }


    /**
     * 获取当前系统语言对应的区号
     */
    public static String getSystemLanguageCountryCode(Context context) {
//                 +86 中文
//                +33 法语
//                +49 德语
//                +39 意大利
//                +81 日本
//                +82 韩国
//                +34  西班牙
        String language = Apputils.getSystemLanguage(context);
        String country_code_value;
        switch (language) {
            case "zh":
                country_code_value = "86";
                break;
            case "de":
                country_code_value = "49";
                break;
            case "es":
                country_code_value = "34";
                break;
            case "fr":
                country_code_value = "33";
                break;
            case "it":
                country_code_value = "39";
                break;
            case "ja":
                country_code_value = "81";
                break;
            case "ko":
                country_code_value = "82";
                break;
            case "en":
            default:
                country_code_value = "1";
                break;
        }
        return country_code_value;
    }


    /**
     * 判断系统语言是否是中文环境
     *
     * @param context
     * @return
     */
    public static String getSystemLanguage(Context context) {
        LocaleListCompat appLocales = AppCompatDelegate.getApplicationLocales();
        if (!appLocales.isEmpty() && appLocales.get(0) != null) {
            return Objects.requireNonNull(appLocales.get(0)).getLanguage();
        } else {
            Locale locale = Utils.getApp().getResources().getConfiguration().locale;
            return locale.getLanguage();
        }
    }

    /**
     * 判断系统语言是否是中文环境
     *
     * @param context
     * @return
     */
    public static boolean systemLanguageIsChinese(Context context) {
        return "zh".equals(getSystemLanguage(context));
    }


    /**
     * 判断系统语言是否是 法 德意
     *
     * @param context
     * @return
     */
    public static boolean systemLanguageIsFA_DE_IT(Context context) {
        return "fr".equals(getSystemLanguage(context)) ||"de".equals(getSystemLanguage(context)) ||"it".equals(getSystemLanguage(context));
    }

    /**
     * 非中文, 非英文
     *
     * @param context
     * @return
     */
    public static boolean systemLanguageIsOther(Context context) {
        String language = getSystemLanguage(context);
        return !language.equals("zh") && !language.equals("en");
    }

    /**
     * 当前程序, HomeActivity 是否当前Activity
     */
    public static boolean isActivityForeground(Context context,
                                               Class<? extends Activity> activity) {
        ActivityManager am = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        List<RunningTaskInfo> list = am.getRunningTasks(1);
        if (list != null && list.size() > 0) {
            ComponentName cpn = list.get(0).topActivity;
            if (activity.getName().equals(cpn.getClassName())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断国际号码是否为正确格式
     *
     * @return
     */
    public static boolean checkPhoneNumber(String phone) {
        if (phone.startsWith("+")) {
            return true;
        } else {
            return phone.length() >= 7 && phone.length() <= 16;
        }
    }

    /**
     * 处理字符串为, 每个单词首字母大写
     *
     * @param title
     * @return
     */
    public static String getUpStar(String title) {
        if (title == null) {
            return null;
        } else {
            String[] parts = title.split(" ");

            StringBuilder sb = new StringBuilder();
            for (String ss : parts) {
                sb.append((ss.charAt(0) + "").toUpperCase()).append(ss.substring(1, ss.length())).append(" ");
            }
            return sb.toString();
        }
    }

//    /**
//     * 清除本地用户数据和痕迹
//     *
//     * @param context
//     */
//    public static void cancelUserData(Context context) {
//
//    }

    /**
     * 获取 App 签名的 HashKey, Facebook 分享时使用
     *
     * @param context
     */
    public static void getAppHashKey(Context context) {
        PackageInfo info;
        try {
            info = context.getPackageManager().getPackageInfo("com.totwoo.totwoo",
                    PackageManager.GET_SIGNATURES);
            for (Signature signature : info.signatures) {
                MessageDigest md;
                md = MessageDigest.getInstance("SHA");
                md.update(signature.toByteArray());
                String something = new String(Base64.encode(md.digest(), 0));
                // String something = new
                // String(Base64.encodeBytes(md.digest()));
                LogUtils.e("hash key", something);
            }
        } catch (NameNotFoundException e1) {
            LogUtils.e("name not found", e1.toString());
        } catch (NoSuchAlgorithmException e) {
            LogUtils.e("no such an algorithm", e.toString());
        } catch (Exception e) {
            LogUtils.e("exception", e.toString());
        }
    }


    public static boolean isScreenOn() {
        PowerManager powerManager = (PowerManager) Utils.getApp().getSystemService(Context.POWER_SERVICE);
        return powerManager.isInteractive();
    }

    /**
     * 检测当前应用是否在前台运行
     *
     * @param ctx
     * @return
     */
    public static boolean isAppForground(Context ctx) {
        ActivityManager manager = (ActivityManager) ctx
                .getSystemService(Context.ACTIVITY_SERVICE);
        String str = manager.getRunningTasks(1).get(0).topActivity
                .getPackageName();
        String ac = ctx.getPackageName();
        if (str.equalsIgnoreCase(ac)) {
            // 在前台运行
            return true;
        } else {
            // 在后台运行
            return false;
        }
    }

    /**
     * 升级 targetSdk 12 后, 强制要求 PendingIntent Flag 追加可变与否的值, 在这里统一控制
     *
     * @param sourceFlag
     * @return
     */
    public static int wrapMutablePendingFlag(int sourceFlag) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return sourceFlag | PendingIntent.FLAG_MUTABLE;
        } else {
            return sourceFlag;
        }
    }

    /**
     * 获取全局的线程池
     *
     * @return
     */
    public static ExecutorService getGlobalExecutor() {
        if (globalExecutor == null || globalExecutor.isShutdown()) {
            globalExecutor = Executors.newSingleThreadExecutor();
        }
        return globalExecutor;
    }

    /**
     * Bundle 转 String
     *
     * @param bundle
     * @return
     */
    public static String bundleToString(Bundle bundle) {
        if (bundle != null) {
            StringBuilder sb = new StringBuilder();
            for (String key : bundle.keySet()) {
                sb.append(key).append(":").append(bundle.get(key)).append(", ");
            }

            if (sb.length() > 1) {
                sb.delete(sb.length() - 1, sb.length());
            }

            return sb.toString();
        } else {
            return "null";
        }
    }

}
