package com.totwoo.library.util;


import android.graphics.Outline;
import android.view.View;
import android.view.ViewOutlineProvider;

/**
 * <AUTHOR>
 * @des:
 * @date 2024/9/19 16:00
 */
public class RoundRectOutlineProvider extends ViewOutlineProvider {

    private final float radius;

    public RoundRectOutlineProvider(float radius) {
        this.radius = radius;
    }

    @Override
    public void getOutline(View view, Outline outline) {
        outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), radius);
    }
}
