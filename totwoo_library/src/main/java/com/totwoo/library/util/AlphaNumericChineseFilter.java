package com.totwoo.library.util;

import android.text.InputFilter;
import android.text.Spanned;

import java.util.regex.Pattern;

public class AlphaNumericChineseFilter implements InputFilter {
    private static final Pattern PATTERN = Pattern.compile("^[a-zA-Z0-9\\u4E00-\\u9FFF]+$");

    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        for (int i = start; i < end; i++) {
            char c = source.charAt(i);
            if (!PATTERN.matcher(String.valueOf(c)).matches()) {
                return "";
            }
        }
        return null;
    }
}