package com.totwoo.library.bitmap;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.media.ExifInterface;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Base64;
import android.widget.ImageView;

import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.totwoo.library.R;
import com.totwoo.library.util.Apputils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;

/**
 * 提供类库中 BitmapUtils 统一的使用方式，定制通用的属性
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class BitmapHelper {
    /**
     * 资源服务器前缀
     */
    public static final String HOSTURL_RESCOURCE = "http://image.totwoo.com/";


    public static void display(Activity activity, ImageView view, int resourceId) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try {
            Glide.with(activity).load(resourceId).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void display(Fragment fragment, ImageView view, int resourceId) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try {
            Glide.with(fragment).load(resourceId).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void setHead(Context context, ImageView view, String url,int gender){
        RequestOptions femaleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        RequestOptions maleOptions = new RequestOptions()
                .error(R.drawable.default_head_yellow);
        if (gender == 0) {
            Glide.with(context).load(BitmapHelper.checkRealPath(url)).apply(maleOptions).into(view);
        } else {
            Glide.with(context).load(BitmapHelper.checkRealPath(url)).apply(femaleOptions).into(view);
        }
    }

    public static void display (Activity activity, ImageView view, String url, int defaultDrawable)
    {
        RequestOptions options = new RequestOptions().placeholder(defaultDrawable);
        try {
            Glide.with(activity).load(checkRealPath(url)).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void display (Activity activity, ImageView view, String url, int defaultDrawable, int length)
    {
        try
        {
            Glide.with(activity).load(checkRealPath(url)).into(view);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void display(Activity activity, ImageView view, String url) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try {
            Glide.with(activity).load(checkRealPath(url)).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static void displayWithoutPlaceHolder(Activity activity, ImageView view, String url) {
        try {
            Glide.with(activity).load(checkRealPath(url)).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void display(Activity activity, ImageView view, File file, int defalutResource)
    {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try
        {
            Glide.with(activity).load(file).apply(options).into(view);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void display(Fragment fragment, ImageView view, String url) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try {
            Glide.with(fragment).load(checkRealPath(url)).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void display(Context mContext, ImageView view, String url) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try {
            Glide.with(mContext).load(checkRealPath(url)).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void display(Context mContext, ImageView view, String url, int drawable) {
        RequestOptions options = new RequestOptions().placeholder(drawable);
        try {
            Glide.with(mContext).load(checkRealPath(url)).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void display(Activity activity, ImageView view, String url, Drawable placeholder) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try {
            Glide.with(activity).load(checkRealPath(url)).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void display(Fragment fragment, ImageView view, String url, Drawable placeholder) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.default_head_yellow);
        try {
            Glide.with(fragment).load(checkRealPath(url)).apply(options).into(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查当前资源是否为绝对路径, 如果是直接返回, 否则添加资源域名前缀
     *
     * @return
     */
    public static String checkRealPath(String url) {
        if(TextUtils.isEmpty(url)){
            return null;
        } if (url.startsWith("http://") || url.startsWith("https://")) {
            return url;
        } else {
            return HOSTURL_RESCOURCE + url;
        }
    }

    /**
     * 功能
     *
     * @param bitmap       要压缩图片
     * @param targetWidth  缩放的目标宽度
     * @param targetHeight 缩放的目标高度
     * @return 缩放后的图片
     */
    public static Bitmap compressBySize(Bitmap bitmap, int targetWidth,
                                        int targetHeight) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(CompressFormat.JPEG, 70, baos);
        BitmapFactory.Options opts = new BitmapFactory.Options();
        opts.inJustDecodeBounds = true;
        BitmapFactory.decodeByteArray(baos.toByteArray(), 0,
                baos.toByteArray().length, opts);
        // 得到图片的宽度、高度；
        int imgWidth = opts.outWidth;
        int imgHeight = opts.outHeight;
        // 分别计算图片宽度、高度与目标宽度、高度的比例；取大于该比例的最小整数；
        int widthRatio = (int) Math.ceil(imgWidth / (float) targetWidth);
        int heightRatio = (int) Math.ceil(imgHeight / (float) targetHeight);
        if (widthRatio > 1 && widthRatio > 1) {
            if (widthRatio > heightRatio) {
                opts.inSampleSize = widthRatio;
            } else {
                opts.inSampleSize = heightRatio;
            }
        }
        // 设置好缩放比例后，加载图片进内存；
        opts.inJustDecodeBounds = false;
        bitmap = BitmapFactory.decodeByteArray(baos.toByteArray(), 0,
                baos.toByteArray().length, opts);
        try {
            baos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return bitmap;
    }

    public static Bitmap compressBitmapByQuality(Bitmap bitmap,int size) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        int quality = 100; // 初始压缩质量设为100
        bitmap.compress(Bitmap.CompressFormat.WEBP, quality, outputStream);
        // 循环压缩直到压缩后的大小小于100KB
        while (outputStream.toByteArray().length > size * 1024 && quality > 0) {
            outputStream.reset(); // 重置outputStream以便重新写入
            quality -= 10; // 每次降低10%的质量
            if (quality <= 0) {
                // 防止质量降至0导致死循环
                break;
            }
            bitmap.compress(Bitmap.CompressFormat.WEBP, quality, outputStream);
        }
        // 根据压缩后的字节数组创建Bitmap
        byte[] compressedData = outputStream.toByteArray();
        return BitmapFactory.decodeByteArray(compressedData, 0, compressedData.length);
    }
    /**
     * 读取照片exif信息中的旋转角度
     *
     * @param path 照片路径
     * @return角度
     */
    public static int readPictureDegree(String path) {
        int degree = 0;
        try {
            ExifInterface exifInterface = new ExifInterface(path);
            int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    degree = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    degree = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    degree = 270;
                    break;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return degree;
    }

    /**
     * 旋转图片
     *
     * @param img
     * @param degree 角度
     * @return
     */
    public static Bitmap toturn(Bitmap img, int degree) {
        Matrix matrix = new Matrix();
        matrix.postRotate(degree); /*翻转90度*/
        int width = img.getWidth();
        int height = img.getHeight();
        img = Bitmap.createBitmap(img, 0, 0, width, height, matrix, true);
        return img;
    }

    /**
     * 解决三星手机 相机 获取图片后的图片旋转问题
     *
     * @param mImageCaptureUri
     * @return
     */

    public static Bitmap rotationImage(Uri mImageCaptureUri, Context context) {
        Bitmap bitmap = null;
        // 不管是拍照还是选择图片每张图片都有在数据中存储也存储有对应旋转角度orientation值
        // 所以我们在取出图片是把角度值取出以便能正确的显示图片,没有旋转时的效果观看
        ContentResolver cr = context.getContentResolver();
        Cursor cursor = cr.query(mImageCaptureUri, null, null, null, null);// 根据Uri从数据库中找
        if (cursor != null) {
            cursor.moveToFirst();// 把游标移动到首位，因为这里的Uri是包含ID的所以是唯一的不需要循环找指向第一个就是了
            String filePath = cursor.getString(cursor.getColumnIndex("_data"));// 获取图片路
            String orientation = cursor.getString(cursor
                    .getColumnIndex("orientation"));// 获取旋转的角度
            cursor.close();
            if (filePath != null) {
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inJustDecodeBounds = true;
                BitmapFactory.decodeFile(filePath, options);
                options.inJustDecodeBounds = false;
                options.inSampleSize = options.outWidth * 2 / Apputils.getScreenWidth(context);

                bitmap = BitmapFactory.decodeFile(filePath, options);//根据Path读取资源图片
                int angle = 0;
                if (orientation != null && !"".equals(orientation)) {
                    angle = Integer.parseInt(orientation);
                }
                if (angle != 0) {
                    // 下面的方法主要作用是把图片转一个角度，也可以放大缩小等
                    Matrix m = new Matrix();
                    int width = bitmap.getWidth();
                    int height = bitmap.getHeight();
                    m.setRotate(angle); // 旋转angle度
                    bitmap = Bitmap.createBitmap(bitmap, 0, 0, width, height,
                            m, true);// 从新生成图片
                }

            }
        }
        return bitmap;
    }

    public static Bitmap stringtoBitmap(String string){
        //将字符串转换成Bitmap类型
        Bitmap bitmap=null;
        try {
            byte[]bitmapArray;
            bitmapArray= Base64.decode(string, Base64.DEFAULT);
            bitmap=BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bitmap;
    }



    public static String bitmaptoString(Bitmap bitmap){
        //将Bitmap转换成字符串
        String string=null;
        ByteArrayOutputStream bStream=new ByteArrayOutputStream();
        bitmap.compress(CompressFormat.PNG,100,bStream);
        byte[]bytes=bStream.toByteArray();
        string=Base64.encodeToString(bytes,Base64.DEFAULT);
        return string;
    }
}
