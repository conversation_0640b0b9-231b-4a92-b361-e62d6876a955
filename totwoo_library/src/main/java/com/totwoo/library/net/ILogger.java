/*
 * Copyright (C) 2015 彭建波(peng<PERSON><PERSON><PERSON>@finalteam.cn), Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.totwoo.library.net;

import com.totwoo.library.util.LogUtils;

/**
 * Desction:
 * Author:pengjianbo
 * Date:2016/2/2 0002 12:49
 */
class ILogger {
    public static  boolean DEBUG = false;

    public static void d(String message, Object... args) {
        if (!DEBUG)
            LogUtils.d(String.format(message, args));
    }

    public static void e(Throwable throwable) {
        if (!DEBUG)
            LogUtils.e("", throwable);
    }

    public static void e(String message, Object... args) {
        if (!DEBUG)
            LogUtils.e(String.format(message, args));
    }

    public static void e(Throwable throwable, String message, Object... args) {
        if (!DEBUG)
            LogUtils.e(String.format(message, args), throwable);

    }

    public static void i(String message, Object... args) {
        if (!DEBUG)
            LogUtils.i(String.format(message, args));
    }

    public static void v(String message, Object... args) {
        if (!DEBUG)
            LogUtils.v(String.format(message, args));
    }

    public static void w(String message, Object... args) {
        if (!DEBUG)
            LogUtils.w(String.format(message, args));

    }

    public static void wtf(String message, Object... args) {
        if (!DEBUG)
            LogUtils.wtf(String.format(message, args));
    }
}
