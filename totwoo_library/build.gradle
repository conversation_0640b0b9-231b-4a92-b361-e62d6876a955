plugins {
    id 'com.android.library'
    id 'maven-publish'
}

apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion

        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }


    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'

        }

    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    namespace 'com.totwoo.library'

    publishing {
        singleVariant('release') {
        }
    }
}
dependencies {

    api ("com.google.android.material:material:1.12.0")

    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // RxJava
    api 'io.reactivex:rxjava:1.3.0'
    api 'io.reactivex:rxandroid:1.2.1'

    api 'com.google.code.gson:gson:2.10.1'
    api 'com.alibaba:fastjson:1.1.72.android'

    // OKHttp - 升级到4.12.0以支持Android 15和最新安全特性
    api 'com.squareup.okhttp3:okhttp:4.12.0'
    api 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // retrofit 网络库 - 保持2.9.0版本，与OkHttp 4.12.0完全兼容
    api 'com.squareup.retrofit2:retrofit:3.0.0'
    api 'com.squareup.retrofit2:converter-gson:3.0.0'
    api 'com.squareup.retrofit2:adapter-rxjava:3.0.0'

//    api 'com.umeng.umsdk:apm:1.9.5'// U-APM产品包依赖，必选

    // 图片加载框架
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
    // 更新OkHttp3集成以支持OkHttp 4.12.0
    api 'com.github.bumptech.glide:okhttp3-integration:4.12.0@aar'

    api("com.blankj:utilcodex:1.31.1") {
        transitive = true
        exclude group: 'androidx.appcompat', module: 'appcompat'
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-android-extensions-runtime'
        exclude group: 'com.google.code.gson', module: 'gson'
    }

    api 'com.tencent.mars:mars-xlog:1.2.6'

    // Kotlin协程依赖 - 为库模块提供协程支持
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
}

publishing {
    publications {
        maven(MavenPublication) {
            groupId = 'com.totwoo'
            artifactId = 'library'
            version = "1.0.0-${new Date().format('yyyyMMdd-HHmm')}"

            afterEvaluate {
                from components.release
            }
        }
    }
    repositories {
        maven {
            name = 'localRepo'
            url = "${rootProject.projectDir}/repo"
        }
    }
}
