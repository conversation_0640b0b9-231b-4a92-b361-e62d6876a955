#!/usr/bin/env zsh

echo ">>>>>>>>>>>>>>>>>>>>>> Start upload symbol >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"

if [[ -z $1 ]]; then
  echo ">>>>> 请输入主版本名(忽略尾部git id) : "
  read -r ver
  ver=$ver'('$(git rev-parse --short=7 HEAD)'.'`expr $(git rev-list HEAD --count) - 1000`')'
else
  ver=$1'('$(git rev-parse --short=7 HEAD)'.'`expr $(git rev-list HEAD --count) - 1000`')'
fi

echo ">>>>> 版本名: $ver"

#curl -k "https://api.bugly.qq.com/openapi/file/upload/symbol?app_key=02CFWlSmMjWjgix3&app_id=900010529" \
#    --form "api_version=1" \
#    --form "app_id=900010529" \
#    --form "app_key=02CFWlSmMjWjgix3" \
#    --form "symbolType=1" \
#    --form "bundleId=com.totwoo.totwoo" \
#    --form "productVersion=$ver" \
#    --form "fileName=mapping.txt" \
#    --form "file=@totwoo/build/outputs/mapping/commonRelease/mapping.txt" \
#    --verbose


/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home/bin/java -jar buglyqq-upload-symbol.jar \
                -appid 900010529 \
                -appkey 02CFWlSmMjWjgix3 \
                -bundleid com.totwoo.totwoo \
                -version "$ver" \
                -platform Android \
                -inputMapping "../totwoo/build/outputs/mapping/commonRelease"
