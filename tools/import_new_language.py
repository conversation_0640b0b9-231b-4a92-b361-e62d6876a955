from openpyxl import load_workbook
from openpyxl.styles import Pattern<PERSON>ill

def process_excel_files(source_file, target_file, new_source_file, new_target_file):
    # 加载源文件和目标文件
    source_workbook = load_workbook(filename=source_file)
    target_workbook = load_workbook(filename=target_file)

    # 创建淡绿色背景填充样式
    fill = PatternFill(fill_type="solid", fgColor="C6EFCE")
    repeat_fill = PatternFill(fill_type="solid", fgColor="666666")

    # 遍历源文件的工作表
    for source_sheet in source_workbook:
        for source_row in source_sheet.iter_rows(min_row=2):
            source_b_value = source_row[1].value  # 获取源文件的B列数据

            if source_b_value is None or len(source_b_value) == 0:
                continue
            elif source_b_value == "REPEAT" and source_row[1].value is not None:
                source_row[10].fill = repeat_fill
                source_row[11].fill = repeat_fill
                continue

            source_k_value = source_row[10].value  # 获取源文件的K列数据
            source_l_value = source_row[11].value  # 获取源文件的L列数据

            # 遍历目标文件的工作表
            for target_sheet in target_workbook:

               # 扩展目标文件的列数
                max_column = target_sheet.max_column  # 获取目标文件的最大列数
                if target_sheet.max_column < 12:
                    target_sheet.insert_cols(target_sheet.max_column, amount=12 - max_column)  # 插入足够数量的列

                for target_row in target_sheet.iter_rows(min_row=2):
                    target_b_value = target_row[1].value  # 获取目标文件的B列数据

                    
                    try:
                        if target_b_value == source_b_value:
                            # 将源文件的K列和L列数据对应到目标文件的K列和L列
                            target_row[10].value = source_k_value
                            target_row[11].value = source_l_value

                            # 设置源文件中匹配到的行背景色为淡绿色
                            source_row[10].fill = fill
                            source_row[11].fill = fill
                            break
                    except Exception as e:
                        print(f"Error: {e}, \nsource_row: {source_row}, target_row: {target_row}")

    # 保存更新后的源文件和目标文件
    source_workbook.save(new_source_file)
    target_workbook.save(new_target_file)

    print(f"已生成更新后的源文件: {new_source_file}")
    print(f"已生成更新后的目标文件: {new_target_file}")

# 指定源文件和目标文件的路径
source_file = '/Users/<USER>/Downloads/韩语&西语文档0720/【韩西】APP所有语言文案全0628_底稿.xlsx'
target_file = '/Users/<USER>/Documents/Developer/ASworkspace/totwoo/tools/doc/APP所有文案最终版230104.xlsx'
new_source_file = 'new_source.xlsx'
new_target_file = 'new_target.xlsx'

# 执行操作
process_excel_files(source_file, target_file, new_source_file, new_target_file)