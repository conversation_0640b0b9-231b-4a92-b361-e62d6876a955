# 1.导入pandas模块
import logging
import os
import sys
import xml.etree.ElementTree as ET
from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from openpyxl.worksheet.worksheet import Worksheet
from xml.etree.ElementTree import Element, XMLParser, TreeBuilder

zhExcle = None
deExcle = None
itExcle = None
jaExcle = None
frExcle = None
enExcle = None
arExcle = None
koExcle = None
esExcle = None

stringExcles = []
already_paired = set()


def go_work(basic_res, source_file):
    """读取 Excel 文言数据, 取各个文言字符串匹配修改"""
    wb: Workbook = load_workbook(filename=source_file)
    for sheet in wb:
        logging.debug("====> start sheet: " + str(sheet))
        for row in sheet.iter_rows(min_row=1, max_col=16):
            result = match_and_modify(
                basic_res,
                row[3].value,
                row[4].value,
                row[5].value,
                row[6].value,
                row[7].value,
                row[8].value,
                row[9].value,
                row[10].value,
                row[11].value,
            )
            if result is not None:
                fg = "ffff00" if result == "REPEAT" else "009900"
                row[0].fill = PatternFill("solid", fgColor=fg)
                row[1].fill = PatternFill("solid", fgColor=fg)
                row[2].fill = PatternFill("solid", fgColor=fg)
                row[10].value = result

    wb.save("z_result.xlsx")


def go_work_from_key(source_file):
    """读取 Excel 文言数据, 取各个文言字符串匹配修改"""
    wb: Workbook = load_workbook(filename=source_file)
    for sheet in wb:
        logging.debug("====> start sheet: " + str(sheet))
        for row in sheet.iter_rows(min_row=2, max_col=16):
            modify_xml_by_key(row[1].value, row[3:12])


def modify_xml_by_key(key, cells):
    if key is not None and key != "REPEAT":
        modify_res(zhExcle, key, cells[0].value, "zh", force_add=True)
        modify_res(enExcle, key, cells[1].value, "en", force_add=True)
        modify_res(frExcle, key, cells[2].value, "fr", force_add=True)
        modify_res(deExcle, key, cells[3].value, "de", force_add=True)
        modify_res(itExcle, key, cells[4].value, "it", force_add=True)
        modify_res(jaExcle, key, cells[5].value, "ja", force_add=True)
        # modify_res(arExcle, key, cells[6].value, "ar", force_add=True)
        modify_res(koExcle, key, cells[7].value, "ko", force_add=True)
        modify_res(esExcle, key, cells[8].value, "es", force_add=True)


def read_basic_res(strings_path):
    # 从xml文件中读取，用getroot获取根节点，根节点也是Element对象
    tree = ET.parse(strings_path)
    root = tree.getroot()

    values = {}

    # 打印根节点的标签和属性
    for child in root:
        if child.tag == "string":
            values[child.attrib["name"]] = child.text
    return values


def match_and_modify(basic: map, zh, en, fr, de, it, ja, ar, ko, es):
    for k, v in basic.items():
        if zh in already_paired:
            logging.error("alread replace %s, %s", zh, en)
            return "REPEAT"
        elif zh is not None and match(zh, v):
            already_paired.add(zh)
            modify_res(enExcle, k, en, "en")
            modify_res(frExcle, k, fr, "fr")
            modify_res(deExcle, k, de, "de")
            modify_res(itExcle, k, it, "it")
            modify_res(jaExcle, k, ja, "ja")
            # modify_res(arExcle, k, ar, "ar")
            modify_res(koExcle, k, ar, "ko")
            modify_res(esExcle, k, ar, "es")
            return k
    return None


def match(target, xmlStr):
    return (
        target == xmlStr
        or target.replace("'", r"\'").replace("\n", r"\n").replace(r"\\", "\\")
        == xmlStr
    )


def modify_res(tree: ET.ElementTree, key, value, note, force_add=False):
    """
    指定 xml 资源, 查找 key, 并替换成 value

    *onte* 对应资源文件的批注
    """
    node = tree.find("./string[@name='%s']" % key)
    if node is not None and value is not None and len(value) > 0:
        logging.debug("modify %s ==:  %s to %s" % (note, key, value))
        node.text = text_processing(value)
    elif force_add and value is not None:
        item = ET.SubElement(tree.getroot(), "string")
        item.set("name", key)
        item.text = text_processing(value)
        logging.warning("Add new item : %s in %s, value: %s" % (key, note, value))
    else:
        logging.warning(
            "Skip replace key: %s in %s, value: %s, old text: %s"
            % (key, note, value, node.text if node is not None else "None")
        )


def text_processing(excle_str: str):
    return (
        excle_str.replace("'", r"\'")
        .replace("\n", r"\n")
        .replace(r"\\", "\\")
        .replace('"', '\\"')
    )


def init_global_res(out_dir: str):
    """
    解析所有的 xml 资源
    *out_dir* init all string xml ElementTree
    """
    global zhExcle, deExcle, itExcle, frExcle, enExcle, jaExcle, koExcle, esExcle
    zhExcle = ET.parse(
        os.path.join(out_dir, "values-zh/strings.xml"), parser=get_default_parse()
    )
    deExcle = ET.parse(
        os.path.join(out_dir, "values-de/strings.xml"), parser=get_default_parse()
    )
    itExcle = ET.parse(
        os.path.join(out_dir, "values-it/strings.xml"), parser=get_default_parse()
    )
    enExcle = ET.parse(
        os.path.join(out_dir, "values/strings.xml"), parser=get_default_parse()
    )
    jaExcle = ET.parse(
        os.path.join(out_dir, "values-ja/strings.xml"), parser=get_default_parse()
    )
    frExcle = ET.parse(
        os.path.join(out_dir, "values-fr/strings.xml"), parser=get_default_parse()
    )
    # arExcle = ET.parse(
    #     os.path.join(out_dir, "values-ar/strings.xml"), parser=get_default_parse()
    # )
    koExcle = ET.parse(
        os.path.join(out_dir, "values-ko/strings.xml"), parser=get_default_parse()
    )
    esExcle = ET.parse(
        os.path.join(out_dir, "values-es/strings.xml"), parser=get_default_parse()
    )


def get_default_parse():
    """
    定制解析器, 防止注释丢失
    """
    return XMLParser(target=TreeBuilder(insert_comments=True))


def save_all(outDir):
    """
    xlml 文件的输出保存
    """
    global zhExcle, deExcle, itExcle, frExcle, enExcle, jaExcle, arExcle, koExcle, esExcle
    zhExcle.write(
        os.path.join(outDir, "values-zh/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )
    deExcle.write(
        os.path.join(outDir, "values-de/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )
    itExcle.write(
        os.path.join(outDir, "values-it/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )
    enExcle.write(
        os.path.join(outDir, "values/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )
    jaExcle.write(
        os.path.join(outDir, "values-ja/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )
    frExcle.write(
        os.path.join(outDir, "values-fr/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )
    # arExcle.write(
    #     os.path.join(outDir, "values-ar/strings.xml"),
    #     encoding="utf-8",
    #     xml_declaration=True,
    # )
    koExcle.write(
        os.path.join(outDir, "values-ko/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )
    esExcle.write(
        os.path.join(outDir, "values-es/strings.xml"),
        encoding="utf-8",
        xml_declaration=True,
    )


def xml_test():
    res: Element = ET.Element("resource")
    ET.SubElement(res, "string", {"name": "app_name"}).text = "大家好".replace("家", r"\'家")
    ET.SubElement(res, "string", {"name": "gogo"}).text = "大家好\n不错的"
    ET.SubElement(res, "string", {"name": "net_error"}).text = r'"大家好\n不错的"'
    ET.ElementTree(res).write("z_test.xml", encoding="utf-8")


def excel_test():
    wb = Workbook()
    sheet: Worksheet = wb.create_sheet("测试", index=0)
    sheet.cell(1, 1).value = "大家好".replace("家", r"\'家")
    sheet.cell(2, 1).value = "大家好\n不错的"
    sheet.cell(3, 1).value = r"大家好\n不错的"
    wb.save("z_test.xlsx")
    wb.close()


if __name__ == "__main__":
    if len(sys.argv) > 1:
        excle_file = sys.argv[1]
    else:
        # excle_file = os.path.join(os.path.dirname(__file__), 'doc', 'meet系列首饰中英文文案230104.xlsx')
        excle_file = os.path.join(
            os.path.dirname(__file__), "doc", "APP所有文案最终版230104.xlsx"
        )

    out_dir = os.path.join(os.path.dirname(__file__), "../totwoo/src/main/res")

    # 读取标准语言的所有 key 和 value 目前已中文为准
    basic_res = read_basic_res(os.path.join(out_dir, "values-zh/strings.xml"))

    init_global_res(out_dir)

    # 已中文字符串为依据的查找, 替换
    # go_work(basic_res, excle_file)

    # 已 key 为依据的填充
    go_work_from_key(excle_file)

    save_all(out_dir)

    logging.warning("success count: %d", len(already_paired))
