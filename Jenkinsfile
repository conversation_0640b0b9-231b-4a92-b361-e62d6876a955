pipeline {
  agent any
  stages {
    stage('检出代码') {
      steps {
        checkout([$class: 'GitSCM',
        branches: [[name: GIT_BUILD_REF]],
        userRemoteConfigs: [[
          url: GIT_REPO_URL,
          credentialsId: CREDENTIALS_ID
        ]]])
      }
    }
    stage('编译构建APK') {
      agent {
        dockerfile {
          filename 'Dockerfile'
          dir 'android'
          additionalBuildArgs '--build-arg version=1.0.2'
          reuseNode true
        }

      }
      steps {
        sh './gradlew clean && rm -rf ./app/build/'
        sh './gradlew assembleCommonDebug'
      }
    }
    stage('apk上传cos') {
      steps {
        sh 'sudo pip install --upgrade pip'
        sh 'sudo pip install -U coscmd'
        sh 'coscmd config -a ${COS_SECRET_ID} -s ${COS_SECRET_KEY} -b ${COS_BUCKET_NAME} -r ${COS_BUCKET_REGION}'
        sh 'coscmd upload -r app/build/outputs/apk/release/*.apk /'
      }
    }
    stage('获取版本名称和版本号') {
      steps {
        sh '''#!/bin/bash
filepath=`ls /root/workspace/build.gradle`
//获取版本号
versionname=`cat $filepath |grep versionName|awk \'{print $3}\'`
//获取版本code
versioncode=`cat $filepath |grep versionCode|awk \'{print $3}\'`
//获取打包好的apk名称
filePath=`ls /root/workspace/app/build/outputs/apk/release/*.apk`
fileName=${filePath##*/}
//打印相关信息
echo ${versionname//,/}
echo ${versioncode//,/}
echo apkurl=${COS_URL}$fileName
'''
      }
    }
  }
  environment {
    COS_URL = 'https://gyapp-android-1257050538.cos.ap-nanjing.myqcloud.com/'
    ENTERPRISE = 'gzmpc'
    PROJECT = 'androidzidonghua'
    ARTIFACT = 'android'
    CODE_DEPOT = 'gyapp'
    ARTIFACT_BASE = "${ENTERPRISE}-generic.pkg.coding.net"
    ARTIFACT_IMAGE = "${ARTIFACT_BASE}/${PROJECT}/${ARTIFACT}/${CODE_DEPOT}"
  }
}