# 使用官方 OpenJDK 17 镜像作为基础镜像
FROM openjdk:17-jdk

# 安装 Android SDK 所需的依赖
RUN apt-get update && apt-get install -y wget unzip lib32stdc++6 lib32z1

# 定义环境变量
ENV ANDROID_HOME /usr/local/android-sdk
ENV PATH $ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools:$PATH

# 下载并安装 Android Commandline Tools
RUN wget https://dl.google.com/android/repository/commandlinetools-linux-6609375_latest.zip -O commandlinetools.zip && \
    unzip commandlinetools.zip && \
    rm commandlinetools.zip

# 移动解压后的文件夹到正确的位置
RUN mv commandlinetools /usr/local/android-sdk

# 接受所有 SDK 工具的许可协议
RUN yes | sdkmanager --licenses

# 安装 Android SDK 平台工具和 API Level 34
RUN sdkmanager "platform-tools" "platforms;android-34"

# 安装必要的构建工具
RUN sdkmanager "build-tools;31.0.0"

# 下载指定版本的 Gradle
RUN wget https://mirrors.cloud.tencent.com/gradle/gradle-8.4-bin.zip -O gradle.zip && \
    unzip gradle.zip && \
    rm gradle.zip

# 移动 Gradle 到合适的位置并配置环境变量
RUN mv gradle-8.4 /opt/gradle
ENV GRADLE_HOME /opt/gradle
ENV PATH $GRADLE_HOME/bin:$PATH

# 设置 Gradle 缓存目录，避免每次构建都重新下载依赖
ENV GRADLE_USER_HOME /cache/.gradle

# 清除 APT 缓存
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# 设置工作目录为 /app
WORKDIR /root/workspace

