plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    resourcePrefix "AndroidMP3RecorderLibrary_"	//这个随便填
    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
        }
    }
    namespace 'com.czt.mp3recorder'

    publishing {
        singleVariant('release') {
        }
    }
}

dependencies {
}

publishing {
    publications {
        maven(MavenPublication) {
            groupId = 'com.totwoo'
            artifactId = 'mp3_re'
            version = "1.0.0-${new Date().format('yyyyMMdd-HHmm')}"

            afterEvaluate {
                from components.release
            }
        }
    }
    repositories {
        maven {
            name = 'localRepo'
            url = "${rootProject.projectDir}/repo"
        }
    }
}
