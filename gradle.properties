## Project-wide Gradle settings.
#
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Sat Jul 23 19:36:20 CST 2016

#org.gradle.java.home=/Applications/Android Studio 2.app/Contents/jbr/Contents/Home
dex.force.jumbo=true
org.gradle.jvmargs=-Xmx2048m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.daemon=true
android.injected.testOnly = false
android.useAndroidX=true
android.enableJetifier=true
#android.useDeprecatedNDK=true
MobSDK.spEdition=FP
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# Android 15 (API 35) Edge-to-Edge 适配
#android.suppressUnsupportedCompileSdk=35
android.enableR8.fullMode=false



####################################################################################################
# 是否使用模块 ease 源码集成  compileOnly totwoo_library
isLibEaseSource=false
# 是否使用模块 mp3_re 源码集成
isLibMp3ReSource=false
# 是否使用模块 VideoRecorder 源码集成
isLibVideoRecorderSource=false
# 是否使用模块 frameworks 源码集成
isLibFrameworkSource=false
# 是否使用模块 totwoo_library 源码集成
isLibTotwooLibrarySource=false


#是否使用模块 tim core源码集成
isLibTimCoreSource=false
