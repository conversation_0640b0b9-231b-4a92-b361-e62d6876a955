# 🚀 AAR依赖管理使用指南（简化版）

## 快速开始

### 1️⃣ 发布AAR
```bash
./scripts/aar-manager.sh publish
```

### 2️⃣ 手动切换模式

**源码开发模式**（修改模块代码时）
编辑 `gradle.properties`，设置为 `true`：
```properties
isLibEaseSource=true
isLibMp3ReSource=true
isLibVideoRecorderSource=true
isLibFrameworkSource=true
isLibTotwooLibrarySource=true
isLibTimCoreSource=true
```

**AAR依赖模式**（使用稳定版本时）
编辑 `gradle.properties`，设置为 `false`：
```properties
isLibEaseSource=false
isLibMp3ReSource=false
isLibVideoRecorderSource=false
isLibFrameworkSource=false
isLibTotwooLibrarySource=false
isLibTimCoreSource=false
```

### 3️⃣ 版本管理
版本号自动生成，格式：`1.0.0-yyyyMMdd-HHmm`
- 每次发布AAR时自动使用当前时间戳
- 主应用使用 `1.0.0-+` 自动依赖最新版本

