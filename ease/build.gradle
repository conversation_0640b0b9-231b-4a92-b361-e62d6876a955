plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }
    namespace 'com.ease'

    publishing {
        singleVariant('release') {
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

//    compileOnly 'com.squareup.retrofit2:retrofit:3.0.0'
//    compileOnly group: 'com.squareup.retrofit2', name: 'converter-gson', version: '3.0.0'
//    compileOnly 'com.squareup.retrofit2:adapter-rxjava:3.0.0'
//    compileOnly 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'io.reactivex:rxjava:1.3.0'
    implementation 'io.reactivex:rxandroid:1.2.1'
    compileOnly project(path: ':totwoo_library')
}

publishing {
    publications {
        maven(MavenPublication) {
            groupId = 'com.totwoo'
            artifactId = 'ease'
            version = "1.0.0-${new Date().format('yyyyMMdd-HHmm')}"

            afterEvaluate {
                from components.release // 仅包含 .aar
            }
        }
    }
    repositories {
        maven {
            name = 'localRepo'
            url = "${rootProject.projectDir}/repo"

        }
    }
}

