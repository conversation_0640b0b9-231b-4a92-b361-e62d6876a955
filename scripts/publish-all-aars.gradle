// 简化的AAR发布脚本
// 使用方法: ./gradlew publishAllAars

// 定义所有需要发布的模块
def publishableModules = [
    'ease',
    'mp3_re',
    'VideoRecorder',
    'framework',
    'totwoo_library',
    'tim:imui_core'
]

// 创建发布所有AAR的任务
task publishAllAars {
    group = 'publishing'
    description = '发布所有模块的AAR到本地仓库'

    doFirst {
        println "开始发布所有AAR..."
        println "发布模块列表: ${publishableModules}"
    }

    // 依赖所有模块的发布任务
    publishableModules.each { moduleName ->
        dependsOn ":${moduleName}:publishToMavenLocal"
        dependsOn ":${moduleName}:publish"
    }

    doLast {
        println "所有AAR发布完成!"
        println "本地仓库路径: ${rootProject.projectDir}/repo"
        println "注意: 如需使用AAR，请手动修改gradle.properties中的开关"
    }
}


