#!/bin/bash

# AAR管理脚本
# 用法: ./scripts/aar-manager.sh [command]

set -e

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 项目根目录
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "AAR管理脚本 - 简化版本"
    echo ""
    echo "用法: $0 [command]"
    echo ""
    echo "命令:"
    echo "  publish         发布所有模块的AAR"
    echo "  clean           清理构建缓存"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 publish      # 发布所有模块AAR"
    echo "  $0 clean        # 清理构建缓存"
    echo ""
    echo "注意:"
    echo "  - 源码/AAR切换请手动修改gradle.properties"
    echo "  - 版本号请手动修改version.properties"
}

# 发布AAR
publish_aars() {
    print_info "发布所有模块的AAR..."
    ./gradlew publishAllAars
    print_success "AAR发布完成"
}

# 清理构建缓存
clean_build() {
    print_info "清理构建缓存..."
    ./gradlew clean
    print_success "构建缓存清理完成"
}

# 主逻辑
case "${1:-help}" in
    "publish")
        publish_aars
        ;;
    "clean")
        clean_build
        ;;
    "help"|*)
        show_help
        ;;
esac
