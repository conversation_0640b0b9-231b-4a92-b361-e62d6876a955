name: Publish AARs

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'ease/**'
      - 'mp3_re/**'
      - 'VideoRecorder/**'
      - 'framework/**'
      - 'totwoo_library/**'
      - 'tim/**'
  workflow_dispatch:
    inputs:
      publish_type:
        description: '发布类型'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  publish:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: <PERSON> execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Grant execute permission for scripts
      run: chmod +x scripts/aar-manager.sh
      
    - name: Check current status
      run: ./scripts/aar-manager.sh status
      
    - name: Publish AARs
      run: ./scripts/aar-manager.sh publish-new
      
    - name: Upload AAR artifacts
      uses: actions/upload-artifact@v3
      with:
        name: published-aars
        path: repo/
        retention-days: 30
        
    - name: Commit version changes
      if: github.event_name == 'workflow_dispatch'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .
        git diff --staged --quiet || git commit -m "Auto-update versions after AAR publish"
        git push
